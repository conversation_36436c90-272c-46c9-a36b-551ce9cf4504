/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "subtree_common.h"


class Depth_02 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void Depth_02::SetUpTestCase()
{
    system("${TEST_HOME}/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultTransactionType=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultIsolationLevel=2\"");
    system("${TEST_HOME}/tools/start.sh -f");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void Depth_02::TearDownTestCase()
{
    int ret = close_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void Depth_02::SetUp()
{
    int ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_OK, ret);
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    AsyncUserDataT userData = {0};
    const char *namespace1 = "NamespaceA01002";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespace(g_stmt_sync, namespace1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    // alloc all stmt
    TestyangallocAllstmt();
    AW_CHECK_LOG_BEGIN();
    char errorMsg5[128] = {};
    char errorMsg6[128] = {};
    (void)snprintf(errorMsg5, sizeof(errorMsg5), "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(errorMsg6, sizeof(errorMsg6), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg5, errorMsg6);
}

void Depth_02::TearDown()
{
    AW_CHECK_LOG_END();
    //  删边
    int ret = GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeT0T1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeT0T1container);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeT0T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt_sync, g_edgeLabelT1ChoiceT2case1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeT1T2Con);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeT1T2List);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt_sync, g_edgeLabetT1T2Choice);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt_sync, g_edgeLabelT2ChoiceT3case1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropEdgeLabel(g_stmt_sync, g_edgeLabelT2ChoiceT3case2);
    ASSERT_EQ(GMERR_OK, ret);
    // 删表
    ret = GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT0);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT1container);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT1choice);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT2Con);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT2List);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT2choice);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT3case1);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT3case2);
    ASSERT_EQ(GMERR_OK, ret);
    // 释放all stmt
    TestyangfreeAllstmt();
    const char *namespace1 = "NamespaceA01002";
    AsyncUserDataT userData = {0};
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);
}
// 双层container类型的内容过滤，根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_007)
{
    int ret ;
    // 建表
    readJanssonFile("schema/SingleSchema/VertexSchema/ContainerSingle.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/SingleSchema/EdgeSchema/ContainerSingleEdge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_S", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, "SubT1container_S", GMC_OPERATION_REPLACE_GRAPH);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1Con, T1Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_7.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/ContainerSingle_C.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_S";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_S");
    EXPECT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// 双层container类型的叶子过滤，根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_008)
{
    int ret ;
    // 建表
    readJanssonFile("schema/SingleSchema/VertexSchema/ContainerSingle.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/SingleSchema/EdgeSchema/ContainerSingleEdge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_S", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, "SubT1container_S", GMC_OPERATION_REPLACE_GRAPH);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1Con, T1Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_8.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/ContainerSingle_L.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_S";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_S");
    EXPECT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// 双层container类型的容器过滤，根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_009)
{
    int ret ;
    // 建表
    readJanssonFile("schema/SingleSchema/VertexSchema/ContainerSingle.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/SingleSchema/EdgeSchema/ContainerSingleEdge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_S", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, "SubT1container_S", GMC_OPERATION_REPLACE_GRAPH);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1Con, T1Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_9.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/ContainerSingle_R.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_S";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_S");
    EXPECT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// container+list类型的内容过滤，根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_010)
{
    int ret ;
    // 建表
    readJanssonFile("schema/SingleSchema/VertexSchema/C_ListSingle.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/SingleSchema/EdgeSchema/C_ListSingleEdge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_S", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_S", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_10.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ListSingle_C.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_S";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_S");
    EXPECT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// container+list类型的叶子过滤，根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_011)
{
    int ret ;
    // 建表
    readJanssonFile("schema/SingleSchema/VertexSchema/C_ListSingle.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/SingleSchema/EdgeSchema/C_ListSingleEdge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_S", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_S", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_11.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson;
    Data.expectStatus = GMERR_OK;
    Data.step = 0;
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ListSingle_L.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_S";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_S");
    EXPECT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// container+list类型的容器过滤，根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_012)
{
    int ret ;
    // 建表
    readJanssonFile("schema/SingleSchema/VertexSchema/C_ListSingle.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/SingleSchema/EdgeSchema/C_ListSingleEdge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_S", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_S", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_12.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson;
    Data.expectStatus = GMERR_OK;
    Data.step = 0;
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ListSingle_R.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_S";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_S");
    EXPECT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// container+choice-case类型的内容过滤，根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_013)
{
    int ret ;
    // 建表
    readJanssonFile("schema/SingleSchema/VertexSchema/C_ChoiceCaseSingle.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/SingleSchema/EdgeSchema/C_ChoiceCaseSingleEdge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_S", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    // 设置child节点 g_stmt_sync_T1choice T1层choice
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT1choice_S", GMC_OPERATION_REPLACE_GRAPH);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choice, T1choice_F0value);
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice case T1层choice case
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT1choiceCase_S", GMC_OPERATION_REPLACE_GRAPH);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase_F0value);
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_sync_T1choicecase, T1choicecase_F0value,
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1choicecase(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_13.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ChoiceCaseSingle_C.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_S";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_S");
    EXPECT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// container+choice-case类型的叶子过滤，根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_014)
{
    int ret ;
    // 建表
    readJanssonFile("schema/SingleSchema/VertexSchema/C_ChoiceCaseSingle.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/SingleSchema/EdgeSchema/C_ChoiceCaseSingleEdge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_S", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    // 设置child节点 g_stmt_sync_T1choice T1层choice
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT1choice_S", GMC_OPERATION_REPLACE_GRAPH);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choice, T1choice_F0value);
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice case T1层choice case
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT1choiceCase_S", GMC_OPERATION_REPLACE_GRAPH);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase_F0value);
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_sync_T1choicecase, T1choicecase_F0value,
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1choicecase(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_14.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ChoiceCaseSingle_L.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_S";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_S");
    EXPECT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// container+choice-case类型的容器过滤，根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_015)
{
    int ret ;
    // 建表
    readJanssonFile("schema/SingleSchema/VertexSchema/C_ChoiceCaseSingle.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/SingleSchema/EdgeSchema/C_ChoiceCaseSingleEdge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_S", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    // 设置child节点 g_stmt_sync_T1choice T1层choice
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT1choice_S", GMC_OPERATION_REPLACE_GRAPH);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choice, T1choice_F0value);
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置child节点 g_stmt_sync_T1choice case T1层choice case
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT1choiceCase_S", GMC_OPERATION_REPLACE_GRAPH);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase_F0value);
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_sync_T1choicecase, T1choicecase_F0value,
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1choicecase(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_15.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ChoiceCaseSingle_R.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_S";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_S");
    EXPECT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// c-c-c-c-c单链类型内容过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_016)
{
    int ret ;
    // 建表
    TestyangallocAllstmt5C();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_C_C_C_C_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_C_C_C_C_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_0", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, "SubT1container_1", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1Con, T1Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2Con, "SubT2container_2", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1Con, g_stmt_sync_T2Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T2Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T2Con, T2Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T2Con, T2Con_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T2Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2Con);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T3Con, "SubT3container_3", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T2Con, g_stmt_sync_T3Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T3Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T3Con, T3Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T3Con, T3Con_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T3Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T3Con);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T4Con, "SubT4container_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T3Con, g_stmt_sync_T4Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T4Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T4Con, T4Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T4Con, T4Con_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T4Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T4Con);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_16.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_C_C_C_C_5_C.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_0";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_0");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmt5C();
    free(FilterJson);
    free(ReturnJson);
}

// c-c-c-c-c单链类型叶子过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_017)
{
    int ret ;
    // 建表
    TestyangallocAllstmt5C();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_C_C_C_C_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_C_C_C_C_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_0", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, "SubT1container_1", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1Con, T1Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2Con, "SubT2container_2", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1Con, g_stmt_sync_T2Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T2Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T2Con, T2Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T2Con, T2Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T2Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2Con);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T3Con, "SubT3container_3", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T2Con, g_stmt_sync_T3Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T3Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T3Con, T3Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T3Con, T3Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T3Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T3Con);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T4Con, "SubT4container_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T3Con, g_stmt_sync_T4Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T4Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T4Con, T4Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T4Con, T4Con_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T4Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T4Con);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_17.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_C_C_C_C_5_L.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_0";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_0");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmt5C();
    free(FilterJson);
    free(ReturnJson);
}

// c-c-c-c-c单链类型容器过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_018)
{
    int ret ;
    // 建表
    TestyangallocAllstmt5C();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_C_C_C_C_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_C_C_C_C_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_0", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, "SubT1container_1", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1Con, T1Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2Con, "SubT2container_2", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1Con, g_stmt_sync_T2Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T2Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T2Con, T2Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T2Con, T2Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T2Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T2Con);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T3Con, "SubT3container_3", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T2Con, g_stmt_sync_T3Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T3Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T3Con, T3Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T3Con, T3Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T3Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T3Con);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T4Con, "SubT4container_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T3Con, g_stmt_sync_T4Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T4Con_F0value = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T4Con, T4Con_F0value);
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T4Con, T4Con_F0value, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T4Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T4Con);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_18.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_C_C_C_C_5_R.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_0";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_0");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmt5C();
    free(FilterJson);
    free(ReturnJson);
}

// c-L-L-L-L单链类型内容过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_019)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCLLLL();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_L_L_L_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_L_L_L_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = m;
        int32_t T1ListF1 = m;
        bool T1ListF2 = true;
        double T1ListF3 = m+1;
        bool T1ListF4 = m+2;
        float T1ListF5 = m+3;
        char *T1ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, T1ListF1, T1ListF2, T1ListF3, T1ListF4, T1ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T2List_pkvalue = m;
        int32_t T2ListF1 = m;
        bool T2ListF2 = true;
        double T2ListF3 = m+1;
        bool T2ListF4 = m+2;
        float T2ListF5 = m+3;
        char *T2ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "SubT2List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T2List, T2List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T2List, T2ListF1, T2ListF2, T2ListF3, T2ListF4, T2ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T3List_pkvalue = m;
        int32_t T3ListF1 = m;
        bool T3ListF2 = true;
        double T3ListF3 = m+1;
        bool T3ListF4 = m+2;
        float T3ListF5 = m+3;
        char *T3ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T3List, "SubT3List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T2List, g_stmt_sync_T3List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T3List, T3List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T3List, T3ListF1, T3ListF2, T3ListF3, T3ListF4, T3ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T3List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T4List_pkvalue = m;
        int32_t T4ListF1 = m;
        bool T4ListF2 = true;
        double T4ListF3 = m+1;
        bool T4ListF4 = m+2;
        float T4ListF5 = m+3;
        char *T4ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T4List, "SubT4List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T3List, g_stmt_sync_T4List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T4List, T4List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T4List, T4ListF1, T4ListF2, T4ListF3, T4ListF4, T4ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T4List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_19.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_L_L_L_5_C.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_L";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_L");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCLLLL();
    free(FilterJson);
    free(ReturnJson);
}

// c-L-L-L-L单链类型叶子过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_020)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCLLLL();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_L_L_L_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_L_L_L_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = m;
        int32_t T1ListF1 = m;
        bool T1ListF2 = true;
        double T1ListF3 = m+1;
        bool T1ListF4 = m+2;
        float T1ListF5 = m+3;
        char *T1ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, T1ListF1, T1ListF2, T1ListF3, T1ListF4, T1ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T2List_pkvalue = m;
        int32_t T2ListF1 = m;
        bool T2ListF2 = true;
        double T2ListF3 = m+1;
        bool T2ListF4 = m+2;
        float T2ListF5 = m+3;
        char *T2ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "SubT2List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T2List, T2List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T2List, T2ListF1, T2ListF2, T2ListF3, T2ListF4, T2ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T3List_pkvalue = m;
        int32_t T3ListF1 = m;
        bool T3ListF2 = true;
        double T3ListF3 = m+1;
        bool T3ListF4 = m+2;
        float T3ListF5 = m+3;
        char *T3ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T3List, "SubT3List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T2List, g_stmt_sync_T3List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T3List, T3List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T3List, T3ListF1, T3ListF2, T3ListF3, T3ListF4, T3ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T3List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T4List_pkvalue = m;
        int32_t T4ListF1 = m;
        bool T4ListF2 = true;
        double T4ListF3 = m+1;
        bool T4ListF4 = m+2;
        float T4ListF5 = m+3;
        char *T4ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T4List, "SubT4List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T3List, g_stmt_sync_T4List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T4List, T4List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T4List, T4ListF1, T4ListF2, T4ListF3, T4ListF4, T4ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T4List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_20.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_L_L_L_5_L.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_L";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_L");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCLLLL();
    free(FilterJson);
    free(ReturnJson);
}

// c-L-L-L-L单链类型容器过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_021)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCLLLL();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_L_L_L_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_L_L_L_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = m;
        int32_t T1ListF1 = m;
        bool T1ListF2 = true;
        double T1ListF3 = m+1;
        bool T1ListF4 = m+2;
        float T1ListF5 = m+3;
        char *T1ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, T1ListF1, T1ListF2, T1ListF3, T1ListF4, T1ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T2List_pkvalue = m;
        int32_t T2ListF1 = m;
        bool T2ListF2 = true;
        double T2ListF3 = m+1;
        bool T2ListF4 = m+2;
        float T2ListF5 = m+3;
        char *T2ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "SubT2List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T2List, T2List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T2List, T2ListF1, T2ListF2, T2ListF3, T2ListF4, T2ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T3List_pkvalue = m;
        int32_t T3ListF1 = m;
        bool T3ListF2 = true;
        double T3ListF3 = m+1;
        bool T3ListF4 = m+2;
        float T3ListF5 = m+3;
        char *T3ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T3List, "SubT3List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T2List, g_stmt_sync_T3List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T3List, T3List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T3List, T3ListF1, T3ListF2, T3ListF3, T3ListF4, T3ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T3List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t m =1; m<11;m++)
    {
        // list 主键的值 F0 从1-10 
        uint32_t T4List_pkvalue = m;
        int32_t T4ListF1 = m;
        bool T4ListF2 = true;
        double T4ListF3 = m+1;
        bool T4ListF4 = m+2;
        float T4ListF5 = m+3;
        char *T4ListF6 = (char *)"string";
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T4List, "SubT4List_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T3List, g_stmt_sync_T4List);
        ASSERT_EQ(GMERR_OK, ret);
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T4List, T4List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T4List, T4ListF1, T4ListF2, T4ListF3, T4ListF4, T4ListF5,
                                         GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T4List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_21.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_L_L_L_5_R.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_L";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_L");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCLLLL();
    free(FilterJson);
    free(ReturnJson);
}

// c-choice-case-choice-case单链类型内容过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_022)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCChCaChCa();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_Ch_Ca_Ch_Ca.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_Ch_Ca_Ch_Ca_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_choice_1, "SubT1choice_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_choice_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_choice_1, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_choice_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_choice_1);
    ASSERT_EQ(GMERR_OK, ret);
    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_case1, "SubT1Case_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_choice_1, g_stmt_sync_case1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_case1, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_case1, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_case1, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_case1);
    ASSERT_EQ(GMERR_OK, ret);

    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_choice2, "SubT2choice_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_case1, g_stmt_sync_choice2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T2choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_choice2, T2choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_choice2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_choice2);
    ASSERT_EQ(GMERR_OK, ret);
    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_case2, "SubT2casee_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_choice2, g_stmt_sync_case2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T2choicecase1_pkvalue = 2;
    testSetg_keyNameAndValue(g_stmt_sync_case2, T2choicecase1_pkvalue,2);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_case2, T2choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_case2, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_case2);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_22.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_Ch_Ca_Ch_Ca_C.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_Ch_Ca";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_Ch_Ca");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCChCaChCa();
    free(FilterJson);
    free(ReturnJson);
}

// c-choice-case-choice-case单链类型叶子过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_023)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCChCaChCa();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_Ch_Ca_Ch_Ca.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_Ch_Ca_Ch_Ca_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_choice_1, "SubT1choice_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_choice_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_choice_1, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_choice_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_choice_1);
    ASSERT_EQ(GMERR_OK, ret);
    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_case1, "SubT1Case_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_choice_1, g_stmt_sync_case1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_case1, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_case1, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_case1, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_case1);
    ASSERT_EQ(GMERR_OK, ret);

    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_choice2, "SubT2choice_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_case1, g_stmt_sync_choice2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T2choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_choice2, T2choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_choice2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_choice2);
    ASSERT_EQ(GMERR_OK, ret);
    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_case2, "SubT2casee_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_choice2, g_stmt_sync_case2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T2choicecase1_pkvalue = 2;
    testSetg_keyNameAndValue(g_stmt_sync_case2, T2choicecase1_pkvalue,2);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_case2, T2choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_case2, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_case2);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_23.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_Ch_Ca_Ch_Ca_L.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_Ch_Ca";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_Ch_Ca");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCChCaChCa();
    free(FilterJson);
    free(ReturnJson);
}

// c-choice-case-choice-case单链类型容器过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_024)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCChCaChCa();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_Ch_Ca_Ch_Ca.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_Ch_Ca_Ch_Ca_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_choice_1, "SubT1choice_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_choice_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_choice_1, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_choice_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_choice_1);
    ASSERT_EQ(GMERR_OK, ret);
    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_case1, "SubT1Case_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_choice_1, g_stmt_sync_case1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_case1, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_case1, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_case1, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_case1);
    ASSERT_EQ(GMERR_OK, ret);

    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice (T2层 choice)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_choice2, "SubT2choice_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_case1, g_stmt_sync_choice2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T2choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_choice2, T2choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_choice2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_choice2);
    ASSERT_EQ(GMERR_OK, ret);
    //设置child节点 g_stmt_sync_T2choice T1层 list下面的 choice case1 (T2层 choice case1)
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_case2, "SubT2casee_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_choice2, g_stmt_sync_case2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T2choicecase1_pkvalue = 2;
    testSetg_keyNameAndValue(g_stmt_sync_case2, T2choicecase1_pkvalue,2);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_case2, T2choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_case2, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_case2);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_24.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_Ch_Ca_Ch_Ca_R.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_Ch_Ca";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_Ch_Ca");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCChCaChCa();
    free(FilterJson);
    free(ReturnJson);
}

// c-list-list-choice-case单链类型内容过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_025)
{
    int ret ;
    // 建表
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_L_Ch_Ca_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_L_Ch_Ca_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_L_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_C_L_Ch_Ca", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "SubT2List_C_L_Ch_Ca", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T2List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T2List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT3choice_C_L_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T2List, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT4Case_C_L_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_25.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_L_Ch_Ca_5_C.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_L_Ch_Ca";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_L_Ch_Ca");
    ASSERT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// c-list-list-choice-case单链类型叶子过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_026)
{
    int ret ;
    // 建表
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_L_Ch_Ca_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_L_Ch_Ca_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_L_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_C_L_Ch_Ca", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "SubT2List_C_L_Ch_Ca", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T2List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T2List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT3choice_C_L_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T2List, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT4Case_C_L_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_26.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_L_Ch_Ca_5_L.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_L_Ch_Ca";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_L_Ch_Ca");
    ASSERT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// c-list-list-choice-case单链类型容器过滤 reply文件需要编写，默认，0,1,2，3查询
TEST_F(Depth_02, Yang_010_002_027)
{
    int ret ;
    // 建表
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_L_Ch_Ca_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_L_Ch_Ca_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_L_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_C_L_Ch_Ca", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "SubT2List_C_L_Ch_Ca", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T2List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T2List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT3choice_C_L_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T2List, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT4Case_C_L_Ch_Ca", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_27.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_L_Ch_Ca_5_R.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_L_Ch_Ca";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_L_Ch_Ca");
    ASSERT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// c-list-choice-case-container单链类型内容过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_028)
{
    int ret ;
    // 建表
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_Ch_Ca_C_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_Ch_Ca_C_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT2choice_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT3Case_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, "SubT4Con_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choicecase, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_28.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_Ch_Ca_C_5_C.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_L_Ch_Ca_C";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_L_Ch_Ca_C");
    ASSERT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// c-list-choice-case-container单链类型叶子过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_029)
{
    int ret ;
    // 建表
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_Ch_Ca_C_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_Ch_Ca_C_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT2choice_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT3Case_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, "SubT4Con_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choicecase, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_29.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_Ch_Ca_C_5_L.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_L_Ch_Ca_C";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_L_Ch_Ca_C");
    ASSERT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// c-list-choice-case-container单链类型容器过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_030)
{
    int ret ;
    // 建表
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_Ch_Ca_C_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_Ch_Ca_C_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT2choice_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT3Case_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1Con, "SubT4Con_C_L_Ch_Ca_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choicecase, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_sync_T1Con, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_sync_T1Con, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1Con);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_30.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_Ch_Ca_C_5_R.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_L_Ch_Ca_C";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_L_Ch_Ca_C");
    ASSERT_EQ(GMERR_OK, ret);
    free(ReturnJson);
    free(FilterJson);
}

// c-list-choice-case-list单链类型内容过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_031)
{
    int ret ;
    // 建表
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_Ch_Ca_L_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_Ch_Ca_L_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT1choice_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT1choiceCase_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "SubT2List_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1choicecase, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T2List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T2List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_31.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_Ch_Ca_L_5_C.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_L_Ch_Ca_L";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_L_Ch_Ca_L");
    ASSERT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// c-list-choice-case-list单链类型叶子过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_032)
{
    int ret ;
    // 建表
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_Ch_Ca_L_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_Ch_Ca_L_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT1choice_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT1choiceCase_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "SubT2List_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1choicecase, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T2List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T2List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_32.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_Ch_Ca_L_5_L.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_L_Ch_Ca_L";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_L_Ch_Ca_L");
    ASSERT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// c-list-choice-case-list单链类型容器过滤 根节点开始，深度为1查询
TEST_F(Depth_02, Yang_010_002_033)
{
    int ret ;
    // 建表
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_Ch_Ca_L_5.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_Ch_Ca_L_5_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
     AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, "SubT1List_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T1List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T1List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choice, "SubT1choice_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_sync_T1choice, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_sync_T1choice, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choice);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1choicecase, "SubT1choiceCase_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T1choice, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase1_pkvalue = 1;
    testSetg_keyNameAndValue(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,1);
    testYangSetVertexProperty_T2choicecase1_F0(g_stmt_sync_T1choicecase, T1choicecase1_pkvalue,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T2choicecase1(g_stmt_sync_T1choicecase, F1, F2, F3, F4, F5,
                                               GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T1choicecase);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T2List, "SubT2List_C_L_Ch_Ca_L", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1choicecase, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_sync_T2List, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_sync_T2List, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_33.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_Ch_Ca_L_5_R.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_L_Ch_Ca_L";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_L_Ch_Ca_L");
    ASSERT_EQ(GMERR_OK, ret);
    free(FilterJson);
    free(ReturnJson);
}

// c-c-c-c-c-c-c-c 8层单链类型内容过滤 中间起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_034)
{
    int ret ;
    // 建表
    TestyangallocAllstmtC8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_1, "SubT1Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_1, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_2, "SubT2Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_1, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_2, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_3, "SubT3Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_2, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_3, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_4, "SubT4Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_3, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 9;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_4, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_5, "SubT5Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_4, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_5, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_6, "SubT6Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_5, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_6, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_7, "SubT7Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_6, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_7, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_34.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_8_C_M.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_8_C";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_8_C");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtC8();
    free(FilterJson);
    free(ReturnJson);
}

// c-c-c-c-c-c-c-c 8层单链类型内容过滤 末位起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_035)
{
    int ret ;
    // 建表
    TestyangallocAllstmtC8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_1, "SubT1Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_1, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_2, "SubT2Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_1, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_2, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_3, "SubT3Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_2, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_3, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_4, "SubT4Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_3, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 9;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_4, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_5, "SubT5Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_4, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_5, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_6, "SubT6Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_5, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_6, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_7, "SubT7Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_6, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_7, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_35.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_8_C_E.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_8_C";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_8_C");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtC8();
    free(FilterJson);
    free(ReturnJson);
}

// c-c-c-c-c-c-c-c 8层单链类型叶子过滤 中间起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_036)
{
    int ret ;
    // 建表
    TestyangallocAllstmtC8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_1, "SubT1Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_1, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_2, "SubT2Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_1, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_2, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_3, "SubT3Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_2, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_3, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_4, "SubT4Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_3, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 9;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_4, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_5, "SubT5Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_4, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_5, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_6, "SubT6Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_5, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_6, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_7, "SubT7Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_6, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_7, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_36.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_8_L_M.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_8_C";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_8_C");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtC8();
    free(ReturnJson);
    free(FilterJson);
}

// c-c-c-c-c-c-c-c 8层单链类型叶子过滤 末位起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_037)
{
    int ret ;
    // 建表
    TestyangallocAllstmtC8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_1, "SubT1Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_1, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_2, "SubT2Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_1, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_2, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_3, "SubT3Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_2, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_3, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_4, "SubT4Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_3, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 9;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_4, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_5, "SubT5Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_4, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_5, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_6, "SubT6Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_5, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_6, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_7, "SubT7Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_6, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_7, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_37.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_8_L_E.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_8_C";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_8_C");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtC8();
    free(FilterJson);
    free(ReturnJson);
}

// c-c-c-c-c-c-c-c 8层单链类型容器过滤 中间起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_038)
{
    int ret ;
    // 建表
    TestyangallocAllstmtC8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_1, "SubT1Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_1, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_2, "SubT2Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_1, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_2, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_3, "SubT3Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_2, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_3, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_4, "SubT4Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_3, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 9;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_4, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_5, "SubT5Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_4, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_5, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_6, "SubT6Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_5, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_6, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_7, "SubT7Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_6, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_7, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_38.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_8_R_M.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_8_C";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_8_C");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtC8();
    free(ReturnJson);
    free(FilterJson);
}

// c-c-c-c-c-c-c-c 8层单链类型容器过滤 末位起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_039)
{
    int ret ;
    // 建表
    TestyangallocAllstmtC8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_1, "SubT1Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_1, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_2, "SubT2Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_1, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_2, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_3, "SubT3Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_2, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_3, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_4, "SubT4Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_3, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 9;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_4, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_5, "SubT5Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_4, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_5, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_6, "SubT6Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_5, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_6, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C8_7, "SubT7Con_8_C", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C8_6, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1Con_F0value = 1;
    testYangSetVertexProperty_T1Con_F0(g_stmt_C8_7, T1Con_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1Con(g_stmt_C8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C8_7);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_39.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_8_R_E.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_8_C";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_8_C");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtC8();
    free(FilterJson);
    free(ReturnJson);
}

// c-choice-case-choice-case-choice-case-choice-case 9层单链类型内容过滤 中间起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_040)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCChCa4();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_ChCa_4.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_ChCa_4_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_1, "SubT1choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_1, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_2, "SubT1Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_1, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_2, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_3, "SubT2choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_2, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_3, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_4, "SubT2Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_3, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_4, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_5, "SubT3choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_4, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_5, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_6, "SubT3Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_5, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_6, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_7, "SubT4choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_6, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_7, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_8, "SubT4Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_7, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_8, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_40.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ChCa_4_C_M.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_ChCa_4";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_ChCa_4");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCChCa4();
    free(FilterJson);
    free(ReturnJson);
}

// c-choice-case-choice-case-choice-case-choice-case 9层单链类型内容过滤 末位起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_041)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCChCa4();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_ChCa_4.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_ChCa_4_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_1, "SubT1choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_1, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_2, "SubT1Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_1, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_2, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_3, "SubT2choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_2, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_3, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_4, "SubT2Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_3, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_4, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_5, "SubT3choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_4, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_5, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_6, "SubT3Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_5, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_6, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_7, "SubT4choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_6, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_7, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_8, "SubT4Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_7, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_8, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_41.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ChCa_4_C_E.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_ChCa_4";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_ChCa_4");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCChCa4();
    free(FilterJson);
    free(ReturnJson);
}

// c-choice-case-choice-case-choice-case-choice-case 9层单链类型叶子过滤 中间起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_042)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCChCa4();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_ChCa_4.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_ChCa_4_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_1, "SubT1choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_1, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_2, "SubT1Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_1, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_2, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_3, "SubT2choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_2, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_3, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_4, "SubT2Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_3, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_4, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_5, "SubT3choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_4, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_5, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_6, "SubT3Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_5, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_6, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_7, "SubT4choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_6, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_7, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_8, "SubT4Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_7, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_8, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_42.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ChCa_4_L_M.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_ChCa_4";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_ChCa_4");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCChCa4();
    free(FilterJson);
    free(ReturnJson);
}

// c-choice-case-choice-case-choice-case-choice-case 9层单链类型叶子过滤 末位起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_043)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCChCa4();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_ChCa_4.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_ChCa_4_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_1, "SubT1choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_1, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_2, "SubT1Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_1, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_2, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_3, "SubT2choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_2, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_3, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_4, "SubT2Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_3, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_4, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_5, "SubT3choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_4, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_5, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_6, "SubT3Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_5, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_6, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_7, "SubT4choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_6, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_7, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_8, "SubT4Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_7, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_8, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_43.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ChCa_4_L_E.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_ChCa_4";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_ChCa_4");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCChCa4();
    free(FilterJson);
    free(ReturnJson);
}

// c-choice-case-choice-case-choice-case-choice-case 9层单链类型容器过滤 中间起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_044)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCChCa4();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_ChCa_4.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_ChCa_4_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_1, "SubT1choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_1, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_2, "SubT1Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_1, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_2, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_3, "SubT2choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_2, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_3, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_4, "SubT2Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_3, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_4, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_5, "SubT3choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_4, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_5, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_6, "SubT3Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_5, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_6, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_7, "SubT4choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_6, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_7, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_8, "SubT4Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_7, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_8, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_44.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ChCa_4_R_M.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_ChCa_4";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_ChCa_4");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCChCa4();
    free(FilterJson);
    free(ReturnJson);
}

// c-choice-case-choice-case-choice-case-choice-case 9层单链类型容器过滤 末位起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_045)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCChCa4();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_ChCa_4.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_ChCa_4_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_1, "SubT1choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_1, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_1);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_2, "SubT1Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_1, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_2, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_2);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_3, "SubT2choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_2, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_3, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_3);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_4, "SubT2Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_3, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_4, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_4);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_5, "SubT3choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_4, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_5, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_5);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_6, "SubT3Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_5, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_6, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_6);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_7, "SubT4choice_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_6, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);
    T1choice_F0value = 1;
    testYangSetVertexProperty_T1choice_F0(g_stmt_C_ChCa_4_7, T1choice_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choice(g_stmt_C_ChCa_4_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_7);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_C_ChCa_4_8, "SubT4Case_C_ChCa_4", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_C_ChCa_4_7, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);
    T1choicecase_F0value = 1;
    testYangSetVertexProperty_T1choicecase_F0(g_stmt_C_ChCa_4_8, T1choicecase_F0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty_T1choicecase(g_stmt_C_ChCa_4_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_C_ChCa_4_8);
    ASSERT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_45.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_ChCa_4_R_E.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_C_ChCa_4";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_C_ChCa_4");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCChCa4();
    free(FilterJson);
    free(ReturnJson);
}

// c-List-List-List-List-List-List-List 8层单链类型内容过滤 中间起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_046)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCL8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_L_8_0", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_1, "SubT0Con_L_8_1", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_1, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_2, "SubT0Con_L_8_2", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_1, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_2, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_3, "SubT0Con_L_8_3", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_2, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_3, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_4, "SubT0Con_L_8_4", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_3, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_4, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_5, "SubT0Con_L_8_5", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_4, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_5, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_6, "SubT0Con_L_8_6", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_5, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_6, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_7, "SubT0Con_L_8_7", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_6, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_7, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_8, "SubT0Con_L_8_8", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_7, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_8, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_46.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_8_C_M.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_L_8_0";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_L_8_0");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCL8();
    free(FilterJson);
    free(ReturnJson);
}

// c-List-List-List-List-List-List-List 8层单链类型内容过滤 末位起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_047)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCL8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_L_8_0", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_1, "SubT0Con_L_8_1", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_1, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_2, "SubT0Con_L_8_2", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_1, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_2, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_3, "SubT0Con_L_8_3", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_2, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_3, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_4, "SubT0Con_L_8_4", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_3, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_4, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_5, "SubT0Con_L_8_5", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_4, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_5, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_6, "SubT0Con_L_8_6", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_5, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_6, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_7, "SubT0Con_L_8_7", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_6, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_7, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_8, "SubT0Con_L_8_8", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_7, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_8, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_47.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_8_C_E.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_L_8_0";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_L_8_0");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCL8();
    free(FilterJson);
    free(ReturnJson);
}

// c-List-List-List-List-List-List-List 8层单链类型叶子过滤 中间起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_048)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCL8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_L_8_0", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_1, "SubT0Con_L_8_1", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_1, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_2, "SubT0Con_L_8_2", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_1, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_2, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_3, "SubT0Con_L_8_3", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_2, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_3, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_4, "SubT0Con_L_8_4", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_3, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_4, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_5, "SubT0Con_L_8_5", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_4, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_5, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_6, "SubT0Con_L_8_6", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_5, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_6, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_7, "SubT0Con_L_8_7", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_6, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_7, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_8, "SubT0Con_L_8_8", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_7, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_8, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_48.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_8_L_M.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_L_8_0";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_L_8_0");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCL8();
    free(FilterJson);
    free(ReturnJson);
}

// c-List-List-List-List-List-List-List 8层单链类型叶子过滤 末位起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_049)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCL8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_L_8_0", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_1, "SubT0Con_L_8_1", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_1, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_2, "SubT0Con_L_8_2", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_1, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_2, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_3, "SubT0Con_L_8_3", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_2, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_3, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_4, "SubT0Con_L_8_4", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_3, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_4, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_5, "SubT0Con_L_8_5", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_4, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_5, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_6, "SubT0Con_L_8_6", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_5, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_6, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_7, "SubT0Con_L_8_7", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_6, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_7, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_8, "SubT0Con_L_8_8", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_7, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_8, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_49.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_8_L_E.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_L_8_0";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_L_8_0");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCL8();
    free(FilterJson);
    free(ReturnJson);
}

// c-List-List-List-List-List-List-List 8层单链类型容器过滤 中间起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_050)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCL8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_L_8_0", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_1, "SubT0Con_L_8_1", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_1, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_2, "SubT0Con_L_8_2", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_1, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_2, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_3, "SubT0Con_L_8_3", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_2, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_3, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_4, "SubT0Con_L_8_4", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_3, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_4, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_5, "SubT0Con_L_8_5", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_4, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_5, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_6, "SubT0Con_L_8_6", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_5, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_6, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_7, "SubT0Con_L_8_7", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_6, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_7, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_8, "SubT0Con_L_8_8", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_7, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_8, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_50.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_8_R_M.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_L_8_0";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_L_8_0");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCL8();
    free(FilterJson);
    free(ReturnJson);
}

// c-List-List-List-List-List-List-List 8层单链类型容器过滤 末位起始位，深度为1查询
TEST_F(Depth_02, Yang_010_002_051)
{
    int ret ;
    // 建表
    TestyangallocAllstmtCL8();
    readJanssonFile("schema/MultiSchema/VertexSchema/C_L_8.gmjson", &g_vertexschema);
    EXPECT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_vertexschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_vertexschema);
    // 建边
    readJanssonFile("schema/MultiSchema/EdgeSchema/C_L_8_Edge.gmjson", &g_edgeschema);
    EXPECT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabel(g_stmt_sync, g_edgeschema, g_labelConfig);
    ASSERT_EQ(GMERR_OK, ret);
    free(g_edgeschema);
    AW_FUN_Log(LOG_STEP, "步骤1.建表");
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.readOnly = false;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    ASSERT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤2.设事务开批量");
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, "SubT0Con_L_8_0", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t pkvalue = 1;
    // stmt句柄 yang操作类型 属性操作类型 属性名称 属性类型 属性值 属相长度
    testYangSetVertexProperty_root_PK(g_stmt_sync_T0, pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    int32_t F1 = 1;
    bool F2 = true;
    double F3 = 13;
    bool F4 = 1;
    float F5 = 16;
    char *F6 = (char *)"string";
    testYangSetVertexProperty(g_stmt_sync_T0, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    ASSERT_EQ(GMERR_OK, ret);

    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_1, "SubT0Con_L_8_1", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_1, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_1, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_1);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_2, "SubT0Con_L_8_2", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_1, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_2, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_2, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_2);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_3, "SubT0Con_L_8_3", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_2, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_3, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_3, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_3);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_4, "SubT0Con_L_8_4", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_3, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_4, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_4, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_4);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_5, "SubT0Con_L_8_5", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_4, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_5, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_5, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_5);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_6, "SubT0Con_L_8_6", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_5, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_6, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_6, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_6);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_7, "SubT0Con_L_8_7", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_6, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_7, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_7, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_7);
        ASSERT_EQ(GMERR_OK, ret);
    }
    for(uint32_t i =1;i<11;i++)
    {
        ret = testGmcPrepareStmtByLabelName(g_stmt_L_8_8, "SubT0Con_L_8_8", GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_L_8_7, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
        // list 主键的值 F0 从1-10 
        uint32_t T1List_pkvalue = i;
        testYangSetVertexProperty_T1List_PK(g_stmt_L_8_8, T1List_pkvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetVertexProperty_T1List(g_stmt_L_8_8, F1, F2, F3, F4, F5, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_L_8_8);
        ASSERT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 依据条件过滤
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "步骤3.插入数据提交事务");
    char *ReturnJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_02_51.json", &ReturnJson);
    EXPECT_NE((void *)NULL, ReturnJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤4.设置回调数据和期望json");
    char *FilterJson = NULL;
    readJanssonFile("FilterReply/C_L_8_R_E.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "SubT0Con_L_8_0";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    char *reply = NULL;
    filter.maxDepth = 2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    AW_FUN_Log(LOG_STEP, "步骤5.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤6.subtree查询和等待响应");
    ret = GmcDropGraphLabel(g_stmt_sync, "SubT0Con_L_8_0");
    ASSERT_EQ(GMERR_OK, ret);
    TestyangfreeAllstmtCL8();
    free(FilterJson);
    free(ReturnJson);
}
