/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : gmdbv5支持乐观事务基本功能测试（V5场景跨namespace）
 Notes        : 001.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行merge，表二执行replace操作
                002.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行merge、表二执行update操作.
                003.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行merge、表二执行delete操作
                004.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行replace、表二执行update操作
                005.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行replace、表二执行delete操作
                006.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行update、表二执行delete操作
                007.创建两个namespace、在namespace中分别创建KV表，分别插入数据，开启乐观事，对表一执行insert again、表二执行delete操作
                008.创建两个namespace、在namespace中分别创建KV表，分别插入数据，开启乐观事，对表一执行delete、表二执行覆盖写操作
                009.创建两个namespace，在namespace中分别创建vertex表（带自增列），分别插入数据。开启乐观事务，表一执行merge、表二执行delete操作
                010.创建两个namespace，在namespace中分别创建vertex表（带自增列），分别插入数据。开启乐观事务，表一执行replace、表二执行update操作
                011.创建两个namespace，在namespace中分别gmimport导表，分别插入数据，开启乐观事务，表一执行delete、表二执行replace操作
                012.创建两个namespace，在namespace中分别gmimport导表，分别插入数据，开启乐观事务，表一执行delete、表二执行update操作
                013.创建两个namespace，在namespace中分别gmimport导KV表，分别插入数据，开启乐观事务，表一执行delete、表二执行insert again操作
                014.创建两个namespace，在namespace中分别gmimport导KV表，分别插入数据，开启乐观事务，表一执行覆盖写、表二执行delete操作
                015.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行DML，表二执行全表扫描操作


 History      :
 Author       : liaoxiang lwx1036939
 Modification : 2022/7/29
*****************************************************************************/
#include "OptimiTrans.h"

#define KVNAME_MAX_LENGTH 128
int g_start_num = 0, g_end_num = 100, affectRows = 0;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char *g_schema = NULL, *g_schema1 = NULL, *g_schema2 = NULL, *g_schema3 = NULL;
char g_pk_name[] = "pk", g_pk_name1[] = "pk1", g_pk_name2[] = "pk2", g_pk_name3[] = "pk3",
    g_pk_name4[] = "pk4", g_pk_name5[] = "pk5";
char g_kv_name[KVNAME_MAX_LENGTH] = "KV0", g_kv_name1[KVNAME_MAX_LENGTH] = "KV1";
const char *g_kv_config = R"(
    {
        "max_record_count":100000,
        "writers":"abc",
        "max_record_count_check":false,
        "isFastReadUncommitted":0
    })";
const char *g_labelname = "OP_T0", *g_labelname1 = "OP_T1", *g_labelname2 = "OP_T2", *g_labelname3 = "OP_T3",
           *g_labelname4 = "OP_T4", *g_labelname5 = "OP_T5";
const char *g_namespace = (const char *)"userA01205", *g_namespace2 = (const char *)"userB01205";
const char *g_namespaceUserName = (const char *)"abc";

class OptTra_FunV5Dif : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
    }
};

void OptTra_FunV5Dif::SetUp()
{
    // 创建同步连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建 namespace 1
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    GmcDropNamespace(g_stmt, g_namespace);
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建 namespace 2
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg2;
    nspCfg2.tablespaceName = NULL;
    nspCfg2.namespaceName = g_namespace2;
    nspCfg2.userName = g_namespaceUserName;
    nspCfg2.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    GmcDropNamespace(g_stmt, g_namespace2);
    ret = GmcCreateNamespaceWithCfg(g_stmt, &nspCfg2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // namespace 1 中创建 表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schemafile/V5/NormalVertexLabel_001.gmjson", &g_schema);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema);

    GmcDropVertexLabel(g_stmt, g_labelname);
    ret = GmcCreateVertexLabel(g_stmt, g_schema, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema);

    // namespace 2 中创建 表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schemafile/V5/NormalVertexLabel_002.gmjson", &g_schema1);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema1);

    GmcDropVertexLabel(g_stmt, g_labelname1);
    ret = GmcCreateVertexLabel(g_stmt, g_schema1, g_tabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema1);
    AW_CHECK_LOG_BEGIN();
}

void OptTra_FunV5Dif::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    // 删除 namespace 1 中的表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelname);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 2 中的表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelname1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 1
    ret = GmcDropNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 2
    ret = GmcDropNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行merge，表二执行replace操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_001)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 向 namespace 1 中的表1 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表1 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname, g_pk_name);
    }

    // 向 namespace 2 中的表2 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表2 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname1, g_pk_name1);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的表2 执行 replace && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否replace成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname1, g_pk_name1);
    }

    // 事务跨namespace操作
    // 向 namespace 1 中的表1 执行 merge && query
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t g_pk_value = i;
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &g_pk_value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否merge成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname, g_pk_name);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 002.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行merge、表二执行update操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_002)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 向 namespace 1 中的表1 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表1 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname, g_pk_name);
    }

    // 向 namespace 2 中的表2 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表2 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname1, g_pk_name1);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的表2 执行 update && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否update成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname1, g_pk_name1);
    }

    // 事务跨namespace操作
    // 向 namespace 1 中的表1 执行 merge && query
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t g_pk_value = i;
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &g_pk_value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否merge成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname, g_pk_name);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 003.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行merge、表二执行delete操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_003)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 向 namespace 1 中的表1 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表1 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname, g_pk_name);
    }

    // 向 namespace 2 中的表2 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表2 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname1, g_pk_name1);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的表2 执行 delete && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 主键删除
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否delete成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(isFinish, true);
    }

    // 事务跨namespace操作
    // 向 namespace 1 中的表1 执行 merge && query
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t g_pk_value = i;
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &g_pk_value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否merge成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname, g_pk_name);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 004.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行replace、表二执行update操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_004)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 向 namespace 1 中的表1 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表1 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname, g_pk_name);
    }

    // 向 namespace 2 中的表2 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表2 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname1, g_pk_name1);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的表2 执行 update && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否update成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname1, g_pk_name1);
    }

    // 事务跨namespace操作
    // 向 namespace 1 中的表1 执行 replace && query
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否replace成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname, g_pk_name);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 005.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行replace、表二执行delete操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_005)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 向 namespace 1 中的表1 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表1 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname, g_pk_name);
    }

    // 向 namespace 2 中的表2 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表2 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname1, g_pk_name1);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的表2 执行 delete && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 主键删除
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否delete成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(isFinish, true);
    }

    // 事务跨namespace操作
    // 向 namespace 1 中的表1 执行 replace && query
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否replace成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname, g_pk_name);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 006.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行update、表二执行delete操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_006)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 向 namespace 1 中的表1 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表1 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname, g_pk_name);
    }

    // 向 namespace 2 中的表2 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表2 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname1, g_pk_name1);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的表2 执行 delete && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 主键删除
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否delete成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(isFinish, true);
    }

    // 事务跨namespace操作
    // 向 namespace 1 中的表1 执行 update && query
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否update成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname, g_pk_name);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 007.创建两个namespace、在namespace中分别创建KV表，分别插入数据，开启乐观事，对表一执行insert again、表二执行delete操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_007)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch, *batch2;
    GmcBatchRetT batchRet, batchRet2;
    GmcBatchOptionT batchOption, batchOption2;

    // namespace 1 中创建 kv表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // create KvTable 1
    GmcKvDropTable(g_stmt, g_kv_name);
    ret = GmcKvCreateTable(g_stmt, g_kv_name, g_kv_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表1 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表1 batch set
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set, strlen(key_set), &value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_EXPECT_EQ_INT(successNum, (uint32_t)g_end_num);

    // namespace 2 中创建 kv表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // create KvTable 2
    GmcKvDropTable(g_stmt, g_kv_name1);
    ret = GmcKvCreateTable(g_stmt, g_kv_name1, g_kv_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表2 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表2 batch set
    ret = GmcBatchOptionInit(&batchOption2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption2, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption2, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvTupleT kvInfo2 = {0};
    char key_set2[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set2, "zhangsan%d", i);
        int32_t value2 = i;
        kvInfo2.key = key_set2;
        kvInfo2.keyLen = strlen(key_set2);
        kvInfo2.value = &value2;
        kvInfo2.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set2, strlen(key_set2), &value2, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch2, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch2, &batchRet2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet2, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_EXPECT_EQ_INT(successNum, (uint32_t)g_end_num);

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的 kv表2 执行 delete
    ret = GmcBatchPrepare(g_conn, &batchOption2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char key_delete[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_delete, "zhangsan%d", i);
        int32_t value = i;
        kvInfo2.key = key_delete;
        kvInfo2.keyLen = strlen(key_delete);
        kvInfo2.valueLen = 0;
        ret = GmcKvInputToStmt(g_stmt, key_delete, strlen(key_delete), NULL, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch2, g_stmt, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch2, &batchRet2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet2, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_ASSERT_EQ_INT(successNum, (uint32_t)g_end_num);

    // 事务跨namespace操作
    // 向 namespace 1 中的 kv表1 执行 insert again
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表1 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = g_start_num + g_end_num; i < g_end_num * 2; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set, strlen(key_set), &value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_ASSERT_EQ_INT(successNum, (uint32_t)g_end_num);

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 1 中的 kv表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 2 中的 kv表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_kv_name1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 008.创建两个namespace、在namespace中分别创建KV表，分别插入数据，开启乐观事，对表一执行delete、表二执行覆盖写操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_008)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch, *batch2;
    GmcBatchRetT batchRet, batchRet2;
    GmcBatchOptionT batchOption, batchOption2;

    // namespace 1 中创建 kv表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // create KvTable 1
    GmcKvDropTable(g_stmt, g_kv_name);
    ret = GmcKvCreateTable(g_stmt, g_kv_name, g_kv_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表1 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表1 batch set
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set, strlen(key_set), &value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_EXPECT_EQ_INT(successNum, (uint32_t)g_end_num);

    // namespace 2 中创建 kv表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // create KvTable 2
    GmcKvDropTable(g_stmt, g_kv_name1);
    ret = GmcKvCreateTable(g_stmt, g_kv_name1, g_kv_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表2 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表2 batch set
    ret = GmcBatchOptionInit(&batchOption2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption2, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption2, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvTupleT kvInfo2 = {0};
    char key_set2[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set2, "zhangsan%d", i);
        int32_t value2 = i;
        kvInfo2.key = key_set2;
        kvInfo2.keyLen = strlen(key_set2);
        kvInfo2.value = &value2;
        kvInfo2.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set2, strlen(key_set2), &value2, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch2, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch2, &batchRet2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet2, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_EXPECT_EQ_INT(successNum, (uint32_t)g_end_num);

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的 kv表2 执行 覆盖写
    ret = GmcBatchPrepare(g_conn, &batchOption2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set2, "zhangsan%d", i);
        int32_t value3 = i + g_end_num;
        kvInfo2.key = key_set2;
        kvInfo2.keyLen = strlen(key_set2);
        kvInfo2.value = &value3;
        kvInfo2.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set2, strlen(key_set2), &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch2, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch2, &batchRet2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet2, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_EXPECT_EQ_INT(successNum, (uint32_t)g_end_num);

    // 事务跨namespace操作
    // 向 namespace 1 中的 kv表1 执行 delete
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表1 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char key_delete[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_delete, "zhangsan%d", i);
        int32_t value = i;
        kvInfo.key = key_delete;
        kvInfo.keyLen = strlen(key_delete);
        kvInfo.valueLen = 0;
        ret = GmcKvInputToStmt(g_stmt, key_delete, strlen(key_delete), NULL, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_ASSERT_EQ_INT(successNum, (uint32_t)g_end_num);

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 1 中的 kv表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 2 中的 kv表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_kv_name1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 009.创建两个namespace，在namespace中分别创建vertex表（带自增列），分别插入数据。开启乐观事务，表一执行merge、表二执行delete操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_009)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";
    const char *g_label_config2 = R"(
        {
            "max_record_count":1000,
            "writers":"efg",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";

    // namespace 1 中创建 表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建 表1
    readJanssonFile("schemafile/V5/AutoIncrement_001.gmjson", &g_schema2);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema2);

    // 主键带自增列
    GmcDropVertexLabel(g_stmt, g_labelname2);
    ret = GmcCreateVertexLabel(g_stmt, g_schema2, g_label_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema2);

    // 表1 insert
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname2, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    }

    // namespace 2 中创建 表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建 表 2
    readJanssonFile("schemafile/V5/AutoIncrement_002.gmjson", &g_schema3);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema3);

    // 主键带自增列
    GmcDropVertexLabel(g_stmt, g_labelname3);
    ret = GmcCreateVertexLabel(g_stmt, g_schema3, g_label_config2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema3);

    // 表2 insert
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname3, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的 表2 执行 delete
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname3, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 主键删除
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname3, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(isFinish, true);
    }

    // 事务跨namespace操作
    // 向 namespace 1 中的 表1 执行 merge // 20230905 自增列作为主键的表不允许执行merge操作
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname2, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 1 中的 表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelname2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 2 中的 表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelname3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 010.创建两个namespace，在namespace中分别创建vertex表（带自增列），分别插入数据。开启乐观事务，表一执行replace、表二执行update操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_010)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";
    const char *g_label_config2 = R"(
        {
            "max_record_count":1000,
            "writers":"efg",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";

    // namespace 1 中创建 表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建 表1
    readJanssonFile("schemafile/V5/AutoIncrement_001.gmjson", &g_schema2);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema2);

    // 主键带自增列
    GmcDropVertexLabel(g_stmt, g_labelname2);
    ret = GmcCreateVertexLabel(g_stmt, g_schema2, g_label_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema2);

    // 表1 insert
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname2, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    }

    // namespace 2 中创建 表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建 表 2
    readJanssonFile("schemafile/V5/AutoIncrement_002.gmjson", &g_schema3);
    AW_MACRO_ASSERT_NE_INT((void *)NULL, g_schema3);

    // 主键带自增列
    GmcDropVertexLabel(g_stmt, g_labelname3);
    ret = GmcCreateVertexLabel(g_stmt, g_schema3, g_label_config2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_schema3);

    // 表2 insert
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname3, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的 表2 执行 replace
    for (int i = g_start_num + g_end_num; i < g_end_num * 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname3, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 事务跨namespace操作
    // 向 namespace 1 中的 表1 执行 update
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname2, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 1 中的 表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelname2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 2 中的 表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelname3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 011.创建两个namespace，在namespace中分别gmimport导表，分别插入数据，开启乐观事务，表一执行delete、表二执行replace操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_011)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    char schema_file[128] = "schemafile/V5/NormalVertexLabel_003.gmjson";
    char schema_file1[128] = "schemafile/V5/NormalVertexLabel_004.gmjson";
    char g_command[512];
    char g_command1[512];

    // namespace 1 中创建 表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // gmimport 导表 1
    GmcDropVertexLabel(g_stmt, g_labelname4);
    snprintf(g_command, 512, "%s/gmimport -c vschema -f %s -ns %s -s %s", g_toolPath, schema_file,
        g_namespace, g_connServer);
    system(g_command);

    // 表1 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname4, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname4, g_pk_name3);
    }

    // namespace 2 中创建 表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // gmimport 导表 2
    GmcDropVertexLabel(g_stmt, g_labelname5);
    snprintf(g_command1, 512, "%s/gmimport -c vschema -f %s -ns %s -s %s", g_toolPath, schema_file1,
        g_namespace2, g_connServer);
    system(g_command1);

    // 表2 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname5, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname5, g_pk_name4);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的 表2 执行 replace
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname5, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否replace成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname5, g_pk_name4);
    }

    // 事务跨namespace操作
    // 向 namespace 1 中的 表1 执行 delete
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname4, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 主键删除
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否delete成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname4, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(isFinish, true);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 1 中的 表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelname4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 2 中的 表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelname5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 012.创建两个namespace，在namespace中分别gmimport导表，分别插入数据，开启乐观事务，表一执行delete、表二执行update操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_012)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    char schema_file[128] = "schemafile/V5/NormalVertexLabel_003.gmjson";
    char schema_file1[128] = "schemafile/V5/NormalVertexLabel_004.gmjson";
    char g_command[512];
    char g_command1[512];

    // namespace 1 中创建 表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // gmimport 导表 1
    GmcDropVertexLabel(g_stmt, g_labelname4);
    snprintf(g_command, 512, "%s/gmimport -c vschema -f %s -ns %s -s %s", g_toolPath, schema_file,
        g_namespace, g_connServer);
    system(g_command);

    // 表1 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname4, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname4, g_pk_name3);
    }

    // namespace 2 中创建 表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // gmimport 导表 2
    GmcDropVertexLabel(g_stmt, g_labelname5);
    snprintf(g_command1, 512, "%s/gmimport -c vschema -f %s -ns %s -s %s", g_toolPath, schema_file1,
        g_namespace2, g_connServer);
    system(g_command1);

    // 表2 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname5, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname5, g_pk_name4);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的 表2 执行 update
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname5, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否update成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname5, g_pk_name4);
    }

    // 事务跨namespace操作
    // 向 namespace 1 中的 表1 执行 delete
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname4, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 主键删除
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否delete成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 检查所删除
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname4, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(isFinish, true);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 1 中的 表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelname4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 2 中的 表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelname5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 013.创建两个namespace，在namespace中分别gmimport导KV表，分别插入数据，开启乐观事务，表一执行delete、表二执行insert again操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_013)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch, *batch2;
    GmcBatchRetT batchRet, batchRet2;
    GmcBatchOptionT batchOption, batchOption2;
    char schema_file[128] = "schemafile/V5/gmimport_Kvtabel_001.gmconfig";
    char schema_file1[128] = "schemafile/V5/gmimport_Kvtabel_002.gmconfig";
    char g_command[512];
    char g_command1[512];

    // namespace 1 中创建 kv表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // gmimport 导kv表1
    GmcKvDropTable(g_stmt, g_kv_name);
    snprintf(g_command, 512, "%s/gmimport -c kvtable -f %s -t %s -ns %s -s %s", g_toolPath, schema_file,
        g_kv_name, g_namespace, g_connServer);
    system(g_command);

    // kv表1 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表1 batch set
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set, strlen(key_set), &value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_EXPECT_EQ_INT(successNum, (uint32_t)g_end_num);

    // namespace 2 中创建 kv表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // gmimport 导kv表2
    GmcKvDropTable(g_stmt, g_kv_name1);
    snprintf(g_command1, 512, "%s/gmimport -c kvtable -f %s -t %s -ns %s -s %s", g_toolPath, schema_file1,
        g_kv_name1, g_namespace2, g_connServer);
    system(g_command1);

    // kv表2 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表2 batch set
    ret = GmcBatchOptionInit(&batchOption2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption2, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption2, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvTupleT kvInfo2 = {0};
    char key_set2[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set2, "zhangsan%d", i);
        int32_t value2 = i;
        kvInfo2.key = key_set2;
        kvInfo2.keyLen = strlen(key_set2);
        kvInfo2.value = &value2;
        kvInfo2.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set2, strlen(key_set2), &value2, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch2, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch2, &batchRet2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet2, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_EXPECT_EQ_INT(successNum, (uint32_t)g_end_num);

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的 kv表2 执行 insert again
    ret = GmcBatchPrepare(g_conn, &batchOption2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = g_start_num + g_end_num; i < g_end_num * 2; i++) {
        sprintf(key_set2, "zhangsan%d", i);
        int32_t value3 = i;
        kvInfo2.key = key_set2;
        kvInfo2.keyLen = strlen(key_set2);
        kvInfo2.value = &value3;
        kvInfo2.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set2, strlen(key_set2), &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch2, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch2, &batchRet2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet2, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_ASSERT_EQ_INT(successNum, (uint32_t)g_end_num);

    // 事务跨namespace操作
    // 向 namespace 1 中的 kv表1 执行 delete
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表1 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char key_delete[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_delete, "zhangsan%d", i);
        int32_t value4 = i;
        kvInfo.key = key_delete;
        kvInfo.keyLen = strlen(key_delete);
        kvInfo.valueLen = 0;
        ret = GmcKvInputToStmt(g_stmt, key_delete, strlen(key_delete), NULL, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_ASSERT_EQ_INT(successNum, (uint32_t)g_end_num);


    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 1 中的 kv表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 2 中的 kv表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_kv_name1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 014.创建两个namespace，在namespace中分别gmimport导KV表，分别插入数据，开启乐观事务，表一执行覆盖写、表二执行delete操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_014)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    GmcBatchT *batch, *batch2;
    GmcBatchRetT batchRet, batchRet2;
    GmcBatchOptionT batchOption, batchOption2;
    char schema_file[128] = "schemafile/V5/gmimport_Kvtabel_001.gmconfig";
    char schema_file1[128] = "schemafile/V5/gmimport_Kvtabel_002.gmconfig";
    char g_command[512];
    char g_command1[512];


    // namespace 1 中创建 kv表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // gmimport 导kv表1
    GmcKvDropTable(g_stmt, g_kv_name);
    snprintf(g_command, 512, "%s/gmimport -c kvtable -f %s -t %s -ns %s -s %s", g_toolPath, schema_file,
        g_kv_name, g_namespace, g_connServer);
    system(g_command);

    // kv表1 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表1 batch set
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvTupleT kvInfo = {0};
    char key_set[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value = i;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set, strlen(key_set), &value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_EXPECT_EQ_INT(successNum, (uint32_t)g_end_num);

    // namespace 2 中创建 kv表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // gmimport 导kv表2
    GmcKvDropTable(g_stmt, g_kv_name1);
    snprintf(g_command1, 512, "%s/gmimport -c kvtable -f %s -t %s -ns %s -s %s", g_toolPath, schema_file1,
        g_kv_name1, g_namespace2, g_connServer);
    system(g_command1);

    // kv表2 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表2 batch set
    ret = GmcBatchOptionInit(&batchOption2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption2, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption2, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvTupleT kvInfo2 = {0};
    char key_set2[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set2, "zhangsan%d", i);
        int32_t value2 = i;
        kvInfo2.key = key_set2;
        kvInfo2.keyLen = strlen(key_set2);
        kvInfo2.value = &value2;
        kvInfo2.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set2, strlen(key_set2), &value2, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch2, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch2, &batchRet2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet2, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_EXPECT_EQ_INT(successNum, (uint32_t)g_end_num);

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的 kv表2 执行 delete
    char key_delete[1024];
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_delete, "zhangsan%d", i);
        int32_t value4 = i;
        kvInfo2.key = key_delete;
        kvInfo2.keyLen = strlen(key_delete);
        kvInfo2.valueLen = 0;
        ret = GmcKvInputToStmt(g_stmt, key_delete, strlen(key_delete), NULL, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch2, g_stmt, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch2, &batchRet2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet2, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_ASSERT_EQ_INT(successNum, (uint32_t)g_end_num);

    // 事务跨namespace操作
    // 向 namespace 1 中的 kv表1 执行 覆盖写
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // kv表1 获取kv
    ret = GmcKvPrepareStmtByLabelName(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = g_start_num; i < g_end_num; i++) {
        sprintf(key_set, "zhangsan%d", i);
        int32_t value = i + g_end_num;
        kvInfo.key = key_set;
        kvInfo.keyLen = strlen(key_set);
        kvInfo.value = &value;
        kvInfo.valueLen = sizeof(int32_t);
        ret = GmcKvInputToStmt(g_stmt, key_set, strlen(key_set), &value, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddKvDML(batch, g_stmt, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, (uint32_t)g_end_num);
    AW_MACRO_EXPECT_EQ_INT(successNum, (uint32_t)g_end_num);

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 1 中的 kv表1
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_kv_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除 namespace 2 中的 kv表2
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvDropTable(g_stmt, g_kv_name1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

// 015.创建两个namespace，在namespace中分别创建vertex表，分别插入数据。开启乐观事务，对表一执行DML，表二执行全表扫描操作
TEST_F(OptTra_FunV5Dif, Yang_012_FunV5DiffNs_015)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;

    // 向 namespace 1 中的表1 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表1 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname, g_pk_name);
    }

    // 向 namespace 2 中的表2 插入数据
    ret = GmcUseNamespace(g_stmt, g_namespace2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 表2 insert && query
    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否插入成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i, g_labelname1, g_pk_name1);
    }

    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向 namespace 2 中的表2 执行 全表扫描
    // 全表扫描
    for (int i = g_start_num; i < g_end_num; i++) {
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname1, g_pk_name1);
    }

    // 事务跨namespace操作
    // 向 namespace 1 中的表1 执行 merge && query
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = g_start_num; i < g_end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelname, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t g_pk_value = i;
        ret = GmcSetIndexKeyName(g_stmt, g_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &g_pk_value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(g_stmt, i + g_end_num);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // 校验数据是否merge成功
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, affectRows);

        // 读
        QueryVertexProperty(g_stmt, i + g_end_num, g_labelname, g_pk_name);
    }

    // 提交事务
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "END");
}

