#include "tools.h"

class yang_compatible_interface : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
public:
    virtual void SetUp();
    virtual void TearDown();
};

void yang_compatible_interface::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"auditLogEnableDML=1\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableDmlOperStat=1\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void yang_compatible_interface::TearDownTestCase(){}

void yang_compatible_interface::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步建连
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    TryDropNameSpace(g_stmtAsync, g_namespace);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmtAsync, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AllocAllStmts();

    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_NO_DATA);
}

void yang_compatible_interface::TearDown()
{
    int ret;
    AsyncUserDataT data = {0};
    // 异步删除namespace
    TryDropNameSpace(g_stmtAsync, g_namespace);
    // 断连
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    GmcDetachAllShmSeg();
    testEnvClean();
}

// 对yang表使用GmcDeleteAllFast
TEST_F(yang_compatible_interface, Yang_095_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel1.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtConRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtConRoot, &g_nodeConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container/root/F1的值
    uint32_t valueConRootF1 = 100;
    ret = SetNodeProperty(g_nodeConRoot, GMC_DATATYPE_UINT32, &valueConRootF1, sizeof(uint32_t),
        "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置container/P_container节点
    GmcNodeT *conPNode = NULL;
    ret = GmcYangEditChildNode(g_nodeConRoot, "P_container", GMC_OPERATION_INSERT, &conPNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container/P_container/P_F1的值
    uint32_t valueConPF1 = 101;
    ret = SetNodeProperty(conPNode, GMC_DATATYPE_UINT32, &valueConPF1, sizeof(uint32_t),
        "P_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container/NP_contianer节点
    GmcNodeT *conNPNode = NULL;
    ret = GmcYangEditChildNode(g_nodeConRoot, "NP_container", GMC_OPERATION_INSERT, &conNPNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container/NP_container/NP_F1的值
    uint32_t valueConNPF1 = 102;
    ret = SetNodeProperty(conNPNode, GMC_DATATYPE_UINT32, &valueConNPF1, sizeof(uint32_t),
        "NP_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container/choice_1节点
    GmcNodeT *conChoiceNode = NULL;
    ret = GmcYangEditChildNode(g_nodeConRoot, "choice_1", GMC_OPERATION_INSERT, &conChoiceNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container/choice_1/case_1_1节点
    GmcNodeT *conCaseNode = NULL;
    ret = GmcYangEditChildNode(conChoiceNode, "case_1_1", GMC_OPERATION_INSERT, &conCaseNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container/choice_1/case_1_1/case_1_1_F1的值
    uint32_t valueConCase1N1F1 = 103;
    ret = SetNodeProperty(conCaseNode, GMC_DATATYPE_UINT32, &valueConCase1N1F1, sizeof(uint32_t),
        "case_1_1_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/1_reply.json");

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDeleteAllFast(stmt, "main_label");
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/1_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 对yang表使用GmcGetOperStatsCnt
TEST_F(yang_compatible_interface, Yang_095_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgeLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangDML();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/sensitive_2.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查询变更记录数
    int maxLabelCnt = 100;
    uint64_t count[maxLabelCnt];
    uint32_t labelCnt = 3;
    const char *labelNames[maxLabelCnt] = {};
    labelNames[0] = "main_label";
    labelNames[1] = "list_label";
    labelNames[2] = "leaf-list_label";
    ret = GmcGetOperStatsCnt(stmt, labelNames, GMC_STATISTICS_TYPE_INSERT, count, labelCnt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, count[0]);
    AW_MACRO_EXPECT_EQ_INT(3, count[1]);
    AW_MACRO_EXPECT_EQ_INT(3, count[2]);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 对yang表使用GmcGetVertexCount
TEST_F(yang_compatible_interface, Yang_095_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgeLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangDML();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/2_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, "main_label", NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, count);

    ret = GmcGetVertexCount(stmt, "list_label", NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, count);

    ret = GmcGetVertexCount(stmt, "leaf-list_label", NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, count);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 对yang表使用GmcGetVertexRecordCount
TEST_F(yang_compatible_interface, Yang_095_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgeLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangDML();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/2_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t count = 0;
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, count);
    
    ret = GmcPrepareStmtByLabelName(stmt, "list_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, count);

    ret = GmcPrepareStmtByLabelName(stmt, "leaf-list_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, count);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 对yang表使用GmcGetVertexRecordCount(正确)
TEST_F(yang_compatible_interface, Yang_095_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgeLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangDML();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/2_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "main_label", 0, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t count = 0;
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, count);
    
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "list_label", 0, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, count);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "leaf-list_label", 0, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, count);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 对yang表使用GmcGetVertexRecordCount(错误)
TEST_F(yang_compatible_interface, Yang_095_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgeLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangDML();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/2_reply.json");
    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "main_label", 1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "list_label", 1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, "leaf-list_label", 1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 对yang表使用GmcSetSuperfieldById
TEST_F(yang_compatible_interface, Yang_095_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgeLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtConRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtConRoot, &g_nodeConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container/root/F1的值
    uint32_t valueConRootF1 = 100;
    ret = SetNodeProperty(g_nodeConRoot, GMC_DATATYPE_UINT32, &valueConRootF1, sizeof(uint32_t),
        "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置superfield的值
    typedef struct superFieldRoot {
        uint32_t rootF2;
        uint32_t rootF3;
    } superFieldRootT;
    superFieldRootT sFCon = {1000, 1001};
    ret = GmcSetSuperfieldById(g_stmtConRoot, 0, &sFCon, sizeof(sFCon));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        // list设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmtListRoot, "list_label", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtListRoot, &g_nodeListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list/root/F1的值
        uint32_t valueListRootF1 = 200 + i;
        ret = SetNodeProperty(g_nodeListRoot, GMC_DATATYPE_UINT32, &valueListRootF1, sizeof(uint32_t),
            "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 设置superfield的值
        superFieldRootT sFList = {1002, 1003};
        ret = GmcSetSuperfieldById(g_stmtListRoot, 0, &sFList, sizeof(sFList));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // leaf-list设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtLeafList, &g_nodeLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置leaf-list/root/F1的值
        uint32_t valueLeafListRootF1 = 300 + i;
        ret = SetNodeProperty(g_nodeLeafList, GMC_DATATYPE_UINT32, &valueLeafListRootF1, sizeof(uint32_t),
            "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/9_reply.json");

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isFinish = false;
    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int fetchRootCnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t *spRootGet = (uint32_t *)malloc(8);
        ret = GmcGetSuperfieldById(stmt, 0, spRootGet, 8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1000, *(uint32_t *)spRootGet);
        AW_MACRO_EXPECT_EQ_INT(1001, *(uint32_t *)(spRootGet + 1));
        free(spRootGet);
        fetchRootCnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(1, fetchRootCnt);

    ret = GmcPrepareStmtByLabelName(stmt, "list_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int fetchListCnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t *spListGet = (uint32_t *)malloc(8);
        ret = GmcGetSuperfieldById(stmt, 0, spListGet, 8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1002, *(uint32_t *)spListGet);
        AW_MACRO_EXPECT_EQ_INT(1003, *(uint32_t *)(spListGet + 1));
        free(spListGet);
        fetchListCnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(3, fetchListCnt);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 对yang表使用GmcSetSuperfieldByName
TEST_F(yang_compatible_interface, Yang_095_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgeLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtConRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtConRoot, &g_nodeConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container/root/F1的值
    uint32_t valueConRootF1 = 100;
    ret = SetNodeProperty(g_nodeConRoot, GMC_DATATYPE_UINT32, &valueConRootF1, sizeof(uint32_t),
        "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置superfield的值
    typedef struct superFieldRoot {
        uint32_t rootF2;
        uint32_t rootF3;
    } superFieldRootT;
    superFieldRootT sFCon = {1000, 1001};
    ret = GmcSetSuperfieldByName(g_stmtConRoot, "superfieldRoot", &sFCon, sizeof(sFCon));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        // list设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmtListRoot, "list_label", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtListRoot, &g_nodeListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list/root/F1的值
        uint32_t valueListRootF1 = 200 + i;
        ret = SetNodeProperty(g_nodeListRoot, GMC_DATATYPE_UINT32, &valueListRootF1, sizeof(uint32_t),
            "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 设置superfield的值
        superFieldRootT sFList = {1002, 1003};
        ret = GmcSetSuperfieldByName(g_stmtListRoot, "superfieldList", &sFList, sizeof(sFList));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // leaf-list设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtLeafList, &g_nodeLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置leaf-list/root/F1的值
        uint32_t valueLeafListRootF1 = 300 + i;
        ret = SetNodeProperty(g_nodeLeafList, GMC_DATATYPE_UINT32, &valueLeafListRootF1, sizeof(uint32_t),
            "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/9_reply.json");

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isFinish = false;
    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int fetchRootCnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t *spRootGet = (uint32_t *)malloc(8);
        ret = GmcGetSuperfieldByName(stmt, "superfieldRoot", 8, spRootGet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1000, *(uint32_t *)spRootGet);
        AW_MACRO_EXPECT_EQ_INT(1001, *(uint32_t *)(spRootGet + 1));
        free(spRootGet);
        fetchRootCnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(1, fetchRootCnt);

    ret = GmcPrepareStmtByLabelName(stmt, "list_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int fetchListCnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t *spListGet = (uint32_t *)malloc(8);
        ret = GmcGetSuperfieldByName(stmt, "superfieldList", 8, spListGet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1002, *(uint32_t *)spListGet);
        AW_MACRO_EXPECT_EQ_INT(1003, *(uint32_t *)(spListGet + 1));
        free(spListGet);
        fetchListCnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(3, fetchListCnt);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 对yang表使用GmcSetVertexProperty
TEST_F(yang_compatible_interface, Yang_095_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgeLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtConRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtConRoot, &g_nodeConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container/root/F1的值
    uint32_t valueConRootF1 = 100;
    ret = SetNodeProperty(g_nodeConRoot, GMC_DATATYPE_UINT32, &valueConRootF1, sizeof(uint32_t),
        "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 用GmcSetVertexProperty设置root_F2的值
    uint32_t valueConRootF2 = 101;
    ret = GmcSetVertexProperty(g_stmtConRoot, "root_F2", GMC_DATATYPE_UINT32, &valueConRootF2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        // list设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmtListRoot, "list_label", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtListRoot, &g_nodeListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list/root/F1的值
        uint32_t valueListRootF1 = 200 + i;
        ret = SetNodeProperty(g_nodeListRoot, GMC_DATATYPE_UINT32, &valueListRootF1, sizeof(uint32_t),
            "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 用GmcSetVertexProperty设置root_F2的值
        uint32_t valueListRootF2 = 201;
        ret = GmcSetVertexProperty(g_stmtListRoot, "root_F2", GMC_DATATYPE_UINT32, &valueListRootF2, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // leaf-list设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtLeafList, &g_nodeLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置leaf-list/root/F1的值
        uint32_t valueLeafListRootF1 = 300 + i;
        ret = SetNodeProperty(g_nodeLeafList, GMC_DATATYPE_UINT32, &valueLeafListRootF1, sizeof(uint32_t),
            "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 用GmcSetVertexProperty设置root_F1的值
        uint32_t valueLeafListRootF2 = 301;
        ret = GmcSetVertexProperty(g_stmtLeafList, "root_F1", GMC_DATATYPE_UINT32, &valueLeafListRootF2, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/11_reply.json");

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 对yang表使用GmcSetVertexPropertyById
TEST_F(yang_compatible_interface, Yang_095_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgeLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    GmcBatchT *batch = NULL;
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtConRoot, "main_label", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtConRoot, &g_nodeConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置container/root/F1的值
    uint32_t valueConRootF1 = 100;
    ret = SetNodeProperty(g_nodeConRoot, GMC_DATATYPE_UINT32, &valueConRootF1, sizeof(uint32_t),
        "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 用GmcSetVertexPropertyById设置root_F2的值
    uint32_t valueConRootF2 = 101;
    ret = GmcSetVertexPropertyById(g_stmtConRoot, 2, GMC_DATATYPE_UINT32, &valueConRootF2, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtConRoot);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点和其属性值
    for (int i = 0; i < 3; i++) {
        // list设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmtListRoot, "list_label", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtListRoot, &g_nodeListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置list/root/F1的值
        uint32_t valueListRootF1 = 200 + i;
        ret = SetNodeProperty(g_nodeListRoot, GMC_DATATYPE_UINT32, &valueListRootF1, sizeof(uint32_t),
            "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 用GmcSetVertexPropertyById设置root_F2的值
        uint32_t valueListRootF2 = 201;
        ret = GmcSetVertexPropertyById(g_stmtListRoot, 3, GMC_DATATYPE_UINT32, &valueListRootF2, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtListRoot);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // leaf-list设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmtLeafList, "leaf-list_label", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtConRoot, g_stmtLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtLeafList, &g_nodeLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置leaf-list/root/F1的值
        uint32_t valueLeafListRootF1 = 300 + i;
        ret = SetNodeProperty(g_nodeLeafList, GMC_DATATYPE_UINT32, &valueLeafListRootF1, sizeof(uint32_t),
            "root_F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 用GmcSetVertexPropertyById设置root_F1的值
        uint32_t valueLeafListRootF2 = 301;
        ret = GmcSetVertexPropertyById(g_stmtLeafList, 2, GMC_DATATYPE_UINT32, &valueLeafListRootF2, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtLeafList);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);

    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/11_reply.json");

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// GmcGetVertexCount的事务隔离
TEST_F(yang_compatible_interface, Yang_095_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgeLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangDML();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/2_reply.json");

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 异步建连
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, "main_label", NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    ret = GmcGetVertexCount(stmt, "list_label", NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    ret = GmcGetVertexCount(stmt, "leaf-list_label", NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, "main_label", NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, count);

    ret = GmcGetVertexCount(stmt, "list_label", NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, count);

    ret = GmcGetVertexCount(stmt, "leaf-list_label", NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, count);

    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// GmcGetVertexRecordCount的事务隔离
TEST_F(yang_compatible_interface, Yang_095_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    ret = CreateVertexLabelAsync(g_stmtAsync, "schemaFile/vertexLabel3.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CreateEdgeLabelAsync(g_stmtAsync, "schemaFile/edgeLabel2.gmjson");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ModelCheck();

    // 启动事务
    ret = TestTransStartAsync(g_connAsync, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    YangDML();
    // subtree查询
    TestSubtreeFilter(g_stmtAsync, "main_label", "subtreeReplyJson/2_reply.json");

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // 异步建连
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespace(stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t count = 0;
    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    
    ret = GmcPrepareStmtByLabelName(stmt, "list_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    ret = GmcPrepareStmtByLabelName(stmt, "leaf-list_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    // 提交事务
    ret = TestTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = GmcTransStart(conn, &g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcPrepareStmtByLabelName(stmt, "main_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, count);
    
    ret = GmcPrepareStmtByLabelName(stmt, "list_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, count);

    ret = GmcPrepareStmtByLabelName(stmt, "leaf-list_label", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetVertexRecordCount(stmt, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(3, count);

    // 提交事务
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
