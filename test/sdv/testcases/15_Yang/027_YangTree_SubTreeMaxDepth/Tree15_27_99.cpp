/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 027_YangTree_SubTreeMaxDepth
 * Author: ywx1037054
 * Create: 2022-9-5
 */
#include "YangTree_MaxDepth.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_sync[10] = {0};
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[10] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[40] = {0};

class YangTreeMaxDepth_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void YangTreeMaxDepth_test::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void YangTreeMaxDepth_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void YangTreeMaxDepth_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcAllocStmt(g_conn, &g_stmt_sync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.tablespaceName = NULL;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    GmcDropNamespace(g_stmt, g_namespace);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建Vertex表
   testCreateLabelMix(g_stmt_async);
   testCreateLabelCL8(g_stmt_async);
   testCreateLabelCCHCA8(g_stmt_async);
   testCreateLabelC8(g_stmt_async);
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_CHECK_LOG_BEGIN();
}

void YangTreeMaxDepth_test::TearDown()
{
   AW_CHECK_LOG_END();
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};

   // 异步删除namespace
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_sync[i]);
    }
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
        g_stmt_sync[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
}

/* MIX1 Yang模型示例
                          C_MIX1_N1(con) —— —— ——  2个...
                             |                    |
                          C_MIX1_N2(list1)     C_MIX1_N2(list2)...
                             |
                          C_MIX1_N3(choice)
                             |
                          C_MIX1_N4(case)
                             |
                          C_MIX1_N5(con)
                             |
                          C_MIX1_N6(list1)
                             |
                          C_MIX1_N7(choice)
                             |
                          C_MIX1_N8(case)
*/
/*****************************************************************************
 Description  : 001.MIX1类型, 内容过滤，第一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX1_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX1_N2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "C_MIX1_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[2], "C_MIX1_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

         ret = GmcYangEditChildNode(g_childNode[3], "C_MIX1_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
         ret = GmcBatchAddDML(batch, g_stmt_list[1]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX1_N6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

      ret = GmcYangEditChildNode(g_childNode[6], "C_MIX1_N7", GMC_OPERATION_INSERT, &g_childNode[7]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      ret = GmcYangEditChildNode(g_childNode[7], "C_MIX1_N8", GMC_OPERATION_INSERT, &g_childNode[8]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 55+i;
      testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
      ret = GmcBatchAddDML(batch, g_stmt_list[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_1.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_1.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 002.MIX1类型, 内容过滤，最后一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX1_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX1_N2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "C_MIX1_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[2], "C_MIX1_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

         ret = GmcYangEditChildNode(g_childNode[3], "C_MIX1_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
         ret = GmcBatchAddDML(batch, g_stmt_list[1]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX1_N6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

      ret = GmcYangEditChildNode(g_childNode[6], "C_MIX1_N7", GMC_OPERATION_INSERT, &g_childNode[7]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      ret = GmcYangEditChildNode(g_childNode[7], "C_MIX1_N8", GMC_OPERATION_INSERT, &g_childNode[8]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 55+i;
      testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
      ret = GmcBatchAddDML(batch, g_stmt_list[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_2.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_2.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 003.MIX1类型, 叶子过滤，第一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX1_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX1_N2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "C_MIX1_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[2], "C_MIX1_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

         ret = GmcYangEditChildNode(g_childNode[3], "C_MIX1_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
         ret = GmcBatchAddDML(batch, g_stmt_list[1]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX1_N6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

      ret = GmcYangEditChildNode(g_childNode[6], "C_MIX1_N7", GMC_OPERATION_INSERT, &g_childNode[7]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      ret = GmcYangEditChildNode(g_childNode[7], "C_MIX1_N8", GMC_OPERATION_INSERT, &g_childNode[8]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 55+i;
      testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
      ret = GmcBatchAddDML(batch, g_stmt_list[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_3.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_3.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 004.MIX1类型, 叶子过滤，第二层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX1_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX1_N2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "C_MIX1_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[2], "C_MIX1_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

         ret = GmcYangEditChildNode(g_childNode[3], "C_MIX1_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
         ret = GmcBatchAddDML(batch, g_stmt_list[1]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX1_N6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

      ret = GmcYangEditChildNode(g_childNode[6], "C_MIX1_N7", GMC_OPERATION_INSERT, &g_childNode[7]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      ret = GmcYangEditChildNode(g_childNode[7], "C_MIX1_N8", GMC_OPERATION_INSERT, &g_childNode[8]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 55+i;
      testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
      ret = GmcBatchAddDML(batch, g_stmt_list[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_4.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_4.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 005.MIX1类型, 容器过滤，第二层开始计算深度，深度为0
 Info  :
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX1_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX1_N2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "C_MIX1_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[2], "C_MIX1_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

         ret = GmcYangEditChildNode(g_childNode[3], "C_MIX1_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
         ret = GmcBatchAddDML(batch, g_stmt_list[1]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX1_N6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

      ret = GmcYangEditChildNode(g_childNode[6], "C_MIX1_N7", GMC_OPERATION_INSERT, &g_childNode[7]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      ret = GmcYangEditChildNode(g_childNode[7], "C_MIX1_N8", GMC_OPERATION_INSERT, &g_childNode[8]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 55+i;
      testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
      ret = GmcBatchAddDML(batch, g_stmt_list[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_5.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_5.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 006.MIX1类型, 容器过滤，第六层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX1_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX1_N2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "C_MIX1_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[2], "C_MIX1_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

         ret = GmcYangEditChildNode(g_childNode[3], "C_MIX1_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
         ret = GmcBatchAddDML(batch, g_stmt_list[1]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX1_N6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[6]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

      ret = GmcYangEditChildNode(g_childNode[6], "C_MIX1_N7", GMC_OPERATION_INSERT, &g_childNode[7]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

      ret = GmcYangEditChildNode(g_childNode[7], "C_MIX1_N8", GMC_OPERATION_INSERT, &g_childNode[8]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 55+i;
      testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
      ret = GmcBatchAddDML(batch, g_stmt_list[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_6.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_6.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX1_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* MIX2 Yang模型示例
                          C_MIX1_N1(con)
                             |
                          C_MIX1_N2(choice)
                             |
                          C_MIX1_N3(case) —— —— ——  待扩展
                             |                    |
                          C_MIX1_N4(list1)     C_MIX1_N4(list2)
                             |
                          C_MIX1_N5(con)
                             |
                          C_MIX1_N6(choice)
                             |
                          C_MIX1_N7(case) —— —— ——  待扩展
                             |                    |
                          C_MIX1_N8(list1)     C_MIX1_N8(list2)
*/
/*****************************************************************************
 Description  : 007.MIX2类型, 内容过滤，第一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX2_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   ret = GmcYangEditChildNode(g_rootNode, "C_MIX2_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_MIX2_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX2_N4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "C_MIX2_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        ret = GmcYangEditChildNode(g_childNode[4], "C_MIX2_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[5], "C_MIX2_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX2_N8", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[7]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      }
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_7.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_7.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX2_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 008.MIX2类型, 内容过滤，最后一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX2_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   ret = GmcYangEditChildNode(g_rootNode, "C_MIX2_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_MIX2_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX2_N4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "C_MIX2_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        ret = GmcYangEditChildNode(g_childNode[4], "C_MIX2_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[5], "C_MIX2_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX2_N8", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[7]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      }
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_8.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_8.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX2_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 009.MIX2类型, 叶子过滤，第一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX2_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   ret = GmcYangEditChildNode(g_rootNode, "C_MIX2_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_MIX2_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX2_N4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "C_MIX2_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        ret = GmcYangEditChildNode(g_childNode[4], "C_MIX2_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[5], "C_MIX2_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX2_N8", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[7]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      }
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_9.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_9.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX2_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 010.MIX2类型, 叶子过滤，第二层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX2_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   ret = GmcYangEditChildNode(g_rootNode, "C_MIX2_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_MIX2_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX2_N4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "C_MIX2_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        ret = GmcYangEditChildNode(g_childNode[4], "C_MIX2_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[5], "C_MIX2_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX2_N8", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[7]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      }
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_10.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_10.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX2_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 011.MIX2类型, 容器过滤，第二层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX2_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   ret = GmcYangEditChildNode(g_rootNode, "C_MIX2_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_MIX2_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX2_N4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "C_MIX2_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        ret = GmcYangEditChildNode(g_childNode[4], "C_MIX2_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[5], "C_MIX2_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX2_N8", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[7]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      }
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_11.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_11.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX2_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 012.MIX2类型, 容器过滤，第六层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_MIX2_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   ret = GmcYangEditChildNode(g_rootNode, "C_MIX2_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_MIX2_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_MIX2_N4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 100 + i;
        testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "C_MIX2_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        ret = GmcYangEditChildNode(g_childNode[4], "C_MIX2_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

         ret = GmcYangEditChildNode(g_childNode[5], "C_MIX2_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100+i;
         testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_MIX2_N8", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[7]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 55 + i;
        testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      }
   }
    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_12.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_12.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_MIX2_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end."); 
}
/* C_L_8模型示例【类似二叉树】
                                    C_MIX1_N1(con)
                                          |
                          C_MIX1_N2(list1)——C_MIX1_N2(list2)
                             |                          |
            C_MIX1_N3(list1)/C_MIX1_N3(list2)   C_MIX1_N3(list1)/C_MIX1_N3(list2)
                ...             ...             ...                 ...
            以此类推，二叉树有2个孩子，一共8层list
*/
/*****************************************************************************
 Description  : 013.C_L_8类型, 内容过滤，第一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_L_8_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
      ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_L_8_N2", GMC_OPERATION_INSERT);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 100 + i;
      testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
         ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_L_8_N3", GMC_OPERATION_INSERT);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100 + i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         for (uint32_t i = 0; i < 2; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "C_L_8_N4", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[4]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            fieldValue = 100 + i;
            testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t i = 0; i < 2; i++) {
               ret = testGmcPrepareStmtByLabelName(g_stmt_list[4], "C_L_8_N5", GMC_OPERATION_INSERT);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcYangBindChild(batch, g_stmt_list[3], g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcGetRootNode(g_stmt_list[4], &g_childNode[5]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               fieldValue = 100 + i;
               testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
               ret = GmcBatchAddDML(batch, g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               for (uint32_t i = 0; i < 2; i++) {
                  ret = testGmcPrepareStmtByLabelName(g_stmt_list[5], "C_L_8_N6", GMC_OPERATION_INSERT);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcYangBindChild(batch, g_stmt_list[4], g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcGetRootNode(g_stmt_list[5], &g_childNode[6]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  fieldValue = 100 + i;
                  testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                  ret = GmcBatchAddDML(batch, g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  for (uint32_t i = 0; i < 2; i++) {
                     ret = testGmcPrepareStmtByLabelName(g_stmt_list[6], "C_L_8_N7", GMC_OPERATION_INSERT);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcYangBindChild(batch, g_stmt_list[5], g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcGetRootNode(g_stmt_list[6], &g_childNode[7]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     fieldValue = 100 + i;
                     testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                     ret = GmcBatchAddDML(batch, g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     for (uint32_t i = 0; i < 2; i++) {
                        ret = testGmcPrepareStmtByLabelName(g_stmt_list[7], "C_L_8_N8", GMC_OPERATION_INSERT);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcYangBindChild(batch, g_stmt_list[6], g_stmt_list[7]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcGetRootNode(g_stmt_list[7], &g_childNode[8]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        fieldValue = 100 + i;
                        testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                           ret = GmcBatchAddDML(batch, g_stmt_list[7]);
                           AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     }
                  }
               }
            }
         }
      }
   }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(255, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(255, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_13.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_13.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_L_8_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 014.C_L_8类型, 内容过滤，最后一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_L_8_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
      ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_L_8_N2", GMC_OPERATION_INSERT);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 100 + i;
      testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
         ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_L_8_N3", GMC_OPERATION_INSERT);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100 + i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         for (uint32_t i = 0; i < 2; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "C_L_8_N4", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[4]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            fieldValue = 100 + i;
            testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t i = 0; i < 2; i++) {
               ret = testGmcPrepareStmtByLabelName(g_stmt_list[4], "C_L_8_N5", GMC_OPERATION_INSERT);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcYangBindChild(batch, g_stmt_list[3], g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcGetRootNode(g_stmt_list[4], &g_childNode[5]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               fieldValue = 100 + i;
               testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
               ret = GmcBatchAddDML(batch, g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               for (uint32_t i = 0; i < 2; i++) {
                  ret = testGmcPrepareStmtByLabelName(g_stmt_list[5], "C_L_8_N6", GMC_OPERATION_INSERT);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcYangBindChild(batch, g_stmt_list[4], g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcGetRootNode(g_stmt_list[5], &g_childNode[6]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  fieldValue = 100 + i;
                  testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                  ret = GmcBatchAddDML(batch, g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  for (uint32_t i = 0; i < 2; i++) {
                     ret = testGmcPrepareStmtByLabelName(g_stmt_list[6], "C_L_8_N7", GMC_OPERATION_INSERT);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcYangBindChild(batch, g_stmt_list[5], g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcGetRootNode(g_stmt_list[6], &g_childNode[7]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     fieldValue = 100 + i;
                     testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                     ret = GmcBatchAddDML(batch, g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     for (uint32_t i = 0; i < 2; i++) {
                        ret = testGmcPrepareStmtByLabelName(g_stmt_list[7], "C_L_8_N8", GMC_OPERATION_INSERT);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcYangBindChild(batch, g_stmt_list[6], g_stmt_list[7]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcGetRootNode(g_stmt_list[7], &g_childNode[8]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        fieldValue = 100 + i;
                        testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                           ret = GmcBatchAddDML(batch, g_stmt_list[7]);
                           AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     }
                  }
               }
            }
         }
      }
   }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(255, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(255, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_14.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_14.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_L_8_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 015.C_L_8类型, 叶子过滤，第一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_L_8_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
      ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_L_8_N2", GMC_OPERATION_INSERT);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 100 + i;
      testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
         ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_L_8_N3", GMC_OPERATION_INSERT);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100 + i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         for (uint32_t i = 0; i < 2; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "C_L_8_N4", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[4]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            fieldValue = 100 + i;
            testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t i = 0; i < 2; i++) {
               ret = testGmcPrepareStmtByLabelName(g_stmt_list[4], "C_L_8_N5", GMC_OPERATION_INSERT);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcYangBindChild(batch, g_stmt_list[3], g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcGetRootNode(g_stmt_list[4], &g_childNode[5]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               fieldValue = 100 + i;
               testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
               ret = GmcBatchAddDML(batch, g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               for (uint32_t i = 0; i < 2; i++) {
                  ret = testGmcPrepareStmtByLabelName(g_stmt_list[5], "C_L_8_N6", GMC_OPERATION_INSERT);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcYangBindChild(batch, g_stmt_list[4], g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcGetRootNode(g_stmt_list[5], &g_childNode[6]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  fieldValue = 100 + i;
                  testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                  ret = GmcBatchAddDML(batch, g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  for (uint32_t i = 0; i < 2; i++) {
                     ret = testGmcPrepareStmtByLabelName(g_stmt_list[6], "C_L_8_N7", GMC_OPERATION_INSERT);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcYangBindChild(batch, g_stmt_list[5], g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcGetRootNode(g_stmt_list[6], &g_childNode[7]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     fieldValue = 100 + i;
                     testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                     ret = GmcBatchAddDML(batch, g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     for (uint32_t i = 0; i < 2; i++) {
                        ret = testGmcPrepareStmtByLabelName(g_stmt_list[7], "C_L_8_N8", GMC_OPERATION_INSERT);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcYangBindChild(batch, g_stmt_list[6], g_stmt_list[7]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcGetRootNode(g_stmt_list[7], &g_childNode[8]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        fieldValue = 100 + i;
                        testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                           ret = GmcBatchAddDML(batch, g_stmt_list[7]);
                           AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     }
                  }
               }
            }
         }
      }
   }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(255, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(255, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_15.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_15.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_L_8_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 016.C_L_8类型, 叶子过滤，第二层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_L_8_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
      ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_L_8_N2", GMC_OPERATION_INSERT);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 100 + i;
      testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
         ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_L_8_N3", GMC_OPERATION_INSERT);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100 + i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         for (uint32_t i = 0; i < 2; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "C_L_8_N4", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[4]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            fieldValue = 100 + i;
            testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t i = 0; i < 2; i++) {
               ret = testGmcPrepareStmtByLabelName(g_stmt_list[4], "C_L_8_N5", GMC_OPERATION_INSERT);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcYangBindChild(batch, g_stmt_list[3], g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcGetRootNode(g_stmt_list[4], &g_childNode[5]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               fieldValue = 100 + i;
               testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
               ret = GmcBatchAddDML(batch, g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               for (uint32_t i = 0; i < 2; i++) {
                  ret = testGmcPrepareStmtByLabelName(g_stmt_list[5], "C_L_8_N6", GMC_OPERATION_INSERT);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcYangBindChild(batch, g_stmt_list[4], g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcGetRootNode(g_stmt_list[5], &g_childNode[6]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  fieldValue = 100 + i;
                  testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                  ret = GmcBatchAddDML(batch, g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  for (uint32_t i = 0; i < 2; i++) {
                     ret = testGmcPrepareStmtByLabelName(g_stmt_list[6], "C_L_8_N7", GMC_OPERATION_INSERT);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcYangBindChild(batch, g_stmt_list[5], g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcGetRootNode(g_stmt_list[6], &g_childNode[7]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     fieldValue = 100 + i;
                     testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                     ret = GmcBatchAddDML(batch, g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     for (uint32_t i = 0; i < 2; i++) {
                        ret = testGmcPrepareStmtByLabelName(g_stmt_list[7], "C_L_8_N8", GMC_OPERATION_INSERT);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcYangBindChild(batch, g_stmt_list[6], g_stmt_list[7]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcGetRootNode(g_stmt_list[7], &g_childNode[8]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        fieldValue = 100 + i;
                        testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                           ret = GmcBatchAddDML(batch, g_stmt_list[7]);
                           AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     }
                  }
               }
            }
         }
      }
   }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(255, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(255, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_16.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_16.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_L_8_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 017.C_L_8类型, 容器过滤，第二层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_L_8_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
      ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_L_8_N2", GMC_OPERATION_INSERT);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 100 + i;
      testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
         ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_L_8_N3", GMC_OPERATION_INSERT);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100 + i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         for (uint32_t i = 0; i < 2; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "C_L_8_N4", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[4]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            fieldValue = 100 + i;
            testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t i = 0; i < 2; i++) {
               ret = testGmcPrepareStmtByLabelName(g_stmt_list[4], "C_L_8_N5", GMC_OPERATION_INSERT);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcYangBindChild(batch, g_stmt_list[3], g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcGetRootNode(g_stmt_list[4], &g_childNode[5]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               fieldValue = 100 + i;
               testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
               ret = GmcBatchAddDML(batch, g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               for (uint32_t i = 0; i < 2; i++) {
                  ret = testGmcPrepareStmtByLabelName(g_stmt_list[5], "C_L_8_N6", GMC_OPERATION_INSERT);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcYangBindChild(batch, g_stmt_list[4], g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcGetRootNode(g_stmt_list[5], &g_childNode[6]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  fieldValue = 100 + i;
                  testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                  ret = GmcBatchAddDML(batch, g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  for (uint32_t i = 0; i < 2; i++) {
                     ret = testGmcPrepareStmtByLabelName(g_stmt_list[6], "C_L_8_N7", GMC_OPERATION_INSERT);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcYangBindChild(batch, g_stmt_list[5], g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcGetRootNode(g_stmt_list[6], &g_childNode[7]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     fieldValue = 100 + i;
                     testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                     ret = GmcBatchAddDML(batch, g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     for (uint32_t i = 0; i < 2; i++) {
                        ret = testGmcPrepareStmtByLabelName(g_stmt_list[7], "C_L_8_N8", GMC_OPERATION_INSERT);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcYangBindChild(batch, g_stmt_list[6], g_stmt_list[7]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcGetRootNode(g_stmt_list[7], &g_childNode[8]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        fieldValue = 100 + i;
                        testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                           ret = GmcBatchAddDML(batch, g_stmt_list[7]);
                           AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     }
                  }
               }
            }
         }
      }
   }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(255, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(255, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_17.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_17.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_L_8_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 018.C_L_8类型, 容器过滤，第六层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_L_8_N1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < 2; i++) {
      ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "C_L_8_N2", GMC_OPERATION_INSERT);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      fieldValue = 100 + i;
      testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
      ret = GmcBatchAddDML(batch, g_stmt_list[1]);
      AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
      for (uint32_t i = 0; i < 2; i++) {
         ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "C_L_8_N3", GMC_OPERATION_INSERT);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[3]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         fieldValue = 100 + i;
         testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
         ret = GmcBatchAddDML(batch, g_stmt_list[2]);
         AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
         for (uint32_t i = 0; i < 2; i++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "C_L_8_N4", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[4]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            fieldValue = 100 + i;
            testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_list[3]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (uint32_t i = 0; i < 2; i++) {
               ret = testGmcPrepareStmtByLabelName(g_stmt_list[4], "C_L_8_N5", GMC_OPERATION_INSERT);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcYangBindChild(batch, g_stmt_list[3], g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               ret = GmcGetRootNode(g_stmt_list[4], &g_childNode[5]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               fieldValue = 100 + i;
               testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
               ret = GmcBatchAddDML(batch, g_stmt_list[4]);
               AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               for (uint32_t i = 0; i < 2; i++) {
                  ret = testGmcPrepareStmtByLabelName(g_stmt_list[5], "C_L_8_N6", GMC_OPERATION_INSERT);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcYangBindChild(batch, g_stmt_list[4], g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  ret = GmcGetRootNode(g_stmt_list[5], &g_childNode[6]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  fieldValue = 100 + i;
                  testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                  ret = GmcBatchAddDML(batch, g_stmt_list[5]);
                  AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                  for (uint32_t i = 0; i < 2; i++) {
                     ret = testGmcPrepareStmtByLabelName(g_stmt_list[6], "C_L_8_N7", GMC_OPERATION_INSERT);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcYangBindChild(batch, g_stmt_list[5], g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     ret = GmcGetRootNode(g_stmt_list[6], &g_childNode[7]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     fieldValue = 100 + i;
                     testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                     ret = GmcBatchAddDML(batch, g_stmt_list[6]);
                     AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     for (uint32_t i = 0; i < 2; i++) {
                        ret = testGmcPrepareStmtByLabelName(g_stmt_list[7], "C_L_8_N8", GMC_OPERATION_INSERT);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcYangBindChild(batch, g_stmt_list[6], g_stmt_list[7]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcGetRootNode(g_stmt_list[7], &g_childNode[8]);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        fieldValue = 100 + i;
                        testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
                           ret = GmcBatchAddDML(batch, g_stmt_list[7]);
                           AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                     }
                  }
               }
            }
         }
      }
   }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(255, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(255, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *ReplyJson = NULL;
    readJanssonFile("FilterReply/Reply/Reply_99_18.json", &ReplyJson);
    EXPECT_NE((void *)NULL, ReplyJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

    char *FilterJson = NULL;
    readJanssonFile("FilterReply/Filter/Filter_01_18.json", &FilterJson);
    EXPECT_NE((void *)NULL, FilterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "C_L_8_N1";
    filter.subtree.json = FilterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    char *reply = NULL;
    AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&Data);
    ASSERT_EQ(GMERR_OK, Data.expectStatus);
    AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
    free(ReplyJson);
    free(FilterJson);
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* C_CHCA_8模型示例
                          C_MIX1_N1(con)
                             |
                          C_MIX1_N2(choice)
                             |
                          C_MIX1_N3(case)
                             |
                          C_MIX1_N4(choice)
                             |
                          C_MIX1_N5(case)
                             |
                          C_MIX1_N6(choice)
                             |
                          C_MIX1_N7(case)
                             |
                          C_MIX1_N8(choice)
                             |
                          C_MIX1_N9(case)
*/
/*****************************************************************************
 Description  : 019.C_CHCA_8类型, 内容过滤，第一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_019)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_CHCA_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "C_CHCA_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_CHCA_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[2], "C_CHCA_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[3], "C_CHCA_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[4], "C_CHCA_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[5], "C_CHCA_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[6], "C_CHCA_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[7], "C_CHCA_8_N9", GMC_OPERATION_INSERT, &g_childNode[8]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_19.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_19.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "C_CHCA_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 020.C_CHCA_8类型, 内容过滤，第七层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_020)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_CHCA_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "C_CHCA_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_CHCA_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[2], "C_CHCA_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[3], "C_CHCA_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[4], "C_CHCA_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[5], "C_CHCA_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[6], "C_CHCA_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[7], "C_CHCA_8_N9", GMC_OPERATION_INSERT, &g_childNode[8]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_20.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_20.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "C_CHCA_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 021.C_CHCA_8类型, 叶子过滤，第一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_021)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_CHCA_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "C_CHCA_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_CHCA_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[2], "C_CHCA_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[3], "C_CHCA_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[4], "C_CHCA_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[5], "C_CHCA_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[6], "C_CHCA_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[7], "C_CHCA_8_N9", GMC_OPERATION_INSERT, &g_childNode[8]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_21.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_21.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "C_CHCA_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 022.C_CHCA_8类型, 叶子过滤，第二层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_022)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_CHCA_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "C_CHCA_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_CHCA_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[2], "C_CHCA_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[3], "C_CHCA_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[4], "C_CHCA_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[5], "C_CHCA_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[6], "C_CHCA_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[7], "C_CHCA_8_N9", GMC_OPERATION_INSERT, &g_childNode[8]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_22.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_22.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "C_CHCA_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 023.C_CHCA_8类型, 容器过滤，第二层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_023)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_CHCA_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "C_CHCA_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_CHCA_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[2], "C_CHCA_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[3], "C_CHCA_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[4], "C_CHCA_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[5], "C_CHCA_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[6], "C_CHCA_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[7], "C_CHCA_8_N9", GMC_OPERATION_INSERT, &g_childNode[8]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_23.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_23.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "C_CHCA_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 024.C_CHCA_8类型, 容器过滤，第二层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_024)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C_CHCA_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "C_CHCA_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[1], "C_CHCA_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[2], "C_CHCA_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[3], "C_CHCA_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[4], "C_CHCA_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[5], "C_CHCA_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[6], "C_CHCA_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   ret = GmcYangEditChildNode(g_childNode[7], "C_CHCA_8_N9", GMC_OPERATION_INSERT, &g_childNode[8]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[8], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_24.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_24.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "C_CHCA_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
/* C_8模型示例
                          C_MIX1_N1(con)
                             |
                          C_MIX1_N2(con)
                             |
                          C_MIX1_N3(con)
                             |
                          C_MIX1_N4(con)
                             |
                          C_MIX1_N5(con)
                             |
                          C_MIX1_N6(con)
                             |
                          C_MIX1_N7(con)
                             |
                          C_MIX1_N8(con)
                             |
                          C_MIX1_N9(con)
*/
/*****************************************************************************
 Description  : 025.C_8类型, 内容过滤，第一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_025)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "Con_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "Con_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[1], "Con_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[2], "Con_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[3], "Con_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[4], "Con_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[5], "Con_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[6], "Con_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_25.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_25.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "Con_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 026.C_8类型, 内容过滤，第七层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_026)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "Con_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "Con_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[1], "Con_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[2], "Con_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[3], "Con_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[4], "Con_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[5], "Con_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[6], "Con_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_26.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_26.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "Con_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 027.C_8类型, 叶子过滤，第一层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_027)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "Con_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "Con_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[1], "Con_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[2], "Con_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[3], "Con_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[4], "Con_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[5], "Con_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[6], "Con_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_27.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_27.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "Con_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 028.C_8类型, 叶子过滤，第二层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_028)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "Con_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "Con_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[1], "Con_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[2], "Con_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[3], "Con_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[4], "Con_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[5], "Con_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[6], "Con_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_28.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_28.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "Con_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 029.C_8类型, 容器过滤，第二层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_029)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "Con_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "Con_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[1], "Con_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[2], "Con_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[3], "Con_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[4], "Con_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[5], "Con_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[6], "Con_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_29.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_29.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "Con_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
/*****************************************************************************
 Description  : 030.C_8类型, 容器过滤，第六层开始计算深度，深度为0
 Info  : 
*****************************************************************************/
TEST_F(YangTreeMaxDepth_test, Yang_027_99_030)
{
   AW_FUN_Log(LOG_STEP, "test start.");
   int ret;
   uint32_t fieldValue;
   uint32_t ID;
   uint32_t PID;
   GmcBatchT *batch = NULL;

   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, "Con_8_N1", GMC_OPERATION_INSERT);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // 设置属性值
   fieldValue = 100;
   testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_rootNode, "Con_8_N2", GMC_OPERATION_INSERT, &g_childNode[1]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

   ret = GmcYangEditChildNode(g_childNode[1], "Con_8_N3", GMC_OPERATION_INSERT, &g_childNode[2]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[2], "Con_8_N4", GMC_OPERATION_INSERT, &g_childNode[3]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[3], "Con_8_N5", GMC_OPERATION_INSERT, &g_childNode[4]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[4], "Con_8_N6", GMC_OPERATION_INSERT, &g_childNode[5]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[5], "Con_8_N7", GMC_OPERATION_INSERT, &g_childNode[6]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[6], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

   ret = GmcYangEditChildNode(g_childNode[6], "Con_8_N8", GMC_OPERATION_INSERT, &g_childNode[7]);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   fieldValue = 100;
   testYangSetNodeProperty(g_childNode[7], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
   // 批处理提交
   ret = GmcBatchAddDML(batch, g_stmt_root);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AsyncUserDataT data = {0};
   ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testWaitAsyncRecv(&data);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
   AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
   AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
   GmcBatchDestroy(batch);
   memset(&data, 0, sizeof(AsyncUserDataT));

   // 提交事务
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // subtree 查询
   char *ReplyJson = NULL;
   readJanssonFile("FilterReply/Reply/Reply_99_30.json", &ReplyJson);
   EXPECT_NE((void *)NULL, ReplyJson);
   SubtreeFilterCbParam Data = {0};
   Data.expectReplyJson = ReplyJson; // 预期要的subtree返回结果
   Data.expectStatus = GMERR_OK; // 预期服务端处理结果
   Data.step = 0; //回调执行次数
   AW_FUN_Log(LOG_STEP, "步骤1.设置回调数据和期望json");

   char *FilterJson = NULL;
   readJanssonFile("FilterReply/Filter/Filter_01_30.json", &FilterJson);
   EXPECT_NE((void *)NULL, FilterJson);

   GmcSubtreeFilterItemT filter = {0};
   filter.rootName = "Con_8_N1";
   filter.subtree.json = FilterJson;
   filter.jsonFlag =  GMC_JSON_INDENT(4);
   filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
   filter.maxDepth = 99;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
   char *reply = NULL;
   AW_FUN_Log(LOG_STEP, "步骤2.设置查询json和查询配置");
   ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
   ASSERT_EQ(GMERR_OK, ret);
   ret = testWaitAsyncSubtreeRecv(&Data);
   ASSERT_EQ(GMERR_OK, Data.expectStatus);
   AW_FUN_Log(LOG_STEP, "步骤3.subtree查询和等待响应");
   free(ReplyJson);
   free(FilterJson);
   ret = testTransCommitAsync(g_conn_async);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   AW_FUN_Log(LOG_STEP, "test end.");
}
