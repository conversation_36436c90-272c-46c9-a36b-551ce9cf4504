/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 018_YangTreeDMLOperation
 * Author: hanyang
 * Create: 2023-6-02
 */
#include "YangTreeNone.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[10] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[40] = {0};

class YangTreeDMLOperation_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void YangTreeDMLOperation_test::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createEpollOneThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void YangTreeDMLOperation_test::TearDownTestCase()
{
    closeEpollOneThread();
    GmcDetachAllShmSeg();
    testEnvClean();
}

YangConnOptionT connOptions = {0};

void YangTreeDMLOperation_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, "Unable to build audit log header, current action is FETCH DIFF.");
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建Vertex表
    TestCreateLabelMulti(g_stmt_async);

    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AddWhiteList(GMERR_SYNTAX_ERROR);
}

void YangTreeDMLOperation_test::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AW_CHECK_LOG_END();

    // 删除Vertex和Edge表
    TestDropLabelMulti(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
}

/*
root--con_1
root--con_2--con_2_1
root--con_3--choice_3_1--case_3_1_1
root--con_4--list_4_1
root--con_5--leaflist_5_1

root--choice_6--case_6_1
root--choice_7--case_7_1--con_7_1_1
root--choice_8--case_8_1--choice_8_1_1--case_8_1_1_1
root--choice_9--case_9_1--list_9_1_1
root--choice_10--case_10_1--leaflist_10_1_1

root--list_11
root--list_12--con_12_1
root--list_13--choice_13_1--case_13_1_1
root--list_14--list_14_1
root--list_15--leaflist_15_1

root--leaflist_16
root--leaflist_17
root--leaflist_18
root--leaflist_19
root--leaflist_20
*/
/*****************************************************************************
 Description  : 001.root-container，root为insert操作，container分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.expTotalNum = 1;
    data.expSuccNum = 1;
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.expTotalNum, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(data.expSuccNum, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_001_01");

    // 提交事务
    AW_YANG_EXPECT_OK(awYangTransOpAsync(AW_YANG_TRANS_COMMIT, g_conn_async, g_stmt_async));
    AW_YANG_EXPECT_OK(testYangDMLDemoRemoveRoot(g_conn_async, g_stmt_async));
    AW_YANG_EXPECT_OK(testYangDMLDemoFullTree(g_conn_async, g_stmt_async));
}

/*****************************************************************************
 Description  : 002.root-container，root为merge操作，container分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_002_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 003.root-container，root为replace操作，container分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_001_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 004.root-container，root为delete操作，container分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 005.root-container，root为remove操作，container分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 006.root-container，root为none操作，container分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_002_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 007.root-list，root为insert操作，list分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_007_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 008.root-list，root为merge操作，list分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 设置属性值
    fieldValue = 400;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_008_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 009.root-list，root为replace操作，list分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_007_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 010.root-list，root为delete操作，list分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 011.root-list，root为remove操作，list分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 012.root-list，root为none操作，list分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 设置属性值
    fieldValue = 400;
    TestYangSetNodePropertyMulti(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_008_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 013.root-choice-case，root为insert操作，choice-case分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_7_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_8_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_7_1", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_8_1", GMC_OPERATION_NONE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_013_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 014.root-choice-case，root为merge操作，choice-case分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_7_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_8_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_7_1", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_8_1", GMC_OPERATION_NONE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 400;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_014_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 015.root-choice-case，root为replace操作，choice-case分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_7_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_8_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_7_1", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_8_1", GMC_OPERATION_NONE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_013_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 016.root-choice-case，root为delete操作，choice-case分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 017.root-choice-case，root为remove操作，choice-case分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 018.root-choice-case，root为none操作，choice-case分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_7_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_8_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_7_1", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_8_1", GMC_OPERATION_NONE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 400;
    TestYangSetNodePropertyMulti(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_014_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 019.root-leaflist，root为insert操作，leaflist分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_019_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 020.root-leaflist，root为merge操作，leaflist分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_020_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 021.root-leaflist，root为replace操作，leaflist分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_019_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 022.root-leaflist，root为delete操作，leaflist分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 023.root-leaflist，root为remove操作，leaflist分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 024.root-leaflist，root为none操作，leaflist分别为六原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /***************************insert***********************************/
    fieldValue = 100;
    TestInsertRootNone(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 replace
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_16", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 remove
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_17", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 200;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 none
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_18", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 300;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_020_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 025.root-container，root为merge操作，container为insert操作，container属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_025_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 026.root-container，root为merge操作，container为merge操作，container属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_026_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 027.root-container，root为merge操作，container为replace操作，container属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_025_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 028.root-container，root为merge操作，container为delete操作，container属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点delete
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 029.root-container，root为merge操作，container为remove操作，container属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_029_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 030.root-container，root为merge操作，container为none操作，container属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 031.root-list，root为merge操作，list为insert操作，list属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_031_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 032.root-list，root为merge操作，list为merge操作，list属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_032_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 033.root-list，root为merge操作，list为replace操作，list属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    ret = TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_031_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 034.root-list，root为merge操作，list为delete操作，list属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 035.root-list，root为merge操作，list为remove操作，list属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_029_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 036.root-list，root为merge操作，list为none操作，list属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点 insert
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_11", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key值
    fieldValue = 100;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetNodePropertyF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 037.root-choice-case，root为merge操作，choice-case为insert操作，choice-case属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点insert
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_037_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 038.root-choice-case，root为merge操作，choice-case为merge操作，choice-case属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_038_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 039.root-choice-case，root为merge操作，choice-case为replace操作，choice-case属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点replace
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_037_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 040.root-choice-case，root为merge操作，choice-case为delete操作，choice-case属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点merge
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 041.root-choice-case，root为merge操作，choice-case为remove操作，choice-case属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点remove
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE,
        GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_029_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 042.root-choice-case，root为merge操作，choice-case为none操作，choice-case属性分别为五原语操作
 Author       : hanyang
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyMulti(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child节点none
    ret = GmcYangEditChildNode(g_rootNode, "choice_6", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "case_6_1", GMC_OPERATION_NONE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;

    ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF1(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF2(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF3(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetNodePropertyF4(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_004_01");

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 043.root-container1-container2，root为none操作，container1为insert操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置contianer 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_2_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置contianer 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_2_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    valueF2 = 100;
    valueF3 = 101;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 044.root-container1-container2，root为none操作，container1为merge操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置contianer 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_2_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置contianer 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_2_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    valueF2 = 100;
    valueF3 = 101;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 045.root-container1-container2，root为none操作，container1为replace操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 046.root-container1-container2，root为none操作，和container1为delete操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 047.root-container1-container2，root为none操作，container1为remove操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置contianer 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_2_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERR_WHITE_LIST(1, "Unable to build audit log header, current action is FETCH DIFF.");
}

// 048.root-container1-container2，root为none操作，container1为none操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置contianer 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_2_1", GMC_OPERATION_NONE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置contianer 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_2_1", GMC_OPERATION_NONE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    valueF2 = 100;
    valueF3 = 101;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 049.root-container1-list，root为none操作，container1为insert操作，list分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_4_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点字段
    uint32_t valueF2 = 1000;
    uint32_t valueF3 = 1001;
    uint32_t valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_4_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点字段
    valueF2 = 1000;
    valueF3 = 1001;
    valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 050.root-container1-list，root为none操作，container1为merge操作，list分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_4_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点字段
    uint32_t valueF2 = 1000;
    uint32_t valueF3 = 1001;
    uint32_t valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_4_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点字段
    valueF2 = 1000;
    valueF3 = 1001;
    valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 051.root-container1-list，root为none操作，container1为replace操作，list分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_4_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点字段
    uint32_t valueF2 = 1000;
    uint32_t valueF3 = 1001;
    uint32_t valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_4_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点字段
    valueF2 = 1000;
    valueF3 = 1001;
    valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 052.root-container1-list，root为none操作，container1为delete操作，list分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_4_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 053.root-container1-list，root为none操作，container1为remove操作，list分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_4_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERR_WHITE_LIST(1, "Unable to build audit log header, current action is FETCH DIFF.");
}

// 054.root-container1-list，root为none操作，container1为none操作，list分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_4_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点字段
    uint32_t valueF2 = 1000;
    uint32_t valueF3 = 1001;
    uint32_t valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_4_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list节点字段
    valueF2 = 1000;
    valueF3 = 1001;
    valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 055.root-container1-choice-case，root为none操作，container1为insert操作，choice-case分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置choice节点
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点
    ret = GmcYangEditChildNode(g_childNode[2], "case_3_1_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置choice节点
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点
    ret = GmcYangEditChildNode(g_childNode[2], "case_3_1_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    valueF2 = 100;
    valueF3 = 101;
    valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 056.root-container1-choice-case，root为none操作，container1为merge操作，choice-case分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置choice节点
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点
    ret = GmcYangEditChildNode(g_childNode[2], "case_3_1_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置choice节点
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3_1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点
    ret = GmcYangEditChildNode(g_childNode[2], "case_3_1_1", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    valueF2 = 100;
    valueF3 = 101;
    valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 057.root-container1-choice-case，root为none操作，container1为replace操作，choice-case分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置choice节点
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点
    ret = GmcYangEditChildNode(g_childNode[2], "case_3_1_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置choice节点
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点
    ret = GmcYangEditChildNode(g_childNode[2], "case_3_1_1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    valueF2 = 100;
    valueF3 = 101;
    valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 058.root-container1-choice-case，root为none操作，container1为delete操作，choice-case分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置choice节点
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3_1", GMC_OPERATION_DELETE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 059.root-container1-choice-case，root为none操作，container1为remove操作，choice-case分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置choice节点
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3_1", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERR_WHITE_LIST(1, "Unable to build audit log header, current action is FETCH DIFF.");
}

// 060.root-container1-choice-case，root为none操作，container1为none操作，choice-case分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置choice节点
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3_1", GMC_OPERATION_NONE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点
    ret = GmcYangEditChildNode(g_childNode[2], "case_3_1_1", GMC_OPERATION_NONE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置choice节点
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3_1", GMC_OPERATION_NONE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点
    ret = GmcYangEditChildNode(g_childNode[2], "case_3_1_1", GMC_OPERATION_NONE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点字段
    valueF2 = 100;
    valueF3 = 101;
    valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[3], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 061.root-container1-leaflist，root为none操作，container1为insert操作，leaflist分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************merge*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_INSERT, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 062.root-container1-leaflist，root为none操作，container1为merge操作，leaflist分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************merge*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 063.root-container1-leaflist，root为none操作，container1为replace操作，leaflist分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************merge*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 064.root-container1-leaflist，root为none操作，container1为delete操作，leaflist分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_DELETE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 065.root-container1-leaflist，root为none操作，container1为remove操作，leaflist分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERR_WHITE_LIST(1, "Unable to build audit log header, current action is FETCH DIFF.");
}

// 066.root-container1-leaflist，root为none操作，container1为none操作，leaflist分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************merge*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 1节点
    ret = GmcYangEditChildNode(g_rootNode, "con_5", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_5_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_ADD_ERR_WHITE_LIST(1, "Unable to build audit log header, current action is FETCH DIFF.");
}

// 067.root-list1-container2，root为none操作，list1为insert操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_12_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container2节点字段
    uint32_t valueF2 = 1000;
    uint32_t valueF3 = 1001;
    uint32_t valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_12_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container2节点字段
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 068.root-list1-container2，root为none操作，list1为merge操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_12_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container2节点字段
    uint32_t valueF2 = 1000;
    uint32_t valueF3 = 1001;
    uint32_t valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_12_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container2节点字段
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 069.root-list1-container2，root为none操作，list1为replace操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_12_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container2节点字段
    uint32_t valueF2 = 1000;
    uint32_t valueF3 = 1001;
    uint32_t valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_12_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container2节点字段
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 070.root-list1-container2，root为none操作，list1为delete操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 071.root-list1-container2，root为none操作，list1为remove操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 072.root-list1-container2，root为none操作，list1为none操作，container2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_12_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container2节点字段
    uint32_t valueF2 = 1000;
    uint32_t valueF3 = 1001;
    uint32_t valueF4 = 1002;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_12", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container 2节点
    ret = GmcYangEditChildNode(g_childNode[1], "con_12_1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置container2节点字段
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 073.root-list1-list2，root为none操作，list1为insert操作，list2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 074.root-list1-list2，root为none操作，list1为merge操作，list2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 075.root-list1-list2，root为none操作，list1为replace操作，list2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 076.root-list1-list2，root为none操作，list1为delete操作，list2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 077.root-list1-list2，root为none操作，list1为remove操作，list2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
     GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 078.root-list1-list2，root为none操作，list1为none操作，list2分别为六原语操作
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    /*****************************create,merge,replace*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    uint32_t valueF2 = 100;
    uint32_t valueF3 = 101;
    uint32_t valueF4 = 102;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F4",
    GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*****************************remove,delete*********************************/
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list1节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_14", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[1], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    // 设置child list2节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_14_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    pkValue = 100;
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "PK",
    GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置list2节点字段
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF2, sizeof(uint32_t), "F2",
    GMC_YANG_PROPERTY_OPERATION_REMOVE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = TestYangSetFieldNone(g_childNode[2], GMC_DATATYPE_UINT32, &valueF3, sizeof(uint32_t), "F3",
    GMC_YANG_PROPERTY_OPERATION_DELETE, GMERR_SYNTAX_ERROR);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(0, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void DMLOperationSucc(GmcOperationTypeE rootOp, GmcOperationTypeE listOp, GmcOperationTypeE choiceOp)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", listOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置主键值
    fieldValue = 1;
    if (listOp == GMC_OPERATION_INSERT || listOp == GMC_OPERATION_REPLACE_GRAPH) {
        TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    } else {
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);
    }

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_childNode[1], "choice_13_1", choiceOp, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (choiceOp != GMC_OPERATION_REMOVE_GRAPH && choiceOp != GMC_OPERATION_DELETE_GRAPH) {
        // 设置child case节点
        ret = GmcYangEditChildNode(g_childNode[2], "case_13_1_1", choiceOp, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置case字段
        ret = TestYangSetNodePropertyF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void DMLOperationFail(GmcOperationTypeE rootOp, GmcOperationTypeE listOp, GmcOperationTypeE choiceOp, int expectedRet)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", listOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置主键值
    fieldValue = 1;
    if (listOp == GMC_OPERATION_INSERT || listOp == GMC_OPERATION_REPLACE_GRAPH) {
        TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    } else {
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);
    }

    // 设置child choice节点，预期失败
    ret = GmcYangEditChildNode(g_childNode[1], "choice_13_1", choiceOp, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(expectedRet, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void RemoveRoot()
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void RemoveList()
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_13", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置主键值
    fieldValue = 1;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/*****************************************************************************
 Description  : 079.root--list_13--choice_13_1--case_13_1_1，
                root为none和merge操作，list为insert操作，choice-case为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 1; i++) {
        DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

        // 恢复到只有root的状态
        RemoveList();
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_NONE, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_REMOVE_GRAPH};
    int expectedRets[3] = {GMERR_SYNTAX_ERROR, GMERR_SYNTAX_ERROR, GMERR_SYNTAX_ERROR};
    for (uint32_t i = 0; i < 3; i++) {
        DMLOperationFail(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsFail[i], expectedRets[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");

        // 恢复到只有root的状态
        RemoveList();
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // choice-case和之前的操作相同
    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

        // 恢复到只有root的状态
        RemoveList();
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        DMLOperationFail(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsFail[i], expectedRets[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");

        // 恢复到只有root的状态
        RemoveList();
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 080.root--list_13--choice_13_1--case_13_1_1，
                root为none和merge操作，list为merge操作，choice-case为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_NONE,
        GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH};
    for (uint32_t i = 0; i < 4; i++) {
        DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");
    }

    DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[4]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");

    DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, GMC_OPERATION_INSERT);
    DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[5]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // choice-case和之前的操作相同
    // 子节点预期成功的六个原语操作
    for (uint32_t i = 0; i < 4; i++) {
        DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");
    }

    DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[4]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");

    DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, GMC_OPERATION_INSERT);
    DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[5]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 081.root--list_13--choice_13_1--case_13_1_1，
                root为none和merge操作，list为replace操作，choice-case为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先insert list再replace
    DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 3; i++) {
        DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_NONE, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_REMOVE_GRAPH};
    int expectedRets[3] = {GMERR_SYNTAX_ERROR, GMERR_SYNTAX_ERROR, GMERR_SYNTAX_ERROR};
    for (uint32_t i = 0; i < 3; i++) {
        DMLOperationFail(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], expectedRets[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // choice-case和之前的操作相同
    // 先insert list再replace
    DMLOperationSucc(GMC_OPERATION_INSERT, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        DMLOperationFail(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], expectedRets[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 082.root--list_13--choice_13_1--case_13_1_1，
                root为none和merge操作，list为delete操作，choice-case为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    /***************************父list insert， 子choice-case六原语***********************************/
    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_NONE,
        GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH};
    for (uint32_t i = 0; i < 6; i++) {
        // 先insert list再delete
        DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

        // delete list
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail(GMC_OPERATION_NONE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // choice-case和之前的操作相同
    // 子节点预期失败的六个原语操作
    for (uint32_t i = 0; i < 1; i++) {
        // 先insert list再delete
        DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

        // delete list
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail(GMC_OPERATION_MERGE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 083.root--list_13--choice_13_1--case_13_1_1，
            root为none和merge操作，list为remove操作，choice-case为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_NONE,
        GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH};
    for (uint32_t i = 0; i < 6; i++) {
        // 先insert list再remove
        DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

        // remove list
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail(GMC_OPERATION_NONE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // choice-case和之前的操作相同
    // 子节点预期失败的六个原语操作
    for (uint32_t i = 0; i < 6; i++) {
        // 先insert list再remove
        DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

        // remove list
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail(GMC_OPERATION_MERGE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 084.root--list_13--choice_13_1--case_13_1_1，root为none和merge操作，list为none操作，choice-case为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};

    // 先insert list再none
    DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

    uint32_t i = 0;
    for (; i < 3; i++) {
        // none list
        DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");
    }

    // none list，remove choice-case
    DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");

    // none list，insert choice-case
    DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

    // none list，delete choice-case
    DMLOperationSucc(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // choice-case和之前的操作相同
    // 子节点预期成功的六个原语操作
    // 先insert list再none
    DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

    for (i = 0; i < 3; i++) {
        // none list
        DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");
    }

    // none list，remove choice-case
    DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");

    // none list，insert choice-case
    DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_01");

    // none list，delete choice-case
    DMLOperationSucc(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void DMLOperationSucc1(GmcOperationTypeE rootOp, GmcOperationTypeE listOp, GmcOperationTypeE leaflistOp)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_15", listOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置主键值
    fieldValue = 1;
    if (listOp == GMC_OPERATION_INSERT || listOp == GMC_OPERATION_REPLACE_GRAPH) {
        TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    } else {
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child leaflist节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_15_1", leaflistOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置主键值
    fieldValue = 1;
    if (leaflistOp == GMC_OPERATION_INSERT || leaflistOp == GMC_OPERATION_REPLACE_GRAPH) {
        TestYangSetNodePropertyPK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    } else {
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_list[2], fieldValue, PID, true);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void DMLOperationFail1(
    GmcOperationTypeE rootOp, GmcOperationTypeE listOp, GmcOperationTypeE leaflistOp, int expectedRet)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_15", listOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置主键值
    fieldValue = 1;
    if (listOp == GMC_OPERATION_INSERT || listOp == GMC_OPERATION_REPLACE_GRAPH) {
        TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    } else {
        PID = 1;
        TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);
    }

    // 设置child leaflist节点
    // 预期失败
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_15_1", leaflistOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(expectedRet, ret);
    AddWhiteList(expectedRet);

    // 操作失败，放弃执行批操作
    GmcBatchDestroy(batch);
}

void RemoveList1()
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child list节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_15", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置主键值
    fieldValue = 1;
    PID = 1;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/*****************************************************************************
 Description  : 085.root-list_15-leaflist_15_1，root为none和merge操作，list为insert操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 3; i++) {
        // insert list
        DMLOperationSucc1(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");

        // 恢复只有root的状态
        RemoveList1();
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_NONE};
    for (uint32_t i = 0; i < 3; i++) {
        // insert list
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail1(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // insert list
        DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");

        // 恢复只有root的状态
        RemoveList1();
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // insert list
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail1(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 086.root-list_15-leaflist_15_1，root为none和merge操作，list为merge操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    uint32_t i = 0;
    // merge list
    for (; i < 3; i++) {
        DMLOperationSucc1(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");
    }

    DMLOperationSucc1(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_05");

    DMLOperationSucc1(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");

    DMLOperationSucc1(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_05");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 子节点预期成功的六个原语操作
    // merge list
    for (i = 0; i < 3; i++) {
        DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");
    }

    DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_05");

    DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");

    DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 087.root-list_15-leaflist_15_1，root为none和merge操作，list为replace操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 3; i++) {
        // replace list
        DMLOperationSucc1(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");

        // 恢复只有root的状态
        RemoveList1();
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_NONE};
    for (uint32_t i = 0; i < 3; i++) {
        // replace list
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail1(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // replace list
        DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");

        // 恢复只有root的状态
        RemoveList1();
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // replace list
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail1(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 088.root-list_15-leaflist_15_1，root为none和merge操作，list为delete操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先insert list，再delete操作
    DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    // delete list
    // 预期的错误码都是GMERR_SYNTAX_ERROR
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFail1(GMC_OPERATION_NONE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 先insert list，再delete操作
    DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    // delete list
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFail1(GMC_OPERATION_MERGE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 089.root-list_15-leaflist_15_1，root为none和merge操作，list为remove操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先insert list，再remove操作
    DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};

    // remove list
    // 预期的错误码都是GMERR_SYNTAX_ERROR
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFail1(GMC_OPERATION_NONE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 先insert list，再remove操作
    DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    // remove list
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFail1(GMC_OPERATION_MERGE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 090.root-list_15-leaflist_15_1，root为none和merge操作，list为none操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先插入list，再进行none操作
    DMLOperationSucc1(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    uint32_t i = 0;
    // none list
    for (; i < 3; i++) {
        DMLOperationSucc1(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");
    }

    DMLOperationSucc1(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_05");

    DMLOperationSucc1(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");

    DMLOperationSucc1(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_05");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 先插入list，再进行none操作
    DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期成功的六个原语操作
    // none list
    for (i = 0; i < 3; i++) {
        DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");
    }

    DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_05");

    DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_04");

    DMLOperationSucc1(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_05");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void DMLOperationSucc2(GmcOperationTypeE rootOp, GmcOperationTypeE choiceOp, GmcOperationTypeE containerOp)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", choiceOp, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (choiceOp != GMC_OPERATION_REMOVE_GRAPH && choiceOp != GMC_OPERATION_DELETE_GRAPH) {
        // 设置child case节点
        ret = GmcYangEditChildNode(g_childNode[1], "case_7_1", choiceOp, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置case字段
        fieldValue = 1;
        ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child container节点
        ret = GmcYangEditChildNode(g_childNode[2], "con_7_1_1", containerOp, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (containerOp != GMC_OPERATION_REMOVE_GRAPH && containerOp != GMC_OPERATION_DELETE_GRAPH) {
            // 设置container字段
            ret = TestYangSetNodePropertyF0(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void DMLOperationFail2(
    GmcOperationTypeE rootOp, GmcOperationTypeE choiceOp, GmcOperationTypeE containerOp, int expectedRet)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", choiceOp, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (choiceOp != GMC_OPERATION_REMOVE_GRAPH && choiceOp != GMC_OPERATION_DELETE_GRAPH) {
        // 设置child case节点
        ret = GmcYangEditChildNode(g_childNode[1], "case_7_1", choiceOp, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置case字段
        ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child container节点
        // 预期失败，对应预期错误码
        ret = GmcYangEditChildNode(g_childNode[2], "con_7_1_1", containerOp, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(expectedRet, ret);
    }

    // 操作失败，放弃执行批操作
    GmcBatchDestroy(batch);
}

void DMLOperationFailChoiceNone2(
    GmcOperationTypeE rootOp, GmcOperationTypeE caseOp, GmcOperationTypeE containerOp, int expectedRet)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = GmcYangEditChildNode(g_childNode[1], "case_7_1", caseOp, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (caseOp != GMC_OPERATION_REMOVE_GRAPH && caseOp != GMC_OPERATION_DELETE_GRAPH) {
        // 设置case字段
        ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child container节点
    // 预期失败，对应预期错误码
    ret = GmcYangEditChildNode(g_childNode[2], "con_7_1_1", containerOp, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(expectedRet, ret);

    // 操作失败，放弃执行批操作
    GmcBatchDestroy(batch);
}

void RemoveChoice2()
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_7", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/*****************************************************************************
 Description  : 091.root-choice_7-case_7_1-container_7_1_1，
                root为none和merge操作，choice-case为insert操作，container为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        DMLOperationSucc2(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");

        // 恢复只有root的状态
        RemoveChoice2();
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_NONE};
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail2(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // container和之前的操作相同
    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");

        // 恢复只有root的状态
        RemoveChoice2();
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail2(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 092.root-choice_7-case_7_1-container_7_1_1，
                root为none和merge操作，choice-case为merge操作，container为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    uint32_t i = 0;
    // merge choice-case
    for (; i < 3; i++) {
        DMLOperationSucc2(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");
    }

    DMLOperationSucc2(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_07");

    DMLOperationSucc2(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");

    DMLOperationSucc2(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_07");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // container和之前的操作相同
    // 子节点预期成功的六个原语操作
    // merge choice-case
    for (i = 0; i < 3; i++) {
        DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");
    }

    DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_07");

    DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");

    DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_07");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 093.root-choice_7-case_7_1-container_7_1_1，
            root为none和merge操作，choice-case为replace操作，container为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        DMLOperationSucc2(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");

        // 恢复只有root的状态
        RemoveChoice2();
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_NONE};
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail2(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // container和之前的操作相同
    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");

        // 恢复只有root的状态
        RemoveChoice2();
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail2(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 094.root-choice_7-case_7_1-container_7_1_1，
                root为none和merge操作，choice为none，case为delete操作，container为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先insert choice-case，再delete操作
    DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    // none choice, delete case
    // 预期的错误码都是GMERR_SYNTAX_ERROR
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone2(
            GMC_OPERATION_NONE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // container和之前的操作相同
    // 先insert choice-case，再delete操作
    DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    // none choice, delete case
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone2(
            GMC_OPERATION_MERGE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 095.root-choice_7-case_7_1-container_7_1_1，
                root为none和merge操作，choice为none，case为remove操作，container为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先insert choice-case，再remove操作
    DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    // none choice, remove case
    // 预期的错误码都是GMERR_SYNTAX_ERROR
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone2(
            GMC_OPERATION_NONE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // container和之前的操作相同
    // 先insert choice-case，再remove操作
    DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期成功的六个原语操作
    // none choice, remove case
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone2(
            GMC_OPERATION_MERGE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 096.root-choice_7-case_7_1-container_7_1_1，
                root为none和merge操作，choice-case为none操作，container为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先插入choice-case-container，再进行none操作
    DMLOperationSucc2(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    uint32_t i = 0;
    // none choice-case
    for (; i < 3; i++) {
        DMLOperationSucc2(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");
    }

    DMLOperationSucc2(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_07");

    DMLOperationSucc2(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");

    DMLOperationSucc2(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_07");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 先插入choice-case-container，再进行none操作
    DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期成功的六个原语操作
    // none container
    for (i = 0; i < 3; i++) {
        DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");
    }

    DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_07");

    DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_06");

    DMLOperationSucc2(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_07");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void DMLOperationSucc3(GmcOperationTypeE rootOp, GmcOperationTypeE choiceOp, GmcOperationTypeE listOp)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_9", choiceOp, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (choiceOp != GMC_OPERATION_REMOVE_GRAPH && choiceOp != GMC_OPERATION_DELETE_GRAPH) {
        // 设置child case节点
        ret = GmcYangEditChildNode(g_childNode[1], "case_9_1", choiceOp, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置case字段
        fieldValue = 1;
        ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child list节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_9_1_1", listOp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置主键值
        fieldValue = 1;
        if (listOp == GMC_OPERATION_INSERT || listOp == GMC_OPERATION_REPLACE_GRAPH) {
            TestYangSetNodePropertyPK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        } else {
            PID = 1;
            TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void DMLOperationFail3(GmcOperationTypeE rootOp, GmcOperationTypeE choiceOp, GmcOperationTypeE listOp, int expectedRet)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_9", choiceOp, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (choiceOp != GMC_OPERATION_REMOVE_GRAPH && choiceOp != GMC_OPERATION_DELETE_GRAPH) {
        // 设置child case节点
        ret = GmcYangEditChildNode(g_childNode[1], "case_9_1", choiceOp, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置case字段
        ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child list节点
        // 预期失败，对应预期错误码
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_9_1_1", listOp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(expectedRet, ret);
    }

    // 操作失败，放弃执行批操作
    GmcBatchDestroy(batch);
}

void DMLOperationFailChoiceNone3(
    GmcOperationTypeE rootOp, GmcOperationTypeE caseOp, GmcOperationTypeE listOp, int expectedRet)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_9", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = GmcYangEditChildNode(g_childNode[1], "case_9_1", caseOp, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (caseOp != GMC_OPERATION_REMOVE_GRAPH && caseOp != GMC_OPERATION_DELETE_GRAPH) {
        // 设置case字段
        ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 设置child list节点
    // 预期失败，对应预期错误码
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_9_1_1", listOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(expectedRet, ret);

    // 操作失败，放弃执行批操作
    GmcBatchDestroy(batch);
}

void RemoveChoice3()
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_9", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/*****************************************************************************
 Description  : 097.root--choice_9--case_9_1--list_9_1_1，root为none和merge操作，choice-case为insert操作，list为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        DMLOperationSucc3(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");

        // 恢复只有root的状态
        RemoveChoice3();
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_NONE};
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail3(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // list和之前的操作相同
    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");

        // 恢复只有root的状态
        RemoveChoice3();
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail3(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 098.root--choice_9--case_9_1--list_9_1_1，root为none和merge操作，choice-case为merge操作，list为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    uint32_t i = 0;
    // merge choice-case
    for (; i < 3; i++) {
        DMLOperationSucc3(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");
    }

    DMLOperationSucc3(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_09");

    DMLOperationSucc3(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");

    DMLOperationSucc3(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_09");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // list和之前的操作相同
    // 子节点预期成功的六个原语操作
    // merge choice-case
    for (i = 0; i < 3; i++) {
        DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");
    }

    DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_09");

    DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");

    DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_09");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 099.root--choice_9--case_9_1--list_9_1_1，root为none和merge操作，choice-case为replace操作，list为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        DMLOperationSucc3(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");

        // 恢复只有root的状态
        RemoveChoice3();
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_NONE};
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail3(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // list和之前的操作相同
    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");

        // 恢复只有root的状态
        RemoveChoice3();
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail3(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 100.root--choice_9--case_9_1--list_9_1_1，
                root为none和merge操作，choice为none操作，case为delete操作，list为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先insert choice-case，再delete操作
    DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    // none choice, delete case
    // 预期的错误码都是GMERR_SYNTAX_ERROR
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone3(
            GMC_OPERATION_NONE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // list和之前的操作相同
    // 先insert choice-case，再delete操作
    DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    // none choice, delete case
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone3(
            GMC_OPERATION_MERGE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 100.root--choice_9--case_9_1--list_9_1_1，root为none和merge操作，choice为none操作，case为remove操作，list为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先insert choice-case，再remove操作
    DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    // none choice, remove case
    // 预期的错误码都是GMERR_SYNTAX_ERROR
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone3(
            GMC_OPERATION_NONE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // list和之前的操作相同
    // 先insert choice-case，再remove操作
    DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    // none choice, remove case
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone3(
            GMC_OPERATION_MERGE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 102.root--choice_9--case_9_1--list_9_1_1，root为none和merge操作，choice-case为none操作，list为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先插入choice-case-list，再进行none操作
    DMLOperationSucc3(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    uint32_t i = 0;
    // none choice-case
    for (; i < 3; i++) {
        DMLOperationSucc3(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");
    }

    DMLOperationSucc3(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_09");

    DMLOperationSucc3(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");

    DMLOperationSucc3(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_09");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // list和之前的操作相同
    // 先插入choice-case-list，再进行none操作
    DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期成功的六个原语操作
    // none choice-case
    for (i = 0; i < 3; i++) {
        DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");
    }

    DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_09");

    DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_08");

    DMLOperationSucc3(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_09");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void DMLOperationSucc4(GmcOperationTypeE rootOp, GmcOperationTypeE choice1Op, GmcOperationTypeE choice2Op)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", choice1Op, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (choice1Op != GMC_OPERATION_REMOVE_GRAPH && choice1Op != GMC_OPERATION_DELETE_GRAPH) {
        // 设置child case节点
        ret = GmcYangEditChildNode(g_childNode[1], "case_8_1", choice1Op, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置case字段
        fieldValue = 1;
        ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child choice节点
        ret = GmcYangEditChildNode(g_childNode[2], "choice_8_1_1", choice2Op, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (choice2Op != GMC_OPERATION_REMOVE_GRAPH && choice2Op != GMC_OPERATION_DELETE_GRAPH) {
            // 设置child case节点
            ret = GmcYangEditChildNode(g_childNode[3], "case_8_1_1_1", choice2Op, &g_childNode[4]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 设置case字段
            fieldValue = 1;
            ret = TestYangSetNodePropertyF0(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void DMLOperationFail4(
    GmcOperationTypeE rootOp, GmcOperationTypeE choice1Op, GmcOperationTypeE choice2Op, int expectedRet)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", choice1Op, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (choice1Op != GMC_OPERATION_REMOVE_GRAPH && choice1Op != GMC_OPERATION_DELETE_GRAPH) {
        // 设置child case节点
        ret = GmcYangEditChildNode(g_childNode[1], "case_8_1", choice1Op, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置case字段
        ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child choice节点
        // 预期失败，对应预期的错误码
        ret = GmcYangEditChildNode(g_childNode[2], "choice_8_1_1", choice2Op, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(expectedRet, ret);
    }

    // 操作失败，放弃执行批操作
    GmcBatchDestroy(batch);
}

void DMLOperationFailChoiceNone4(
    GmcOperationTypeE rootOp, GmcOperationTypeE case1Op, GmcOperationTypeE choice2Op, int expectedRet)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = GmcYangEditChildNode(g_childNode[1], "case_8_1", case1Op, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    // 预期失败，对应预期的错误码
    ret = GmcYangEditChildNode(g_childNode[2], "choice_8_1_1", choice2Op, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(expectedRet, ret);

    // 操作失败，放弃执行批操作
    GmcBatchDestroy(batch);
}

void RemoveChoice4()
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_8", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/*****************************************************************************
 Description  : 103.root--choice_8--case_8_1--choice_8_1_1--case_8_1_1_1，
                root为none和merge操作，choice_8-case_8_1为insert操作，choice_8_1_1-case_8_1_1_1为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        DMLOperationSucc4(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");

        // 恢复只有root的状态
        RemoveChoice4();
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_NONE};
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail4(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // 下层choice-case和之前的操作相同
    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");

        // 恢复只有root的状态
        RemoveChoice4();
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail4(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 104.root--choice_8--case_8_1--choice_8_1_1--case_8_1_1_1，
                root为none和merge操作，choice_8-case_8_1为merge操作，choice_8_1_1-case_8_1_1_1为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    uint32_t i = 0;
    // merge choice-case
    for (; i < 3; i++) {
        DMLOperationSucc4(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");
    }

    DMLOperationSucc4(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_11");

    DMLOperationSucc4(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");

    DMLOperationSucc4(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_11");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // choice_8_1_1-case_8_1_1_1和之前的操作相同
    // 子节点预期成功的六个原语操作
    // merge choice-case
    for (i = 0; i < 3; i++) {
        DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");
    }

    DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_11");

    DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");

    DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_11");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 105.root--choice_8--case_8_1--choice_8_1_1--case_8_1_1_1，
                root为none和merge操作，choice_8-case_8_1为replace操作，choice_8_1_1-case_8_1_1_1为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        DMLOperationSucc4(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");

        // 恢复只有root的状态
        RemoveChoice4();
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_NONE};
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail4(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // choice_8_1_1-case_8_1_1_1和之前的操作相同
    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");

        // 恢复只有root的状态
        RemoveChoice4();
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail4(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 106.root--choice_8--case_8_1--choice_8_1_1--case_8_1_1_1，
                root为none和merge操作，choice_8-case_8_1为delete操作，choice_8_1_1-case_8_1_1_1为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先insert 上层choice-case，再delete操作
    DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    // none choice, delete case
    // 预期的错误码都是GMERR_SYNTAX_ERROR
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone4(
            GMC_OPERATION_NONE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // choice_8_1_1-case_8_1_1_1和之前的操作相同
    // 先insert choice-case，再delete操作
    DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    // none choice, delete case
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone4(
            GMC_OPERATION_MERGE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 107.root--choice_8--case_8_1--choice_8_1_1--case_8_1_1_1，
                root为none和merge操作，choice_8-case_8_1为remove操作，choice_8_1_1-case_8_1_1_1为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先insert 上层choice-case，再remove操作
    DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    // none choice, remove case
    // 预期的错误码都是GMERR_SYNTAX_ERROR
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone4(
            GMC_OPERATION_NONE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // choice_8_1_1-case_8_1_1_1和之前的操作相同
    // 先insert 上层choice-case，再remove操作
    DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    // none choice, remove case
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone4(
            GMC_OPERATION_MERGE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 108.root--choice_8--case_8_1--choice_8_1_1--case_8_1_1_1，
                root为none和merge操作，choice_8-case_8_1为none操作，choice_8_1_1-case_8_1_1_1为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先插入choice_8-case_8_1-choice_8_1_1-case_8_1_1_1，再进行none操作
    DMLOperationSucc4(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    uint32_t i = 0;
    // none choice_8-case_8_1
    for (; i < 3; i++) {
        DMLOperationSucc4(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");
    }

    DMLOperationSucc4(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_11");

    DMLOperationSucc4(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");

    DMLOperationSucc4(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_11");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // choice_8_1_1-case_8_1_1_1和之前的操作相同
    // 先插入choice_8-case_8_1-choice_8_1_1-case_8_1_1_1，再进行none操作
    DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期成功的六个原语操作
    // none choice_8-case_8_1
    for (i = 0; i < 3; i++) {
        DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");
    }

    DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_11");

    DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_10");

    DMLOperationSucc4(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_11");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void DMLOperationSucc5(GmcOperationTypeE rootOp, GmcOperationTypeE choiceOp, GmcOperationTypeE listOp)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    uint32_t stmt_num = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    stmt_num++;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_10", choiceOp, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (choiceOp != GMC_OPERATION_REMOVE_GRAPH && choiceOp != GMC_OPERATION_DELETE_GRAPH) {
        // 设置child case节点
        ret = GmcYangEditChildNode(g_childNode[1], "case_10_1", choiceOp, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置case字段
        fieldValue = 1;
        ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child leaflist节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_10_1_1", listOp);
        stmt_num++;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置主键值
        fieldValue = 1;
        if (listOp == GMC_OPERATION_INSERT || listOp == GMC_OPERATION_REPLACE_GRAPH) {
            TestYangSetNodePropertyPK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        } else {
            PID = 1;
            TestSetKeyNameAndValue(g_stmt_list[1], fieldValue, PID, true);
        }

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(stmt_num, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(stmt_num, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void DMLOperationFail5(GmcOperationTypeE rootOp, GmcOperationTypeE choiceOp, GmcOperationTypeE listOp, int expectedRet)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_10", choiceOp, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (choiceOp != GMC_OPERATION_REMOVE_GRAPH && choiceOp != GMC_OPERATION_DELETE_GRAPH) {
        // 设置child case节点
        ret = GmcYangEditChildNode(g_childNode[1], "case_10_1", choiceOp, &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置case字段
        fieldValue = 1;
        ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child leaflist节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_10_1_1", listOp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(expectedRet, ret);
    }

    // 操作失败，放弃执行批操作
    GmcBatchDestroy(batch);
}

void DMLOperationFailChoiceNone5(
    GmcOperationTypeE rootOp, GmcOperationTypeE caseOp, GmcOperationTypeE listOp, int expectedRet)
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", rootOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_10", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = GmcYangEditChildNode(g_childNode[1], "case_10_1", caseOp, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case字段
    if (caseOp != GMC_OPERATION_REMOVE_GRAPH && caseOp != GMC_OPERATION_DELETE_GRAPH) {
        fieldValue = 1;
        ret = TestYangSetNodePropertyF0(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child leaflist节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_10_1_1", listOp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(expectedRet, ret);

    // 操作失败，放弃执行批操作
    GmcBatchDestroy(batch);
}

void RemoveChoice5()
{
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(g_rootNode, "choice_10", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    data.rootName = "root";
    data.namespaceName = g_namespace;
    AW_YANG_EXPECT_OK(awYangBatchExecuteAsync(g_conn_async, g_stmt_async, batch, NULL, &data));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/*****************************************************************************
 Description  : 109.root--choice_10--case_10_1--leaflist_10_1_1，
                root为none和merge操作，choice-case为insert操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        DMLOperationSucc5(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");

        // 恢复只有root的状态
        RemoveChoice5();
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_NONE};
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail5(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");

        // 恢复只有root的状态
        RemoveChoice5();
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // insert choice-case
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail5(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 104.root--choice_10--case_10_1--leaflist_10_1_1，
                root为none和merge操作，choice-case为merge操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    uint32_t i = 0;
    // merge choice-case
    for (; i < 3; i++) {
        DMLOperationSucc5(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");
    }

    DMLOperationSucc5(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_13");

    DMLOperationSucc5(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");

    DMLOperationSucc5(GMC_OPERATION_NONE, GMC_OPERATION_MERGE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_13");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 子节点预期成功的六个原语操作
    // merge choice-case
    for (i = 0; i < 3; i++) {
        DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");
    }

    DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_13");

    DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");

    DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_MERGE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_13");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 111.root--choice_10--case_10_1--leaflist_10_1_1，
                root为none和merge操作，choice-case为replace操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 子节点预期成功的三个原语操作
    GmcOperationTypeE operationsSucc[3] = {GMC_OPERATION_INSERT, GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH};
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        DMLOperationSucc5(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");

        // 恢复只有root的状态
        RemoveChoice5();
    }

    // 子节点预期失败的三个原语操作
    GmcOperationTypeE operationsFail[3] = {GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_DELETE_GRAPH, GMC_OPERATION_NONE};
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail5(GMC_OPERATION_NONE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 子节点预期成功的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");

        // 恢复只有root的状态
        RemoveChoice5();
    }

    // 子节点预期失败的三个原语操作
    for (uint32_t i = 0; i < 3; i++) {
        // replace choice
        // 预期的错误码都是GMERR_SYNTAX_ERROR
        DMLOperationFail5(GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_03");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 112.root--choice_10--case_10_1--leaflist_10_1_1，
                root为none和merge操作，choice-case为delete操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先insert choice-case，再delete操作
    DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    // none choice, delete case
    // 预期的错误码都是GMERR_SYNTAX_ERROR
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone5(
            GMC_OPERATION_NONE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 先insert choice-case，再delete操作
    DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    // none choice, delete case
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone5(
            GMC_OPERATION_MERGE, GMC_OPERATION_DELETE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 113.root--choice_10--case_10_1--leaflist_10_1_1，
                root为none和merge操作，choice-case为remove操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先insert choice-case，再remove操作
    DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    GmcOperationTypeE operationsFail[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    // none choice, remove case
    // 预期的错误码都是GMERR_SYNTAX_ERROR
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone5(
            GMC_OPERATION_NONE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");
    }

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 先insert choice-case，再remove操作
    DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期失败的六个原语操作
    // none choice, remove case
    for (uint32_t i = 0; i < 6; i++) {
        DMLOperationFailChoiceNone5(
            GMC_OPERATION_MERGE, GMC_OPERATION_REMOVE_GRAPH, operationsFail[i], GMERR_SYNTAX_ERROR);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 114.root--choice_10--case_10_1--leaflist_10_1_1，
                root为none和merge操作，choice-case为none操作，leaflist为六原语操作
 Author       : xujing
*****************************************************************************/
TEST_F(YangTreeDMLOperation_test, Yang_018_prim_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    /***************************insert root***********************************/
    TestInsertRootNone(g_conn_async, "root", 100);

    // 启动事务
    int ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 先插入choice-case-leaflist，再进行none操作
    DMLOperationSucc5(GMC_OPERATION_NONE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期成功的六个原语操作
    GmcOperationTypeE operationsSucc[6] = {GMC_OPERATION_MERGE, GMC_OPERATION_REPLACE_GRAPH, GMC_OPERATION_NONE,
        GMC_OPERATION_REMOVE_GRAPH, GMC_OPERATION_INSERT, GMC_OPERATION_DELETE_GRAPH};
    uint32_t i = 0;
    // none choice-case
    for (; i < 3; i++) {
        DMLOperationSucc5(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");
    }

    DMLOperationSucc5(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_13");

    DMLOperationSucc5(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");

    DMLOperationSucc5(GMC_OPERATION_NONE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_13");

    /***************************remove root***********************************/
    // 删除根节点
    RemoveRoot();

    /***************************merge root***********************************/
    // leaflist和之前的操作相同
    // 先插入choice-case-leaflist，再进行none操作
    DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_INSERT, GMC_OPERATION_INSERT);

    // 子节点预期成功的六个原语操作
    // none choice-case
    for (i = 0; i < 3; i++) {
        DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
        TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");
    }

    DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_13");

    DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i++]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_12");

    DMLOperationSucc5(GMC_OPERATION_MERGE, GMC_OPERATION_NONE, operationsSucc[i]);
    TestSubtreeFilterNone(g_stmt_async, "root", "Yang_018_Prim_079_13");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
