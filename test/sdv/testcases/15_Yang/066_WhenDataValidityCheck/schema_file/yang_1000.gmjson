[{"type": "container", "name": "root_1000", "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0001", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0001 > 0"}]}, {"name": "F0002", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0002 > 0"}]}, {"name": "F0003", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0003 > 0"}]}, {"name": "F0004", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0004 > 0"}]}, {"name": "F0005", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0005 > 0"}]}, {"name": "F0006", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0006 > 0"}]}, {"name": "F0007", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0007 > 0"}]}, {"name": "F0008", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0008 > 0"}]}, {"name": "F0009", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0009 > 0"}]}, {"name": "F0010", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0010 > 0"}]}, {"name": "F0011", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0011 > 0"}]}, {"name": "F0012", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0012 > 0"}]}, {"name": "F0013", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0013 > 0"}]}, {"name": "F0014", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0014 > 0"}]}, {"name": "F0015", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0015 > 0"}]}, {"name": "F0016", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0016 > 0"}]}, {"name": "F0017", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0017 > 0"}]}, {"name": "F0018", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0018 > 0"}]}, {"name": "F0019", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0019 > 0"}]}, {"name": "F0020", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0020 > 0"}]}, {"name": "F0021", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0021 > 0"}]}, {"name": "F0022", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0022 > 0"}]}, {"name": "F0023", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0023 > 0"}]}, {"name": "F0024", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0024 > 0"}]}, {"name": "F0025", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0025 > 0"}]}, {"name": "F0026", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0026 > 0"}]}, {"name": "F0027", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0027 > 0"}]}, {"name": "F0028", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0028 > 0"}]}, {"name": "F0029", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0029 > 0"}]}, {"name": "F0030", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0030 > 0"}]}, {"name": "F0031", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0031 > 0"}]}, {"name": "F0032", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0032 > 0"}]}, {"name": "F0033", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0033 > 0"}]}, {"name": "F0034", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0034 > 0"}]}, {"name": "F0035", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0035 > 0"}]}, {"name": "F0036", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0036 > 0"}]}, {"name": "F0037", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0037 > 0"}]}, {"name": "F0038", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0038 > 0"}]}, {"name": "F0039", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0039 > 0"}]}, {"name": "F0040", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0040 > 0"}]}, {"name": "F0041", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0041 > 0"}]}, {"name": "F0042", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0042 > 0"}]}, {"name": "F0043", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0043 > 0"}]}, {"name": "F0044", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0044 > 0"}]}, {"name": "F0045", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0045 > 0"}]}, {"name": "F0046", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0046 > 0"}]}, {"name": "F0047", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0047 > 0"}]}, {"name": "F0048", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0048 > 0"}]}, {"name": "F0049", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0049 > 0"}]}, {"name": "F0050", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0050 > 0"}]}, {"name": "F0051", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0051 > 0"}]}, {"name": "F0052", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0052 > 0"}]}, {"name": "F0053", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0053 > 0"}]}, {"name": "F0054", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0054 > 0"}]}, {"name": "F0055", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0055 > 0"}]}, {"name": "F0056", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0056 > 0"}]}, {"name": "F0057", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0057 > 0"}]}, {"name": "F0058", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0058 > 0"}]}, {"name": "F0059", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0059 > 0"}]}, {"name": "F0060", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0060 > 0"}]}, {"name": "F0061", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0061 > 0"}]}, {"name": "F0062", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0062 > 0"}]}, {"name": "F0063", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0063 > 0"}]}, {"name": "F0064", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0064 > 0"}]}, {"name": "F0065", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0065 > 0"}]}, {"name": "F0066", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0066 > 0"}]}, {"name": "F0067", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0067 > 0"}]}, {"name": "F0068", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0068 > 0"}]}, {"name": "F0069", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0069 > 0"}]}, {"name": "F0070", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0070 > 0"}]}, {"name": "F0071", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0071 > 0"}]}, {"name": "F0072", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0072 > 0"}]}, {"name": "F0073", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0073 > 0"}]}, {"name": "F0074", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0074 > 0"}]}, {"name": "F0075", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0075 > 0"}]}, {"name": "F0076", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0076 > 0"}]}, {"name": "F0077", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0077 > 0"}]}, {"name": "F0078", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0078 > 0"}]}, {"name": "F0079", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0079 > 0"}]}, {"name": "F0080", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0080 > 0"}]}, {"name": "F0081", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0081 > 0"}]}, {"name": "F0082", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0082 > 0"}]}, {"name": "F0083", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0083 > 0"}]}, {"name": "F0084", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0084 > 0"}]}, {"name": "F0085", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0085 > 0"}]}, {"name": "F0086", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0086 > 0"}]}, {"name": "F0087", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0087 > 0"}]}, {"name": "F0088", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0088 > 0"}]}, {"name": "F0089", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0089 > 0"}]}, {"name": "F0090", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0090 > 0"}]}, {"name": "F0091", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0091 > 0"}]}, {"name": "F0092", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0092 > 0"}]}, {"name": "F0093", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0093 > 0"}]}, {"name": "F0094", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0094 > 0"}]}, {"name": "F0095", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0095 > 0"}]}, {"name": "F0096", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0096 > 0"}]}, {"name": "F0097", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0097 > 0"}]}, {"name": "F0098", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0098 > 0"}]}, {"name": "F0099", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0099 > 0"}]}, {"name": "F0100", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0100 > 0"}]}, {"name": "F0101", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0101 > 0"}]}, {"name": "F0102", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0102 > 0"}]}, {"name": "F0103", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0103 > 0"}]}, {"name": "F0104", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0104 > 0"}]}, {"name": "F0105", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0105 > 0"}]}, {"name": "F0106", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0106 > 0"}]}, {"name": "F0107", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0107 > 0"}]}, {"name": "F0108", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0108 > 0"}]}, {"name": "F0109", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0109 > 0"}]}, {"name": "F0110", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0110 > 0"}]}, {"name": "F0111", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0111 > 0"}]}, {"name": "F0112", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0112 > 0"}]}, {"name": "F0113", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0113 > 0"}]}, {"name": "F0114", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0114 > 0"}]}, {"name": "F0115", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0115 > 0"}]}, {"name": "F0116", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0116 > 0"}]}, {"name": "F0117", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0117 > 0"}]}, {"name": "F0118", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0118 > 0"}]}, {"name": "F0119", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0119 > 0"}]}, {"name": "F0120", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0120 > 0"}]}, {"name": "F0121", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0121 > 0"}]}, {"name": "F0122", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0122 > 0"}]}, {"name": "F0123", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0123 > 0"}]}, {"name": "F0124", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0124 > 0"}]}, {"name": "F0125", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0125 > 0"}]}, {"name": "F0126", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0126 > 0"}]}, {"name": "F0127", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0127 > 0"}]}, {"name": "F0128", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0128 > 0"}]}, {"name": "F0129", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0129 > 0"}]}, {"name": "F0130", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0130 > 0"}]}, {"name": "F0131", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0131 > 0"}]}, {"name": "F0132", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0132 > 0"}]}, {"name": "F0133", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0133 > 0"}]}, {"name": "F0134", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0134 > 0"}]}, {"name": "F0135", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0135 > 0"}]}, {"name": "F0136", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0136 > 0"}]}, {"name": "F0137", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0137 > 0"}]}, {"name": "F0138", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0138 > 0"}]}, {"name": "F0139", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0139 > 0"}]}, {"name": "F0140", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0140 > 0"}]}, {"name": "F0141", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0141 > 0"}]}, {"name": "F0142", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0142 > 0"}]}, {"name": "F0143", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0143 > 0"}]}, {"name": "F0144", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0144 > 0"}]}, {"name": "F0145", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0145 > 0"}]}, {"name": "F0146", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0146 > 0"}]}, {"name": "F0147", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0147 > 0"}]}, {"name": "F0148", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0148 > 0"}]}, {"name": "F0149", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0149 > 0"}]}, {"name": "F0150", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0150 > 0"}]}, {"name": "F0151", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0151 > 0"}]}, {"name": "F0152", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0152 > 0"}]}, {"name": "F0153", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0153 > 0"}]}, {"name": "F0154", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0154 > 0"}]}, {"name": "F0155", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0155 > 0"}]}, {"name": "F0156", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0156 > 0"}]}, {"name": "F0157", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0157 > 0"}]}, {"name": "F0158", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0158 > 0"}]}, {"name": "F0159", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0159 > 0"}]}, {"name": "F0160", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0160 > 0"}]}, {"name": "F0161", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0161 > 0"}]}, {"name": "F0162", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0162 > 0"}]}, {"name": "F0163", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0163 > 0"}]}, {"name": "F0164", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0164 > 0"}]}, {"name": "F0165", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0165 > 0"}]}, {"name": "F0166", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0166 > 0"}]}, {"name": "F0167", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0167 > 0"}]}, {"name": "F0168", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0168 > 0"}]}, {"name": "F0169", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0169 > 0"}]}, {"name": "F0170", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0170 > 0"}]}, {"name": "F0171", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0171 > 0"}]}, {"name": "F0172", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0172 > 0"}]}, {"name": "F0173", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0173 > 0"}]}, {"name": "F0174", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0174 > 0"}]}, {"name": "F0175", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0175 > 0"}]}, {"name": "F0176", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0176 > 0"}]}, {"name": "F0177", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0177 > 0"}]}, {"name": "F0178", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0178 > 0"}]}, {"name": "F0179", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0179 > 0"}]}, {"name": "F0180", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0180 > 0"}]}, {"name": "F0181", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0181 > 0"}]}, {"name": "F0182", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0182 > 0"}]}, {"name": "F0183", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0183 > 0"}]}, {"name": "F0184", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0184 > 0"}]}, {"name": "F0185", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0185 > 0"}]}, {"name": "F0186", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0186 > 0"}]}, {"name": "F0187", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0187 > 0"}]}, {"name": "F0188", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0188 > 0"}]}, {"name": "F0189", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0189 > 0"}]}, {"name": "F0190", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0190 > 0"}]}, {"name": "F0191", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0191 > 0"}]}, {"name": "F0192", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0192 > 0"}]}, {"name": "F0193", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0193 > 0"}]}, {"name": "F0194", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0194 > 0"}]}, {"name": "F0195", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0195 > 0"}]}, {"name": "F0196", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0196 > 0"}]}, {"name": "F0197", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0197 > 0"}]}, {"name": "F0198", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0198 > 0"}]}, {"name": "F0199", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0199 > 0"}]}, {"name": "F0200", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0200 > 0"}]}, {"name": "F0201", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0201 = 0"}]}, {"name": "F0202", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0202 = 0"}]}, {"name": "F0203", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0203 = 0"}]}, {"name": "F0204", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0204 = 0"}]}, {"name": "F0205", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0205 = 0"}]}, {"name": "F0206", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0206 = 0"}]}, {"name": "F0207", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0207 = 0"}]}, {"name": "F0208", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0208 = 0"}]}, {"name": "F0209", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0209 = 0"}]}, {"name": "F0210", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0210 = 0"}]}, {"name": "F0211", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0211 = 0"}]}, {"name": "F0212", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0212 = 0"}]}, {"name": "F0213", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0213 = 0"}]}, {"name": "F0214", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0214 = 0"}]}, {"name": "F0215", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0215 = 0"}]}, {"name": "F0216", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0216 = 0"}]}, {"name": "F0217", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0217 = 0"}]}, {"name": "F0218", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0218 = 0"}]}, {"name": "F0219", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0219 = 0"}]}, {"name": "F0220", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0220 = 0"}]}, {"name": "F0221", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0221 = 0"}]}, {"name": "F0222", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0222 = 0"}]}, {"name": "F0223", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0223 = 0"}]}, {"name": "F0224", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0224 = 0"}]}, {"name": "F0225", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0225 = 0"}]}, {"name": "F0226", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0226 = 0"}]}, {"name": "F0227", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0227 = 0"}]}, {"name": "F0228", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0228 = 0"}]}, {"name": "F0229", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0229 = 0"}]}, {"name": "F0230", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0230 = 0"}]}, {"name": "F0231", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0231 = 0"}]}, {"name": "F0232", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0232 = 0"}]}, {"name": "F0233", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0233 = 0"}]}, {"name": "F0234", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0234 = 0"}]}, {"name": "F0235", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0235 = 0"}]}, {"name": "F0236", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0236 = 0"}]}, {"name": "F0237", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0237 = 0"}]}, {"name": "F0238", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0238 = 0"}]}, {"name": "F0239", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0239 = 0"}]}, {"name": "F0240", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0240 = 0"}]}, {"name": "F0241", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0241 = 0"}]}, {"name": "F0242", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0242 = 0"}]}, {"name": "F0243", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0243 = 0"}]}, {"name": "F0244", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0244 = 0"}]}, {"name": "F0245", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0245 = 0"}]}, {"name": "F0246", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0246 = 0"}]}, {"name": "F0247", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0247 = 0"}]}, {"name": "F0248", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0248 = 0"}]}, {"name": "F0249", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0249 = 0"}]}, {"name": "F0250", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0250 = 0"}]}, {"type": "container", "name": "con_1000", "fields": [{"name": "F0001", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0001 > 0"}]}, {"name": "F0002", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0002 > 0"}]}, {"name": "F0003", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0003 > 0"}]}, {"name": "F0004", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0004 > 0"}]}, {"name": "F0005", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0005 > 0"}]}, {"name": "F0006", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0006 > 0"}]}, {"name": "F0007", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0007 > 0"}]}, {"name": "F0008", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0008 > 0"}]}, {"name": "F0009", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0009 > 0"}]}, {"name": "F0010", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0010 > 0"}]}, {"name": "F0011", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0011 > 0"}]}, {"name": "F0012", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0012 > 0"}]}, {"name": "F0013", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0013 > 0"}]}, {"name": "F0014", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0014 > 0"}]}, {"name": "F0015", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0015 > 0"}]}, {"name": "F0016", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0016 > 0"}]}, {"name": "F0017", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0017 > 0"}]}, {"name": "F0018", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0018 > 0"}]}, {"name": "F0019", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0019 > 0"}]}, {"name": "F0020", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0020 > 0"}]}, {"name": "F0021", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0021 > 0"}]}, {"name": "F0022", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0022 > 0"}]}, {"name": "F0023", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0023 > 0"}]}, {"name": "F0024", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0024 > 0"}]}, {"name": "F0025", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0025 > 0"}]}, {"name": "F0026", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0026 > 0"}]}, {"name": "F0027", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0027 > 0"}]}, {"name": "F0028", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0028 > 0"}]}, {"name": "F0029", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0029 > 0"}]}, {"name": "F0030", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0030 > 0"}]}, {"name": "F0031", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0031 > 0"}]}, {"name": "F0032", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0032 > 0"}]}, {"name": "F0033", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0033 > 0"}]}, {"name": "F0034", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0034 > 0"}]}, {"name": "F0035", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0035 > 0"}]}, {"name": "F0036", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0036 > 0"}]}, {"name": "F0037", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0037 > 0"}]}, {"name": "F0038", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0038 > 0"}]}, {"name": "F0039", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0039 > 0"}]}, {"name": "F0040", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0040 > 0"}]}, {"name": "F0041", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0041 > 0"}]}, {"name": "F0042", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0042 > 0"}]}, {"name": "F0043", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0043 > 0"}]}, {"name": "F0044", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0044 > 0"}]}, {"name": "F0045", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0045 > 0"}]}, {"name": "F0046", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0046 > 0"}]}, {"name": "F0047", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0047 > 0"}]}, {"name": "F0048", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0048 > 0"}]}, {"name": "F0049", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0049 > 0"}]}, {"name": "F0050", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0050 > 0"}]}, {"name": "F0051", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0051 > 0"}]}, {"name": "F0052", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0052 > 0"}]}, {"name": "F0053", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0053 > 0"}]}, {"name": "F0054", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0054 > 0"}]}, {"name": "F0055", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0055 > 0"}]}, {"name": "F0056", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0056 > 0"}]}, {"name": "F0057", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0057 > 0"}]}, {"name": "F0058", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0058 > 0"}]}, {"name": "F0059", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0059 > 0"}]}, {"name": "F0060", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0060 > 0"}]}, {"name": "F0061", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0061 > 0"}]}, {"name": "F0062", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0062 > 0"}]}, {"name": "F0063", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0063 > 0"}]}, {"name": "F0064", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0064 > 0"}]}, {"name": "F0065", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0065 > 0"}]}, {"name": "F0066", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0066 > 0"}]}, {"name": "F0067", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0067 > 0"}]}, {"name": "F0068", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0068 > 0"}]}, {"name": "F0069", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0069 > 0"}]}, {"name": "F0070", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0070 > 0"}]}, {"name": "F0071", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0071 > 0"}]}, {"name": "F0072", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0072 > 0"}]}, {"name": "F0073", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0073 > 0"}]}, {"name": "F0074", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0074 > 0"}]}, {"name": "F0075", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0075 > 0"}]}, {"name": "F0076", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0076 > 0"}]}, {"name": "F0077", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0077 > 0"}]}, {"name": "F0078", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0078 > 0"}]}, {"name": "F0079", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0079 > 0"}]}, {"name": "F0080", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0080 > 0"}]}, {"name": "F0081", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0081 > 0"}]}, {"name": "F0082", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0082 > 0"}]}, {"name": "F0083", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0083 > 0"}]}, {"name": "F0084", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0084 > 0"}]}, {"name": "F0085", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0085 > 0"}]}, {"name": "F0086", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0086 > 0"}]}, {"name": "F0087", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0087 > 0"}]}, {"name": "F0088", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0088 > 0"}]}, {"name": "F0089", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0089 > 0"}]}, {"name": "F0090", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0090 > 0"}]}, {"name": "F0091", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0091 > 0"}]}, {"name": "F0092", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0092 > 0"}]}, {"name": "F0093", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0093 > 0"}]}, {"name": "F0094", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0094 > 0"}]}, {"name": "F0095", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0095 > 0"}]}, {"name": "F0096", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0096 > 0"}]}, {"name": "F0097", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0097 > 0"}]}, {"name": "F0098", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0098 > 0"}]}, {"name": "F0099", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0099 > 0"}]}, {"name": "F0100", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0100 > 0"}]}, {"name": "F0101", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0101 > 0"}]}, {"name": "F0102", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0102 > 0"}]}, {"name": "F0103", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0103 > 0"}]}, {"name": "F0104", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0104 > 0"}]}, {"name": "F0105", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0105 > 0"}]}, {"name": "F0106", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0106 > 0"}]}, {"name": "F0107", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0107 > 0"}]}, {"name": "F0108", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0108 > 0"}]}, {"name": "F0109", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0109 > 0"}]}, {"name": "F0110", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0110 > 0"}]}, {"name": "F0111", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0111 > 0"}]}, {"name": "F0112", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0112 > 0"}]}, {"name": "F0113", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0113 > 0"}]}, {"name": "F0114", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0114 > 0"}]}, {"name": "F0115", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0115 > 0"}]}, {"name": "F0116", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0116 > 0"}]}, {"name": "F0117", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0117 > 0"}]}, {"name": "F0118", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0118 > 0"}]}, {"name": "F0119", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0119 > 0"}]}, {"name": "F0120", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0120 > 0"}]}, {"name": "F0121", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0121 > 0"}]}, {"name": "F0122", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0122 > 0"}]}, {"name": "F0123", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0123 > 0"}]}, {"name": "F0124", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0124 > 0"}]}, {"name": "F0125", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0125 > 0"}]}, {"name": "F0126", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0126 > 0"}]}, {"name": "F0127", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0127 > 0"}]}, {"name": "F0128", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0128 > 0"}]}, {"name": "F0129", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0129 > 0"}]}, {"name": "F0130", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0130 > 0"}]}, {"name": "F0131", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0131 > 0"}]}, {"name": "F0132", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0132 > 0"}]}, {"name": "F0133", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0133 > 0"}]}, {"name": "F0134", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0134 > 0"}]}, {"name": "F0135", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0135 > 0"}]}, {"name": "F0136", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0136 > 0"}]}, {"name": "F0137", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0137 > 0"}]}, {"name": "F0138", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0138 > 0"}]}, {"name": "F0139", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0139 > 0"}]}, {"name": "F0140", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0140 > 0"}]}, {"name": "F0141", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0141 > 0"}]}, {"name": "F0142", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0142 > 0"}]}, {"name": "F0143", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0143 > 0"}]}, {"name": "F0144", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0144 > 0"}]}, {"name": "F0145", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0145 > 0"}]}, {"name": "F0146", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0146 > 0"}]}, {"name": "F0147", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0147 > 0"}]}, {"name": "F0148", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0148 > 0"}]}, {"name": "F0149", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0149 > 0"}]}, {"name": "F0150", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0150 > 0"}]}, {"name": "F0151", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0151 > 0"}]}, {"name": "F0152", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0152 > 0"}]}, {"name": "F0153", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0153 > 0"}]}, {"name": "F0154", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0154 > 0"}]}, {"name": "F0155", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0155 > 0"}]}, {"name": "F0156", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0156 > 0"}]}, {"name": "F0157", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0157 > 0"}]}, {"name": "F0158", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0158 > 0"}]}, {"name": "F0159", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0159 > 0"}]}, {"name": "F0160", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0160 > 0"}]}, {"name": "F0161", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0161 > 0"}]}, {"name": "F0162", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0162 > 0"}]}, {"name": "F0163", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0163 > 0"}]}, {"name": "F0164", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0164 > 0"}]}, {"name": "F0165", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0165 > 0"}]}, {"name": "F0166", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0166 > 0"}]}, {"name": "F0167", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0167 > 0"}]}, {"name": "F0168", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0168 > 0"}]}, {"name": "F0169", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0169 > 0"}]}, {"name": "F0170", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0170 > 0"}]}, {"name": "F0171", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0171 > 0"}]}, {"name": "F0172", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0172 > 0"}]}, {"name": "F0173", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0173 > 0"}]}, {"name": "F0174", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0174 > 0"}]}, {"name": "F0175", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0175 > 0"}]}, {"name": "F0176", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0176 > 0"}]}, {"name": "F0177", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0177 > 0"}]}, {"name": "F0178", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0178 > 0"}]}, {"name": "F0179", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0179 > 0"}]}, {"name": "F0180", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0180 > 0"}]}, {"name": "F0181", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0181 > 0"}]}, {"name": "F0182", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0182 > 0"}]}, {"name": "F0183", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0183 > 0"}]}, {"name": "F0184", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0184 > 0"}]}, {"name": "F0185", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0185 > 0"}]}, {"name": "F0186", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0186 > 0"}]}, {"name": "F0187", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0187 > 0"}]}, {"name": "F0188", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0188 > 0"}]}, {"name": "F0189", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0189 > 0"}]}, {"name": "F0190", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0190 > 0"}]}, {"name": "F0191", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0191 > 0"}]}, {"name": "F0192", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0192 > 0"}]}, {"name": "F0193", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0193 > 0"}]}, {"name": "F0194", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0194 > 0"}]}, {"name": "F0195", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0195 > 0"}]}, {"name": "F0196", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0196 > 0"}]}, {"name": "F0197", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0197 > 0"}]}, {"name": "F0198", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0198 > 0"}]}, {"name": "F0199", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0199 > 0"}]}, {"name": "F0200", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0200 > 0"}]}, {"name": "F0201", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0201 = 0"}]}, {"name": "F0202", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0202 = 0"}]}, {"name": "F0203", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0203 = 0"}]}, {"name": "F0204", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0204 = 0"}]}, {"name": "F0205", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0205 = 0"}]}, {"name": "F0206", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0206 = 0"}]}, {"name": "F0207", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0207 = 0"}]}, {"name": "F0208", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0208 = 0"}]}, {"name": "F0209", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0209 = 0"}]}, {"name": "F0210", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0210 = 0"}]}, {"name": "F0211", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0211 = 0"}]}, {"name": "F0212", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0212 = 0"}]}, {"name": "F0213", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0213 = 0"}]}, {"name": "F0214", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0214 = 0"}]}, {"name": "F0215", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0215 = 0"}]}, {"name": "F0216", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0216 = 0"}]}, {"name": "F0217", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0217 = 0"}]}, {"name": "F0218", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0218 = 0"}]}, {"name": "F0219", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0219 = 0"}]}, {"name": "F0220", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0220 = 0"}]}, {"name": "F0221", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0221 = 0"}]}, {"name": "F0222", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0222 = 0"}]}, {"name": "F0223", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0223 = 0"}]}, {"name": "F0224", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0224 = 0"}]}, {"name": "F0225", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0225 = 0"}]}, {"name": "F0226", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0226 = 0"}]}, {"name": "F0227", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0227 = 0"}]}, {"name": "F0228", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0228 = 0"}]}, {"name": "F0229", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0229 = 0"}]}, {"name": "F0230", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0230 = 0"}]}, {"name": "F0231", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0231 = 0"}]}, {"name": "F0232", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0232 = 0"}]}, {"name": "F0233", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0233 = 0"}]}, {"name": "F0234", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0234 = 0"}]}, {"name": "F0235", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0235 = 0"}]}, {"name": "F0236", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0236 = 0"}]}, {"name": "F0237", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0237 = 0"}]}, {"name": "F0238", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0238 = 0"}]}, {"name": "F0239", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0239 = 0"}]}, {"name": "F0240", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0240 = 0"}]}, {"name": "F0241", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0241 = 0"}]}, {"name": "F0242", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0242 = 0"}]}, {"name": "F0243", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0243 = 0"}]}, {"name": "F0244", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0244 = 0"}]}, {"name": "F0245", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0245 = 0"}]}, {"name": "F0246", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0246 = 0"}]}, {"name": "F0247", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0247 = 0"}]}, {"name": "F0248", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0248 = 0"}]}, {"name": "F0249", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0249 = 0"}]}, {"name": "F0250", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0250 = 0"}]}]}, {"type": "choice", "name": "choice_1000", "fields": [{"type": "case", "name": "case_1000", "fields": [{"name": "F0001", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0001 > 0"}]}, {"name": "F0002", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0002 > 0"}]}, {"name": "F0003", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0003 > 0"}]}, {"name": "F0004", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0004 > 0"}]}, {"name": "F0005", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0005 > 0"}]}, {"name": "F0006", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0006 > 0"}]}, {"name": "F0007", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0007 > 0"}]}, {"name": "F0008", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0008 > 0"}]}, {"name": "F0009", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0009 > 0"}]}, {"name": "F0010", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0010 > 0"}]}, {"name": "F0011", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0011 > 0"}]}, {"name": "F0012", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0012 > 0"}]}, {"name": "F0013", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0013 > 0"}]}, {"name": "F0014", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0014 > 0"}]}, {"name": "F0015", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0015 > 0"}]}, {"name": "F0016", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0016 > 0"}]}, {"name": "F0017", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0017 > 0"}]}, {"name": "F0018", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0018 > 0"}]}, {"name": "F0019", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0019 > 0"}]}, {"name": "F0020", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0020 > 0"}]}, {"name": "F0021", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0021 > 0"}]}, {"name": "F0022", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0022 > 0"}]}, {"name": "F0023", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0023 > 0"}]}, {"name": "F0024", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0024 > 0"}]}, {"name": "F0025", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0025 > 0"}]}, {"name": "F0026", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0026 > 0"}]}, {"name": "F0027", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0027 > 0"}]}, {"name": "F0028", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0028 > 0"}]}, {"name": "F0029", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0029 > 0"}]}, {"name": "F0030", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0030 > 0"}]}, {"name": "F0031", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0031 > 0"}]}, {"name": "F0032", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0032 > 0"}]}, {"name": "F0033", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0033 > 0"}]}, {"name": "F0034", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0034 > 0"}]}, {"name": "F0035", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0035 > 0"}]}, {"name": "F0036", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0036 > 0"}]}, {"name": "F0037", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0037 > 0"}]}, {"name": "F0038", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0038 > 0"}]}, {"name": "F0039", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0039 > 0"}]}, {"name": "F0040", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0040 > 0"}]}, {"name": "F0041", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0041 > 0"}]}, {"name": "F0042", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0042 > 0"}]}, {"name": "F0043", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0043 > 0"}]}, {"name": "F0044", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0044 > 0"}]}, {"name": "F0045", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0045 > 0"}]}, {"name": "F0046", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0046 > 0"}]}, {"name": "F0047", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0047 > 0"}]}, {"name": "F0048", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0048 > 0"}]}, {"name": "F0049", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0049 > 0"}]}, {"name": "F0050", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0050 > 0"}]}, {"name": "F0051", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0051 > 0"}]}, {"name": "F0052", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0052 > 0"}]}, {"name": "F0053", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0053 > 0"}]}, {"name": "F0054", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0054 > 0"}]}, {"name": "F0055", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0055 > 0"}]}, {"name": "F0056", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0056 > 0"}]}, {"name": "F0057", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0057 > 0"}]}, {"name": "F0058", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0058 > 0"}]}, {"name": "F0059", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0059 > 0"}]}, {"name": "F0060", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0060 > 0"}]}, {"name": "F0061", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0061 > 0"}]}, {"name": "F0062", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0062 > 0"}]}, {"name": "F0063", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0063 > 0"}]}, {"name": "F0064", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0064 > 0"}]}, {"name": "F0065", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0065 > 0"}]}, {"name": "F0066", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0066 > 0"}]}, {"name": "F0067", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0067 > 0"}]}, {"name": "F0068", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0068 > 0"}]}, {"name": "F0069", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0069 > 0"}]}, {"name": "F0070", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0070 > 0"}]}, {"name": "F0071", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0071 > 0"}]}, {"name": "F0072", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0072 > 0"}]}, {"name": "F0073", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0073 > 0"}]}, {"name": "F0074", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0074 > 0"}]}, {"name": "F0075", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0075 > 0"}]}, {"name": "F0076", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0076 > 0"}]}, {"name": "F0077", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0077 > 0"}]}, {"name": "F0078", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0078 > 0"}]}, {"name": "F0079", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0079 > 0"}]}, {"name": "F0080", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0080 > 0"}]}, {"name": "F0081", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0081 > 0"}]}, {"name": "F0082", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0082 > 0"}]}, {"name": "F0083", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0083 > 0"}]}, {"name": "F0084", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0084 > 0"}]}, {"name": "F0085", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0085 > 0"}]}, {"name": "F0086", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0086 > 0"}]}, {"name": "F0087", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0087 > 0"}]}, {"name": "F0088", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0088 > 0"}]}, {"name": "F0089", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0089 > 0"}]}, {"name": "F0090", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0090 > 0"}]}, {"name": "F0091", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0091 > 0"}]}, {"name": "F0092", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0092 > 0"}]}, {"name": "F0093", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0093 > 0"}]}, {"name": "F0094", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0094 > 0"}]}, {"name": "F0095", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0095 > 0"}]}, {"name": "F0096", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0096 > 0"}]}, {"name": "F0097", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0097 > 0"}]}, {"name": "F0098", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0098 > 0"}]}, {"name": "F0099", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0099 > 0"}]}, {"name": "F0100", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0100 > 0"}]}, {"name": "F0101", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0101 > 0"}]}, {"name": "F0102", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0102 > 0"}]}, {"name": "F0103", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0103 > 0"}]}, {"name": "F0104", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0104 > 0"}]}, {"name": "F0105", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0105 > 0"}]}, {"name": "F0106", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0106 > 0"}]}, {"name": "F0107", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0107 > 0"}]}, {"name": "F0108", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0108 > 0"}]}, {"name": "F0109", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0109 > 0"}]}, {"name": "F0110", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0110 > 0"}]}, {"name": "F0111", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0111 > 0"}]}, {"name": "F0112", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0112 > 0"}]}, {"name": "F0113", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0113 > 0"}]}, {"name": "F0114", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0114 > 0"}]}, {"name": "F0115", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0115 > 0"}]}, {"name": "F0116", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0116 > 0"}]}, {"name": "F0117", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0117 > 0"}]}, {"name": "F0118", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0118 > 0"}]}, {"name": "F0119", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0119 > 0"}]}, {"name": "F0120", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0120 > 0"}]}, {"name": "F0121", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0121 > 0"}]}, {"name": "F0122", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0122 > 0"}]}, {"name": "F0123", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0123 > 0"}]}, {"name": "F0124", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0124 > 0"}]}, {"name": "F0125", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0125 > 0"}]}, {"name": "F0126", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0126 > 0"}]}, {"name": "F0127", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0127 > 0"}]}, {"name": "F0128", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0128 > 0"}]}, {"name": "F0129", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0129 > 0"}]}, {"name": "F0130", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0130 > 0"}]}, {"name": "F0131", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0131 > 0"}]}, {"name": "F0132", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0132 > 0"}]}, {"name": "F0133", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0133 > 0"}]}, {"name": "F0134", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0134 > 0"}]}, {"name": "F0135", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0135 > 0"}]}, {"name": "F0136", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0136 > 0"}]}, {"name": "F0137", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0137 > 0"}]}, {"name": "F0138", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0138 > 0"}]}, {"name": "F0139", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0139 > 0"}]}, {"name": "F0140", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0140 > 0"}]}, {"name": "F0141", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0141 > 0"}]}, {"name": "F0142", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0142 > 0"}]}, {"name": "F0143", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0143 > 0"}]}, {"name": "F0144", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0144 > 0"}]}, {"name": "F0145", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0145 > 0"}]}, {"name": "F0146", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0146 > 0"}]}, {"name": "F0147", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0147 > 0"}]}, {"name": "F0148", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0148 > 0"}]}, {"name": "F0149", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0149 > 0"}]}, {"name": "F0150", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0150 > 0"}]}, {"name": "F0151", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0151 > 0"}]}, {"name": "F0152", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0152 > 0"}]}, {"name": "F0153", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0153 > 0"}]}, {"name": "F0154", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0154 > 0"}]}, {"name": "F0155", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0155 > 0"}]}, {"name": "F0156", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0156 > 0"}]}, {"name": "F0157", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0157 > 0"}]}, {"name": "F0158", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0158 > 0"}]}, {"name": "F0159", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0159 > 0"}]}, {"name": "F0160", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0160 > 0"}]}, {"name": "F0161", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0161 > 0"}]}, {"name": "F0162", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0162 > 0"}]}, {"name": "F0163", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0163 > 0"}]}, {"name": "F0164", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0164 > 0"}]}, {"name": "F0165", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0165 > 0"}]}, {"name": "F0166", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0166 > 0"}]}, {"name": "F0167", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0167 > 0"}]}, {"name": "F0168", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0168 > 0"}]}, {"name": "F0169", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0169 > 0"}]}, {"name": "F0170", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0170 > 0"}]}, {"name": "F0171", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0171 > 0"}]}, {"name": "F0172", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0172 > 0"}]}, {"name": "F0173", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0173 > 0"}]}, {"name": "F0174", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0174 > 0"}]}, {"name": "F0175", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0175 > 0"}]}, {"name": "F0176", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0176 > 0"}]}, {"name": "F0177", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0177 > 0"}]}, {"name": "F0178", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0178 > 0"}]}, {"name": "F0179", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0179 > 0"}]}, {"name": "F0180", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0180 > 0"}]}, {"name": "F0181", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0181 > 0"}]}, {"name": "F0182", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0182 > 0"}]}, {"name": "F0183", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0183 > 0"}]}, {"name": "F0184", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0184 > 0"}]}, {"name": "F0185", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0185 > 0"}]}, {"name": "F0186", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0186 > 0"}]}, {"name": "F0187", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0187 > 0"}]}, {"name": "F0188", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0188 > 0"}]}, {"name": "F0189", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0189 > 0"}]}, {"name": "F0190", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0190 > 0"}]}, {"name": "F0191", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0191 > 0"}]}, {"name": "F0192", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0192 > 0"}]}, {"name": "F0193", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0193 > 0"}]}, {"name": "F0194", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0194 > 0"}]}, {"name": "F0195", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0195 > 0"}]}, {"name": "F0196", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0196 > 0"}]}, {"name": "F0197", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0197 > 0"}]}, {"name": "F0198", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0198 > 0"}]}, {"name": "F0199", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0199 > 0"}]}, {"name": "F0200", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0200 > 0"}]}, {"name": "F0201", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0201 = 0"}]}, {"name": "F0202", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0202 = 0"}]}, {"name": "F0203", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0203 = 0"}]}, {"name": "F0204", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0204 = 0"}]}, {"name": "F0205", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0205 = 0"}]}, {"name": "F0206", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0206 = 0"}]}, {"name": "F0207", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0207 = 0"}]}, {"name": "F0208", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0208 = 0"}]}, {"name": "F0209", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0209 = 0"}]}, {"name": "F0210", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0210 = 0"}]}, {"name": "F0211", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0211 = 0"}]}, {"name": "F0212", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0212 = 0"}]}, {"name": "F0213", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0213 = 0"}]}, {"name": "F0214", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0214 = 0"}]}, {"name": "F0215", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0215 = 0"}]}, {"name": "F0216", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0216 = 0"}]}, {"name": "F0217", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0217 = 0"}]}, {"name": "F0218", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0218 = 0"}]}, {"name": "F0219", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0219 = 0"}]}, {"name": "F0220", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0220 = 0"}]}, {"name": "F0221", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0221 = 0"}]}, {"name": "F0222", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0222 = 0"}]}, {"name": "F0223", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0223 = 0"}]}, {"name": "F0224", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0224 = 0"}]}, {"name": "F0225", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0225 = 0"}]}, {"name": "F0226", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0226 = 0"}]}, {"name": "F0227", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0227 = 0"}]}, {"name": "F0228", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0228 = 0"}]}, {"name": "F0229", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0229 = 0"}]}, {"name": "F0230", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0230 = 0"}]}, {"name": "F0231", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0231 = 0"}]}, {"name": "F0232", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0232 = 0"}]}, {"name": "F0233", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0233 = 0"}]}, {"name": "F0234", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0234 = 0"}]}, {"name": "F0235", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0235 = 0"}]}, {"name": "F0236", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0236 = 0"}]}, {"name": "F0237", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0237 = 0"}]}, {"name": "F0238", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0238 = 0"}]}, {"name": "F0239", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0239 = 0"}]}, {"name": "F0240", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0240 = 0"}]}, {"name": "F0241", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0241 = 0"}]}, {"name": "F0242", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0242 = 0"}]}, {"name": "F0243", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0243 = 0"}]}, {"name": "F0244", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0244 = 0"}]}, {"name": "F0245", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0245 = 0"}]}, {"name": "F0246", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0246 = 0"}]}, {"name": "F0247", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0247 = 0"}]}, {"name": "F0248", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0248 = 0"}]}, {"name": "F0249", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0249 = 0"}]}, {"name": "F0250", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0250 = 0"}]}]}]}], "keys": [{"name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_1000", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0001", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0001 > 0"}]}, {"name": "F0002", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0002 > 0"}]}, {"name": "F0003", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0003 > 0"}]}, {"name": "F0004", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0004 > 0"}]}, {"name": "F0005", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0005 > 0"}]}, {"name": "F0006", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0006 > 0"}]}, {"name": "F0007", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0007 > 0"}]}, {"name": "F0008", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0008 > 0"}]}, {"name": "F0009", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0009 > 0"}]}, {"name": "F0010", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0010 > 0"}]}, {"name": "F0011", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0011 > 0"}]}, {"name": "F0012", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0012 > 0"}]}, {"name": "F0013", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0013 > 0"}]}, {"name": "F0014", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0014 > 0"}]}, {"name": "F0015", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0015 > 0"}]}, {"name": "F0016", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0016 > 0"}]}, {"name": "F0017", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0017 > 0"}]}, {"name": "F0018", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0018 > 0"}]}, {"name": "F0019", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0019 > 0"}]}, {"name": "F0020", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0020 > 0"}]}, {"name": "F0021", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0021 > 0"}]}, {"name": "F0022", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0022 > 0"}]}, {"name": "F0023", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0023 > 0"}]}, {"name": "F0024", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0024 > 0"}]}, {"name": "F0025", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0025 > 0"}]}, {"name": "F0026", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0026 > 0"}]}, {"name": "F0027", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0027 > 0"}]}, {"name": "F0028", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0028 > 0"}]}, {"name": "F0029", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0029 > 0"}]}, {"name": "F0030", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0030 > 0"}]}, {"name": "F0031", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0031 > 0"}]}, {"name": "F0032", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0032 > 0"}]}, {"name": "F0033", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0033 > 0"}]}, {"name": "F0034", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0034 > 0"}]}, {"name": "F0035", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0035 > 0"}]}, {"name": "F0036", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0036 > 0"}]}, {"name": "F0037", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0037 > 0"}]}, {"name": "F0038", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0038 > 0"}]}, {"name": "F0039", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0039 > 0"}]}, {"name": "F0040", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0040 > 0"}]}, {"name": "F0041", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0041 > 0"}]}, {"name": "F0042", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0042 > 0"}]}, {"name": "F0043", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0043 > 0"}]}, {"name": "F0044", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0044 > 0"}]}, {"name": "F0045", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0045 > 0"}]}, {"name": "F0046", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0046 > 0"}]}, {"name": "F0047", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0047 > 0"}]}, {"name": "F0048", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0048 > 0"}]}, {"name": "F0049", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0049 > 0"}]}, {"name": "F0050", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0050 > 0"}]}, {"name": "F0051", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0051 > 0"}]}, {"name": "F0052", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0052 > 0"}]}, {"name": "F0053", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0053 > 0"}]}, {"name": "F0054", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0054 > 0"}]}, {"name": "F0055", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0055 > 0"}]}, {"name": "F0056", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0056 > 0"}]}, {"name": "F0057", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0057 > 0"}]}, {"name": "F0058", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0058 > 0"}]}, {"name": "F0059", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0059 > 0"}]}, {"name": "F0060", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0060 > 0"}]}, {"name": "F0061", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0061 > 0"}]}, {"name": "F0062", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0062 > 0"}]}, {"name": "F0063", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0063 > 0"}]}, {"name": "F0064", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0064 > 0"}]}, {"name": "F0065", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0065 > 0"}]}, {"name": "F0066", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0066 > 0"}]}, {"name": "F0067", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0067 > 0"}]}, {"name": "F0068", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0068 > 0"}]}, {"name": "F0069", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0069 > 0"}]}, {"name": "F0070", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0070 > 0"}]}, {"name": "F0071", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0071 > 0"}]}, {"name": "F0072", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0072 > 0"}]}, {"name": "F0073", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0073 > 0"}]}, {"name": "F0074", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0074 > 0"}]}, {"name": "F0075", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0075 > 0"}]}, {"name": "F0076", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0076 > 0"}]}, {"name": "F0077", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0077 > 0"}]}, {"name": "F0078", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0078 > 0"}]}, {"name": "F0079", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0079 > 0"}]}, {"name": "F0080", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0080 > 0"}]}, {"name": "F0081", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0081 > 0"}]}, {"name": "F0082", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0082 > 0"}]}, {"name": "F0083", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0083 > 0"}]}, {"name": "F0084", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0084 > 0"}]}, {"name": "F0085", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0085 > 0"}]}, {"name": "F0086", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0086 > 0"}]}, {"name": "F0087", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0087 > 0"}]}, {"name": "F0088", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0088 > 0"}]}, {"name": "F0089", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0089 > 0"}]}, {"name": "F0090", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0090 > 0"}]}, {"name": "F0091", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0091 > 0"}]}, {"name": "F0092", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0092 > 0"}]}, {"name": "F0093", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0093 > 0"}]}, {"name": "F0094", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0094 > 0"}]}, {"name": "F0095", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0095 > 0"}]}, {"name": "F0096", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0096 > 0"}]}, {"name": "F0097", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0097 > 0"}]}, {"name": "F0098", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0098 > 0"}]}, {"name": "F0099", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0099 > 0"}]}, {"name": "F0100", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0100 > 0"}]}, {"name": "F0101", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0101 > 0"}]}, {"name": "F0102", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0102 > 0"}]}, {"name": "F0103", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0103 > 0"}]}, {"name": "F0104", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0104 > 0"}]}, {"name": "F0105", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0105 > 0"}]}, {"name": "F0106", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0106 > 0"}]}, {"name": "F0107", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0107 > 0"}]}, {"name": "F0108", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0108 > 0"}]}, {"name": "F0109", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0109 > 0"}]}, {"name": "F0110", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0110 > 0"}]}, {"name": "F0111", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0111 > 0"}]}, {"name": "F0112", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0112 > 0"}]}, {"name": "F0113", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0113 > 0"}]}, {"name": "F0114", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0114 > 0"}]}, {"name": "F0115", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0115 > 0"}]}, {"name": "F0116", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0116 > 0"}]}, {"name": "F0117", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0117 > 0"}]}, {"name": "F0118", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0118 > 0"}]}, {"name": "F0119", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0119 > 0"}]}, {"name": "F0120", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0120 > 0"}]}, {"name": "F0121", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0121 > 0"}]}, {"name": "F0122", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0122 > 0"}]}, {"name": "F0123", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0123 > 0"}]}, {"name": "F0124", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0124 > 0"}]}, {"name": "F0125", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0125 > 0"}]}, {"name": "F0126", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0126 > 0"}]}, {"name": "F0127", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0127 > 0"}]}, {"name": "F0128", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0128 > 0"}]}, {"name": "F0129", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0129 > 0"}]}, {"name": "F0130", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0130 > 0"}]}, {"name": "F0131", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0131 > 0"}]}, {"name": "F0132", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0132 > 0"}]}, {"name": "F0133", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0133 > 0"}]}, {"name": "F0134", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0134 > 0"}]}, {"name": "F0135", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0135 > 0"}]}, {"name": "F0136", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0136 > 0"}]}, {"name": "F0137", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0137 > 0"}]}, {"name": "F0138", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0138 > 0"}]}, {"name": "F0139", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0139 > 0"}]}, {"name": "F0140", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0140 > 0"}]}, {"name": "F0141", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0141 > 0"}]}, {"name": "F0142", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0142 > 0"}]}, {"name": "F0143", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0143 > 0"}]}, {"name": "F0144", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0144 > 0"}]}, {"name": "F0145", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0145 > 0"}]}, {"name": "F0146", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0146 > 0"}]}, {"name": "F0147", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0147 > 0"}]}, {"name": "F0148", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0148 > 0"}]}, {"name": "F0149", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0149 > 0"}]}, {"name": "F0150", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0150 > 0"}]}, {"name": "F0151", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0151 > 0"}]}, {"name": "F0152", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0152 > 0"}]}, {"name": "F0153", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0153 > 0"}]}, {"name": "F0154", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0154 > 0"}]}, {"name": "F0155", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0155 > 0"}]}, {"name": "F0156", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0156 > 0"}]}, {"name": "F0157", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0157 > 0"}]}, {"name": "F0158", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0158 > 0"}]}, {"name": "F0159", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0159 > 0"}]}, {"name": "F0160", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0160 > 0"}]}, {"name": "F0161", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0161 > 0"}]}, {"name": "F0162", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0162 > 0"}]}, {"name": "F0163", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0163 > 0"}]}, {"name": "F0164", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0164 > 0"}]}, {"name": "F0165", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0165 > 0"}]}, {"name": "F0166", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0166 > 0"}]}, {"name": "F0167", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0167 > 0"}]}, {"name": "F0168", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0168 > 0"}]}, {"name": "F0169", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0169 > 0"}]}, {"name": "F0170", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0170 > 0"}]}, {"name": "F0171", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0171 > 0"}]}, {"name": "F0172", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0172 > 0"}]}, {"name": "F0173", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0173 > 0"}]}, {"name": "F0174", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0174 > 0"}]}, {"name": "F0175", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0175 > 0"}]}, {"name": "F0176", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0176 > 0"}]}, {"name": "F0177", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0177 > 0"}]}, {"name": "F0178", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0178 > 0"}]}, {"name": "F0179", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0179 > 0"}]}, {"name": "F0180", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0180 > 0"}]}, {"name": "F0181", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0181 > 0"}]}, {"name": "F0182", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0182 > 0"}]}, {"name": "F0183", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0183 > 0"}]}, {"name": "F0184", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0184 > 0"}]}, {"name": "F0185", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0185 > 0"}]}, {"name": "F0186", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0186 > 0"}]}, {"name": "F0187", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0187 > 0"}]}, {"name": "F0188", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0188 > 0"}]}, {"name": "F0189", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0189 > 0"}]}, {"name": "F0190", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0190 > 0"}]}, {"name": "F0191", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0191 > 0"}]}, {"name": "F0192", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0192 > 0"}]}, {"name": "F0193", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0193 > 0"}]}, {"name": "F0194", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0194 > 0"}]}, {"name": "F0195", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0195 > 0"}]}, {"name": "F0196", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0196 > 0"}]}, {"name": "F0197", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0197 > 0"}]}, {"name": "F0198", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0198 > 0"}]}, {"name": "F0199", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0199 > 0"}]}, {"name": "F0200", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0200 > 0"}]}, {"name": "F0201", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0201 = 0"}]}, {"name": "F0202", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0202 = 0"}]}, {"name": "F0203", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0203 = 0"}]}, {"name": "F0204", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0204 = 0"}]}, {"name": "F0205", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0205 = 0"}]}, {"name": "F0206", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0206 = 0"}]}, {"name": "F0207", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0207 = 0"}]}, {"name": "F0208", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0208 = 0"}]}, {"name": "F0209", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0209 = 0"}]}, {"name": "F0210", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0210 = 0"}]}, {"name": "F0211", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0211 = 0"}]}, {"name": "F0212", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0212 = 0"}]}, {"name": "F0213", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0213 = 0"}]}, {"name": "F0214", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0214 = 0"}]}, {"name": "F0215", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0215 = 0"}]}, {"name": "F0216", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0216 = 0"}]}, {"name": "F0217", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0217 = 0"}]}, {"name": "F0218", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0218 = 0"}]}, {"name": "F0219", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0219 = 0"}]}, {"name": "F0220", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0220 = 0"}]}, {"name": "F0221", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0221 = 0"}]}, {"name": "F0222", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0222 = 0"}]}, {"name": "F0223", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0223 = 0"}]}, {"name": "F0224", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0224 = 0"}]}, {"name": "F0225", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0225 = 0"}]}, {"name": "F0226", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0226 = 0"}]}, {"name": "F0227", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0227 = 0"}]}, {"name": "F0228", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0228 = 0"}]}, {"name": "F0229", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0229 = 0"}]}, {"name": "F0230", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0230 = 0"}]}, {"name": "F0231", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0231 = 0"}]}, {"name": "F0232", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0232 = 0"}]}, {"name": "F0233", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0233 = 0"}]}, {"name": "F0234", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0234 = 0"}]}, {"name": "F0235", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0235 = 0"}]}, {"name": "F0236", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0236 = 0"}]}, {"name": "F0237", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0237 = 0"}]}, {"name": "F0238", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0238 = 0"}]}, {"name": "F0239", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0239 = 0"}]}, {"name": "F0240", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0240 = 0"}]}, {"name": "F0241", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0241 = 0"}]}, {"name": "F0242", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0242 = 0"}]}, {"name": "F0243", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0243 = 0"}]}, {"name": "F0244", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0244 = 0"}]}, {"name": "F0245", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0245 = 0"}]}, {"name": "F0246", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0246 = 0"}]}, {"name": "F0247", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0247 = 0"}]}, {"name": "F0248", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0248 = 0"}]}, {"name": "F0249", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0249 = 0"}]}, {"name": "F0250", "type": "uint32", "nullable": true, "clause": [{"type": "when", "formula": "/root_1000/F0250 = 0"}]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "leaf-list", "name": "leaflist_1000", "min-elements": 0, "max-elements": 10000, "clause": [{"type": "when", "formula": "/root_1000/F0001 != 0"}], "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}]