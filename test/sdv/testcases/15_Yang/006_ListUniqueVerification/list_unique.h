/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 006_ListUniqueVerification
 * Author: tangguagnming
 * Create: 2022-08-10
 */
#ifndef LIST_UNIQUE_H
#define LIST_UNIQUE_H
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string>
#include <unistd.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <string.h>
#include <sys/timerfd.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"
#include "semaphore.h"

using namespace std;

GmcConnT *g_T0_conn = NULL;
GmcStmtT *g_T0_stmt = NULL;
GmcStmtT *g_T0T2_stmt = NULL;

GmcConnT *g_T0_conn2 = NULL;
GmcStmtT *g_T0_stmt2 = NULL;
GmcStmtT *g_T0T2_stmt2 = NULL;

char *g_vertexLabelJson = NULL;
char *g_edgeLabelJson = NULL;
const char *g_cfgJson = R"({"max_record_count":1000, "auto_increment":101, "isFastReadUncommitted":false})";

const char *g_labelName1 = "T0_T2";
const char *g_labelName2 = "T0::T2";
const char *g_labelName3 = "T0";
const char *g_nspName = "Nsp006";

// 清空nsp中所有元数据，包括数据和各种表
void testClearNsp(GmcStmtT *stmt, const char *namespaceName)
{
    AsyncUserDataT data = {0};
    int ret = GmcClearNamespaceAsync(stmt, namespaceName, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void testCreateLabel(GmcStmtT *g_T0_stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.userData = &data;
    insertRequestCtx.insertCb = insert_vertex_callback;

    ret = GmcCreateVertexLabelAsync(g_T0_stmt, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateEdgeLabelAsync(g_T0_stmt, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放json文件
    free(g_vertexLabelJson);
    free(g_edgeLabelJson);
}

void testDropLabel(GmcStmtT *g_T0_stmt)
{
    int ret = 0;
    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(g_T0_stmt, "Nsp006", ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

/*------------------------------------- list -------------------------------*/
int testYangSetField(
    GmcStmtT *stmt, const char *name, GmcDataTypeE type, void *value, uint32_t size, GmcYangPropOpTypeE opType)
{
    int32_t ret;
    GmcPropValueT propValue;
    memcpy(propValue.propertyName, name, (strlen(name)+1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetVertexProperty(stmt, &propValue, opType);
    return ret;
}

// 批量操作
int TestBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

// 初始化根节点
void testRootInit(GmcBatchT *batch, GmcStmtT *root_stmt)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(root_stmt, "T0", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, root_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, root_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 对list表写入初始数据，唯一性字段只有一个值，F0为主键
void TestInsertBasicListData_F2(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt, bool isSetF2Value = true,
    bool F0IsString = false)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    // 插入T0，对应根节点
    ret = testGmcPrepareStmtByLabelName(g_T0_stmt, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0 = 1;
    ret = testYangSetField(g_T0_stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 在list表（T2）写入2条记录
    for (uint32_t i = 1; i <= 2; ++i) {
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isSetF2Value) {
            ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_T0T2_stmt);
}

// 对list表写入初始数据，唯一性字段有多个值，不带默认值，F2为主键
void TestInsertBasicListData_F4(
    GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt, bool isSetF4Value = true, bool F0IsString = false)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    // 插入T0，对应根节点
    ret = testGmcPrepareStmtByLabelName(g_T0_stmt, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0 = 1;
    ret = testYangSetField(g_T0_stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 在list表（T2）写入2条记录
    
    for (uint32_t i = 1; i <= 2; ++i) {
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isSetF4Value) {
            ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &i, sizeof(i),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

// 对list表写入初始数据，唯一性字段有多个值，带默认值，F2为主键
void TestInsertBasicListData_F5(
    GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt, bool isSetF5Value = true, bool F0IsString = false)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    // 插入T0，对应根节点
    ret = testGmcPrepareStmtByLabelName(g_T0_stmt, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0 = 1;
    ret = testYangSetField(g_T0_stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 在list表（T2）写入2条记录
    for (uint32_t i = 1; i <= 2; ++i) {
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isSetF5Value) {
            ret = testYangSetField(g_T0T2_stmt, "F5", GMC_DATATYPE_INT32, &i, sizeof(i),
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

// 对list表写入初始数据，唯一性字段有多个值，不带默认值，F0为主键
void TestInsertBasicListData_F14(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    // 插入T0，对应根节点
    ret = testGmcPrepareStmtByLabelName(g_T0_stmt, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0 = 1;
    ret = testYangSetField(g_T0_stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 在list表（T2）写入2条记录
    
    for (uint32_t i = 1; i <= 2; ++i) {
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F5", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F6", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F7", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F8", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F9", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F10", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F11", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F12", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F13", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F14", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("第%d个F1 = %d \n", i, i);
               
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

// 对list表写入初始数据，唯一性字段有多个值，不带默认值，F0为主键
void TestInsertBasicListData_F15(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    // 插入T0，对应根节点
    ret = testGmcPrepareStmtByLabelName(g_T0_stmt, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0 = 1;
    ret = testYangSetField(g_T0_stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 在list表（T2）写入2条记录
    
    for (uint32_t i = 1; i <= 2; ++i) {
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F5", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F6", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F7", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F8", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F9", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F10", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F11", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F12", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F13", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F14", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F15", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

// 对list表写入初始数据，唯一性字段有多个值，不带默认值，F0为主键
void TestInsertBasicListData_F16(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    // 插入T0，对应根节点
    ret = testGmcPrepareStmtByLabelName(g_T0_stmt, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f0 = 1;
    ret = testYangSetField(g_T0_stmt, "F0", GMC_DATATYPE_INT32, &f0, sizeof(f0),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 在list表（T2）写入2条记录
    
    for (uint32_t i = 1; i <= 2; ++i) {
        ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F5", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F6", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F7", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F8", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F9", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F10", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F11", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F12", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F13", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F14", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F15", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetField(g_T0T2_stmt, "F16", GMC_DATATYPE_INT32, &i, sizeof(i),
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
               
        ret = GmcBatchAddDML(batch, g_T0T2_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

// 向表中增加一条数据，F0/F2为主键，具体看shemale定义
void TestAddDataToListSuccess_F4(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt,
    int32_t F0 = 0, int32_t F1 = 0, int32_t F2 = 0, int32_t F3 = 0, int32_t F4 = 0)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    testRootInit(batch, g_T0_stmt);

    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置属性值
    ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &F0, sizeof(F0),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &F1, sizeof(F1),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &F2, sizeof(F2),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &F3, sizeof(F3),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &F4, sizeof(F4),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

// 向表中增加一条数据，F2为主键，唯一性字段冲突
void TestAddDataToListByListLocalhashViolation_F4(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt,
    int32_t F0 = 0, int32_t F1 = 0, int32_t F2 = 0, int32_t F3 = 0, int32_t F4 = 0)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    testRootInit(batch, g_T0_stmt);

    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置属性值
    ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &F0, sizeof(F0),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &F1, sizeof(F1),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &F2, sizeof(F2),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &F3, sizeof(F3),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &F4, sizeof(F4),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_T0T2_stmt);
}

// 向表中增加一条数据，F2为主键，主键冲突
void TestAddDataToListByPrimaryKeyViolation_F4(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt,
    int32_t F0 = 0, int32_t F1 = 0, int32_t F2 = 0, int32_t F3 = 0, int32_t F4 = 0)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);
    
    testRootInit(batch, g_T0_stmt);
    
    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置属性值
    ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &F0, sizeof(F0),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &F1, sizeof(F1),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &F2, sizeof(F2),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &F3, sizeof(F3),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &F4, sizeof(F4),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    
    GmcFreeStmt(g_T0T2_stmt);
}

// 向表中增加一条数据，F2为主键，主键冲突
void TestAddDataToListByPrimaryKeyViolation_F41(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt,
    int32_t F0 = 0, int32_t F1 = 0, int32_t F2 = 0, int32_t F3 = 0, int32_t F4 = 0)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);
    
    testRootInit(batch, g_T0_stmt);
    
    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置属性值
    ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &F0, sizeof(F0),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &F1, sizeof(F1),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &F2, sizeof(F2),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &F3, sizeof(F3),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &F4, sizeof(F4),
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    
    GmcFreeStmt(g_T0T2_stmt);
}

// merge修改数据，F2为主键
void TestMergeVertexSuccess_F4(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt,
    int32_t F0 = 0, int32_t F1 = 0, int32_t F2 = 0, int32_t F3 = 0, int32_t F4 = 0)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    testRootInit(batch, g_T0_stmt);

    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 绑定子节点并设置索引名
    ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_T0T2_stmt, "T2.F2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置主键索引字段
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 1, GMC_DATATYPE_INT32, &F0, sizeof(F0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 2, GMC_DATATYPE_INT32, &F1, sizeof(F1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 3, GMC_DATATYPE_INT32, &F2, sizeof(F2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &F3, sizeof(F3),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &F4, sizeof(F4),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

// merge修改数据，F0为主键
void TestMergeVertexSuccessMultiSecIndex_F4(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt,
    int32_t F0 = 0, int32_t F1 = 0, int32_t F2 = 0, int32_t F3 = 0, int32_t F4 = 0)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    testRootInit(batch, g_T0_stmt);

    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 绑定子节点并设置索引名
    ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_T0T2_stmt, "T2.F0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置主键索引字段
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 1, GMC_DATATYPE_INT32, &F0, sizeof(F0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &F1, sizeof(F1),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &F2, sizeof(F2),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &F3, sizeof(F3),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &F4, sizeof(F4),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

// merge修改数据失败，F2为主键，唯一性字段冲突
void TestMergeVertexListLocalhashViolation_F4(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt,
    int32_t F0 = 0, int32_t F1 = 0, int32_t F2 = 0, int32_t F3 = 0, int32_t F4 = 0)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);
    
    testRootInit(batch, g_T0_stmt);

    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 绑定子节点并设置索引名
    ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_T0T2_stmt, "T2.F2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置主键索引字段
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 1, GMC_DATATYPE_INT32, &F0, sizeof(F0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 2, GMC_DATATYPE_INT32, &F1, sizeof(F1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 3, GMC_DATATYPE_INT32, &F2, sizeof(F2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &F3, sizeof(F3),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &F4, sizeof(F4),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_T0T2_stmt);
}

// merge修改数据，F0为主键
void TestMergeVertexFailureMultiSecIndex_F4(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt,
    int32_t F0 = 0, int32_t F1 = 0, int32_t F2 = 0, int32_t F3 = 0, int32_t F4 = 0)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    testRootInit(batch, g_T0_stmt);

    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 绑定子节点并设置索引名
    ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_T0T2_stmt, "T2.F0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置主键索引字段
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 1, GMC_DATATYPE_INT32, &F0, sizeof(F0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &F1, sizeof(F1),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &F2, sizeof(F2),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &F3, sizeof(F3),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &F4, sizeof(F4),
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

// replace修改数据成功，F2为主键
void TestReplaceVertexSuccess_F4(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt,
    int32_t F0 = 0, int32_t F1 = 0, int32_t F2 = 0, int32_t F3 = 0, int32_t F4 = 0)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    testRootInit(batch, g_T0_stmt);

    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 绑定子节点并设置索引名
    ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_T0T2_stmt, "T2.F2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置主键索引字段
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 1, GMC_DATATYPE_INT32, &F0, sizeof(F0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 2, GMC_DATATYPE_INT32, &F1, sizeof(F1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 3, GMC_DATATYPE_INT32, &F2, sizeof(F2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &F0, sizeof(F0),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &F1, sizeof(F1),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &F2, sizeof(F2),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &F3, sizeof(F3),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &F4, sizeof(F4),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

// replace修改数据失败，F2为主键，唯一性字段冲突
void TestReplaceVertexListLocalhashViolation_F4(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt,
    int32_t F0 = 0, int32_t F1 = 0, int32_t F2 = 0, int32_t F3 = 0, int32_t F4 = 0)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    testRootInit(batch, g_T0_stmt);

    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 绑定子节点并设置索引名
    ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_T0T2_stmt, "T2.F2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置主键索引字段
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 1, GMC_DATATYPE_INT32, &F0, sizeof(F0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 2, GMC_DATATYPE_INT32, &F1, sizeof(F1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 3, GMC_DATATYPE_INT32, &F2, sizeof(F2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(g_T0T2_stmt, "F0", GMC_DATATYPE_INT32, &F0, sizeof(F0),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F1", GMC_DATATYPE_INT32, &F1, sizeof(F1),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F2", GMC_DATATYPE_INT32, &F2, sizeof(F2),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F3", GMC_DATATYPE_INT32, &F3, sizeof(F3),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_T0T2_stmt, "F4", GMC_DATATYPE_INT32, &F4, sizeof(F4),
        GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNIQUE_VIOLATION, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

// delete一条数据，F2为主键
void TestDeleteOneRecord_F4(GmcConnT *g_T0_conn, GmcStmtT *g_T0_stmt, int32_t F0 = 0,
    int32_t F1 = 0, int32_t F2 = 0)
{
    int ret = 0;
    // 初始化T2句柄
    GmcStmtT *g_T0T2_stmt = NULL;

    // 申请句柄
    ret = GmcAllocStmt(g_T0_conn, &g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批量处理
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_T0_conn, &batch);

    testRootInit(batch, g_T0_stmt);

    ret = testGmcPrepareStmtByLabelName(g_T0T2_stmt, "T0::T2", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_T0_stmt, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 设置需要删除数据的索引键和主键索引值
    ret = GmcSetIndexKeyName(g_T0T2_stmt, "T2.F2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 1, GMC_DATATYPE_INT32, &F0, sizeof(F0));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 2, GMC_DATATYPE_INT32, &F1, sizeof(F1));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_T0T2_stmt, 3, GMC_DATATYPE_INT32, &F2, sizeof(F2));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_T0T2_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_T0T2_stmt);
}

static const char *g_subtreeExportNull = R"(
{
    "F0":1
}
)";

bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB);

bool testYangJsonIsEqualReal(const json_t *valueA, const json_t *valueB)
{
    double doubleA = json_real_value(valueA);
    double doubleB = json_real_value(valueB);
    if (fabs(doubleA - doubleB) < 1e-6) {
        return true;
    }
    return false;
}

bool testYangJsonIsEqualField(const json_t *valueA, const json_t *valueB)
{
    if (json_typeof(valueA) == JSON_STRING) {
        return strcmp(json_string_value(valueA), json_string_value(valueB)) == 0;
    }
    if (json_typeof(valueA) == JSON_INTEGER) {
        return json_integer_value(valueA) == json_integer_value(valueB);
    }
    if (json_typeof(valueA) == JSON_REAL) {
        return testYangJsonIsEqualReal(valueA, valueB);
    }
    return true;
}

bool testYangJsonIsEqualArray(const json_t *valueA, const json_t *valueB)
{
    bool isEqual = true;
    uint32_t sizeA = (uint32_t)json_array_size(valueA);
    uint32_t sizeB = (uint32_t)json_array_size(valueB);
    if (sizeA != sizeB) {
        return false;
    }
    for (uint32_t i = 0; i < sizeA; ++i) {
        json_t *itemA = json_array_get(valueA, i);
        json_t *itemB = json_array_get(valueB, i);
        if (json_typeof(itemA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(itemA, itemB);
        } else {
            isEqual = testYangJsonIsEqualField(itemA, itemB);
        }
        if (!isEqual) {
            return false;
        }
    }
    return true;
}

bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB)
{
    bool isEqual = true;
    void *itA = json_object_iter((json_t *)jsonA);
    void *itB = json_object_iter((json_t *)jsonB);

    if (itA) {
        const char *keyA = json_object_iter_key(itA);

        while ((strcmp(keyA, "ID") == 0) || (strcmp(keyA, "PID") == 0)) {
            itA = json_object_iter_next((json_t *)jsonA, itA);
            if (itA == NULL) {
                return true;
            }
            keyA = json_object_iter_key(itA);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }
    if (itB) {
        const char *keyB = json_object_iter_key(itB);

        while ((strcmp(keyB, "ID") == 0) || (strcmp(keyB, "PID") == 0)) {
            itB = json_object_iter_next((json_t *)jsonA, itB);
            if (itB == NULL) {
                return true;
            }
            keyB = json_object_iter_key(itB);  // 规避方案，subtree查出来的ID和PID不对用户体现，不参与结果比较
        }
    }

    while (itA && itB) {
        const char *keyA = json_object_iter_key(itA);
        json_t *valueA = json_object_iter_value(itA);
        const char *keyB = json_object_iter_key(itB);
        json_t *valueB = json_object_iter_value(itB);

        if ((json_typeof(valueA) != json_typeof(valueB)) || (strcmp(keyA, keyB) != 0)) {
            return false;
        }
        if (json_typeof(valueA) == JSON_OBJECT) {
            isEqual = testYangJsonIsEqualInner(valueA, valueB);
        } else if (json_typeof(valueA) == JSON_ARRAY) {
            isEqual = testYangJsonIsEqualArray(valueA, valueB);
        } else {
            isEqual = testYangJsonIsEqualField(valueA, valueB);
        }
        if (!isEqual) {
            return false;
        }
        itA = json_object_iter_next((json_t *)jsonA, itA);
        itB = json_object_iter_next((json_t *)jsonB, itB);
    }

    return itA == itB;
}

bool testYangJsonIsEqual(const char *json1, const char *json2)
{
    json_error_t jsonError;
    json_t *jsonA = json_loads(json1, JSON_REJECT_DUPLICATES, &jsonError);
    json_t *jsonB = json_loads(json2, JSON_REJECT_DUPLICATES, &jsonError);
    bool isEqual = testYangJsonIsEqualInner(jsonA, jsonB);
    json_decref(jsonA);
    json_decref(jsonB);
    return isEqual;
}
// userData结构
struct SubtreeFilterCbParam {
    int step;
    int32_t expectStatus;          // 预期的操作状态
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};

// userData ：用户数据 replyJson ：服务端返回的子树 json status ：服务器端操作处理结果  errMsg ：错误信息
void AsyncSubtreeFilterCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    SubtreeFilterCbParam *param = (SubtreeFilterCbParam *)(userData);
    param->expectStatus = status;
    if (GMERR_OK != status) {
        AW_FUN_Log(LOG_ERROR, "[err] status is %d  errMsg  is %s   \n ", status, errMsg);
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
    ASSERT_TRUE(isEnd);
    ASSERT_EQ(1, count);
    ASSERT_TRUE(jsonReply != NULL);
    if (param->expectReplyJson != NULL) {
        ASSERT_TRUE(testYangJsonIsEqual((const char*)jsonReply[0], param->expectReplyJson));
    } else {
        AW_FUN_Log(LOG_ERROR, "[err] no replyjson   \n ");
    }
    param->step++;
}

int testWaitAsyncSubtreeRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    SubtreeFilterCbParam *userdata1 = (SubtreeFilterCbParam *)userData;
    while (userdata1->step != expRecvNum) {
        usleep(10);
        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf ", (double)duration / 1000000);
            return -1;  // 接收超时
        }
    }
    return 0;
}

// subtree查询
void testSubtreeFilter(GmcStmtT *stmt, const char * rootName, const char * jsonName)
{
    int ret;

    // subtree 查询
    char replyPath[1024] = {0};
    ret = snprintf(replyPath, 1024, "SubtreeReplyJson/%s_Reply.json", jsonName);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *replyJson = NULL;
    readJanssonFile(replyPath, &replyJson);
    ASSERT_NE((void *)NULL, replyJson);

    SubtreeFilterCbParam data = {0};
    data.expectReplyJson = replyJson;
    data.expectStatus = GMERR_OK;
    data.step = 0;

    char filterPath[1024] = {0};
    ret = snprintf(filterPath, 1024, "SubTreeFilterJson/Yang_006.json");
    AW_MACRO_EXPECT_NE_INT(0, ret);
    char *filterJson = NULL;
    readJanssonFile(filterPath, &filterJson);
    ASSERT_NE((void *)NULL, filterJson);

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = rootName;
    filter.subtree.json = filterJson;
    filter.jsonFlag =  GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncSubtreeFilterCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.expectStatus);

    free(filterJson);
    free(replyJson);
    filterJson = NULL;
    replyJson = NULL;
}

void TestSubtreeQueryData()
{
    int ret;
    SubtreeFilterCbParam data = {0};

    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = g_subtreeExportNull;
    filter.jsonFlag = GMC_JSON_EXPORT_NULL_INFO;
    filter.defaultMode = GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED;
    char *replyJson = NULL;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_JSON;
    filters.filter = &filter;
    ret = GmcYangSubtreeFilterExecuteAsync(g_T0_stmt, &filters, NULL, AsyncSubtreeFilterCb, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.expectStatus);
    GmcFreeJsonStr(g_T0_stmt, replyJson);
}

void testCreateLabelAndInsertData(GmcStmtT *T0_stmt, GmcConnT *T0_conn, GmcBatchT *batch = NULL)
{
    TestBatchPrepare(T0_conn, &batch);

    // 读文件
    readJanssonFile("schemaFile/unique_vertexLabelNormal.gmjson", &g_vertexLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_vertexLabelJson);
    readJanssonFile("schemaFile/unique_edgeLabel.gmjson", &g_edgeLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_edgeLabelJson);

    // 建表
    testCreateLabel(T0_stmt);

    // 向表中写入两条数据A和B 1 1 1 1 1 / 2 2 2 2 2
    TestInsertBasicListData_F4(T0_conn, T0_stmt, true, false);
    testRootInit(batch, T0_stmt);
}

#endif /* LIST_UNIQUE_H */
