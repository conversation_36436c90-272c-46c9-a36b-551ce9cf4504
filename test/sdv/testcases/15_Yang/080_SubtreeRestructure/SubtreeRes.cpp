/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 080_SubtreeRestructure
 * Author: hanyang
 * Create: 2024-03-12
 */
#include "SubtreeRes.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[100] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[100] = {0};

class SubtreeRestructure_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void SubtreeRestructure_test::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void SubtreeRestructure_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
}

void SubtreeRestructure_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 100; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建表
    TestCreateLabel(g_stmt_async);

    AW_CHECK_LOG_BEGIN();
}

void SubtreeRestructure_test::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AW_CHECK_LOG_END();

    // 删除表
    TestDropLabel(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 100; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 100; i++) {
        g_stmt_list[i] = NULL;
    }
    g_rootNode = NULL;
    for (i = 0; i < 100; i++) {
        g_childNode[i] = NULL;
    }
}

/*
root--con_1(NP,无默认值)--list_1
    --con_2(NP,全默认值)--list_2
    --con_3(P,无默认值)--list_3
    --con_4(P,全默认值)--list_4
    --leaflist_1(无默认值)
    --leaflist_2(默认值，555,666,777)
*/

/*****************************************************************************
 Description  : 001.NP节点可见，属性无默认值和用户设置值，下层节点不符合查询条件，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_1", "list_1", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_001_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_001_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_001_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_001_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 002.NP节点可见，属性有默认值和用户设置值，用户设置值等于默认值，
                下层节点不符合查询条件，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 1;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_2", "list_2", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_002_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_002_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_002_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_002_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 003.NP节点可见，属性有默认值和用户设置值，用户设置值不等于默认值，
                下层节点不符合查询条件，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 2;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_2", "list_2", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_003_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_003_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_003_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_003_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 004.NP节点可见，属性有默认值和用户设置值，都不符合本节点内容过滤，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 2;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_2", "list_2", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_004_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_004_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_004_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_004_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 005.NP节点可见，属性有默认值，默认值不符合本节点内容过滤，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 1;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_2", "list_2", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_005_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_005_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_005_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_005_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 006.NP节点可见，不符合本节点叶子过滤，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_1", "list_1", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，叶子过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_006_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，叶子过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_006_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，叶子过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_006_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，叶子过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_006_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 007.NP节点可见，属性无默认值和用户设置值，本节点容器过滤，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_1", "list_1", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，容器过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_007_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，容器过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_007_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，容器过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_007_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，容器过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_007_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 008.NP节点可见，属性设置值都等于默认值，不符合trim模式过滤结果，返回空NP节点
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 1;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_2", "list_2", fieldValue, insertType, false);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，容器过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_008_01", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 009.NP节点可见，只有默认值，不设置用户值，不符合explicit模式过滤结果，返回空NP节点
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_2", "list_2", fieldValue, insertType, false);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，容器过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_009_01", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 010.NP节点可见，属性无默认值和用户设置值，不符合report-all模式过滤结果，返回空NP节点
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_1", "list_1", fieldValue, insertType, false);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，容器过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_010_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 011.NP节点可见，属性无默认值和用户设置值，不符合report-all-tagged模式过滤结果，返回空NP节点
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_1", "list_1", fieldValue, insertType, false);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，容器过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_011_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 012.P节点可见，属性无默认值和用户设置值，下层节点不符合查询条件，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_3", "list_3", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_012_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_012_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_012_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_012_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 013.P节点可见，属性有默认值和用户设置值，用户设置值等于默认值，
                下层节点不符合查询条件，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 1;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_4", "list_4", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_013_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_013_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_013_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_013_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 014.P节点可见，属性有默认值和用户设置值，用户设置值不等于默认值，
                下层节点不符合查询条件，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 2;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_4", "list_4", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_014_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_014_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_014_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_childNode[2], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_014_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 015.P节点可见，属性有默认值和用户设置值，都不符合本节点内容过滤，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 2;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_4", "list_4", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_015_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_015_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_015_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_015_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 016.P节点可见，属性有默认值，默认值不符合本节点内容过滤，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 1;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_4", "list_4", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_016_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_016_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_016_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_016_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 017.P节点可见，不符合本节点叶子过滤，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_3", "list_3", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，叶子过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_017_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，叶子过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_017_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，叶子过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_017_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，叶子过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_017_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 018.P节点可见，属性无默认值和用户设置值，本节点容器过滤，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_3", "list_3", fieldValue, insertType);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，容器过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_018_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，容器过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_018_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，容器过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_018_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，容器过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_018_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 019.P节点可见，属性设置值都等于默认值，不符合trim模式过滤结果，返回空P节点
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 1;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_4", "list_4", fieldValue, insertType, false);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，容器过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_019_01", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 020.P节点可见，只有默认值，不设置用户值，不符合explicit模式过滤结果，返回空P节点
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_4", "list_4", fieldValue, insertType, false);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，容器过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_4", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_020_01", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 021.P节点可见，属性无默认值和用户设置值，不符合report-all模式过滤结果，返回空P节点
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_3", "list_3", fieldValue, insertType, false);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，容器过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_021_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 022.P节点可见，属性无默认值和用户设置值，不符合report-all-tagged模式过滤结果，返回空P节点
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    insertType = 0;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertData(g_conn_async, "root", "con_3", "list_3", fieldValue, insertType, false);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，容器过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_3", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_022_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 023.list下有子节点，list有多条数据，过滤条件为子节点容器过滤，
                返回所有匹配的list数据，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertDataListCon1(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，容器过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_023_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，容器过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_023_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，容器过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_023_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，容器过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_023_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 024.list下有子节点，list有多条数据，过滤条件为子节点叶子过滤，
                返回所有匹配的list数据，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertDataListCon1(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，叶子过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_024_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，叶子过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_024_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，叶子过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_024_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，叶子过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_NULL, NULL, 0,
        "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_NULL, NULL, 0,
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_024_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 025.list下有子节点，list有多条数据，过滤条件为子节点内容过滤，
                返回所有匹配的list数据，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertDataListCon1(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 300;
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_025_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 300;
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_025_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 300;
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_025_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[1], "list_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 300;
    ret = TestYangSetField(g_childNode[3], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_025_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 026.leaflist有默认值，默认值符合内容过滤条件，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 666;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_026_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 666;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_026_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 666;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_026_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 666;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_026_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 027.leaflist有默认值和用户设置值，默认值符合内容过滤条件，覆盖四种默认值查询模式
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertDataLeaflist(g_conn_async, "root", "leaflist_2", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 666;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_027_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 666;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_027_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 666;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_027_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 666;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_027_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 028.leaflist有默认值和用户设置值，用户设置值符合内容过滤条件，
                覆盖四种默认值查询模式，删除用户设置值，相同条件再次查询
 Author       : hanyang
*****************************************************************************/
TEST_F(SubtreeRestructure_test, Yang_080_Func_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t insertType;  //0:不设置属性值，1:设置和默认值相同值，2:设置和默认值不同值
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 写入预期数据
    fieldValue = 100;
    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertDataLeaflist(g_conn_async, "root", "leaflist_2", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_028_01", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_028_02", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_028_03", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_028_04", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除PK=3的数据数据
    fieldValue = 3;
    TestDeleteDataLeaflist(g_conn_async, "root", "leaflist_2", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询，内容过滤，report-all-tagged
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_028_05", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL_TAGGED);

    // subtree查询，内容过滤，report-all
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_028_06", "root",
        GMC_DEFAULT_FILTER_REPORT_ALL);

    // subtree查询，内容过滤，explicit
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_028_07", "root",
        GMC_DEFAULT_FILTER_EXPLICIT);

    // subtree查询，内容过滤，trim
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_rootNode = NULL;
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_rootNode, "leaflist_2", GMC_OPERATION_SUBTREE_FILTER, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    ret = TestYangSetField(g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t),
        "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestSubtreeFilterObj(g_stmt_async, g_rootNode, "Yang_080_Func_028_08", "root",
        GMC_DEFAULT_FILTER_TRIM);

    // 事务提交
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
