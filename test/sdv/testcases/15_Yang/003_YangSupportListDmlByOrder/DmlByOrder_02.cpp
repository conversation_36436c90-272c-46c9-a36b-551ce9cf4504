/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : 图模型支持list节点有序
 Notes        : 028 container节点不支持数据的移动，预期按list节点流程进行container节点位置移动预期位置移动接口无效
                029
case节点不支持数据的移动，预期按list节点流程进行case节点数据位置移动预期位置移动接口无效。切换到另外一个文件 053
同父亲多个list子节点场景，子节点List1进行非位置移动的DMl操作，
                    子节点list2进行位置移动的DML操作,子节点list1进行位置移动DMl操作，
                    子节点lsit2进行非位置移动DML操作，L预期都成功
                056 多个线程对同一个父亲的2个list顶点进行操作，
                    线程一对list1进行纯数据的操作，后进行list节点的位置操作。
                    线程二对list2进行位置移动DMl操作后，再进行纯数据的操作。

 History      :
 Author       : youwanyong ywx1157510
 Modification : 2022/7/23
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <atomic>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "YangLISTDMLOperation_test.h"

using namespace std;

GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_Con = NULL;
int32_t ret;
GmcStmtT *g_stmt_C1 = NULL;
GmcStmtT *g_stmt_C2 = NULL;
GmcStmtT *g_stmt_C3 = NULL;

#define T0 "T0"
#define T2 "T0:T2"
#define T3 "T0:T3"
#define CC0 "CC0"
#define CC1 "CC0:CC1"
#define C0 "C0"
#define C1 "C0:C1"
#define C2 "C0:C1:C2"
#define C3 "C0:C1:C3"

class YangListNormalDmlByOrder_02 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void YangListNormalDmlByOrder_02::SetUpTestCase()
{
    int ret;

    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
}

void YangListNormalDmlByOrder_02::TearDownTestCase()
{   
    AW_FUN_Log(LOG_STEP, "testcase end");
    int ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    testEnvClean();
}

void YangListNormalDmlByOrder_02::SetUp()
{
    int ret;

    g_mstrx_config.transMode = GMC_TRANS_USED_IN_CS;
    g_mstrx_config.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mstrx_config.readOnly = false;
    g_mstrx_config.trxType = GMC_OPTIMISTIC_TRX;

    ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观
    AsyncUserDataT data{0};
    GmcDropNamespace(g_stmt, g_namespace);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt, g_namespace);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
}

void YangListNormalDmlByOrder_02::TearDown()
{
    AW_CHECK_LOG_END();
    int ret;
    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 异步删除namespace 
    AW_FUN_Log(LOG_STEP, "异步删除namespace");
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    uint32_t i = 0;
    GmcFreeStmt(g_stmt_root);

    g_stmt_Con = NULL;
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
}

/* Yang模型示例
                       (container)root
                             |
                             |
                       container(child)


/*****************************************************************************
Description  :   028 container节点不支持数据的移动，预期按list节点流程进行container节点位置移动预期位置移动接口无效。
切换到另外一个文件

Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_028)
{
    uint32_t keyvalue;
    uint32_t newvalue;
    uint32_t pid;
    int ret = 0;
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "创建container子表");

    readJanssonFile("schema_file/Con_Con.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vlabelSchema, g_msConfigTrans);
    ASSERT_EQ(GMERR_OK, ret);
    free(vlabelSchema);

    AW_FUN_Log(LOG_STEP, "进行节点的DML操作");

    /* **************************insert********************************** */
   ret = testTransStartAsync(g_conn_async, g_mstrx_config);
   EXPECT_EQ(GMERR_OK, ret);
   keyvalue = 100;
   testInsertConConRoot(g_conn_async, keyvalue, CC0);
   testInsertConConChild(g_conn_async, keyvalue, CC0, CC1);
   testTransCommitAsync(g_conn_async);
   // 查询校验值
   uint64_t count = 1;

   /* **************************container子节点进行位置移动DMl操作********************************** */
   // 启动事务
   ret = testTransStartAsync(g_conn_async, g_mstrx_config);
   EXPECT_EQ(GMERR_OK, ret);

   // 设置批处理batch参数
   ret = testBatchPrepare(g_conn_async, &batch);
   EXPECT_EQ(GMERR_OK, ret);

   // 设置根节点
   ret = testGmcPrepareStmtByLabelName(g_stmt_root, CC0, GMC_OPERATION_NONE);
   EXPECT_EQ(GMERR_OK, ret);
   ret = GmcYangSetRoot(batch, g_stmt_root);
   EXPECT_EQ(GMERR_OK, ret);
   GmcNodeT *rootNode = NULL, *childNode = NULL;
   ret = GmcGetRootNode(g_stmt_root, &rootNode);
   AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
   ret = GmcYangEditChildNode(rootNode, CC1, GMC_OPERATION_MERGE, &childNode);
   AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   uint32_t f0 = 6;
   GmcPropValueT refKey;
   GmcYangListLocatorT listProp;
   for (uint32_t i = 6; i <= 10; ++i) {
       // 设置子节点位置移动选择GMC_LIST_LAST MERGE
       f0 = i - 1;
       InitRefKeys(&refKey, 1, &f0);
       InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, &refKey);
       ret = GmcYangSetListLocator(g_stmt_root, &listProp);
       EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
       UninitListProperty(&listProp);

       // 设置属性值
       ret = GmcSetIndexKeyName(g_stmt_root, g_keyname);
       AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
       newvalue = i;
       testYangSetNodeProperty(childNode, newvalue, GMC_YANG_PROPERTY_OPERATION_MERGE);

       GmcBatchAddDML(batch, g_stmt_root);
    }

    /* **************************异步等待接受消息********************************** */
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(5, data.totalNum);
    ASSERT_EQ(5, data.succNum);

    GmcBatchDestroy(batch);

    // 事务提交
    testTransCommitAsync(g_conn_async);

    /* ---------------------------------删表，回收资源----------------- */
    sleep(1);
    AW_FUN_Log(LOG_STEP, "删除container-Con类型的vertex表和edge表");
}

/* Yang模型示例
                           root(con)
                             |
                             |
                           choice
                ┬------------|------------┐
                |                         |
               case                     case
*/

/* ****************************************************************************
 Description  :   029
case节点不支持数据的移动，预期按list节点流程进行case节点数据位置移动预期位置移动接口无效。切换到另外一个文件

 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_029)
{
    uint32_t keyvalue;
    uint32_t newvalue;
    uint32_t pid;
    int ret = 0;
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "创建container子表");

    readJanssonFile("schema_file/Con_Choice_Case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vlabelSchema, g_msConfigTrans);
    ASSERT_EQ(GMERR_OK, ret);

    free(vlabelSchema);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    /* **************************insert********************************** */

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, C0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL, *childNode1 = NULL, *childNode2 = NULL, *childNode3 = NULL;
    ret = GmcGetRootNode(g_stmt_root, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyvalue = 100;
    testYangSetNodeProperty_PK(rootNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(rootNode, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置child choice节点
    ret = GmcYangEditChildNode(rootNode, C1, GMC_OPERATION_INSERT, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode1, C2, GMC_OPERATION_INSERT, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    keyvalue = 100;
    testYangSetNodeProperty_PK(childNode2, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(childNode2, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    /* **************************case子节点进行位置移动DMl操作********************************** */
    uint32_t f0 = 6;
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;
    // 设置子节点位置移动选择GMC_LIST_LAST
    // 设置child case2节点
    f0 = 5;
    InitRefKeys(&refKey, 1, &f0);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, &refKey);
    ret = GmcYangSetListLocator(g_stmt_root, &listProp);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    UninitListProperty(&listProp);

    ret = GmcYangEditChildNode(childNode1, C3, GMC_OPERATION_INSERT, &childNode3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    keyvalue = 10;
    testYangSetNodeProperty_PK(childNode3, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(childNode3, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    GmcBatchAddDML(batch, g_stmt_root);

    /* **************************异步等待接受消息********************************** */
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(1, data.totalNum);
    ASSERT_EQ(1, data.succNum);
    AW_FUN_Log(LOG_STEP, "last批操作验证完毕，8条全部执行成功");

    GmcBatchDestroy(batch);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    // ---------------------------------删表，回收资源-----------------
    sleep(1);
    AW_FUN_Log(LOG_STEP, "删除container-Choice_Case类型的vertex表和edge表");
    // 删除container-Con类型的vertex表和edge表
}

static void InsertBasicList2Data(
    GmcConnT *conn, GmcStmtT *rootStmt, GmcStmtT *t2Stmt, GmcConnT *tconn, GmcStmtT *tstmt, int t2Count = 5)
{
    // 批操作准备
    GmcBatchT *batch = NULL;
    ASSERT_NO_FATAL_FAILURE(LltBatchPrepare(conn, &batch, true));
    // 插入T0，对应根节点
    ASSERT_EQ(GMERR_OK, testGmcPrepareStmtByLabelName(rootStmt, T0, GMC_OPERATION_NONE));
    ASSERT_EQ(GMERR_OK, GmcYangSetRoot(batch, rootStmt));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, rootStmt));

    // 插入5条T3，list类型,
    for (uint32_t i = 1; i <= 5; ++i) {
        ASSERT_EQ(GMERR_OK, testGmcPrepareStmtByLabelName(t2Stmt, T3, GMC_OPERATION_INSERT));
        ASSERT_EQ(GMERR_OK, GmcYangBindChild(batch, rootStmt, t2Stmt));
        ret = testYangSetField(
            t2Stmt, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
        EXPECT_EQ(GMERR_OK, ret);
        ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, t2Stmt));
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(6, data.totalNum);
    ASSERT_EQ(6, data.succNum);
}
void normalDML(
    GmcBatchT *batch, GmcStmtT *g_stmt_root, GmcStmtT *g_stmt_list, const char *vertexLabelName, uint32_t keyvalue = 1)
{
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_list, vertexLabelName, GMC_OPERATION_DELETE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    EXPECT_EQ(GMERR_OK, ret);
    keyvalue = 4;
    // 设置key
    uint32_t pid = 1;
    testSetKeyNameAndValue(g_stmt_list, keyvalue, pid);
    GmcBatchAddDML(batch, g_stmt_list);
}

/* Yang模型示例
                          root(con)
                             |
   ┌-------------------------┼-------------------------┐
   |                                                   |
list1                                                 list2
*/

/* ****************************************************************************
 Description  :   053 同父亲多个list子节点场景，子节点List1进行非位置移动的DMl操作，
                  子节点list2进行位置移动的DML操作,子节点list1进行位置移动DMl操作，
                  子节点lsit2进行非位置移动DML操作，L预期都成功

 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_053)
{
    int ret;
    GmcBatchT *batch = NULL;
    // 创建拥有两个list子节点的yang模型
    char *vlabelSchema = NULL;
    char *vlabelSchema1 = NULL;
    uint32_t keyvalue;

    ret = GmcAllocStmt(g_conn_async, &g_stmt_C1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_C2);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "创建俩个list子表");

    readJanssonFile("schema_file/Con_double_List.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vlabelSchema, g_msConfigTrans);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/Con_double_List_Edge.gmjson", &vlabelSchema1);
    ASSERT_NE((void *)NULL, vlabelSchema1);
    ret = GmcCreateEdgeLabel(g_stmt, vlabelSchema1, g_msConfigTrans);
    ASSERT_EQ(GMERR_OK, ret);

    free(vlabelSchema);
    free(vlabelSchema1);

    // 初始数据
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);
    InsertBasicListData(g_conn_async, g_stmt_root, g_stmt_C1, g_conn, g_stmt);
    InsertBasicList2Data(g_conn_async, g_stmt_root, g_stmt_C2, g_conn, g_stmt);
    testTransCommitAsync(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    /* **************************insert********************************** */

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* ****************List1进行非位置移动的DMl操作，子节点list2进行位置移动的DML操作******************* */
    normalDML(batch, g_stmt_root, g_stmt_C1, T2);
    uint32_t f0;
    GmcYangListLocatorT listProp;

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点位置移动选择GMC_LIST_LAST
    ret = testGmcPrepareStmtByLabelName(g_stmt_C2, T3, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_C2);
    EXPECT_EQ(GMERR_OK, ret);
    f0 = 5;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, &refKey);
    ret = GmcYangSetListLocator(g_stmt_C2, &listProp);
    EXPECT_EQ(GMERR_OK, ret);
    UninitListProperty(&listProp);
    keyvalue = 10;
    ret = testYangSetField(
        g_stmt_C2, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchAddDML(batch, g_stmt_C2);

    /* ****************List2进行非位置移动的DMl操作，子节点list1进行位置移动的DML操作******************* */
    normalDML(batch, g_stmt_root, g_stmt_C2, T3, 2);
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点位置移动选择GMC_LIST_before
    ret = testGmcPrepareStmtByLabelName(g_stmt_C1, T2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_C1);
    EXPECT_EQ(GMERR_OK, ret);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, &refKey);
    ret = GmcYangSetListLocator(g_stmt_C1, &listProp);
    EXPECT_EQ(GMERR_OK, ret);
    UninitListProperty(&listProp);
    keyvalue = 10;
    ret = testYangSetField(
        g_stmt_C1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchAddDML(batch, g_stmt_C1);

    // 异步执行接收批操作信息
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(9, data.totalNum);
    ASSERT_EQ(9, data.succNum);

    GmcBatchDestroy(batch);

    // 事务提交
    testTransCommitAsync(g_conn_async);

    GmcFreeStmt(g_stmt_C1);
    GmcFreeStmt(g_stmt_C2);
}

void positionInsertT3FiveNormalData1(GmcBatchT *batch, GmcStmtT *g_stmt_root, GmcStmtT *g_stmt_list, uint32_t begin,
    uint32_t end, GmcYangListPositionE position, uint32_t keyvalue = 1)
{
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;
    uint32_t f0;
    int ret;

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = begin; i <= end; ++i) {
        // 设置child节点位置移动选择GMC_LIST_before
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, T3, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        EXPECT_EQ(GMERR_OK, ret);
        f0 = 1;
        InitRefKeys(&refKey, 1, &f0);
        InitListProperty(&listProp, position, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        EXPECT_EQ(GMERR_OK, ret);
        UninitListProperty(&listProp);
        keyvalue = i;
        uint32_t pid = 1;
        testSetKeyNameAndValue(g_stmt_list, keyvalue, pid);
        testYangSetVertexProperty(g_stmt_list, keyvalue, GMC_YANG_PROPERTY_OPERATION_MERGE);
        GmcBatchAddDML(batch, g_stmt_list);
    }
}

void normalReplaceT3Dml(GmcBatchT *batch, GmcStmtT *g_stmt_root, GmcStmtT *g_stmt_list, uint32_t begin = 1,
    uint32_t end = 5, uint32_t keyvalue = 1)
{
    // 设置根节点
    int ret;
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -------------------------插入5条新数据-----------------------------------
    for (uint32_t i = begin; i <= end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, T3, GMC_OPERATION_REPLACE_GRAPH);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        EXPECT_EQ(GMERR_OK, ret);
        keyvalue = i;
        // 设置属性值
        testYangSetVertexProperty_PK(g_stmt_list, keyvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        uint32_t newvalue = 200;
        testYangSetVertexProperty(g_stmt_list, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void normalMergeT3Dml(GmcBatchT *batch, GmcStmtT *g_stmt_root, GmcStmtT *g_stmt_list, uint32_t begin = 1,
    uint32_t end = 10, uint32_t keyvalue = 1)
{
    // 设置根节点
    // 设置根节点
    int ret;
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // -------------------------更新数据-----------------------------------
    for (uint32_t i = begin; i <= end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, T3, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        EXPECT_EQ(GMERR_OK, ret);
        keyvalue = i;
        // 设置属性值
        uint32_t pid = 1;
        testSetKeyNameAndValue(g_stmt_list, keyvalue, pid);
        testYangSetVertexProperty(g_stmt_list, keyvalue, GMC_YANG_PROPERTY_OPERATION_MERGE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

GmcConnT *g_conn2 = NULL;
void *doubleListThread2NormalDML(void *args)
{
    GmcStmtT *g_stmt2 = NULL;
    AsyncUserDataT data{0};
    int ret;
    uint32_t keyvalue;
    GmcStmtT *g_stmt_async2 = NULL;
    GmcConnT *g_conn_async2 = NULL;
    GmcStmtT *g_stmt_root2 = NULL;
    GmcStmtT *g_stmt_list2 = NULL;
    ret = testGmcConnect(&g_conn2, &g_stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async2, &g_stmt_async2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(g_stmt_async2, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcAllocStmt(g_conn_async2, &g_stmt_root2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async2, &g_stmt_list2);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async2, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async2, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root2, T0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 31; i <= 100; ++i) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2, T3, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2, g_stmt_list2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testYangSetField(
            g_stmt_list2, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchAddDML(batch, g_stmt_list2);
    }

    uint32_t begin = 61;
    uint32_t end = 100;
    uint32_t pid;
    for (uint32_t i = begin; i <= end; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2, T3, GMC_OPERATION_REMOVE_GRAPH);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2, g_stmt_list2);
        EXPECT_EQ(GMERR_OK, ret);
        keyvalue = i;
        pid = 1;
        testSetKeyNameAndValue(g_stmt_list2, keyvalue, pid);

        GmcBatchAddDML(batch, g_stmt_list2);
    }

    begin = 31;
    end = 50;
    positionInsertT3FiveNormalData1(batch, g_stmt_root2, g_stmt_list2, begin, end, GMC_YANG_LIST_POSITION_REMAIN, 2);
    for (uint32_t i = begin; i <= end; ++i) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list2, T3, GMC_OPERATION_DELETE_GRAPH);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root2, g_stmt_list2);
        EXPECT_EQ(GMERR_OK, ret);
        keyvalue = i;
        pid = 1;
        testSetKeyNameAndValue(g_stmt_list2, keyvalue, pid);

        GmcBatchAddDML(batch, g_stmt_list2);
    }

    begin = 26;
    end = 30;
    positionInsertT3FiveNormalData1(batch, g_stmt_root2, g_stmt_list2, begin, end, GMC_YANG_LIST_POSITION_AFTER, 2);
    normalReplaceT3Dml(batch, g_stmt_root2, g_stmt_list2, begin, end);

    begin = 21;
    end = 25;
    positionInsertT3FiveNormalData1(batch, g_stmt_root2, g_stmt_list2, begin, end, GMC_YANG_LIST_POSITION_LAST, 2);
    normalMergeT3Dml(batch, g_stmt_root2, g_stmt_list2, begin, end);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(176, data.totalNum);
    EXPECT_EQ(176, data.succNum);

    GmcBatchDestroy(batch);

    // 事务提交
    testTransCommitAsync(g_conn_async2);
    AW_FUN_Log(LOG_STEP, "T3校验结果");

    ret = testGmcDisconnect(g_conn2, g_stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "T3断链");
    uint32_t i = 0;
    GmcFreeStmt(g_stmt_root2);
    AW_FUN_Log(LOG_STEP, "GmcFreeStmt");
    GmcFreeStmt(g_stmt_list2);
    AW_FUN_Log(LOG_STEP, "GmcFreeStmt g_stmt_root2");
    g_stmt_list2 = NULL;
    ret = testGmcDisconnect(g_conn_async2, g_stmt_async2);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "T3断链");
    g_stmt_root2 = NULL;
    AW_FUN_Log(LOG_STEP, "T3线程结束");
}

/* Yang模型示例
                          root(con)
                             |
   ┌-------------------------┼-------------------------┐
   |                                                   |
list1                                              list_2
*/

/* ****************************************************************************
 Description  :   056 多个线程对同一个父亲的2个list顶点进行操作，
                  线程一对list1进行纯数据的操作，后进行list节点的位置操作。
                  线程二对list2进行位置移动DMl操作后，再进行纯数据的操作。
                  DTS2022080100392

 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_056)
{
    int ret;
    GmcBatchT *batch = NULL;
    // 创建拥有两个list子节点的yang模型
    char *vlabelSchema = NULL;
    char *vlabelSchema1 = NULL;
    uint32_t keyvalue;

    ret = GmcAllocStmt(g_conn_async, &g_stmt_C1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_C2);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "创建俩个list子表");

    readJanssonFile("schema_file/Con_double_List.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateVertexLabel(g_stmt, vlabelSchema, g_msConfigTrans);
    ASSERT_EQ(GMERR_OK, ret);

    readJanssonFile("schema_file/Con_double_List_Edge.gmjson", &vlabelSchema1);
    ASSERT_NE((void *)NULL, vlabelSchema1);
    ret = GmcCreateEdgeLabel(g_stmt, vlabelSchema1, g_msConfigTrans);
    ASSERT_EQ(GMERR_OK, ret);

    free(vlabelSchema);

    free(vlabelSchema1);

    // 初始数据
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);
    InsertBasicListData(g_conn_async, g_stmt_root, g_stmt_C1, g_conn, g_stmt);
    InsertBasicList2Data(g_conn_async, g_stmt_root, g_stmt_C2, g_conn, g_stmt);
    testTransCommitAsync(g_conn_async);

    sleep(1);
    // 创建多个线程
    int32_t thread_num = 2;
    int num[thread_num];
    for (int i = 0; i < thread_num; i++) {
        num[i] = i;
    }
    // 定义线程的 id 变量，多个变量使用数组
    pthread_t client_thr[thread_num];

    // 参数依次是：创建的线程id，线程参数，调用的函数，传入的函数参数
    ret = pthread_create(&client_thr[0], NULL, doubleListThread2NormalDML, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = pthread_create(&client_thr[1], NULL, Thread2NormalDml, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = pthread_join(client_thr[0], NULL);
    ASSERT_EQ(GMERR_OK, ret);

    ret = pthread_join(client_thr[1], NULL);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "删表");

    GmcFreeStmt(g_stmt_C1);
    GmcFreeStmt(g_stmt_C2);
}

void listPositonChoice(GmcStmtT *t1Stmt, GmcYangListPositionE expPosition, uint32_t expValue, bool pkIsEight = false)
{
    GmcYangListLocatorT listLocator;
    // 用于设置参考主键
    GmcPropValueT refKey[7];
    InitRefKeys(&refKey[0], 1, &expValue);
    InitRefKeys(&refKey[1], 2, &expValue);
    if (pkIsEight) {
        // 用于接收全部参考主键
        GmcPropValueT *refKeys[7];
        InitRefKeys(&refKey[2], 3, &expValue);
        InitRefKeys(&refKey[3], 4, &expValue);
        InitRefKeys(&refKey[4], 5, &expValue);
        InitRefKeys(&refKey[5], 6, &expValue);
        InitRefKeys(&refKey[6], 7, &expValue);
        refKeys[2] = &refKey[2];
        refKeys[3] = &refKey[3];
        refKeys[4] = &refKey[4];
        refKeys[5] = &refKey[5];
        refKeys[6] = &refKey[6];
        refKeys[0] = &refKey[0];
        refKeys[1] = &refKey[1];
        listLocator.refKeyFieldsCount = 7;
        listLocator.refKeyFields = refKeys;
        listLocator.position = expPosition;
        GmcYangSetListLocator(t1Stmt, &listLocator);
    } else {
        // 用于接收全部参考主键
        GmcPropValueT *refKeys[2];
        refKeys[0] = &refKey[0];
        refKeys[1] = &refKey[1];
        listLocator.refKeyFieldsCount = 2;
        listLocator.refKeyFields = refKeys;
        listLocator.position = expPosition;
        GmcYangSetListLocator(t1Stmt, &listLocator);
    }
}

void allListPositonChoice(GmcStmtT *t1Stmt, GmcYangListPositionE expPosition, uint32_t expValue, bool pkIsEight = false)
{
    GmcYangListLocatorT listLocator;
    int64_t f0 = expValue;
    GmcPropValueT refKey[2];
    InitRefKeys(&refKey[0], 1, &f0, GMC_DATATYPE_INT64, sizeof(f0));
    uint64_t f1 = expValue;
    InitRefKeys(&refKey[1], 2, &f1, GMC_DATATYPE_UINT64, sizeof(f1));
    GmcPropValueT *refKeys[2];
    refKeys[0] = &refKey[0];
    refKeys[1] = &refKey[1];
    listLocator.refKeyFields = refKeys;
    listLocator.refKeyFieldsCount = 2;

    listLocator.position = expPosition;
    GmcYangSetListLocator(t1Stmt, &listLocator);
}
// list主键为两个字段
void testCreatePkIsTwoLabel(GmcStmtT *stmt)
{
    int ret = 0;
    char *vlabelSchema = NULL;
    char *vlabelSchema1 = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/Con_ListPkIsTwo.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_ListPkIsTwo_Edge.gmjson", &vlabelSchema1);
    ASSERT_NE((void *)NULL, vlabelSchema1);
    ret = GmcCreateEdgeLabelAsync(stmt, vlabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);
    free(vlabelSchema1);
}

// list主键为八个字段
void testCreatePkIsEightLabel(GmcStmtT *stmt)
{
    int ret = 0;
    char *vlabelSchema = NULL;
    char *vlabelSchema1 = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/Con_ListPkIsEight.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateVertexLabelAsync(stmt, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_ListPkIsEight_Edge.gmjson", &vlabelSchema1);
    ASSERT_NE((void *)NULL, vlabelSchema1);
    ret = GmcCreateEdgeLabelAsync(stmt, vlabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);
    free(vlabelSchema1);
}

// list主键为两个字段(八个字段也行)
void testDropPkIsTwoLabel(GmcStmtT *stmt)
{
    int ret = 0;
    AsyncUserDataT data{0};
    sleep(1);
}

/* ****************************************************************************
 Description  :   061 list节点主键为两个字段时节点支持last位置移动insert操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_061)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    /* ***********************进行位置移动DML操作************************** */

    // 向根节点插入数据
    AW_FUN_Log(LOG_STEP, "4.向根节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    PK_value = 1;
    ret = testYangSetField(
        g_stmt_async, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    // list节点插入数据
    AW_FUN_Log(LOG_STEP, "5.list节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "6.list位置last插入移动");
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_LAST, 1);
    GmcBatchAddDML(batch, g_stmt_root);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 校验数据
    f0 = 2;
    uint32_t numArray[] = {1};
    AW_FUN_Log(LOG_STEP, "8.校验数据");

    // 事务提交
    AW_FUN_Log(LOG_STEP, "9.事务提交");
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_061.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   062 list节点主键为两个字段时节点支持first位置移动insert操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_062)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    /* ***********************进行位置移动DML操作************************** */

    // 向根节点插入数据
    AW_FUN_Log(LOG_STEP, "4.向根节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    PK_value = 1;
    ret = testYangSetField(
        g_stmt_async, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    // list节点插入数据
    AW_FUN_Log(LOG_STEP, "5.list节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "6.list位置first插入移动");
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_FIRST, 1);
    GmcBatchAddDML(batch, g_stmt_root);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 校验数据
    f0 = 2;
    uint32_t numArray[] = {1};
    AW_FUN_Log(LOG_STEP, "8.校验数据");

    // 事务提交
    AW_FUN_Log(LOG_STEP, "9.事务提交");
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_062.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

void initDataForBeforeAndAfter(GmcBatchT *batch, GmcStmtT *g_stmt_async, uint32_t PK_value = 1)
{
    AsyncUserDataT data = {0};
    GmcStmtT *stmt_root1 = NULL;
    ret = GmcAllocStmt(g_conn_async, &stmt_root1);
    EXPECT_EQ(GMERR_OK, ret);
    // 向根节点插入数据
    AW_FUN_Log(LOG_STEP, "根节点插入数据");
    int ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    PK_value = 1;
    ret = testYangSetField(
        g_stmt_async, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    // list节点插入数据
    AW_FUN_Log(LOG_STEP, "list节点插入数据");
    ret = testGmcPrepareStmtByLabelName(stmt_root1, T2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, stmt_root1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        stmt_root1, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        stmt_root1, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchAddDML(batch, stmt_root1);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.succNum);

    GmcFreeStmt(stmt_root1);
}

/* ****************************************************************************
 Description  :   063 list节点主键为两个字段时节点支持before位置移动insert操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_063)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行位置移动DML操作************************** */

    // 向根节点插入数据
    AW_FUN_Log(LOG_STEP, "5.向根节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    // list节点插入数据
    AW_FUN_Log(LOG_STEP, "6.list节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    PK_value = 2;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "7.list位置before插入移动");
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_BEFORE, 1);
    GmcBatchAddDML(batch, g_stmt_root);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "8.数据插入成功");

    GmcBatchDestroy(batch);

    // 校验数据
    uint32_t numArray[] = {2, 1};
    AW_FUN_Log(LOG_STEP, "9.校验数据");

    // 事务提交
    AW_FUN_Log(LOG_STEP, "10.事务提交");
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_063.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "11.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   064 list节点主键为两个字段时节点支持after位置移动insert操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_064)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行位置移动DML操作************************** */

    // 向根节点插入数据
    AW_FUN_Log(LOG_STEP, "5.向根节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    // list节点插入数据
    AW_FUN_Log(LOG_STEP, "6.list节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    PK_value = 2;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "7.list位置after插入移动");
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_AFTER, 1);
    GmcBatchAddDML(batch, g_stmt_root);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "8.数据插入成功");

    GmcBatchDestroy(batch);

    // 事务提交
    AW_FUN_Log(LOG_STEP, "9.事务提交");
    testTransCommitAsync(g_conn_async);

     // 校验数据
    uint32_t numArray[] = {1, 2};
    AW_FUN_Log(LOG_STEP, "10.校验数据");
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_064.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "11.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   065 list节点主键为两个字段时节点支持stay位置移动insert操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_065)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行last位置移动DML操作************************** */

    // 向根节点插入数据
    AW_FUN_Log(LOG_STEP, "5.向根节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    // list节点插入数据
    AW_FUN_Log(LOG_STEP, "6.list节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    PK_value = 2;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "7.list位置stay插入移动");
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_REMAIN, 1);
    GmcBatchAddDML(batch, g_stmt_root);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "8.数据插入成功");

    GmcBatchDestroy(batch);
    // 事务提交
    AW_FUN_Log(LOG_STEP, "9.事务提交");
    testTransCommitAsync(g_conn_async);

    // 校验数据
    uint32_t numArray[] = {1, 2};
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_065.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);
    AW_FUN_Log(LOG_STEP, "10.校验数据");

    AW_FUN_Log(LOG_STEP, "11.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   066 list节点主键为两个字段时节点支持last位置移动replace操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_066)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行last位置移动DML操作************************** */

    // replace 插入新数据，后更新原数据
    AW_FUN_Log(LOG_STEP, "5.replace 插入新数据200");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    uint32_t newvalue = 200;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_LAST, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    AW_FUN_Log(LOG_STEP, "6.replace 更新数据200");
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_LAST, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);
    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

    // 校验数据
    uint32_t numArray[] = {1, 200};
    AW_FUN_Log(LOG_STEP, "9.校验数据");

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_066.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   067 list节点主键为两个字段时节点支持first位置移动replace操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_067)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行last位置移动DML操作************************** */

    // replace 插入新数据，后更新原数据
    AW_FUN_Log(LOG_STEP, "5.replace 插入新数据200");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    uint32_t newvalue = 200;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_FIRST, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    AW_FUN_Log(LOG_STEP, "6.replace 更新数据200");
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_FIRST, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

     // 校验数据
    uint32_t numArray[] = {200, 1};
    AW_FUN_Log(LOG_STEP, "9.校验数据");

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_067.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   068 list节点主键为两个字段时节点支持before位置移动replace操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_068)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行last位置移动DML操作************************** */

    // replace 插入新数据，后更新原数据
    AW_FUN_Log(LOG_STEP, "5.replace 插入新数据200");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    uint32_t newvalue = 200;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_BEFORE, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    AW_FUN_Log(LOG_STEP, "6.replace 更新数据200");
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_BEFORE, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

    // 校验数据
    uint32_t numArray[] = {200, 1};
    AW_FUN_Log(LOG_STEP, "9.校验数据");

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_068.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   069 list节点主键为两个字段时节点支持after位置移动replace操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_069)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行last位置移动DML操作************************** */

    // replace 插入新数据，后更新原数据
    AW_FUN_Log(LOG_STEP, "5.replace 插入新数据200");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    uint32_t newvalue = 200;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_AFTER, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    AW_FUN_Log(LOG_STEP, "6.replace 更新数据200");
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_AFTER, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

    // 校验数据
    uint32_t numArray[] = {1, 200};
    AW_FUN_Log(LOG_STEP, "9.校验数据");

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_069.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   070 list节点主键为两个字段时节点支持stay位置移动replace操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_070)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行位置移动DML操作************************** */

    // replace 插入新数据，后更新原数据
    AW_FUN_Log(LOG_STEP, "5.replace 插入新数据200");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    uint32_t newvalue = 200;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_REMAIN, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    AW_FUN_Log(LOG_STEP, "6.replace 更新数据200");
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_REMAIN, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);
    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

    // 校验数据
    uint32_t numArray[] = {1, 200};
    AW_FUN_Log(LOG_STEP, "9.校验数据");

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_070.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   071 list节点主键为两个字段时节点支持last位置移动merge操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_071)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行last位置移动DML操作************************** */

    // merge 插入新数据后更新原数据
    AW_FUN_Log(LOG_STEP, "5.merge 插入新数据200");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t newvalue = 200;
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_LAST, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    newvalue = 200;
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_LAST, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 校验数据
    uint32_t numArray[] = {1, 200};

    AW_FUN_Log(LOG_STEP, "8.校验数据");

    // 事务提交
    AW_FUN_Log(LOG_STEP, "9.事务提交");
    testTransCommitAsync(g_conn_async);

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_071.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   072 list节点主键为两个字段时节点支持first位置移动merge操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_072)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行last位置移动DML操作************************** */

    // merge 插入新数据后更新原数据
    AW_FUN_Log(LOG_STEP, "5.merge 插入新数据200");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t newvalue = 200;
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_FIRST, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    newvalue = 200;
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_FIRST, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

    // 校验数据
    uint32_t numArray[] = {200, 1};
    AW_FUN_Log(LOG_STEP, "9.校验数据");

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_072.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   073 list节点主键为两个字段时节点支持before位置移动merge操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_073)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行last位置移动DML操作************************** */

    // merge 插入新数据后更新原数据
    AW_FUN_Log(LOG_STEP, "5.merge 插入新数据200");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t newvalue = 200;
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_BEFORE, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    newvalue = 200;
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_BEFORE, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

     // 校验数据
    uint32_t numArray[] = {200, 1};
    AW_FUN_Log(LOG_STEP, "9.校验数据");

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_073.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   074 list节点主键为两个字段时节点支持after位置移动merge操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_074)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行last位置移动DML操作************************** */

    // merge 插入新数据后更新原数据
    AW_FUN_Log(LOG_STEP, "5.merge 插入新数据200");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t newvalue = 200;
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_AFTER, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    newvalue = 200;
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_AFTER, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

    // 校验数据
    uint32_t numArray[] = {1, 200};
    AW_FUN_Log(LOG_STEP, "9.校验数据");

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_074.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

/* ****************************************************************************
 Description  :   075 list节点主键为两个字段时节点支持stay位置移动merge操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_075)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsTwoLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入数据1
    AW_FUN_Log(LOG_STEP, "4.插入数据1");
    initDataForBeforeAndAfter(batch, g_stmt_async);
    /* ***********************进行last位置移动DML操作************************** */

    // merge 插入新数据后更新原数据
    AW_FUN_Log(LOG_STEP, "5.merge 插入新数据200");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t newvalue = 200;
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_REMAIN, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    newvalue = 200;
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_REMAIN, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

     // 校验数据
    uint32_t numArray[] = {1, 200};
    AW_FUN_Log(LOG_STEP, "9.校验数据");

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_075.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

void pkIsEightPositionInsert(
    GmcBatchT *batch, GmcYangListPositionE expPosition, uint32_t PK_value = 2, uint32_t expValue = 1)
{
    // list节点插入数据
    AW_FUN_Log(LOG_STEP, "list节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F6", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F8", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "list位置插入移动");
    listPositonChoice(g_stmt_root, expPosition, expValue, true);
    GmcBatchAddDML(batch, g_stmt_root);
}

/* ****************************************************************************
 Description  :   076 list节点主键为八个字段时节点支持位置移动insert操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_076)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsEightLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    /* ***********************进行位置移动DML操作************************** */

    // 向根节点插入数据
    AW_FUN_Log(LOG_STEP, "4.向根节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    PK_value = 1;
    ret = testYangSetField(
        g_stmt_async, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    // list节点插入数据 默认插入为2
    pkIsEightPositionInsert(batch, GMC_YANG_LIST_POSITION_LAST);
    pkIsEightPositionInsert(batch, GMC_YANG_LIST_POSITION_FIRST, 3, 2);
    pkIsEightPositionInsert(batch, GMC_YANG_LIST_POSITION_BEFORE, 4, 3);
    pkIsEightPositionInsert(batch, GMC_YANG_LIST_POSITION_AFTER, 5, 4);
    pkIsEightPositionInsert(batch, GMC_YANG_LIST_POSITION_REMAIN, 6, 5);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(6, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 校验数据
    f0 = 1;
    uint32_t numArray[] = {4, 5, 3, 1, 2, 6};
    AW_FUN_Log(LOG_STEP, "8.校验数据");

    // 事务提交
    AW_FUN_Log(LOG_STEP, "9.事务提交");
    testTransCommitAsync(g_conn_async);

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_076.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

void pkIsEightPositionReplace(
    GmcBatchT *batch, GmcYangListPositionE expPosition, uint32_t oldValue, uint32_t positionValue = 1)
{
    // replace 插入新数据，后更新原数据
    AW_FUN_Log(LOG_STEP, "replace 插入新数据200");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_NONE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    uint32_t newvalue = 200;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F3", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F4", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F5", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F6", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F7", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F8", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_REMAIN, 1);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    AW_FUN_Log(LOG_STEP, "replace 更新数据%d", oldValue);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &oldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &oldValue, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &oldValue, sizeof(uint32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &oldValue, sizeof(uint32_t), "F3", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &oldValue, sizeof(uint32_t), "F4", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &oldValue, sizeof(uint32_t), "F5", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &oldValue, sizeof(uint32_t), "F6", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &oldValue, sizeof(uint32_t), "F7", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &oldValue, sizeof(uint32_t), "F8", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, expPosition, positionValue, true);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  :   077 list节点主键为八个字段时节点支持位置移动replace操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_077)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsEightLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    /* ***********************进行位置移动DML操作************************** */

    // 向根节点插入数据
    AW_FUN_Log(LOG_STEP, "4.向根节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    PK_value = 1;
    ret = testYangSetField(
        g_stmt_async, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);
    // 位置移动replace操作
    pkIsEightPositionReplace(batch, GMC_YANG_LIST_POSITION_LAST, 100);
    pkIsEightPositionReplace(batch, GMC_YANG_LIST_POSITION_FIRST, 100, 200);
    pkIsEightPositionReplace(batch, GMC_YANG_LIST_POSITION_BEFORE, 500, 100);
    pkIsEightPositionReplace(batch, GMC_YANG_LIST_POSITION_AFTER, 500, 100);
    pkIsEightPositionReplace(batch, GMC_YANG_LIST_POSITION_REMAIN, 100, 500);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(16, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据replace移动成功");

    GmcBatchDestroy(batch);

    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

    // 校验数据
    f0 = 1;
    uint32_t numArray[] = {2, 1};
    AW_FUN_Log(LOG_STEP, "9.校验数据");

    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_077.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

void pkIsEightPositionMerge(
    GmcBatchT *batch, GmcYangListPositionE expPosition, uint32_t newvalue, uint32_t positionValue = 1)
{
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 3, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 4, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 5, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 6, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 7, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F7", GMC_YANG_PROPERTY_OPERATION_MERGE);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &newvalue, sizeof(uint32_t), "F8", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    listPositonChoice(g_stmt_root, expPosition, positionValue, true);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  :   078 list节点主键为八个字段时节点支持位置移动merge操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_078)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsEightLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    /* ***********************进行位置移动DML操作************************** */

    // 向根节点插入数据
    AW_FUN_Log(LOG_STEP, "4.向根节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    PK_value = 1;
    ret = testYangSetField(
        g_stmt_async, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    // 位置移动
    pkIsEightPositionMerge(batch, GMC_YANG_LIST_POSITION_LAST, 9);
    pkIsEightPositionMerge(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    pkIsEightPositionMerge(batch, GMC_YANG_LIST_POSITION_BEFORE, 10, 9);
    pkIsEightPositionMerge(batch, GMC_YANG_LIST_POSITION_AFTER, 11, 9);
    pkIsEightPositionMerge(batch, GMC_YANG_LIST_POSITION_REMAIN, 20);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(6, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 校验数据
    f0 = 2;
    uint32_t numArray[] = {2, 1};
    AW_FUN_Log(LOG_STEP, "8.校验数据");

    // 事务提交
    AW_FUN_Log(LOG_STEP, "9.事务提交");
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_078.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

void pkIsEightPositionDelete(
    GmcBatchT *batch, GmcYangListPositionE expPosition, uint32_t keyvalue, uint32_t positionValue = 1)
{
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_DELETE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    // 设置key
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 3, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 4, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 5, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 6, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 7, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_root, g_keyname);
    EXPECT_EQ(GMERR_OK, ret);
    listPositonChoice(g_stmt_root, expPosition, positionValue, true);
    GmcBatchAddDML(batch, g_stmt_root);
}

/* ****************************************************************************
 Description  :   079 list节点主键为八个字段时节点支持位置移动delete操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_079)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsEightLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    /* ***********************进行位置移动DML操作************************** */

    // 向根节点插入数据
    AW_FUN_Log(LOG_STEP, "4.向根节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    PK_value = 1;
    ret = testYangSetField(
        g_stmt_async, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    // list节点插入数据
    AW_FUN_Log(LOG_STEP, "5.list节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 3, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 4, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 5, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 6, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 7, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F8", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchAddDML(batch, g_stmt_root);

    PK_value = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
  ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 3, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 4, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 5, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 6, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 7, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F8", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "6.list位置last插入移动");
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_AFTER, 1, true);
    GmcBatchAddDML(batch, g_stmt_root);
    pkIsEightPositionMerge(batch, GMC_YANG_LIST_POSITION_REMAIN, 20);
    pkIsEightPositionMerge(batch, GMC_YANG_LIST_POSITION_BEFORE, 9, 20);
    pkIsEightPositionMerge(batch, GMC_YANG_LIST_POSITION_AFTER, 10, 9);

    // 位置删除
    pkIsEightPositionDelete(batch, GMC_YANG_LIST_POSITION_AFTER, 1, 1);
    pkIsEightPositionDelete(batch, GMC_YANG_LIST_POSITION_BEFORE, 2, 1);
    pkIsEightPositionDelete(batch, GMC_YANG_LIST_POSITION_REMAIN, 9, 1);
    pkIsEightPositionDelete(batch, GMC_YANG_LIST_POSITION_LAST, 20, 1);
    pkIsEightPositionDelete(batch, GMC_YANG_LIST_POSITION_FIRST, 10, 1);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(11, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);
    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

    // 校验数据
    f0 = 2;
    uint32_t numArray[] = {1, 2};
    AW_FUN_Log(LOG_STEP, "9.校验数据");
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_079.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

void pkIsEightPositionRemove(
    GmcBatchT *batch, GmcYangListPositionE expPosition, uint32_t keyvalue, uint32_t positionValue = 1)
{
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_REMOVE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    // 设置key
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 3, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 4, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 5, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 6, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 7, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_root, g_keyname);
    EXPECT_EQ(GMERR_OK, ret);
    listPositonChoice(g_stmt_root, expPosition, positionValue, true);
    GmcBatchAddDML(batch, g_stmt_root);
}

/* ****************************************************************************
 Description  :   080 list节点主键为八个字段时节点支持位置移动remove操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_080)
{
    uint32_t f0;
    uint32_t PK_value;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    AW_FUN_Log(LOG_STEP, "1.建表");
    testCreatePkIsEightLabel(g_stmt_async);

    // 启动事务
    AW_FUN_Log(LOG_STEP, "2.开启事务");
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 准备批处理batch参数
    AW_FUN_Log(LOG_STEP, "3.准备批处理batch参数");
    ret = testBatchPrepare(g_conn_async, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    /* ***********************进行位置移动DML操作************************** */

    // 向根节点插入数据
    AW_FUN_Log(LOG_STEP, "4.向根节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, T0, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    GmcYangSetRoot(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    PK_value = 1;
    ret = testYangSetField(
        g_stmt_async, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    ASSERT_EQ(GMERR_OK, ret);

    // list节点插入数据
    AW_FUN_Log(LOG_STEP, "5.list节点插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 3, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 4, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 5, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 6, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 7, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F8", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchAddDML(batch, g_stmt_root);

    PK_value = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, T2, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
  ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 3, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 4, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 5, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 6, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_root, 7, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "F8", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "6.list位置last插入移动");
    listPositonChoice(g_stmt_root, GMC_YANG_LIST_POSITION_AFTER, 1, true);
    GmcBatchAddDML(batch, g_stmt_root);
    pkIsEightPositionMerge(batch, GMC_YANG_LIST_POSITION_REMAIN, 20);
    pkIsEightPositionMerge(batch, GMC_YANG_LIST_POSITION_BEFORE, 9, 20);
    pkIsEightPositionMerge(batch, GMC_YANG_LIST_POSITION_AFTER, 10, 9);

    // 位置删除
    pkIsEightPositionRemove(batch, GMC_YANG_LIST_POSITION_AFTER, 1, 1);
    pkIsEightPositionRemove(batch, GMC_YANG_LIST_POSITION_BEFORE, 2, 1);
    pkIsEightPositionRemove(batch, GMC_YANG_LIST_POSITION_REMAIN, 9, 1);
    pkIsEightPositionRemove(batch, GMC_YANG_LIST_POSITION_LAST, 20, 1);
    pkIsEightPositionRemove(batch, GMC_YANG_LIST_POSITION_FIRST, 10, 1);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(11, data.succNum);
    AW_FUN_Log(LOG_STEP, "7.数据插入成功");

    GmcBatchDestroy(batch);

    // 事务提交
    AW_FUN_Log(LOG_STEP, "8.事务提交");
    testTransCommitAsync(g_conn_async);

    // 校验数据
    f0 = 2;
    uint32_t numArray[] = {1, 2};
    AW_FUN_Log(LOG_STEP, "9.校验数据");
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Yang_003_SetListLocator_080.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    CheckT2ListOrderResult(g_stmt,suntreeReturnJson);

    AW_FUN_Log(LOG_STEP, "10.删表");
    testDropPkIsTwoLabel(g_stmt_async);
}

struct ValidateParam {
    std::atomic_uint32_t *step;
    int32_t exceptStatus;  // 预期的操作状态
    bool validateRes;     // 预期返回的mandatory校验结果
};

static inline int testWaitValidateAsyncRecv(
    void *userData1, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true, int waitCnt = 0)
{
    ValidateParam *userData = (ValidateParam *)userData1;
    while (*(userData->step) != expRecvNum) {
        usleep(10);
        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            return -1;  // 接收超时
        }
    }
    return 0;
}

void AsyncValidateCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    ValidateParam *param = (ValidateParam *)userData;
    EXPECT_EQ(param->exceptStatus, status) << errMsg;
    if (GMERR_OK == status) {
        ASSERT_EQ(param->validateRes, validateRes.validateRes);
    }
    (*(param->step))++;
}

void mandatoryCheck(
    GmcStmtT *g_stmt_async, bool exceptRes, int32_t exceptStatus = GMERR_OK, uint32_t apiSupport = GMERR_OK)
{
#ifdef FEATURE_YANG_VALIDATION
    std::atomic_uint32_t step{0};
    ValidateParam param = {.step = &step, .exceptStatus = exceptStatus, .validateRes = exceptRes};

    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    int ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(apiSupport, ret);

    ret = testWaitValidateAsyncRecv(&param);
    AW_MACRO_ASSERT_EQ_INT(exceptStatus, param.exceptStatus);
#endif
}

/* ****************************************************************************
 Description  :   081 list节点位置移动后进行mandatory校验
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_081)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Mandatory_List.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Mandatory_List_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);
    // 模型校验
    ModelCheck(g_stmt_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数,mandatory依赖diff进行批操作需开启diff开关
    ret = testBatchPrepareOpenDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list节点位置移动插入数据
    // 进行DML操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "C0", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key
    uint32_t keyvalue = 100;
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "C0:L1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // // 设置key
    keyvalue = 101;
    uint32_t pid = 1;
    testSetKeyNameAndValue(g_stmt_root, keyvalue, 1);
    GmcYangListLocatorT listLocator;
    uint32_t num = 11;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &num);
    GmcPropValueT *refKeys[1];
    refKeys[0] = &refKey;
    listLocator.refKeyFieldsCount = 1;
    listLocator.refKeyFields = refKeys;
    listLocator.position = GMC_YANG_LIST_POSITION_LAST;
    GmcYangSetListLocator(g_stmt_root, &listLocator);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    mandatoryCheck(g_stmt_async, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);
}

void complexListPositionInsert(GmcBatchT *batch, GmcYangListPositionE expPosition, int pk, uint32_t expValue = 1)
{
    // list节点插入数据
    AW_FUN_Log(LOG_STEP, "list节点包含所有数据类型插入数据");
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "CC0:CC1", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_INT64, &value9, sizeof(int64_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t value10 = 1000 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_INT32, &value6, sizeof(int32_t), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value7 = pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t), "F3", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = 100 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_INT16, &value4, sizeof(int16_t), "F4", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = 1000 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t), "F5", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = 1 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_INT8, &value2, sizeof(int8_t), "F6", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = 10 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t), "F7", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_BOOL, &value8, sizeof(bool), "F8", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_FLOAT, &value11, sizeof(float), "F9", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_DOUBLE, &value12, sizeof(double), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_TIME, &value10, sizeof(uint64_t), "F11", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr0 = 'a';
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_CHAR, &teststr0, sizeof(char), "F12", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char), "F13", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr3[17] = "fixedfixedfixeda";
    EXPECT_EQ(17, strlen(teststr3) + 1);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)), "F14", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)), "F15", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_BYTES, teststr15, sizeof(uint32_t), "F16", GMC_YANG_PROPERTY_OPERATION_CREATE);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "list位置插入移动");
    allListPositonChoice(g_stmt_root, expPosition, expValue);
    ret = GmcBatchAddDML(batch, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  :   082 list节点数据类型包含所有支持类型，支持list节点last移动insert操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_082)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "CC0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // lis位置移动写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "CC0:CC1", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_LAST, 10);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   083 list节点数据类型包含所有支持类型，支持list节点first移动insert操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_083)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "CC0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // lis位置移动写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "CC0:CC1", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_FIRST, 10);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   084 list节点数据类型包含所有支持类型，支持list节点stay移动insert操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_084)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "CC0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // lis位置移动写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "CC0:CC1", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   085 list节点数据类型包含所有支持类型，支持list节点before移动insert操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_085)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "CC0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // lis位置移动写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "CC0:CC1", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_BEFORE, 11, 1010);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   086 list节点数据类型包含所有支持类型，支持list节点after移动insert操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_086)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "CC0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    // lis位置移动写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "CC0:CC1", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_AFTER, 11, 1010);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

void complexListPositionMerge(GmcBatchT *batch, GmcYangListPositionE expPosition, int pk, uint32_t expValue = 1)
{
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, CC1, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t newvalue = pk;
    uint64_t value10 = pk;
    ret = GmcSetIndexKeyValue(g_stmt_root, 1, GMC_DATATYPE_INT64, &newvalue, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_root, 2, GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    int32_t value6 = pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_INT32, &value6, sizeof(int32_t), "F2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value7 = pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t), "F3", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = 100 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_INT16, &value4, sizeof(int16_t), "F4", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = 1000 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t), "F5", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = 1 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_INT8, &value2, sizeof(int8_t), "F6", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = 10 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t), "F7", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_BOOL, &value8, sizeof(bool), "F8", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_FLOAT, &value11, sizeof(float), "F9", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_DOUBLE, &value12, sizeof(double), "F10", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_TIME, &value10, sizeof(uint64_t), "F11", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr0 = 'a';
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_CHAR, &teststr0, sizeof(char), "F12", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char), "F13", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr3[17] = "fixedfixedfixeda";
    EXPECT_EQ(17, strlen(teststr3) + 1);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)), "F14", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)), "F15", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_BYTES, teststr15, sizeof(uint32_t), "F16", GMC_YANG_PROPERTY_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    allListPositonChoice(g_stmt_root, expPosition, expValue);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/* ****************************************************************************
 Description  :   087 list节点数据类型包含所有支持类型，支持list节点last移动merge操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_087)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_LAST, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_LAST, 10);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   088 list节点数据类型包含所有支持类型，支持list节点first移动merge操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_088)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_FIRST, 10);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   089 list节点数据类型包含所有支持类型，支持list节点before移动merge操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_089)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_BEFORE, 11, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_BEFORE, 10, 11);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(4, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   090 list节点数据类型包含所有支持类型，支持list节点after移动merge操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_090)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_AFTER, 11, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_AFTER, 10, 11);

    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(4, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   091 list节点数据类型包含所有支持类型，支持list节点stay移动merge操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_091)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_REMAIN, 11, 10);
    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

void complexListPositionReplace(GmcBatchT *batch, GmcYangListPositionE expPosition, int pk, uint32_t expValue = 1)
{
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, CC1, GMC_OPERATION_REPLACE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_root);
    EXPECT_EQ(GMERR_OK, ret);
    // 设置属性值
    int64_t value9 = pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_INT64, &value9, sizeof(int64_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t value10 = pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t value6 = pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_INT32, &value6, sizeof(int32_t), "F2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value7 = pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t), "F3", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = 100 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_INT16, &value4, sizeof(int16_t), "F4", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = 1000 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t), "F5", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = 1 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_INT8, &value2, sizeof(int8_t), "F6", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = 10 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t), "F7", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_BOOL, &value8, sizeof(bool), "F8", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_FLOAT, &value11, sizeof(float), "F9", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_DOUBLE, &value12, sizeof(double), "F10", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_TIME, &value10, sizeof(uint64_t), "F11", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr0 = 'a';
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_CHAR, &teststr0, sizeof(char), "F12", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char), "F13", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr3[17] = "fixedfixedfixeda";
    EXPECT_EQ(17, strlen(teststr3) + 1);
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_FIXED, teststr3, (strlen(teststr3)), "F14", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)), "F15", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = testYangSetField(
        g_stmt_root, GMC_DATATYPE_BYTES, teststr15, sizeof(uint32_t), "F16", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    // 位置移动参数分别为当前操作句柄，位置移动方式，参照点
    allListPositonChoice(g_stmt_root, expPosition, expValue);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  :   092 list节点数据类型包含所有支持类型，支持list节点last移动replace操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_092)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_LAST, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_LAST, 10);
    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   093 list节点数据类型包含所有支持类型，支持list节点first移动replace操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_093)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(3, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   094 list节点数据类型包含所有支持类型，支持list节点before移动replace操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_094)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_BEFORE, 11, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_BEFORE, 11, 10);
    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(4, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   095 list节点数据类型包含所有支持类型，支持list节点after移动replace操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_095)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_AFTER, 11, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_AFTER, 11, 10);
    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(4, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   096 list节点数据类型包含所有支持类型，支持list节点stay移动replace操作
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_096)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_REMAIN, 11, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_REMAIN, 11, 10);
    // 异步执行接收批操作信息
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    ASSERT_EQ(4, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :   097 list节点交互list唯一性,位置移动last交互list唯一性
                位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_097)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_LAST, 10);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_LAST, 11, 10);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_LAST, 12, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(4, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   098 list节点交互list唯一性,位置移动first交互list唯一性
                位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_098)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_FIRST, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   099 list节点交互list唯一性,位置移动before交互list唯一性
                位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_099)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_BEFORE, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   100 list节点交互list唯一性,位置移动after交互list唯一性
                位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_100)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_AFTER, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   101 list节点交互list唯一性,位置移动stay交互list唯一性
                位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_101)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);
    complexListPositionInsert(batch, GMC_YANG_LIST_POSITION_REMAIN, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(3, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   102 list节点交互list唯一性,位置移动Last交互list唯一性
                merge位置移动插入一条新数据，插入成功，再插入这条数据插入失败   待和开发确认是否是问题  2022 8 23 21：20
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_102)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_LAST, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_LAST, 11, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_LAST, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(4, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   103 list节点交互list唯一性,位置移动first交互list唯一性
                merge位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_103)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcStmtT *g_stmt_C1 = NULL;
    GmcBatchT *batch = NULL;
    ret = GmcAllocStmt(g_conn_async, &g_stmt_C1);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_FIRST, 11, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_FIRST, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(4, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(g_stmt_C1);
}

/* ****************************************************************************
 Description  :   104 list节点交互list唯一性,位置移动before交互list唯一性
                merge位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_104)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_BEFORE, 11, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_BEFORE, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(4, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   105 list节点交互list唯一性,位置移动after交互list唯一性
                merge位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_105)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_AFTER, 11, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_AFTER, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(4, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   106 list节点交互list唯一性,位置移动stay交互list唯一性
                merge位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_106)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_REMAIN, 11, 10);
    complexListPositionMerge(batch, GMC_YANG_LIST_POSITION_REMAIN, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(4, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   107 list节点交互list唯一性,位置移动last交互list唯一性
                replace位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_107)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_LAST, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_LAST, 11, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_LAST, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(4, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   108 list节点交互list唯一性,位置移动first交互list唯一性
                replace位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_108)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_FIRST, 11, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_FIRST, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(4, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   109 list节点交互list唯一性,位置移动before交互list唯一性
                replace位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_109)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_BEFORE, 11, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_BEFORE, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(4, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   110 list节点交互list唯一性,位置移动after交互list唯一性
                replace位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_110)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_FIRST, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_AFTER, 11, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_AFTER, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(4, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :   111 list节点交互list唯一性,位置移动stay交互list唯一性
                replace位置移动插入一条新数据，插入成功，再插入这条数据插入失败
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangListNormalDmlByOrder_02, Yang_003_SetListLocator_111)
{
    int ret;
    char *vLabelSchema = NULL;
    char *vLabelSchema1 = NULL;
    GmcBatchT *batch = NULL;

    AW_FUN_Log(LOG_STEP, "建表");

    readJanssonFile("schema_file/Con_List_Complex_Unique.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);

    readJanssonFile("schema_file/Con_List_Complex_Unique_Edge.gmjson", &vLabelSchema1);
    ASSERT_NE((void *)NULL, vLabelSchema1);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vLabelSchema1, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    free(vLabelSchema);
    free(vLabelSchema1);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mstrx_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t keyvalue = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, CC0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 设置属性值
    testYangSetVertexProperty_PK(g_stmt_async, keyvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_REMAIN, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_REMAIN, 11, 10);
    complexListPositionReplace(batch, GMC_YANG_LIST_POSITION_REMAIN, 11, 10);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_UNIQUE_VIOLATION, data.status);
    ASSERT_EQ(4, data.totalNum);
    ASSERT_EQ(2, data.succNum);
    AW_FUN_Log(LOG_STEP, "list数据插入成功");

    // 事务回滚
    ret = GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}
