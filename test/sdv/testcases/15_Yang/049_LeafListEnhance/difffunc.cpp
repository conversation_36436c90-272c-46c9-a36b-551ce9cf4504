/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "TreeSubTree_common.h"
#include "t_datacom_lite.h"

class difffunc : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void difffunc::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void difffunc::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void difffunc::SetUp()
{
    int ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "Namespace49";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // alloc all stmt
    TestYangAllocAllstmt();
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt_sync, namespace1);
    EXPECT_EQ(GMERR_OK, ret);
    //  删边
    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //.建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);
    AW_CHECK_LOG_BEGIN();
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
}

void difffunc::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    const char *namespace1 = "Namespace49";
    AsyncUserDataT userData = {0};
    //  删边
    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 释放all stmt
    TestYangFreeAllstmt();

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
static vector<string> expectDiff35 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
};
static vector<string> expectDiff36 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1containerP:create\n"
"SubT1containerP.F0:create(1)\n"
"SubT1containerP.F1:create(1)\n"
"SubT1containerP.F2:create(NIL:8)\n"
"SubT1containerP.F3:create(1.000000)\n"
"SubT1containerP.F4:create(NIL:8)\n"
"SubT1containerP.F5:create(1.000000)\n"
"SubT1containerP.F6:create(string)\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:2)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
};
static vector<string> expectDiff37 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1containerP:create\n"
"SubT1containerP.F0:create(1)\n"
"SubT1containerP.F1:create(1)\n"
"SubT1containerP.F2:create(NIL:8)\n"
"SubT1containerP.F3:create(1.000000)\n"
"SubT1containerP.F4:create(NIL:8)\n"
"SubT1containerP.F5:create(1.000000)\n"
"SubT1containerP.F6:create(string)\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:2)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:3), preKey(PID:1,F0:2)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:4), preKey(PID:1,F0:3)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:5), preKey(PID:1,F0:4)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:6), preKey(PID:1,F0:5)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:7), preKey(PID:1,F0:6)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:8), preKey(PID:1,F0:7)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:9), preKey(PID:1,F0:8)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:10), preKey(PID:1,F0:9)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:11), preKey(PID:1,F0:10)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:11)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:12)),(NULL)]\n"
"SubT1containerP.leaf_SubT1containerP:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:13)),(NULL)]\n"
};
static vector<string> expectDiff38 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.SubT1containerP:remove\n"
"SubT1containerP.F0:remove(1)\n"
"SubT1containerP.F1:remove(1)\n"
"SubT1containerP.F2:remove(NIL:8)\n"
"SubT1containerP.F3:remove(1.000000)\n"
"SubT1containerP.F4:remove(NIL:8)\n"
"SubT1containerP.F5:remove(1.000000)\n"
"SubT1containerP.F6:remove(string)\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:2))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:6), preKey(PID:1,F0:5))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:7), preKey(PID:1,F0:6))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:8), preKey(PID:1,F0:7))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:9), preKey(PID:1,F0:8))]\n"
};
static vector<string> expectDiff39 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.SubT1containerP:remove\n"
"SubT1containerP.F0:remove(1)\n"
"SubT1containerP.F1:remove(1)\n"
"SubT1containerP.F2:remove(NIL:8)\n"
"SubT1containerP.F3:remove(1.000000)\n"
"SubT1containerP.F4:remove(NIL:8)\n"
"SubT1containerP.F5:remove(1.000000)\n"
"SubT1containerP.F6:remove(string)\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:2))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:3), preKey(PID:1,F0:2))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:4), preKey(PID:1,F0:3))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:6), preKey(PID:1,F0:5))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:7), preKey(PID:1,F0:6))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:8), preKey(PID:1,F0:7))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:9), preKey(PID:1,F0:8))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:10), preKey(PID:1,F0:9))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:11), preKey(PID:1,F0:10))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:12), preKey(PID:1,F0:11))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:13), preKey(PID:1,F0:12))]\n"
"SubT1containerP.leaf_SubT1containerP:remove[(NULL),(priKey(PID:1,F0:14), preKey(PID:1,F0:13))]\n"
};
static vector<string> expectDiff4001 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
};
static vector<string> expectDiff4002 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:remove(1)\n"
"SubT1container.F1:remove(1)\n"
"SubT1container.F2:remove(NIL:8)\n"
"SubT1container.F3:remove(1.000000)\n"
"SubT1container.F4:remove(NIL:8)\n"
"SubT1container.F5:remove(1.000000)\n"
"SubT1container.F6:remove(string)\n"
};
static vector<string> expectDiff41 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
};
static vector<string> expectDiff4201 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:9)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:12)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16), preKey(PID:1,F0:15)),(NULL)]\n"
};
static vector<string> expectDiff4202 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:remove(1)\n"
"SubT1container.F1:remove(1)\n"
"SubT1container.F2:remove(NIL:8)\n"
"SubT1container.F3:remove(1.000000)\n"
"SubT1container.F4:remove(NIL:8)\n"
"SubT1container.F5:remove(1.000000)\n"
"SubT1container.F6:remove(string)\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:12), preKey(PID:1,F0:9))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:13), preKey(PID:1,F0:12))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:14), preKey(PID:1,F0:13))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:15), preKey(PID:1,F0:14))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:16), preKey(PID:1,F0:15))]\n"
};
static vector<string> expectDiff4301 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:9)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:12)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16), preKey(PID:1,F0:15)),(NULL)]\n"
};
static vector<string> expectDiff4302 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:12), preKey(PID:1,F0:4)),(priKey(PID:1,F0:12), preKey(PID:1,F0:9))]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:5), preKey(PID:1,F0:16)),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
};
static vector<string> expectDiff44 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:9)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:12)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16), preKey(PID:1,F0:15)),(NULL)]\n"
};
static vector<string> expectDiff4501 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:9)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:12)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16), preKey(PID:1,F0:15)),(NULL)]\n"
};
static vector<string> expectDiff4502 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:8), preKey(PID:1,F0:12)),(priKey(PID:1,F0:8), preKey(PID:1,F0:7))]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:7), preKey(PID:1,F0:8)),(priKey(PID:1,F0:7), preKey(PID:1,F0:6))]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:6), preKey(PID:1,F0:7)),(priKey(PID:1,F0:6), preKey(PID:1,F0:5))]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:5), preKey(PID:1,F0:6)),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:13), preKey(PID:1,F0:5)),(priKey(PID:1,F0:13), preKey(PID:1,F0:12))]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:9), preKey(PID:1,F0:4)),(priKey(PID:1,F0:9), preKey(PID:1,F0:8))]\n"
};
static vector<string> expectDiff4601 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:9)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:12)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16), preKey(PID:1,F0:15)),(NULL)]\n"
};
static vector<string> expectDiff4602 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:20), preKey(PID:1,F0:16)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:21), preKey(PID:1,F0:20)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:22), preKey(PID:1,F0:21)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:23), preKey(PID:1,F0:22)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:24), preKey(PID:1,F0:23)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:25), preKey(PID:1,F0:24)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:26), preKey(PID:1,F0:25)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:27), preKey(PID:1,F0:26)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:28), preKey(PID:1,F0:27)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:29), preKey(PID:1,F0:28)),(NULL)]\n"
};
static vector<string> expectDiff4701 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:9)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:12)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16), preKey(PID:1,F0:15)),(NULL)]\n"
};
static vector<string> expectDiff4702 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:12), preKey(PID:1,F0:9))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:16), preKey(PID:1,F0:15))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:15), preKey(PID:1,F0:14))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:14), preKey(PID:1,F0:13))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:13), preKey(PID:1,F0:12))]\n"
};
static vector<string> expectDiff48 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:16)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:15)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:2), preKey(PID:1,F0:12)),(priKey(PID:1,F0:2))]\n"
};
static vector<string> expectDiff49 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:9)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:12)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16), preKey(PID:1,F0:15)),(NULL)]\n"
};
static vector<string> expectDiff50 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:4)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:12)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16), preKey(PID:1,F0:15)),(NULL)]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:5), preKey(PID:1,F0:16)),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
};
static vector<string> expectDiff51 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16), preKey(PID:1,F0:7)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:16)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:15)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:8), preKey(PID:1,F0:12)),(priKey(PID:1,F0:8), preKey(PID:1,F0:7))]\n"
};
static vector<string> expectDiff5201 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:9)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:12)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16), preKey(PID:1,F0:15)),(NULL)]\n"
};
static vector<string> expectDiff5202 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:8), preKey(PID:1,F0:12)),(priKey(PID:1,F0:8), preKey(PID:1,F0:7))]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:7), preKey(PID:1,F0:8)),(priKey(PID:1,F0:7), preKey(PID:1,F0:6))]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:6), preKey(PID:1,F0:7)),(priKey(PID:1,F0:6), preKey(PID:1,F0:5))]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:5), preKey(PID:1,F0:6)),(priKey(PID:1,F0:5), preKey(PID:1,F0:4))]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:13), preKey(PID:1,F0:5)),(priKey(PID:1,F0:13), preKey(PID:1,F0:12))]\n"
"SubT1container.leaf_SubT1container:update[(priKey(PID:1,F0:9), preKey(PID:1,F0:4)),(priKey(PID:1,F0:9), preKey(PID:1,F0:8))]\n"
};
static vector<string> expectDiff5301 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.F0:create(1)\n"
"SubT0Con.F1:create(1)\n"
"SubT0Con.F2:create(NIL:8)\n"
"SubT0Con.F3:create(1.000000)\n"
"SubT0Con.F4:create(NIL:8)\n"
"SubT0Con.F5:create(1.000000)\n"
"SubT0Con.F6:create(string)\n"
"SubT0Con.F8:create(98)\n"
"SubT0Con.F9:create(1)\n"
"SubT0Con.F10:create(1)\n"
"SubT0Con.F11:create(1)\n"
"SubT0Con.F12:create(1)\n"
"SubT0Con.F13:create(1)\n"
"SubT0Con.F14:create(1)\n"
"SubT0Con.F15:create(1)\n"
"SubT0Con.F16:create(1)\n"
"SubT0Con.F17:create(NIL:15)\n"
"SubT0Con.F18:create(NIL:16)\n"
"SubT0Con.F19:create(NIL:17)\n"
"SubT0Con.F20:create(NIL:21)\n"
"SubT0Con.F21:create(NIL:22)\n"
"SubT0Con.F22:create(NIL:23)\n"
"SubT0Con.F23:create(NIL:18)\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:create(1)\n"
"SubT1container.F1:create(1)\n"
"SubT1container.F2:create(NIL:8)\n"
"SubT1container.F3:create(1.000000)\n"
"SubT1container.F4:create(NIL:8)\n"
"SubT1container.F5:create(1.000000)\n"
"SubT1container.F6:create(string)\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:10), preKey(PID:1,F0:9)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:11), preKey(PID:1,F0:10)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:12), preKey(PID:1,F0:11)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:13), preKey(PID:1,F0:12)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:14), preKey(PID:1,F0:13)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:15), preKey(PID:1,F0:14)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:16), preKey(PID:1,F0:15)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:17), preKey(PID:1,F0:16)),(NULL)]\n"
"SubT1container.leaf_SubT1container:create[(priKey(PID:1,F0:18), preKey(PID:1,F0:17)),(NULL)]\n"
};
static vector<string> expectDiff5302 = {
"SubT0Con:update[(priKey(ID:1)),(priKey(ID:1))]\n"
"SubT0Con.SubT1container:update\n"
"SubT1container.F0:remove(1)\n"
"SubT1container.F1:remove(1)\n"
"SubT1container.F2:remove(NIL:8)\n"
"SubT1container.F3:remove(1.000000)\n"
"SubT1container.F4:remove(NIL:8)\n"
"SubT1container.F5:remove(1.000000)\n"
"SubT1container.F6:remove(string)\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:10), preKey(PID:1,F0:9))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:11), preKey(PID:1,F0:10))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:12), preKey(PID:1,F0:11))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:13), preKey(PID:1,F0:12))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:14), preKey(PID:1,F0:13))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:15), preKey(PID:1,F0:14))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:16), preKey(PID:1,F0:15))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:17), preKey(PID:1,F0:16))]\n"
"SubT1container.leaf_SubT1container:remove[(NULL),(priKey(PID:1,F0:18), preKey(PID:1,F0:17))]\n"
};
/*****************************************************************************
 * Description  : 35.父亲节点是P节点 且 P节点没有被创建实例   diff查询 为NONE
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由不可见到不可见 祖先就没实例  lieflist diff为none
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *SubT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff35, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
/*****************************************************************************
 * Description  : 36.最近节点为P节点  P节点被创建 leaflist 没有被创建  查询diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由不可见到可见 leaflist状态为creat 值为默认值
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *SubT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(T0Node, SubT1containerPNodeName, GMC_OPERATION_INSERT, &SubT1containerPNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_F0(SubT1containerPNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(SubT1containerPNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff36, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
/*****************************************************************************
 * Description  : 37.最近节点为P节点  P节点被创建 leaflist 被创建  查询diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由不可见 到可见 leaflist 状态为creat 值为set的值
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *SubT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(T0Node, SubT1containerPNodeName, GMC_OPERATION_INSERT, &SubT1containerPNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_F0(SubT1containerPNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(SubT1containerPNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 2; i < 15; i++) {
        uint32_t listf1 = i;
        GmcNodeT *leaf_SubT1containerPNode = NULL;
        const char *leaf_SubT1containerPName = "leaf_SubT1containerP";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerPName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerPNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyuint32_PK(leaf_SubT1containerPNode, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff37, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
/*****************************************************************************
 * Description  : 38.最近节点为P节点 且 P节点被删除   P节点下 没有leaflist实例  查询diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到不可见 leaflist 状态为remove 值为默认值
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *SubT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(T0Node, SubT1containerPNodeName, GMC_OPERATION_INSERT, &SubT1containerPNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_F0(SubT1containerPNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(SubT1containerPNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务
    TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    T0Node = NULL;
    SubT1containerPNode = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点属性
    ret = GmcYangEditChildNode(T0Node, SubT1containerPNodeName, GMC_OPERATION_DELETE_GRAPH, &SubT1containerPNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff38, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 39.最近节点为P节点 且 P节点被删除   P节点下 有leaflist实例  查询diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到不可见 leaflist 状态为remove 值为set的值
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *SubT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcYangEditChildNode(T0Node, SubT1containerPNodeName, GMC_OPERATION_INSERT, &SubT1containerPNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testYangSetVertexProperty_F0(SubT1containerPNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(SubT1containerPNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 9; i < 15; i++) {
        uint32_t listf1 = i;
        GmcNodeT *leaf_SubT1containerPNode = NULL;
        const char *leaf_SubT1containerPName = "leaf_SubT1containerP";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerPName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerPNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyuint32_PK(leaf_SubT1containerPNode, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务
    TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    T0Node = NULL;
    SubT1containerPNode = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点属性
    ret = GmcYangEditChildNode(T0Node, SubT1containerPNodeName, GMC_OPERATION_DELETE_GRAPH, &SubT1containerPNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff39, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 40.创建/删除  无子树leaflist实例的NP节点 查询diff （leaf list 的属性 就是默认值 无diff）
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到可见 NP节点没有创建leaflist实例之前之后diff无变化 
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4001, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务
    TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    T0Node = NULL;
    T1containerNode = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点属性
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_DELETE_GRAPH, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4002, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  :41.创建NP节点及子树 leaflist节点且leaflist的属性 为默认值 查询diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到可见 （因为 leaflist 本来就是可见 值且等于默认值 leaflist的diff为none）
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 2; i < 10; i++) {
        uint32_t listf1 = i;
        GmcNodeT *leaf_SubT1container = NULL;
        const char *leaf_SubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1container);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyuint32_PK(leaf_SubT1container, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff41, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 42.创建NP节点及子树 leaflist节点且leaflist的属性 为非默认值 查询diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到可见 （liaflist可见 但插入的数据非默认值 故有diff 且状态字段状态为creat 值为非默认值）
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 12; i < 17; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leaf_SubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4201, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务
    TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    T0Node = NULL;
    T1containerNode = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点属性
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_DELETE_GRAPH, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4202, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 43.创建NP节点及子树 leaflist节点且leaflist的属性 为非默认值 ，移动leaflist属性顺序    查询diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到可见 （leaflist 状态为update 将移动位置后的 字段 前驱变化表示出来）
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t i = 12; i < 17; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leaf_SubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4301, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务
    TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    T0Node = NULL;
    T1containerNode = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点属性
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t i = 12; i < 17; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leaf_SubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t f0 = 5;
            GmcPropValueT refKey ;
            InitRefKeys(&refKey, 1, &f0);
            GmcYangListLocatorT listProp;
            InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
            ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
            UninitListProperty(&listProp);
            testSetKeyNameAndValue(g_stmt_sync_T1List, listf1, 1, true);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4302, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 44. 执行 insert 六元语操作后 获取 diff 
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification :  由可见到可见 插入的非默认值的数据   状态为creat 值为非默认值  
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t i = 12; i < 17; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leaf_SubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff44, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 45. 执行 merge 六元语操作后   获取 diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到可见 将默认值移动到某非默认值后面  状态为update 被移动位置数据得前驱发生变化
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t i = 12; i < 17; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leaf_SubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4501, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务
    TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    T0Node = NULL;
    T1containerNode = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点属性
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t i = 5; i < 9; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leaf_SubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t f0 = 12;
            GmcPropValueT refKey ;
            InitRefKeys(&refKey, 1, &f0);
            GmcYangListLocatorT listProp;
            InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
            ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
            UninitListProperty(&listProp);
            testSetKeyNameAndValue(g_stmt_sync_T1List, listf1, 1, true);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4502, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 46. 执行 replace  六元语操作后   获取 diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到可见 repalce操作 插入新的非数据 状态为creat 数据为被插入的非默认值
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t i = 12; i < 17; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leaf_SubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4601, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务
    TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    T0Node = NULL;
    T1containerNode = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点属性
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t i = 20; i < 30; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leaf_SubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4602, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 47. 执行  delete 六元语操作后  获取 diff （insert操作 和delete操作 非默认值）
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见 到 不可见 状态为remove 字段为删除的非默认值字段
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t i = 12; i < 17; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leaf_SubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4701, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务
    TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    T0Node = NULL;
    T1containerNode = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点属性
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t i = 12; i < 17; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leaf_SubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_DELETE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    testSetKeyNameAndValue(g_stmt_sync_T1List, listf1, 1, true);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff4702, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 48.插入数据 在 list 最前   获取 diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到可见 状态为creat 值为默认值
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 12; i < 17; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leaf_SubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcYangListLocatorT listProp;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
        UninitListProperty(&listProp);
        testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff48, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
/*****************************************************************************
 * Description  : 49.插入数据 在 list最后 获取 diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到可见 状态为creat 值为默认值
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 12; i < 17; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leaf_SubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcYangListLocatorT listProp;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, NULL);
        ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
        UninitListProperty(&listProp);
        testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff49, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
/*****************************************************************************
 * Description  : 50.插入数据 在 list 指定位置之前    获取 diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到可见 状态为creat 值为默认值
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 12; i < 17; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leaf_SubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0 = 5;
        GmcPropValueT refKey ;
        InitRefKeys(&refKey, 1, &f0);
        GmcYangListLocatorT listProp;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
        ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
        UninitListProperty(&listProp);
        testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff50, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

}
/*****************************************************************************
 * Description  : 51.插入数据 在 list 指定位置之后    获取 diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到可见 状态为creat 值为默认值
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 12; i < 17; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leaf_SubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0 = 7;
        GmcPropValueT refKey ;
        InitRefKeys(&refKey, 1, &f0);
        GmcYangListLocatorT listProp;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
        ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
        UninitListProperty(&listProp);
        testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff51, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 52.移动已存在元素位置    获取 diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到可见 状态为creat 值为被移动的值
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t i = 12; i < 17; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leaf_SubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff5201, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务
    TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    T0Node = NULL;
    T1containerNode = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点属性
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t i = 5; i < 9; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leaf_SubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint32_t f0 = 12;
            GmcPropValueT refKey ;
            InitRefKeys(&refKey, 1, &f0);
            GmcYangListLocatorT listProp;
            InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
            ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
            UninitListProperty(&listProp);
            testSetKeyNameAndValue(g_stmt_sync_T1List, listf1, 1, true);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff5202, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 53. 执行  delete 删除包含默认值 六元语操作后  获取 diff
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 由可见到不可见 状态为remove 值为set的值
 * *****************************************************************************/
TEST_F(difffunc, Yang_049_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 2; i < 19; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leaf_SubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaf_SubT1containerName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff5301, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 开启事务
    TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    T0Node = NULL;
    T1containerNode = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBatchOptionSetDiffType(&batchOption, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点属性
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_DELETE_GRAPH, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    // 获取diff
    testFetchAndDeparseDiff(g_stmt_sync_T0, batch, expectDiff5302, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}



