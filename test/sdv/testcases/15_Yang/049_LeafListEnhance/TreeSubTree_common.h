
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef TREESUBTREE_COMMON_H
#define TREESUBTREE_COMMON_H
extern "C" {
}

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <string>
#include <atomic>
#include <vector>
#include <iostream>
#include <cmath>
#include <chrono>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"

#include "t_datacom_lite.h"
#include "jansson.h"

#define TIMEZONEMAXCMD 512

GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL;
char *g_vertexschema = NULL, *g_edgeschema = NULL;
char *g_vertexschema1 = NULL;
const char *g_vertexLabelT0 = "SubT0Con";
const char *g_vertexLabelT1 = "SubT1List";
const char *g_vertexLabelleaf0 = "leaf_rootcontainer";
const char *g_vertexLabelleaf1 = "leaf_SubT1container";
const char *g_vertexLabelleaf2 = "leaf_SubT1containerP";
const char *g_vertexLabelleaf3 = "leaf_case";
const char *g_vertexLabelleaf4 = "Listleaf";
const char *g_vertexLabelleaf5 = "leaf_uint8";
const char *g_vertexLabelleaf6 = "leaf_int8";
const char *g_vertexLabelleaf7 = "leaf_int16";
const char *g_vertexLabelleaf8 = "leaf_uint16";
const char *g_vertexLabelleaf9 = "leaf_uint32";
const char *g_vertexLabelleaf10 = "leaf_int32";
const char *g_vertexLabelleaf11 = "leaf_uint64";
const char *g_vertexLabelleaf12 = "leaf_int64";
const char *g_vertexLabelleaf13 = "leaf_uchar";
const char *g_vertexLabelleaf14 = "leaf_char";
const char *g_vertexLabelleaf15 = "leaf_bool";
const char *g_vertexLabelleaf16 = "leaf_double";
const char *g_vertexLabelleaf17 = "leaf_float";
const char *g_vertexLabelleaf18 = "leaf_string";
const char *g_vertexLabelleaf19 = "leaf_time";
const char *g_vertexLabelleaf20 = "leaf_bytes";
const char *g_vertexLabelleaf21 = "leaf_fixed";
const char *g_vertexLabelleaf22 = "leaf_bitmap";
const char *g_vertexLabelleaf23 = "leaf_casedef";

const char *g_edgeLabeT0T1 = "SubT0ConSubT1List";
const char *g_edgeLabeT0leaf0 = "rootleaf";
const char *g_edgeLabeT0leaf1 = "containerPleaf";
const char *g_edgeLabeT0leaf2 = "containerleaf";
const char *g_edgeLabeT0leaf3 = "SubT1choiceCaseleaf";
const char *g_edgeLabeT0leaf4 = "listleaf";
const char *g_edgeLabeT0leaf5 = "uint8leaf";
const char *g_edgeLabeT0leaf6 = "int8leaf";
const char *g_edgeLabeT0leaf7 = "uint16leaf";
const char *g_edgeLabeT0leaf8 = "int16leaf";
const char *g_edgeLabeT0leaf9 = "uint32leaf";
const char *g_edgeLabeT0leaf10 = "int32leaf";
const char *g_edgeLabeT0leaf11 = "uint64leaf";
const char *g_edgeLabeT0leaf12 = "int64leaf";
const char *g_edgeLabeT0leaf13 = "charleaf";
const char *g_edgeLabeT0leaf14 = "ucharleaf";
const char *g_edgeLabeT0leaf15 = "boolleaf";
const char *g_edgeLabeT0leaf16 = "doubleleaf";
const char *g_edgeLabeT0leaf17 = "floatleaf";
const char *g_edgeLabeT0leaf18 = "stringleaf";
const char *g_edgeLabeT0leaf19 = "timeleaf";
const char *g_edgeLabeT0leaf20 = "bytesleaf";
const char *g_edgeLabeT0leaf21 = "fixedleaf";
const char *g_edgeLabeT0leaf22 = "bitmapleaf";
const char *g_edgeLabeT0leaf23 = "SubT1choiceCaseleafdef";

const char *g_labelCcehConfig = R"(
{
"hash_type":"cceh"
}
)";
GmcTxConfigT g_trxConfig = {
    .readOnly = false,
    .transMode = GMC_TRANS_USED_IN_CS,
    .type = GMC_TX_ISOLATION_REPEATABLE,
    .trxType = GMC_OPTIMISTIC_TRX
};

// 清空nsp中所有元数据，包括数据和各种表
void testClearNsp(GmcStmtT *stmt, const char *namespaceName)
{
    AsyncUserDataT data = {0};
    int ret = GmcClearNamespaceAsync(stmt, namespaceName, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

// 写数据到内存满用的普通vertex表
const char *g_vertexLabel = "labelOOM";
// 32deep
const char *g_vertexLabelroot = "root";
const char *g_labelconfig = R"(
{
    "max_record_count":10000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";
const char *g_keyName = "PK";
// 申请各个表用的stmt
GmcStmtT *g_stmt_sync_T0 = NULL;
GmcStmtT *g_stmt_sync_T1List = NULL;
GmcStmtT *g_stmt_sync_leaflist = NULL;
void TestYangAllocAllstmt()
{
    int ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_sync_leaflist);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void TestYangFreeAllstmt()
{
    GmcFreeStmt(g_stmt_sync_T0);
    GmcFreeStmt(g_stmt_sync_T1List);
    GmcFreeStmt(g_stmt_sync_leaflist);
}
int testTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testsubtreeSetvalue(GmcNodeT * Node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldname, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldname, (strlen(fieldname) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(Node, &propValue, optype);
    return ret;
}
// yang set 字段
int testYangSetField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldname, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldname, (strlen(fieldname) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    return ret;
}
// yang set T0层f0
void testYangSetVertexProperty_F0(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    uint32_t f0value = i;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &f0value, sizeof(uint32_t), "F0", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set T0层一般字段 all type
void testYangSetVertexProperty(GmcNodeT *node, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(node, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(node, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f7 = 'A';
    ret = testYangSetField(node, GMC_DATATYPE_CHAR, &f7, sizeof(char), "F7", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char f8 = 'b';
    ret = testYangSetField(node, GMC_DATATYPE_UCHAR, &f8, sizeof(unsigned char), "F8", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t f9 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_INT8, &f9, sizeof(int8_t), "F9", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t f10 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_UINT8, &f10, sizeof(uint8_t), "F10", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t f11 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_INT16, &f11, sizeof(int16_t), "F11", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t f12 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_UINT16, &f12, sizeof(uint16_t), "F12", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f13 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &f13, sizeof(uint32_t), "F13", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t f14 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_INT64, &f14, sizeof(int64_t), "F14", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t f15 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_UINT64, &f15, sizeof(uint64_t), "F15", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t f16 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_TIME, &f16, sizeof(uint64_t), "F16", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t f17 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_BITFIELD8, &f17, sizeof(uint8_t), "F17", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t f18 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_BITFIELD16, &f18, sizeof(uint16_t), "F18", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f19 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_BITFIELD32, &f19, sizeof(uint32_t), "F19", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f20[] = "AAAAAA";
    ret = testYangSetField(node, GMC_DATATYPE_BYTES, &f20, 7, "F20", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f21[] = "AAAAAA";
    ret = testYangSetField(node, GMC_DATATYPE_FIXED, &f21, 7, "F21", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xffff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = testYangSetField(node, GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap), "F22", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t f23 = 1;
    ret = testYangSetField(node, GMC_DATATYPE_BITFIELD64, &f23, sizeof(uint64_t), "F23", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(node, GMC_DATATYPE_NULL, NULL, 0, "F24", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void testYangSetNodeProperty(GmcNodeT *node, int32_t f1, bool f2, double f3, bool f4, float f5,
    GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(node, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_BOOL, &f2, sizeof(f2), "F2", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_DOUBLE, &f3, sizeof(f3), "F3", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_BOOL, &f4, sizeof(f4), "F4", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(node, GMC_DATATYPE_FLOAT, &f5, sizeof(f5), "F5", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(node, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "F6", optype);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set list类型 主键
void testYangSetNodeProperty_PK(GmcNodeT *node, uint32_t f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 uint8类型
void testYangSetNodePropertyuint8_PK(GmcNodeT *node, uint8_t f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint8_t pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_UINT8, &pkValue, sizeof(uint8_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 int8类型
void testYangSetNodePropertyint8_PK(GmcNodeT *node, int8_t f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int8_t pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_INT8, &pkValue, sizeof(int8_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 uint16类型
void testYangSetNodePropertyuint16_PK(GmcNodeT *node, uint16_t f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint16_t pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_UINT16, &pkValue, sizeof(uint16_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 int16类型
void testYangSetNodePropertyint16_PK(GmcNodeT *node, int16_t f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int16_t pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_INT16, &pkValue, sizeof(int16_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 uint32类型
void testYangSetNodePropertyuint32_PK(GmcNodeT *node, uint32_t f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_UINT32, &pkValue, sizeof(uint32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 int32类型
void testYangSetNodePropertyint32_PK(GmcNodeT *node, int32_t f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int32_t pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_INT32, &pkValue, sizeof(int32_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 uint64类型
void testYangSetNodePropertyuint64_PK(GmcNodeT *node, uint64_t f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint64_t pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_UINT64, &pkValue, sizeof(uint64_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 int64类型
void testYangSetNodePropertyint64_PK(GmcNodeT *node, int64_t f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int64_t pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_INT64, &pkValue, sizeof(int64_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 uchar类型
void testYangSetNodePropertyuchar_PK(GmcNodeT *node, unsigned char f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    unsigned char pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_UCHAR, &pkValue, sizeof(unsigned char), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 char类型
void testYangSetNodePropertychar_PK(GmcNodeT *node, char f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    char pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_CHAR, &pkValue, sizeof(char), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 string类型
void testYangSetNodePropertystring_PK(GmcNodeT *node, int32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    char pkValue[10][7];
    ret = sprintf(pkValue[0], "strin0");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[1], "strin1");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[2], "strin2");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[3], "strin3");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[4], "strin4");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[5], "strin5");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[6], "strin6");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[7], "strin7");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[8], "strin8");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[9], "strin9");
    EXPECT_LT(0, ret);
    ret = testYangSetField(node, GMC_DATATYPE_STRING, &pkValue[i], (strlen(pkValue[i])), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 time类型
void testYangSetNodePropertytime_PK(GmcNodeT *node, uint64_t f0, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint64_t pkValue = f0;
    ret = testYangSetField(node, GMC_DATATYPE_TIME, &pkValue, sizeof(uint64_t), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 bytes类型
void testYangSetNodePropertybytes_PK(GmcNodeT *node, int32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    char pkValue[10][7];
    ret = sprintf(pkValue[0], "strin0");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[1], "strin1");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[2], "strin2");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[3], "strin3");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[4], "strin4");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[5], "strin5");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[6], "strin6");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[7], "strin7");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[8], "strin8");
    EXPECT_LT(0, ret);
    ret = sprintf(pkValue[9], "strin9");
    EXPECT_LT(0, ret);
    ret = testYangSetField(node, GMC_DATATYPE_BYTES, &pkValue[i], (strlen(pkValue[i])), "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// yang set leaf list类型 主键 fixed类型
void testYangSetNodePropertyfixed_PK(GmcNodeT *node, int32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    char pkValue[7];
    // 通过i来区分不同fixed主键
    ret = snprintf(pkValue, 7, "strin%d", i);
    EXPECT_LT(0, ret);
    ret = testYangSetField(node, GMC_DATATYPE_FIXED, &pkValue, 7, "F0", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void testSetKeyNameAndValue(GmcStmtT *stmt, uint32_t keyvalue, uint32_t numPID = 0, bool isList = false)
{
    int ret;

    // 设置KeyValue，PID为上层节点的ID，自增从1开始，PID=0，代表是根节点
    // yang模型现在修改为只能ID或PID为主键，只有list可以有PID+其他属性为主键
    if (numPID != 0) {
        // 只有list的主键允许设置为PID+属性
        if (isList) {
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = GmcSetIndexKeyName(stmt, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 向表中insert 数据到内存满
void TestGmcInsertVertex_OUTMem(GmcStmtT *stmt)
{
    int ret = 0;
    unsigned int len;
    int32_t i = 0;
    while (ret == 0) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_vertexLabel, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        i++;
        if ((i % 30000) == 0) {
            printf("till now:insert records %d\n", i);
        }
    }
    printf("actul insert records is:%d\n", i);
    EXPECT_EQ(GMERR_OUT_OF_MEMORY, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 向表中insert 数据
void TestGmcInsertVertex(GmcStmtT *stmt)
{
    int ret = 0;
    unsigned int len;
    int32_t i = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, g_vertexLabel, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    i++;
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 模型树直接预置 数据 Pleaflist为0 代表 不对Pleaflist设值 Pcon为0为创建实例
void testYangPresetAllDate(GmcConnT *conn, int Pleaflist = 0, int Pcon = 0, int NPcon = 0, int NPleaflist = 0)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    GmcNodeT *T1SubT1choice = NULL;
    GmcNodeT *SubT1choiceCase = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    const char *t1choiceNodeName = "SubT1choice";
    const char *t1choiceCaseNodeName = "SubT1choiceCase";
    const char *subT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 根节点 set 字段值
    uint32_t f0value = 1;
    int32_t f1 = 1;
    bool f2 = true;
    double f3 = 1;
    bool f4 = true;
    float f5 = 1;
    // 设置根节点属性
    testYangSetVertexProperty_F0(T0Node, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetVertexProperty(T0Node, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    if (NPcon == 1) {
        // 设置child节点
        ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置node节点属性
        testYangSetVertexProperty_F0(T1containerNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodeProperty(T1containerNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    } else if (NPcon == 5) {
        // 设置child节点
        ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_INSERT, &T1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置Pcontainer 节点和属性
    if (Pcon == 0) {
        ret = GmcYangEditChildNode(T0Node, subT1containerPNodeName, GMC_OPERATION_INSERT, &SubT1containerPNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetVertexProperty_F0(SubT1containerPNode, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodeProperty(SubT1containerPNode, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    } else if (Pcon == 5) {
        printf(" Pcon is %d \n", Pcon);
        ret = GmcYangEditChildNode(T0Node, subT1containerPNodeName, GMC_OPERATION_INSERT, &SubT1containerPNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置child节点
    ret = GmcYangEditChildNode(T0Node, t1choiceNodeName, GMC_OPERATION_INSERT, &T1SubT1choice);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T1SubT1choice, t1choiceCaseNodeName, GMC_OPERATION_INSERT, &SubT1choiceCase);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点属性
    testYangSetVertexProperty_F0(SubT1choiceCase, f0value, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodeProperty(SubT1choiceCase, f1, f2, f3, f4, f5, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    // 添加 根节点下的 leaflist
    for (uint32_t i = 1; i < 11; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_rootcontainerNode = NULL;
        const char *leafrootcontainerName = "leaf_rootcontainer";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafrootcontainerName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_rootcontainerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty_PK(leaf_rootcontainerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (NPleaflist == 1) {
        // 添加 T1container下的 leaflist
        for (uint32_t i = 2; i < 7; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leafSubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafSubT1containerName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    } else if (NPleaflist == 2) {
        // 添加 T1container下的 leaflist
        for (uint32_t i = 12; i < 17; i++) {
            int32_t listf1 = i;
            bool listf2 = true;
            double listf3 = i;
            bool listf4 = true;
            float listf5 = 1;
            GmcNodeT *leaf_SubT1containerNode = NULL;
            const char *leafSubT1containerName = "leaf_SubT1container";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafSubT1containerName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    // 添加 SubT1choiceCase下的 leaflist
    for (uint32_t i = 1; i < 11; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_caseNode = NULL;
        const char *leafcaseName = "leaf_case";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafcaseName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_caseNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty_PK(leaf_caseNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // // 添加 根节点下的 uint8类型leaflist
    for (uint32_t i = 9; i < 20; i++) {
        uint8_t listf1 = i;
        GmcNodeT *leaf_uint8Node = NULL;
        const char *leafuint8Name = "leaf_uint8";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafuint8Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_uint8Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyuint8_PK(leaf_uint8Node, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 int8类型leaflist
    for (uint32_t i = 6; i < 15; i++) {
        int8_t listf1 = i;
        GmcNodeT *leaf_int8Node = NULL;
        const char *leafint8Name = "leaf_int8";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafint8Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_int8Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyint8_PK(leaf_int8Node, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 uint16类型leaflist
    for (uint32_t i = 7; i < 16; i++) {
        uint16_t listf1 = i;
        GmcNodeT *leaf_uint16Node = NULL;
        const char *leafuint16Name = "leaf_uint16";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafuint16Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_uint16Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyuint16_PK(leaf_uint16Node, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 int16类型leaflist
    for (uint32_t i = 8; i < 18; i++) {
        int16_t listf1 = i;
        GmcNodeT *leaf_int16Node = NULL;
        const char *leafint16Name = "leaf_int16";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafint16Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_int16Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyint16_PK(leaf_int16Node, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 uint32类型leaflist
    for (uint32_t i = 9; i < 19; i++) {
        uint32_t listf1 = i;
        GmcNodeT *leaf_uint32Node = NULL;
        const char *leafuint32Name = "leaf_uint32";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafuint32Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_uint32Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyuint32_PK(leaf_uint32Node, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 int32类型leaflist
    for (uint32_t i = 3; i < 13; i++) {
        int32_t listf1 = i;
        GmcNodeT *leaf_int32Node = NULL;
        const char *leafint32Name = "leaf_int32";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafint32Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_int32Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyint32_PK(leaf_int32Node, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 uint64类型leaflist
    for (uint32_t i = 4; i < 14; i++) {
        uint64_t listf1 = i;
        GmcNodeT *leaf_uint64Node = NULL;
        const char *leafuint64Name = "leaf_uint64";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafuint64Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_uint64Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyuint64_PK(leaf_uint64Node, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 int64类型leaflist
    for (uint32_t i = 5; i < 15; i++) {
        int64_t listf1 = i;
        GmcNodeT *leaf_uint64Node = NULL;
        const char *leafint64Name = "leaf_int64";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafint64Name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_uint64Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyint64_PK(leaf_uint64Node, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 char 类型leaflist
    for (uint32_t i = 0; i < 10; i++) {
        int64_t listf1 = i;
        char a[10] = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j'};
        GmcNodeT *leaf_charNode = NULL;
        const char *leafcharName = "leaf_char";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafcharName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_charNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertychar_PK(leaf_charNode, a[i], GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 uchar 类型leaflist
    for (uint32_t i = 0; i < 10; i++) {
        int64_t listf1 = i;
        char a[10] = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'};
        GmcNodeT *leaf_charNode = NULL;
        const char *leafucharName = "leaf_uchar";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafucharName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_charNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyuchar_PK(leaf_charNode, a[i], GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 string 类型leaflist
    for (uint32_t i = 0; i < 10; i++) {
        GmcNodeT *leaf_stringNode = NULL;
        const char *leafstringName = "leaf_string";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafstringName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_stringNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertystring_PK(leaf_stringNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 time 类型leaflist
    for (uint32_t i = 0; i < 10; i++) {
        GmcNodeT *leaf_timeNode = NULL;
        uint64_t m = i;
        const char *leaftimeName = "leaf_time";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaftimeName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_timeNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertytime_PK(leaf_timeNode, m, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 bytes 类型leaflist
    for (uint32_t i = 0; i < 10; i++) {
        GmcNodeT *leaf_bytesNode = NULL;
        const char *leafbytesName = "leaf_bytes";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafbytesName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_bytesNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertybytes_PK(leaf_bytesNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 添加 根节点下的 fixed 类型leaflist
    for (uint32_t i = 0; i < 10; i++) {
        GmcNodeT *leaf_fixedNode = NULL;
        const char *leaffixedName = "leaf_fixed";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leaffixedName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_fixedNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodePropertyfixed_PK(leaf_fixedNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (uint32_t i = 1; i < 11; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *T1ListNode = NULL;
        GmcNodeT *T2containerNode = NULL;
        GmcNodeT *T2choiceNode = NULL;
        GmcNodeT *T2choiceCaseNode = NULL;
        const char *t1ListNodeName = "SubT1List";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &T1ListNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty_PK(T1ListNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodeProperty(T1ListNode, listf1, listf2, listf3, listf4, listf5,
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加 SubT1List下的 leaflist
        for (uint32_t k = 1; k < 11; k++) {
            GmcNodeT *ListleafNode = NULL;
            const char *listleafName = "Listleaf";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_leaflist, listleafName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_leaflist);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_leaflist, &ListleafNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodeProperty_PK(ListleafNode, k, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_leaflist);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    // P类型 container 下的 leaflist 设置值 且 设置为默认值
    if (Pleaflist == 1) {
        for (uint32_t i = 2; i < 7; i++) {
            uint32_t listf1 = i;
            GmcNodeT *leaf_SubT1containerPNode = NULL;
            const char *leafSubT1containerPName = "leaf_SubT1containerP";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafSubT1containerPName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerPNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodePropertyuint32_PK(leaf_SubT1containerPNode, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    } else if (Pleaflist == 2) { // P类型 container 下的 leaflist 设置值 且 设置为非默认值
        for (uint32_t i = 11; i < 20; i++) {
            uint32_t listf1 = i;
            GmcNodeT *leaf_SubT1containerPNode = NULL;
            const char *leafSubT1containerPName = "leaf_SubT1containerP";
            // 这里需要prepar list的labelname
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafSubT1containerPName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerPNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            testYangSetNodePropertyuint32_PK(leaf_SubT1containerPNode, listf1, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    printf("data.succNum is %d \n", data.succNum);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void InitListProperty(
    GmcYangListLocatorT *listProperty, GmcYangListPositionE position, GmcPropValueT *referenceKey)
{
    if (listProperty == NULL) {
        return;
    }
    listProperty->position = position;
    if (referenceKey != NULL) {
        listProperty->refKeyFields = (GmcPropValueT **)malloc(sizeof(GmcPropValueT *));
        listProperty->refKeyFields[0] = referenceKey;
        listProperty->refKeyFieldsCount = 1;
    } else {
        listProperty->refKeyFields = NULL;
        listProperty->refKeyFieldsCount = 0;
    }
}

void UninitListProperty(GmcYangListLocatorT *listProperty)
{
    if (listProperty->refKeyFields == NULL) {
        return;
    }
    free(listProperty->refKeyFields);
    listProperty->refKeyFields = NULL;
}
// 模型树中的表完成merge操作
void testYangMergeDate(GmcConnT *conn)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    GmcNodeT *T1SubT1choice = NULL;
    GmcNodeT *SubT1choiceCase = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    const char *t1choiceNodeName = "SubT1choice";
    const char *t1choiceCaseNodeName = "SubT1choiceCase";
    const char *subT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 8; i < 17; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leafSubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafSubT1containerName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testSetKeyNameAndValue(g_stmt_sync_T1List, listf1, 1, true);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    printf("data.succNum is %d \n", data.succNum);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 模型树中的表完成merge操作
void testYangMergeListFIirstDate(GmcConnT *conn)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    GmcNodeT *T1SubT1choice = NULL;
    GmcNodeT *SubT1choiceCase = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    const char *t1choiceNodeName = "SubT1choice";
    const char *t1choiceCaseNodeName = "SubT1choiceCase";
    const char *subT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 8; i < 17; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leafSubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafSubT1containerName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcYangListLocatorT listProp;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
        UninitListProperty(&listProp);
        testSetKeyNameAndValue(g_stmt_sync_T1List, listf1, 1, true);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 模型树中的表完成merge操作
void testYangMergeListLastDate(GmcConnT *conn)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    GmcNodeT *T1SubT1choice = NULL;
    GmcNodeT *SubT1choiceCase = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    const char *t1choiceNodeName = "SubT1choice";
    const char *t1choiceCaseNodeName = "SubT1choiceCase";
    const char *subT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 8; i < 17; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leafSubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafSubT1containerName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcYangListLocatorT listProp;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, NULL);
        ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
        UninitListProperty(&listProp);
        testSetKeyNameAndValue(g_stmt_sync_T1List, listf1, 1, true);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void InitRefKeys(GmcPropValueT *refKey, uint32_t propId, void *value, GmcDataTypeE type = GMC_DATATYPE_UINT32,
    uint32_t sizeValue = 4)
{
    refKey->propertyId = propId;
    refKey->propertyName[0] = '\0';
    refKey->type = type;
    refKey->size = sizeValue;
    refKey->value = value;
}
 
// 模型树中的表完成merge操作GMC_YANG_LIST_POSITION_BEFORE
void testYangMergeListBeforeDate(GmcConnT *conn)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    GmcNodeT *T1SubT1choice = NULL;
    GmcNodeT *SubT1choiceCase = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    const char *t1choiceNodeName = "SubT1choice";
    const char *t1choiceCaseNodeName = "SubT1choiceCase";
    const char *subT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 8; i < 17; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leafSubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafSubT1containerName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0 = 5;
        GmcPropValueT refKey ;
        InitRefKeys(&refKey, 1, &f0);
        GmcYangListLocatorT listProp;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
        ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
        UninitListProperty(&listProp);
        testSetKeyNameAndValue(g_stmt_sync_T1List, listf1, 1, true);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 模型树中的表完成merge操作GMC_YANG_LIST_POSITION_AFTER
void testYangMergeListAfterDate(GmcConnT *conn)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    GmcNodeT *T1SubT1choice = NULL;
    GmcNodeT *SubT1choiceCase = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    const char *t1choiceNodeName = "SubT1choice";
    const char *t1choiceCaseNodeName = "SubT1choiceCase";
    const char *subT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 8; i < 17; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leafSubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafSubT1containerName, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0 = 2;
        GmcPropValueT refKey ;
        InitRefKeys(&refKey, 1, &f0);
        GmcYangListLocatorT listProp;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
        ASSERT_EQ(GMERR_OK, GmcYangSetListLocator(g_stmt_sync_T1List, &listProp));
        UninitListProperty(&listProp);
        testSetKeyNameAndValue(g_stmt_sync_T1List, listf1, 1, true);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    printf("data.succNum is %d \n", data.succNum);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 模型树中的表完成replace操作
void testYangReplaceDate(GmcConnT *conn)
{
    int ret ;
    // 开启事务
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    GmcNodeT *T1SubT1choice = NULL;
    GmcNodeT *SubT1choiceCase = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    const char *t1choiceNodeName = "SubT1choice";
    const char *t1choiceCaseNodeName = "SubT1choiceCase";
    const char *subT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = 8; i < 17; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leafSubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafSubT1containerName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    printf("succNum is %d\n", data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 模型树中的表完成delete操作
void testYangdeleteDate(GmcConnT *conn)
{
    int ret = 0;
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    GmcNodeT *T0Node = NULL;
    GmcNodeT *T1containerNode = NULL;
    GmcNodeT *T1SubT1choice = NULL;
    GmcNodeT *SubT1choiceCase = NULL;
    GmcNodeT *SubT1containerPNode = NULL;
    const char *t1containerNodeName = "SubT1container";
    const char *t1choiceNodeName = "SubT1choice";
    const char *t1choiceCaseNodeName = "SubT1choiceCase";
    const char *subT1containerPNodeName = "SubT1containerP";
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_T0, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, t1containerNodeName, GMC_OPERATION_NONE, &T1containerNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    for (uint32_t i = 12; i < 15; i++) {
        int32_t listf1 = i;
        bool listf2 = true;
        double listf3 = i;
        bool listf4 = true;
        float listf5 = 1;
        GmcNodeT *leaf_SubT1containerNode = NULL;
        const char *leafSubT1containerName = "leaf_SubT1container";
        // 这里需要prepar list的labelname
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T1List, leafSubT1containerName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T0, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &leaf_SubT1containerNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testSetKeyNameAndValue(g_stmt_sync_T1List, listf1, 1, true);
        testYangSetNodeProperty_PK(leaf_SubT1containerNode, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("data.succNum is %d \n", data.succNum);
    EXPECT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 模型树中的表完成remove操作
void testYangRemoveDate(GmcConnT *conn)
{
    int ret = 0;
    GmcTxConfigT TrxConfig = {0};
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // merge操作
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_T0, g_vertexLabelT0, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    uint32_t  keyvalue = 1;
    testSetKeyNameAndValue(g_stmt_sync_T0, keyvalue);
    ret = GmcBatchAddDML(batch, g_stmt_sync_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    printf("data.succNum is %d \n", data.succNum);
    EXPECT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}
// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}
string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
        }
}
string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}
void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}
// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        cout << "actual diff：\n" << res;
        ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void FetchDiff_callback(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            ASSERT_TRUE(isEnd);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}
// 获取diff
void testFetchAndDeparseDiff(GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data,
    int rets = GMERR_OK)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

int GetTimeZoneType()
{
    char timecmd[TIMEZONEMAXCMD] = {0};
    // 将date 定向到 time.ini中
    (void)snprintf(timecmd, TIMEZONEMAXCMD, " date > time.ini");
    system(timecmd);

    // 返回0为CST时区，1为UTC时区
    char key1[2048] = "CST";
    char key2[2048] = "Defaul";
    char key3[2048] = "UTC";
    FILE *fp;
    char buffer[2048];
    // 从 time.ini 获取 date时区
    (void)snprintf(timecmd, TIMEZONEMAXCMD, "cat  time.ini");
    printf("%s\n", timecmd);
    fp = popen(timecmd, "r");
    (void)fgets(buffer, sizeof(buffer), fp);
    printf("%s", buffer);
    // 如果获取的字符串中 包括 CST 则时区类型为 CST
    if (strstr(buffer, key1)) {
        printf("key1 is %s  buffer  is %s \n", key1, buffer);
        (void)pclose(fp);
         fp = NULL;
        return 0;
    // 如果获取的字符串中 包括 Defaul 则时区类型为 Defaul
    } else if (strstr(buffer, key2)) {
        printf("key2 is %s  buffer  is %s \n", key2, buffer);
        (void)pclose(fp);
         fp = NULL;
        return 1;
    } else if (strstr(buffer, key3)) {
        printf("key3 is %s  buffer  is %s \n", key3, buffer);
        (void)pclose(fp);
         fp = NULL;
        return 1;
    } else {
        printf(" buffer  is %s \n", buffer);
        (void)pclose(fp);
        fp = NULL;
        return 0;
    }
}
#endif
