/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "RCA_COMMON.h"
#include "../../../reliability/try.h"
#include "aliasTool.h"

class xpathMergeCon : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void xpathMergeCon::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"yangMergeXPath=1\"");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void xpathMergeCon::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}


void xpathMergeCon::SetUp()
{
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务

    const char *namespace1 = "xpathMergeCon";
    const char *namespaceUserName = "abc";
    snprintf(g_nspName, sizeof(g_nspName), "xpathMergeCon");

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // alloc all stmt
    TestYangAllocAllstmt();
}

void xpathMergeCon::TearDown()
{
    const char *namespace1 = "xpathMergeCon";
    TryDropNameSpace(g_stmt_async, namespace1);

    // 释放all stmt
    TestYangFreeAllstmt();

    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


void TestCheckValidateModelAsync(GmcStmtT *stmt)
{
    // 模型校验
    YangValidateUserDataT checkData = {0};
    int ret = GmcYangValidateModelAsync(stmt, TestYangValidateCb, &checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&checkData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, checkData.status);
    AW_MACRO_EXPECT_EQ_INT(true, checkData.validateRes);
    AW_MACRO_EXPECT_EQ_INT(0, checkData.failCount);
    memset(&checkData, 0, sizeof(YangValidateUserDataT));
}

// *typedef void (*GmcYangValidateDoneT)(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg);*/
void AsyncValidateLeafRefCb(void *userData, GmcValidateResT validateRes, int32_t status, const char *errMsg)
{
    if (userData) {
        YangValidateUserDataT *uData = (YangValidateUserDataT *)userData;
        uData->status = status;
        if ((status != GMERR_OK) && (errMsg != NULL)) {
            printf("YangValidate errMsg: %s\n", errMsg);
        }
        uData->validateRes = validateRes.validateRes;
        uData->failCount = validateRes.failCount;

        printf(">>> validateRes: %d\n", validateRes.validateRes);
        printf(">>> failCount: %u\n", validateRes.failCount);

        if (uData->isValidErrorPathInfo) {
            GmcErrorPathInfoT msg;
            ASSERT_EQ(GMERR_OK, GmcYangGetErrorPathInfo(&msg));

            // 结果检查
            printf("--- errcode: %d\n", msg.errorCode);
            printf("--- errorClauseIndex: %u\n", msg.errorClauseIndex);
            printf("--- errorMsg: %s\n", msg.errorMsg);
            printf("--- errorPath: %s\n", msg.errorPath);
            EXPECT_EQ(uData->expectedErrCode, msg.errorCode);
            EXPECT_EQ(uData->expectedErrClauseIndex, msg.errorClauseIndex);
            EXPECT_STREQ(uData->expectedErrMsg, msg.errorMsg);
            EXPECT_STREQ(uData->expectedErrPath, msg.errorPath);
            ASSERT_NO_FATAL_FAILURE(GmcYangFreeErrorPathInfo());
        }

        uData->recvNum++;
    }
}


/*****************************************************************************
 * Description  : 001.P contain节点，兄弟属性上有相同的when条件xpath，xpath里包括绝对路径，预期合并
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");
    ret = checkXpathMergeView(1, "alias_ContainerOne/F0 = 200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = checkXpathMergeView(1, "alias_ListOne/F1 = 101");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_001");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 002.NP contain节点，兄弟属性上有相同的when条件xpath，xpath里包括相对路径，预期合并
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel2.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));

    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");
    ret = checkXpathMergeView(1, "alias_ContainerOne/F0 = 200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 有一个没默认值，不合并
    ret = checkXpathMergeView(2, "alias_ListOne/F1 = 101");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_002");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 003.contain节点，兄弟属性上有相同的when条件xpath，xpath里包括复杂路径跨表、带逻辑，预期合并
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel3.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");   
    ret = checkXpathMergeView(1, "alias_ContainerOne/F0 = 200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = checkXpathMergeView(1, "alias_ContainerOne/F0 = 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全部合并
    ret = checkXpathMergeView(1, "alias_ListOne/F1 = 101 AND alias_ListOne/F6 = \\\"str00101\\\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // // // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_003");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 004.contain节点，兄弟属性上有相同的when条件xpath，里面包含curent()，预期不会合并
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel4.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");   不合并
    ret = checkXpathMergeView(4, "alias_ContainerOne/F0 = 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不合并
    ret = checkXpathMergeView(5, "AND alias_ListOne/F6 = \\\"str00101\\\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_004");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 005.contain节点，兄弟属性上有相同的when条件xpath，里面包含curent()并且路径终点为同一个属性节点，预期不会合并
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel5.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");   合并
    ret = checkXpathMergeView(1, "alias_ContainerOne/F0 = alias_ContainerOne/F9");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // xpath一样，但是落点在自己身上的不合并
    ret = checkXpathMergeView(2, "alias_ListOne/ContainerTwo/F9 = alias_ContainerOne/F0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_005");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 006.contain节点，同级非兄弟属性上有相同的when条件xpath，预期不会合并
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel6.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");   
    ret = checkXpathMergeView(2, "alias_ListOne/F1 = alias_ContainerOne/F0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_006");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 007.contain节点，父子层级属性上有相同的when条件xpath，预期不会合并
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel7.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");   
    ret = checkXpathMergeView(2, "alias_ListOne/F1 != alias_ContainerOne/F0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

       // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_007");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 008.contain节点，兄弟层级属性节点上有相同的when条件xpath，预期不会合并
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel8.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");   不合并
    ret = checkXpathMergeView(2, "alias_ListOne/F1 = alias_ContainerOne/F0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

       // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_BUTT;
    dataLef.expectedErrMsg = "";
    dataLef.expectedErrPath = "";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_008");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 009.contain节点，兄弟属性上有相同的must条件xpath，预期不会合并
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel9.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");   不合并
    ret = checkXpathMergeView(5, "alias_ContainerOne/F0 = alias_ContainerOne/F9", "MUST");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = checkXpathMergeView(5, "alias_ListOne/ContainerTwo/F9 = alias_ContainerOne/F0", "MUST");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

       // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F1";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_009");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}

/*****************************************************************************
 * Description  : 010.contain节点，兄弟属性上有相同的leafref条件xpath，预期不会合并
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel10.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");   不合并
    ret = checkXpathMergeView(5, "alias_ContainerOne/F0", "LEAF-REF");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = checkXpathMergeView(5, "alias_ListOne/F1", "LEAF-REF");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

       // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F5";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_010");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 011.contain节点，父子兄弟节点，包括P、NP，有相同的when条件xpath，预期不会合并
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel11.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");   不合并
    ret = checkXpathMergeView(8, "alias_ContainerOne/F0 = 200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

       // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/F5";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_011");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 012.contain节点，兄弟属性上有相同的when条件xpath，预期合并，must校验依赖when校验结果
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel12.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");   不合并
    ret = checkXpathMergeView(1, "alias_ContainerOne/F0 = 200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = checkXpathMergeView(5, "alias_ContainerOne/F1 = 100", "MUST");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

       // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MUST;
    dataLef.expectedErrMsg = "violated must clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/ContainerTwo/F5";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_012");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 013.contain节点，兄弟属性上有相同的when条件xpath，预期合并，leafref校验依赖when校验结果
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel13.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");
    ret = checkXpathMergeView(1, "alias_ContainerOne/F0 = 200");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = checkXpathMergeView(3, "alias_ListOne/ContainerTwo/F6", "LEAF-REF");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

       // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/ContainerThree/F5";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_013");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 014.contain节点，兄弟属性上有相同的when条件xpath，预期合并，mandatory校验依赖when校验结果
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel14.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图system("gmsysview -q V\\$YANG_PLAN_EXPLAIN_INFO");
    ret = checkXpathMergeView(1, "alias_ContainerOne/F0 = 100");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = checkXpathMergeView(1, "alias_ListOne/ContainerTwo/F6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

       // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = false;
    dataLef.expectedErrCode = GMC_VIOLATES_LEAFREF;
    dataLef.expectedErrMsg = "violated leaf-ref clause 0";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne[F0=\"str00100\"]/ContainerThree/F5";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_014");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}


/*****************************************************************************
 * Description  : 015.contain节点，兄弟属性上有相同的when条件xpath，预期合并，min-max校验依赖when校验结果
 * Input        : None
 * Output       : None

 * Author       : wk/swx703884
 * Modification : 
 * *****************************************************************************/
TEST_F(xpathMergeCon, Yang_093_xpathMergeCon_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    //.建表 建边
    readJanssonFile("schemaXpathCon/SubTreeVertexLabel15.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schemaXpathCon/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);

     // 模型校验
    ASSERT_NO_FATAL_FAILURE(TestCheckValidateModelAsync(g_stmt_async));


    // 检查合并xpath视图
    ret = checkXpathMergeView(2, "alias_ContainerOne/F0 = 101");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建乐观事务
    TransStart(g_conn_async);
    int nDmlCnt = 0;

    // 设置批处理,开启diff
    GmcBatchT *batch = NULL;
    ret = testBatchPrepare(g_conn_async, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_async, g_vertexLabelT0, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_vertexLabelT0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t fieldValue = 100;
    testYangSetVertexProperty_F0(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F2");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F3");
    testYangSetVertexProperty_Fx(g_vertexLabelT0Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F4");

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    nDmlCnt++;

    for (int i = 100; i < 103; i++) {
        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T1List, g_vertexLabelT1, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T1List, &g_vertexLabelT1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListOne
        char fieldStr[15] = {0};
        snprintf(fieldStr, sizeof(fieldStr), "str00%d", i);
        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F0",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        fieldValue = i;
        testYangSetVertexProperty_Fx(g_vertexLabelT1Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F1");

        ret = testYangSetField(g_vertexLabelT1Node, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对 container NP 子节点做replace操作  -- ContainerTwo 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName2, GMC_OPERATION_REPLACE_GRAPH, &g_containerT2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        testYangSetVertexProperty_Fx(g_containerT2Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE, "F9");

        // 对 container P 子节点做replace操作  -- ContainerThree 
        ret = TestGmcYangEditChildNode(g_vertexLabelT1Node, g_containerNodeName3, GMC_OPERATION_REPLACE_GRAPH, &g_containerT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty(g_containerT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

       // 对 ContainerFour 子节点做replace操作 -- ContainerThree.ContainerFour
        GmcNodeT *tmpNode1 = NULL;
        ret = TestGmcYangEditChildNode(g_containerT3Node, "ContainerFour", GMC_OPERATION_REPLACE_GRAPH, &tmpNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(tmpNode1, GMC_DATATYPE_STRING, &fieldStr, (strlen(fieldStr)), "F6",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T1List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;

        // 这里需要prepar list的labelname
        ret = TestYangGmcPrepareStmtByLabelName(NULL, g_stmt_sync_T2List, g_vertexLabelT3, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_T1List, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_sync_T2List, &g_vertexLabelT3Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 对子节点字段 做replace操作  --ListTwo
        fieldValue = i;
        testYangSetVertexProperty_F0(g_vertexLabelT3Node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_T2List);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        nDmlCnt++;
    }

    // 提交批处理
    BatchExecute(batch, nDmlCnt, nDmlCnt);

    // when校验
    bool isDataService = true;

    YangValidateUserDataT dataLef = {0};
    dataLef.status = GMERR_OK;
    dataLef.isValidErrorPathInfo = true;
    dataLef.expectedErrCode = GMC_VIOLATES_MAX_ELEMENTS;
    dataLef.expectedErrMsg = "violated max-elements clause";
    dataLef.expectedErrPath = "/alias_ContainerOne/alias_ListOne";

    GmcValidateResT sucRes = {.validateRes = true, .failCount = 0};
    GmcValidateResT failRes = {.validateRes = false, .failCount = 1};
    GmcValidateResT checkRes = (dataLef.isValidErrorPathInfo)?failRes:sucRes;
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL_FORCE, .cfgJson = NULL};

    ret = GmcYangValidateAsync(g_stmt_async, &cfg, AsyncValidateLeafRefCb, &dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestWaitYangValidateRecv(&dataLef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, dataLef.status);
    AW_MACRO_EXPECT_EQ_INT(checkRes.validateRes, dataLef.validateRes);
    AW_MACRO_EXPECT_EQ_INT(checkRes.failCount, dataLef.failCount);
    memset(&dataLef, 0, sizeof(YangValidateUserDataT));

    // subtree查询 obj
    TestSubtreeFilterObjAll(g_stmt_async, "ContainerOne", "Yang_093_xpathMergeCon_015");

    // 提交事务
    TransCommit(g_conn_async);

    system("gmsysview subtree -ns xpathMergeCon -rn ContainerOne -defaultMode REPORT_ALL");
    AW_FUN_Log(LOG_STEP, "END");
}
