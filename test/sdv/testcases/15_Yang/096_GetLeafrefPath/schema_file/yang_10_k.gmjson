[{"type": "container", "name": "root_10_k", "presence": false, "fields": [{"name": "ID", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}], "keys": [{"name": "PK", "fields": ["ID"], "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_1_k", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_1_1", "presence": false, "fields": [{"name": "F0", "type": "uint32", "nullable": true, "default": 100}, {"name": "F1", "type": "uint32", "nullable": true, "default": 100}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}, {"type": "choice", "name": "choice_2", "fields": [{"type": "case", "name": "case_2_1", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_2_2", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_2_k", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_1_1", "presence": false, "fields": [{"name": "F0", "type": "uint32", "nullable": true, "default": 100, "clause": [{"type": "leafref", "formula": "/root_10/list_1[PK=current()/../../PK]/con_1_1/F0", "require-instance": false}]}, {"name": "F1", "type": "uint32", "nullable": true, "default": 100, "clause": [{"type": "leafref", "formula": "/root_10/list_1[PK=current()/../../PK]/con_1_1/F0", "require-instance": true}]}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}, {"type": "choice", "name": "choice_2", "fields": [{"type": "case", "name": "case_2_1", "fields": [{"name": "F0", "type": "uint32", "nullable": true, "clause": [{"type": "leafref", "formula": "/root_10/list_1[PK=current()/../../../PK]/choice_2/case_2_1/F0", "require-instance": false}]}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_2_2", "fields": [{"name": "F0", "type": "uint32", "nullable": true, "clause": [{"type": "leafref", "formula": "/root_10/list_1[PK=current()/../../../PK]/choice_2/case_2_1/F0", "require-instance": false}]}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_3_k", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_1_2", "presence": true, "fields": [{"name": "F0", "type": "uint32", "nullable": true, "default": 100}, {"name": "F1", "type": "uint32", "nullable": true, "default": 100}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_4_k", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"type": "container", "name": "con_1_2", "presence": true, "fields": [{"name": "F0", "type": "uint32", "nullable": true, "default": 100, "clause": [{"type": "leafref", "formula": "/root_10/list_3[PK=current()/../../PK]/con_1_2/F0", "require-instance": false}]}, {"name": "F1", "type": "uint32", "nullable": true, "default": 100, "clause": [{"type": "leafref", "formula": "/root_10/list_3[PK=current()/../../PK]/con_1_2/F0", "require-instance": true}]}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_5_k", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"type": "choice", "name": "choice_1", "fields": [{"type": "case", "name": "case_1_1", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_1_2", "default": true, "fields": [{"name": "F0", "type": "uint32", "nullable": true, "default": 100}, {"name": "F1", "type": "uint32", "nullable": true, "default": 100}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_6_k", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"type": "choice", "name": "choice_1", "fields": [{"type": "case", "name": "case_1_1", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_1_2", "default": true, "fields": [{"name": "F0", "type": "uint32", "nullable": true, "default": 100, "clause": [{"type": "leafref", "formula": "/root_10/list_5[PK=current()/../../../PK]/choice_1/case_1_2/F0", "require-instance": false}]}, {"name": "F1", "type": "uint32", "nullable": true, "default": 100, "clause": [{"type": "leafref", "formula": "/root_10/list_5[PK=current()/../../../PK]/choice_1/case_1_2/F1", "require-instance": true}]}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_7_k", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"type": "choice", "name": "choice_3", "fields": [{"type": "case", "name": "case_3_1", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_3_2", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}, {"type": "list", "name": "list_8_k", "fields": [{"name": "ID", "type": "uint32", "nullable": false, "auto_increment": true}, {"name": "PID", "type": "uint32", "nullable": false}, {"name": "PK", "type": "uint32", "nullable": false}, {"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}, {"type": "choice", "name": "choice_3", "fields": [{"type": "case", "name": "case_3_1", "fields": [{"name": "F0", "type": "uint32", "nullable": true, "clause": [{"type": "leafref", "formula": "/root_10/list_7[PK=100]/choice_3/case_3_1/F0", "require-instance": false}]}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}, {"type": "case", "name": "case_3_2", "fields": [{"name": "F0", "type": "uint32", "nullable": true, "clause": [{"type": "leafref", "formula": "/root_10/list_7[PK=200]/choice_3/case_3_1/F0", "require-instance": false}]}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "uint32", "nullable": true}]}]}], "keys": [{"fields": ["PID", "PK"], "name": "PK", "index": {"type": "primary"}, "constraints": {"unique": true}}]}]