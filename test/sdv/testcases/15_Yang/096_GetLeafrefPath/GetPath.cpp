/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: 096_GetLeafrefPath
 * Author: hanyang
 * Create: 2024-07-22
 */
#include "GetPath.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[100] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[100] = {0};

class GetPath_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GetPath_test::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"yangAutoIndex=1\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GetPath_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void GetPath_test::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 100; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_CHECK_LOG_BEGIN();
}

void GetPath_test::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AW_CHECK_LOG_END();

    // 删除表
    TestDropLabel(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 100; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 100; i++) {
        g_stmt_list[i] = NULL;
    }
    g_rootNode = NULL;
    for (i = 0; i < 100; i++) {
        g_childNode[i] = NULL;
    }
}

/*****************************************************************************
 Description  : 001.新增接口的参数为空指针
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 接口测试
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]"
    };
    data.leafrefPathes.num = expectPathNum;
    data.leafrefPathes.paths = expectPath01;
    ret = GmcYangGetLeafrefPathAsync(NULL, "/root_1/list1[PK=1]", NULL, GetPath_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, NULL, NULL, GetPath_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 002.新增接口的路径参数内容存在不合法字符
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 接口测试
    expectPathNum = 0;
    data.leafrefPathes.num = expectPathNum;
    data.expStatus = GMERR_SYNTAX_ERROR;
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_1/list@@@@1[PK=1]", NULL, GetPath_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_1/list中文1[PK=1]", NULL, GetPath_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_1/list_1[PK<>>1]", NULL, GetPath_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_1/list@@@@1[PK=1]\\con_1", NULL, GetPath_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_SYNTAX_ERROR);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 003.新增接口的路径参数内容为不存在的路径
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 接口测试
    expectPathNum = 0;
    data.leafrefPathes.num = expectPathNum;
    data.expStatus = GMERR_SYNTAX_ERROR;
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_1/list_4[PK=1]", NULL, GetPath_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_1/list_1[PK=1]/con_4", NULL, GetPath_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_1/list_1[PK=1]/choice_3/case_3/list_3[PK=1]", NULL,
        GetPath_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_SYNTAX_ERROR);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 004.新增接口的路径参数内容包含32层嵌套，实际存在引用关系，按照实际情况返回引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 建表
    TestCreateLabel3248(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3248", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_3248_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置32层嵌套数据
    for (uint32_t i = 1; i <= 31; i++) {
        char conName[128] = {0};
        (void)sprintf(conName, "c%d", i);
        ret = GmcYangEditChildNode(g_childNode[i], (const char *)conName, GMC_OPERATION_INSERT, &g_childNode[i+1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 1;
        TestYangSetNodePropertyBasic(g_childNode[i+1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_3248_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置32层嵌套数据
    for (uint32_t i = 1; i <= 31; i++) {
        char conName[128] = {0};
        (void)sprintf(conName, "c%d", i);
        ret = GmcYangEditChildNode(g_childNode[i], (const char *)conName, GMC_OPERATION_INSERT, &g_childNode[i+1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 1;
        TestYangSetNodePropertyBasic(g_childNode[i+1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_3248/list_3248_2[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root,
        "/root_3248/list_3248_1[PK=1]/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12"
        "/c13/c14/c15/c16/c17/c18/c19/c20/c21/c22/c23/c24/c25/c26/c27"
        "/c28/c29/c30/c31",
        expectPathNum, (const char**)expectPath01, data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 005.新增接口的路径参数内容包含跨表48层嵌套，实际存在引用关系，按照实际情况返回引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 建表
    TestCreateLabel3248(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3248", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_3248_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置32层嵌套数据
    for (uint32_t i = 1; i <= 31; i++) {
        char conName[128] = {0};
        (void)sprintf(conName, "c%d", i);
        ret = GmcYangEditChildNode(g_childNode[i], (const char *)conName, GMC_OPERATION_INSERT, &g_childNode[i+1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 1;
        TestYangSetNodePropertyBasic(g_childNode[i+1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[11], "list_3248_1_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[11], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置16层嵌套数据
    for (uint32_t i = 1; i <= 14; i++) {
        char conName[128] = {0};
        (void)sprintf(conName, "c%d", i);
        ret = GmcYangEditChildNode(g_childNode[i], (const char *)conName, GMC_OPERATION_INSERT, &g_childNode[i+1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 1;
        TestYangSetNodePropertyBasic(g_childNode[i+1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_3248_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置32层嵌套数据
    for (uint32_t i = 1; i <= 31; i++) {
        char conName[128] = {0};
        (void)sprintf(conName, "c%d", i);
        ret = GmcYangEditChildNode(g_childNode[i], (const char *)conName, GMC_OPERATION_INSERT, &g_childNode[i+1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 1;
        TestYangSetNodePropertyBasic(g_childNode[i+1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[22], "list_3248_2_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[22]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[22], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置16层嵌套数据
    for (uint32_t i = 1; i <= 14; i++) {
        char conName[128] = {0};
        (void)sprintf(conName, "c%d", i);
        ret = GmcYangEditChildNode(g_childNode[i], (const char *)conName, GMC_OPERATION_INSERT, &g_childNode[i+1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = 1;
        TestYangSetNodePropertyBasic(g_childNode[i+1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    }

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[22]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_3248/list_3248_2[PK=1]"
    };
    data.leafrefPathes.num = expectPathNum;
    data.leafrefPathes.paths = expectPath01;
    TestGetLeafrefPath(g_stmt_root,
        "/root_3248/list_3248_1[PK=1]/c1/c2/c3/c4/c5/c6/c7/c8/c9/c10/c11/c12"
        "/c13/c14/c15/c16/c17/c18/c19/c20/c21/c22/c23/c24/c25/c26/c27"
        "/c28/c29/c30/c31/list_3248_1_1[PK=1]/c1/c2/c3/c4/c5/c6/c7/c8"
        "/c9/c10/c11/c12/c13/c14",
        expectPathNum, (const char**)expectPath01, data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 006.新增接口的路径参数内容为存在的路径，但无节点引用该路径
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 0;
    data.leafrefPathes.num = expectPathNum;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=100]", expectPathNum, NULL, data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 007.require-instance设置为其他值，建表报错
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_01.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_DATATYPE_MISMATCH);
}

/*****************************************************************************
 Description  : 008.when条件下设置require-instance，建表报错
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_02.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
}

/*****************************************************************************
 Description  : 009.must条件下设置require-instance，建表报错
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;
    char *vLabelSchema = NULL;

    readJanssonFile("./schema_file/yang_fail_03.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vLabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, data.status);
    free(vLabelSchema);
    vLabelSchema = NULL;
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
}

/*****************************************************************************
 Description  : 010.require-instance设置为true，写入数据符合leafref条件，校验成功，
                修改数据不符合leafref条件，校验返回errorPath
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_re_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改数据
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_re_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue);
    fieldValue = 200;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_re_1/list_re_2[PK=100]/F0",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 011.require-instance设置为false，写入数据符合leafref条件，校验成功，
                修改数据不符合leafref条件，校验成功
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire02(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_re_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改数据
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_2", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_re_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue);
    fieldValue = 200;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 012.require-instance设置为false，普通字段的默认值不符合leafref校验条件，校验成功
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire02(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 013.require-instance设置为false，leaflist的默认值不符合leafref校验条件，校验成功
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire02(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 014.同一字段下有多条校验条件，require-instance设置为false，
                写入不符合must和leafref条件的数据，校验返回must失败的errorPath，
                修改数据符合must校验，校验成功
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire03(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_MUST,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated must clause 0",
        .expectedErrPath = "/root_re_3/F1",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_3", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 015.同一字段下有多条校验条件，require-instance设置为false，
                写入不符合when增量和leafref条件的数据，校验返回when失败的errorPath，
                修改数据符合when校验，校验成功
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire03(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_WHEN,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated when clause 0",
        .expectedErrPath = "/root_re_3/F3",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_3", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 016.同一字段下有多条校验条件，require-instance设置为false，
                写入不符合when全量和leafref条件的数据，校验删除数据，
                重新写入数据符合when校验，校验成功
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire03(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true, GMERR_OK, GMERR_OK, GMC_YANG_VALIDATION_ALL_FORCE);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_3", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值, F3之前的校验被删除，重新create
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F3",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 017.表中有多个leafref语句，require-instance设置成true或false，
                都写入不符合条件的数据，校验返回require-instance设置为true的errorPath
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire04(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 200;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_re_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_re_4/list_re_2[PK=100]/F0",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 018.表中有多个leafref语句，require-instance设置成false，
                都写入不符合条件的数据，校验返回成功
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire02(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 200;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_re_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 019.root下的字段被多个节点下的字段引用，包括require-instance设置成false的，
                通过接口查询引用关系，返回空路径，root-F1被root-list-F1引用
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire02(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 200;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_re_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_re_2", expectPathNum, NULL, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 020.list下的字段被多个节点下的字段引用，包括require-instance设置成false的，
                指定主键，通过接口查询引用关系，自动建索引
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]",
        "/root_1/list_3[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 2;
    const char *expectPath02[expectPathNum] =
    {
        "/root_1/list_2[PK=3]",
        "/root_1/list_3[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, (const char**)expectPath02, data);

    expectPathNum = 2;
    const char *expectPath03[expectPathNum] =
    {
        "/root_1/list_2[PK=4]",
        "/root_1/list_3[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]", expectPathNum, (const char**)expectPath03, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 022.list下的字段被多个节点下的字段引用，通过接口查询引用关系，
                增加部分引用的数据，再次通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]",
        "/root_1/list_3[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 2;
    const char *expectPath02[expectPathNum] =
    {
        "/root_1/list_2[PK=3]",
        "/root_1/list_3[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, (const char**)expectPath02, data);

    expectPathNum = 2;
    const char *expectPath03[expectPathNum] =
    {
        "/root_1/list_2[PK=4]",
        "/root_1/list_3[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]", expectPathNum, (const char**)expectPath03, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 增加数据
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 10; i < 15; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 10; i < 15; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 10; i < 15; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 2;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, (const char**)expectPath02, data);

    expectPathNum = 2;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]", expectPathNum, (const char**)expectPath03, data);

    expectPathNum = 2;
    const char *expectPath04[expectPathNum] =
    {
        "/root_1/list_2[PK=11]",
        "/root_1/list_3[PK=11]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=11]", expectPathNum, (const char**)expectPath04, data);

    expectPathNum = 2;
    const char *expectPath05[expectPathNum] =
    {
        "/root_1/list_2[PK=13]",
        "/root_1/list_3[PK=13]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=13]", expectPathNum, (const char**)expectPath05, data);

    expectPathNum = 2;
    const char *expectPath06[expectPathNum] =
    {
        "/root_1/list_2[PK=14]",
        "/root_1/list_3[PK=14]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=14]", expectPathNum, (const char**)expectPath06, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 023.list下的字段被多个节点下的字段引用，通过接口查询引用关系，
                删除部分引用的数据，再次通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]",
        "/root_1/list_3[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 2;
    const char *expectPath02[expectPathNum] =
    {
        "/root_1/list_2[PK=3]",
        "/root_1/list_3[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, (const char**)expectPath02, data);

    expectPathNum = 2;
    const char *expectPath03[expectPathNum] =
    {
        "/root_1/list_2[PK=4]",
        "/root_1/list_3[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]", expectPathNum, (const char**)expectPath03, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 1; i <= 3; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestSetKeyNameAndValue(g_stmt_list[1], fieldValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestSetKeyNameAndValue(g_stmt_list[2], fieldValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i <= 3; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestSetKeyNameAndValue(g_stmt_list[3], fieldValue);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, NULL, data);

    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, NULL, data);

    expectPathNum = 2;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]", expectPathNum, (const char**)expectPath03, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 024.list下的字段被多个节点下的字段引用，包括require-instance设置成false的，
                list下被引用的字段无默认值且不写入值，即入参对象不存在，通过接口查询引用关系，
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, NULL, data);
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, NULL, data);
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]", expectPathNum, NULL, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 025.list下的字段被多个节点下的字段引用，包括require-instance设置成false的，
                只写被引用的字段的值，引用的字段无默认值且不写入值，即查询结果对象不存在，
                通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(6, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(6, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, NULL, data);
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, NULL, data);
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]", expectPathNum, NULL, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 026.list下的字段被多个节点下的字段引用，包括require-instance设置成false的，
                list下被引用的字段有默认值，且默认值符合引用条件，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel02(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值, 不写其他字段的值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_2/list_2[PK=100]",
        "/root_2/list_3[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_2/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 027.list下的字段被多个节点下的字段引用，包括require-instance设置成false的，
                只写被引用的字段的值，引用的字段有默认值，且默认值符合引用条件，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel03(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值, 不写其他字段的值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值, 不写其他字段的值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_3/list_2[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_3/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 028.存在级联引用关系，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel09(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_9", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_9/list_2[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_9/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 1;
    const char *expectPath02[expectPathNum] =
    {
        "/root_9/list_2[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_9/list_1[PK=3]", expectPathNum, (const char**)expectPath02, data);

    expectPathNum = 1;
    const char *expectPath03[expectPathNum] =
    {
        "/root_9/list_2[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_9/list_1[PK=4]", expectPathNum, (const char**)expectPath03, data);

    expectPathNum = 1;
    const char *expectPath04[expectPathNum] =
    {
        "/root_9/list_3[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_9/list_2[PK=1]", expectPathNum, (const char**)expectPath04, data);

    expectPathNum = 1;
    const char *expectPath05[expectPathNum] =
    {
        "/root_9/list_3[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_9/list_2[PK=3]", expectPathNum, (const char**)expectPath05, data);

    expectPathNum = 1;
    const char *expectPath06[expectPathNum] =
    {
        "/root_9/list_3[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_9/list_2[PK=4]", expectPathNum, (const char**)expectPath06, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 029.list的下层list_1节点被其他list_2的字段引用，通过list和list_1都能查询到引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel04(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // list_1_1被list_2_2引用
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < 3; j++) {
        // list_1_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[11], "list_1_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[11]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[11], &g_childNode[11]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = j;
        TestYangSetNodeProperty_PK(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = j;
        TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[11]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_2_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "con_2_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t j = 0; j < 3; j++) {
        // list_2_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[22], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[22]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[22], &g_childNode[22]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = j;
        TestYangSetNodeProperty_PK(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = j;
        TestYangSetNodePropertyBasic(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[22]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(9, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(9, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    // 查询list_1
    expectPathNum = 3;
    const char *expectPath01[expectPathNum] =
    {
        "/root_4/list_2[PK=1]/con_2_2/con_2_2_1/list_2_2[PK=0]",
        "/root_4/list_2[PK=1]/con_2_2/con_2_2_1/list_2_2[PK=1]",
        "/root_4/list_2[PK=1]/con_2_2/con_2_2_1/list_2_2[PK=2]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 1;
    const char *expectPath02[expectPathNum] =
    {
        "/root_4/list_2[PK=1]/con_2_2/con_2_2_1/list_2_2[PK=0]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]/con_1_1/list_1_1[PK=0]", expectPathNum,
        (const char**)expectPath02, data);

    expectPathNum = 1;
    const char *expectPath03[expectPathNum] =
    {
        "/root_4/list_2[PK=1]/con_2_2/con_2_2_1/list_2_2[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]/con_1_1/list_1_1[PK=1]", expectPathNum,
        (const char**)expectPath03, data);

    expectPathNum = 1;
    const char *expectPath04[expectPathNum] =
    {
        "/root_4/list_2[PK=1]/con_2_2/con_2_2_1/list_2_2[PK=2]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]/con_1_1/list_1_1[PK=2]", expectPathNum,
        (const char**)expectPath04, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 030.list--list_1--list_2，list_1被list_3引用，list_2被list_4引用，
                分别通过list、list_1、list_2查询引用关系，根据子树关系返回能查询到的引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel04(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // list_1_1被list_2_2引用,list_1_1_1被list_3引用
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[11], "list_1_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[11], &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t k = 0; k < 3; k++) {
        // list_1_1_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[21], "list_1_1_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[11], g_stmt_list[21]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[21], &g_childNode[21]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = k;
        TestYangSetNodeProperty_PK(g_childNode[21], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = k;
        TestYangSetNodePropertyBasic(g_childNode[21], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[21]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_2_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "con_2_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[22], "list_2_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[22]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[22], &g_childNode[22]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[22]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[3], "choice_3", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_childNode[4], "case_3", GMC_OPERATION_INSERT, &g_childNode[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[5], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 4;
    const char *expectPath01[expectPathNum] =
    {
        "/root_4/list_2[PK=1]/con_2_2/con_2_2_1/list_2_2[PK=1]",
        "/root_4/list_3[PK=0]",
        "/root_4/list_3[PK=1]",
        "/root_4/list_3[PK=2]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);

    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]/con_1_1/list_1_1[PK=1]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 1;
    const char *expectPath02[expectPathNum] =
    {
        "/root_4/list_3[PK=0]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]/con_1_1/list_1_1[PK=1]/list_1_1_1[PK=0]",
        expectPathNum, (const char**)expectPath02, data);

    expectPathNum = 1;
    const char *expectPath03[expectPathNum] =
    {
        "/root_4/list_3[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]/con_1_1/list_1_1[PK=1]/list_1_1_1[PK=1]",
        expectPathNum, (const char**)expectPath03, data);

    expectPathNum = 1;
    const char *expectPath04[expectPathNum] =
    {
        "/root_4/list_3[PK=2]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]/con_1_1/list_1_1[PK=1]/list_1_1_1[PK=2]",
        expectPathNum, (const char**)expectPath04, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_4", "Yang_096_Func_030_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 031.list的字段被list_1和下层的list_2同时引用，通过查询返回list_1的路径
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel05(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_5", "Yang_096_Func_031_01");

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_5", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[2], "con_2_2", GMC_OPERATION_INSERT, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_childNode[3], "con_2_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // list_2_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[22], "list_2_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[22]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[22], &g_childNode[22]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[22]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_5/list_2[PK=0]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_5/list_1[PK=0]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 1;
    const char *expectPath02[expectPathNum] =
    {
        "/root_5/list_2[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_5/list_1[PK=1]", expectPathNum, (const char**)expectPath02, data);

    expectPathNum = 1;
    const char *expectPath03[expectPathNum] =
    {
        "/root_5/list_2[PK=2]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_5/list_1[PK=2]", expectPathNum, (const char**)expectPath03, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_5", "Yang_096_Func_031_02");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 032.list的字段被其下层多个字段引用，通过查询返回空路径
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel06(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[11]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[12]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[12], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcYangEditChildNode(g_childNode[12], "con_1_2_1", GMC_OPERATION_INSERT, &g_childNode[13]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[13], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "choice_1", GMC_OPERATION_INSERT, &g_childNode[14]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_childNode[14], "case_1", GMC_OPERATION_INSERT, &g_childNode[15]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[15], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_6/list_1[PK=0]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_6/list_1[PK=1]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_6/list_1[PK=2]", expectPathNum, NULL, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 033.list的字段被相同主键的同层多个字段引用，通过查询返回空路径
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel06(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_6/list_1[PK=0]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_6/list_1[PK=1]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_6/list_1[PK=2]", expectPathNum, NULL, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 034.list的字段被不同主键的同层多个字段引用，通过查询返回实际引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel07(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = 100;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_7/list_1[PK=0]",
        "/root_7/list_1[PK=2]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_7/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_7/list_1[PK=0]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_7/list_1[PK=2]", expectPathNum, NULL, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 038.list的下层list_1节点被其他list_2的字段引用，list_1的数据中存在多个符合引用关系的数据，查询结果返回空
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel04(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // list_1_1被list_2_2引用
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[1], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[11]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (uint32_t j = 0; j < 3; j++) {
            // list_1_1
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[11], "list_1_1", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[11]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[11], &g_childNode[11]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 设置属性值
            fieldValue = j;
            TestYangSetNodeProperty_PK(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            fieldValue = j;
            TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_list[11]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    for (uint32_t i = 0; i < 3; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        ret = GmcYangEditChildNode(g_childNode[2], "con_2_2", GMC_OPERATION_INSERT, &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(g_childNode[3], "con_2_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (uint32_t j = 0; j < 3; j++) {
            // list_2_2
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[22], "list_2_2", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[22]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[22], &g_childNode[22]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 设置属性值
            fieldValue = j;
            TestYangSetNodeProperty_PK(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            fieldValue = j;
            TestYangSetNodePropertyBasic(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_list[22]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(25, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(25, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    // 查询list_1
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=0]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=2]", expectPathNum, NULL, data);

    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=0]/con_1_1/list_1_1[PK=0]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]/con_1_1/list_1_1[PK=0]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=2]/con_1_1/list_1_1[PK=0]", expectPathNum, NULL, data);

    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=0]/con_1_1/list_1_1[PK=1]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]/con_1_1/list_1_1[PK=1]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=2]/con_1_1/list_1_1[PK=1]", expectPathNum, NULL, data);

    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=0]/con_1_1/list_1_1[PK=2]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=1]/con_1_1/list_1_1[PK=2]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_4/list_1[PK=2]/con_1_1/list_1_1[PK=2]", expectPathNum, NULL, data);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_4", "Yang_096_Func_038_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 039.GmcYangGetLeafrefPathOptionsCreate接口为空指针
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 接口测试
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=100]"
    };
    data.leafrefPathes.num = expectPathNum;
    data.leafrefPathes.paths = expectPath01;

    GmcGetLeafrefPathOptionsT *leafrefOption = NULL;
    ret = GmcYangGetLeafrefPathOptionsCreate(GMC_LEAFREF_GET_PATH_ALL, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_1/list_1[PK=100]", NULL, GetPath_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 040.GmcYangGetLeafrefPathOptionsCreate接口输入枚举值超过枚举值范围
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 接口测试
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=100]"
    };
    data.leafrefPathes.num = expectPathNum;
    data.leafrefPathes.paths = expectPath01;

    GmcGetLeafrefPathOptionsT *leafrefOption = NULL;
    ret = GmcYangGetLeafrefPathOptionsCreate(GMC_LEAFREF_GET_PATH_BUTT, &leafrefOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_1/list_1[PK=100]", NULL, GetPath_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 041.leaflist下存在leafref条件，require-instance设置为false，写入数据符合leafref条件，
                校验成功，修改数据不符合leafref条件，校验成功
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // leaflist_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_re_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改数据为不符合校验条件
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 042.leaflist下存在leafref条件，require-instance设置为false，
                写入多条数据，部分数据符合leafref条件，部分不符合，校验成功
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    ret = TestYangSetField(g_rootNode, GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // leaflist_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_re_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // leaflist_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_re_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // leaflist_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_re_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 300;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 043.NP节点下的字段存在默认值，默认值有leafref条件，符合leafref条件，不写NP节点，查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_2[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 044.NP节点下的字段存在默认值，默认值有leafref条件，符合leafref条件，
                写NP节点，修改字段值为不符合leafref条件，xpath校验前后都查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 修改默认值为不符合校验的值
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_10/list_2[PK=100]/con_1_1/F1",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 045.P节点下的字段存在默认值，默认值有leafref条件，符合leafref条件，不写P节点，查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 046.P节点下的字段存在默认值，默认值有leafref条件，符合leafref条件，
                写P节点，修改字段值为不符合leafref条件，xpath校验前后都查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 修改默认值为不符合校验的值
    ret = GmcYangEditChildNode(g_childNode[2], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_10/list_4[PK=100]/con_1_2/F1",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 047.存在默认case，默认值有leafref条件，符合leafref条件，不写case节点，查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_5
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_5", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_6
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_6[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_5[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_5[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_5[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_5[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 048.存在默认case，默认值有leafref条件，符合leafref条件，写默认case节点，
                修改字段值为不符合leafref条件，xpath校验前后都查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_5
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_5", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_6
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 修改默认值为不符合校验的值
    ret = GmcYangEditChildNode(g_childNode[2], "choice_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_1_2", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 200;
    TestYangSetNodePropertyBasic(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_5[PK=100]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_5[PK=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_10/list_6[PK=100]/choice_1/case_1_2/F1",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_5[PK=100]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_5[PK=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 049.choice下有多个case，leafref条件引用的字段相同，分别写入不同的case，查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[1], "choice_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[11], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[12], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[2], "choice_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_2[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入另一个case
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestSetKeyNameAndValue(g_stmt_list[2], fieldValue);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[2], "choice_2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_2", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 050.choice下有多个case，leafref条件引用的字段不同，分别写入不同的case，查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_7
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[11], "case_3_1", GMC_OPERATION_INSERT, &g_childNode[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[12], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_7
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_7", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 200;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[1], "choice_3", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[11], "case_3_1", GMC_OPERATION_INSERT, &g_childNode[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[12], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_8
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_8", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[2], "choice_3", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_3_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_8[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=200]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=200]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 1;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=200]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=200]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入另一个case
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_8
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_8", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestSetKeyNameAndValue(g_stmt_list[2], fieldValue);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[2], "choice_3", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_3_2", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=200]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=200]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=100]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 1;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=200]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=200]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=100]", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_7[PK=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_10", "Yang_096_Func_050_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 051.list下的主键被多个节点下的字段引用，包括require-instance设置成false的，
                指定主键，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel11(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_11/list_2[PK=0]",
        "/root_11/list_3[PK=0]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_11/list_1[PK=0]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 2;
    const char *expectPath02[expectPathNum] =
    {
        "/root_11/list_2[PK=1]",
        "/root_11/list_3[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_11/list_1[PK=1]", expectPathNum, (const char**)expectPath02, data);

    expectPathNum = 2;
    const char *expectPath03[expectPathNum] =
    {
        "/root_11/list_2[PK=2]",
        "/root_11/list_3[PK=2]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_11/list_1[PK=2]", expectPathNum, (const char**)expectPath03, data);

    // 只返回1个
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/list_1[PK=0]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/list_1[PK=1]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/list_1[PK=2]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 052.leaflist下的主键被多个节点下的字段引用，包括require-instance设置成false的，
                指定主键，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel11(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    // leaflist的结果只到leaflist的上一级
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_11"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=0]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=1]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=2]", expectPathNum, (const char**)expectPath01, data);

    // 只返回1个
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=0]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=1]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=2]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_11", "Yang_096_Func_052_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 053.leaflist下的主键默认值被多个节点下的字段引用，包括require-instance设置成false的，
                指定主键，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel11(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_5
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_5", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        if (i == 0) {
            fieldValue = 555;
        } else if (i == 1) {
            fieldValue = 666;
        } else {
            fieldValue = 777;
        }
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    // leaflist的结果只到leaflist的上一级
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_11"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_4[PK=555]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_4[PK=666]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_4[PK=777]", expectPathNum, (const char**)expectPath01, data);

    // 只返回1个
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_4[PK=555]", expectPathNum, NULL,
        data, GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_4[PK=666]", expectPathNum, NULL,
        data, GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_4[PK=777]", expectPathNum, NULL,
        data, GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // subtree查询
    TestSubtreeFilterObjAll(g_stmt_async, "root_11", "Yang_096_Func_053_01");

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 054.查询条件不指定谓词，被引用者只插入1条数据，数据符合leafref条件，
                数据校验前后都通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel12(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_12", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_12/list_2[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 055.查询条件不指定谓词，被引用者只插入1条数据，数据不符合leafref条件，
                数据校验前后都通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel12(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_12", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 200;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_12/list_2[PK=100]/F1",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 056.查询条件不指定谓词，引用者插入多条数据，只有1条数据符合leafref条件，
                数据校验前后都通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel12(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_12", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100 + i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        if (i == 2) {
            fieldValue = 100;
        } else {
            fieldValue = 200;
        }
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_12/list_2[PK=102]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_12/list_2[PK=100]/F1",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 057.查询条件不指定谓词，引用者插入多条数据，多条数据符合leafref条件，
                数据校验前后都通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel12(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_12", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = 100 + i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        if (i != 2) {
            fieldValue = 100;
        } else {
            fieldValue = 200;
        }
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 4;
    const char *expectPath01[expectPathNum] =
    {
        "/root_12/list_2[PK=100]",
        "/root_12/list_2[PK=101]",
        "/root_12/list_2[PK=103]",
        "/root_12/list_2[PK=104]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_12/list_1", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_12/list_2[PK=102]/F1",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取引用路径
    expectPathNum = 4;
    TestGetLeafrefPath(g_stmt_root, "/root_12/list_1", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_12/list_1", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 058.查询条件指定谓词为identity类型的主键，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;
    int32_t idValue;

    // 创建表
    TestCreateLabel13(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_13", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    idValue = 1;
    TestYangSetIDValue(g_childNode[1], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 1;
    TestYangSetIDValue(g_childNode[1], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    idValue = 1;
    TestYangSetIDValue(g_childNode[2], "PKID1", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    idValue = 1;
    TestYangSetIDValue(g_childNode[2], "ID2", idValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_13/list_2[PKID1=\"level1\"]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_13/list_1[PKID1='level1']", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_13/list_1[PKID1='level1']", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_13/list_1[PKID1='level1']", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_13/list_1[PKID1='level1']", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 059.查询条件指定谓词为非主键的字段值--list下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]",
        "/root_1/list_3[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[F0=1]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 2;
    const char *expectPath02[expectPathNum] =
    {
        "/root_1/list_2[PK=3]",
        "/root_1/list_3[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[F0=3]", expectPathNum, (const char**)expectPath02, data);

    expectPathNum = 2;
    const char *expectPath03[expectPathNum] =
    {
        "/root_1/list_2[PK=4]",
        "/root_1/list_3[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[F0=4]", expectPathNum, (const char**)expectPath03, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 060.查询条件指定谓词为非主键的字段值--leaflist下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel11(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    // leaflist的结果只到leaflist的上一级
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_11"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=0]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=1]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=2]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1", expectPathNum, (const char**)expectPath01, data);

    // 只返回1个
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=0]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=1]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=2]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 061.查询条件指定谓词为非主键的字段值--P节点下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_4[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[con_1_2/F0=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[con_1_2/F0=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[con_1_2/F0=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[con_1_2/F0=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 062.查询条件指定谓词为非主键的字段值--NP节点下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_2[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[con_1_1/F0=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[con_1_1/F0=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[con_1_1/F0=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[con_1_1/F0=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 063.查询条件指定谓词为非主键的字段值--case下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[1], "choice_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[11], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[12], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[2], "choice_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_2[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[choice_2/case_2_1/F0=100]", expectPathNum,
        (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[choice_2/case_2_1/F0=100]", expectPathNum,
        (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[choice_2/case_2_1/F0=100]", expectPathNum,
        (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[choice_2/case_2_1/F0=100]", expectPathNum,
        (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 064.查询条件指定到字段--list下主键，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]/PK", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]/PK", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]/PK", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 10;
    const char *expectPath04[expectPathNum] =
    {
        "/root_1/list_2[PK=0]",
        "/root_1/list_2[PK=1]",
        "/root_1/list_2[PK=2]",
        "/root_1/list_2[PK=3]",
        "/root_1/list_2[PK=4]",
        "/root_1/list_3[PK=0]",
        "/root_1/list_3[PK=1]",
        "/root_1/list_3[PK=2]",
        "/root_1/list_3[PK=3]",
        "/root_1/list_3[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1", expectPathNum, (const char**)expectPath04, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 065.查询条件指定到字段--List下的leaf(属性)，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]",
        "/root_1/list_3[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]/F0", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[PK=1]/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 2;
    const char *expectPath02[expectPathNum] =
    {
        "/root_1/list_2[PK=3]",
        "/root_1/list_3[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]/F0", expectPathNum, (const char**)expectPath02, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[PK=3]/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 2;
    const char *expectPath03[expectPathNum] =
    {
        "/root_1/list_2[PK=4]",
        "/root_1/list_3[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]/F0", expectPathNum, (const char**)expectPath03, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[PK=4]/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 066.查询条件指定到字段--leafList下leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel11(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    // leaflist的结果只到leaflist的上一级
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_11"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=0]/PK", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=1]/PK", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=2]/PK", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1", expectPathNum, (const char**)expectPath01, data);

    // 只返回1个
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=0]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=1]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=2]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 067.查询条件指定到字段--P节点下leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_4[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]/con_1_2/F0", expectPathNum,
        (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]/con_1_2/F0", expectPathNum,
        (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]/con_1_2/F0", expectPathNum,
        (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]/con_1_2/F0", expectPathNum,
        (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 068.查询条件指定到字段--NP节点下leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_2[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/con_1_1/F0", expectPathNum,
        (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/con_1_1/F0", expectPathNum,
        (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/con_1_1/F0", expectPathNum,
        (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/con_1_1/F0", expectPathNum,
        (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 069.查询条件指定到字段--case下leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[1], "choice_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[11], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[12], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[2], "choice_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_2[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/choice_2/case_2_1/F0", expectPathNum,
        (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/choice_2/case_2_1/F0", expectPathNum,
        (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/choice_2/case_2_1/F0", expectPathNum,
        (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/choice_2/case_2_1/F0", expectPathNum,
        (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 070.查询条件指定到字段--根节点下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel14(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_14", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_14"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_14/F0", expectPathNum,
        (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_14/F0", expectPathNum,
        (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_14/F0", expectPathNum,
        (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_14/F0", expectPathNum,
        (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 071.list1[PK=1,2,3]，分别被list2[PK=1,2,3]引用，指定主键和不指定主键查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 1; i < 4; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 1; i < 4; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(7, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(7, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 1;
    const char *expectPath02[expectPathNum] =
    {
        "/root_1/list_2[PK=2]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=2]", expectPathNum, (const char**)expectPath02, data);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=2]", expectPathNum, (const char**)expectPath02, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 1;
    const char *expectPath03[expectPathNum] =
    {
        "/root_1/list_2[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, (const char**)expectPath03, data);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, (const char**)expectPath03, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 3;
    const char *expectPath04[expectPathNum] =
    {
        "/root_1/list_2[PK=1]",
        "/root_1/list_2[PK=2]",
        "/root_1/list_2[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1", expectPathNum, (const char**)expectPath04, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 072.root引用子树的字段，包括require-instance设置成false的，通过接口查询引用关系，
                返回实际引用关系，root-list-F1被root-F1引用
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel15(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_15", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_15"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_15/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_15/list_1[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 073.存在多条引用关系，分别使用不同的枚举值查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]",
        "/root_1/list_3[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 2;
    const char *expectPath02[expectPathNum] =
    {
        "/root_1/list_2[PK=3]",
        "/root_1/list_3[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, (const char**)expectPath02, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 2;
    const char *expectPath03[expectPathNum] =
    {
        "/root_1/list_2[PK=4]",
        "/root_1/list_3[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]", expectPathNum, (const char**)expectPath03, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[PK=4]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 076.存在跨树的leafref引用，require-instance设置为false，
                写入数据符合leafref条件，校验成功，修改数据不符合leafref条件，校验成功
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabelRequire01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_re_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_1_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_rootNode, "con_1", GMC_OPERATION_INSERT, &g_childNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_re_1_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_re_2_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // leaflist_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_re_1_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(4, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(4, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 修改数据
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_re_1", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_re_1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestSetKeyNameAndValue(g_stmt_list[1], fieldValue);
    fieldValue = 200;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    std::atomic_uint32_t step{0};
    GmcValidateResT validateRes {.validateRes = false, .failCount = 1};
    ValidateParam param = {
        .step = &step,
        .exceptStatus = GMERR_OK,
        .validateRes = validateRes,
        .isValidateErrorPath = true,
        .expectedErrCode = GMC_VIOLATES_LEAFREF,
        .expectedErrClauseIndex = 0,
        .expectedErrMsg = "violated leaf-ref clause 0",
        .expectedErrPath = "/root_re_1_k/list_re_2_k[PK=100]/F0",
    };
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_ALL, .cfgJson = NULL};
    ret = GmcYangValidateAsync(g_stmt_root, &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 077.存在跨树的leafref引用，查询条件指定谓词为非主键的字段值--list下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 4;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]",
        "/root_1/list_3[PK=1]",
        "/root_1_k/list_2_k[PK=1]",
        "/root_1_k/list_3_k[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[F0=1]", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[F0=1]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 4;
    const char *expectPath02[expectPathNum] =
    {
        "/root_1/list_2[PK=3]",
        "/root_1/list_3[PK=3]",
        "/root_1_k/list_2_k[PK=3]",
        "/root_1_k/list_3_k[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[F0=3]", expectPathNum, (const char**)expectPath02, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[F0=3]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 4;
    const char *expectPath03[expectPathNum] =
    {
        "/root_1/list_2[PK=4]",
        "/root_1/list_3[PK=4]",
        "/root_1_k/list_2_k[PK=4]",
        "/root_1_k/list_3_k[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[F0=4]", expectPathNum, (const char**)expectPath03, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[F0=4]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 078.存在跨树的leafref引用，查询条件指定谓词为非主键的字段值--leaflist下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel11(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_11_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_2_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_3_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    // leaflist的结果只到leaflist的上一级
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_11",
        "/root_11_k"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=0]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=1]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=2]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1", expectPathNum, (const char**)expectPath01, data);

    // 只返回1个
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=0]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=1]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=2]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 079.存在跨树的leafref引用，查询条件指定谓词为非主键的字段值--P节点下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_3_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_4_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_4[PK=100]",
        "/root_10_k/list_4_k[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[con_1_2/F0=100]", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_3[con_1_2/F0=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[con_1_2/F0=100]", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_3[con_1_2/F0=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 080.存在跨树的leafref引用，查询条件指定谓词为非主键的字段值--NP节点下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_2[PK=100]",
        "/root_10_k/list_2_k[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[con_1_1/F0=100]", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_1[con_1_1/F0=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[con_1_1/F0=100]", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_1[con_1_1/F0=100]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 081.存在跨树的leafref引用，查询条件指定谓词为非主键的字段值--case下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[1], "choice_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[11], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[12], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[2], "choice_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[1], "choice_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[11], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[12], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[2], "choice_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_2[PK=100]",
        "/root_10_k/list_2_k[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[choice_2/case_2_1/F0=100]", expectPathNum,
        (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_1[choice_2/case_2_1/F0=100]", expectPathNum,
        NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[choice_2/case_2_1/F0=100]", expectPathNum,
        (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_1[choice_2/case_2_1/F0=100]", expectPathNum,
        NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 082.存在跨树的leafref引用，查询条件指定到字段--list下主键，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 0;
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]/PK", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]/PK", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]/PK", expectPathNum, NULL, data);
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 获取引用路径
    expectPathNum = 4;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]",
        "/root_1/list_3[PK=1]",
        "/root_1_k/list_2_k[PK=1]",
        "/root_1_k/list_3_k[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 20;
    const char *expectPath04[expectPathNum] =
    {
        "/root_1/list_2[PK=0]",
        "/root_1/list_2[PK=1]",
        "/root_1/list_2[PK=2]",
        "/root_1/list_2[PK=3]",
        "/root_1/list_2[PK=4]",
        "/root_1/list_3[PK=0]",
        "/root_1/list_3[PK=1]",
        "/root_1/list_3[PK=2]",
        "/root_1/list_3[PK=3]",
        "/root_1/list_3[PK=4]",
        "/root_1_k/list_2_k[PK=0]",
        "/root_1_k/list_2_k[PK=1]",
        "/root_1_k/list_2_k[PK=2]",
        "/root_1_k/list_2_k[PK=3]",
        "/root_1_k/list_2_k[PK=4]",
        "/root_1_k/list_3_k[PK=0]",
        "/root_1_k/list_3_k[PK=1]",
        "/root_1_k/list_3_k[PK=2]",
        "/root_1_k/list_3_k[PK=3]",
        "/root_1_k/list_3_k[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1", expectPathNum, (const char**)expectPath04, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 083.存在跨树的leafref引用，查询条件指定到字段--List下的leaf(属性)，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 4;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]",
        "/root_1/list_3[PK=1]",
        "/root_1_k/list_2_k[PK=1]",
        "/root_1_k/list_3_k[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]/F0", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[PK=1]/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 4;
    const char *expectPath02[expectPathNum] =
    {
        "/root_1/list_2[PK=3]",
        "/root_1/list_3[PK=3]",
        "/root_1_k/list_2_k[PK=3]",
        "/root_1_k/list_3_k[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]/F0", expectPathNum, (const char**)expectPath02, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[PK=3]/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    expectPathNum = 4;
    const char *expectPath03[expectPathNum] =
    {
        "/root_1/list_2[PK=4]",
        "/root_1/list_3[PK=4]",
        "/root_1_k/list_2_k[PK=4]",
        "/root_1_k/list_3_k[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]/F0", expectPathNum, (const char**)expectPath03, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_1/list_1[PK=4]/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 084.存在跨树的leafref引用，查询条件指定到字段--leafList下leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel11(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_11", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_11_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "leaflist_1_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "leaflist_2_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 3; i++) {
        // leaflist_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "leaflist_3_k", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(10, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(10, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    // leaflist的结果只到leaflist的上一级
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_11",
        "/root_11_k"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=0]/PK", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=1]/PK", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1[PK=2]/PK", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_11/leaflist_1", expectPathNum, (const char**)expectPath01, data);

    // 只返回1个
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=0]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=1]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_11/leaflist_1[PK=2]/PK", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 085.存在跨树的leafref引用，查询条件指定到字段--P节点下leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_3", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_4", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_3
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_3_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_4
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_4_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_4[PK=100]",
        "/root_10_k/list_4_k[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]/con_1_2/F0", expectPathNum,
        (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_3[PK=100]/con_1_2/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_3[PK=100]/con_1_2/F0", expectPathNum,
        (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_3[PK=100]/con_1_2/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 086.存在跨树的leafref引用，查询条件指定到字段--NP节点下leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "con_1_1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_2[PK=100]",
        "/root_10_k/list_2_k[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/con_1_1/F0", expectPathNum,
        (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_1[PK=100]/con_1_1/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/con_1_1/F0", expectPathNum,
        (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_1[PK=100]/con_1_1/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 087.存在跨树的leafref引用，查询条件指定到字段--case下leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[1], "choice_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[11], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[12], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[2], "choice_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[1], "choice_2", GMC_OPERATION_INSERT, &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[11], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[12], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_2
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置属性值
    ret = GmcYangEditChildNode(g_childNode[2], "choice_2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[3], "case_2_1", GMC_OPERATION_INSERT, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[4], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10/list_2[PK=100]",
        "/root_10_k/list_2_k[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/choice_2/case_2_1/F0", expectPathNum,
        (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_1[PK=100]/choice_2/case_2_1/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    TestGetLeafrefPath(g_stmt_root, "/root_10/list_1[PK=100]/choice_2/case_2_1/F0", expectPathNum,
        (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_10/list_1[PK=100]/choice_2/case_2_1/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 088.存在跨树的leafref引用，查询条件指定到字段--根节点下的leaf，通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel14(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_14", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 跨树引用
    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_14_k", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_14",
        "/root_14_k"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_14/F0", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_14/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    TestGetLeafrefPath(g_stmt_root, "/root_14/F0", expectPathNum, (const char**)expectPath01, data);
    expectPathNum = 1;
    TestGetLeafrefPathOnlyNum(g_stmt_root, "/root_14/F0", expectPathNum, NULL, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


/*****************************************************************************
 Description  : 089.问题单DTS2025031251578补充用例,choice-case-leaflist,
                choice上有mandatory校验，写入数据后校验, 并通过接口查询引用关系
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test, Yang_096_Func_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel10_m(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_10_m", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_5
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_5", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[1], "choice_1", GMC_OPERATION_INSERT, &g_childNode[21]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[21], "case_1_2", GMC_OPERATION_INSERT, &g_childNode[22]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // leaflist_5
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[11], "leaflist_5", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[1], g_stmt_list[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[11], &g_childNode[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[11], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[11]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_6
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    ret = GmcYangEditChildNode(g_childNode[2], "choice_1", GMC_OPERATION_INSERT, &g_childNode[21]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[21], "case_1_2", GMC_OPERATION_INSERT, &g_childNode[22]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // leaflist_6
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[12], "leaflist_6", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[12], &g_childNode[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodeProperty_PK(g_childNode[12], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[12]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(5, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(5, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 获取引用路径
    expectPathNum = 1;
    const char *expectPath01[expectPathNum] =
    {
        "/root_10_m/list_6[PK=100]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_10_m/list_5[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10_m/list_5[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 数据校验
    WhenDataCheck(g_stmt_root, true, GMERR_OK, GMERR_OK, GMC_YANG_VALIDATION_ALL_FORCE);

    // 获取引用路径
    TestGetLeafrefPath(g_stmt_root, "/root_10_m/list_5[PK=100]", expectPathNum, (const char**)expectPath01, data);
    TestGetLeafrefPath(g_stmt_root, "/root_10_m/list_5[PK=100]", expectPathNum, (const char**)expectPath01, data,
        GMERR_OK, GMC_LEAFREF_GET_PATH_ONE);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

class GetPath_test1 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GetPath_test1::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"yangAutoIndex=0\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GetPath_test1::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void GetPath_test1::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 100; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_CHECK_LOG_BEGIN();
}

void GetPath_test1::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AW_CHECK_LOG_END();

    // 删除表
    TestDropLabel(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 100; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 100; i++) {
        g_stmt_list[i] = NULL;
    }
    g_rootNode = NULL;
    for (i = 0; i < 100; i++) {
        g_childNode[i] = NULL;
    }
}

/*****************************************************************************
 Description  : 021.list下的字段被多个节点下的字段引用，包括require-instance设置成false的，
                指定主键，通过接口查询引用关系，不自动建索引
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test1, Yang_096_Func_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 创建表
    TestCreateLabel01(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 5; i++) {
        // list_1
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_2
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[2], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (uint32_t i = 0; i < 5; i++) {
        // list_3
        ret = testGmcPrepareStmtByLabelName(g_stmt_list[3], "list_3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list[3], &g_childNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        fieldValue = i;
        TestYangSetNodeProperty_PK(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        fieldValue = i;
        TestYangSetNodePropertyBasic(g_childNode[3], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(16, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(16, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 2;
    const char *expectPath01[expectPathNum] =
    {
        "/root_1/list_2[PK=1]",
        "/root_1/list_3[PK=1]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=1]", expectPathNum, (const char**)expectPath01, data);

    expectPathNum = 2;
    const char *expectPath02[expectPathNum] =
    {
        "/root_1/list_2[PK=3]",
        "/root_1/list_3[PK=3]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=3]", expectPathNum, (const char**)expectPath02, data);

    expectPathNum = 2;
    const char *expectPath03[expectPathNum] =
    {
        "/root_1/list_2[PK=4]",
        "/root_1/list_3[PK=4]"
    };
    TestGetLeafrefPath(g_stmt_root, "/root_1/list_1[PK=4]", expectPathNum, (const char**)expectPath03, data);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

class GetPath_test2 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GetPath_test2::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=6,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"yangAutoIndex=1\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GetPath_test2::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void GetPath_test2::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 100; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_CHECK_LOG_BEGIN();
}

void GetPath_test2::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AW_CHECK_LOG_END();

    // 删除表
    TestDropLabel(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 100; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 100; i++) {
        g_stmt_list[i] = NULL;
    }
    g_rootNode = NULL;
    for (i = 0; i < 100; i++) {
        g_childNode[i] = NULL;
    }
}

/*****************************************************************************
 Description  : 035.查询结果返回的数据超过2M，报错
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test2, Yang_096_Func_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 建表
    TestCreateLabel32(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_32", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_32_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (uint32_t cycle = 0; cycle < 20; cycle++) {
        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_32", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t startNum = 1000 * cycle;
        uint32_t endNum = startNum + 1000;

        for (uint32_t i = startNum; i < endNum; i++) {
            // list_2
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_32_2", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[1]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 设置属性值
            fieldValue = i;
            TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            fieldValue = 1;
            TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 设置32层嵌套数据
            for (uint32_t j = 1; j <= 31; j++) {
                char conName[128] = {0};
                (void)sprintf(conName, "c%d", j);
                ret = GmcYangEditChildNode(g_childNode[j], (const char *)conName, GMC_OPERATION_INSERT, &g_childNode[j+1]);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                fieldValue = 1;
                TestYangSetNodePropertyBasic(g_childNode[j+1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            }

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_list[2]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // list_2_1
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[22], "list_32_2_1", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[22]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[22], &g_childNode[22]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 设置属性值
            fieldValue = i;
            TestYangSetNodeProperty_PK(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            fieldValue = 1;
            TestYangSetNodePropertyBasic(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_list[22]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2001, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2001, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径, 由于超过限制，实际返回path=0
    expectPathNum = 0;
    data.leafrefPathes.num = expectPathNum;
    data.expStatus = GMERR_PROGRAM_LIMIT_EXCEEDED;

    // 超过发送消息大小，接收超时
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_32/list_32_1[PK=1]", NULL, GetPath_callbackOnlyNum, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
}

/*****************************************************************************
 Description  : 036.list_1的字段被list_2引用，list_2中只有10k数据有关联关系，
                list_2有20k总数据和20W总数据时，分别查询引用关系，记录时间，两次查询的内存上限，
                连接视图的内存，循环1000次，长连接，内存不上涨，自动建索引
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test2, Yang_096_Func_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    // 创建表
    TestCreateLabel08(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 写入root和list_1数据
    fieldValue = 1;
    TestInsertDataList1(g_conn_async, "root_8", fieldValue);

    // 写入20k list_2数据，只有10K有引用关系
    TestInsertDataList2(g_conn_async, "root_8", fieldValue, false);

    //查询内存before
    AW_FUN_Log(LOG_INFO, "==================20k Before=====================");
    GetComDynCtx();
    GetCltProcessConn();

    // 循环100次，比较时间
    for (uint32_t i = 0; i < 100; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        gettimeofday(&start, NULL);

        // 获取引用路径
        expectPathNum = 10000;
        data.leafrefPathes.num = expectPathNum;

        ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_8/list_1[PK=1]", NULL, GetPath_callbackOnlyNum, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
        if ((i % 10) == 0) {
            AW_FUN_Log(LOG_INFO, "[INFO] GmcYangGetLeafrefPathAsync Operation time is %lf s\n",
                (double)duration / 1000000);
        }

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询内存after
    AW_FUN_Log(LOG_INFO, "==================20k After=====================");
    GetComDynCtx();
    GetCltProcessConn();

    // 写入20W list_2数据，只有10K有引用关系
    TestInsertDataList2(g_conn_async, "root_8", fieldValue, true);

    //查询内存before
    AW_FUN_Log(LOG_INFO, "==================20W Before=====================");
    GetComDynCtx();
    GetCltProcessConn();

    // 循环100次，比较时间
    for (uint32_t i = 0; i < 100; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        gettimeofday(&start, NULL);

        // 获取引用路径
        expectPathNum = 10000;
        data.leafrefPathes.num = expectPathNum;

        ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_8/list_1[PK=1]", NULL, GetPath_callbackOnlyNum, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
        if ((i % 10) == 0) {
            AW_FUN_Log(LOG_INFO, "[INFO] GmcYangGetLeafrefPathAsync Operation time is %lf s\n",
                (double)duration / 1000000);
        }

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询内存after
    AW_FUN_Log(LOG_INFO, "==================20W After=====================");
    GetComDynCtx();
    GetCltProcessConn();
}


/*****************************************************************************
 Description  : 074.查询结果返回的数据接近2M，查询返回成功
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test2, Yang_096_Func_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    // 建表
    TestCreateLabel32(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_32", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    TestYangSetNodePropertyBasic(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "list_32_1", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    fieldValue = 1;
    TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    for (uint32_t cycle = 0; cycle < 10; cycle++) {
        // 设置批处理batch参数
        ret = TestBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_root, "root_32", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t startNum = 1000 * cycle;
        uint32_t endNum = startNum + 1000;

        for (uint32_t i = startNum; i < endNum; i++) {
            // list_2
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[2], "list_32_2", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[2]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[2], &g_childNode[1]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 设置属性值
            fieldValue = i;
            TestYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            fieldValue = 1;
            TestYangSetNodePropertyBasic(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 设置32层嵌套数据
            for (uint32_t j = 1; j <= 31; j++) {
                char conName[128] = {0};
                (void)sprintf(conName, "c%d", j);
                ret = GmcYangEditChildNode(g_childNode[j], (const char *)conName, GMC_OPERATION_INSERT, &g_childNode[j+1]);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                fieldValue = 1;
                TestYangSetNodePropertyBasic(g_childNode[j+1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            }

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_list[2]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // list_2_1
            ret = testGmcPrepareStmtByLabelName(g_stmt_list[22], "list_32_2_1", GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_list[2], g_stmt_list[22]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt_list[22], &g_childNode[22]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 设置属性值
            fieldValue = i;
            TestYangSetNodeProperty_PK(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
            fieldValue = 1;
            TestYangSetNodePropertyBasic(g_childNode[22], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_list[22]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2001, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2001, data.succNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    // 数据校验
    WhenDataCheck(g_stmt_root, true);

    // 获取引用路径
    expectPathNum = 10000;
    data.leafrefPathes.num = expectPathNum;

    // 超过发送消息大小，接收超时
    ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_32/list_32_1[PK=1]", NULL, GetPath_callbackOnlyNum, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 075.list_1的字段被list_2引用，list_2中只有10k数据有关联关系，
                list_2有20k总数据和20W总数据时，
                分别查询引用关系，设置为只返回1条结果，记录时间，
                两次查询的内存上限，连接视图的内存，循环1000次，长连接，内存不上涨
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test2, Yang_096_Func_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    // 创建表
    TestCreateLabel08(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 写入root和list_1数据
    fieldValue = 1;
    TestInsertDataList1(g_conn_async, "root_8", fieldValue);

    // 写入20k list_2数据，只有10K有引用关系
    TestInsertDataList2(g_conn_async, "root_8", fieldValue, false);

    //查询内存before
    AW_FUN_Log(LOG_INFO, "==================20k Before=====================");
    GetComDynCtx();
    GetCltProcessConn();

    // 循环100次，比较时间
    for (uint32_t i = 0; i < 100; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        gettimeofday(&start, NULL);

        // 获取引用路径
        expectPathNum = 1;
        data.leafrefPathes.num = expectPathNum;

        GmcGetLeafrefPathOptionsT *leafrefOption = NULL;
        ret = GmcYangGetLeafrefPathOptionsCreate(GMC_LEAFREF_GET_PATH_ONE, &leafrefOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_8/list_1[PK=1]", leafrefOption,
            GetPath_callbackOnlyNum, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
        if ((i % 10) == 0) {
            AW_FUN_Log(LOG_INFO, "[INFO] GmcYangGetLeafrefPathAsync Operation time is %lf s\n",
                (double)duration / 1000000);
        }
        GmcYangGetLeafrefPathOptionsDestroy(leafrefOption);

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询内存after
    AW_FUN_Log(LOG_INFO, "==================20k After=====================");
    GetComDynCtx();
    GetCltProcessConn();

    // 写入20W list_2数据，只有10K有引用关系
    TestInsertDataList2(g_conn_async, "root_8", fieldValue, true);

    //查询内存before
    AW_FUN_Log(LOG_INFO, "==================20W Before=====================");
    GetComDynCtx();
    GetCltProcessConn();

    // 循环100次，比较时间
    for (uint32_t i = 0; i < 100; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        gettimeofday(&start, NULL);

        // 获取引用路径
        expectPathNum = 1;
        data.leafrefPathes.num = expectPathNum;

        GmcGetLeafrefPathOptionsT *leafrefOption = NULL;
        ret = GmcYangGetLeafrefPathOptionsCreate(GMC_LEAFREF_GET_PATH_ONE, &leafrefOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_8/list_1[PK=1]", leafrefOption,
            GetPath_callbackOnlyNum, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
        if ((i % 10) == 0) {
            AW_FUN_Log(LOG_INFO, "[INFO] GmcYangGetLeafrefPathAsync Operation time is %lf s\n",
                (double)duration / 1000000);
        }
        GmcYangGetLeafrefPathOptionsDestroy(leafrefOption);

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询内存after
    AW_FUN_Log(LOG_INFO, "==================20W After=====================");
    GetComDynCtx();
    GetCltProcessConn();
}

class GetPath_test3 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void GetPath_test3::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=6,200,300\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"yangAutoIndex=0\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void GetPath_test3::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void GetPath_test3::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t i = 0;
    for (i = 0; i < 100; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_CHECK_LOG_BEGIN();
}

void GetPath_test3::TearDown()
{
    int ret;
    uint32_t i = 0;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AW_CHECK_LOG_END();

    // 删除表
    TestDropLabel(g_stmt_async);

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 100; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 100; i++) {
        g_stmt_list[i] = NULL;
    }
    g_rootNode = NULL;
    for (i = 0; i < 100; i++) {
        g_childNode[i] = NULL;
    }
}

/*****************************************************************************
 Description  : 037.list_1的字段被list_2引用，list_2中只有10k数据有关联关系，
                list_2有20k总数据和20W总数据时，分别查询引用关系，记录时间，两次查询的内存上限，
                连接视图的内存，循环1000次，长连接，内存不上涨，非自动建索引
 Author       : hanyang
*****************************************************************************/
TEST_F(GetPath_test3, Yang_096_Func_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    uint32_t expectPathNum = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    // 创建表
    TestCreateLabel08(g_stmt_async);

    // 模型校验和数据校验
    ModelCheckAndDataCheck(g_conn_async, g_stmt_async);

    // 写入root和list_1数据
    fieldValue = 1;
    TestInsertDataList1(g_conn_async, "root_8", fieldValue);

    // 写入20k list_2数据，只有10K有引用关系
    TestInsertDataList2(g_conn_async, "root_8", fieldValue, false);

    //查询内存before
    AW_FUN_Log(LOG_INFO, "==================20k Before=====================");
    GetComDynCtx();
    GetCltProcessConn();

    // 循环100次，比较时间
    for (uint32_t i = 0; i < 100; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        gettimeofday(&start, NULL);

        // 获取引用路径
        expectPathNum = 10000;
        data.leafrefPathes.num = expectPathNum;

        ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_8/list_1[PK=1]", NULL, GetPath_callbackOnlyNum, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
        if ((i % 10) == 0) {
            AW_FUN_Log(LOG_INFO, "[INFO] GmcYangGetLeafrefPathAsync Operation time is %lf s\n",
                (double)duration / 1000000);
        }

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询内存after
    AW_FUN_Log(LOG_INFO, "==================20k After=====================");
    GetComDynCtx();
    GetCltProcessConn();

    // 写入20W list_2数据，只有10K有引用关系
    TestInsertDataList2(g_conn_async, "root_8", fieldValue, true);

    //查询内存before
    AW_FUN_Log(LOG_INFO, "==================20W Before=====================");
    GetComDynCtx();
    GetCltProcessConn();

    // 循环100次，比较时间
    for (uint32_t i = 0; i < 100; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        gettimeofday(&start, NULL);

        // 获取引用路径
        expectPathNum = 10000;
        data.leafrefPathes.num = expectPathNum;

        ret = GmcYangGetLeafrefPathAsync(g_stmt_root, "/root_8/list_1[PK=1]", NULL, GetPath_callbackOnlyNum, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        gettimeofday(&end, NULL);
        duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
        if ((i % 10) == 0) {
            AW_FUN_Log(LOG_INFO, "[INFO] GmcYangGetLeafrefPathAsync Operation time is %lf s\n",
                (double)duration / 1000000);
        }

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询内存after
    AW_FUN_Log(LOG_INFO, "==================20W After=====================");
    GetComDynCtx();
    GetCltProcessConn();
}
