/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : 树模型支持mandatory校验
 Notes        :

 History      :
 Author       : youwanyong ywx1157510
 Modification : 2022/9/8
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <atomic>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "YangSuppportMandatoryCheck_test.h"

using namespace std;

int32_t ret;

class YangTreeSupportMandatoryCheck_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void YangTreeSupportMandatoryCheck_test::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createEpollOneThread();
    EXPECT_EQ(GMERR_OK, ret);
}

void YangTreeSupportMandatoryCheck_test::TearDownTestCase()
{
    closeEpollOneThread();
    testEnvClean();
}

void YangTreeSupportMandatoryCheck_test::SetUp()
{
    int ret;
    g_conn_async = NULL;
    g_stmt_async = NULL;
	// 单线程异步标识
    g_isOneThreadEpoll = true;
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_namespace;
    nspCfg.userName = g_namespaceUserName;

    // 可重复读+乐观
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
    AsyncUserDataT data{0};
    GmcDropNamespace(g_stmt, g_namespace);
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    // 添加日志检测白名单
    AddErrWhiteLst(GMERR_TRANS_MODE_MISMATCH);
    AW_ADD_TRUNCATION_WHITE_LIST(1, "The resource usage of the GMDBV5 component is about to exceed the upper limit");
}

void YangTreeSupportMandatoryCheck_test::TearDown()
{
    AW_CHECK_LOG_END();
	// 单线程异步标识
    g_isOneThreadEpoll = true;
    int ret;
    AsyncUserDataT data = {0};

    // 删表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn_async = NULL;
    g_stmt_async = NULL;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

/* ****************************************************************************
 Description  :  111.List_list进行mandatory校验
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_111)
{
    // 创建Vertex表
    AW_FUN_Log(LOG_STEP, "test start");
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/list_list.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_list_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入root节点数据******************************* */
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "choice", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "case1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF0 = 100;
    ret = testYangSetField(
        g_childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_container_chioce——case
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //
    ret = GmcYangEditChildNode(g_childNode[7], "container1", GMC_OPERATION_MERGE, &g_childNode[10]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_childNode[10], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[10], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[10], "listcontainerchioce", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "container1case1", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[4], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[4], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[10], "listcontainerchioce", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "container1case2", GMC_OPERATION_MERGE, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[5], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[5], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "container1case2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[3], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[3], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 2);
    mandatoryCheck(g_stmt_async, true);
    // 事务提交
    testTransCommitAsync(g_conn_async);

    subtreeQuery(g_stmt_async);
}

/* ****************************************************************************
 Description  :  112.container_container进行mandatory校验
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_112)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    char *vlabelEdgeSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    AW_FUN_Log(LOG_STEP, "test start");

    readJanssonFile("schema_file/container_container.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);
    AW_FUN_Log(LOG_STEP, "建树模型表con_con完成");

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // container np节点可见
    insertContainerChioceCase(batch, g_stmt_async, true);
    mandatoryCheck(g_stmt_async, false);

    //
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 1);
    // 字段级不满足mandatory
    mandatoryCheck(g_stmt_async, false);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入剩下部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 1);
    // 字段级不满足mandatory
    mandatoryCheck(g_stmt_async, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    subtreeQuery(g_stmt_async);
}

/* ****************************************************************************
 Description  :  113.container_list进行mandatory校验
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_113)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    AW_FUN_Log(LOG_STEP, "test start");

    readJanssonFile("schema_file/container_list.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_container_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(vlabelSchema);

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入root节点数据******************************* */
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 1);
    mandatoryCheck(g_stmt_async, true);

    // list
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t valueF0 = 100;
    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 2);
    mandatoryCheck(g_stmt_async, true);

    // list delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t PID = 1;
    testSetKeyNameAndValue(g_stmt_list, valueF0, PID, true);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 2);
    mandatoryCheck(g_stmt_async, true);

    // list merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    PID = 1;
    testSetKeyNameAndValue(g_stmt_list, valueF0, PID, true);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 2);
    mandatoryCheck(g_stmt_async, true);
    // 事务提交
    testTransCommitAsync(g_conn_async);
    subtreeQuery(g_stmt_async);
}

/* ****************************************************************************
 Description  :  114.container_chioce_case_List
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_114)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    AW_FUN_Log(LOG_STEP, "test start");

    readJanssonFile("schema_file/list_container_chioce_case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_container_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(vlabelSchema);

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入root节点数据******************************* */
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "choice", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "case1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF0 = 100;
    ret = testYangSetField(
        g_childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_container_chioce——case
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //
    ret = GmcYangEditChildNode(g_childNode[7], "container1", GMC_OPERATION_MERGE, &g_childNode[10]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(
        g_childNode[10], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[10], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(g_childNode[10], "listcontainerchioce", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "container1case1", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[4], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[4], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[10], "listcontainerchioce", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "container1case2", GMC_OPERATION_MERGE, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[5], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[5], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "container1case2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[3], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[3], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 2);
    mandatoryCheck(g_stmt_async, true);
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :  115.container_chioce_case_container
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_115)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/Container_mandatory_container.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);
    AW_FUN_Log(LOG_STEP, "建树模型表con_con完成");
    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入root节点数据******************************* */
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "choice", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "case1", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF0 = 100;
    ret = testYangSetField(
        g_childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "case2", GMC_OPERATION_REPLACE_GRAPH, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[3], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[3], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 1);
    mandatoryCheck(g_stmt_async, true);
    // 事务提交
    testTransCommitAsync(g_conn_async);
    subtreeQuery(g_stmt_async);
}

/* ****************************************************************************
 Description  :  116.container_container_list
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_116)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/container_container_list.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_container_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(vlabelSchema);

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入root节点数据******************************* */
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入剩下部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入剩下部分container节点数据");

    fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t valueF0 = 100;
    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // // 设置key
    fieldValue = 100;
    uint32_t PID = 1;
    testSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 5);
    mandatoryCheck(g_stmt_async, true);
    // 事务提交
    testTransCommitAsync(g_conn_async);
    // subtree 查詢
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/Container_container_list.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async, suntreeReturnJson);
    mandatoryCheck(g_stmt_async, true, GMERR_TRANS_MODE_MISMATCH);
    free(suntreeReturnJson);
}

/* ****************************************************************************
 Description  :  117.diff开关关闭，进行mandatory校验
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_117)
{
    {
        AW_FUN_Log(LOG_STEP, "test start");
        uint32_t fieldValue = 100;
        char *vlabelSchema = NULL;
        char *vlabelEdgeSchema = NULL;
        GmcBatchT *batch = NULL;
        AsyncUserDataT data{0};
        // 创建Vertex表
        readJanssonFile("schema_file/Container_Choice_Case.gmjson", &vlabelSchema);
        ASSERT_NE((void *)NULL, vlabelSchema);

        ret =
            GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

        free(vlabelSchema);
        AW_FUN_Log(LOG_STEP, "建树模型表con_con完成");
        ret = TestCheckValidateModelAsync(g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 开启事务 开启diff 进行批操作
        ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数 关闭diff开关
        ret = testBatchPrepare(g_conn_async, &batch, GMC_YANG_DIFF_OFF);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 插入数据
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        ret = testYangSetField(g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        testWait(batch, 1);
        data.isValidErrorPathInfo = false;
        data.expectedErrMsg = "";
        data.expectedErrPath = "";
        // mandatory 校验，字段部分无数据，diff有数据，预期返回false
        AW_FUN_Log(LOG_STEP, "diff关闭异常状态返回事务回滚");
        mandatoryCheck(g_stmt_async, false, 0);

        // 事务回滚
        GmcTransRollBackAsync(g_conn_async, trans_rollback_callback, &data);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_FUN_Log(LOG_STEP, "事务回滚");
        ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
       AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char *suntreeReturnJson = NULL;
        char *container_contentJson = NULL;
        readJanssonFile("SubTreeFilterJson/containerleaf_Listnode.json", &container_contentJson);
        ASSERT_NE((void *)NULL, container_contentJson);
        GmcSubtreeFilterItemT filter = {0};
        filter.rootName = "root";
        filter.subtree.json = container_contentJson;
        filter.jsonFlag = GMC_JSON_INDENT(4);
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_JSON,   // 如果只查询一颗树这里count是1
            .filter = &filter,  // 包含GmcSubtreeFilterItemT 的 filter
        };
        char *reply = NULL;
        SubtreeFilterCbParam Data = {0};
        Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
        Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
        Data.step = 0;                             // 回调执行次数
        ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb, &Data);
        ret = testWaitAsyncSubtreeRecv(&Data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
        free(container_contentJson);
        testTransCommitAsync(g_conn_async);

        AW_FUN_Log(LOG_STEP, "test end");
    }
}

/* ****************************************************************************
 Description  :  118.对 container节点设置nullable:false ，进行建表
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_118)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    AW_FUN_Log(LOG_STEP, "test start");

    readJanssonFile("schema_file/errorContainer.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vlabelSchema);
    // 添加日志检测白名单
    AddErrWhiteLst(GMERR_INVALID_TABLE_DEFINITION);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
}

/* ****************************************************************************
 Description  :  119.对 list节点设置nullable:false ，进行建表
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_119)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    AW_FUN_Log(LOG_STEP, "test start");
    AddErrWhiteLst(GMERR_INVALID_TABLE_DEFINITION);

    readJanssonFile("schema_file/errorList.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    free(vlabelSchema);
}

/* ****************************************************************************
 Description  :  120.六原语异常，导致的diff为空场景    本來有數據但是最後刪除了diff爲空
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_120)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/container_container_list.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_container_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(vlabelSchema);

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入root节点数据******************************* */
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入剩下部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入剩下部分container节点数据");

    fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t valueF0 = 100;
    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list delete
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置key
    fieldValue = 100;
    uint32_t PID = 1;
    testSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list merge
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetKeyNameAndValue(g_stmt_list, fieldValue, PID, true);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 6);
    // 当前diff为空
    mandatoryCheck(g_stmt_async, true);
    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    subtreeQuery(g_stmt_async, suntreeReturnJson);
}

/* ****************************************************************************
 Description  :
121.32层树模型vertex为container：包含15层的choice_case,只有最后一层为(chioce)nullable:false，其余为default case Author
: youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_121)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/fifth_chioce_case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 建三十二层嵌套树模型表只有最后一层为nullablefalse表，其余均case1为default case
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向模型中插入数据case均只插case2，最后一层才插case1
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async, true);
    mandatoryCheck(g_stmt_async, false);

    //
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 1);
    // 字段级不满足mandatory
    mandatoryCheck(g_stmt_async, false);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入剩下部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char Choice_case_label_name[20] = "lableName";
    /* *********************************插入14层case2节点数据******************************* */
    fourtheenCHoiceCaseInsert(g_childNode);

    // // 进行第15层嵌套choice_case的mandatory校验
    OnlyFifthChoiceCase(g_childNode);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 1);

    // nullable:false 父节点存在数据，子节点不存在数据，返回false
    mandatoryCheck(g_stmt_async, true);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/fiftheenchoicecse.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async, suntreeReturnJson);
    free(suntreeReturnJson);
}

/* ****************************************************************************
 Description  :  122.32层树模型vertex为container：包含15层的choice_case都满足(chioce)nullable:false
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_122)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/mandatory_fiftheen.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 建三十二层嵌套树模型表只有最后一层为nullablefalse表，其余均case1为default case
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向模型中插入数据case均只插case2，最后一层才插case1
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async, true);
    mandatoryCheck(g_stmt_async, false);

    //
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 1);
    // 字段级不满足mandatory
    mandatoryCheck(g_stmt_async, false);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入剩下部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char Choice_case_label_name[20] = "lableName";
    /* *********************************插入14层case2节点数据******************************* */
    fourtheenCHoiceCaseInsert(g_childNode, true);

    // 进行第15层嵌套choice_case的mandatory校验
    OnlyFifthChoiceCase(g_childNode, 15, true);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);

    // nullable:false 父节点存在数据
    mandatoryCheck(g_stmt_async, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/FiftheenNoCase2.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async, suntreeReturnJson);
    free(suntreeReturnJson);
}

/* ****************************************************************************
 Description  :  123.32层树模型vertex为container：包含15层的choice_case都不满足(chioce)nullable:false,最后一层为default
case Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_123)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/fifth_chioce_case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 建三十二层嵌套树模型表只有最后一层为nullablefalse表，其余均case1为default case
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向模型中插入数据case均只插case2，最后一层才插case1
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async, true);
    mandatoryCheck(g_stmt_async, false);

    //
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 1);
    // 字段级不满足mandatory
    mandatoryCheck(g_stmt_async, false);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入剩下部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char Choice_case_label_name[20] = "lableName";
    /* *********************************插入14层case2节点数据******************************* */
    fourtheenCHoiceCaseInsert(g_childNode, true);

    // 进行第15层嵌套choice_case的mandatory校验
    OnlyFifthChoiceCase(g_childNode, 15, true);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 1);

    // nullable:false 父节点存在数据
    mandatoryCheck(g_stmt_async, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/FiftheenNoCase2.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async, suntreeReturnJson);
    free(suntreeReturnJson);
}

/* ****************************************************************************
 Description  :  124.32层树模型vertex为list：包含15层的choice_case,只有最后一层为(chioce)nullable:false，其余为default
case Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_124)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/list_fiftheen_chioce_case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 建三十二层嵌套树模型表只有最后一层为nullablefalse表，其余均case1为default case
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_container_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向模型中插入数据case均只插case2，最后一层才插case1
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async, true);
    mandatoryCheck(g_stmt_async, false);

    //
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /* *********************************插入14层case2节点数据******************************* */

    fourtheenCHoiceCaseInsert(g_childNode);

    // 进行第15层嵌套choice_case的mandatory校验
    OnlyFifthChoiceCase(g_childNode);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_listChildNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t valueF0 = 100;

    ret = testYangSetField(
        g_listChildNode[0], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_listChildNode[0], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_listChildNode[0], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_listChildNode[0], "container2", GMC_OPERATION_MERGE, &g_listChildNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    ret = testYangSetField(g_listChildNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_listChildNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 后14层choice_case嵌套
    fourtheenCHoiceCaseInsert(g_listChildNode, true, 1, 15, 15);

    // // 进行第15层嵌套choice_case的mandatory校驗
    char choiceName[20] = "choice30";
    OnlyFifthChoiceCase(g_listChildNode, 15, true, choiceName);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 2);
    // 获取errorPath
    data.isValidErrorPathInfo = true;
    data.expectedErrMsg = "mandatory verify no field";
    data.expectedErrPath =
        "/root/list[F0=100]/container2/choice16/case12/choice17/case22/choice18/case32/choice19/case42/choice20/"
        "case52/choice21/case62/choice22/case72/choice23/case82/choice24/case92/choice25/case102/choice26/case112/"
        "choice27/case122/choice28/case132/choice29/case142/F0";
    // nullable:false 父节点存在数据
    ErrorPathMandatoryCheck(g_stmt_async, data, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/list_conrainer_mandatory_fiftheen.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async, suntreeReturnJson);
    free(suntreeReturnJson);
}

/* ****************************************************************************
 Description  :  125.32层树模型vertex为list：包含15层的choice_case都满足(chioce)nullable:false
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_125)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/list_fiftheen_mandatory.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 建三十二层嵌套树模型表只有最后一层为nullablefalse表，其余均case1为default case
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_container_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向模型中插入数据case均只插case2，最后一层才插case1
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async, true);
    mandatoryCheck(g_stmt_async, false);

    //
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /* *********************************插入14层case2节点数据******************************* */

    fourtheenCHoiceCaseInsert(g_childNode);

    // 进行第15层嵌套choice_case的mandatory校验
    OnlyFifthChoiceCase(g_childNode);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_listChildNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t valueF0 = 100;

    ret = testYangSetField(
        g_listChildNode[0], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_listChildNode[0], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_listChildNode[0], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_listChildNode[0], "container2", GMC_OPERATION_MERGE, &g_listChildNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    ret = testYangSetField(g_listChildNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_listChildNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 后14层choice_case嵌套
    fourtheenCHoiceCaseInsert(g_listChildNode, true, 1, 15, 15);

    // // 进行第15层嵌套choice_case的mandatory校驗
    char choiceName[20] = "choice30";
    OnlyFifthChoiceCase(g_listChildNode, 15, true, choiceName);
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 2);
    // 获取errorPath
    data.isValidErrorPathInfo = true;
    data.expectedErrMsg = "mandatory verify no field";
    data.expectedErrPath =
        "/root/list[F0=100]/container2/choice16/case12/choice17/case22/choice18/case32/choice19/case42/choice20/"
        "case52/choice21/case62/choice22/case72/choice23/case82/choice24/case92/choice25/case102/choice26/case112/"
        "choice27/case122/choice28/case132/choice29/case142/F0";
    // nullable:false 父节点存在数据
    ErrorPathMandatoryCheck(g_stmt_async, data, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/list_conrainer_mandatory_fiftheen.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async, suntreeReturnJson);

    free(suntreeReturnJson);
}

/* ****************************************************************************
 Description  :  126.32层树模型vertex为list：包含15层的choice_case都不满足(chioce)nullable:false,最后一层为default case
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_126)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/list_fiftheen_choice_case_end_default_case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 建三十二层嵌套树模型表只有最后一层为nullablefalse表，其余均case1为default case
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_container_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向模型中插入数据case均只插case2，最后一层才插case1
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async, true);
    mandatoryCheck(g_stmt_async, false);

    //
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "container1", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入部分container节点数据
    AW_FUN_Log(LOG_STEP, "插入部分container节点数据");

    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /* *********************************插入14层case2节点数据******************************* */

    fourtheenCHoiceCaseInsert(g_childNode);

    // 进行第15层嵌套choice_case的mandatory校验
    OnlyFifthChoiceCase(g_childNode);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_listChildNode[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t valueF0 = 100;

    ret = testYangSetField(
        g_listChildNode[0], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_listChildNode[0], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_listChildNode[0], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_listChildNode[0], "container2", GMC_OPERATION_MERGE, &g_listChildNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    fieldValue = 100;
    ret = testYangSetField(g_listChildNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_listChildNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 后14层choice_case嵌套
    fourtheenCHoiceCaseInsert(g_listChildNode, true, 1, 15, 15);

    // // 进行第15层嵌套choice_case的mandatory校驗
    char choiceName[20] = "choice30";
    OnlyFifthChoiceCase(g_listChildNode, 15, true, choiceName);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 2);
    // 获取errorPath
    data.isValidErrorPathInfo = true;
    data.expectedErrMsg = "mandatory verify no field";
    data.expectedErrPath =
        "/root/list[F0=100]/container2/choice16/case12/choice17/case22/choice18/case32/choice19/case42/choice20/"
        "case52/choice21/case62/choice22/case72/choice23/case82/choice24/case92/choice25/case102/choice26/case112/"
        "choice27/case122/choice28/case132/choice29/case142/F0";
    // nullable:false 父节点存在数据
    ErrorPathMandatoryCheck(g_stmt_async, data, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/list_conrainer_mandatory_fiftheen.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async, suntreeReturnJson);
    free(suntreeReturnJson);
}
/* ****************************************************************************
 Description  :  127.chioce节点为nullable：false,case节点不能为defaultcase
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_127)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    AW_FUN_Log(LOG_STEP, "test start");

    readJanssonFile("schema_file/choice_false_case_default.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 含nullable:faslechoice有default case
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(vlabelSchema);
    // 添加日志检测白名单
    AddErrWhiteLst(GMERR_INVALID_TABLE_DEFINITION);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
}

/* ****************************************************************************
 Description  :  128.32层树模型嵌套，list为根节点子node都是container
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_128)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    char Choice_case_label_name[20] = "lableName";

    readJanssonFile("schema_file/thity_two_container.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 含nullable:faslechoice有default case
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_container_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向模型中插入数据case均只插case2，最后一层才插case1
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async, true);
    mandatoryCheck(g_stmt_async, false);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入31层container节点数据******************************* */
    uint32_t start_num = 1;
    uint32_t end_num = 32;
    uint32_t fieldValue = 100;
    AW_FUN_Log(LOG_STEP, "插入container数据");
    for (uint32_t i = start_num; i < end_num; i++) {

        // 设置child choice节点
        sprintf(Choice_case_label_name, "container%d", i);
        ret = GmcYangEditChildNode(g_childNode[i], Choice_case_label_name, GMC_OPERATION_MERGE, &g_childNode[i + 1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_childNode[i + 1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetField(g_childNode[i + 1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /***********************設置list节点*************************/
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_listChildNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t valueF0 = 100;

    ret = testYangSetField(
        g_listChildNode[1], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_listChildNode[1], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入31层container节点数据******************************* */
    start_num = 1;
    end_num = 32;
    fieldValue = 100;
    AW_FUN_Log(LOG_STEP, "插入container数据");
    for (uint32_t i = start_num; i < end_num; i++) {

        // 设置child choice节点
        sprintf(Choice_case_label_name, "container%d", i);
        ret = GmcYangEditChildNode(
            g_listChildNode[i], Choice_case_label_name, GMC_OPERATION_MERGE, &g_listChildNode[i + 1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i != 15) {
            ret = testYangSetField(g_listChildNode[i + 1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        ret = testYangSetField(g_listChildNode[i + 1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 2);
    // 获取errorPath
    data.isValidErrorPathInfo = true;
    data.expectedErrMsg = "mandatory verify no field";
    data.expectedErrPath =
        "/root/list[F0=100]/container1/container2/container3/container4/container5/container6/container7/container8/"
        "container9/container10/container11/container12/container13/container14/container15/F0";
    // nullable:false 父节点存在数据
    ErrorPathMandatoryCheck(g_stmt_async, data, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/list_thity_one_contaimer.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async, suntreeReturnJson);
    free(suntreeReturnJson);
}

/* ****************************************************************************
 Description  :  129.32层树模型嵌套，container为根节点子node都是container
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_129)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    char Choice_case_label_name[20] = "lableName";

    readJanssonFile("schema_file/thity_two_container.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 含nullable:faslechoice有default case
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_container_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向模型中插入数据case均只插case2，最后一层才插case1
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async, true);
    mandatoryCheck(g_stmt_async, false);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入31层container节点数据******************************* */
    uint32_t start_num = 1;
    uint32_t end_num = 32;
    uint32_t fieldValue = 100;
    AW_FUN_Log(LOG_STEP, "插入container数据");
    for (uint32_t i = start_num; i < end_num; i++) {

        // 设置child choice节点
        sprintf(Choice_case_label_name, "container%d", i);
        ret = GmcYangEditChildNode(g_childNode[i], Choice_case_label_name, GMC_OPERATION_MERGE, &g_childNode[i + 1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (i != 15) {
            ret = testYangSetField(g_childNode[i + 1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0",
                GMC_YANG_PROPERTY_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        ret = testYangSetField(g_childNode[i + 1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2",
            GMC_YANG_PROPERTY_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 1);
    // 获取errorPath
    data.isValidErrorPathInfo = true;
    data.expectedErrMsg = "mandatory verify no field";
    data.expectedErrPath =
        "/root/container1/container2/container3/container4/container5/container6/container7/container8/container9/"
        "container10/container11/container12/container13/container14/container15/F0";
    // nullable:false 父节点存在数据
    ErrorPathMandatoryCheck(g_stmt_async, data, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/thirty_container.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async, suntreeReturnJson);
    free(suntreeReturnJson);
}
// 当前校验mandatory如果存在多个nullable false字段为空时，进行madantory校验errmsg返回值将为随机值
/* ****************************************************************************
 Description  :  130.32层树模型，list为根节点子node最后一层为container其余为choice_case;
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_130)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    AW_FUN_Log(LOG_STEP, "test start");

    readJanssonFile("schema_file/list_thirty_two_end_container.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 建三十二层嵌套树模型表只有最后一层为nullablefalse表，其余均case1为default case
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_container_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向模型中插入数据case均只插case2，
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async, true);
    mandatoryCheck(g_stmt_async, true);

    //
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入15层case2节点数据******************************* */

    fourtheenCHoiceCaseInsert(g_childNode, true, 1, 16);
    uint32_t layers = 16;
    uint32_t valueF0 = 100;
    // 插入最後一层container
    ret =
        GmcYangEditChildNode(g_childNode[2 * layers - 1], "container32", GMC_OPERATION_MERGE, &g_childNode[2 * layers]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_childNode[2 * layers], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2 * layers], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "P2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*********插入list节点数据**********/
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_listChildNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    valueF0 = 100;

    ret = testYangSetField(
        g_listChildNode[1], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_listChildNode[1], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_listChildNode[1], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入15层case2节点数据******************************* */

    fourtheenCHoiceCaseInsert(g_listChildNode, false, 1, 16, 15);
    layers = 16;
    valueF0 = 100;
    // 插入最後一层container
    ret = GmcYangEditChildNode(
        g_listChildNode[2 * layers - 1], "container32", GMC_OPERATION_MERGE, &g_listChildNode[2 * layers]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_listChildNode[2 * layers], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_listChildNode[2 * layers], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "P2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 2);
    // 获取errorPath
    data.isValidErrorPathInfo = true;
    data.expectedErrMsg = "mandatory verify no field";
    data.expectedErrPath = "/root/choice1/case12/choice2/case22/choice3/case32/choice4/case42/choice5/case52/choice6/"
                           "case62/choice7/case72/choice8/case82/choice9/case92/choice10/case102/choice11/case112/"
                           "choice12/case122/choice13/case132/choice14/case142/F0";
    // nullable:false 父节点存在数据
    ErrorPathMandatoryCheck(g_stmt_async, data, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/list_thirty_two_end_of_container.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async, suntreeReturnJson);

    free(suntreeReturnJson);
}

/* ****************************************************************************
 Description  :  131.32层树模型，container为根节点子node最后一层为container其余为choice_case
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_131)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    AW_FUN_Log(LOG_STEP, "test start");

    readJanssonFile("schema_file/container_choice_case_end_with_container.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 建三十二层嵌套树模型表只有最后一层为nullablefalse表，其余均case1为default case
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_container_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vlabelSchema);

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 向模型中插入数据case均只插case2，
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async, true);
    // 根节点有数据，default caes为true
    mandatoryCheck(g_stmt_async, true);

    //
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入15层case2节点数据******************************* */

    fourtheenCHoiceCaseInsert(g_childNode, true, 1, 16);
    uint32_t layers = 16;
    uint32_t valueF0 = 100;
    // 插入最後一层container
    ret =
        GmcYangEditChildNode(g_childNode[2 * layers - 1], "container32", GMC_OPERATION_MERGE, &g_childNode[2 * layers]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(g_childNode[2 * layers], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(g_childNode[2 * layers], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "P2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 1);
    // 获取errorPath
    data.isValidErrorPathInfo = true;
    data.expectedErrMsg = "mandatory verify no field";
    data.expectedErrPath = "/root/choice1/case12/choice2/case22/choice3/case32/choice4/case42/choice5/case52/choice6/"
                           "case62/choice7/case72/choice8/case82/choice9/case92/choice10/case102/choice11/case112/"
                           "choice12/case122/choice13/case132/choice14/case142/F0";
    // nullable:false 父节点存在数据
    ErrorPathMandatoryCheck(g_stmt_async, data, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/thirty_two_container_chioce_case_container.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async, suntreeReturnJson);
    free(suntreeReturnJson);
}

// 读线程
void *readThread(void *args)
{
    //
    GmcConnT *readConn = (GmcConnT *)args;
    GmcStmtT *readStmt = NULL;
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/thirty_two_container_chioce_case_container.json", &suntreeReturnJson);
    ret = GmcAllocStmt(readConn, &readStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    subtreeQuery(readStmt);
    free(suntreeReturnJson);
}

// 写线程

void *Thread_132_01(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============[3] Yang REPLACE start==================\n\n");
    int ret;
    uint32_t fieldValue;
    uint32_t newvalue;
    uint32_t ID;
    uint32_t PID;
    GmcStmtT *stmt_async[100];
    GmcConnT *conn_async[100];
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    AsyncUserDataT data = {0};
    int index = *(int *)args;
    ret = createEpollOneThread(&g_epAsync[index]);
    EXPECT_EQ(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[index].userEpollFd;
    ret = TestYangGmcConnect(&conn_async[index], &stmt_async[index], GMC_CONN_TYPE_ASYNC, &connOptions);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_async[index], g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = GmcTransStartAsync(conn_async[index], &g_mSTrxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 设置批处理batch参数
    ret = testBatchPrepare(conn_async[index], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt_async[index], "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_async[index]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_async[index], &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child choice节点
    ret = GmcYangEditChildNode(rootNode, "choice", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child case节点
    ret = GmcYangEditChildNode(childNode1, "case1", GMC_OPERATION_MERGE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    newvalue = 200;
    testYangSetNodeProperty(childNode2, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, stmt_async[index]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    std::atomic_uint32_t step{0};
    ValidateParam param = {.step = &step, .exceptStatus = GMERR_OK, .validateRes = true};
    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ret = GmcYangValidateAsync(stmt_async[index], &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitValidateAsyncRecv(&param, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);
    memset(&param, 0, sizeof(ValidateParam));
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = GmcTransCommitAsync(conn_async[index], trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = testGmcDisconnect(conn_async[index], stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "==============[3] Yang REPLACE end==================\n\n");
}

void *Thread_132_02(void *args)
{
    AW_FUN_Log(LOG_STEP, "==============diff is null==================\n\n");
    int ret;
    uint32_t fieldValue;
    uint32_t newvalue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = testTransStartAsync(conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    mandatoryCheck(stmt_root, true, 0);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 回滚事务
    ret = GmcTransRollBackAsync(conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  :  132.多线程
树模型container—choice-case满足mandatory结构，每线程分别开启事务执行不同的六原语操作进行mandatory校验，线程1提交，其余线程回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_132)
{
    // 建表
    // 创建Vertex表

    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/Container_Choice_Case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);
    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async);

    // 事务提交
    testTransCommitAsync(g_conn_async);

    // 多个线程写数据，读数据
    // 创建多个线程
    int32_t thread_num = 2;
    // 定义线程的 id 变量，多个变量使用数组
    pthread_t client_thr[thread_num];

    // // 参数依次是：创建的线程id，线程参数，调用的函数，传入的函数参数
    // 写线程
    ret = pthread_create(&client_thr[0], NULL, Thread_132_02, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 写线程
    int32_t indexValue = 10;
    ret = pthread_create(&client_thr[1], NULL, Thread_132_01, &indexValue);
    EXPECT_EQ(GMERR_OK, ret);

    for (uint32_t i = 0; i < 2; i++) {
        ret = pthread_join(client_thr[i], NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "多线程mandatory验证");

    // 含冲突的数据，部分回滚，部分提交
}
#define MAX_CONN MAX_ASYNC_CONN_SIZE_PER_PRO
/* ****************************************************************************
 Description  :  133.连接满场景下，树模型container—choice-case结构满足mandatory执行六原语操作。进行mandatory校验
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_133)
{
    int ret;

    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/Container_Choice_Case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);
    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async);

    // 事务提交
    testTransCommitAsync(g_conn_async);

    // 调用视图查询当前连接数
    uint32_t existConnNum;
    testGetConnNum(&existConnNum);
    GmcConnT *conn[MAX_CONN] = {0};
    GmcStmtT *stmt[MAX_CONN] = {0};
    for (int i = 0; i < MAX_CONN - existConnNum; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i], GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    AW_FUN_Log(LOG_STEP, " root更新F0");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t fieldValue = 100;
    // 设置属性值
    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 1);
    data.isValidErrorPathInfo = true;
    data.expectedErrMsg = "";
    data.expectedErrPath = "";
    // mandatory 校验，字段部分无数据，diff有数据，预期返回false
    mandatoryCheck(g_stmt_async, false);

    // 事务提交
    testTransCommitAsync(g_conn_async);

    // 开启事务 开启diff 进行批操作 使用merge插入其余字段
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    AW_FUN_Log(LOG_STEP, " root更新P2");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 1);

    // mandatory diff有数据预期返回true
    mandatoryCheck(g_stmt_async, true);

    // 事务提交
    testTransCommitAsync(g_conn_async);

    char *container_contentJson = NULL;
    readJanssonFile("SubtreeReplyJson/container_chioce_case133.json", &container_contentJson);
    ASSERT_NE((void *)NULL, container_contentJson);
    subtreeQuery(g_stmt_async, container_contentJson);
    AW_FUN_Log(LOG_STEP, "test end");
    free(container_contentJson);
    for (int i = 0; i < MAX_CONN - existConnNum; i++) {
        testGmcDisconnect(conn[i], stmt[i]);
    }
}

void *List1MandatoryDML(void *args)
{

    uint32_t ret;
    GmcStmtT *stmt_async[100];
    GmcConnT *conn_async[100];
    GmcStmtT *stmt_list = NULL;
    GmcBatchT *batch = NULL;
    int index = *(int *)args;
    AsyncUserDataT data{0};
    GmcNodeT *rootNode[10] = {0};
    GmcNodeT *childNode[40] = {0};
    GmcNodeT *listChildNode[40] = {0};

    ret = createEpollOneThread(&g_epAsync[index]);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "[thread_one_epoll_DML2] g_epAsync[%d].userEpollFd : %d", index, g_epAsync[index].userEpollFd);
    // 建立连接
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[index].userEpollFd;
    ret = TestYangGmcConnect(&conn_async[index], &stmt_async[index], GMC_CONN_TYPE_ASYNC, &connOptions);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async[index], &stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "线程listMandatory建连成功%d",index);
    // 可重复读+乐观
    // use namespace
    ret = GmcUseNamespaceAsync(stmt_async[index], g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 對list節點進行操作
    //  开启事务 开启diff 进行批操作
    ret = GmcTransStartAsync(conn_async[index], &g_mSTrxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "线程listMandatory开启事务成功%d",index);

    // 设置批处理batch参数
    ret = testBatchPrepare(conn_async[index], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入root节点数据******************************* */
    // 插入数据
    AW_FUN_Log(LOG_STEP, "线程listMandatory%d开启事务成功%d", index);
    ret = testGmcPrepareStmtByLabelName(stmt_async[index], "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_async[index]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_async[index], &rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t fieldValue = 100;
    ret = testYangSetField(
        rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode[1], "choice", GMC_OPERATION_MERGE, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(childNode[1], "case1", GMC_OPERATION_MERGE, &childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF0 = 100;
    ret = testYangSetField(
        childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt_async[index]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_chioce——case
    ret = testGmcPrepareStmtByLabelName(stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, stmt_async[index], stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_list, &childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(childNode[7], "listchoice", GMC_OPERATION_MERGE, &childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(childNode[6], "listcase1", GMC_OPERATION_MERGE, &childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        childNode[4], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        childNode[4], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(childNode[7], "listchoice", GMC_OPERATION_MERGE, &childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    memset(&data, 0, sizeof(AsyncUserDataT));
     ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    std::atomic_uint32_t step{0};
    ValidateParam param = {.step = &step, .exceptStatus = GMERR_OK, .validateRes = true};
    AW_FUN_Log(LOG_STEP, "mandatory校验%d", index);

    // subtreeQuery查询
    char *suntreeReturnJson = R"(
        {
    "F0": 100,
    "P2": 100,
    "choice": {
        "case1": {
            "F0": 100,
            "F1": 100
        }
    },
    "list": [
        {
            "F0": 100,
            "F1": 100,
            "listchoice": {
                "listcase1": {
                    "F0": 100,
                    "F1": 100
                }
            }
        }
    ]
}
    )";
    char *containerContentJson = NULL;
    readJanssonFile("SubTreeFilterJson/containerleaf_Listnode.json", &containerContentJson);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "root";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);

    // 此处的GmcSubtreeFilterT 里需要包含filter
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,  // 包含GmcSubtreeFilterItemT 的 filter
    };

    char *reply = NULL;
    SubtreeFilterCbParam data1 = {0};
    data1.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    data1.expectStatus = GMERR_OK;              // 预期服务端处理结果
    data1.step = 0;                             // 回调执行次数
    ret = GmcYangSubtreeFilterExecuteAsync(stmt_async[index], &filters, NULL, AsyncSubtreeFilterCb, &data1);
    ret = testWaitAsyncSubtreeRecv(&data1, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.expectStatus);
    free(containerContentJson);

    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ret = GmcYangValidateAsync(stmt_async[index], &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitValidateAsyncRecv(&param, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);
    memset(&param, 0, sizeof(ValidateParam));

    // 回滚事务
    ret = GmcTransRollBackAsync(conn_async[index], trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 关闭连接
    GmcFreeStmt(stmt_list);
    ret = testGmcDisconnect(conn_async[index], stmt_async[index]);
    EXPECT_EQ(GMERR_OK, ret);
    conn_async[index] = NULL;
    stmt_async[index] = NULL;
    closeEpollOneThread(&g_epAsync[index]);
    AW_FUN_Log(LOG_STEP, "线程listMandatory断连成功%d", index);
}

void *Container2MandatoryDML(void *args)
{
    uint32_t ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_async[100];
    GmcConnT *conn_async[100];
    AsyncUserDataT data{0};
    GmcNodeT *rootNode[10] = {0};
    GmcNodeT *childNode[40] = {0};
    GmcNodeT *listChildNode[40] = {0};

    int index = *(int *)args;
    ret = createEpollOneThread(&g_epAsync[index]);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_DEBUG, "[thread_one_epoll_DML2] g_epAsync[%d].userEpollFd : %d\n", index, g_epAsync[index].userEpollFd);
    // 建立连接
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[index].userEpollFd;
    ret = TestYangGmcConnect(&conn_async[index], &stmt_async[index], GMC_CONN_TYPE_ASYNC, &connOptions);
    EXPECT_EQ(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "线程containerMandatory%d建连成功", index);
    // 可重复读+乐观
    // use namespace
    ret = GmcUseNamespaceAsync(stmt_async[index], g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "线程开启事务成功");
    //  开启事务 开启diff 进行批操作 
    ret = GmcTransStartAsync(conn_async[index], &g_mSTrxConfig, trans_start_callback, &data);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 设置批处理batch参数
    ret = testBatchPrepare(conn_async[index], &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "线程containerMandatory%d插入数据", index);
    insertContainerChioceCase2(batch, stmt_async[index]);
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data.succNum, data.totalNum);
    memset(&data, 0, sizeof(AsyncUserDataT));

    std::atomic_uint32_t step{0};
    ValidateParam param = {.step = &step, .exceptStatus = GMERR_OK, .validateRes = true};
    AW_FUN_Log(LOG_STEP, "mandatory校验%d", index);

    char *suntreeReturnJson = R"(
        {
    "F0": 100,
    "P2": 100,
    "choice": {
        "case1": {
            "F0": 102,
            "F1": 102
        }
    }
}
    )";
    char *containerContentJson = NULL;
    readJanssonFile("SubTreeFilterJson/containerleaf_Listnode.json", &containerContentJson);
    AW_MACRO_EXPECT_NE_INT((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "root";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);

    // 此处的GmcSubtreeFilterT 里需要包含filter
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,  // 包含GmcSubtreeFilterItemT 的 filter
    };

    char *reply = NULL;
    SubtreeFilterCbParam data1 = {0};
    data1.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    data1.expectStatus = GMERR_OK;              // 预期服务端处理结果
    data1.step = 0;                             // 回调执行次数
    ret = GmcYangSubtreeFilterExecuteAsync(stmt_async[index], &filters, NULL, AsyncSubtreeFilterCb, &data1);
    ret = testWaitAsyncSubtreeRecv(&data1, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.expectStatus);
    free(containerContentJson);

    GmcValidateConfigT cfg = {.type = GMC_YANG_VALIDATION_MANDATORY, .cfgJson = NULL};
    ret = GmcYangValidateAsync(stmt_async[index], &cfg, AsyncValidateCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitValidateAsyncRecv(&param, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, param.exceptStatus);
    memset(&param, 0, sizeof(ValidateParam));
    // 回滚事务
    ret = GmcTransRollBackAsync(conn_async[index], trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, g_epAsync[index].userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "线程containerMandatory事务回滚成功%d", index);

    // 关闭连接
    ret = testGmcDisconnect(conn_async[index], stmt_async[index]);
    EXPECT_EQ(GMERR_OK, ret);
    conn_async[index] = NULL;
    stmt_async[index] = NULL;
    AW_FUN_Log(LOG_STEP, "线程containerMandatory断连成功");
}

void *Container3MandatoryDML(void *args)
{
    uint32_t ret;
    GmcBatchT *batch = NULL;
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    GmcNodeT *rootNode[10] = {0};
    GmcNodeT *childNode[40] = {0};
    GmcNodeT *listChildNode[40] = {0};
    AsyncUserDataT data{0};
    // 建立连接
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.线程container3Mandatory建连成功");
    // 可重复读+乐观
    // use namespace
    ret = GmcUseNamespaceAsync(stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 對container節點進行操作
    insertContainerChioceCase(batch, stmt_async);
    mandatoryCheck(stmt_async, true);

    // 删除case节点(删除部分数据)
    ret = testGmcPrepareStmtByLabelName(stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_async, &rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode[1], "choice", GMC_OPERATION_NONE, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(childNode[1], "case1", GMC_OPERATION_MERGE, &childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF0 = 100;
    ret = testYangSetField(
        childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 1);

    mandatoryCheck(stmt_async, false);

    // 删除case节点(删除case整个节点)
    ret = testGmcPrepareStmtByLabelName(stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt_async, &rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(rootNode[1], "choice", GMC_OPERATION_NONE, &childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(childNode[1], "case1", GMC_OPERATION_DELETE_GRAPH, &childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWait(batch, 1);

    mandatoryCheck(stmt_async, false);

    // 回滚事务
    ret = GmcTransRollBackAsync(conn_async, trans_rollback_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "线程container3Mandatory事务回滚成功");
    // 关闭连接
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    conn_async = NULL;
    stmt_async = NULL;
    AW_FUN_Log(LOG_STEP, "线程containerMandatory断连成功");
}

void *List4MandatoryDML(void *args)
{

    uint32_t ret;
    GmcStmtT *stmt_async = NULL;
    GmcConnT *conn_async = NULL;
    AsyncUserDataT data{0};
    // 建立连接
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "线程listMandatory建连成功");
    // 可重复读+乐观
    // use namespace
    ret = GmcUseNamespaceAsync(stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 對list節點進行操作

    // 关闭连接
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    conn_async = NULL;
    stmt_async = NULL;
    AW_FUN_Log(LOG_STEP, "线程listMandatory断连成功");
}
/* ****************************************************************************
 Description  :  136.线程数为10，每个线程开启事务，循环1000次执行六原语操作进行mandatory校验，线程一提交，其它线程回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_136)
{
    // 建表
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    int32_t thread_num = 10;
    // 后台缩容线程拿锁失败
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
#ifdef ENV_RTOSV2X
    thread_num = 5;
#endif
    pthread_t Dml[thread_num];

    readJanssonFile("schema_file/list_mandatory.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(vlabelSchema);
    AW_FUN_Log(LOG_STEP, "建树模型表list_mandatory完成");

    // 分别对表数据进行操作
    // 开启多个线程
    int num[thread_num];
    g_isOneThreadEpoll = true;
    for (uint32_t i = 0; i < thread_num; i++) {
        num[i] = i;
        if (i % 2 == 0) {
            ret = pthread_create(&Dml[i], NULL, List1MandatoryDML, (void *)&num[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = pthread_create(&Dml[i], NULL, Container2MandatoryDML, (void *)&num[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    for (int i = 0; i < thread_num; i++) {
        pthread_join(Dml[i], NULL);
    }
    g_isOneThreadEpoll = false;
}

/* ****************************************************************************
 Description  :
137.开启两个线程，线程一事务内执行六原语，线程二事务内循环100次执行六原语，两个线程含冲突数据，线程一提交，线程二回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_137)
{
    // 建表
    // 创建Vertex表
    int pthread_1=0;
    int pthread_2=1;
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    pthread_t Dml[2];

    readJanssonFile("schema_file/list_mandatory.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);
    AW_FUN_Log(LOG_STEP, "建树模型表list_mandatory完成");
    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 分别对表数据进行操作
    // 开启多个线程

    ret = pthread_create(&Dml[0], NULL, List1MandatoryDML, &pthread_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = pthread_create(&Dml[1], NULL, Container2MandatoryDML, &pthread_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 2; i++) {
        pthread_join(Dml[i], NULL);
    }
}

/* ****************************************************************************
 Description  :  138.树模型container_choice_case,存在两个case(case1,case2),循环依次插入case1,case2相同数据1000000次
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_138)
{

    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    AW_FUN_Log(LOG_STEP, "test start");

    readJanssonFile("schema_file/Container_Choice_Case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);
    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "建表完成");
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 预置数据
    insertContainerChioceCase(batch, g_stmt_async);

    // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/list_thity_one_contaimer.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    free(suntreeReturnJson);
    subtreeQuery(g_stmt_async);
    sleep(1);
    AW_FUN_Log(LOG_STEP, "预置数据完成");
    // 分别插入case1、case2
    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // root节点none操作choice节点none操作，case循环insert
    uint32_t cycle_num = 0;
    AW_FUN_Log(LOG_STEP, "循环向两个case中插入数据");
    while (cycle_num < 10000) {
        cycle_num++;
        // 设置根节点
        // 设置批处理batch参数
        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child choice节点
        ret = GmcYangEditChildNode(g_rootNode[1], "choice", GMC_OPERATION_NONE, &g_rootNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child case节点
        ret = GmcYangEditChildNode(g_rootNode[2], "case2", GMC_OPERATION_INSERT, &g_rootNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        uint32_t newvalue = 200;
        testYangSetNodeProperty(g_rootNode[3], newvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child choice节点
        ret = GmcYangEditChildNode(g_rootNode[1], "choice", GMC_OPERATION_NONE, &g_rootNode[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child case节点
        ret = GmcYangEditChildNode(g_rootNode[2], "case1", GMC_OPERATION_INSERT, &g_rootNode[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        newvalue = 200;
        testYangSetNodeProperty(g_rootNode[3], newvalue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(2, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(2, data.succNum);
        GmcBatchDestroy(batch);

        if (cycle_num % 10000 == 0) {
            mandatoryCheck(g_stmt_async, true);
        }
        memset(&data, 0, sizeof(AsyncUserDataT));
    }

    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :  139.树模型list_choice_case,存在两个case(case1,case2),循环依次插入case1,case2相同数据1000000次
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_139)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/list_chioce_case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);
    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "建树模型表con_con完成");

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入root节点数据******************************* */
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "choice", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "case1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF0 = 100;
    ret = testYangSetField(
        g_childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_chioce——case
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[7], "listchoice", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "listcase1", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[4], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[4], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[7], "listchoice", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "listcase2", GMC_OPERATION_MERGE, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[5], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[5], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "listcase2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[3], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[3], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 2);
    mandatoryCheck(g_stmt_async, true);
    // 事务提交
    testTransCommitAsync(g_conn_async);
}

/* ****************************************************************************
 Description  :  140.树模型container_choice_case,结构满足mandatory，插入父节点数据，调用mandatory校验接口1000000次，
 插入case数据调用校验接口1000000次，删除case数据，调用接口1000000次,删除container数据，调用接口1000000次
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_140)
{

    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    uint32_t cycle_num = 0;
    AsyncUserDataT data{0};
    AW_FUN_Log(LOG_STEP, "test start");

    readJanssonFile("schema_file/Container_Choice_Case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);
    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入root
    AW_FUN_Log(LOG_STEP, "插入root");
    insertContainerChioceCase(batch, g_stmt_async, true);
    cycle_num = 0;
    while (cycle_num < 10000) {
        cycle_num++;
        mandatoryCheck(g_stmt_async, true);
    }
    // 插入chioce
    AW_FUN_Log(LOG_STEP, "插入chioce");
    insertContainerChioceCase(batch, g_stmt_async, false, true);
    cycle_num = 0;
    while (cycle_num < 10000) {
        cycle_num++;
        mandatoryCheck(g_stmt_async, true);
    }
    // case no f0
    AW_FUN_Log(LOG_STEP, "case no f0");
    insertContainerChioceCase(batch, g_stmt_async, false, false, true);
    cycle_num = 0;
    while (cycle_num < 10000) {
        cycle_num++;
        mandatoryCheck(g_stmt_async, false);
    }
    // case all in
    AW_FUN_Log(LOG_STEP, "delete case");
    insertContainerChioceCase(batch, g_stmt_async);
    cycle_num = 0;
    while (cycle_num < 10000) {
        cycle_num++;
        mandatoryCheck(g_stmt_async, true);
    }

    // delete case
    AW_FUN_Log(LOG_STEP, "delete case");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "choice", GMC_OPERATION_NONE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置child case节点
    ret = GmcYangEditChildNode(g_childNode[1], "case1", GMC_OPERATION_REMOVE_GRAPH, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 1);
 
    while (cycle_num < 10000) {
        cycle_num++;
        mandatoryCheck(g_stmt_async, true);
    }
    
    // delete root
    AW_FUN_Log(LOG_STEP, "delete root");
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    testWait(batch, 1);
 
    while (cycle_num < 10000) {
        cycle_num++;
        mandatoryCheck(g_stmt_async, true);
    }
     // 事务提交
    testTransCommitAsync(g_conn_async);
    char *suntreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/reply140.json", &suntreeReturnJson);
    ASSERT_NE((void *)NULL, suntreeReturnJson);
    subtreeQuery(g_stmt_async);
    free(suntreeReturnJson);

    memset(&data, 0, sizeof(AsyncUserDataT));
}

/* ****************************************************************************
 Description  :  141.树模型list_choice_case,结构满足mandatory，插入父节点数据，调用mandatory校验接口1000000次，
 插入case数据调用校验接口1000000次，删除case数据，调用接口1000000次，删除list数据，调用接口1000000次
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_141)
{
    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/list_chioce_case.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(vlabelSchema);
    ret = TestCheckValidateModelAsync(g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "建树模型表con_con完成");

    // 开启事务 开启diff 进行批操作
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* *********************************插入root节点数据******************************* */
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &g_rootNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    uint32_t fieldValue = 100;
    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_rootNode[1], GMC_DATATYPE_UINT32, &fieldValue, sizeof(uint32_t), "P2", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_rootNode[1], "choice", GMC_OPERATION_MERGE, &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[1], "case1", GMC_OPERATION_MERGE, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF0 = 100;
    ret = testYangSetField(
        g_childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[2], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // list_chioce——case
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, "list", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_childNode[7]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[7], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[7], "listchoice", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "listcase1", GMC_OPERATION_MERGE, &g_childNode[4]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[4], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[4], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[7], "listchoice", GMC_OPERATION_MERGE, &g_childNode[6]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "listcase2", GMC_OPERATION_MERGE, &g_childNode[5]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[5], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[5], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[6], "listcase2", GMC_OPERATION_MERGE, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[3], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangSetField(
        g_childNode[3], GMC_DATATYPE_UINT32, &valueF0, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testWait(batch, 2);
    mandatoryCheck(g_stmt_async, true);
    // 事务提交
    testTransCommitAsync(g_conn_async);
}


/* ****************************************************************************
 Description  :   142.list节点id字段不设置自增预期报错
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_142){

     // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};

    readJanssonFile("schema_file/list_chioce_case_error.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    // 添加日志检测白名单
    AddErrWhiteLst(GMERR_INVALID_TABLE_DEFINITION);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    GmcGetLastError();
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, data.status);

    // 开启事务 开启diff 进行批操作

    free(vlabelSchema);
    AW_FUN_Log(LOG_STEP, "建树模型表con_list完成");
    
}

/* ****************************************************************************
 Description  :   143.container节点id字段设置自增预期报错
 Author       : youwanyong
**************************************************************************** */
TEST_F(YangTreeSupportMandatoryCheck_test, Yang_020_143){

    // 创建Vertex表
    char *vlabelSchema = NULL;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data{0};
    // 添加日志检测白名单
    AddErrWhiteLst(GMERR_INVALID_TABLE_DEFINITION);
    readJanssonFile("schema_file/list_chioce_case_error2.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);

    ret = GmcCreateVertexLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);

    GmcGetLastError();
    // 边表
    free(vlabelSchema);
    vlabelSchema = NULL;
    readJanssonFile("schema_file/list_chioce_case_edge.gmjson", &vlabelSchema);
    ASSERT_NE((void *)NULL, vlabelSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, vlabelSchema, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, data.status);

    free(vlabelSchema);
    AW_FUN_Log(LOG_STEP, "建树模型表con_list完成");
    
}
