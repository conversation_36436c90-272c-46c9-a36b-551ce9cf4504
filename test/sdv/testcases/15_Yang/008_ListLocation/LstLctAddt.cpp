/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "ListLocation.h"

class ListLocationAdditional : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void ListLocationAdditional::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\" ");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void ListLocationAdditional::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void ListLocationAdditional::SetUp()
{
    int ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "Namespace008";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};  // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // .建表 建边
    readJanssonFile("schema/list_location_vertex_label.json", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    AsyncUserDataT data{0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelConfig, create_vertex_label_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    free(g_vertexschema);

    readJanssonFile("schema/list_location_edge_label.json", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelConfig, create_vertex_label_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    ASSERT_EQ(GMERR_OK, data.status);
    free(g_edgeschema);
    // alloc all stmt
    TestyangallocAllstmt();
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

void ListLocationAdditional::TearDown()
{
    AW_CHECK_LOG_END();
    const char *namespace1 = "Namespace008";
    AsyncUserDataT userData = {0};
    int ret;
    // 释放all stmt
    TestyangfreeAllstmt();

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 * Description  : 055.GmcYangExecuteSubtreeFilterAsync 接口GmcSubtreeFilterT结
 构体isLocationFilter字段使用宏GMC_LOCATION_FILTER设值为执行list元素的位置查询操
 作，list元素对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);
    
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/02_only_real_key.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/02_only_real_key.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 056.GmcYangExecuteSubtreeFilterAsync 接口GmcSubtreeFilterT结
 构体isLocationFilter字段使用宏GMC_LOCATION_FILTER设值为执行list元素的位置查询操
 作，list元素不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/11_key_not_match.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/11_key_not_match.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 057.GmcYangExecuteSubtreeFilterAsync 接口GmcSubtreeFilterT结
 构体isLocationFilter字段使用宏GMC_LOCATION_FILTER设值为执行subtree过滤查询，list
 元素对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/sub_match.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/02_only_real_key.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 058.GmcYangExecuteSubtreeFilterAsync 接口GmcSubtreeFilterT结
 构体isLocationFilter字段使用宏GMC_LOCATION_FILTER设值为执行subtree过滤查询，list
 元素不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/11_key_not_match.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/11_key_not_match.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.defaultMode = GMC_DEFAULT_FILTER_TRIM;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 059. 查询list key元素对应且为第一条list key元素
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/02_only_real_key_front.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/02_only_real_key_front.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 060. 查询list key元素对应且前后都存在list key元素
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/02_only_real_key.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/02_only_real_key.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 061. 查询list key元素对应且为最后一条list key元素
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/02_only_real_key_back.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/02_only_real_key_back.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 062. 查询list key元素为两个且两个key元素均对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/12_two_or_condition_and_key_all_real.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/12_two_or_condition_and_key_all_real.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 063. 查询list key元素为两个且第一个key元素对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/12_two_or_condition_and_key_all_real.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/12_two_or_condition_and_first_key_real.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 064. 查询list key元素为两个且第二个key元素对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/01_key_null.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/12_two_or_condition_and_last_key_real.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 065. 查询list key元素为两个且两个key元素均不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/01_key_null.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/12_two_or_condition_and_no_key_real.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 066. 查询list key元素为empty
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/06_key_empty.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/06_key_empty.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 067. 查询树未提供key也未提供其它过滤条件
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/01_key_null.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/01_key_null.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 068. 查询树中key元素为empty非key子节点存在且对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/07_key_and_other_field_all_empty.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/07_key_empty_other_field_true.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 069. 查询树中key元素为empty非key子节点存在且不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/11_key_not_match.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/07_key_empty_other_field_false.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 070. 查询树中key元素为empty非key子节点为empty
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/07_key_and_other_field_all_empty.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/07_key_and_other_field_all_empty.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 071. 查询树中key元素为null非key子节点存在且对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/10_key_null_and_other_field_real.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/10_key_null_and_other_field_real.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 072. 查询树中key元素为null非key子节点存在且不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/11_key_not_match.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/10_key_null_and_other_field_fake.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 073. 查询树中key元素为null非key子节点为empty
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/07_key_and_other_field_all_empty.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/10_key_null_and_other_field_empty.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 074. 查询树中非key子节点存在且对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/03_key_and_other_field_all_real.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/03_key_and_other_field_all_real.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 075. 查询树中非key子节点存在且不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/05_key_real_but_other_field_not_match.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/05_key_real_but_other_field_not_match.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 076. 查询树中非key子节点为empty
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/04_key_real_and_other_field_empty.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/04_key_real_and_other_field_empty.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 077. 查询树中子节点过滤条件对应且被查询树中目标key元素的兄弟key
 元素的子节点也对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/23_nested_nomal_node_all.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/23_nested_nomal_node_all.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 078. 查询树中子节点过滤条件对应而被查询树中目标key元素的兄弟key
 元素的子节点部分对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/23_nested_nomal_node_part.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/23_nested_nomal_node_part.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 079. 查询树中子节点过滤条件对应而被查询树中目标key元素的兄弟key
 元素的子节点都不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/23_nested_nomal_node_none.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/23_nested_nomal_node_none.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 080. 查询树中子节点过滤条件不对应而被查询树中目标key元素的兄弟key
 元素的子节点对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/23_nested_nomal_node_all_wrong_key.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/23_nested_nomal_node_all_wrong_key.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 081. 查询树中子节点过滤条件不对应而被查询树中目标key元素的兄弟key
 元素的子节点部分对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/23_nested_nomal_node_part_wrong_key.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/23_nested_nomal_node_part_wrong_key.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 082. 查询树中子节点过滤条件不对应且被查询树中目标key元素的兄弟key
 元素的子节点都不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/23_nested_nomal_node_none_wrong_key.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/23_nested_nomal_node_none_wrong_key.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 083. 查询树中子节点过滤条件为empty且被查询树中目标key元素的兄弟
 key元素与目标key元素子节点全部一致
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/23_nested_nomal_node_all_nokey.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/23_nested_nomal_node_all_nokey.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 084. 查询树中子节点过滤条件为empty而被查询树中目标key元素的兄弟
 key元素与目标key元素子节点部分一致
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/23_nested_nomal_node_part_nokey.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/23_nested_nomal_node_part_nokey.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 085. 查询树中子节点过滤条件为empty而被查询树中目标key元素的兄弟
 key元素与目标key元素子节点均不一致
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/23_nested_nomal_node_none_nokey.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/23_nested_nomal_node_none_nokey.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 086. 查询树中key元素子节点list的key元素对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/16_nested_list_key_real.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/16_nested_list_key_real.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 087. 查询树中key元素子节点list的key元素不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/16_nested_list_key_fake.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/16_nested_list_key_fake.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 088. 查询树中key元素子节点list的key元素为empty
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/16_nested_list_key_empty.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/16_nested_list_key_empty.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 089. 查询树中key元素子节点list的key元素为null
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);
    // 开启事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/16_nested_list_key_null.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
    // 回滚事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 * Description  : 090. 查询树中key元素子节点list的key元素对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/31_between_two_key_had_nomal_real.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/31_between_two_key_had_nomal_real.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 091. 查询树中key元素子节点list的key元素不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/31_between_two_key_had_nomal_fake.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/31_between_two_key_had_nomal_fake.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 092. 查询树中key元素子节点list的key元素为empty
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/31_between_two_key_had_nomal_empty.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/31_between_two_key_had_nomal_empty.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 093. 查询树中key元素子节点list的key元素为null
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/31_between_two_key_had_nomal_null.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 094. 查询树中有两个list，两个list的key元素均对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/32_tow_list_all_real.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/32_tow_list_all_real.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 095. 查询树中有两个list，第一个list的key元素对应，第二个list的
 key元素不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/32_tow_list_last_fake.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/32_tow_list_last_fake.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 096. 查询树中有两个list，第一个list的key元素不对应，第二个list
 的key元素对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/32_tow_list_first_fake.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/32_tow_list_first_fake.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 097. 查询树中有两个list，两个list的key 元素均不对应
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/32_tow_list_all_fake.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/32_tow_list_all_fake.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 098. 查询树list父节点匹配失败
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/01_key_null.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/err_33_between_two_key_fake.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 099. list节点移动过后，再进行list位置查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcBatchT *batch = NULL;

    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //进行节点移动
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L1, g_vertexLabelT1L1, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t T1_L1_mk = 1;
    GmcYangListLocatorT listProp;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &T1_L1_mk);
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
    ret = GmcYangSetListLocator(g_stmt_async_T1_L1, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);
    T1_L1_mk = 2;
    uint32_t pid = 1;
    testSetKeyNameAndValue(g_stmt_async_T1_L1, T1_L1_mk, pid);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async_T1_L1, 1, GMC_DATATYPE_UINT32, &T1_L1_mk, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchAddDML(batch, g_stmt_async_T1_L1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //进行节点移动
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L1, g_vertexLabelT1L1, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    T1_L1_mk = 1;
    InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
    ret = GmcYangSetListLocator(g_stmt_async_T1_L1, &listProp);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    UninitListProperty(&listProp);

    T1_L1_mk = 2;
    testSetKeyNameAndValue(g_stmt_async_T1_L1, T1_L1_mk, pid);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async_T1_L1, 1, GMC_DATATYPE_UINT32, &T1_L1_mk, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchAddDML(batch, g_stmt_async_T1_L1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 事务提交
    testTransCommitAsync(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/02_only_real_key.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/02_only_real_key.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 100. list节点进行六原语操作后，再进行list位置查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);
    testYangPresetAllDateDML(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/22_list_node_parent_has_other_match_failed.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/test.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 101. 被查询树内无list节点
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 开启事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *rootNode = NULL;
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async_T0, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t pkvalue = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &pkvalue, sizeof(uint32_t), "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F1 = 2;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置T0::T1_C的字段值，T0下的子节点container
    GmcNodeT *childNodeT1C = NULL;
    ret = GmcYangEditChildNode(rootNode, g_vertexLabelT1C, GMC_OPERATION_INSERT, &childNodeT1C);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F10 = 11;
    ret = testYangSetNodePropertyNew(
        childNodeT1C, GMC_DATATYPE_UINT32, &F10, sizeof(uint32_t), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/34_no_list_tree.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/34_no_list_tree.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
    rootNode = NULL;
    childNodeT1C = NULL;
}

/* ****************************************************************************
 * Description  : 102. 字段属性为int32_t时进行list位置查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 开启事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async_T0, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t pkvalue = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &pkvalue, sizeof(uint32_t), "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F1 = 2;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置T0::T1_C的字段值，T0下的子节点container
    GmcNodeT *childNodeT1C = NULL;
    ret = GmcYangEditChildNode(rootNode, g_vertexLabelT1C, GMC_OPERATION_INSERT, &childNodeT1C);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t F10 = 11;
    ret = testYangSetNodePropertyNew(
        childNodeT1C, GMC_DATATYPE_UINT32, &F10, sizeof(uint32_t), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/34_no_list_tree.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/34_no_list_tree.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
    rootNode = NULL;
    childNodeT1C = NULL;
}

/* ****************************************************************************
 * Description  : 103. 字段属性为bool时进行list位置查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 开启事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async_T0, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t pkvalue = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &pkvalue, sizeof(uint32_t), "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F1 = 2;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置T0::T1_C的字段值，T0下的子节点container
    GmcNodeT *childNodeT1C = NULL;
    ret = GmcYangEditChildNode(rootNode, g_vertexLabelT1C, GMC_OPERATION_INSERT, &childNodeT1C);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F10 = 1;
    ret = testYangSetNodePropertyNew(
        childNodeT1C, GMC_DATATYPE_UINT32, &F10, sizeof(uint32_t), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/35_no_list_tree_bool.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/35_no_list_tree_bool.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
    rootNode = NULL;
    childNodeT1C = NULL;
}

/* ****************************************************************************
 * Description  : 104. 字段属性为uint_64时进行list位置查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 开启事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async_T0, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t pkvalue = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &pkvalue, sizeof(uint32_t), "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F1 = 2;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置T0::T1_C的字段值，T0下的子节点container
    GmcNodeT *childNodeT1C = NULL;
    ret = GmcYangEditChildNode(rootNode, g_vertexLabelT1C, GMC_OPERATION_INSERT, &childNodeT1C);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t F10 = 11;
    ret = testYangSetNodePropertyNew(
        childNodeT1C, GMC_DATATYPE_UINT32, &F10, sizeof(uint32_t), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/34_no_list_tree.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/34_no_list_tree.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
    rootNode = NULL;
    childNodeT1C = NULL;
}

/* ****************************************************************************
 * Description  : 105. 字段属性为double时进行list位置查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 开启事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async_T0, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t pkvalue = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &pkvalue, sizeof(uint32_t), "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F1 = 2;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置T0::T1_C的字段值，T0下的子节点container
    GmcNodeT *childNodeT1C = NULL;
    ret = GmcYangEditChildNode(rootNode, g_vertexLabelT1C, GMC_OPERATION_INSERT, &childNodeT1C);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    double F10 = 11;
    uint32_t newF10 = (uint32_t)F10;
    ret = testYangSetNodePropertyNew(
        childNodeT1C, GMC_DATATYPE_UINT32, &newF10, sizeof(uint32_t), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/34_no_list_tree.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/34_no_list_tree.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
    rootNode = NULL;
    childNodeT1C = NULL;
}

/* ****************************************************************************
 * Description  : 106. 字段属性为float时进行list位置查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 开启事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 启动事务
    ret = testTransStartAsync(g_conn_async, TrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async_T0, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t pkvalue = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &pkvalue, sizeof(uint32_t), "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F1 = 2;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置T0::T1_C的字段值，T0下的子节点container
    GmcNodeT *childNodeT1C = NULL;
    ret = GmcYangEditChildNode(rootNode, g_vertexLabelT1C, GMC_OPERATION_INSERT, &childNodeT1C);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    float F10 = 11;
    uint32_t newF10 = (uint32_t)F10;
    ret = testYangSetNodePropertyNew(
        childNodeT1C, GMC_DATATYPE_UINT32, &newF10, sizeof(uint32_t), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/34_no_list_tree.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);
    subtreeFilterCbParam Data = {0};
    Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
    Data.step = 0;                             // 回调执行次数

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/34_no_list_tree.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
    rootNode = NULL;
    childNodeT1C = NULL;
}

/* ****************************************************************************
 * Description  : 107. 静态循环10000次list位置查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char wordname[20] = "0";
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/02_only_real_key.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/02_only_real_key.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    int repeatTimes = 0;
#ifdef ENV_RTOSV2X
    repeatTimes = 2000;
#else
    repeatTimes = 10000;
#endif
    for (int i = 0; i < repeatTimes; i++) {
        subtreeFilterCbParam Data = {0};
        Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
        Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
        Data.step = 0;                             // 回调执行次数
        ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
        ret = testWaitAsyncSubtreeRecv(&Data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
        sprintf(wordname, "完成%d次", i);
        AW_FUN_Log(LOG_STEP, wordname);
    }
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}

/* ****************************************************************************
 * Description  : 108. 动态循环10000次list位置查询
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wangxuming/wwx1146477
 * Modification :
 * **************************************************************************** */
TEST_F(ListLocationAdditional, Yang_008_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int ret;
    char wordname[20] = "0";
    GmcBatchT *batch = NULL;
    // 模型树所有表均预置数据
    testYangPresetAllDate(g_conn_async);

    // subtree 查询
    char *suntreeReturnJson = NULL;
    readJanssonFile("schema/reply/02_only_real_key.json", &suntreeReturnJson);
    EXPECT_NE((void *)NULL, suntreeReturnJson);

    // 进行list位置查詢
    uint32_t isLocationFilter = GMC_LOCATION_FILTER;
    char *containerContentJson = NULL;
    readJanssonFile("schema/subtree/02_only_real_key.json", &containerContentJson);
    EXPECT_NE((void *)NULL, containerContentJson);
    GmcSubtreeFilterItemT filter = {0};
    filter.rootName = "T0";
    filter.subtree.json = containerContentJson;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.isLocationFilter = isLocationFilter;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    int repeatTimes = 0;
#ifdef ENV_RTOSV2X
    repeatTimes = 2000;
#else
    repeatTimes = 10000;
#endif
    for (int i = 0; i < repeatTimes; i++) {
        GmcTxConfigT TrxConfig;
        TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
        TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        TrxConfig.readOnly = false;
        TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
        // 启动事务
        ret = testTransStartAsync(g_conn_async, TrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置批处理batch参数
        ret = testBatchPrepare(g_conn_async, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置根节点 T0层
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_async_T0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_async_T0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //进行节点移动
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L1, g_vertexLabelT1L1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t T1_L1_mk = 1;
        GmcYangListLocatorT listProp;
        GmcPropValueT refKey;
        InitRefKeys(&refKey, 2, &T1_L1_mk);
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
        AddWhiteList(GMERR_INVALID_PARAMETER_VALUE);
        ret = GmcYangSetListLocator(g_stmt_async_T1_L1, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
        UninitListProperty(&listProp);
        T1_L1_mk = 2;
        uint32_t pid = 1;
        testSetKeyNameAndValue(g_stmt_async_T1_L1, T1_L1_mk, pid);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async_T1_L1, 1, GMC_DATATYPE_UINT32, &T1_L1_mk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchAddDML(batch, g_stmt_async_T1_L1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //进行节点移动
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L1, g_vertexLabelT1L1, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        T1_L1_mk = 1;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
        ret = GmcYangSetListLocator(g_stmt_async_T1_L1, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
        UninitListProperty(&listProp);

        T1_L1_mk = 2;
        testSetKeyNameAndValue(g_stmt_async_T1_L1, T1_L1_mk, pid);
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async_T1_L1, 1, GMC_DATATYPE_UINT32, &T1_L1_mk, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchAddDML(batch, g_stmt_async_T1_L1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AsyncUserDataT data = {0};
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(data.succNum, data.totalNum);
        GmcBatchDestroy(batch);
        memset(&data, 0, sizeof(AsyncUserDataT));

        subtreeFilterCbParam Data = {0};
        Data.expectReplyJson = suntreeReturnJson;  // 预期要的subtree返回结果
        Data.expectStatus = GMERR_OK;              // 预期服务端处理结果
        Data.step = 0;                             // 回调执行次数
        ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async_T0, &filters, NULL, AsyncSubtreeFilterCb, &Data);
        ret = testWaitAsyncSubtreeRecv(&Data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);
        sprintf(wordname, "完成%d次", i);
        AW_FUN_Log(LOG_STEP, wordname);

        // 事务提交
        testTransCommitAsync(g_conn_async);
    }
    testDropEdgeAndVertex(g_stmt_async, "Namespace008");
    free(suntreeReturnJson);
    free(containerContentJson);
}
