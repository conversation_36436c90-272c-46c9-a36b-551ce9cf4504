/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef LISTLOCATION_H
#define LISTLOCATION_H

extern "C" {}

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

#define MAX_DEPTH 48

GmcTxConfigT g_mSTrxConfig;
GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL;

char *g_vertexschema = NULL, *g_edgeschema = NULL;
const char *g_vertexLabelT0 = "T0";
const char *g_vertexLabelT1C = "T0::T1_C";
const char *g_vertexLabelT2L2 = "T0::T1_C::T2_L2";
const char *g_vertexLabelT1L1 = "T0::T1_L1";
const char *g_vertexLabelT2C = "T0::T1_L1::T2_C";
const char *g_vertexLabelT3L1 = "T0::T1_L1::T2_C::T3_L1";
const char *g_vertexLabelT3L2 = "T0::T1_L1::T2_C::T3_L2";
const char *g_vertexLabelT2L1 = "T0::T1_L1::T2_L1";
const char *g_vertexLabelT3L3 = "T0::T1_L1::T2_L1::T3_L3";
const char *g_vertexLabelT1L2 = "T0::T1_L2";
const char *g_vertexLabelT1L3 = "T0::T1_L3";

const char *g_edgeLabelT0T1C = "T0_T1_C";
const char *g_edgeLabelT1CT2L2 = "T1_C_T2_L2";
const char *g_edgeLabelT0T1L1 = "T0_T1_L1";
const char *g_edgeLabelT1L1T2C = "T1_L1_T2_C";
const char *g_edgeLabelT2CT3L1 = "T2_C_T3_L1";
const char *g_edgeLabelT2CT3L2 = "T2_C_T3_L2";
const char *g_edgeLabelT1L1T2L1 = "T1_L1_T2_L1";
const char *g_edgeLabelT2L1T3L3 = "T2_L1_T3_L3";
const char *g_edgeLabelT0T1L2 = "T0_T1_L2";
const char *g_edgeLabelT0T1L3 = "T0_T1_L3";

const char *g_labelConfig = "{\"max_record_count\" : 10000, \"isFastReadUncommitted\":0,"
                            " \"auto_increment\":1, \"hash_type\": \"cceh\", \"yang_model\":1}";

const char *g_keyName = "PK";
// 申请各个表用的stmt
GmcStmtT *g_stmt_async_T0 = NULL;
GmcStmtT *g_stmt_async_T1_C = NULL;
GmcStmtT *g_stmt_async_T2_L2 = NULL;
GmcStmtT *g_stmt_async_T1_L1 = NULL;
GmcStmtT *g_stmt_async_T2_C = NULL;
GmcStmtT *g_stmt_async_T3_L1 = NULL;
GmcStmtT *g_stmt_async_T3_L2 = NULL;
GmcStmtT *g_stmt_async_T2_L1 = NULL;
GmcStmtT *g_stmt_async_T3_L3 = NULL;
GmcStmtT *g_stmt_async_T1_L2 = NULL;
GmcStmtT *g_stmt_async_T1_L3 = NULL;
GmcStmtT *g_stmt_father = NULL;
GmcStmtT *g_stmt_child = NULL;
GmcStmtT *g_stmt_father1 = NULL;

char *g_suntreeReturnJson = NULL;
char *g_containerContentJson = NULL;
uint32_t isLocationFilter = GMC_LOCATION_FILTER;

void TestyangallocAllstmt()
{
    int ret = GmcAllocStmt(g_conn_async, &g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async_T1_C);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async_T2_L2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async_T2_C);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async_T3_L1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async_T3_L2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async_T2_L1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async_T3_L3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async_T1_L2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async_T1_L3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_father);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_child);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_father1);
    EXPECT_EQ(GMERR_OK, ret);
}
void TestyangfreeAllstmt()
{
    GmcFreeStmt(g_stmt_async_T0);
    GmcFreeStmt(g_stmt_async_T1_C);
    GmcFreeStmt(g_stmt_async_T2_L2);
    GmcFreeStmt(g_stmt_async_T1_L1);
    GmcFreeStmt(g_stmt_async_T2_C);
    GmcFreeStmt(g_stmt_async_T3_L1);
    GmcFreeStmt(g_stmt_async_T3_L2);
    GmcFreeStmt(g_stmt_async_T2_L1);
    GmcFreeStmt(g_stmt_async_T3_L3);
    GmcFreeStmt(g_stmt_async_T1_L2);
    GmcFreeStmt(g_stmt_async_T1_L3);
    GmcFreeStmt(g_stmt_father);
    GmcFreeStmt(g_stmt_child);
    GmcFreeStmt(g_stmt_father1);
}

// userData结构
struct subtreeFilterCbParam {
    int step;
    int32_t expectStatus;         // 预期的操作状态
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};
// userData ：用户数据 jsonReply ：服务端返回的子树 json status ：服务器端操作处理结果  errMsg ：错误信息
void AsyncSubtreeFilterCb(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    subtreeFilterCbParam *param = (subtreeFilterCbParam *)(userData);
    param->expectStatus = status;
    bool isEnd = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    // 将replyJson 与预期json比较
    if (param->expectReplyJson != NULL) {
        ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
        ASSERT_TRUE(isEnd);
        ASSERT_EQ(1, count);
        ASSERT_TRUE(jsonReply != NULL);
        bool jsonEqual = testYangJsonIsEqual((const char *)jsonReply[0], param->expectReplyJson);
        AW_MACRO_EXPECT_EQ_INT((uint32_t) true, (uint32_t)jsonEqual);
    }
    param->step++;
    if (GMERR_OK != status) {
        printf("[err] status is %d  errMsg  is %s   \n ", status, errMsg);
        return;
    }
}

int testWaitAsyncSubtreeRecv(void *userData, int expRecvNum = 1, int timeout = -1, bool isAutoReset = true)
{
    int waitCnt = 0;
    struct timeval start;
    struct timeval end;
    unsigned long duration;

    gettimeofday(&start, NULL);
    subtreeFilterCbParam *userdata1 = (subtreeFilterCbParam *)userData;
    while (userdata1->step != expRecvNum) {
        usleep(10);
        waitCnt++;
        if (timeout > 0 && waitCnt >= timeout) {
            gettimeofday(&end, NULL);
            duration = 1000000 * (end.tv_sec - start.tv_sec) + end.tv_usec - start.tv_usec;
            printf("[INFO] Recv Timeout %lf ", (double)duration / 1000000);
            return -1;  // 接收超时
        }
    }
    return 0;
}

int testTransStartAsync(GmcConnT *conn, GmcTxConfigT Config)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransStartAsync(conn, &Config, trans_start_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}
int testTransCommitAsync(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        return ret;
    }
}

int testTransCommitAsyncRollback(GmcConnT *conn)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = testWaitAsyncRecv(&data);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
        return ret;
    }
}

int testBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        EXPECT_EQ(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

// yang set 字段
int testYangSetField(
    GmcStmtT *stmt, GmcDataTypeE type, void *value, uint32_t size, const char *fieldName, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetVertexProperty(stmt, &propValue, optype);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

void InitRefKeys(GmcPropValueT *refKey, uint32_t propId, void *value, GmcDataTypeE type = GMC_DATATYPE_UINT32,
    uint32_t sizeValue = 4)
{
    refKey->propertyId = propId;
    refKey->propertyName[0] = '\0';
    refKey->type = type;
    refKey->size = sizeValue;
    refKey->value = value;
}

void InitListProperty(GmcYangListLocatorT *listProperty, GmcYangListPositionE position, GmcPropValueT *referenceKey)
{
    if (listProperty == NULL) {
        return;
    }
    listProperty->position = position;
    if (referenceKey != NULL) {
        listProperty->refKeyFields = (GmcPropValueT **)malloc(sizeof(GmcPropValueT *));
        listProperty->refKeyFields[0] = referenceKey;
        listProperty->refKeyFieldsCount = 1;
    } else {
        listProperty->refKeyFields = NULL;
        listProperty->refKeyFieldsCount = 0;
    }
}

void UninitListProperty(GmcYangListLocatorT *listProperty)
{
    if (listProperty->refKeyFields == NULL) {
        return;
    }
    free(listProperty->refKeyFields);
    listProperty->refKeyFields = NULL;
}

void testSetKeyNameAndValue(GmcStmtT *stmt, uint32_t keyvalue, uint32_t PID = 0)
{
    int ret;

    // 设置KeyValue，PID为上层节点的ID，自增从1开始，PID=0，代表是根节点
    // 六原语操作时不需要设置PID
    if (PID == 0) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void memPositionInsertFiveNormalData(GmcBatchT *batch, GmcStmtT *g_stmt_async_T0, GmcStmtT *g_stmt_async_T1_L1,
    uint32_t begin, uint32_t end, GmcYangListPositionE position, uint32_t keyvalue = 1)
{
    GmcYangListLocatorT listProp;
    uint32_t f0;
    int ret;

    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置key

    testSetKeyNameAndValue(g_stmt_async_T0, keyvalue);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcPropValueT refKey;
    for (uint32_t i = begin; i <= end; ++i) {
        // 设置child节点位置移动选择GMC_LIST_before
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L1, g_vertexLabelT1L1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L1);
        EXPECT_EQ(GMERR_OK, ret);
        f0 = 4;
        InitRefKeys(&refKey, 1, &f0);
        InitListProperty(&listProp, position, &refKey);
        ret = GmcYangSetListLocator(g_stmt_async_T1_L1, &listProp);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testYangSetField(g_stmt_async_T1_L1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t), "T1_L1_mk",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        EXPECT_EQ(GMERR_OK, ret);
        GmcBatchAddDML(batch, g_stmt_async_T1_L1);
        // printf("执行成功\n");
    }
}

// yang set T0层主键
void testYangSetVertexProperty_root_PK(GmcStmtT *stmt, uint32_t i, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    uint32_t PK_value = i;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t), "PK", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// yang set T0层一般字段
void testYangSetVertexProperty(GmcStmtT *stmt, uint32_t f0, uint32_t f1, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", optype);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f1, sizeof(uint32_t), "F1", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// yang set T1_C层 一般字段
void testYangSetVertexProperty_T1_C(GmcStmtT *stmt, uint32_t f10, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f10, sizeof(uint32_t), "F10", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// yang set T2_L2层 主鍵
void testYangSetVertexProperty_T2_L2_PK(GmcStmtT *stmt, uint32_t t2_l2_mk, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &t2_l2_mk, sizeof(uint32_t), "T2_L2_mk", optype);
    EXPECT_EQ(GMERR_OK, ret);
    // PID是不是不用set
}
// yang set T1_L1层 主鍵
void testYangSetVertexProperty_T1_L1_PK(GmcStmtT *stmt, uint32_t t1_l1_mk, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &t1_l1_mk, sizeof(uint32_t), "T1_L1_mk", optype);
    EXPECT_EQ(GMERR_OK, ret);
    // PID是不是不用set
}
// yang set T1_L1层 普通節點
void testYangSetVertexProperty_T1_L1(GmcStmtT *stmt, uint32_t f20, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f20, sizeof(uint32_t), "F20", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// yang set T2_C層 普通節點
void testYangSetVertexProperty_T2_C(GmcStmtT *stmt, uint32_t f200, uint32_t f201, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f201, sizeof(uint32_t), "F200", optype);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f201, sizeof(uint32_t), "F201", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// yang set T3_L1层 主鍵
void testYangSetVertexProperty_T3_L1_PK(GmcStmtT *stmt, uint32_t t3_l1_mk, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &t3_l1_mk, sizeof(uint32_t), "T3_L1_mk", optype);
    EXPECT_EQ(GMERR_OK, ret);
    // PID是不是不用set
}
// yang set T3_L1层
void testYangSetVertexProperty_T3_L1(GmcStmtT *stmt, uint32_t f2000, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f2000, sizeof(uint32_t), "F2000", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// yang set T3_L2層 主键
void testYangSetVertexProperty_T3_L2_PK(GmcStmtT *stmt, uint32_t t3_l2_mk, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &t3_l2_mk, sizeof(uint32_t), "T3_L2_mk", optype);
    EXPECT_EQ(GMERR_OK, ret);
    // PID是不是不用set
}
// yang set T3_L2層
void testYangSetVertexProperty_T3_L2(GmcStmtT *stmt, uint32_t f2000, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f2000, sizeof(uint32_t), "F2000", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// yang set T2_L1层 主鍵
void testYangSetVertexProperty_T2_L1_PK(GmcStmtT *stmt, uint32_t t2_l1_mk, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &t2_l1_mk, sizeof(uint32_t), "T2_L1_mk", optype);
    EXPECT_EQ(GMERR_OK, ret);
    // PID是不是不用set
}
// yang set T2_L1层
void testYangSetVertexProperty_T2_L1(GmcStmtT *stmt, uint32_t f200, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f200, sizeof(uint32_t), "F200", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// yang set T3_L3层 主鍵
void testYangSetVertexProperty_T3_L3_PK(GmcStmtT *stmt, uint32_t f2000, GmcYangPropOpTypeE optype)
{
    int ret = 0;
    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f2000, sizeof(uint32_t), "F2000", optype);
    EXPECT_EQ(GMERR_OK, ret);
    // PID是不是不用set
}
// yang set T1_L2层 主鍵
void testYangSetVertexProperty_T1_L2_PK(GmcStmtT *stmt, uint32_t f30, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f30, sizeof(uint32_t), "F30", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// yang set T1_L2层
void testYangSetVertexProperty_T2_L2(GmcStmtT *stmt, uint32_t f31, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f31, sizeof(uint32_t), "F31", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// yang set T1_L3层 主鍵
void testYangSetVertexProperty_T1_L3_PK(
    GmcStmtT *stmt, uint32_t t1_l3_mk_1, uint32_t t1_l3_mk_2, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &t1_l3_mk_1, sizeof(uint32_t), "T1_L3_mk_1", optype);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &t1_l3_mk_2, sizeof(uint32_t), "T1_L3_mk_2", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// yang set T1_L3层
void testYangSetVertexProperty_T1_L3(GmcStmtT *stmt, uint32_t f40, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    ret = testYangSetField(stmt, GMC_DATATYPE_UINT32, &f40, sizeof(uint32_t), "F40", optype);
    EXPECT_EQ(GMERR_OK, ret);
}
// 模型树直接预置 数据
void testYangPresetAllDate(GmcConnT *conn)
{
    int ret;
    // 开启事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async_T0, &rootNode);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t pkvalue = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &pkvalue, sizeof(uint32_t), "PK", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 1;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F1 = 2;
    ret = testYangSetNodePropertyNew(
        rootNode, GMC_DATATYPE_UINT32, &F1, sizeof(uint32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置T0::T1_C的字段值，T0下的子节点container
    GmcNodeT *childNodeT1C = NULL;
    ret = GmcYangEditChildNode(rootNode, g_vertexLabelT1C, GMC_OPERATION_INSERT, &childNodeT1C);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F10 = 11;
    ret = testYangSetNodePropertyNew(
        childNodeT1C, GMC_DATATYPE_UINT32, &F10, sizeof(uint32_t), "F10", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);

    // 句柄g_stmt_async_T2_L2  T0::T1_C::T2_L2
    GmcNodeT *childNodeT2L2 = NULL;
    for (uint32_t i = 1; i < 4; i++) {
        uint32_t T2_L2_mk = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T2_L2, g_vertexLabelT2L2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T2_L2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async_T2_L2, &childNodeT2L2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(childNodeT2L2, GMC_DATATYPE_UINT32, &T2_L2_mk, sizeof(uint32_t), "T2_L2_mk",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, g_stmt_async_T2_L2);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 句柄g_stmt_async_T1_L1  T0::T1_L1
    GmcNodeT *childNodeT1L1 = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L1, g_vertexLabelT1L1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async_T1_L1, &childNodeT1L1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // list 的值
    uint32_t T1_L1_mk = 1;
    ret = testYangSetNodePropertyNew(childNodeT1L1, GMC_DATATYPE_UINT32, &T1_L1_mk, sizeof(uint32_t), "T1_L1_mk",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t F20 = 211;
    ret = testYangSetNodePropertyNew(
        childNodeT1L1, GMC_DATATYPE_UINT32, &F20, sizeof(uint32_t), "F20", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置T0::T1_L1::T2_C的值
    GmcNodeT *childNodeT1L1T2C = NULL;
    ret = GmcYangEditChildNode(childNodeT1L1, g_vertexLabelT2C, GMC_OPERATION_INSERT, &childNodeT1L1T2C);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t F200 = 221;
    ret = testYangSetNodePropertyNew(
        childNodeT1L1T2C, GMC_DATATYPE_UINT32, &F200, sizeof(uint32_t), "F200", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);

    // 句柄g_stmt_async_T2_C  T0::T1_L1::T2_C::T3_L1
    GmcNodeT *childNodeT1L1T2CT3L1 = NULL;
    for (uint32_t i = 11; i < 14; i++) {
        uint32_t T3_L1_mk = i;
        uint32_t F2000 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T3_L1, g_vertexLabelT3L1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T1_L1, g_stmt_async_T3_L1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async_T3_L1, &childNodeT1L1T2CT3L1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(childNodeT1L1T2CT3L1, GMC_DATATYPE_UINT32, &T3_L1_mk, sizeof(uint32_t),
            "T3_L1_mk", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(childNodeT1L1T2CT3L1, GMC_DATATYPE_UINT32, &F2000, sizeof(uint32_t), "F2000",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async_T3_L1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 句柄g_stmt_async_T2_L1  T0::T1_L1::T2_L1
    uint32_t keyvalue = 1;
    F200 = 2101;
    uint32_t F2000 = 21001;
    GmcNodeT *childNodeT1L1T2L1 = NULL;
    GmcNodeT *childNodeT3L3 = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    testSetKeyNameAndValue(g_stmt_async_T0, keyvalue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L1, g_vertexLabelT1L1, GMC_OPERATION_NONE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);
    T1_L1_mk = 1;
    testSetKeyNameAndValue(g_stmt_async_T1_L1, T1_L1_mk, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 1; i < 4; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T2_L1, g_vertexLabelT2L1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T1_L1, g_stmt_async_T2_L1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async_T2_L1, &childNodeT1L1T2L1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // list 的值
        uint32_t T2_L1_mk = i;
        ret = testYangSetNodePropertyNew(childNodeT1L1T2L1, GMC_DATATYPE_UINT32, &T2_L1_mk, sizeof(uint32_t),
            "T2_L1_mk", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(childNodeT1L1T2L1, GMC_DATATYPE_UINT32, &F200, sizeof(uint32_t), "F200",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async_T2_L1);
        EXPECT_EQ(GMERR_OK, ret);
        // 句柄g_stmt_async_T3_L3  T0::T1_L1::T2_L1::T3_L3
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T3_L3, g_vertexLabelT3L3, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T2_L1, g_stmt_async_T3_L3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async_T3_L3, &childNodeT3L3);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(
            childNodeT3L3, GMC_DATATYPE_UINT32, &F2000, sizeof(uint32_t), "F2000", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async_T3_L3);
        F200++;
        F2000++;
    }

    // 句柄g_stmt_async_T1_L1  T0::T1_L1
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    testSetKeyNameAndValue(g_stmt_async_T0, keyvalue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L1, g_vertexLabelT1L1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async_T1_L1, &childNodeT1L1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // list 的值
    T1_L1_mk = 2;
    ret = testYangSetNodePropertyNew(childNodeT1L1, GMC_DATATYPE_UINT32, &T1_L1_mk, sizeof(uint32_t), "T1_L1_mk",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    F20 = 212;
    ret = testYangSetNodePropertyNew(
        childNodeT1L1, GMC_DATATYPE_UINT32, &F20, sizeof(uint32_t), "F20", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(childNodeT1L1, g_vertexLabelT2C, GMC_OPERATION_INSERT, &childNodeT1L1T2C);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    F200 = 221;
    ret = testYangSetNodePropertyNew(
        childNodeT1L1T2C, GMC_DATATYPE_UINT32, &F200, sizeof(uint32_t), "F200", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);

    // 句柄g_stmt_async_T3_L1  T0::T1_L1::T2_C::T3_L1
    for (uint32_t i = 11; i < 14; i++) {
        uint32_t T3_L1_mk = i;
        uint32_t F2000 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T3_L1, g_vertexLabelT3L1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T1_L1, g_stmt_async_T3_L1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async_T3_L1, &childNodeT1L1T2CT3L1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(childNodeT1L1T2CT3L1, GMC_DATATYPE_UINT32, &T3_L1_mk, sizeof(uint32_t),
            "T3_L1_mk", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(childNodeT1L1T2CT3L1, GMC_DATATYPE_UINT32, &F2000, sizeof(uint32_t), "F2000",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async_T3_L1);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 句柄g_stmt_async_T3_L2  T0::T1_L1::T2_C::T3_L2
    GmcNodeT *childNodeT1L1T2CT3L2 = NULL;
    for (uint32_t i = 21; i < 24; i++) {
        uint32_t T3_L2_mk = i;
        uint32_t F2000 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T3_L2, g_vertexLabelT3L2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T1_L1, g_stmt_async_T3_L2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async_T3_L2, &childNodeT1L1T2CT3L2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(childNodeT1L1T2CT3L2, GMC_DATATYPE_UINT32, &T3_L2_mk, sizeof(uint32_t),
            "T3_L2_mk", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(childNodeT1L1T2CT3L2, GMC_DATATYPE_UINT32, &F2000, sizeof(uint32_t), "F2000",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async_T3_L2);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 句柄g_stmt_async_T2_L1  T0::T1_L1::T2_L1
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    testSetKeyNameAndValue(g_stmt_async_T0, keyvalue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L1, g_vertexLabelT1L1, GMC_OPERATION_NONE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);
    T1_L1_mk = 2;
    testSetKeyNameAndValue(g_stmt_async_T1_L1, T1_L1_mk, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t m = 2101;
    F2000 = 21003;
    for (uint32_t i = 1; i < 4; i++) {
        uint32_t T2_L1_mk = i;
        F200 = m;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T2_L1, g_vertexLabelT2L1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T1_L1, g_stmt_async_T2_L1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async_T2_L1, &childNodeT1L1T2L1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(childNodeT1L1T2L1, GMC_DATATYPE_UINT32, &T2_L1_mk, sizeof(uint32_t),
            "T2_L1_mk", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(childNodeT1L1T2L1, GMC_DATATYPE_UINT32, &F200, sizeof(uint32_t), "F200",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async_T2_L1);
        EXPECT_EQ(GMERR_OK, ret);

        // 句柄 g_stmt_async_T3_L3 T0::T1_L1::T2_L1::T3_L3
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T3_L3, g_vertexLabelT3L3, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T2_L1, g_stmt_async_T3_L3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async_T3_L3, &childNodeT3L3);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(
            childNodeT3L3, GMC_DATATYPE_UINT32, &F2000, sizeof(uint32_t), "F2000", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async_T3_L3);
        EXPECT_EQ(GMERR_OK, ret);
        m++;
    }
    // 句柄g_stmt_async_T1_L1  T0::T1_L1
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    testSetKeyNameAndValue(g_stmt_async_T0, keyvalue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L1, g_vertexLabelT1L1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async_T1_L1, &childNodeT1L1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    T1_L1_mk = 3;
    ret = testYangSetNodePropertyNew(childNodeT1L1, GMC_DATATYPE_UINT32, &T1_L1_mk, sizeof(uint32_t), "T1_L1_mk",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);
    // 句柄g_stmt_async_T2_L1  T0::T1_L1::T2_L1
    m = 3;
    for (uint32_t i = 1; i < 4; i++) {
        uint32_t T2_L1_mk = i;
        F200 = m;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T2_L1, g_vertexLabelT2L1, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T1_L1, g_stmt_async_T2_L1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async_T2_L1, &childNodeT1L1T2L1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetNodePropertyNew(childNodeT1L1T2L1, GMC_DATATYPE_UINT32, &T2_L1_mk, sizeof(uint32_t),
            "T2_L1_mk", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodePropertyNew(childNodeT1L1T2L1, GMC_DATATYPE_UINT32, &F200, sizeof(uint32_t), "F200",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async_T2_L1);
        EXPECT_EQ(GMERR_OK, ret);
        m++;
        if (i == 3) {
            for (uint32_t n = 21001; n < 21003; n++) {
                F2000 = n;
                // 句柄g_stmt_async_T3_L3  T0::T1_L1::T2_L1::T3_L3
                ret = testGmcPrepareStmtByLabelName(g_stmt_async_T3_L3, g_vertexLabelT3L3, GMC_OPERATION_INSERT);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, g_stmt_async_T2_L1, g_stmt_async_T3_L3);
                EXPECT_EQ(GMERR_OK, ret);
                ret = GmcGetRootNode(g_stmt_async_T3_L3, &childNodeT3L3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = testYangSetNodePropertyNew(childNodeT3L3, GMC_DATATYPE_UINT32, &F2000, sizeof(uint32_t), "F2000",
                    GMC_YANG_PROPERTY_OPERATION_CREATE);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, g_stmt_async_T3_L3);
                EXPECT_EQ(GMERR_OK, ret);
            }
        }
    }

    // 句柄g_stmt_async_T1_L1  T0::T1_L1
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    testSetKeyNameAndValue(g_stmt_async_T0, keyvalue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L1, g_vertexLabelT1L1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async_T1_L1, &childNodeT1L1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    T1_L1_mk = 4;
    ret = testYangSetNodePropertyNew(childNodeT1L1, GMC_DATATYPE_UINT32, &T1_L1_mk, sizeof(uint32_t), "T1_L1_mk",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 句柄g_stmt_async_T2_C  T0::T1_L1::T2_C
    ret = GmcYangEditChildNode(childNodeT1L1, g_vertexLabelT2C, GMC_OPERATION_INSERT, &childNodeT1L1T2C);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    F200 = 222;
    ret = testYangSetNodePropertyNew(
        childNodeT1L1T2C, GMC_DATATYPE_UINT32, &F200, sizeof(uint32_t), "F200", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async_T1_L1);
    EXPECT_EQ(GMERR_OK, ret);

    // 句柄g_stmt_async_T1_L2  T0::T1_L2
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    testSetKeyNameAndValue(g_stmt_async_T0, keyvalue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);

    GmcNodeT *childNodeT1L2 = NULL;
    m = 310;
    for (uint32_t i = 300; i < 302; i++) {
        uint32_t F30 = i;
        uint32_t F31 = m;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L2, g_vertexLabelT1L2, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async_T1_L2, &childNodeT1L2);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetNodePropertyNew(
            childNodeT1L2, GMC_DATATYPE_UINT32, &F30, sizeof(uint32_t), "F30", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetNodePropertyNew(
            childNodeT1L2, GMC_DATATYPE_UINT32, &F31, sizeof(uint32_t), "F31", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        m++;
        ret = GmcBatchAddDML(batch, g_stmt_async_T1_L2);
        EXPECT_EQ(GMERR_OK, ret);
    }

    GmcNodeT *childNodeT1L3 = NULL;
    // 句柄g_stmt_async_T1_L3  T0::T1_L3
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_NONE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    testSetKeyNameAndValue(g_stmt_async_T0, keyvalue);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    m = 1;
    for (uint32_t i = 410; i < 414; i++) {
        uint32_t T1_L3_mk_1 = m;
        uint32_t T1_L3_mk_2 = m;
        uint32_t F40 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async_T1_L3, g_vertexLabelT1L3, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_async_T0, g_stmt_async_T1_L3);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_async_T1_L3, &childNodeT1L3);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetNodePropertyNew(childNodeT1L3, GMC_DATATYPE_UINT32, &T1_L3_mk_1, sizeof(uint32_t),
            "T1_L3_mk_1", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetNodePropertyNew(childNodeT1L3, GMC_DATATYPE_UINT32, &T1_L3_mk_2, sizeof(uint32_t),
            "T1_L3_mk_2", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testYangSetNodePropertyNew(
            childNodeT1L3, GMC_DATATYPE_UINT32, &F40, sizeof(uint32_t), "F40", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        m++;
        ret = GmcBatchAddDML(batch, g_stmt_async_T1_L3);
        EXPECT_EQ(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    EXPECT_EQ(GMERR_OK, ret);
    rootNode = NULL;
    childNodeT1C = NULL;
    childNodeT2L2 = NULL;
    childNodeT1L1 = NULL;
    childNodeT1L1T2C = NULL;
    childNodeT1L1T2CT3L1 = NULL;
    childNodeT1L1T2L1 = NULL;
    childNodeT3L3 = NULL;
    childNodeT1L1T2CT3L2 = NULL;
    childNodeT1L2 = NULL;
    childNodeT1L3 = NULL;
}

void testYangPresetAllDateDML(GmcConnT *conn)
{
    int ret;
    // 开启事务
    GmcTxConfigT TrxConfig;
    TrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    TrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    TrxConfig.readOnly = false;
    TrxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 启动事务
    ret = testTransStartAsync(conn, TrxConfig);
    EXPECT_EQ(GMERR_OK, ret);
    // 写数据
    GmcBatchOptionT batchOption;
    GmcBatchT *batch = NULL;
    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBatchType(&batchOption, GMC_BATCH_YANG);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    // 设置根节点 T0层
    ret = testGmcPrepareStmtByLabelName(g_stmt_async_T0, g_vertexLabelT0, GMC_OPERATION_DELETE_GRAPH);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_async_T0);
    EXPECT_EQ(GMERR_OK, ret);
    // 设置属性值
    uint32_t keyvalue = 1;
    testSetKeyNameAndValue(g_stmt_async_T0, keyvalue);
    ret = GmcBatchAddDML(batch, g_stmt_async_T0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(data.succNum, data.totalNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = testTransCommitAsync(conn);
    EXPECT_EQ(GMERR_OK, ret);
}

void *Thread01(void *args)
{
    int32_t ret = 0;
    // alloc all stmt
    TestyangallocAllstmt();
    // 释放all stmt
    TestyangfreeAllstmt();
    return ((void *)0);
}

void AsyncSubtreeFilterCb_01(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    subtreeFilterCbParam *param = (subtreeFilterCbParam *)(userData);
    param->expectStatus = status;
    bool isEnd = false;
    uint32_t count = 0;
    const char **jsonReply = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchJsonRetDeparse(fetchRet, &isEnd, &jsonReply, &count));
    ASSERT_TRUE(isEnd);
    ASSERT_EQ(1, count);
    ASSERT_TRUE(jsonReply != NULL);
    // 将replyJson 与预期json比较
    printf("replyJson is %s \n", jsonReply[0]);
    param->step++;
    if (GMERR_OK != status) {
        printf("[err] status is %d  errMsg  is %s   \n ", status, errMsg);
        return;
    }
}

void *Thread02(void *args)
{
    int ret;
    // subtree 查询
    subtreeFilterCbParam data = {0};
    data.expectReplyJson = NULL;   // 预期要的subtree返回结果
    data.expectStatus = GMERR_OK;  // 预期服务端处理结果
    data.step = 0;                 // 回调执行次数
    // 进行subtree查询
    GmcSubtreeFilterItemT filter = {
        filter.rootName = "T0",
        filter.subtree.json = g_containerContentJson,
        filter.jsonFlag = GMC_JSON_INDENT(4),
        filter.defaultMode = GMC_DEFAULT_FILTER_TRIM,
        filter.isLocationFilter = isLocationFilter,
    };

    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_JSON,
        .filter = &filter,
    };
    char *reply = NULL;
    ret = GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncSubtreeFilterCb_01, &data);
    ret = testWaitAsyncSubtreeRecv(&data);
    EXPECT_EQ(GMERR_OK, data.expectStatus);
    return ((void *)0);
}

void testDropEdgeAndVertex(GmcStmtT *g_stmt_async, const char *namespace1)
{
    int ret;
    AsyncUserDataT data{0};
    ret = GmcClearNamespaceAsync(g_stmt_async, namespace1, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

void testDropEdgeAndVertex_thousand(GmcStmtT *g_stmt_async)
{
    int ret;
    AsyncUserDataT data{0};
    char edgename[20] = "T";
    char vertexname[20] = "T";
    ret = GmcClearNamespaceAsync(g_stmt_async, "Yang_008_053", ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
}

#endif /* LISTLOCATION_H */
