 #include "yangTest.h"

class yangTest : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void yangTest::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void yangTest::TearDownTestCase()
{
    int ret = close_epoll_thread();
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	GmcDetachAllShmSeg();
	testEnvClean();
}

GmcConnT *g_connAsync = NULL;
GmcStmtT *g_stmtAsync = NULL;

GmcConnT *g_connAsync_1 = NULL;
GmcStmtT *g_stmtAsync_1 = NULL;

GmcConnT *g_connAsync_2 = NULL;
GmcStmtT *g_stmtAsync_2 = NULL;

#if defined ENV_RTOSV2X  // HPE设备
#define TABLE_NUM 1
#else
#define TABLE_NUM 10
#endif

void yangTest::SetUp()
{
    system("rm -rf reply/*");
    AsyncUserDataT data = {0};

        // 创建异步连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    connOptions.timeoutMs = 200000;
    int ret = TestYangGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, &connOptions);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    AddWhiteList(GMERR_UNEXPECTED_NULL_VALUE);
}

void yangTest::TearDown()
{
    AW_CHECK_LOG_END();
    AsyncUserDataT data = {0};

    // 断连
    int ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf reply/*");
}

char g_str[1024];

// 001.1个连接异步写正常获取回调，写入20万条数据 //1个连接分别异步写获取回调，不到1G限制，批量执行不报错STATUS_BATCH_BUFFER_FULL
TEST_F(yangTest, Yang_v3Trans_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};

    int tNum = TABLE_NUM;
    for (int i = 0; i < tNum; i++)
    {
        AW_FUN_Log(LOG_INFO, ">>> test %d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 配置ns级别,RR+乐观事务模式
        memset(g_str, 0, sizeof(g_str));
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        yangNspCfg.namespaceName = g_str;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 删除0号表 
        ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &yangNspCfg, create_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 0号表和四张确定性表
        CreateYangLabel0(g_stmtAsync, data);

        // 启动事务
        GmcStmtT *stmt = NULL;
        GmcStmtT *stmt_1 = NULL;
        GmcBatchT *batch = NULL;
        GmcTxConfigT trxConfig;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcNodeT *childNode1 = NULL;
        GmcNodeT *childNode2 = NULL;
        int ret = GmcAllocStmt(g_connAsync, &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcAllocStmt(g_connAsync, &stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcTransStartAsync(g_connAsync, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        for (int j = 0; j < CYCLE_NUM; j++) {
            // 设置批处理batch参数和设置diff开关
            ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);

            // 设置根节点 root
            if (j == 0) {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置属性值
                testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            } else {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }

            // 设置List节点 root::P1::T1  4+4+7=15*200000 = 2.86 m
            for (int i = 0 + j * 2000; i < 2000 + j * 2000; i++) {
                if (i % 199999 == 0 && i != 0) {
                    AW_FUN_Log(LOG_INFO, "Label Zero Insert Data successfully: %d\n", i + 1);
                }
                ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, stmt, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }

            // 批处理提交
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
            TEST_EXPECT_INT32(2001, data.totalNum);
            TEST_EXPECT_INT32(2001, data.succNum);
            ret = GmcBatchDestroy(batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(&data, 0, sizeof(AsyncUserDataT));
        }

        // 提交事务
        ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);

        GmcFreeStmt(stmt_1);
        GmcFreeStmt(stmt);
    }

    for (int i = 0; i < tNum; i++)
    {
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 删除0号表 
        DropYangLabel(g_stmtAsync, g_str);

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        int ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    AddWhiteList(GMERR_DUPLICATE_OBJECT);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}


// 002.1个连接异步写正常获取回调，写入20万条数据,不获取回调，批量执行报错STATUS_BATCH_BUFFER_FULL
TEST_F(yangTest, Yang_v3Trans_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};

    int tNum = TABLE_NUM;
#if defined FEATURE_PERSISTENCE // 光启环境
    tNum = 2;
#endif
#if defined(CPU_BIT_32) && defined(ENV_RTOSV2)
    tNum = 2;
#endif
    for (int i = 0; i < tNum; i++)
    {
        AW_FUN_Log(LOG_INFO, ">>> test %d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 配置ns级别,RR+乐观事务模式
        memset(g_str, 0, sizeof(g_str));
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        yangNspCfg.namespaceName = g_str;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 删除0号表 
        ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &yangNspCfg, create_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 0号表和四张确定性表
        CreateYangLabel0(g_stmtAsync, data);

        // 启动事务
        GmcStmtT *stmt = NULL;
        GmcStmtT *stmt_1 = NULL;
        GmcBatchT *batch = NULL;
        GmcTxConfigT trxConfig;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcNodeT *childNode1 = NULL;
        GmcNodeT *childNode2 = NULL;
        int ret = GmcAllocStmt(g_connAsync, &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcAllocStmt(g_connAsync, &stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcTransStartAsync(g_connAsync, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
            int nSucc = 0;
            int nFail = 0;

        for (int j = 0; j < CYCLE_NUM; j++) {
            // 设置批处理batch参数和设置diff开关
            ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);

            // 设置根节点 root
            if (j == 0) {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置属性值
                testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            } else {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
             

            // 设置List节点 root::P1::T1  4+4+7=15*200000 = 2.86 m
            for (int i = 0 + j * 2000; i < 2000 + j * 2000; i++) {
                if (i % 199999 == 0 && i != 0) {
                    AW_FUN_Log(LOG_INFO, "Label Zero Insert Data successfully: %d\n", i + 1);
                }
                ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, stmt, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }

            // 批处理提交
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
            if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
                nFail++;
                sleep(5);
                break;
            } else {
                nSucc++;
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }

            ret = GmcBatchDestroy(batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(&data, 0, sizeof(AsyncUserDataT));
        }
        if (0 == nFail) {
            sleep(5);
        }
        AW_FUN_Log(LOG_INFO, ">>> succ: %d fail: %d\n", nSucc, nFail);
        // 提交事务
         memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);

        GmcFreeStmt(stmt_1);
        GmcFreeStmt(stmt);
    }

    for (int i = 0; i < tNum; i++)
    {
        memset(&data, 0, sizeof(AsyncUserDataT));
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 删除0号表 
        DropYangLabel(g_stmtAsync, g_str);

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        int ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    AddWhiteList(GMERR_CONNECTION_SEND_BUFFER_FULL);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}


// 003.001 多个连接分别异步写不获取回调，直至达到buff限制，批量执行报错STATUS_BATCH_BUFFER_FULL
TEST_F(yangTest, Yang_v3Trans_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    int tNum = TABLE_NUM;
    GmcConnT *tConnAsync[tNum] = {0};
    GmcStmtT *tStmtAsync[tNum] = {0};

    for (int i = 0; i < tNum; i++)
    {
        // 异步建连
        int ret = testGmcConnect(&tConnAsync[i], &tStmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info,
            NULL, NULL, NULL, NULL, -1, 0, &g_epollData.userEpollFd, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 0; i < tNum; i++)
    {
        AW_FUN_Log(LOG_INFO, ">>> test %d", i);
        AsyncUserDataT data = {0};
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 配置ns级别,RR+乐观事务模式
        memset(g_str, 0, sizeof(g_str));
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        yangNspCfg.namespaceName = g_str;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcDropNamespaceAsync(tStmtAsync[i], g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 删除0号表 
        ret = GmcCreateNamespaceWithCfgAsync(tStmtAsync[i], &yangNspCfg, create_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(tStmtAsync[i], g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 0号表和四张确定性表
        CreateYangLabel0(tStmtAsync[i], data);

        // 启动事务
        GmcStmtT *stmt = NULL;
        GmcStmtT *stmt_1 = NULL;
        GmcBatchT *batch = NULL;
        GmcTxConfigT trxConfig;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcNodeT *childNode1 = NULL;
        GmcNodeT *childNode2 = NULL;
        int ret = GmcAllocStmt(tConnAsync[i], &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcAllocStmt(tConnAsync[i], &stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcTransStartAsync(tConnAsync[i], &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        int nSucc = 0;
        int nFail = 0;

        for (int j = 0; j < CYCLE_NUM; j++) {
            // 设置批处理batch参数和设置diff开关
            ret = testBatchPrepareAndSetDiff(tConnAsync[i], &batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);

            // 设置根节点 root
            if (j == 0) {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置属性值
                testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            } else {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
             

            // 设置List节点 root::P1::T1  4+4+7=15*200000 = 2.86 m
            for (int i = 0 + j * 2000; i < 2000 + j * 2000; i++) {
                if (i % 199999 == 0 && i != 0) {
                    AW_FUN_Log(LOG_INFO, "Label Zero Insert Data successfully: %d\n", i + 1);
                }
                ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, stmt, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }

            // 批处理提交
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
            if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
                nFail++;
                sleep(5);
                break;
            } else {
                nSucc++;
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }

            ret = GmcBatchDestroy(batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(&data, 0, sizeof(AsyncUserDataT));
        }
        if (0 == nFail) {
            sleep(5);
        }
        AW_FUN_Log(LOG_INFO, ">>> succ: %d fail: %d\n", nSucc, nFail);
        // 提交事务
         memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcTransCommitAsync(tConnAsync[i], trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);

        GmcFreeStmt(stmt_1);
        GmcFreeStmt(stmt);
    }

    for (int i = 0; i < tNum; i++)
    {
        AsyncUserDataT data = {0};
        memset(&data, 0, sizeof(AsyncUserDataT));
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        ret = GmcUseNamespaceAsync(tStmtAsync[i], g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 删除0号表 
        DropYangLabel(tStmtAsync[i], g_str);

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        int ret = GmcDropNamespaceAsync(tStmtAsync[i], g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    for (int i = 0; i < tNum; i++)
    {
        int ret = testGmcDisconnect(tConnAsync[i], tStmtAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_CONNECTION_SEND_BUFFER_FULL);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}


// 004.002 一个连接一次批量写1G以上的objects，报错STATUS_BATCH_BUFFER_FULL (V5内存限制2M)
TEST_F(yangTest, Yang_v3Trans_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};

    int tNum = TABLE_NUM;
    for (int i = 0; i < tNum; i++)
    {
        AW_FUN_Log(LOG_INFO, ">>> test %d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 配置ns级别,RR+乐观事务模式
        memset(g_str, 0, sizeof(g_str));
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        yangNspCfg.namespaceName = g_str;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 删除0号表 
        ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &yangNspCfg, create_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 0号表和四张确定性表
        CreateYangLabel0(g_stmtAsync, data);

        // 启动事务
        GmcStmtT *stmt = NULL;
        GmcStmtT *stmt_1 = NULL;
        GmcBatchT *batch = NULL;
        GmcTxConfigT trxConfig;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcNodeT *childNode1 = NULL;
        GmcNodeT *childNode2 = NULL;
        int ret = GmcAllocStmt(g_connAsync, &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcAllocStmt(g_connAsync, &stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcTransStartAsync(g_connAsync, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 设置批处理batch参数和设置diff开关
        ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);

         int nCnt = 0;
         int nLCnt = 0;
        for (int j = 0; j < CYCLE_NUM; j++) {
            // 设置根节点 root
            if (j == 0) {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置属性值
                testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                nCnt++;
            } else {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);

                if (GMERR_BATCH_BUFFER_FULL == ret) {
                    AW_FUN_Log(LOG_INFO, ">>>1 GMERR_BATCH_BUFFER_FULL\n");
                    break;
                } else {
                    nCnt++;
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }

            // 设置List节点 root::P1::T1  4+4+7 *2 =30 *200000 = 2.86 m  16375次最多添加，0.23m
            // yang反序列化后，内存会膨胀，不能按写入字段大小算buff占用
            for (int i = 0 + j * 2000; i < 2000 + j * 2000; i++) {
                if (i % 199999 == 0 && i != 0) {
                    AW_FUN_Log(LOG_INFO, "Label Zero Insert Data successfully: %d\n", i + 1);
                }
                ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, stmt, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, stmt_1);

                if (GMERR_BATCH_BUFFER_FULL == ret) {
                     AW_FUN_Log(LOG_INFO, ">>>2 GMERR_BATCH_BUFFER_FULL\n");
                    break;
                } else {
                    nCnt++;
                    nLCnt++;
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
        }

        // 批处理提交
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, ">>> ncnt %d  nLCnt: %d\n",  nCnt, nLCnt);
        TEST_EXPECT_INT32(nCnt, data.totalNum);
        TEST_EXPECT_INT32(nCnt, data.succNum);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);

        GmcFreeStmt(stmt_1);
        GmcFreeStmt(stmt);
    }

    for (int i = 0; i < tNum; i++)
    {
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 删除0号表 
        DropYangLabel(g_stmtAsync, g_str);

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        int ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    AddWhiteList(GMERR_BATCH_BUFFER_FULL);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}


// 005.003 一个连接一次批量写接近但小于1G的objects，成功 (V5内存限制2M)
TEST_F(yangTest, Yang_v3Trans_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};

    int tNum = TABLE_NUM;
    for (int i = 0; i < tNum; i++)
    {
        AW_FUN_Log(LOG_INFO, ">>> test %d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 配置ns级别,RR+乐观事务模式
        memset(g_str, 0, sizeof(g_str));
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        yangNspCfg.namespaceName = g_str;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 删除0号表 
        ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &yangNspCfg, create_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 0号表和四张确定性表
        CreateYangLabel0(g_stmtAsync, data);

        // 启动事务
        GmcStmtT *stmt = NULL;
        GmcStmtT *stmt_1 = NULL;
        GmcBatchT *batch = NULL;
        GmcTxConfigT trxConfig;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcNodeT *childNode1 = NULL;
        GmcNodeT *childNode2 = NULL;
        int ret = GmcAllocStmt(g_connAsync, &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcAllocStmt(g_connAsync, &stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcTransStartAsync(g_connAsync, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 设置批处理batch参数和设置diff开关
        ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        int nCnt = 0;
        int nLCnt = 0;
#if defined ENV_RTOSV2X  // HPE设备
        int nCycle = 1;
#else
        int nCycle = 8;
#endif
        // 超过会内存buff满错误；
        for (int j = 0; j < nCycle; j++) {
            // 设置根节点 root
            if (j == 0) {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置属性值
                testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                nCnt++;
            } else {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                nCnt++;
                TEST_EXPECT_INT32(GMERR_OK, ret);

            }

            // 设置List节点 root::P1::T1  4+4+7 *2 =30 *200000 = 2.86 m  16375次最多添加，0.23m
            // yang反序列化后，内存会膨胀，不能按写入字段大小算buff占用
            for (int i = 0 + j * 2000; i < 2000 + j * 2000; i++) {
                if (i % 199999 == 0 && i != 0) {
                    AW_FUN_Log(LOG_INFO, "Label Zero Insert Data successfully: %d\n", i + 1);
                }
                ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, stmt, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, stmt_1);
                nCnt++;
                nLCnt++;
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }

        // 批处理提交
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, ">>> ncnt %d  nLCnt: %d\n",  nCnt, nLCnt);
        TEST_EXPECT_INT32(nCnt, data.totalNum);
        TEST_EXPECT_INT32(nCnt, data.succNum);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);

        GmcFreeStmt(stmt_1);
        GmcFreeStmt(stmt);
    }

    for (int i = 0; i < tNum; i++)
    {
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 删除0号表 
        DropYangLabel(g_stmtAsync, g_str);

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        int ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    AddWhiteList(GMERR_BATCH_BUFFER_FULL);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}


// 006.006 一个连接一次批量更新正常数据量的objects，成功
TEST_F(yangTest, Yang_v3Trans_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};

    int tNum = TABLE_NUM;
    for (int i = 0; i < tNum; i++)
    {
        AW_FUN_Log(LOG_INFO, ">>> test %d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 配置ns级别,RR+乐观事务模式
        memset(g_str, 0, sizeof(g_str));
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        yangNspCfg.namespaceName = g_str;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 删除0号表 
        ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &yangNspCfg, create_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 0号表和四张确定性表
        CreateYangLabel0(g_stmtAsync, data);

        // 启动事务
        GmcStmtT *stmt = NULL;
        GmcStmtT *stmt_1 = NULL;
        GmcBatchT *batch = NULL;
        GmcTxConfigT trxConfig;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcNodeT *childNode1 = NULL;
        GmcNodeT *childNode2 = NULL;
        int ret = GmcAllocStmt(g_connAsync, &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcAllocStmt(g_connAsync, &stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcTransStartAsync(g_connAsync, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 设置批处理batch参数和设置diff开关
        ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        int nCnt = 0;
        int nLCnt = 0;
#if defined ENV_RTOSV2X  // HPE设备
        int nCycle = 1;
#else
        int nCycle = 5;
#endif
        // 超过会内存buff满错误；
        for (int j = 0; j < nCycle; j++) {
            // 设置根节点 root
            if (j == 0) {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置属性值
                testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_MERGE);

                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_MERGE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_MERGE);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                nCnt++;
            } else {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                nCnt++;
                TEST_EXPECT_INT32(GMERR_OK, ret);

            }

            // 设置List节点 root::P1::T1  4+4+7 *2 =30 *200000 = 2.86 m  16375次最多添加，0.23m
            // yang反序列化后，内存会膨胀，不能按写入字段大小算buff占用
            for (int i = 0 + j * 2000; i < 2000 + j * 2000; i++) {
                if (i % 199999 == 0 && i != 0) {
                    AW_FUN_Log(LOG_INFO, "Label Zero Insert Data successfully: %d\n", i + 1);
                }
                ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_MERGE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, stmt, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);

                ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyName(stmt_1, "PK");
                TEST_EXPECT_INT32(GMERR_OK, ret);

                testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, stmt_1);
                nCnt++;
                nLCnt++;
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
        }

        // 批处理提交
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, ">>> ncnt %d  nLCnt: %d\n",  nCnt, nLCnt);
        TEST_EXPECT_INT32(nCnt, data.totalNum);
        TEST_EXPECT_INT32(nCnt, data.succNum);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);

        GmcFreeStmt(stmt_1);
        GmcFreeStmt(stmt);
    }

    for (int i = 0; i < tNum; i++)
    {
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 删除0号表 
        DropYangLabel(g_stmtAsync, g_str);

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        int ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    AddWhiteList(GMERR_BATCH_BUFFER_FULL);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}


// 007.005 一个连接一次批量更新大数据量的objects，报错STATUS_BATCH_BUFFER_FULL
TEST_F(yangTest, Yang_v3Trans_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};

    int tNum = TABLE_NUM;
    for (int i = 0; i < tNum; i++)
    {
        AW_FUN_Log(LOG_INFO, ">>> test %d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 配置ns级别,RR+乐观事务模式
        memset(g_str, 0, sizeof(g_str));
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        yangNspCfg.namespaceName = g_str;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 删除0号表 
        ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &yangNspCfg, create_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 0号表和四张确定性表
        CreateYangLabel0(g_stmtAsync, data);

        // 启动事务
        GmcStmtT *stmt = NULL;
        GmcStmtT *stmt_1 = NULL;
        GmcBatchT *batch = NULL;
        GmcTxConfigT trxConfig;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcNodeT *childNode1 = NULL;
        GmcNodeT *childNode2 = NULL;
        int ret = GmcAllocStmt(g_connAsync, &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcAllocStmt(g_connAsync, &stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcTransStartAsync(g_connAsync, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 设置批处理batch参数和设置diff开关
        ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        int nCnt = 0;
        int nLCnt = 0;

        // 超过会内存buff满错误；
        for (int j = 0; j < CYCLE_NUM; j++) {
            // 设置根节点 root
            if (j == 0) {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置属性值
                testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_MERGE);

                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_MERGE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_MERGE);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                nCnt++;
            } else {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                if (GMERR_BATCH_BUFFER_FULL == ret) {
                    AW_FUN_Log(LOG_INFO, ">>>1 GMERR_BATCH_BUFFER_FULL\n");
                    break;
                } else {
                    nCnt++;
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }

            // 设置List节点 root::P1::T1  4+4+7 *2 =30 *200000 = 2.86 m  16375次最多添加，0.23m
            // yang反序列化后，内存会膨胀，不能按写入字段大小算buff占用
            for (int i = 0 + j * 2000; i < 2000 + j * 2000; i++) {
                if (i % 199999 == 0 && i != 0) {
                    AW_FUN_Log(LOG_INFO, "Label Zero Insert Data successfully: %d\n", i + 1);
                }
                ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_MERGE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, stmt, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);

                ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyName(stmt_1, "PK");
                TEST_EXPECT_INT32(GMERR_OK, ret);

                testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, stmt_1);
                if (GMERR_BATCH_BUFFER_FULL == ret) {
                     AW_FUN_Log(LOG_INFO, ">>>2 GMERR_BATCH_BUFFER_FULL\n");
                    break;
                } else {
                    nCnt++;
                    nLCnt++;
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
            }
        }

        // 批处理提交
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        AW_FUN_Log(LOG_INFO, ">>> ncnt %d  nLCnt: %d\n",  nCnt, nLCnt);
        TEST_EXPECT_INT32(nCnt, data.totalNum);
        TEST_EXPECT_INT32(nCnt, data.succNum);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);

        GmcFreeStmt(stmt_1);
        GmcFreeStmt(stmt);
    }

    for (int i = 0; i < tNum; i++)
    {
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 删除0号表 
        DropYangLabel(g_stmtAsync, g_str);

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        int ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    AddWhiteList(GMERR_BATCH_BUFFER_FULL);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}


// 008.004 多个连接分别异步更新不获取回调，直至达到1G限制，批量执行报错STATUS_BATCH_BUFFER_FULL
TEST_F(yangTest, Yang_v3Trans_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    int tNum = TABLE_NUM;
    GmcConnT *tConnAsync[tNum] = {0};
    GmcStmtT *tStmtAsync[tNum] = {0};

    for (int i = 0; i < tNum; i++)
    {
        // 异步建连
        int ret = testGmcConnect(&tConnAsync[i], &tStmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info,
            NULL, NULL, NULL, NULL, -1, 0, &g_epollData.userEpollFd, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < tNum; i++)
    {
        AW_FUN_Log(LOG_INFO, ">>> test %d", i);
        AsyncUserDataT data = {0};
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 配置ns级别,RR+乐观事务模式
        memset(g_str, 0, sizeof(g_str));
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        yangNspCfg.namespaceName = g_str;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcDropNamespaceAsync(tStmtAsync[i], g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 删除0号表 
        ret = GmcCreateNamespaceWithCfgAsync(tStmtAsync[i], &yangNspCfg, create_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(tStmtAsync[i], g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 0号表和四张确定性表
        CreateYangLabel0(tStmtAsync[i], data);

        // 启动事务
        GmcStmtT *stmt = NULL;
        GmcStmtT *stmt_1 = NULL;
        GmcBatchT *batch = NULL;
        GmcTxConfigT trxConfig;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcNodeT *childNode1 = NULL;
        GmcNodeT *childNode2 = NULL;
        int ret = GmcAllocStmt(tConnAsync[i], &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcAllocStmt(tConnAsync[i], &stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcTransStartAsync(tConnAsync[i], &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        int nSucc = 0;
        int nFail = 0;

        // 超过会内存buff满错误；
        for (int j = 0; j < CYCLE_NUM; j++) {
            // 设置批处理batch参数和设置diff开关
            ret = testBatchPrepareAndSetDiff(tConnAsync[i], &batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);

            // 设置根节点 root
            if (j == 0) {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_MERGE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置属性值
                testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_MERGE);

                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_MERGE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_MERGE);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            } else {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }

            // 设置List节点 root::P1::T1  4+4+7 *2 =30 *200000 = 2.86 m  16375次最多添加，0.23m
            // yang反序列化后，内存会膨胀，不能按写入字段大小算buff占用
            for (int i = 0 + j * 2000; i < 2000 + j * 2000; i++) {
                if (i % 199999 == 0 && i != 0) {
                    AW_FUN_Log(LOG_INFO, "Label Zero Insert Data successfully: %d\n", i + 1);
                }
                ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_MERGE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, stmt, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);

                ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyName(stmt_1, "PK");
                TEST_EXPECT_INT32(GMERR_OK, ret);

                testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }

            // 批处理提交
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback, NULL);
            if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
                nFail++;
                sleep(5);
                break;
            } else {
                nSucc++;
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            ret = GmcBatchDestroy(batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }

        if (0 == nFail) {
            sleep(5);
        }

        // 提交事务
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcTransCommitAsync(tConnAsync[i], trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);

        GmcFreeStmt(stmt_1);
        GmcFreeStmt(stmt);
    }

    for (int i = 0; i < tNum; i++)
    {
        AsyncUserDataT data = {0};
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(tStmtAsync[i], g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 删除0号表 
        DropYangLabel(tStmtAsync[i], g_str);

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        int ret = GmcDropNamespaceAsync(tStmtAsync[i], g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    for (int i = 0; i < tNum; i++)
    {
        int ret = testGmcDisconnect(tConnAsync[i], tStmtAsync[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AddWhiteList(GMERR_BATCH_BUFFER_FULL);
    AddWhiteList(GMERR_CONNECTION_SEND_BUFFER_FULL);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

// 009.007 1连接，多次建表并subtree过滤读正常
TEST_F(yangTest, Yang_v3Trans_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AsyncUserDataT data = {0};

    int tNum = TABLE_NUM;
    for (int i = 0; i < tNum; i++)
    {
        AW_FUN_Log(LOG_INFO, ">>> test %d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 配置ns级别,RR+乐观事务模式
        memset(g_str, 0, sizeof(g_str));
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        yangNspCfg.namespaceName = g_str;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 删除0号表 
        ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &yangNspCfg, create_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 0号表和四张确定性表
        CreateYangLabel0(g_stmtAsync, data);

        // 启动事务
        GmcStmtT *stmt = NULL;
        GmcStmtT *stmt_1 = NULL;
        GmcBatchT *batch = NULL;
        GmcTxConfigT trxConfig;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcNodeT *childNode1 = NULL;
        GmcNodeT *childNode2 = NULL;
        int ret = GmcAllocStmt(g_connAsync, &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcAllocStmt(g_connAsync, &stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcTransStartAsync(g_connAsync, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        for (int j = 0; j < CYCLE_NUM; j++) {
            // 设置批处理batch参数和设置diff开关
            ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);

            // 设置根节点 root
            if (j == 0) {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置属性值
                testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            } else {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }

            // 设置List节点 root::P1::T1  4+4+7=15*200000 = 2.86 m
            for (int i = 0 + j * 2000; i < 2000 + j * 2000; i++) {
                if (i % 199999 == 0 && i != 0) {
                    AW_FUN_Log(LOG_INFO, "Label Zero Insert Data successfully: %d\n", i + 1);
                }
                ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, stmt, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
                    // 批处理提交
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
            TEST_EXPECT_INT32(2001, data.totalNum);
            TEST_EXPECT_INT32(2001, data.succNum);
            ret = GmcBatchDestroy(batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(&data, 0, sizeof(AsyncUserDataT));
        }

        // 提交事务
        ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);

        GmcFreeStmt(stmt_1);
        GmcFreeStmt(stmt);

        ret = GmcTransStartAsync(g_connAsync, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        GmcNodeT *root = NULL;
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "root", GMC_OPERATION_SUBTREE_FILTER);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0 = 100;
        ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };

        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };

        std::vector<std::string> reply(0);
    
        FetchRetCbParam123 param = {
            .step = 0,
            .stmt = g_stmtAsync,
            .expectStatus = GMERR_OK,
            .filterMode = filters.filterMode,
            .lastExpectIdx = 0,
            .expectReply = reply,
        };

        ret = GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncFetchRetCb123, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 提交事务
        ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);
    }

    for (int i = 0; i < tNum; i++)
    {
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        ret = GmcUseNamespaceAsync(g_stmtAsync, g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 删除0号表 
        DropYangLabel(g_stmtAsync, g_str);

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        int ret = GmcDropNamespaceAsync(g_stmtAsync, g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    AddWhiteList(GMERR_DUPLICATE_OBJECT);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}


// 010.007 多个连接，分别subtree过滤读 
TEST_F(yangTest, Yang_v3Trans_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;

    int tNum = TABLE_NUM;
    GmcConnT *tConnAsync[tNum] = {0};
    GmcStmtT *tStmtAsync[tNum] = {0};

    for (int i = 0; i < tNum; i++)
    {
        // 异步建连
        int ret = testGmcConnect(&tConnAsync[i], &tStmtAsync[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info,
            NULL, NULL, NULL, NULL, -1, 0, &g_epollData.userEpollFd, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AsyncUserDataT data = {0};
    for (int i = 0; i < tNum; i++)
    {
        AW_FUN_Log(LOG_INFO, ">>> test %d", i);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 配置ns级别,RR+乐观事务模式
        memset(g_str, 0, sizeof(g_str));
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        yangNspCfg.namespaceName = g_str;
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcDropNamespaceAsync(tStmtAsync[i], g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        ret = GmcCreateNamespaceWithCfgAsync(tStmtAsync[i], &yangNspCfg, create_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcUseNamespaceAsync(tStmtAsync[i], g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 0号表和四张确定性表
        CreateYangLabel0(tStmtAsync[i], data);

        // 启动事务
        GmcStmtT *stmt = NULL;
        GmcStmtT *stmt_1 = NULL;
        GmcBatchT *batch = NULL;
        GmcTxConfigT trxConfig;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcNodeT *childNode1 = NULL;
        GmcNodeT *childNode2 = NULL;
        int ret = GmcAllocStmt(tConnAsync[i], &stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcAllocStmt(tConnAsync[i], &stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        ret = GmcTransStartAsync(tConnAsync[i], &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        for (int j = 0; j < CYCLE_NUM; j++) {
            // 设置批处理batch参数和设置diff开关
            ret = testBatchPrepareAndSetDiff(tConnAsync[i], &batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);

            // 设置根节点 root
            if (j == 0) {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置属性值
                testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            } else {
                ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangSetRoot(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt, &rootNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 设置node节点 P1
                ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcBatchAddDML(batch, stmt);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }

            // 设置List节点 root::P1::T1  4+4+7=15*200000 = 2.86 m
            for (int i = 0 + j * 2000; i < 2000 + j * 2000; i++) {
                if (i % 199999 == 0 && i != 0) {
                    AW_FUN_Log(LOG_INFO, "Label Zero Insert Data successfully: %d\n", i + 1);
                }
                ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_INSERT);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, stmt, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
                    // 批处理提交
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
            TEST_EXPECT_INT32(2001, data.totalNum);
            TEST_EXPECT_INT32(2001, data.succNum);
            ret = GmcBatchDestroy(batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(&data, 0, sizeof(AsyncUserDataT));
        }

        // 提交事务
        ret = GmcTransCommitAsync(tConnAsync[i], trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);

        GmcFreeStmt(stmt_1);
        GmcFreeStmt(stmt);
    }

    for (int i = 0; i < tNum; i++)
    {
        memset(&data, 0, sizeof(AsyncUserDataT));
        GmcTxConfigT trxConfig;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        ret = GmcTransStartAsync(tConnAsync[i], &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        GmcNodeT *root = NULL;
        ret = testGmcPrepareStmtByLabelName(tStmtAsync[i], "root", GMC_OPERATION_SUBTREE_FILTER);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(tStmtAsync[i], &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0 = 100;
        ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };

        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };

        std::vector<std::string> reply(0);
    
        FetchRetCbParam123 param = {
            .step = 0,
            .stmt = tStmtAsync[i],
            .expectStatus = GMERR_OK,
            .filterMode = filters.filterMode,
            .lastExpectIdx = 0,
            .expectReply = reply,
        };

        ret = GmcYangSubtreeFilterExecuteAsync(tStmtAsync[i], &filters, NULL, NULL, &param);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 提交事务
        ret = GmcTransCommitAsync(tConnAsync[i], trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    }

    for (int i = 0; i < tNum; i++)
    {
        AsyncUserDataT data = {0};
        snprintf(g_str, MAX_CMD_SIZE, "yang%d", i);
        ret = GmcUseNamespaceAsync(tStmtAsync[i], g_str, use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 删除0号表 
        DropYangLabel(tStmtAsync[i], g_str);

        // 异步删除namespace
        memset(&data, 0, sizeof(AsyncUserDataT));
        int ret = GmcDropNamespaceAsync(tStmtAsync[i], g_str, drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }

    AddWhiteList(GMERR_DUPLICATE_OBJECT);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
}

