/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef YANG_LONG_STABILITY_COMMON_H
#define YANG_LONG_STABILITY_COMMON_H

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <string>
#include <atomic>
#include <vector>
#include <iostream>
#include <cmath>
#include <chrono>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"

#include "t_datacom_lite.h"
#include "jansson.h"

using namespace std;
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

const char *g_labelconfig = "{\"max_record_count\" : 5000000, \"isFastReadUncommitted\":0, \"auto_increment\":1,"
    "\"yang_model\":1}";
static vector<string> expectDiffBase000  = {
};

#if defined ENV_RTOSV2X || (defined(CPU_BIT_32) && defined(ENV_RTOSV2)) // HPE设备
#define CYCLE_NUM 3   // CYCLE_NUM*2000=表0数据量
#define RECORD_NUM 1000  // 表1/2/3数据量
#define RECORD_NUM1 50  // 200张表数据量
#define UNCOMMIT_NUM 200  // label0未提交事务的数据量
#define UNCOMMIT_NUM1 20  // label0未提交事务的数据量
#define UNCOMMIT_NUM2 100  // label1/2/3未提交事务的数据量
#define UNCOMMIT_NUM3 8  // 随机表未提交事务的数据量
#define UPDATE_NUM 50  // 事务操作的数据量
#define BATCH_NUM 301  // 批量操作提交的数据量
#define UPDATE_NUM1 400  // 事务操作的数据量
#define BATCH_NUM1 1201  // 批量操作提交的数据量
#define START_NUM 501  // 批操作数据区间开始值
#define UPDATE_NUM2 100  // 事务操作的数据量
#define BATCH_NUM2 301  // 批量操作提交的数据量
#define RANDOM_NUM1 10  // 随机操作提交的数据量
#define RANDOM_BATCH_NUM1 33  // 批量操作提交的数据量
#define RANDOM_NUM2 400  // 随机操作提交的数据量
#define RANDOM_BATCH_NUM2 1203  // 批量操作提交的数据量
#define THREAD5_NUM 100  // 线程5批量操作提交的数据量
#define THREAD5_BATCH_NUM 301  // 线程5批量操作提交的数据量
#else
#define CYCLE_NUM 100  // CYCLE_NUM*2000=表0数据量
#define RECORD_NUM 5000  // 表1/2/3数据量
#define RECORD_NUM1 100  // 200张表数据量
#define UNCOMMIT_NUM 20000  // label0未提交事务的数据量
#define UNCOMMIT_NUM1 2000  // label0未提交事务的数据量
#define UNCOMMIT_NUM2 2000  // label1/2/3未提交事务的数据量
#define UNCOMMIT_NUM3 80  // 随机表未提交事务的数据量
#define UPDATE_NUM 100  // 事务操作的数据量
#define BATCH_NUM 601  // 批量操作提交的数据量
#define UPDATE_NUM1 4000  // 事务操作的数据量
#define BATCH_NUM1 12001  // 批量操作提交的数据量
#define START_NUM 5001  // 批操作数据区间开始值
#define UPDATE_NUM2 300  // 事务操作的数据量
#define BATCH_NUM2 901  // 批量操作提交的数据量
#define RANDOM_NUM1 100  // 随机操作提交的数据量
#define RANDOM_BATCH_NUM1 303  // 批量操作提交的数据量
#define RANDOM_NUM2 4000  // 随机操作提交的数据量
#define RANDOM_BATCH_NUM2 12003  // 批量操作提交的数据量
#define THREAD5_NUM 1000  // 线程5批量操作提交的数据量
#define THREAD5_BATCH_NUM 3001  // 线程5批量操作提交的数据量
#endif
// 创建0号表
void CreateYangLabel0(GmcStmtT *stmt, AsyncUserDataT data)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret;

    readJanssonFile("schema_file/schema0.gmjson", &vertexSchema);
    EXPECT_NE((void *)NULL, vertexSchema);
    readJanssonFile("schema_file/schema0_edge.gmjson", &edgeSchema);
    EXPECT_NE((void *)NULL, edgeSchema);

    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_labelconfig, create_vertex_label_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_labelconfig, create_vertex_label_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    free(edgeSchema);
    free(vertexSchema);
}
// 创建四类表
void CreateYangLabel1(GmcStmtT *stmt, AsyncUserDataT data)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret;

    readJanssonFile("schema_file/schema1.gmjson", &vertexSchema);
    EXPECT_NE((void *)NULL, vertexSchema);
    readJanssonFile("schema_file/schema1_edge.gmjson", &edgeSchema);
    EXPECT_NE((void *)NULL, edgeSchema);

    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_labelconfig, create_vertex_label_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_labelconfig, create_vertex_label_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    free(edgeSchema);
    free(vertexSchema);
}
void CreateYangLabel2(GmcStmtT *stmt, AsyncUserDataT data)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret;

    readJanssonFile("schema_file/schema2.gmjson", &vertexSchema);
    EXPECT_NE((void *)NULL, vertexSchema);
    readJanssonFile("schema_file/schema2_edge.gmjson", &edgeSchema);
    EXPECT_NE((void *)NULL, edgeSchema);

    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_labelconfig, create_vertex_label_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_labelconfig, create_vertex_label_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    free(edgeSchema);
    free(vertexSchema);
}
void CreateYangLabel3(GmcStmtT *stmt, AsyncUserDataT data)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret;

    readJanssonFile("schema_file/schema3.gmjson", &vertexSchema);
    EXPECT_NE((void *)NULL, vertexSchema);
    readJanssonFile("schema_file/schema3_edge.gmjson", &edgeSchema);
    EXPECT_NE((void *)NULL, edgeSchema);

    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_labelconfig, create_vertex_label_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_labelconfig, create_vertex_label_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    free(edgeSchema);
    free(vertexSchema);
}
void CreateYangLabel4(GmcStmtT *stmt, AsyncUserDataT data)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    int ret;

    readJanssonFile("schema_file/schema4.gmjson", &vertexSchema);
    EXPECT_NE((void *)NULL, vertexSchema);
    readJanssonFile("schema_file/schema4_edge.gmjson", &edgeSchema);
    EXPECT_NE((void *)NULL, edgeSchema);

    ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_labelconfig, create_vertex_label_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_labelconfig, create_vertex_label_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    free(edgeSchema);
    free(vertexSchema);
}
// 创建200张随机表
void CreateYangFiftyLablesRandomly(GmcStmtT *stmt, AsyncUserDataT data)
{
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    char eschemaFile[1024];
    char vschemaFile[1024];
    int ret;
    for (int j = 1; j < 5; j++) {
        for (int i = 1; i < 51; i++) {
            ret = snprintf(vschemaFile, 1024, "random/label%d/Vertexlabel%d.gmjson", j, i);
            if (ret < 0) {
                AW_FUN_Log(LOG_INFO, "snprintf error\n");
                return;
            }
            ret = snprintf(eschemaFile, 1024, "random/label%d/Edgelabel%d.gmjson", j, i);
            if (ret < 0) {
                AW_FUN_Log(LOG_INFO, "snprintf error\n");
                return;
            }
            readJanssonFile((const char*)vschemaFile, &vertexSchema);
            EXPECT_NE((void *)NULL, vertexSchema);
            readJanssonFile((const char*)eschemaFile, &edgeSchema);
            EXPECT_NE((void *)NULL, edgeSchema);

            ret = GmcCreateVertexLabelAsync(stmt, vertexSchema, g_labelconfig, create_vertex_label_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
            if (data.status) {
                AW_FUN_Log(LOG_INFO, "vschemaFile=%s\n", vschemaFile);
            }
            ret = GmcCreateEdgeLabelAsync(stmt, edgeSchema, g_labelconfig, create_vertex_label_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
            if (data.status) {
                AW_FUN_Log(LOG_INFO, "eschemaFile=%s\n", eschemaFile);
            }
            vertexSchema = NULL;
            edgeSchema = NULL;
        }
    }
    free(edgeSchema);
    free(vertexSchema);
}
// prepare batch句柄
int testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        return ret;
    }

    // 2M大小
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        return ret;
    }

    return ret;
}
// 设置属性值
void testInitPropValue(GmcPropValueT *propValue, const char *name, GmcDataTypeE type, void *value, uint32_t size)
{
    if (propValue == NULL) {
        return;
    }
    strcpy_s(propValue->propertyName, strlen(name) + 1, name);
    propValue->type = type;
    propValue->size = size;
    propValue->value = (void *)value;
}
void testYangSetNodeProperty(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    GmcPropValueT propValue = {0};
    uint32_t valueF0 = i;
    uint32_t valueF1 = i;
    char valueF2[8] = "string";
    testInitPropValue(&propValue, "F0", GMC_DATATYPE_UINT32, &valueF0, sizeof(valueF0));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF1, sizeof(valueF1));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testInitPropValue(&propValue, "F2", GMC_DATATYPE_STRING, valueF2, strlen(valueF2));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
void testYangSetNodeProperty_merge(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    GmcPropValueT propValue = {0};
    uint32_t valueF0 = i;
    uint32_t valueF1 = i;
    char valueF2[8] = "string";
    testInitPropValue(&propValue, "F1", GMC_DATATYPE_UINT32, &valueF1, sizeof(valueF1));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testInitPropValue(&propValue, "F2", GMC_DATATYPE_STRING, valueF2, strlen(valueF2));
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    TEST_EXPECT_INT32(GMERR_OK, ret);
}
// 0号表写入数据20w条
void writeLabelZero(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int j = 0; j < CYCLE_NUM; j++) {
        ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 设置批处理batch参数和设置diff开关
        ret = testBatchPrepareAndSetDiff(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置根节点 root
        if (j == 0) {
            ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcYangSetRoot(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt, &rootNode);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            // 设置属性值
            testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

            // 设置node节点 P1
            ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        } else {
            ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcYangSetRoot(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt, &rootNode);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            // 设置node节点 P1
            ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }

        // 设置List节点 root::P1::T1
        for (int i = 0 + j * 2000; i < 2000 + j * 2000; i++) {
            if (i % 199999 == 0 && i != 0) {
                AW_FUN_Log(LOG_INFO, "Label Zero Insert Data successfully: %d\n", i + 1);
            }
            ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, stmt, stmt_1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt_1, &rootNode1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, stmt_1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(2001, data.totalNum);
        TEST_EXPECT_INT32(2001, data.succNum);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        sleep(1);
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// 四类确定性表各写入数据5k条
void writeCertainLabelTwo(GmcConnT *conn, AsyncUserDataT data, uint32_t count = 5000)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    char rootName[512];
    char childName[512];
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root2", GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置属性值
    testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_INSERT, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_INSERT, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_INSERT, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testYangSetNodeProperty(childNode3, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置子节点（list） root1::P1::A1::T1
    for (int i = 0; i < RECORD_NUM; i++) {
        if (i % 4999 == 0 && i != 0) {
            AW_FUN_Log(LOG_INFO, "Label Two Insert Data successfully: %d\n", i + 1);
        }
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root2::P1::A2::P2::A1::T1", GMC_OPERATION_INSERT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    TEST_EXPECT_INT32(RECORD_NUM + 1, data.totalNum);
    TEST_EXPECT_INT32(RECORD_NUM + 1, data.succNum);
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    usleep(200);
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
void writeCertainLabelOne(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root1", GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置属性值
    testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_INSERT, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置子节点（list） root1::P1::A1::T1
    for (int i = 0; i < RECORD_NUM; i++) {
        if (i % 4999 == 0 && i != 0) {
            AW_FUN_Log(LOG_INFO, "Label One Insert Data successfully: %d\n", i + 1);
        }
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root1::P1::A1::T1", GMC_OPERATION_INSERT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    TEST_EXPECT_INT32(RECORD_NUM + 1, data.totalNum);
    TEST_EXPECT_INT32(RECORD_NUM + 1, data.succNum);
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    usleep(200);
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
void writeCertainLabelThree(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root3", GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置属性值
    testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置node节点 P1(container)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置node节点 P2(container)
    ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_INSERT, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置node节点 P3(container)
    ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_INSERT, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testYangSetNodeProperty(childNode2, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    // 设置node节点 P4(container)
    ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_INSERT, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testYangSetNodeProperty(childNode3, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置子节点（list） root1::P1::A1::T1
    for (int i = 0; i < RECORD_NUM; i++) {
        if (i % 4999 == 0 && i != 0) {
            AW_FUN_Log(LOG_INFO, "Label Three Insert Data successfully: %d\n", i + 1);
        }
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root3::P1::P2::P3::P4::T1", GMC_OPERATION_INSERT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    TEST_EXPECT_INT32(RECORD_NUM + 1, data.totalNum);
    TEST_EXPECT_INT32(RECORD_NUM + 1, data.succNum);
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    usleep(200);
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
void writeCertainLabelFour(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root4", GMC_OPERATION_INSERT);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置属性值
    testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置node节点(container) P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    
    // 设置List节点 root4::P1::T1
    for (int i = 0; i < RECORD_NUM; i++) {
        if (i % 4999 == 0 && i != 0) {
            AW_FUN_Log(LOG_INFO, "Label Four Insert Data successfully: %d\n", i + 1);
        }
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root4::P1::T1", GMC_OPERATION_INSERT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    TEST_EXPECT_INT32(RECORD_NUM + 1, data.totalNum);
    TEST_EXPECT_INT32(RECORD_NUM + 1, data.succNum);
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    usleep(200);
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// 四类随机表共200,各写入数据100条
void writeRandomLabelOne(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    char rootName[1024];
    char childName[1024];
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int j = 1; j < 51; j++) {
        if (j % 50 == 0) {
            AW_FUN_Log(LOG_INFO, "Fifty Random Class One Labels Insert Data successfully, each laebl 100 data\n");
        }
        ret = snprintf(rootName, 1024, "rootOne%d", j);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(childName, 1024, "rootOne%d::P1::A1::T1", j);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 设置批处理batch参数和设置diff开关
        ret = testBatchPrepareAndSetDiff(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置根节点 root
        ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_INSERT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &rootNode);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置属性值
        testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 设置node节点 P1(choice)
        ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置node节点 A1(case)
        ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        // 设置子节点（list） root1::P1::A1::T1
        for (int i = 0; i < RECORD_NUM1; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_1, childName, GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, stmt, stmt_1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt_1, &rootNode1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, stmt_1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RECORD_NUM1 + 1, data.totalNum);
        TEST_EXPECT_INT32(RECORD_NUM1 + 1, data.succNum);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
void writeRandomLabelTwo(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    char rootName[512];
    char childName[512];
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int j = 1; j < 51; j++) {
        if (j % 50 == 0) {
            AW_FUN_Log(LOG_INFO, "Fifty Random Class Two Labels Insert Data successfully, each laebl 100 data\n");
        }
        ret = snprintf(rootName, 1024, "rootTwo%d", j);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(childName, 1024, "rootTwo%d::P1::A2::P2::A1::T1", j);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 设置批处理batch参数和设置diff开关
        ret = testBatchPrepareAndSetDiff(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置根节点 root
        ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_INSERT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &rootNode);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置属性值
        testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 设置node节点 P1(choice)
        ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置node节点 A1(case)
        ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 设置node节点 P1(choice)
        ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置node节点 A1(case)
        ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_INSERT, &childNode3);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode3, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        // 设置子节点（list） root1::P1::A1::T1
        for (int i = 0; i < RECORD_NUM1; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_1, childName, GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, stmt, stmt_1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt_1, &rootNode1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, stmt_1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RECORD_NUM1 + 1, data.totalNum);
        TEST_EXPECT_INT32(RECORD_NUM1 + 1, data.succNum);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
void writeRandomLabelThree(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    char rootName[1024];
    char childName[1024];
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int j = 1; j < 51; j++) {
        if (j % 50 == 0) {
            AW_FUN_Log(LOG_INFO, "Fifty Random Class Three Labels Insert Data successfully, each laebl 100 data\n");
        }
        ret = snprintf(rootName, 1024, "rootThree%d", j);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(childName, 1024, "rootThree%d::P1::P2::P3::P4::T1", j);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 设置批处理batch参数和设置diff开关
        ret = testBatchPrepareAndSetDiff(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置根节点 root
        ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_INSERT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &rootNode);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置属性值
        testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 设置node节点 P1(container)
        ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 设置node节点 P2(container)
        ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 设置node节点 P3(container)
        ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 设置node节点 P4(container)
        ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_INSERT, &childNode3);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode3, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);

        // 设置子节点（list） root1::P1::A1::T1
        for (int i = 0; i < RECORD_NUM1; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_1, childName, GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, stmt, stmt_1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt_1, &rootNode1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, stmt_1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RECORD_NUM1 + 1, data.totalNum);
        TEST_EXPECT_INT32(RECORD_NUM1 + 1, data.succNum);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
void writeRandomLabelZero(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    char rootName[1024];
    char childName[1024];
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int j = 1; j < 51; j++) {
        if (j % 50 == 0) {
            AW_FUN_Log(LOG_INFO, "Fifty Random Class Zero Labels Insert Data successfully, each laebl 100 data\n");
        }
        ret = snprintf(rootName, 1024, "rootFour%d", j);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(childName, 1024, "rootFour%d::P1::T1", j);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);

        // 设置批处理batch参数和设置diff开关
        ret = testBatchPrepareAndSetDiff(conn, &batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置根节点 root
        ret = testGmcPrepareStmtByLabelName(stmt, rootName, GMC_OPERATION_INSERT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &rootNode);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 设置属性值
        testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 设置node节点(container) P1
        ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_INSERT, &childNode);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcBatchAddDML(batch, stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        
        // 设置List节点 root4::P1::T1
        for (int i = 0; i < RECORD_NUM1; i++) {
            ret = testGmcPrepareStmtByLabelName(stmt_1, childName, GMC_OPERATION_INSERT);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, stmt, stmt_1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetRootNode(stmt_1, &rootNode1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, stmt_1);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        // 批处理提交
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RECORD_NUM1 + 1, data.totalNum);
        TEST_EXPECT_INT32(RECORD_NUM1 + 1, data.succNum);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
void DropYangLabel(GmcStmtT *stmt, const char *nameSpace)
{
    AsyncUserDataT data = {0};
    int ret = GmcClearNamespaceAsync(stmt, nameSpace, ClearNSCallbak, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
}
// 创建namespace和yang表
void yang_long_stability_init(GmcStmtT *stmt, AsyncUserDataT data)
{
    int ret;
    system("sh generate_label.sh");
    // 配置ns级别,RR+乐观事务模式
    GmcNspCfgT yangNspCfg = {};
    yangNspCfg.tablespaceName = NULL;
    yangNspCfg.namespaceName = "yang0";
    yangNspCfg.userName = "abc";
    yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
    GmcNspCfgT yangNspCfg1 = {};
    yangNspCfg1.tablespaceName = NULL;
    yangNspCfg1.namespaceName = "yang1";
    yangNspCfg1.userName = "abc";
    yangNspCfg1.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
    GmcNspCfgT yangNspCfg2 = {};
    yangNspCfg2.tablespaceName = NULL;
    yangNspCfg2.namespaceName = "yang2";
    yangNspCfg2.userName = "abc";
    yangNspCfg2.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
    GmcNspCfgT yangNspCfg3 = {};
    yangNspCfg3.tablespaceName = NULL;
    yangNspCfg3.namespaceName = "yang3";
    yangNspCfg3.userName = "abc";
    yangNspCfg3.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
    ret = GmcCreateNamespaceWithCfgAsync(stmt, &yangNspCfg, create_namespace_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    ret = GmcCreateNamespaceWithCfgAsync(stmt, &yangNspCfg1, create_namespace_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    ret = GmcCreateNamespaceWithCfgAsync(stmt, &yangNspCfg2, create_namespace_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    ret = GmcCreateNamespaceWithCfgAsync(stmt, &yangNspCfg3, create_namespace_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    if (g_envType == 0) {
        // 导入ns权限
        const char *yangFile2 = "./schemaFile/gmpolicy_file/NormalPolicy.gmpolicy";
        ret = snprintf(g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy  -f %s -s %s", g_toolPath, yangFile2,
            g_connServer);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        AW_FUN_Log(LOG_INFO, "[INFO]%s\n", g_command);
        ret = executeCommand(g_command, "Import single policy file", "successfully");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(g_command, 0, sizeof(g_command));
    }
    ret = GmcUseNamespaceAsync(stmt, "yang0", use_namespace_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    // 0号表和四张确定性表
    CreateYangLabel0(stmt, data);
    CreateYangLabel1(stmt, data);
    CreateYangLabel2(stmt, data);
    CreateYangLabel3(stmt, data);
    // 200张随机表
    CreateYangFiftyLablesRandomly(stmt, data);
}
// 删除表
void yang_long_stability_Uninit(GmcStmtT *stmt, AsyncUserDataT data)
{
    int ret;
    ret = GmcUseNamespaceAsync(stmt, "yang0", use_namespace_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    char edgeName[512];
    char vertexName[512];
    char vertexName1[512];
    // 删除0号表和确定性表
    DropYangLabel(stmt, "yang0");
    // 200张随机表
    for (int i = 1; i < 51; i++) {
        ret = snprintf(edgeName, 512, "rootOne%d_to_T1", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(vertexName, 512, "rootOne%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(vertexName1, 512, "rootOne%d::P1::A1::T1", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        DropYangLabel(stmt, "yang0");
        ret = snprintf(edgeName, 512, "rootTwo%d_to_T1", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(vertexName, 512, "rootTwo%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(vertexName1, 512, "rootTwo%d::P1::A2::P2::A1::T1", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        DropYangLabel(stmt, "yang0");
        ret = snprintf(edgeName, 512, "rootThree%d_to_T1", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(vertexName, 512, "rootThree%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(vertexName1, 512, "rootThree%d::P1::P2::P3::P4::T1", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        DropYangLabel(stmt, "yang0");
        ret = snprintf(edgeName, 512, "rootFour%d_to_T1", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(vertexName, 512, "rootFour%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(vertexName1, 512, "rootFour%d::P1::T1", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        DropYangLabel(stmt, "yang0");
    }
}
void yang_long_stability_write(GmcConnT *conn, AsyncUserDataT data)
{
    // 0号表写入数据20w条
    writeLabelZero(conn, data);
    // 四类确定性表各写入数据5K条
    writeCertainLabelOne(conn, data);
    writeCertainLabelTwo(conn, data);
    writeCertainLabelThree(conn, data);
    // 四类随机表各写入数据100条
    writeRandomLabelOne(conn, data);
    writeRandomLabelTwo(conn, data);
    writeRandomLabelThree(conn, data);
    writeRandomLabelZero(conn, data);
}

uint64_t ComGettimeofdayUsec()
{
    struct timeval now;
    uint64_t usec;
    gettimeofday(&now, NULL);
    usec = (uint64_t)(now.tv_sec * 1000 * 1000) + (int)(now.tv_usec);
    return usec;
}
static vector<string> expectDiffNULLBase = {};
string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const int *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
    }
}

string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}

void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}

// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        TEST_EXPECT_INT32(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            TEST_EXPECT_INT32(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

// 比较s1 s2两个字符串,如果字符串相同,返回空串,不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}

void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    TEST_EXPECT_INT32(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        TEST_EXPECT_INT32(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        TEST_EXPECT_INT32(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        cout << "actual diff:\n" << res;
        if (expectReply.size() == count) {
            ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        }
        TEST_EXPECT_INT32(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

void FetchDiff_callback(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            TEST_EXPECT_INT32(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
            TEST_EXPECT_INT32((uint32_t)(*userData1->expectDiff).size(), count);
            ASSERT_TRUE(isEnd);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}
// list节点位置移动参考点
void InitRefKeys(GmcPropValueT *refKey, uint32_t propId, void *value, GmcDataTypeE type = GMC_DATATYPE_UINT32,
    uint32_t sizeValue = 4)
{
    refKey->propertyId = propId;
    refKey->propertyName[0] = '\0';
    refKey->type = type;
    refKey->size = sizeValue;
    refKey->value = value;
}

void UninitListProperty(GmcYangListLocatorT *listProperty)
{
    if (listProperty->refKeyFields == NULL) {
        return;
    }
    free(listProperty->refKeyFields);
    listProperty->refKeyFields = NULL;
}

void InitListProperty(
    GmcYangListLocatorT *listProperty, GmcYangListPositionE position, GmcPropValueT *referenceKey)
{
    if (listProperty == NULL) {
        return;
    }
    listProperty->position = position;
    if (referenceKey != NULL) {
        listProperty->refKeyFields = (GmcPropValueT **)malloc(sizeof(GmcPropValueT *));
        listProperty->refKeyFields[0] = referenceKey;
        listProperty->refKeyFieldsCount = 1;
    } else {
        listProperty->refKeyFields = NULL;
        listProperty->refKeyFieldsCount = 0;
    }
}

void yang_table0_certainOp_update100(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // delete 5001-5100 remove 7001-7100
    for (int i = START_NUM; i < START_NUM + UPDATE_NUM ; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_DELETE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    for (int i = START_NUM + 2000; i < START_NUM + 2000 + UPDATE_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    uint32_t indexVal = START_NUM - 1;
    // merge 5001-5100 insert 7001-7100
    for (int i = START_NUM; i < START_NUM + UPDATE_NUM ; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitRefKeys(&refKey, 1, &indexVal);
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_MERGE, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        indexVal++;
    }
    indexVal = START_NUM + 1999;
    for (int i = START_NUM + 2000; i < START_NUM + 2000 + UPDATE_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_INSERT);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitRefKeys(&refKey, 1, &indexVal);
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        indexVal++;
    }
    indexVal = START_NUM - 1;
    // replace 5001-5100 7001-7100
    for (int i = START_NUM + 2000; i < START_NUM + 2000 + UPDATE_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    indexVal = START_NUM + 1999;
    for (int i = START_NUM + 2000; i < START_NUM + 2000 + UPDATE_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(BATCH_NUM, data.totalNum);
        TEST_EXPECT_INT32(BATCH_NUM, data.succNum);
        data.stmt = stmt;
        data.expectDiff = &expectDiffBase000;
        ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// table0固定更新4K条
void yang_table0_certainOp_update4K(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove 4k条
    for (int i = START_NUM; i < START_NUM + UPDATE_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    uint32_t indexVal = START_NUM - 1;
    // merge 4k条
    for (int i = START_NUM; i < START_NUM + UPDATE_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitRefKeys(&refKey, 1, &indexVal);
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_MERGE, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        indexVal++;
    }
    // replace 4k条
    for (int i = START_NUM; i < START_NUM + UPDATE_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(BATCH_NUM1, data.totalNum);
        TEST_EXPECT_INT32(BATCH_NUM1, data.succNum);
        data.stmt = stmt;
        data.expectDiff = &expectDiffBase000;
        ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// table0随机更新100条
void yang_table0_RandomOp_update100(GmcConnT *conn, AsyncUserDataT data)
{
    // 取区间10002-20w的随机数
    uint32_t randowVal[100];
    for (int i = 0; i < 100; i++) {
        #if defined ENV_RTOSV2X
        randowVal[i] = rand() % 6000;
        #else
        randowVal[i] = rand() % 189998 + 10002;
        #endif
    }
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t indexVal;
    // remove/merge/replace 100条
    for (int i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &randowVal[i], sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &randowVal[i], sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        indexVal = randowVal[i] - 1;
        GmcPropValueT refKey;
        InitRefKeys(&refKey, 1, &indexVal);
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, randowVal[i], GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_MERGE, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, randowVal[i], GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
    
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, randowVal[i], GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, randowVal[i], GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(301, data.totalNum);
        TEST_EXPECT_INT32(301, data.succNum);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// table0随机更新4K条
void yang_table0_RandomOp_update4K(GmcConnT *conn, AsyncUserDataT data)
{
    // 取区间10002-20w的随机数
    uint32_t randowVal[UPDATE_NUM1];
    for (int i = 0; i < UPDATE_NUM1; i++) {
        #if defined ENV_RTOSV2X
        randowVal[i] = rand() % 6000;
        #else
        randowVal[i] = rand() % 189998 + 10002;
        #endif
    }
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t indexVal;
    // remove/merge/replace 4K条
    for (int i = 0; i < UPDATE_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &randowVal[i], sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &randowVal[i], sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        indexVal = randowVal[i] - 1;
        GmcPropValueT refKey;
        InitRefKeys(&refKey, 1, &indexVal);
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, randowVal[i], GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_MERGE, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, randowVal[i], GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
    
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, randowVal[i], GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, randowVal[i], GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(BATCH_NUM1, data.totalNum);
        TEST_EXPECT_INT32(BATCH_NUM1, data.succNum);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
bool testYangJsonIsEqualInner(const json_t *jsonA, const json_t *jsonB);

// userData结构
struct SubtreeFilterCbParam {
    int step;
    int32_t expectStatus;          // 预期的操作状态
    const char *expectReplyJson;  // 预期返回的subtree查询结果, json字符串
};
struct FetchRetCbParam123 {
    int step;
    GmcStmtT *stmt;
    int32_t expectStatus;                    // 预期的操作状态
    uint32_t filterMode;                    // 过滤模式,使用枚举GmcSubtreeFilterModeE设置值
    uint32_t lastExpectIdx;                 // 分批查询上次查询期望结果的最后索引
    std::vector<std::string> &expectReply;  // 过滤模式下预期返回的查询结果, 校验用的字符串
};
string testYangTreeToStr(GmcStmtT *stmt, const GmcYangTreeT *reply, bool isDiff)
{
    string res;
    if (!isDiff) {
        char *replyJson = NULL;
        EXPECT_EQ(GMERR_OK, GmcYangTreeToJson(reply, &replyJson));
        res = string(replyJson);
        return res;
    }
}
void CheckTreeReply(const GmcYangTreeT **yangTree, uint32_t count, FetchRetCbParam123 *param, bool isDiff = false)
{
    uint32_t idx = param->lastExpectIdx;
    ASSERT_TRUE(param->expectReply.size() >= (idx + count));  // 断言防止越界
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            ASSERT_STREQ(param->expectReply[idx + i].c_str(), "{}");
            continue;
        }
        std::string reply = testYangTreeToStr(param->stmt, yangTree[i], isDiff);
        EXPECT_TRUE(testYangJsonIsEqual(reply.c_str(), param->expectReply[idx + i].c_str()));
        GmcYangFreeTree(yangTree[i]);
    }
}
void AsyncFetchRetCb123(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    FetchRetCbParam123 *param = reinterpret_cast<FetchRetCbParam123 *>(userData);
    TEST_EXPECT_INT32(param->expectStatus, status);
    if (param->expectStatus != GMERR_OK) {
        testGmcGetLastError();
        AW_FUN_Log(LOG_STEP, "param->expectStatus return == %d\n", param->expectStatus);
        return;
    }
    if (status != GMERR_OK) {
        testGmcGetLastError();
        AW_FUN_Log(LOG_STEP, "status return == %d\n", status);
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    uint32_t idx = param->lastExpectIdx;
    int ret = GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count);
    if (ret) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testGmcGetLastError();
        AW_FUN_Log(LOG_STEP, "GmcYangFetchRetDeparse return == %d\n", ret);
    }
    if (yangTree == NULL) {
        return;
    }
    if (param->expectReply.size() != 0) {
        CheckTreeReply(yangTree, count, param);
    }
    // 判断是否全部结果都已获取,全部获取则释放fetchRet,否则再发送一次查询直至全部结果都已经获取
    if (isEnd) {
        param->step++;
        GmcYangFreeFetchRet(fetchRet);
        return;
    }
    param->lastExpectIdx = idx + count;
    TEST_EXPECT_INT32(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param->stmt, NULL, fetchRet,
        AsyncFetchRetCb123, param));
    return;
}

int testYangSetField(
    GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size, const char *fieldname, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldname, (strlen(fieldname) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, optype);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testGmcGetLastError();
        TEST_EXPECT_INT32(GMERR_OK, ret);
        return ret;
    }
    return ret;
}
#if defined ENV_RTOSV2X
// subtree查询0号表
void yang_long_stability_subtree(GmcStmtT *stmt)
{
    // subtree 查询
    int ret;
    std::vector<std::string> reply(10);
    char *subtreeReturnJson1 = NULL;
    readJanssonFile("schema_file/containerReplyZero.json", &subtreeReturnJson1);
    ASSERT_NE((void *)NULL, subtreeReturnJson1);
    reply[0] = subtreeReturnJson1;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_SUBTREE_FILTER);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t f0 = 100;
    ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subtreeReturnJson1);
    AW_FUN_Log(LOG_INFO, "subtree query zero label successfully!\n");
}
#else
void yang_long_stability_subtree(GmcStmtT *stmt)
{
    // subtree 查询
    int ret;
    std::vector<std::string> reply(10);
    char *subtreeReturnJson1 = NULL;
    char *subtreeReturnJson2 = NULL;
    char *subtreeReturnJson3 = NULL;
    char *subtreeReturnJson4 = NULL;
    char *subtreeReturnJson5 = NULL;
    char *subtreeReturnJson6 = NULL;
    char *subtreeReturnJson7 = NULL;
    char *subtreeReturnJson8 = NULL;
    char *subtreeReturnJson9 = NULL;
    char *subtreeReturnJson10 = NULL;
    readJanssonFile("schema_file/containerReplyZero1.json", &subtreeReturnJson1);
    ASSERT_NE((void *)NULL, subtreeReturnJson1);
    readJanssonFile("schema_file/containerReplyZero2.json", &subtreeReturnJson2);
    ASSERT_NE((void *)NULL, subtreeReturnJson2);
    readJanssonFile("schema_file/containerReplyZero3.json", &subtreeReturnJson3);
    ASSERT_NE((void *)NULL, subtreeReturnJson3);
    readJanssonFile("schema_file/containerReplyZero4.json", &subtreeReturnJson4);
    ASSERT_NE((void *)NULL, subtreeReturnJson4);
    readJanssonFile("schema_file/containerReplyZero5.json", &subtreeReturnJson5);
    ASSERT_NE((void *)NULL, subtreeReturnJson5);
    readJanssonFile("schema_file/containerReplyZero6.json", &subtreeReturnJson6);
    ASSERT_NE((void *)NULL, subtreeReturnJson6);
    readJanssonFile("schema_file/containerReplyZero7.json", &subtreeReturnJson7);
    ASSERT_NE((void *)NULL, subtreeReturnJson7);
    readJanssonFile("schema_file/containerReplyZero8.json", &subtreeReturnJson8);
    ASSERT_NE((void *)NULL, subtreeReturnJson8);
    readJanssonFile("schema_file/containerReplyZero9.json", &subtreeReturnJson9);
    ASSERT_NE((void *)NULL, subtreeReturnJson9);
    readJanssonFile("schema_file/containerReplyZero10.json", &subtreeReturnJson10);
    ASSERT_NE((void *)NULL, subtreeReturnJson10);
    reply[0] = subtreeReturnJson1;
    reply[1] = subtreeReturnJson2;
    reply[2] = subtreeReturnJson3;
    reply[3] = subtreeReturnJson4;
    reply[4] = subtreeReturnJson5;
    reply[5] = subtreeReturnJson6;
    reply[6] = subtreeReturnJson7;
    reply[7] = subtreeReturnJson8;
    reply[8] = subtreeReturnJson9;
    reply[9] = subtreeReturnJson10;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_SUBTREE_FILTER);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t f0 = 100;
    ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subtreeReturnJson1);
    free(subtreeReturnJson2);
    free(subtreeReturnJson3);
    free(subtreeReturnJson4);
    free(subtreeReturnJson5);
    free(subtreeReturnJson6);
    free(subtreeReturnJson7);
    free(subtreeReturnJson8);
    free(subtreeReturnJson9);
    free(subtreeReturnJson10);
    AW_FUN_Log(LOG_INFO, "subtree query zero label successfully!\n");
}
#endif
// subtree查询第一类确定性表
void yang_long_stability_subtree_certain_label1(GmcStmtT *stmt)
{
    // subtree 查询
    int ret;
    std::vector<std::string> reply(4);
    char *subtreeReturnJson = NULL;
    #if defined ENV_RTOSV2X
    readJanssonFile("schema_file/containerReplyOne.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    #else
    readJanssonFile("schema_file/containerReplyOne5K.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    #endif
    reply[0] = subtreeReturnJson;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "root1", GMC_OPERATION_SUBTREE_FILTER);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t f0 = 100;
    ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subtreeReturnJson);
    AW_FUN_Log(LOG_INFO, "subtree query certain label One 5K successfully!\n");
}

// subtree查询第一类随机表
void yang_long_stability_subtree_random_label1(GmcStmtT *stmt)
{
    // subtree 查询
    int ret;
    system("rm -rf subtreeReply1");
    system("mkdir subtreeReply1");
    char labelName1[512];
    char cmd1[1024];
    std::vector<std::string> reply(4);
    for (int i = 1; i < 51; i++) {
        char *subtreeReturnJson = NULL;
        #if defined ENV_RTOSV2X
        ret = snprintf(cmd1, 1024, "cp ./schema_file/containerReplyLabelOneV2.json subtreeReply1/");
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd1);
        ret = snprintf(cmd1, 1024, "sed -i 's/root1/rootOne%d/g' ./subtreeReply1/containerReplyLabelOneV2.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd1);
        ret = snprintf(labelName1, 512, "rootOne%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(cmd1, 1024, "mv subtreeReply1/containerReplyLabelOneV2.json subtreeReply1/Label1ReplyHu"
            "ndred_%d.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        readJanssonFile("subtreeReply1/containerReplyLabelOneV2.json", &subtreeReturnJson);
        ASSERT_NE((void *)NULL, subtreeReturnJson);
        #else
        ret = snprintf(cmd1, 1024, "cp ./schema_file/containerReplyupdate100_1.json subtreeReply1/");
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd1);
        ret = snprintf(cmd1, 1024, "sed -i 's/root1/rootOne%d/g' ./subtreeReply1/containerReplyupdate100_1.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd1);
        ret = snprintf(labelName1, 512, "rootOne%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(cmd1, 1024, "mv subtreeReply1/containerReplyupdate100_1.json subtreeReply1/Label1ReplyHu"
            "ndred_%d.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        readJanssonFile("subtreeReply1/containerReplyupdate100_1.json", &subtreeReturnJson);
        ASSERT_NE((void *)NULL, subtreeReturnJson);
        #endif
        reply[0] = subtreeReturnJson;
        GmcNodeT *root = NULL;
        ret = testGmcPrepareStmtByLabelName(stmt, (const char*)labelName1, GMC_OPERATION_SUBTREE_FILTER);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t f0 = 100;
        ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };
        FetchRetCbParam123 param = {
            .step = 0,
            .stmt = stmt,
            .expectStatus = GMERR_OK,
            .filterMode = filters.filterMode,
            .lastExpectIdx = 0,
            .expectReply = reply,
        };
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        free(subtreeReturnJson);
        system(cmd1);
    }
}

// 第一类表批量更新（merge/remove/replace 300条/4K条）
void yang_LabelOne_certainOp_update(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root1", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // merge 300条
    for (int i = 0; i < UPDATE_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root1::P1::A1::T1", GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // remove 300条
    for (int i = 0; i < UPDATE_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root1::P1::A1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // replace 300条
    for (int i = UPDATE_NUM2 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root1::P1::A1::T1", GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(BATCH_NUM2, data.totalNum);
        TEST_EXPECT_INT32(BATCH_NUM2, data.succNum);
        data.stmt = stmt;
        data.expectDiff = &expectDiffBase000;
        ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }

    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root1", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // merge 4K条
    for (int i = 0; i < UPDATE_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root1::P1::A1::T1", GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // remove 4K条
    for (int i = 0; i < UPDATE_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root1::P1::A1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // replace 4K条
    for (int i = UPDATE_NUM1 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root1::P1::A1::T1", GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(BATCH_NUM1, data.totalNum);
        TEST_EXPECT_INT32(BATCH_NUM1, data.succNum);
        data.stmt = stmt;
        data.expectDiff = &expectDiffBase000;
        ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// subtree查询第二类确定性表
void yang_long_stability_subtree_certain_label2(GmcStmtT *stmt)
{
    // subtree 查询
    int ret;
    std::vector<std::string> reply(4);
    char *subtreeReturnJson = NULL;
    #if defined ENV_RTOSV2X
    readJanssonFile("schema_file/containerReplyTwo.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    #else
    readJanssonFile("schema_file/containerReplyTwo5K.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    #endif
    reply[0] = subtreeReturnJson;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "root2", GMC_OPERATION_SUBTREE_FILTER);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t f0 = 100;
    ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subtreeReturnJson);
    AW_FUN_Log(LOG_INFO, "subtree query certain label Two 5K successfully!\n");
}
// subtree查询第二类随机表
void yang_long_stability_subtree_random_label2(GmcStmtT *stmt)
{
    // subtree 查询
    int ret;
    system("rm -rf subtreeReply2");
    system("mkdir subtreeReply2");
    char labelName2[512];
    char cmd2[1024];
    std::vector<std::string> reply(4);
    for (int i = 1; i < 51; i++) {
        char *subtreeReturnJson = NULL;
        #if defined ENV_RTOSV2X
        ret = snprintf(cmd2, 1024, "cp ./schema_file/containerReplyLabelTwoV2.json subtreeReply2/");
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd2);
        ret = snprintf(cmd2, 1024, "sed -i 's/root2/rootTwo%d/g' ./subtreeReply2/containerReplyLabelTwoV2.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd2);
        ret = snprintf(labelName2, 512, "rootTwo%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(cmd2, 1024, "mv subtreeReply2/containerReplyLabelTwoV2.json subtreeReply2/Label2ReplyHund"
            "red_%d.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        readJanssonFile("subtreeReply2/containerReplyLabelTwoV2.json", &subtreeReturnJson);
        ASSERT_NE((void *)NULL, subtreeReturnJson);
        #else
        ret = snprintf(cmd2, 1024, "cp ./schema_file/containerReplyupdate100_2.json subtreeReply2/");
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd2);
        ret = snprintf(cmd2, 1024, "sed -i 's/root2/rootTwo%d/g' ./subtreeReply2/containerReplyupdate100_2.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd2);
        ret = snprintf(labelName2, 512, "rootTwo%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(cmd2, 1024, "mv subtreeReply2/containerReplyupdate100_2.json subtreeReply2/Label2ReplyHund"
            "red_%d.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        readJanssonFile("subtreeReply2/containerReplyupdate100_2.json", &subtreeReturnJson);
        ASSERT_NE((void *)NULL, subtreeReturnJson);
        #endif
        reply[0] = subtreeReturnJson;
        GmcNodeT *root = NULL;
        ret = testGmcPrepareStmtByLabelName(stmt, (const char*)labelName2, GMC_OPERATION_SUBTREE_FILTER);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t f0 = 100;
        ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };
        FetchRetCbParam123 param = {
            .step = 0,
            .stmt = stmt,
            .expectStatus = GMERR_OK,
            .filterMode = filters.filterMode,
            .lastExpectIdx = 0,
            .expectReply = reply,
        };
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        free(subtreeReturnJson);
        system(cmd2);
    }
}
// 第二类表批量更新（merge/remove/replace 300条/4K条）
void yang_LabelTwo_certainOp_update(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    char rootName[512];
    char childName[512];
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root2", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // merge 300条
    for (int i = 0; i < UPDATE_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root2::P1::A2::P2::A1::T1", GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // remove 300条
    for (int i = 0; i < UPDATE_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root2::P1::A2::P2::A1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // replace 300条
    for (int i = UPDATE_NUM2 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root2::P1::A2::P2::A1::T1", GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(BATCH_NUM2, data.totalNum);
        TEST_EXPECT_INT32(BATCH_NUM2, data.succNum);
        data.stmt = stmt;
        data.expectDiff = &expectDiffBase000;
        ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }

    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root2", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // merge 4K条
    for (int i = 0; i < UPDATE_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root2::P1::A2::P2::A1::T1", GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // remove 4K条
    for (int i = 0; i < UPDATE_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root2::P1::A2::P2::A1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // replace 4K条
    for (int i = UPDATE_NUM1 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root2::P1::A2::P2::A1::T1", GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(BATCH_NUM1, data.totalNum);
        TEST_EXPECT_INT32(BATCH_NUM1, data.succNum);
        data.stmt = stmt;
        data.expectDiff = &expectDiffBase000;
        ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// subtree查询第三类确定性表
void yang_long_stability_subtree_certain_label3(GmcStmtT *stmt)
{
    // subtree 查询
    int ret;
    std::vector<std::string> reply(4);
    char *subtreeReturnJson = NULL;
    #if defined ENV_RTOSV2X
    readJanssonFile("schema_file/containerReplyThree.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    #else
    readJanssonFile("schema_file/containerReplyThree5K.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    #endif
    reply[0] = subtreeReturnJson;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "root3", GMC_OPERATION_SUBTREE_FILTER);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t f0 = 100;
    ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subtreeReturnJson);
    AW_FUN_Log(LOG_INFO, "subtree query certain label Three 5K successfully!\n");
}
// subtree查询第三类随机表
void yang_long_stability_subtree_random_label3(GmcStmtT *stmt)
{
    // subtree 查询
    int ret;
    system("rm -rf subtreeReply3");
    system("mkdir subtreeReply3");
    char labelName3[512];
    char cmd3[1024];
    std::vector<std::string> reply(4);
    for (int i = 1; i < 51; i++) {
        char *subtreeReturnJson = NULL;
        #if defined ENV_RTOSV2X
        ret = snprintf(cmd3, 1024, "cp ./schema_file/containerReplyLabelThreeV2.json subtreeReply3/");
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd3);
        ret = snprintf(cmd3, 1024, "sed -i 's/root3/rootThree%d/g' ./subtreeReply3/containerReplyLabelThreeV2.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd3);
        ret = snprintf(labelName3, 512, "rootThree%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(cmd3, 1024, "mv subtreeReply3/containerReplyLabelThreeV2.json subtreeReply3/Label3Reply"
            "Hundred_%d.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        readJanssonFile("subtreeReply3/containerReplyLabelThreeV2.json", &subtreeReturnJson);
        ASSERT_NE((void *)NULL, subtreeReturnJson);
        #else
        ret = snprintf(cmd3, 1024, "cp ./schema_file/containerReplyupdate100_3.json subtreeReply3/");
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd3);
        ret = snprintf(cmd3, 1024, "sed -i 's/root3/rootThree%d/g' ./subtreeReply3/containerReplyupdate100_3.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd3);
        ret = snprintf(labelName3, 512, "rootThree%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(cmd3, 1024, "mv subtreeReply3/containerReplyupdate100_3.json subtreeReply3/Label3Reply"
            "Hundred_%d.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        readJanssonFile("subtreeReply3/containerReplyupdate100_3.json", &subtreeReturnJson);
        ASSERT_NE((void *)NULL, subtreeReturnJson);
        #endif
        reply[0] = subtreeReturnJson;
        GmcNodeT *root = NULL;
        ret = testGmcPrepareStmtByLabelName(stmt, (const char*)labelName3, GMC_OPERATION_SUBTREE_FILTER);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t f0 = 100;
        ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };
        FetchRetCbParam123 param = {
            .step = 0,
            .stmt = stmt,
            .expectStatus = GMERR_OK,
            .filterMode = filters.filterMode,
            .lastExpectIdx = 0,
            .expectReply = reply,
        };
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        free(subtreeReturnJson);
        system(cmd3);
    }
}
// 第三类表批量更新（merge/remove/replace 300条/4K条）
void yang_LabelThree_certainOp_update(GmcConnT *conn, AsyncUserDataT data)
{
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root3", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(container)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P2(container)
    ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P3(container)
    ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P4(container)
    ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // merge 300条
    for (int i = 0; i < UPDATE_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root3::P1::P2::P3::P4::T1", GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_MERGE, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // remove 300条
    for (int i = 0; i < UPDATE_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root3::P1::P2::P3::P4::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // replace 300条
    for (int i = UPDATE_NUM2 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root3::P1::P2::P3::P4::T1", GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(BATCH_NUM2, data.totalNum);
        TEST_EXPECT_INT32(BATCH_NUM2, data.succNum);
        data.stmt = stmt;
        data.expectDiff = &expectDiffBase000;
        ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }

    // 事务2批量更新4k条
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root3", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(container)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P2(container)
    ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P3(container)
    ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P4(container)
    ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // merge 4K条
    for (int i = 0; i < UPDATE_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root3::P1::P2::P3::P4::T1", GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_MERGE, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // remove 4K条
    for (int i = 0; i < UPDATE_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root3::P1::P2::P3::P4::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // replace 4K条
    for (int i = UPDATE_NUM1 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root3::P1::P2::P3::P4::T1", GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(BATCH_NUM1, data.totalNum);
        TEST_EXPECT_INT32(BATCH_NUM1, data.succNum);
        data.stmt = stmt;
        data.expectDiff = &expectDiffBase000;
        ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        ret = GmcBatchDestroy(batch);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));

        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// subtree查询第四类确定性表
void yang_long_stability_subtree_certain_label4(GmcStmtT *stmt)
{
    // subtree 查询
    int ret;
    std::vector<std::string> reply(4);
    char *subtreeReturnJson = NULL;
    readJanssonFile("schema_file/containerReply4.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    reply[0] = subtreeReturnJson;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, "root4", GMC_OPERATION_SUBTREE_FILTER);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    uint32_t f0 = 100;
    ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    free(subtreeReturnJson);
}
// subtree查询第四类随机表
void yang_long_stability_subtree_random_label4(GmcStmtT *stmt)
{
    // subtree 查询
    int ret;
    system("rm -rf subtreeReply4");
    system("mkdir subtreeReply4");
    char labelName4[512];
    char cmd4[1024];
    std::vector<std::string> reply(4);
    for (int i = 1; i < 51; i++) {
        char *subtreeReturnJson = NULL;
        #if defined ENV_RTOSV2X
        ret = snprintf(cmd4, 1024, "cp ./schema_file/containerReplyLabelFourV2.json subtreeReply4/");
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd4);
        ret = snprintf(cmd4, 1024, "sed -i 's/root4/rootFour%d/g' ./subtreeReply4/containerReplyLabelFourV2.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd4);
        ret = snprintf(labelName4, 512, "rootFour%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(cmd4, 1024, "mv subtreeReply4/containerReplyLabelFourV2.json subtreeReply4/Label4ReplyHun"
            "dred_%d.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        readJanssonFile("subtreeReply4/containerReplyLabelFourV2.json", &subtreeReturnJson);
        ASSERT_NE((void *)NULL, subtreeReturnJson);
        #else
        ret = snprintf(cmd4, 1024, "cp ./schema_file/containerReplyupdate100_4.json subtreeReply4/");
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd4);
        ret = snprintf(cmd4, 1024, "sed -i 's/root4/rootFour%d/g' ./subtreeReply4/containerReplyupdate100_4.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        system(cmd4);
        ret = snprintf(labelName4, 512, "rootFour%d", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        ret = snprintf(cmd4, 1024, "mv subtreeReply4/containerReplyupdate100_4.json subtreeReply4/Label4ReplyHun"
            "dred_%d.json", i);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "snprintf error\n");
            return;
        }
        readJanssonFile("subtreeReply4/containerReplyupdate100_4.json", &subtreeReturnJson);
        ASSERT_NE((void *)NULL, subtreeReturnJson);
        #endif
        reply[0] = subtreeReturnJson;
        GmcNodeT *root = NULL;
        ret = testGmcPrepareStmtByLabelName(stmt, (const char*)labelName4, GMC_OPERATION_SUBTREE_FILTER);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt, &root);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        uint32_t f0 = 100;
        ret = testYangSetField(root, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcSubtreeFilterItemT filter = {
            .rootName = NULL,
            .subtree = {.obj = root},
            .jsonFlag = GMC_JSON_INDENT(4),
            .maxDepth = 0,
            .isLocationFilter = 0,
            .defaultMode = 0,
            .configFlag = 0,
        };
        GmcSubtreeFilterT filters = {
            .filterMode = GMC_FETCH_OBJ,
            .filter = &filter,
        };
        FetchRetCbParam123 param = {
            .step = 0,
            .stmt = stmt,
            .expectStatus = GMERR_OK,
            .filterMode = filters.filterMode,
            .lastExpectIdx = 0,
            .expectReply = reply,
        };
        ret = GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncSubtreeRecv_API(&param);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        free(subtreeReturnJson);
        system(cmd4);
    }
}

// 读取所有表数据
void yang_long_stability_read(GmcStmtT *stmt)
{
    // subtree查询所有表数据
    yang_long_stability_subtree(stmt);
    yang_long_stability_subtree_certain_label1(stmt);
    yang_long_stability_subtree_certain_label2(stmt);
    yang_long_stability_subtree_certain_label3(stmt);
    yang_long_stability_subtree_random_label1(stmt);
    yang_long_stability_subtree_random_label2(stmt);
    yang_long_stability_subtree_random_label3(stmt);
    yang_long_stability_subtree_random_label4(stmt);
}
// 对50张表（除0号表）进行随机操作，开启事务，
// 表1操作100条元素merge，表2操作100条元素remove，表3操作100条元素replace，3个操作一起提交事务，
// 表4操作4K条元素merge，表5操作4K条元素remove，表6操作4K条元素replace，3个操作一起提交事务，
// 若其中有一个操作失败，则回滚，成功则提交

// 第一类表随机批量更新（merge/remove/replace 100条/4K条)
void yang_LabelOne_randomOp_update(GmcConnT *conn, AsyncUserDataT data)
{
    uint32_t randowVal[6];
    for (int i = 0; i < 6; i++) {
        randowVal[i] = rand() % 50 + 1;
    }
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    char label1Root[512];
    char label1List[512];
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 表1 merge100条
    ret = snprintf(label1Root, 512, "rootOne%d", randowVal[0]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label1List, 512, "rootOne%d::P1::A1::T1", randowVal[0]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label1Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // merge 100条
    for (int i = 0; i < RANDOM_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label1List, GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 表2 remove100条
    ret = snprintf(label1Root, 512, "rootOne%d", randowVal[1]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label1List, 512, "rootOne%d::P1::A1::T1", randowVal[1]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label1Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove 100条
    for (int i = 0; i < RANDOM_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label1List, GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 表3 replace100条
    ret = snprintf(label1Root, 512, "rootOne%d", randowVal[2]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label1List, 512, "rootOne%d::P1::A1::T1", randowVal[2]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label1Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // replace 100条
    for (int i = 0; i < RANDOM_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label1List, GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        // 回滚事务
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM1, data.totalNum);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM1, data.succNum);
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    // 事务2进行表456的操作
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 表4 merge100条
    ret = snprintf(label1Root, 512, "rootOne%d", randowVal[3]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label1List, 512, "rootOne%d::P1::A1::T1", randowVal[3]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label1Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // merge 4K条
    for (int i = 0; i < RANDOM_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label1List, GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 表5 remove4K条
    ret = snprintf(label1Root, 512, "rootOne%d", randowVal[4]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label1List, 512, "rootOne%d::P1::A1::T1", randowVal[4]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label1Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove 4K条
    for (int i = 0; i < RANDOM_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label1List, GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 表6 replace4K条
    ret = snprintf(label1Root, 512, "rootOne%d", randowVal[5]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label1List, 512, "rootOne%d::P1::A1::T1", randowVal[5]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label1Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // replace 4K条
    for (int i = 0; i < RANDOM_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label1List, GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM2, data.totalNum);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM2, data.succNum);
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// 第二类表随机批量更新（merge/remove/replace 100条/4K条）
void yang_LabelTwo_randomOp_update(GmcConnT *conn, AsyncUserDataT data)
{
    uint32_t randowVal[6];
    for (int i = 0; i < 6; i++) {
        randowVal[i] = rand() % 50 + 1;
    }
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    char label2Root[512];
    char label2List[512];
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 事务1操作随机表123
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = snprintf(label2Root, 512, "rootTwo%d", randowVal[0]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label2List, 512, "rootTwo%d::P1::A2::P2::A1::T1", randowVal[0]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label2Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // merge 100条
    for (int i = 0; i < RANDOM_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label2List, GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label2Root, 512, "rootTwo%d", randowVal[1]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label2List, 512, "rootTwo%d::P1::A2::P2::A1::T1", randowVal[1]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label2Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove 100条
    for (int i = 0; i < RANDOM_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label2List, GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label2Root, 512, "rootTwo%d", randowVal[2]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label2List, 512, "rootTwo%d::P1::A2::P2::A1::T1", randowVal[2]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label2Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // replace 100条
    for (int i = RANDOM_NUM1 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label2List, GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM1, data.totalNum);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM1, data.succNum);
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    // 事务2操作随机表456
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = snprintf(label2Root, 512, "rootTwo%d", randowVal[3]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label2List, 512, "rootTwo%d::P1::A2::P2::A1::T1", randowVal[3]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label2Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // merge 4K条
    for (int i = 0; i < RANDOM_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label2List, GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label2Root, 512, "rootTwo%d", randowVal[4]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label2List, 512, "rootTwo%d::P1::A2::P2::A1::T1", randowVal[4]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label2Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove 4K条
    for (int i = 0; i < RANDOM_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label2List, GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label2Root, 512, "rootTwo%d", randowVal[5]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label2List, 512, "rootTwo%d::P1::A2::P2::A1::T1", randowVal[5]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label2Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // replace 4K条
    for (int i = RANDOM_NUM2 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label2List, GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM2, data.totalNum);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM2, data.succNum);
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}

// 第三类表随机批量更新（merge/remove/replace 100条/4K条）
void yang_LabelThree_randomOp_update(GmcConnT *conn, AsyncUserDataT data)
{
    uint32_t randowVal[6];
    for (int i = 0; i < 6; i++) {
        randowVal[i] = rand() % 50 + 1;
    }
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    char label3Root[512];
    char label3List[512];
    // 事务1操作随机表123
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = snprintf(label3Root, 512, "rootThree%d", randowVal[0]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label3List, 512, "rootThree%d::P1::P2::P3::P4::T1", randowVal[0]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, label3Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(container)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P2(container)
    ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P3(container)
    ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P4(container)
    ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // merge 100条
    for (int i = 0; i < RANDOM_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label3List, GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_MERGE, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label3Root, 512, "rootThree%d", randowVal[1]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label3List, 512, "rootThree%d::P1::P2::P3::P4::T1", randowVal[1]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, label3Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(container)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P2(container)
    ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P3(container)
    ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P4(container)
    ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove 100条
    for (int i = 0; i < RANDOM_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label3List, GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label3Root, 512, "rootThree%d", randowVal[2]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label3List, 512, "rootThree%d::P1::P2::P3::P4::T1", randowVal[2]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, label3Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(container)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P2(container)
    ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P3(container)
    ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P4(container)
    ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // replace 100条
    for (int i = RANDOM_NUM1 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label3List, GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM1, data.totalNum);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM1, data.succNum);
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    // 事务2操作随机表456
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = snprintf(label3Root, 512, "rootThree%d", randowVal[3]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label3List, 512, "rootThree%d::P1::P2::P3::P4::T1", randowVal[3]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, label3Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(container)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P2(container)
    ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P3(container)
    ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P4(container)
    ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // merge 4K条
    for (int i = 0; i < RANDOM_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label3List, GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_MERGE, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label3Root, 512, "rootThree%d", randowVal[4]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label3List, 512, "rootThree%d::P1::P2::P3::P4::T1", randowVal[4]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, label3Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(container)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P2(container)
    ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P3(container)
    ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P4(container)
    ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove 4K条
    for (int i = 0; i < RANDOM_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label3List, GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label3Root, 512, "rootThree%d", randowVal[5]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label3List, 512, "rootThree%d::P1::P2::P3::P4::T1", randowVal[5]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = testGmcPrepareStmtByLabelName(stmt, label3Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(container)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P2(container)
    ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P3(container)
    ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P4(container)
    ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // replace 4K条
    for (int i = RANDOM_NUM2 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label3List, GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM2, data.totalNum);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM2, data.succNum);
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// 第四类表随机批量更新（merge/remove/replace 100条/4K条
void yang_LabelFour_randomOp_update(GmcConnT *conn, AsyncUserDataT data)
{
    uint32_t randowVal[6];
    for (int i = 0; i < 6; i++) {
        randowVal[i] = rand() % 50 + 1;
    }
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    int ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    char label4Root[512];
    char label4List[512];
    // 事务1操作随机表123
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = snprintf(label4Root, 512, "rootFour%d", randowVal[0]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label4List, 512, "rootFour%d::P1::T1", randowVal[0]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label4Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点(container) P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    
    // merge 100条
    for (int i = 0; i < RANDOM_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label4List, GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_MERGE, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label4Root, 512, "rootFour%d", randowVal[1]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label4List, 512, "rootFour%d::P1::T1", randowVal[1]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label4Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点(container) P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove 100条
    for (int i = 0; i < RANDOM_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label4List, GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label4Root, 512, "rootFour%d", randowVal[2]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label4List, 512, "rootFour%d::P1::T1", randowVal[2]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label4Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点(container) P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // replace 100条
    for (int i = RANDOM_NUM1 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label4List, GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM1, data.totalNum);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM1, data.succNum);
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    // 事务2操作随机表456
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = snprintf(label4Root, 512, "rootFour%d", randowVal[3]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label4List, 512, "rootFour%d::P1::T1", randowVal[3]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label4Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点(container) P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    
    // merge 4K条
    for (int i = 0; i < RANDOM_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label4List, GMC_OPERATION_MERGE);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_MERGE, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label4Root, 512, "rootFour%d", randowVal[4]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label4List, 512, "rootFour%d::P1::T1", randowVal[4]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label4Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点(container) P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove 4K条
    for (int i = 0; i < RANDOM_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label4List, GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    ret = snprintf(label4Root, 512, "rootFour%d", randowVal[5]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(label4List, 512, "rootFour%d::P1::T1", randowVal[5]);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, label4Root, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点(container) P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // replace 4K条
    for (int i = RANDOM_NUM2 - 1; i >= 0; i--) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, label4List, GMC_OPERATION_REPLACE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        GmcPropValueT refKey;
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, NULL);
        ret = GmcYangSetListLocator(stmt_1, &listProp);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        UninitListProperty(&listProp);
        testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (data.status) {
        if (data.status != GMERR_OUT_OF_MEMORY) {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
    } else {
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM2, data.totalNum);
        TEST_EXPECT_INT32(RANDOM_BATCH_NUM2, data.succNum);
        // 提交事务
        ret = GmcTransCommitAsync(conn, trans_commit_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        testWaitAsyncRecv(&data);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        } else {
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
    }
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// 空闲的conn[3]启动事物，对table0的固定操作区域的元素批量删除2000条，不提交
void yang_label0_remove_TwoThousand_uncommit(GmcConnT *conn)
{
    int ret;
    AsyncUserDataT data = {0};
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
	AsyncUserDataT data1 = {0};
    ret = GmcTransCreateSavepointAsync(conn, "sp1", trans_start_callback, &data1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    TEST_EXPECT_INT32(GMERR_OK, data1.status);
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = 0; i < UNCOMMIT_NUM1; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (!data.status) {
        TEST_EXPECT_INT32(UNCOMMIT_NUM1 + 1, data.totalNum);
        TEST_EXPECT_INT32(UNCOMMIT_NUM1 + 1, data.succNum);
    }
    ret = GmcTransRollBackSavepointAsync(conn, "sp1", trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
}
// 空闲的conn[7]启动事物，然后对table0的随机操作区域的元素批量删除20000条，不提交
void yang_label0_remove_TwentyThousand_uncommit(GmcConnT *conn)
{
    int ret;
    AsyncUserDataT data = {0};
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    GmcYangListLocatorT listProp;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = 0; i < UNCOMMIT_NUM / 2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    TEST_EXPECT_INT32(UNCOMMIT_NUM / 2 + 1, data.totalNum);
    TEST_EXPECT_INT32(UNCOMMIT_NUM / 2 + 1, data.succNum);
    AsyncUserDataT data1 = {0};
    ret = GmcTransCreateSavepointAsync(conn, "sp1", trans_start_callback, &data1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    TEST_EXPECT_INT32(GMERR_OK, data1.status);
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    for (int i = UNCOMMIT_NUM / 2; i < UNCOMMIT_NUM; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root::P1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (!data.status) {
        TEST_EXPECT_INT32(UNCOMMIT_NUM / 2 + 1, data.totalNum);
        TEST_EXPECT_INT32(UNCOMMIT_NUM / 2 + 1, data.succNum);
    }
    ret = GmcTransCreateSavepointAsync(conn, "sp2", trans_start_callback, &data1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    TEST_EXPECT_INT32(GMERR_OK, data1.status);
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
}
// 空闲的conn[11]启动事物，对schema1固定表固定的元素批量删除2000条，不提交
void yang_label1_certain_remove_uncommit(GmcConnT *conn)
{
    int ret;
    AsyncUserDataT data = {0};
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root1", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove 2000条
    for (int i = 0; i < UNCOMMIT_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root1::P1::A1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (!data.status) {
        TEST_EXPECT_INT32(UNCOMMIT_NUM2 + 1, data.totalNum);
        TEST_EXPECT_INT32(UNCOMMIT_NUM2 + 1, data.succNum);
    }
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// 空闲的conn[15]启动事物，对schema1随机表元素批量删除80条，不提交
void yang_label1_random_remove_uncommit(GmcConnT *conn)
{
    int ret;
    AsyncUserDataT data = {0};
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    uint32_t randowVal = rand() % 50 + 1;
    char labelList[512];
    char labelRoot[512];
    ret = snprintf(labelRoot, 512, "rootOne%d", randowVal);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(labelList, 512, "rootOne%d::P1::A1::T1", randowVal);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, (const char*)labelRoot, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A1", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // remove 80条
    for (int i = 0; i < UNCOMMIT_NUM3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, (const char*)labelList, GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (!data.status) {
        TEST_EXPECT_INT32(UNCOMMIT_NUM3 + 1, data.totalNum);
        TEST_EXPECT_INT32(UNCOMMIT_NUM3 + 1, data.succNum);
    }
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}

// 空闲的conn[19]启动事物，对schema2固定表固定的元素批量删除2000条，不提交
void yang_label2_certain_remove_uncommit(GmcConnT *conn)
{
    int ret;
    AsyncUserDataT data = {0};
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root2", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // remove 2000条
    for (int i = 0; i < UNCOMMIT_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root2::P1::A2::P2::A1::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (!data.status) {
        TEST_EXPECT_INT32(UNCOMMIT_NUM2 + 1, data.totalNum);
        TEST_EXPECT_INT32(UNCOMMIT_NUM2 + 1, data.succNum);
    }

    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// 空闲的conn[23]启动事物，对schema2随机表元素批量删除80条，不提交
void yang_label2_random_remove_uncommit(GmcConnT *conn)
{
    int ret;
    AsyncUserDataT data = {0};
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    uint32_t randowVal = rand() % 50 + 1;
    char labelList[512];
    char labelRoot[512];
    ret = snprintf(labelRoot, 512, "rootTwo%d", randowVal);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(labelList, 512, "rootTwo%d::P1::A2::P2::A1::T1", randowVal);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, (const char*)labelRoot, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode, "A2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(choice)
    ret = GmcYangEditChildNode(childNode1, "P2", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 A1(case)
    ret = GmcYangEditChildNode(childNode2, "A1", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // remove 80条
    for (int i = 0; i < UNCOMMIT_NUM3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, (const char*)labelList, GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (!data.status) {
        TEST_EXPECT_INT32(UNCOMMIT_NUM3 + 1, data.totalNum);
        TEST_EXPECT_INT32(UNCOMMIT_NUM3 + 1, data.succNum);
    }

    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// 空闲的conn[27]启动事物，对schema3固定表固定的元素批量删除2000条，不提交
void yang_label3_certain_remove_uncommit(GmcConnT *conn)
{
    int ret;
    AsyncUserDataT data = {0};
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransCreateSavepointAsync(conn, "sp1", trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, "root3", GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(container)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P2(container)
    ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P3(container)
    ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P4(container)
    ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // remove 2000条
    for (int i = 0; i < UNCOMMIT_NUM2; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, "root3::P1::P2::P3::P4::T1", GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (!data.status) {
        TEST_EXPECT_INT32(UNCOMMIT_NUM2 + 1, data.totalNum);
        TEST_EXPECT_INT32(UNCOMMIT_NUM2 + 1, data.succNum);
    }
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
// 空闲的conn[31]启动事物，对schema3随机表元素批量删除80条，不提交
void yang_label3_random_remove_uncommit(GmcConnT *conn)
{
    int ret;
    AsyncUserDataT data = {0};
    // 启动事务
    GmcStmtT *stmt = NULL;
    GmcStmtT *stmt_1 = NULL;
    GmcBatchT *batch = NULL;
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;
    uint32_t randowVal = rand() % 50 + 1;
    char labelList[512];
    char labelRoot[512];
    ret = snprintf(labelRoot, 512, "rootThree%d", randowVal);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = snprintf(labelList, 512, "rootThree%d::P1::P2::P3::P4::T1", randowVal);
    if (ret < 0) {
        AW_FUN_Log(LOG_INFO, "snprintf error\n");
        return;
    }
    ret = GmcAllocStmt(conn, &stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcAllocStmt(conn, &stmt_1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransStartAsync(conn, &trxConfig, trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);

    // 设置批处理batch参数和设置diff开关
    ret = testBatchPrepareAndSetDiff(conn, &batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcTransCreateSavepointAsync(conn, "sp1", trans_start_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    TEST_EXPECT_INT32(GMERR_OK, data.status);
    // 设置根节点 root
    ret = testGmcPrepareStmtByLabelName(stmt, (const char*)labelRoot, GMC_OPERATION_NONE);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &rootNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P1(container)
    ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P2(container)
    ret = GmcYangEditChildNode(childNode, "P2", GMC_OPERATION_NONE, &childNode1);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P3(container)
    ret = GmcYangEditChildNode(childNode1, "P3", GMC_OPERATION_NONE, &childNode2);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    // 设置node节点 P4(container)
    ret = GmcYangEditChildNode(childNode2, "P4", GMC_OPERATION_NONE, &childNode3);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmt);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // remove 80条
    for (int i = 0; i < UNCOMMIT_NUM3; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_1, (const char*)labelList, GMC_OPERATION_REMOVE_GRAPH);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmt, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt_1, &rootNode1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_1, "PK");
        TEST_EXPECT_INT32(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    testWaitAsyncRecv(&data);
    if (!data.status) {
        TEST_EXPECT_INT32(UNCOMMIT_NUM3 + 1, data.totalNum);
        TEST_EXPECT_INT32(UNCOMMIT_NUM3 + 1, data.succNum);
    }
    ret = GmcBatchDestroy(batch);
    TEST_EXPECT_INT32(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(stmt_1);
    GmcFreeStmt(stmt);
}
GmcStmtT *stmt[32] = {0};
GmcConnT *conn[32] = {0};

// 线程0,常规操作,namespace 1为主,table0确定性操作,table0随机操作,schema0确定操作,schema0随机操作 conn[0]~conn[7]
void OperationThread0Func()
{
    int ret;
    AsyncUserDataT data = {0};
    uint64_t currentTime = 0;
    double timeDiff = 0;
    uint64_t table0Update100StartTime = 0;
    uint64_t table0Update4KStartTime = 0;
    uint64_t table0SubtreeAllStartTime = 0;
    uint64_t table0ReadFilterStartTime = 0;
    while (1) {
        bool flag = false;
        // 创建异步连接
        for (int i = 0; i < 4; i++) {
            YangConnOptionT connOptions = {0};
            connOptions.isLobConn = true;
            connOptions.timeoutMs = 200000;
            ret = TestYangGmcConnect(&conn[i], &stmt[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            // 当建连失败退出本次循环，不应往下执行操作
            if (ret != GMERR_OK) {
                if (ret != GMERR_MEMORY_OPERATE_FAILED && ret != GMERR_NO_DATA &&
                    ret != GMERR_CONNECTION_RESET_BY_PEER) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
                flag = true;
                break;
            }
            ret = GmcUseNamespaceAsync(stmt[i], "yang0", use_namespace_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        if (flag) {
            continue;
        }
        yang_label0_remove_TwoThousand_uncommit(conn[1]);
        yang_label0_remove_TwentyThousand_uncommit(conn[2]);
        currentTime = ComGettimeofdayUsec();
        timeDiff = (currentTime - table0Update100StartTime) / 1000.0 / 1000.0;
        if (table0Update100StartTime == 0 || timeDiff >= 10) {
            table0Update100StartTime = currentTime;
            yang_table0_certainOp_update100(conn[0], data);
            yang_table0_certainOp_update4K(conn[0], data);
            yang_table0_RandomOp_update100(conn[0], data);
        }
        timeDiff = (currentTime - table0Update4KStartTime) / 1000.0 / 1000.0;
        if (table0Update4KStartTime == 0 || timeDiff >= 120) {
            table0Update4KStartTime = currentTime;
            yang_table0_RandomOp_update4K(conn[0], data);
        }
        timeDiff = (currentTime - table0SubtreeAllStartTime) / 1000.0 / 1000.0;
        if (table0SubtreeAllStartTime == 0 || timeDiff >= 300) {
            table0SubtreeAllStartTime = currentTime;
            yang_long_stability_subtree(stmt[0]);
        }
        // 释放异步连接
        for (int i = 0; i < 4; i++) {
            ret = testGmcDisconnect(conn[i], stmt[i]);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}

// 线程1,常规操作,namespace 1为主,schema1确定性操作,其他表随机操作 conn[8]~conn[15]
void OperationThread1Func()
{
    int ret;
    AsyncUserDataT data = {0};
    while (1) {
        bool flag = false;
        // 创建异步连接
        for (int i = 4; i < 8; i++) {
            YangConnOptionT connOptions = {0};
            connOptions.isLobConn = true;
            connOptions.timeoutMs = 200000;
            ret = TestYangGmcConnect(&conn[i], &stmt[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            // 当建连失败退出本次循环，不应往下执行操作
            if (ret != GMERR_OK) {
                if (ret != GMERR_MEMORY_OPERATE_FAILED && ret != GMERR_NO_DATA &&
                    ret != GMERR_CONNECTION_RESET_BY_PEER) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
                flag = true;
                break;
            }
            ret = GmcUseNamespaceAsync(stmt[i], "yang0", use_namespace_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        if (flag) {
            continue;
        }
        yang_label1_certain_remove_uncommit(conn[4]);
        yang_LabelOne_certainOp_update(conn[5], data);
        yang_label1_random_remove_uncommit(conn[6]);
        // 五十张表随机操作
        yang_LabelOne_randomOp_update(conn[7], data);
        yang_LabelTwo_randomOp_update(conn[7], data);
        yang_LabelThree_randomOp_update(conn[7], data);
        yang_LabelFour_randomOp_update(conn[7], data);
        // 释放异步连接
        for (int i = 4; i < 8; i++) {
            ret = testGmcDisconnect(conn[i], stmt[i]);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}

// 线程2,常规操作,namespace 1为主,schema2确定性操作,其他表随机操作 conn[16]~conn[23]
void OperationThread2Func()
{
    int ret;
    AsyncUserDataT data = {0};
    while (1) {
        bool flag = false;
        // 创建异步连接
        for (int i = 8; i < 12; i++) {
            YangConnOptionT connOptions = {0};
            connOptions.isLobConn = true;
            connOptions.timeoutMs = 200000;
            ret = TestYangGmcConnect(&conn[i], &stmt[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            // 当建连失败退出本次循环，不应往下执行操作
            if (ret != GMERR_OK) {
                if (ret != GMERR_MEMORY_OPERATE_FAILED && ret != GMERR_NO_DATA &&
                    ret != GMERR_CONNECTION_RESET_BY_PEER) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
                flag = true;
                break;
            }
            ret = GmcUseNamespaceAsync(stmt[i], "yang0", use_namespace_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        if (flag) {
            continue;
        }
        yang_label2_certain_remove_uncommit(conn[8]);
        yang_LabelTwo_certainOp_update(conn[9], data);
        yang_label2_random_remove_uncommit(conn[10]);
        // 五十张表随机操作
        yang_LabelOne_randomOp_update(conn[11], data);
        yang_LabelTwo_randomOp_update(conn[11], data);
        yang_LabelThree_randomOp_update(conn[11], data);
        yang_LabelFour_randomOp_update(conn[11], data);
        // 释放异步连接
        for (int i = 8; i < 12; i++) {
            ret = testGmcDisconnect(conn[i], stmt[i]);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}

// 线程3,常规操作,namespace 1为主,schema3确定性操作,其他表随机操作 conn[24]~conn[32]
void OperationThread3Func()
{
    int ret;
    AsyncUserDataT data = {0};
    while (1) {
        bool flag = false;
        // 创建异步连接
        for (int i = 12; i < 16; i++) {
            YangConnOptionT connOptions = {0};
            connOptions.isLobConn = true;
            connOptions.timeoutMs = 200000;
            ret = TestYangGmcConnect(&conn[i], &stmt[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            // 当建连失败退出本次循环，不应往下执行操作
            if (ret != GMERR_OK) {
                if (ret != GMERR_MEMORY_OPERATE_FAILED && ret != GMERR_NO_DATA &&
                    ret != GMERR_CONNECTION_RESET_BY_PEER) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
                flag = true;
                break;
            }
            ret = GmcUseNamespaceAsync(stmt[i], "yang0", use_namespace_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        if (flag) {
            continue;
        }
        yang_label3_certain_remove_uncommit(conn[12]);
        yang_LabelThree_certainOp_update(conn[13], data);
        yang_label3_random_remove_uncommit(conn[14]);
        // 五十张表随机操作
        yang_LabelOne_randomOp_update(conn[15], data);
        yang_LabelTwo_randomOp_update(conn[15], data);
        yang_LabelThree_randomOp_update(conn[15], data);
        yang_LabelFour_randomOp_update(conn[15], data);
        // 释放异步连接
        for (int i = 12; i < 16; i++) {
            ret = testGmcDisconnect(conn[i], stmt[i]);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}

// 线程4,常规操作,namespace 1为主,schema3确定性操作,其他表随机操作 conn[24]~conn[32]
void OperationThread4Func()
{
    int ret;
    AsyncUserDataT data = {0};
    while (1) {
        bool flag = false;
        // 创建异步连接
        for (int i = 16; i < 20; i++) {
            YangConnOptionT connOptions = {0};
            connOptions.isLobConn = true;
            connOptions.timeoutMs = 200000;
            ret = TestYangGmcConnect(&conn[i], &stmt[i], GMC_CONN_TYPE_ASYNC, &connOptions);
            // 当建连失败退出本次循环，不应往下执行操作
            if (ret != GMERR_OK) {
                if (ret != GMERR_MEMORY_OPERATE_FAILED && ret != GMERR_NO_DATA &&
                    ret != GMERR_CONNECTION_RESET_BY_PEER) {
                    TEST_EXPECT_INT32(GMERR_OK, ret);
                }
                flag = true;
                break;
            }
            ret = GmcUseNamespaceAsync(stmt[i], "yang0", use_namespace_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
        }
        if (flag) {
            continue;
        }
        yang_label3_certain_remove_uncommit(conn[16]);
        yang_LabelThree_certainOp_update(conn[17], data);
        yang_label3_random_remove_uncommit(conn[18]);
        // 五十张表随机操作
        yang_LabelOne_randomOp_update(conn[19], data);
        yang_LabelTwo_randomOp_update(conn[19], data);
        yang_LabelThree_randomOp_update(conn[19], data);
        yang_LabelFour_randomOp_update(conn[19], data);
        // 释放异步连接
        for (int i = 16; i < 20; i++) {
            ret = testGmcDisconnect(conn[i], stmt[i]);
            TEST_EXPECT_INT32(GMERR_OK, ret);
        }
    }
}

// 线程5,常规操作循环1-3
// 1.开事务开启diff
// 2.开同名savepoint，编辑数据，提交数据，失败回滚savepoint，该步骤失败需要重新走一遍
// 3.步骤2成功查询diff，提交/回滚事务
// 线程0有循环全量subtree查询，这里不查询

void OperationThread5Func()
{
    int ret;
    AsyncUserDataT data = {0};
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt = NULL;
    while (1) {
        // 创建异步连接
        YangConnOptionT connOptions = {0};
        connOptions.isLobConn = true;
        connOptions.timeoutMs = 200000;
        // 有可能是长事务不开启监控
        connOptions.logThreshold = 0xFFFFFFFF;
        connOptions.rollBackThreshold = 0xFFFFFFFF;
        ret = TestYangGmcConnect(&g_conn, &g_stmt, GMC_CONN_TYPE_ASYNC, &connOptions);
        // 当建连失败退出本次循环，不应往下执行操作
        if (ret != GMERR_OK) {
            if (ret != GMERR_MEMORY_OPERATE_FAILED && ret != GMERR_NO_DATA &&
                ret != GMERR_CONNECTION_RESET_BY_PEER) {
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            continue;
        }
        ret = GmcUseNamespaceAsync(g_stmt, "yang0", use_namespace_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        // 启动事务
        GmcStmtT *g_stmt_1 = NULL;
        GmcBatchT *batch = NULL;
        GmcTxConfigT trxConfig;
        GmcYangListLocatorT listProp;
        trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        trxConfig.readOnly = false;
        trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        GmcNodeT *rootNode = NULL;
        GmcNodeT *rootNode1 = NULL;
        GmcNodeT *childNode = NULL;
        GmcNodeT *childNode1 = NULL;
        GmcNodeT *childNode2 = NULL;
        ret = GmcAllocStmt(g_conn, &g_stmt_1);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = GmcTransStartAsync(g_conn, &trxConfig, trans_start_callback, &data);
        TEST_EXPECT_INT32(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        TEST_EXPECT_INT32(GMERR_OK, data.status);
        while (1) {
            // 设置批处理batch参数和设置diff开关
            ret = testBatchPrepareAndSetDiff(g_conn, &batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcTransCreateSavepointAsync(g_conn, "sp5", trans_start_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
            // 设置根节点 root
            ret = testGmcPrepareStmtByLabelName(g_stmt, "root", GMC_OPERATION_NONE);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcYangSetRoot(batch, g_stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcGetRootNode(g_stmt, &rootNode);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            // 设置node节点 P1
            ret = GmcYangEditChildNode(rootNode, "P1", GMC_OPERATION_NONE, &childNode);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, g_stmt);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            // remove 1k条
            for (int i = START_NUM; i < START_NUM + THREAD5_NUM; i++) {
                ret = testGmcPrepareStmtByLabelName(g_stmt_1, "root::P1::T1", GMC_OPERATION_REMOVE_GRAPH);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, g_stmt, g_stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(g_stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(g_stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyName(g_stmt_1, "PK");
                TEST_EXPECT_INT32(GMERR_OK, ret);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, g_stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            uint32_t indexVal = START_NUM - 1;
            // merge 1k条
            for (int i = START_NUM; i < START_NUM + THREAD5_NUM; i++) {
                ret = testGmcPrepareStmtByLabelName(g_stmt_1, "root::P1::T1", GMC_OPERATION_MERGE);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, g_stmt, g_stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(g_stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(g_stmt_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcSetIndexKeyName(g_stmt_1, "PK");
                TEST_EXPECT_INT32(GMERR_OK, ret);
                GmcPropValueT refKey;
                InitRefKeys(&refKey, 1, &indexVal);
                InitListProperty(&listProp, GMC_YANG_LIST_POSITION_AFTER, &refKey);
                ret = GmcYangSetListLocator(g_stmt_1, &listProp);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty_merge(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_MERGE, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_MERGE, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_MERGE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, g_stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                UninitListProperty(&listProp);
                indexVal++;
            }
            // replace 1k条
            for (int i = START_NUM; i < START_NUM + THREAD5_NUM; i++) {
                ret = testGmcPrepareStmtByLabelName(g_stmt_1, "root::P1::T1", GMC_OPERATION_REPLACE_GRAPH);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, g_stmt, g_stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcGetRootNode(g_stmt_1, &rootNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_REPLACE);
                ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_INSERT, &childNode1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = GmcYangEditChildNode(childNode1, "A1", GMC_OPERATION_INSERT, &childNode2);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testYangSetNodeProperty(childNode2, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, g_stmt_1);
                TEST_EXPECT_INT32(GMERR_OK, ret);
            }
            ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            if (data.status) {
                if (data.status != GMERR_OUT_OF_MEMORY) {
                    TEST_EXPECT_INT32(GMERR_OK, data.status);
                }
                // 编辑数据失败回滚savepoint
                ret = GmcTransRollBackSavepointAsync(g_conn, "sp5", trans_start_callback, &data);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = testWaitAsyncRecv(&data);
                TEST_EXPECT_INT32(GMERR_OK, data.status);
                ret = GmcTransReleaseSavepointAsync(g_conn, "sp5", trans_start_callback, &data);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                ret = testWaitAsyncRecv(&data);
                TEST_EXPECT_INT32(GMERR_OK, data.status);
                sleep(1);
                continue;
            } else {
                TEST_EXPECT_INT32(GMERR_OK, data.status);
                TEST_EXPECT_INT32(THREAD5_BATCH_NUM, data.totalNum);
                TEST_EXPECT_INT32(THREAD5_BATCH_NUM, data.succNum);
            }
            // 当上述操作成功时查询diff提交事务
            data.stmt = g_stmt;
            data.expectDiff = &expectDiffBase000;
            ret = GmcYangFetchDiffExecuteAsync(g_stmt, NULL, FetchDiff_callback, &data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecvOneThread(&data);
            TEST_EXPECT_INT32(GMERR_OK, data.status);
            ret = GmcBatchDestroy(batch);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            memset(&data, 0, sizeof(AsyncUserDataT));
            // 提交事务
            ret = GmcTransCommitAsync(g_conn, trans_commit_callback, &data);
            TEST_EXPECT_INT32(GMERR_OK, ret);
            testWaitAsyncRecv(&data);
            if (data.status == GMERR_RESTRICT_VIOLATION) {
                ret = GmcTransRollBackAsync(g_conn, trans_rollback_callback, &data);
                TEST_EXPECT_INT32(GMERR_OK, ret);
                testWaitAsyncRecv(&data);
                TEST_EXPECT_INT32(GMERR_OK, data.status);
            } else {
                TEST_EXPECT_INT32(GMERR_OK, data.status);
            }
            break;
        }
        GmcFreeStmt(g_stmt_1);
        // 释放异步连接
        ret = testGmcDisconnect(g_conn, g_stmt);
        TEST_EXPECT_INT32(GMERR_OK, ret);
    }
}

void *OperationThread0(void *args)
{
    AW_FUN_Log(LOG_INFO, "OperationThread0 begin\n");
    OperationThread0Func();
    AW_FUN_Log(LOG_INFO, "OperationThread0 end\n");
    return NULL;
}

void *OperationThread1(void *args)
{
    AW_FUN_Log(LOG_INFO, "OperationThread1 begin\n");
    OperationThread1Func();
    AW_FUN_Log(LOG_INFO, "OperationThread1 end\n");
    return NULL;
}

void *OperationThread2(void *args)
{
    AW_FUN_Log(LOG_INFO, "OperationThread2 begin\n");
    OperationThread2Func();
    AW_FUN_Log(LOG_INFO, "OperationThread2 end\n");
    return NULL;
}

void *OperationThread3(void *args)
{
    AW_FUN_Log(LOG_INFO, "OperationThread3 begin\n");
    OperationThread3Func();
    AW_FUN_Log(LOG_INFO, "OperationThread3 end\n");
    return NULL;
}

void *OperationThread4(void *args)
{
    AW_FUN_Log(LOG_INFO, "OperationThread4 begin\n");
    OperationThread4Func();
    AW_FUN_Log(LOG_INFO, "OperationThread4 end\n");
    return NULL;
}
void *OperationThread5(void *args)
{
    AW_FUN_Log(LOG_INFO, "OperationThread5 begin\n");
    OperationThread5Func();
    AW_FUN_Log(LOG_INFO, "OperationThread5 end\n");
    return NULL;
}
#endif
