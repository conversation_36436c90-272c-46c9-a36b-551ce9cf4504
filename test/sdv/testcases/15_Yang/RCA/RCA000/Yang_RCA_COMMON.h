
/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#ifndef YANG_RCA_COMMON_H
#define YANG_RCA_COMMON_H
extern "C" {
}
#define TIMEZONEMAXCMD 512
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include <iostream>
#include <fstream>
#include <list>
#include <atomic>
#include <vector>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
using namespace std;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

// 链接句柄
GmcConnT *g_conn_async_big = NULL;
GmcStmtT *g_stmt_async_big = NULL;

// 清空nsp中所有元数据，包括数据和各种表
void testClearNsp(GmcStmtT *stmt, const char *namespaceName)
{
    AsyncUserDataT data = {0};
    int ret = GmcClearNamespaceAsync(stmt, namespaceName, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

// 表
char *g_vertexschema370 = NULL, *g_edgeschema370 = NULL;
const char *g_labelconfig = R"(
{
    "max_record_count":500000,
    "isFastReadUncommitted":0,
    "auto_increment":1,
    "yang_model":1
})";
struct FetchRetCbParam01 {
    int step;
    GmcStmtT *stmt;
    int32_t expectStatus;                    // 预期的操作状态
    uint32_t filterMode;                    // 过滤模式，使用枚举GmcSubtreeFilterModeE设置值
    uint32_t lastExpectIdx;                 // 分批查询上次查询期望结果的最后索引
    std::vector<std::string> &expectReply;  // 过滤模式下预期返回的查询结果, 校验用的字符串
};
AsyncUserDataT userData = {0};

const char *g_savepointName = "sp", *g_savepointName2 = "sp2", *g_savepointName3 = "sp3",
           *g_savepointName4 = "sp4", *g_savepointName5 = "sp5";

const char *g_edgeLabe1 = "ietf-yang-library:yang-library::module-set";
const char *g_edgeLabe2 = "ietf-yang-library:yang-library::module-set::module";
const char *g_edgeLabe3 = "ietf-yang-library:yang-library::module-set::module::location";
const char *g_edgeLabe4 = "ietf-yang-library:yang-library::module-set::module::submodule";
const char *g_edgeLabe5 = "ietf-yang-library:yang-library::module-set::module::submodule::location";
const char *g_edgeLabe6 = "huawei-acl:acl::ip-pools::ip-pool";
const char *g_edgeLabe7 = "huawei-acl:acl::ip-pools::ip-pool::apply-type::apply-ip::ipaddrs::ipaddr";
const char *g_edgeLabe8 = "huawei-acl:acl::port-pools::port-pool";
const char *g_edgeLabe9 = "huawei-acl:acl::port-pools::port-pool::ports::port";
const char *g_edgeLabe10 = "huawei-network-instance:network-instance::instances::instance";
const char *g_edgeLabe11 = "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af";
const char *g_edgeLabe12 = "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::"
    "huawei-routing:routing::routing-manage::topologys::topology";

const char *g_vertexLabel1 = "ietf-yang-library:yang-library";
const char *g_vertexLabel2 = "ietf-yang-library:yang-library::module-set";
const char *g_vertexLabel3 = "ietf-yang-library:yang-library::module-set::module";
const char *g_vertexLabel4 = "ietf-yang-library:yang-library::module-set::module::location";
const char *g_vertexLabel5 = "ietf-yang-library:yang-library::module-set::module::submodule";
const char *g_vertexLabel6 = "ietf-yang-library:yang-library::module-set::module::submodule::location";
const char *g_vertexLabel7 = "huawei-acl:acl";
const char *g_vertexLabel8 = "huawei-acl:acl::ip-pools::ip-pool";
const char *g_vertexLabel9 = "huawei-acl:acl::ip-pools::ip-pool::apply-type::apply-ip::ipaddrs::ipaddr";
const char *g_vertexLabel10 = "huawei-acl:acl::port-pools::port-pool";
const char *g_vertexLabel11 = "huawei-acl:acl::port-pools::port-pool::ports::port";
const char *g_vertexLabel12 = "huawei-network-instance:network-instance";
const char *g_vertexLabel13 = "huawei-network-instance:network-instance::instances::instance";
const char *g_vertexLabel14 = "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af";
const char *g_vertexLabel15 = "huawei-network-instance:network-instance::instances::instance::huawei-l3vpn:afs::af::"
    "huawei-routing:routing::routing-manage::topologys::topology";

// 申请stmt
GmcStmtT *g_stmt_sync_1 = NULL;
GmcStmtT *g_stmt_sync_2 = NULL;
GmcStmtT *g_stmt_sync_3 = NULL;
GmcStmtT *g_stmt_sync_4 = NULL;
GmcStmtT *g_stmt_sync_5 = NULL;
GmcStmtT *g_stmt_sync_6 = NULL;
GmcStmtT *g_stmt_sync_7 = NULL;
GmcStmtT *g_stmt_sync_8 = NULL;
GmcStmtT *g_stmt_sync_9 = NULL;
GmcStmtT *g_stmt_sync_10 = NULL;
GmcStmtT *g_stmt_sync_11 = NULL;
GmcStmtT *g_stmt_sync_12 = NULL;
GmcStmtT *g_stmt_sync_13 = NULL;
GmcStmtT *g_stmt_sync_14 = NULL;
GmcStmtT *g_stmt_sync_15 = NULL;

// stmt申请
void TestYangAllocAllstmt()
{
    int ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_14);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_big, &g_stmt_sync_15);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void TestYangFreeAllstmt()
{
    GmcFreeStmt(g_stmt_sync_1);
    GmcFreeStmt(g_stmt_sync_2);
    GmcFreeStmt(g_stmt_sync_3);
    GmcFreeStmt(g_stmt_sync_4);
    GmcFreeStmt(g_stmt_sync_5);
    GmcFreeStmt(g_stmt_sync_6);
    GmcFreeStmt(g_stmt_sync_7);
    GmcFreeStmt(g_stmt_sync_8);
    GmcFreeStmt(g_stmt_sync_9);
    GmcFreeStmt(g_stmt_sync_10);
    GmcFreeStmt(g_stmt_sync_11);
    GmcFreeStmt(g_stmt_sync_12);
    GmcFreeStmt(g_stmt_sync_13);
    GmcFreeStmt(g_stmt_sync_14);
    GmcFreeStmt(g_stmt_sync_15);
}

void TransStart(GmcConnT *conn)
{
    int ret = 0;
    // 事务结构体
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务

    // 开启乐观事务
    ret = GmcTransStartAsync(conn, &config, trans_start_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
        AW_FUN_Log(LOG_DEBUG, "trans start error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void TransCommit(GmcConnT *conn, int expectStatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransCommitAsync(conn, trans_commit_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(expectStatus, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void TransRollback(GmcConnT *conn, int expectStatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransRollBackAsync(conn, trans_rollback_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expectStatus) {
        AW_MACRO_EXPECT_EQ_INT(expectStatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "trans rollback error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void CreateSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransCreateSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "create savepoint error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void ReleaseSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransReleaseSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "release savepoint error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

void RollbackSavepoint(GmcConnT *conn, const char *savepointname, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcTransRollBackSavepointAsync(conn, savepointname, TransSavePointCb, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (userData.status != expstatus) {
        AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
        AW_FUN_Log(LOG_DEBUG, "rollback savepoint error code:%d\n", userData.status);
        ret = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    memset(&userData, 0, sizeof(AsyncUserDataT));
}

int testBatchPrepare(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchtype = GMC_BATCH_YANG)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 32768);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchtype);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}
int testBatchPrepare1(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchtype = GMC_BATCH_YANG)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchtype);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}
int testBatchPrepareAndSetDiff(GmcConnT *conn, GmcBatchT **batch, GmcBatchTypeE batchType = GMC_BATCH_YANG,
    GmcYangDiffTypeE diffType = GMC_YANG_DIFF_DELAY_READ_ON)
{
    int ret = 0;
    GmcBatchOptionT batchOption;

    // 准备批量操作
    ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchOptionSetBatchType(&batchOption, batchType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcYangBatchOptionSetDiffType(&batchOption, diffType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        return ret;
    }

    return ret;
}

void BatchExecute(GmcBatchT *batch, int expstatus = GMERR_OK)
{
    int ret = 0;
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expstatus, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    GmcBatchDestroy(batch);
}

string GetOpTypeString(GmcDiffOpTypeE op)
{
    switch (op) {
        case GMC_DIFF_OP_CREATE:
            return "create";
        case GMC_DIFF_OP_REMOVE:
            return "remove";
        case GMC_DIFF_OP_UPDATE:
            return "update";
        default:
            return "invalid";
    }
}

// 比较s1 s2两个字符串，如果字符串相同，返回空串，不同返回从不同位置开始的子串
string StrCmp(string &s1, string &s2)
{
    int i = 0;
    for (; s1[i] != 0 && s2[i] != 0; i++) {
        if (s1[i] != s2[i]) {
            return s1.substr(i);
        }
    }

    if (s1[i] == 0 && s2[i] == 0) {
        return string("");
    } else if (s2[i] == 0) {
        return s1.substr(i);
    } else {
        return s2.substr(i);
    }
}
string GetValueString(GmcYangNodeValueT *value)
{
    switch (value->type) {
        case GMC_DATATYPE_STRING:
            return string("") + (const char *)value->value;
        case GMC_DATATYPE_CHAR:
        case GMC_DATATYPE_UCHAR:
            return "" + to_string(*(const char *)value->value);
            break;
        case GMC_DATATYPE_INT8:
        case GMC_DATATYPE_UINT8:
            return "" + to_string(*(const uint8_t *)value->value);
        case GMC_DATATYPE_INT16:
        case GMC_DATATYPE_UINT16:
            return "" + to_string(*(const uint16_t *)value->value);
        case GMC_DATATYPE_INT32:
        case GMC_DATATYPE_UINT32:
            return "" + to_string(*(const uint32_t *)value->value);
        case GMC_DATATYPE_UINT64:
        case GMC_DATATYPE_INT64:
        case GMC_DATATYPE_TIME:
            return "" + to_string(*(const uint64_t *)value->value);
        case GMC_DATATYPE_FLOAT:
            return "" + to_string(*(const float *)value->value);
        case GMC_DATATYPE_DOUBLE:
            return "" + to_string(*(const double *)value->value);
        case GMC_DATATYPE_NULL:
            return string("NULL");
        default:
            return string("NIL:") + to_string(value->type);
        }
}
string GetVertexString(GmcStmtT *stmt, GmcYangNodeT *info, bool isNewData)
{
    GmcYangNodeValueT *propValue = NULL;
    string res = "";
    int32_t ret;
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    uint32_t propNum = 0;
    if ((isNewData && opType == GMC_DIFF_OP_CREATE) || (!isNewData && opType == GMC_DIFF_OP_REMOVE) ||
        opType == GMC_DIFF_OP_UPDATE) {
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
            return "";
        }
        EXPECT_EQ(GMERR_OK, ret);
        res += "priKey(";
        for (unsigned int i = 0; i < propNum; i++) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetKeyPropValue(info, i, &propValue));
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    bool isHasNewPrevNode = false;
    bool isHasOldPrevNode = false;
    ret = GmcYangNodeHasNewPrev(info, &isHasNewPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcYangNodeHasOldPrev(info, &isHasOldPrevNode);
    if (ret == GMERR_FEATURE_NOT_SUPPORTED) {
        return "";
    }
    EXPECT_EQ(GMERR_OK, ret);
    if ((isNewData && isHasNewPrevNode) || (!isNewData && isHasOldPrevNode) || opType == GMC_DIFF_OP_UPDATE) {
        bool isExist = (isNewData ? isHasNewPrevNode : isHasOldPrevNode);
        if (!isExist) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        res += ", preKey(";
        ret = GmcYangNodeGetKeyPropNum(info, &propNum);
        if (ret != GMERR_OK) {
            if (res == "") {
                res = "NULL";
            }
            return res;
        }
        for (unsigned int i = 0; i < propNum; i++) {
            ret = isNewData ? GmcYangNodeGetNewPrevKey(info, i, &propValue) :
                              GmcYangNodeGetOldPrevKey(info, i, &propValue);
            if (ret == GMERR_INVALID_NAME) {
                continue;
            } else if (ret != GMERR_OK) {
                cout << "error:" << ret << ", " << propValue->name;
            }
            string propNameString = propValue->name;
            res += propNameString + ":" + GetValueString(propValue) + ",";
        }
        if (res[res.size() - 1] == ',') {
            res[res.size() - 1] = ')';
        } else {
            res += ")";
        }
    }
    if (res == "") {
        res = "NULL";
    }
    return res;
}
void GetYangInfoString(GmcStmtT *stmt, GmcYangNodeT *info, string parentFix, string &res)
{
    res = parentFix + ":";
    GmcDiffOpTypeE opType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetDiffOpType(info, &opType));
    res += GetOpTypeString((GmcDiffOpTypeE)opType);

    GmcYangNodeTypeE nodeType;
    EXPECT_EQ(GMERR_OK, GmcYangNodeGetType(info, &nodeType));
    if (nodeType == GMC_YANG_FIELD) {
        GmcYangNodeValueT *newValue = NULL;
        GmcYangNodeValueT *oldValue = NULL;
        if (opType == GMC_DIFF_OP_CREATE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            res += "(" + GetValueString(newValue) + ")";
        } else if (opType == GMC_DIFF_OP_REMOVE) {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(oldValue) + ")";
        } else {
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetNewValue(info, &newValue));
            EXPECT_EQ(GMERR_OK, GmcYangNodeGetOldValue(info, &oldValue));
            res += "(" + GetValueString(newValue) + "," + GetValueString(oldValue) + ")";
        }
    } else {
        if (strcmp(GetVertexString(stmt, info, true).c_str(), "") != 0) {
            res += "[(" + GetVertexString(stmt, info, true) + "),";
        }
        if (strcmp(GetVertexString(stmt, info, false).c_str(), "") != 0) {
            res += "(" + GetVertexString(stmt, info, false) + ")]";
        }
    }
}
// 深度遍历生成diff信息字符串
void DFSYangNode(GmcStmtT *stmt, GmcYangNodeT *parent, string prefix, string &resStr)
{
    GmcYangNodeT *child = NULL;
    GmcYangNodeT *prevChild = NULL;
    string res = "";
    string diffStr;
    do {
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetNext(parent, prevChild, &child));
        prevChild = child;
        if (child != NULL) {
            // 打印diff信息
            const char *nodeName;
            ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(child, &nodeName));
            string childName = prefix + nodeName;
            ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, child, childName, diffStr));
            res += diffStr + "\n";
            string childStr;
            DFSYangNode(stmt, child, nodeName + string("."), childStr);
            res += childStr;
        }
    } while (child != NULL);
    resStr += res;
}

void TestCheckYangTree(GmcStmtT *stmt, const GmcYangTreeT **yangTrees, uint32_t count, vector<string> &expectReply)
{
    ASSERT_EQ(expectReply.size(), count);
    GmcYangNodeT *rootInfo = NULL;
    for (uint32_t i = 0; i < count; i++) {
        ASSERT_EQ(GMERR_OK, GmcYangGetRootNode(yangTrees[i], &rootInfo));
        string res;
        const char *rootName;
        ASSERT_EQ(GMERR_OK, GmcYangNodeGetName(rootInfo, &rootName));
        ASSERT_NO_FATAL_FAILURE(GetYangInfoString(stmt, rootInfo, rootName + string(""), res));
        res += "\n";
        ASSERT_NO_FATAL_FAILURE(DFSYangNode(stmt, rootInfo, rootName + string("."), res));
        char fileName[128] = {0};
        int ret = sprintf_s(fileName, sizeof(fileName), "perf_%dDiffTreeInfo", count);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "sprintf_s error\n");
            return;
        }
        FILE *fp = fopen(fileName, "w");
        if (fp == NULL) {
            AW_FUN_Log(LOG_INFO, "fopen error\n");
            return;
        }
        ret = fputs(res.c_str(), fp);
        if (ret < 0) {
            AW_FUN_Log(LOG_INFO, "fputs error\n");
            return;
        }
        ret = fclose(fp);
        if (ret == -1) {
            AW_FUN_Log(LOG_INFO, "fclose error\n");
            return;
        }
        cout << "actual diff：\n" << res;
        ASSERT_STREQ(StrCmp(expectReply[i], res).c_str(), "") << i;
        ASSERT_EQ(GMERR_OK, GmcYangFreeTree(yangTrees[i]));
    }
}

// diff 回调
void FetchDiff_callback(
    void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        bool isEnd = false;
        uint32_t count = 0;
        AsyncUserDataT *userData1 = (AsyncUserDataT *)userData;
        userData1->historyRecvNum++;
        userData1->status = status;
        if (userData1->lastError != NULL) {
            int ret = strcmp(userData1->lastError, errMsg);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        if (status == GMERR_OK) {
            const GmcYangTreeT **yangTree = NULL;
            ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));

            ASSERT_EQ((uint32_t)(*userData1->expectDiff).size(), count);
            ASSERT_TRUE(isEnd);
            TestCheckYangTree(userData1->stmt, yangTree, count, *userData1->expectDiff);
            GmcYangFreeFetchRet(fetchRet);
        }
        userData1->recvNum++;
    }
}
// 获取diff
void testFetchAndDeparseDiff(GmcStmtT *stmt, GmcBatchT *batch, vector<string> &expectDiff, AsyncUserDataT data,
    int rets = GMERR_OK)
{
    data.stmt = stmt;
    data.expectDiff = &expectDiff;
    int ret = GmcYangFetchDiffExecuteAsync(stmt, NULL, FetchDiff_callback, &data);
    if (ret != GMERR_OK) {
        testGmcGetLastError(NULL);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(rets, data.status);
}

void TestFetchSubtreeAcl(GmcStmtT *stmt, const char *vertexlabelname, const char *returnJson)
{
    // 使用接口完成subtree 查询
    int ret = 0;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, vertexlabelname, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 2,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *subtreeReturnJson = NULL;
    readJanssonFile(returnJson, &subtreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = subtreeReturnJson;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subtreeReturnJson);
}

int g_writeTimes = 0;
void AsyncFetchRetCb123(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    g_writeTimes++;
    cout << endl << "g_writeTimes:" << g_writeTimes << endl;
    FetchRetCbParam *param = reinterpret_cast<FetchRetCbParam *>(userData);
    ASSERT_EQ(param->expectStatus, status) << errMsg;
    if (param->expectStatus != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        ASSERT_NE(strcmp(errMsg, ""), 0);
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_EQ((uint32_t)param->expectReply.size(), count);
    ASSERT_TRUE(yangTree != NULL);
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            ASSERT_STREQ(param->expectReply[i].c_str(), "");
            continue;
        }
        char *reply = NULL;
        ASSERT_EQ(GMERR_OK, GmcYangTreeToJson(yangTree[i], &reply));

        ofstream fout;
        (void)snprintf(g_command, MAX_CMD_SIZE, "./reply/reply%d.txt", g_writeTimes);
        fout.open(g_command);
        fout << reply << endl;
        fout.close();

        GmcYangFreeTree(yangTree[i]);
    }
    // 判断是否全部结果都已获取，全部获取则释放fetchRet，否则再发送一次查询直至全部结果都已经获取
    if (isEnd) {
        param->step++;
        GmcYangFreeFetchRet(fetchRet);
        g_writeTimes = 0;
        return;
    }
    ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param->stmt, NULL, fetchRet, AsyncFetchRetCb123, param));
    return;
}

void TestTimesFetchSubtreeAcl(GmcStmtT *stmt, const char *vertexlabelname)
{
    // 使用接口完成subtree 查询
    int ret = 0;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, vertexlabelname, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 2,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    std::vector<std::string> reply(1);
    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestFetchSubtreeIetf(GmcStmtT *stmt, const char *vertexlabelname, const char *returnJson)
{
    // 使用接口完成subtree 查询
    int ret = 0;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, vertexlabelname, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 2,
        .configFlag = 1,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *subtreeReturnJson = NULL;
    readJanssonFile(returnJson, &subtreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = subtreeReturnJson;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subtreeReturnJson);
}

void TestFetchsubtreeNetwork(GmcStmtT *stmt, const char *vertexlabelname, const char *returnJson)
{
    // 使用接口完成subtree 查询
    int ret = 0;
    GmcNodeT *root = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, vertexlabelname, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 2,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *subtreeReturnJson = NULL;
    readJanssonFile(returnJson, &subtreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = subtreeReturnJson;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subtreeReturnJson);
}
void TestFetchSubtreeNamespace(GmcStmtT *stmt, const char **returnJson)
{
    // subtree全量查询
    int ret = 0;
    char *subtreeReturnJson = NULL;
    char *subtreeReturnJson1 = NULL;
    char *subtreeReturnJson2 = NULL;
    readJanssonFile(returnJson[0], &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    readJanssonFile(returnJson[1], &subtreeReturnJson1);
    ASSERT_NE((void *)NULL, subtreeReturnJson1);
    readJanssonFile(returnJson[2], &subtreeReturnJson2);
    ASSERT_NE((void *)NULL, subtreeReturnJson2);
    std::vector<std::string> reply(3);
    reply[0] = subtreeReturnJson;
    reply[1] = subtreeReturnJson1;
    reply[2] = subtreeReturnJson2;
    GmcSubtreeFilterT filters = {0};
    filters.filterMode = GMC_FETCH_FULL_OBJ;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = stmt,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(stmt, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subtreeReturnJson);
    free(subtreeReturnJson1);
    free(subtreeReturnJson2);
}
string testYangTreeToStr(GmcStmtT *stmt, const GmcYangTreeT *reply, bool isDiff)
{
    string res;
    if (!isDiff) {
        char *replyJson = NULL;
        EXPECT_EQ(GMERR_OK, GmcYangTreeToJson(reply, &replyJson));
        res = string(replyJson);
        return res;
    }
}
void CheckTreeReply(const GmcYangTreeT **yangTree, uint32_t count, FetchRetCbParam01 *param01, bool isDiff = false)
{
    uint32_t idx = param01->lastExpectIdx;
    ASSERT_TRUE(param01->expectReply.size() >= (idx + count));  // 断言防止越界
    for (uint32_t i = 0; i < count; i++) {
        if (yangTree[i] == NULL) {
            ASSERT_STREQ(param01->expectReply[idx + i].c_str(), "{}");
            continue;
        }
        std::string reply = testYangTreeToStr(param01->stmt, yangTree[i], isDiff);
        EXPECT_TRUE(testYangJsonIsEqual(reply.c_str(), param01->expectReply[idx + i].c_str()))
            << "replyJson:\n"
            << reply << endl;
        GmcYangFreeTree(yangTree[i]);
    }
}
void AsyncFetchRetCb01(void *userData, GmcFetchRetT *fetchRet, int32_t status, const char *errMsg)
{
    FetchRetCbParam01 *param01 = reinterpret_cast<FetchRetCbParam01 *>(userData);
    ASSERT_EQ(param01->expectStatus, status) << errMsg;
    if (param01->expectStatus != GMERR_OK) {
        ASSERT_NE((errMsg != NULL), 0);
        ASSERT_NE(strcmp(errMsg, ""), 0);
        return;
    }
    bool isEnd = false;
    uint32_t count = 0;
    const GmcYangTreeT **yangTree = NULL;
    uint32_t idx = param01->lastExpectIdx;
    ASSERT_EQ(GMERR_OK, GmcYangFetchRetDeparse(fetchRet, &isEnd, &yangTree, &count));
    ASSERT_TRUE(yangTree != NULL);
    if (param01->expectReply.size() != 0) {
        CheckTreeReply(yangTree, count, param01);
    }
    // 判断是否全部结果都已获取，全部获取则释放fetchRet，否则再发送一次查询直至全部结果都已经获取
    if (isEnd) {
        param01->step++;
        GmcYangFreeFetchRet(fetchRet);
        return;
    }
    param01->lastExpectIdx = idx + count;
    ASSERT_EQ(GMERR_OK, GmcYangSubtreeFilterExecuteAsync(param01->stmt, NULL, fetchRet, AsyncFetchRetCb01, param01));
    return;
}
int testsubtreeSetvalue(GmcNodeT * Node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldname, GmcYangPropOpTypeE optype)
{
    int ret = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldname, (strlen(fieldname) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(Node, &propValue, optype);
    return ret;
}
int testYangSetField(GmcStmtT *stmt, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetVertexProperty(stmt, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}
void testYanginsertietf(GmcConnT *conn)
{
    int ret;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel1Node = NULL;
    GmcNodeT *vertexLabel2Node = NULL;
    GmcNodeT *vertexLabel3Node = NULL;
    GmcNodeT *vertexLabel4Node = NULL;
    GmcNodeT *vertexLabel5Node = NULL;
    GmcNodeT *vertexLabel6Node = NULL;
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char f6value[8] = "string";
    ret = testYangSetField(g_stmt_sync_1, GMC_DATATYPE_STRING, &f6value, (strlen(f6value)), "content-id",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groupsAA
    GmcNodeT *groupAANode = NULL;
    ret = GmcYangEditChildNode(vertexLabel1Node, "groupAA", GMC_OPERATION_INSERT, &groupAANode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 2; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_2, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)), "name",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int m = 0; m < 2; m++) {
            // 设置g_vertexLabel3 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_3, g_vertexLabel3, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_2, g_stmt_sync_3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_3, &vertexLabel3Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue1[15];
            memset(namevalue1, 0, sizeof(namevalue1));
            (void)snprintf(namevalue1, 15, "name%d", m);
            ret = testYangSetField(g_stmt_sync_3, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)), "name",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue2[15];
            memset(namevalue2, 0, sizeof(namevalue2));
            (void)snprintf(namevalue2, 15, "revision%d", m);
            ret = testYangSetField(g_stmt_sync_3, GMC_DATATYPE_STRING, &namevalue2, (strlen(namevalue2)), "revision",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue3[15];
            memset(namevalue3, 0, sizeof(namevalue3));
            (void)snprintf(namevalue3, 15, "namespace%d", m);
            ret = testYangSetField(g_stmt_sync_3, GMC_DATATYPE_STRING, &namevalue3, (strlen(namevalue3)), "namespace",
                GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            for (int n = 0; n < 2; n++) {
                // 设置g_vertexLabel4 list
                ret = testGmcPrepareStmtByLabelName(g_stmt_sync_4, g_vertexLabel4, GMC_OPERATION_INSERT);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, g_stmt_sync_3, g_stmt_sync_4);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 通过stmt句柄拿到Treenode
                ret = GmcGetRootNode(g_stmt_sync_4, &vertexLabel4Node);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                char namevalue4[15];
                memset(namevalue4, 0, sizeof(namevalue4));
                (void)snprintf(namevalue4, 15, "location%d", n);
                ret = testYangSetField(g_stmt_sync_4, GMC_DATATYPE_STRING, &namevalue4, (strlen(namevalue4)),
                    "location", GMC_YANG_PROPERTY_OPERATION_CREATE);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, g_stmt_sync_4);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            for (int k = 0; k < 2; k++) {
                // 设置g_vertexLabel5 list
                ret = testGmcPrepareStmtByLabelName(g_stmt_sync_5, g_vertexLabel5, GMC_OPERATION_INSERT);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, g_stmt_sync_3, g_stmt_sync_5);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 通过stmt句柄拿到Treenode
                ret = GmcGetRootNode(g_stmt_sync_5, &vertexLabel5Node);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                char namevalue5[15];
                memset(namevalue5, 0, sizeof(namevalue5));
                (void)snprintf(namevalue5, 15, "name%d", k);
                ret = testYangSetField(g_stmt_sync_5, GMC_DATATYPE_STRING, &namevalue5, (strlen(namevalue5)),
                    "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                char namevalue6[15];
                memset(namevalue6, 0, sizeof(namevalue6));
                (void)snprintf(namevalue6, 15, "revision%d", k);
                ret = testYangSetField(g_stmt_sync_5, GMC_DATATYPE_STRING, &namevalue6, (strlen(namevalue6)),
                    "revision", GMC_YANG_PROPERTY_OPERATION_CREATE);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, g_stmt_sync_5);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                for (int l = 0; l < 2; l++) {
                // 设置g_vertexLabel5 list
                ret = testGmcPrepareStmtByLabelName(g_stmt_sync_6, g_vertexLabel6, GMC_OPERATION_INSERT);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, g_stmt_sync_5, g_stmt_sync_6);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 通过stmt句柄拿到Treenode
                ret = GmcGetRootNode(g_stmt_sync_6, &vertexLabel6Node);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                char namevalue7[15];
                memset(namevalue7, 0, sizeof(namevalue7));
                (void)snprintf(namevalue7, 15, "location%d", l);
                ret = testYangSetField(g_stmt_sync_6, GMC_DATATYPE_STRING, &namevalue7, (strlen(namevalue7)),
                    "location", GMC_YANG_PROPERTY_OPERATION_CREATE);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, g_stmt_sync_6);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                }
            }
        }
    }
    // 提交批处理
    BatchExecute(batch);
    // 提交事务
    TransCommit(conn);
}
void testYanginsertacl(GmcConnT *conn)
{
    int ret;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groups
    GmcNodeT *groupsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_INSERT, &groupsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groupsAA
    GmcNodeT *groupAANode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "groupAA", GMC_OPERATION_INSERT, &groupAANode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ip-pools
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_INSERT, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 port-pools
    GmcNodeT *portpoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_INSERT, &portpoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 2; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_8, &vertexLabel8Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)),
            "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 apply-type
        GmcNodeT *applytypeNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel8Node, "apply-type", GMC_OPERATION_INSERT, &applytypeNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 apply-ip
        GmcNodeT *applyipNode = NULL;
        ret = GmcYangEditChildNode(applytypeNode, "apply-ip", GMC_OPERATION_INSERT, &applyipNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ipaddrs
        GmcNodeT *ipaddrsNode = NULL;
        ret = GmcYangEditChildNode(applyipNode, "ipaddrs", GMC_OPERATION_INSERT, &ipaddrsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        for (int k = 0; k < 2; k++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_9, g_vertexLabel9, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_8, g_stmt_sync_9);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_9, &vertexLabel9Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue1[10];
            memset(namevalue1, 0, sizeof(namevalue1));
            (void)snprintf(namevalue1, 10, "address%d", k);
            ret = testYangSetField(g_stmt_sync_9, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)),
                "address", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue2[10];
            memset(namevalue2, 0, sizeof(namevalue2));
            (void)snprintf(namevalue2, 10, "mask%d", k);
            ret = testYangSetField(g_stmt_sync_9, GMC_DATATYPE_STRING, &namevalue2, (strlen(namevalue2)),
                "mask", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_9);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    for (int j = 0; j < 2; j++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_10, g_vertexLabel10, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_10, &vertexLabel10Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue3[10];
        memset(namevalue3, 0, sizeof(namevalue3));
        (void)snprintf(namevalue3, 10, "name%d", j);
        ret = testYangSetField(g_stmt_sync_10, GMC_DATATYPE_STRING, &namevalue3, (strlen(namevalue3)),
            "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ports
        GmcNodeT *portsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel10Node, "ports", GMC_OPERATION_INSERT, &portsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int l = 0; l < 2; l++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_11, g_vertexLabel11, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_10, g_stmt_sync_11);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_11, &vertexLabel11Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue5[10];
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 10, "operation%d", l);
            ret = testYangSetField(g_stmt_sync_11, GMC_DATATYPE_STRING, &namevalue5, (strlen(namevalue5)),
                "operation", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint16_t numberbegin = l;
            ret = testYangSetField(g_stmt_sync_11, GMC_DATATYPE_UINT16, &numberbegin, sizeof(uint16_t),
                "number-begin", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint16_t numberend = l;
            ret = testYangSetField(g_stmt_sync_11, GMC_DATATYPE_UINT16, &numberend, sizeof(uint16_t),
                "number-end", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_11);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    // 提交批处理
    BatchExecute(batch);
    // 提交事务
    TransCommit(conn);
}
void testYanginsertnetwork(GmcConnT *conn)
{
    int ret;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel12Node = NULL;
    GmcNodeT *vertexLabel13Node = NULL;
    GmcNodeT *vertexLabel14Node = NULL;
    GmcNodeT *vertexLabel15Node = NULL;
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, g_vertexLabel12, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_12, &vertexLabel12Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groups
    GmcNodeT *instancesNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel12Node, "instances", GMC_OPERATION_INSERT, &instancesNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 2; j++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_13, g_vertexLabel13, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_13);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_13, &vertexLabel13Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue3[10];
        memset(namevalue3, 0, sizeof(namevalue3));
        (void)snprintf(namevalue3, 10, "name%d", j);
        ret = testYangSetField(g_stmt_sync_13, GMC_DATATYPE_STRING, &namevalue3, (strlen(namevalue3)),
            "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ports
        GmcNodeT *huaweil3vpnafsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel13Node, "huawei-l3vpn:afs", GMC_OPERATION_INSERT, &huaweil3vpnafsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_13);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int l = 0; l < 2; l++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_14, g_vertexLabel14, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_13, g_stmt_sync_14);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_14, &vertexLabel14Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue4[10];
            memset(namevalue4, 0, sizeof(namevalue4));
            (void)snprintf(namevalue4, 10, "type%d", l);
            ret = testYangSetField(g_stmt_sync_14, GMC_DATATYPE_STRING, &namevalue4, (strlen(namevalue4)),
                "type", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 ports
            GmcNodeT *huaweiroutingroutingNode = NULL;
            ret = GmcYangEditChildNode(vertexLabel14Node, "huawei-routing:routing", GMC_OPERATION_INSERT,
                &huaweiroutingroutingNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 ports
            GmcNodeT *routingmanageNode = NULL;
            ret = GmcYangEditChildNode(huaweiroutingroutingNode, "routing-manage", GMC_OPERATION_INSERT,
                &routingmanageNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 ports
            GmcNodeT *topologysNode = NULL;
            ret = GmcYangEditChildNode(routingmanageNode, "topologys", GMC_OPERATION_INSERT, &topologysNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 ports
            GmcNodeT *staticroutingNode = NULL;
            ret = GmcYangEditChildNode(huaweiroutingroutingNode, "static-routing", GMC_OPERATION_INSERT,
                &staticroutingNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 ports
            GmcNodeT *unicastroute2sNode = NULL;
            ret = GmcYangEditChildNode(staticroutingNode, "unicast-route2s", GMC_OPERATION_INSERT, &unicastroute2sNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 ports
            GmcNodeT *ipv4routesNode = NULL;
            ret = GmcYangEditChildNode(staticroutingNode, "ipv4-routes", GMC_OPERATION_INSERT, &ipv4routesNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_14);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            for (int p = 0; p < 2; p++) {
                // 设置g_vertexLabel2 list
                ret = testGmcPrepareStmtByLabelName(g_stmt_sync_15, g_vertexLabel15, GMC_OPERATION_INSERT);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcYangBindChild(batch, g_stmt_sync_14, g_stmt_sync_15);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 通过stmt句柄拿到Treenode
                ret = GmcGetRootNode(g_stmt_sync_15, &vertexLabel15Node);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                char namevalue5[10];
                memset(namevalue5, 0, sizeof(namevalue5));
                (void)snprintf(namevalue5, 10, "name%d", p);
                ret = testYangSetField(g_stmt_sync_15, GMC_DATATYPE_STRING, &namevalue5, (strlen(namevalue5)),
                    "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                // 添加DML操作
                ret = GmcBatchAddDML(batch, g_stmt_sync_15);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        }
    }
    // 提交批处理
    BatchExecute(batch);
    // 提交事务
    TransCommit(conn);
}
void testYanginsertaclconfig(GmcConnT *conn)
{
    int ret;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
    ret = testBatchPrepare(conn, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groups
    GmcNodeT *groupsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_INSERT, &groupsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groupsAA
    GmcNodeT *groupAANode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "groupAA", GMC_OPERATION_INSERT, &groupAANode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 groupsCC
    GmcNodeT *groupsCCNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "groupsCC", GMC_OPERATION_INSERT, &groupsCCNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 ip-pools
    GmcNodeT *ippoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_INSERT, &ippoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建child节点 port-pools
    GmcNodeT *portpoolsNode = NULL;
    ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_INSERT, &portpoolsNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmt_sync_7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 2; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_8, &vertexLabel8Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)snprintf(namevalue, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)),
            "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建child节点 apply-type
        GmcNodeT *applytypeNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel8Node, "apply-type", GMC_OPERATION_INSERT, &applytypeNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 apply-ip
        GmcNodeT *applyipNode = NULL;
        ret = GmcYangEditChildNode(applytypeNode, "apply-ip", GMC_OPERATION_INSERT, &applyipNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ipaddrs
        GmcNodeT *ipaddrsNode = NULL;
        ret = GmcYangEditChildNode(applyipNode, "ipaddrs", GMC_OPERATION_INSERT, &ipaddrsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        for (int k = 0; k < 2; k++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_9, g_vertexLabel9, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_8, g_stmt_sync_9);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_9, &vertexLabel9Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue1[10];
            memset(namevalue1, 0, sizeof(namevalue1));
            (void)snprintf(namevalue1, 10, "address%d", k);
            ret = testYangSetField(g_stmt_sync_9, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)),
                "address", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue2[10];
            memset(namevalue2, 0, sizeof(namevalue2));
            (void)snprintf(namevalue2, 10, "mask%d", k);
            ret = testYangSetField(g_stmt_sync_9, GMC_DATATYPE_STRING, &namevalue2, (strlen(namevalue2)),
                "mask", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_9);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    for (int j = 0; j < 2; j++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_10, g_vertexLabel10, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_10, &vertexLabel10Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue3[10];
        memset(namevalue3, 0, sizeof(namevalue3));
        (void)snprintf(namevalue3, 10, "name%d", j);
        ret = testYangSetField(g_stmt_sync_10, GMC_DATATYPE_STRING, &namevalue3, (strlen(namevalue3)),
            "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ports
        GmcNodeT *portsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel10Node, "ports", GMC_OPERATION_INSERT, &portsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int l = 0; l < 2; l++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_11, g_vertexLabel11, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_10, g_stmt_sync_11);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_11, &vertexLabel11Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue5[10];
            memset(namevalue5, 0, sizeof(namevalue5));
            (void)snprintf(namevalue5, 10, "operation%d", l);
            ret = testYangSetField(g_stmt_sync_11, GMC_DATATYPE_STRING, &namevalue5, (strlen(namevalue5)),
                "operation", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint16_t numberbegin = l;
            ret = testYangSetField(g_stmt_sync_11, GMC_DATATYPE_UINT16, &numberbegin, sizeof(uint16_t),
                "number-begin", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint16_t numberend = l;
            ret = testYangSetField(g_stmt_sync_11, GMC_DATATYPE_UINT16, &numberend, sizeof(uint16_t),
                "number-end", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_11);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    // 提交批处理
    BatchExecute(batch);
    // 提交事务
    TransCommit(conn);
}
void testYanginsertaclconfig20480(GmcConnT *conn)
{
    int ret;
    // 创建乐观事务
    TransStart(conn);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
    for (int k = 0; k < 11; k++) {
        ret = testBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *groupsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_NONE, &groupsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groupsAA
        GmcNodeT *groupAANode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groupAA", GMC_OPERATION_NONE, &groupAANode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groupsCC
        GmcNodeT *groupsCCNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groupsCC", GMC_OPERATION_NONE, &groupsCCNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ip-pools
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 port-pools
        GmcNodeT *portpoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_NONE, &portpoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = (3 + (2048 * k)); i < 2048 * (k + 1); i++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_8, &vertexLabel8Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue[10];
            memset(namevalue, 0, sizeof(namevalue));
            (void)snprintf(namevalue, 10, "name%d", i);
            ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)),
                "name", GMC_YANG_PROPERTY_OPERATION_CREATE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 创建child节点 apply-type
            GmcNodeT *applytypeNode = NULL;
            ret = GmcYangEditChildNode(vertexLabel8Node, "apply-type", GMC_OPERATION_INSERT, &applytypeNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 apply-ip
            GmcNodeT *applyipNode = NULL;
            ret = GmcYangEditChildNode(applytypeNode, "apply-ip", GMC_OPERATION_INSERT, &applyipNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 ipaddrs
            GmcNodeT *ipaddrsNode = NULL;
            ret = GmcYangEditChildNode(applyipNode, "ipaddrs", GMC_OPERATION_INSERT, &ipaddrsNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        }
        // 提交批处理
        BatchExecute(batch);
    }

    // 提交事务
    TransCommit(conn);
}

void testYangRemoveaclconfig20480(GmcConnT *conn)
{
    int ret;
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    GmcNodeT *vertexLabel9Node = NULL;
    GmcNodeT *vertexLabel10Node = NULL;
    GmcNodeT *vertexLabel11Node = NULL;
    for (int k = 0; k < 11; k++) {
        // 创建乐观事务
        TransStart(conn);
        ret = testBatchPrepare(conn, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *groupsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_NONE, &groupsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groupsAA
        GmcNodeT *groupAANode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groupAA", GMC_OPERATION_NONE, &groupAANode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groupsCC
        GmcNodeT *groupsCCNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groupsCC", GMC_OPERATION_NONE, &groupsCCNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 ip-pools
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 port-pools
        GmcNodeT *portpoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "port-pools", GMC_OPERATION_NONE, &portpoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (int i = (3 + (2048 * k)); i < 2048 * (k + 1); i++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_REMOVE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_8, &vertexLabel8Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue[10];
            memset(namevalue, 0, sizeof(namevalue));
            (void)snprintf(namevalue, 10, "name%d", i);
            ret = GmcSetIndexKeyValue(g_stmt_sync_8, 1, GMC_DATATYPE_STRING, namevalue, strlen(namevalue));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt_sync_8, "k0");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        }
        // 提交批处理
        BatchExecute(batch);
        // 提交事务
        TransCommit(conn);
        AW_FUN_Log(LOG_INFO, "remove %s benin: %d, end: %d .", g_vertexLabel8, 3 + (2048 * k), 2048 * (k + 1));
    }
}
#endif
