/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "Yang_RCA_COMMON.h"


class RCA_ONE : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void RCA_ONE::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh -f");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void RCA_ONE::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void RCA_ONE::SetUp()
{
    // 建立大报文连接
    YangConnOptionT connOptions = {0};
    connOptions.isLobConn = true;
    connOptions.timeoutMs = 100000;
    int ret = TestYangGmcConnect(&g_conn_async_big, &g_stmt_async_big, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    const char *namespace1 = "NamespaceRCA";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async_big, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async_big, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 建表 建边
    readJanssonFile("SOHO_S380/S380_VertexLabel.json", &g_vertexschema370);
    ASSERT_NE((void *)NULL, g_vertexschema370);
    ret = GmcCreateVertexLabelAsync(g_stmt_async_big, g_vertexschema370, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema370);
    readJanssonFile("SOHO_S380/S380_EdgeLabel.json", &g_edgeschema370);
    ASSERT_NE((void *)NULL, g_edgeschema370);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async_big, g_edgeschema370, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema370);
    TestYangAllocAllstmt();
}

void RCA_ONE::TearDown()
{
    AW_CHECK_LOG_END();
    const char *namespace1 = "NamespaceRCA";
    int ret = 0;
    //  删边
    testClearNsp(g_stmt_async_big, namespace1);
    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async_big, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    TestYangFreeAllstmt();
    ret = testGmcDisconnect(g_conn_async_big, g_stmt_async_big);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 多savepoint 跨表dml操作和查询 Release savepoint
 * Input        : 1.创建 事务，对10-15张表预置数据；
                    2. savepoint1 使用表1做inert操作 subtree查询
                    3. savepoint2 使用表2做merge操作 subtree查询
                    4. savepoint3 使用表3做replace操作 subtree查询
                    5.提交事务
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 acl表主要包括   P NP 空节点 字段默认值 对acl表 根节点 做subtree默认值查询+空节点查询 容器过滤
 itef表主要为  状态/配置节点裁剪后  根节点作 isconfig subtree查询  容器过滤
network表主要做 other查询
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据 SOHO380表 三棵树 ietf acl network
    testYanginsertietf(g_conn_async_big);
    testYanginsertacl(g_conn_async_big);
    testYanginsertnetwork(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel1Node = NULL;
    GmcNodeT *vertexLabel2Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 8000; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)(void)snprintf(namevalue, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_2, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)), "name",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    // itef 根节点容器过滤 默认值查询
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_001_1.json");
    // create savepoint2
    CreateSavepoint(g_conn_async_big, g_savepointName2);
    GmcNodeT *vertexLabel12Node = NULL;
    GmcNodeT *vertexLabel13Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int m = 3; m < 2000; m++) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, g_vertexLabel12, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_12, &vertexLabel12Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *instancesNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "instances", GMC_OPERATION_NONE, &instancesNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_13, g_vertexLabel13, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_13);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue3[10];
        memset(namevalue3, 0, sizeof(namevalue3));
        (void)(void)snprintf(namevalue3, 10, "name%d", m);
        ret = GmcSetIndexKeyValue(g_stmt_sync_13, 1, GMC_DATATYPE_STRING, &namevalue3, (strlen(namevalue3)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_13, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_13);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    // network 根节点容器过滤 默认值查询
    TestFetchsubtreeNetwork(g_stmt_sync_2, g_vertexLabel12, "SubtreeReplyJson/RCA_ONE_001_2.json");
    // create savepoint3
    CreateSavepoint(g_conn_async_big, g_savepointName3);
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 5; i < 8000; i++) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue4[10];
        memset(namevalue4, 0, sizeof(namevalue4));
        (void)(void)snprintf(namevalue4, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue4, (strlen(namevalue4)), "name",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    // acl 根节点容器过滤  预期要有 默认值 P节点空间为显示节点名称
    // 不同的环境可能触发分批返回，这里全部允许分批返回并集中校验
    TestTimesFetchSubtreeAcl(g_stmt_sync_2, g_vertexLabel7);
    // 校验,euler环境全检，其他环境可能较慢，抽检
    int checkDensity = 0;
    if (g_envType == 0) {
        checkDensity = 1;
    } else {
        checkDensity = 100;
    }
    for (int i = 5; i < 8000; i++) {
        if (i % checkDensity == 0) {
            string str1 = "\":id\": " + to_string(i - 2) + ",";
            const char *ch1 = str1.c_str();
            string str2 = "\"name\": \"name" + to_string(i) + "\",";
            const char *ch2 = str2.c_str();
            snprintf(g_command, MAX_CMD_SIZE, "grep -rB 2 '%s' reply/", ch2);
            executeCommand(g_command, ch1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    system("rm -rf reply/reply*");
    // 提交事务
    TransCommit(g_conn_async_big);

}
/*****************************************************************************
 * Description  : 多savepoint 跨表dml操作和查询 Release savepoint
 * Input        : 1.创建事务 ，对10-15张表预置数据；
                    2. savepoint1 对 表1做merge操作  subtree查询
                    3. savepoint2 对 表1做delete操作 subtree查询  rollback savepoint
                    4. savepoint3 对 表2做replace操作  （在savepoint1的查询结果基础上 含有表2的replace结果 ）
                    5.提交事务
                    6 subtree查询  （和savepoint3 查询结果一致）
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertietf(g_conn_async_big);
    testYanginsertacl(g_conn_async_big);
    testYanginsertnetwork(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel1Node = NULL;
    GmcNodeT *vertexLabel2Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_002_1.json");
    // release savepoint
    ReleaseSavepoint(g_conn_async_big, g_savepointName);

    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName2);
    // 设置g_vertexLabel1
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_002_2.json");
    // Rollback savepoint
    RollbackSavepoint(g_conn_async_big, g_savepointName2);

    // create savepoint2  对acl做merge操作
    CreateSavepoint(g_conn_async_big, g_savepointName3);
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 5; i < 1000; i++) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue4[10];
        memset(namevalue4, 0, sizeof(namevalue4));
        (void)(void)snprintf(namevalue4, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue4, (strlen(namevalue4)), "name",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch);
    // 对整个namespace 做查询 预期itef表的数据为savepoint1中的结果
    const char *returnJsonPath[3];
    returnJsonPath[0] = "SubtreeReplyJson/RCA_ONE_002_namespace1.json";
    returnJsonPath[1] = "SubtreeReplyJson/RCA_ONE_002_namespace2.json";
    returnJsonPath[2] = "SubtreeReplyJson/RCA_ONE_002_namespace3.json";
    TestFetchSubtreeNamespace(g_stmt_sync_8, returnJsonPath);
    // release savepoint
    ReleaseSavepoint(g_conn_async_big, g_savepointName3);
    // 提交事务
    TransCommit(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // namesapce 查询
    TestFetchSubtreeNamespace(g_stmt_sync_8, returnJsonPath);
    // 提交事务
    TransCommit(g_conn_async_big);

}
/*****************************************************************************
 * Description  : 多savepoint 跨表dml操作和查询 最后rollback savepoint
 * Input        : 1.创建事务 ，对10-15张表预置数据；
                    2. savepoint1 对表1 做merge操作  subtree查询
                    3. savepoint2 对表1 做delete操作 subtree查询 
                    4. savepoint3 对表2 做repalce操作  rollback savepoint
                    5.提交事务
                    6 subtree查询 （和savepoint2 查询结果保持一致）
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertietf(g_conn_async_big);
    testYanginsertacl(g_conn_async_big);
    testYanginsertnetwork(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel1Node = NULL;
    GmcNodeT *vertexLabel2Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_003_1.json");
    // release savepoint
    ReleaseSavepoint(g_conn_async_big, g_savepointName);
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName2);
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_003_2.json");
    // Rollback savepoint
    RollbackSavepoint(g_conn_async_big, g_savepointName2);
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName2);
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 5; i < 1000; i++) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue4[10];
        memset(namevalue4, 0, sizeof(namevalue4));
        (void)(void)snprintf(namevalue4, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue4, (strlen(namevalue4)), "name",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);

    // 提交事务
    TransCommit(g_conn_async_big);
    // 对整个namespace 做查询 预期itef表的数据为savepoint1中的结果
    const char *returnJsonPath[3];
    returnJsonPath[0] = "SubtreeReplyJson/RCA_ONE_002_namespace1.json";
    returnJsonPath[1] = "SubtreeReplyJson/RCA_ONE_002_namespace2.json";
    returnJsonPath[2] = "SubtreeReplyJson/RCA_ONE_002_namespace3.json";
    // 创建乐观事务
    TransStart(g_conn_async_big);
    TestFetchSubtreeNamespace(g_stmt_sync_8, returnJsonPath);
    // 提交事务
    TransCommit(g_conn_async_big);
}
/*****************************************************************************
 * Description  : 多savepoint 跨表dml操作和查询 有增量数据 最后rollback
 * Input        : 1.创建事务 ，对10-15张表预置数据； 
                    2. 进行namespace查询
                    3.savepoint1 对表1 删除数据，进行subtree查询，写入表1数据原来的数据并含有增量 release savepoint；
                    4.savepoint 2 对表 1 表2 做更新数据 ，subtree查询 并rollbacksavepint；
                    5.提交事务；
                    6.进行namespace全量查询  （和savepoint1 查询结果保持一致）
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertietf(g_conn_async_big);
    testYanginsertacl(g_conn_async_big);
    testYanginsertnetwork(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel1Node = NULL;
    GmcNodeT *vertexLabel2Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    // 提交事务
    TransCommit(g_conn_async_big);
    // namespace查询
    const char *returnJsonPath[3];
    returnJsonPath[0] = "SubtreeReplyJson/RCA_ONE_004_namespace1.json";
    returnJsonPath[1] = "SubtreeReplyJson/RCA_ONE_004_namespace2.json";
    returnJsonPath[2] = "SubtreeReplyJson/RCA_ONE_004_namespace3.json";
    // 创建乐观事务
    TransStart(g_conn_async_big);
    TestFetchSubtreeNamespace(g_stmt_sync_2, returnJsonPath);
    // 提交事务
    TransCommit(g_conn_async_big);

    // 创建乐观事务
    TransStart(g_conn_async_big);
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName2);
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    // 查询
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_004_1.json");
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 300; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    ReleaseSavepoint(g_conn_async_big, g_savepointName2);
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_004_2.json");
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName3);
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 5; i < 1000; i++) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *groupsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_NONE, &groupsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue4[10];
        memset(namevalue4, 0, sizeof(namevalue4));
        (void)(void)snprintf(namevalue4, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue4, (strlen(namevalue4)), "name",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel7, "SubtreeReplyJson/RCA_ONE_004_3.json");
    // Rollback savepoint
    RollbackSavepoint(g_conn_async_big, g_savepointName3);
    // 提交事务
    TransCommit(g_conn_async_big);
    returnJsonPath[2] = "SubtreeReplyJson/RCA_ONE_004_namespace4.json";
    // 创建乐观事务
    TransStart(g_conn_async_big);
    TestFetchSubtreeNamespace(g_stmt_sync_2, returnJsonPath);
    // 提交事务
    TransCommit(g_conn_async_big);
}
/*****************************************************************************
 * Description  : 多savepoint 跨表dml操作和查询  步骤2-3循环操作
 * Input        : 1.创建事务 ，对10-15张表预置数据；
                    2. savepoint1 对表1 做merge操作  subtree查询
                    3. savepoint2 对表1 做delete操作 subtree查询 rollback savepoint
                    4. 提交事务
                    5 subtree查询 （和savepoint1 查询结果保持一致）
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertietf(g_conn_async_big);
    testYanginsertacl(g_conn_async_big);
    testYanginsertnetwork(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    for(int l = 0; l <1000; l++) {
        // create savepoint
        CreateSavepoint(g_conn_async_big, g_savepointName);
        GmcBatchT *batch = NULL;
        GmcNodeT *vertexLabel1Node = NULL;
        GmcNodeT *vertexLabel2Node = NULL;
        ret = testBatchPrepare(g_conn_async_big, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for(int i = 3; i < 200; i++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_MERGE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue1[10];
            memset(namevalue1, 0, sizeof(namevalue1));
            (void)(void)snprintf(namevalue1, 10, "name%d", i);
            ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 提交批处理
        BatchExecute(batch);
        TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_003_1.json");
        // release savepoint
        ReleaseSavepoint(g_conn_async_big, g_savepointName);
        // create savepoint
        CreateSavepoint(g_conn_async_big, g_savepointName2);
        ret = testBatchPrepare(g_conn_async_big, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for(int i = 3; i < 200; i++) {
            // 设置g_vertexLabel2 list
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_DELETE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue1[10];
            memset(namevalue1, 0, sizeof(namevalue1));
            (void)(void)snprintf(namevalue1, 10, "name%d", i);
            ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 提交批处理
        BatchExecute(batch);
        TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_003_2.json");
        // release savepoint
        RollbackSavepoint(g_conn_async_big, g_savepointName2);
    }
    // 提交事务
    TransCommit(g_conn_async_big);
    // commit事务后清理表，可能purge线程还在处理数据，清理表获取不到事务锁
    sleep(1);
    // 对整个namespace 做查询 预期itef表的数据为savepoint1中的结果
    const char *returnJsonPath[3];
    returnJsonPath[0] = "SubtreeReplyJson/RCA_ONE_004_namespace1.json";
    returnJsonPath[1] = "SubtreeReplyJson/RCA_ONE_004_namespace2.json";
    returnJsonPath[2] = "SubtreeReplyJson/RCA_ONE_004_namespace3.json";
    // 创建乐观事务
    TransStart(g_conn_async_big);
    TestFetchSubtreeNamespace(g_stmt_sync_2, returnJsonPath);
    // 提交事务
    TransCommit(g_conn_async_big);
}
/*****************************************************************************
 * Description  : 多savepoint 跨表dml操作和查询 大数据量 acl表写入40W数据
 * Input        : 1.创建事务 ，对10-15张表预置数据；
                    2. savepoint1 对表1 做merge操作  subtree查询
                    3. savepoint2 对表1 做delete操作 subtree查询 
                    4. savepoint3 对表2 做repalce操作  subtree查询
                    5.提交事务
                    6 subtree查询 （和savepoint2 查询结果保持一致）
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据 SOHO380表 三棵树 ietf acl network
    testYanginsertietf(g_conn_async_big);
    testYanginsertacl(g_conn_async_big);
    testYanginsertnetwork(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel1Node = NULL;
    GmcNodeT *vertexLabel2Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 8000; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue[10];
        memset(namevalue, 0, sizeof(namevalue));
        (void)(void)snprintf(namevalue, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_2, GMC_DATATYPE_STRING, &namevalue, (strlen(namevalue)), "name",
            GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    // itef 根节点容器过滤 默认值查询
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_001_1.json");
    // create savepoint2
    CreateSavepoint(g_conn_async_big, g_savepointName2);
    GmcNodeT *vertexLabel12Node = NULL;
    GmcNodeT *vertexLabel13Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int m = 3; m < 2000; m++) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_12, g_vertexLabel12, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_12, &vertexLabel12Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *instancesNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel12Node, "instances", GMC_OPERATION_NONE, &instancesNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_13, g_vertexLabel13, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_12, g_stmt_sync_13);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue3[10];
        memset(namevalue3, 0, sizeof(namevalue3));
        (void)(void)snprintf(namevalue3, 10, "name%d", m);
        ret = GmcSetIndexKeyValue(g_stmt_sync_13, 1, GMC_DATATYPE_STRING, &namevalue3, (strlen(namevalue3)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_13, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_13);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    // network 根节点容器过滤 默认值查询
    TestFetchsubtreeNetwork(g_stmt_sync_2, g_vertexLabel12, "SubtreeReplyJson/RCA_ONE_001_2.json");
    // create savepoint3
    CreateSavepoint(g_conn_async_big, g_savepointName3);
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    // acl 表写入40W数据
    for(int k = 0; k < 50; k++) {
        ret = testBatchPrepare(g_conn_async_big, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for(int i = 5 + k * 8000; i < 8000 * (k + 1); i++) {
            // 设置g_vertexLabel1
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangSetRoot(batch, g_stmt_sync_7);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 通过stmt句柄拿到Treenode
            ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 创建child节点 groups
            GmcNodeT *ippoolsNode = NULL;
            ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
            ret = GmcBatchAddDML(batch, g_stmt_sync_7);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_REPLACE_GRAPH);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            char namevalue4[10];
            memset(namevalue4, 0, sizeof(namevalue4));
            (void)(void)snprintf(namevalue4, 10, "name%d", i);
            ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue4, (strlen(namevalue4)), "name",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, g_stmt_sync_8);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // 提交批处理
        BatchExecute(batch);
    }
    // 提交事务
    TransCommit(g_conn_async_big);
}
/*****************************************************************************
 * Description  : 多savepoint 跨表dml操作和查询  同名savepoint
 * Input        : 1.创建事务 ，对10-15张表预置数据； 
                    2. 进行namespace查询
                    3.savepoint1 对表1 删除数据，进行subtree查询，写入表1数据原来的数据并含有增量 release savepoint；
                    4.savepoint 2 对表 1 表2 做更新数据 ，subtree查询 并rollbacksavepint；
                    5.提交事务；
                    6.进行namespace全量查询  （和savepoint1 查询结果保持一致）
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertietf(g_conn_async_big);
    testYanginsertacl(g_conn_async_big);
    testYanginsertnetwork(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel1Node = NULL;
    GmcNodeT *vertexLabel2Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    // 提交事务
    TransCommit(g_conn_async_big);
    // namespace查询
    const char *returnJsonPath[3];
    returnJsonPath[0] = "SubtreeReplyJson/RCA_ONE_004_namespace1.json";
    returnJsonPath[1] = "SubtreeReplyJson/RCA_ONE_004_namespace2.json";
    returnJsonPath[2] = "SubtreeReplyJson/RCA_ONE_004_namespace3.json";
    // 创建乐观事务
    TransStart(g_conn_async_big);
    TestFetchSubtreeNamespace(g_stmt_sync_2, returnJsonPath);
    // 提交事务
    TransCommit(g_conn_async_big);

    // 创建乐观事务
    TransStart(g_conn_async_big);
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName);
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    // 查询
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_004_1.json");
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 300; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    ReleaseSavepoint(g_conn_async_big, g_savepointName);
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_004_2.json");
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName);
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 5; i < 1000; i++) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcNodeT *groupsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "groups", GMC_OPERATION_NONE, &groupsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue4[10];
        memset(namevalue4, 0, sizeof(namevalue4));
        (void)(void)snprintf(namevalue4, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue4, (strlen(namevalue4)), "name",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel7, "SubtreeReplyJson/RCA_ONE_004_3.json");
    // Rollback savepoint
    RollbackSavepoint(g_conn_async_big, g_savepointName);
    // 提交事务
    TransCommit(g_conn_async_big);
    returnJsonPath[2] = "SubtreeReplyJson/RCA_ONE_004_namespace4.json";
    // 创建乐观事务
    TransStart(g_conn_async_big);
    TestFetchSubtreeNamespace(g_stmt_sync_2, returnJsonPath);
    // 提交事务
    TransCommit(g_conn_async_big);
}
/*****************************************************************************
 * Description  : 多savepoint 跨表dml操作和查询  同名savepoint
 * Input        :1.对10-15张表预置数据；创建事务1，
        2. savepoint1 对 表1 做dml操作  subtree查询
        3. savepoint2 对 表1 做delete操作 subtree查询 
        4. savepoint3  并对表2 做dml操作  rollback savepoint
        5.开启事务2
        6事务2的saveponit1 中对表1做replace操作 做全量查询  
        7.提交事务1 回滚事务2
        8.做全量查询和事务1结果保持一致
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertietf(g_conn_async_big);
    testYanginsertacl(g_conn_async_big);
    testYanginsertnetwork(g_conn_async_big);
    // 创建乐观事务一
    TransStart(g_conn_async_big);
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel1Node = NULL;
    GmcNodeT *vertexLabel2Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_003_1.json");
    // release savepoint
    ReleaseSavepoint(g_conn_async_big, g_savepointName);
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName2);
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_003_2.json");
    // Rollback savepoint
    RollbackSavepoint(g_conn_async_big, g_savepointName2);
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName2);
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 5; i < 1000; i++) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue4[10];
        memset(namevalue4, 0, sizeof(namevalue4));
        (void)(void)snprintf(namevalue4, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue4, (strlen(namevalue4)), "name",
                GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    // 对整个namespace 做查询 预期itef表的数据为savepoint1中的结果
    const char *returnJsonPath[3];
    returnJsonPath[0] = "SubtreeReplyJson/RCA_ONE_002_namespace1.json";
    returnJsonPath[1] = "SubtreeReplyJson/RCA_ONE_002_namespace2.json";
    returnJsonPath[2] = "SubtreeReplyJson/RCA_ONE_002_namespace3.json";
    TestFetchSubtreeNamespace(g_stmt_sync_8, returnJsonPath);

    // 开启事务二
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    GmcStmtT *stmt_async1 = NULL;
    const char *namespace1 = "NamespaceRCA";
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn_async, &stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT userData1 = {0};
    ret = GmcUseNamespaceAsync(stmt_async, namespace1, use_namespace_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE; // 可重复读
    config.readOnly = false;
    config.trxType = GMC_OPTIMISTIC_TRX; // 乐观事务
    ret = GmcTransStartAsync(conn_async, &config, trans_start_callback, &userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData1.status);
    memset(&userData1, 0, sizeof(AsyncUserDataT));
    GmcBatchT *batch1 = NULL;
    ret = testBatchPrepare1(conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(stmt_async, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(stmt_async, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch1, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 201; i < 400; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(stmt_async1, g_vertexLabel2, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch1, stmt_async, stmt_async1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(stmt_async1, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue10[10];
        memset(namevalue10, 0, sizeof(namevalue10));
        (void)(void)snprintf(namevalue10, 10, "name%d", i);
        ret = testYangSetField(stmt_async1, GMC_DATATYPE_STRING, &namevalue10, (strlen(namevalue10)), "name", GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch1, stmt_async1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch1);
    TestFetchSubtreeIetf(stmt_async1, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_008.json");
    // 提交事务一
    TransCommit(g_conn_async_big);
    // 回滚事务2
    TransRollback(conn_async);
        
}
/*****************************************************************************
 * Description  : 多savepoint 跨表dml操作和查询 每个操作都rollback
 * Input        : 1.创建事务 ，对10-15张表预置数据；
                    2. savepoint1 对表1 做merge操作  subtree查询 rollback savepoint
                    3. savepoint2 对表2 做repalce操作  rollback savepoint
                    5.提交事务
                    6 subtree查询
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertietf(g_conn_async_big);
    testYanginsertacl(g_conn_async_big);
    testYanginsertnetwork(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName);
    GmcBatchT *batch = NULL;
    GmcNodeT *vertexLabel1Node = NULL;
    GmcNodeT *vertexLabel2Node = NULL;
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置g_vertexLabel1
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel1, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 通过stmt句柄拿到Treenode
    ret = GmcGetRootNode(g_stmt_sync_1, &vertexLabel1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_sync_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 3; i < 200; i++) {
        // 设置g_vertexLabel2 list
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_2, g_vertexLabel2, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_1, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_2, &vertexLabel2Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue1[10];
        memset(namevalue1, 0, sizeof(namevalue1));
        (void)(void)snprintf(namevalue1, 10, "name%d", i);
        ret = GmcSetIndexKeyValue(g_stmt_sync_2, 1, GMC_DATATYPE_STRING, &namevalue1, (strlen(namevalue1)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_sync_2, "k0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    TestFetchSubtreeIetf(g_stmt_sync_2, g_vertexLabel1, "SubtreeReplyJson/RCA_ONE_003_1.json");
    // release savepoint
    RollbackSavepoint(g_conn_async_big, g_savepointName);
    GmcNodeT *vertexLabel7Node = NULL;
    GmcNodeT *vertexLabel8Node = NULL;
    // create savepoint
    CreateSavepoint(g_conn_async_big, g_savepointName2);
    ret = testBatchPrepare(g_conn_async_big, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for(int i = 5; i < 1000; i++) {
        // 设置g_vertexLabel1
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_7, g_vertexLabel7, GMC_OPERATION_NONE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangSetRoot(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 通过stmt句柄拿到Treenode
        ret = GmcGetRootNode(g_stmt_sync_7, &vertexLabel7Node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建child节点 groups
        GmcNodeT *ippoolsNode = NULL;
        ret = GmcYangEditChildNode(vertexLabel7Node, "ip-pools", GMC_OPERATION_NONE, &ippoolsNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_sync_7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync_8, g_vertexLabel8, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_sync_7, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char namevalue4[10];
        memset(namevalue4, 0, sizeof(namevalue4));
        (void)(void)snprintf(namevalue4, 10, "name%d", i);
        ret = testYangSetField(g_stmt_sync_8, GMC_DATATYPE_STRING, &namevalue4, (strlen(namevalue4)), "name",
            GMC_YANG_PROPERTY_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_sync_8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 提交批处理
    BatchExecute(batch);
    RollbackSavepoint(g_conn_async_big, g_savepointName2);
    // 提交事务
    TransCommit(g_conn_async_big);
    // 对整个namespace 做查询 预期itef表的数据为savepoint1中的结果
    const char *returnJsonPath[3];
    returnJsonPath[0] = "SubtreeReplyJson/RCA_ONE_009_namespace1.json";
    returnJsonPath[1] = "SubtreeReplyJson/RCA_ONE_009_namespace2.json";
    returnJsonPath[2] = "SubtreeReplyJson/RCA_ONE_009_namespace3.json";
    // 创建乐观事务
    TransStart(g_conn_async_big);
    TestFetchSubtreeNamespace(g_stmt_sync_15, returnJsonPath);
    // 提交事务
    TransCommit(g_conn_async_big);
}
/*****************************************************************************
 * Description  : isconfig 为 true（配置） 空节点   做config查询 的配置查询 容器过滤 
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertaclconfig(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // 使用接口完成subtree 查询
    GmcNodeT * root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_1, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT * groups = NULL;
    const char *groupsNodename = "groups";
    ret = GmcYangEditChildNode(root, groupsNodename, GMC_OPERATION_SUBTREE_FILTER, &groups);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 1,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *subtreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/RCA_ONE_010.json", &subtreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = subtreeReturnJson;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_sync_1,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_1, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subtreeReturnJson);
    // 提交事务
    TransCommit(g_conn_async_big);
}
/*****************************************************************************
 * Description  : isconfig 为 true（配置） 空节点   做config查询 的状态查询 容器过滤
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertaclconfig(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // 使用接口完成subtree 查询
    GmcNodeT * root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_1, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT * groups = NULL;
    const char *groupsNodename = "groups";
    ret = GmcYangEditChildNode(root, groupsNodename, GMC_OPERATION_SUBTREE_FILTER, &groups);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 2,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    std::vector<std::string> reply(1);
    reply[0] = "{}";
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_sync_1,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_1, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交事务
    TransCommit(g_conn_async_big);
}
/*****************************************************************************
 * Description  : isconfig 为 false（状态） 空节点   做config查询 的状态查询 容器过滤
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertaclconfig(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // 使用接口完成subtree 查询
    GmcNodeT * root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_1, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT * groupsCC = NULL;
    const char *groupsCCNodename = "groupsCC";
    ret = GmcYangEditChildNode(root, groupsCCNodename, GMC_OPERATION_SUBTREE_FILTER, &groupsCC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 2,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *subtreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/RCA_ONE_012.json", &subtreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = subtreeReturnJson;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_sync_1,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_1, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subtreeReturnJson);
    // 提交事务
    TransCommit(g_conn_async_big);
}
/*****************************************************************************
 * Description  : isconfig 为 false（状态） 空节点   做config查询 的配置查询 容器过滤 
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertaclconfig(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // 使用接口完成subtree 查询
    GmcNodeT * root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_1, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT * groupsCC = NULL;
    const char *groupsCCNodename = "groupsCC";
    ret = GmcYangEditChildNode(root, groupsCCNodename, GMC_OPERATION_SUBTREE_FILTER, &groupsCC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 1,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *subtreeReturnJson = "{}";
    std::vector<std::string> reply(1);
    reply[0] = subtreeReturnJson;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_sync_1,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_1, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交事务
    TransCommit(g_conn_async_big);
}
/*****************************************************************************
 * Description  : 节点 isconfig 为true   字段isconfig 为 false 做config查询 的配置查询 容器过滤
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertaclconfig(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // 使用接口完成subtree 查询
    GmcNodeT * root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_1, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT * groupAA = NULL;
    const char *groupAANodename = "groupAA";
    ret = GmcYangEditChildNode(root, groupAANodename, GMC_OPERATION_SUBTREE_FILTER, &groupAA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 1,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *subtreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/RCA_ONE_014.json", &subtreeReturnJson);
    std::vector<std::string> reply(1);
    reply[0] = subtreeReturnJson;
    FetchRetCbParam param = {
        .step = 0,
        .stmt = g_stmt_sync_1,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_1, &filters, NULL, AsyncFetchRetCb, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subtreeReturnJson);
    // 提交事务
    TransCommit(g_conn_async_big);
}
/*****************************************************************************
 * Description  :config 查询 分批返回数据
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(RCA_ONE, RCA_ONE_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 预制数据
    testYanginsertaclconfig(g_conn_async_big);
    testYanginsertaclconfig20480(g_conn_async_big);
    // 创建乐观事务
    TransStart(g_conn_async_big);
    // 使用接口完成subtree 查询
    GmcNodeT * root = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync_1, g_vertexLabel7, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_sync_1, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcSubtreeFilterItemT filter = {
        .rootName = NULL,
        .subtree = {.obj = root},
        .jsonFlag = GMC_JSON_INDENT(4),
        .maxDepth = 0,
        .isLocationFilter = 0,
        .defaultMode = 0,
        .configFlag = 0,
    };
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *subtreeReturnJson = NULL;
    readJanssonFile("SubtreeReplyJson/RCA_ONE_01501.json", &subtreeReturnJson);
    char *subtreeReturnJson01 = NULL;
    readJanssonFile("SubtreeReplyJson/RCA_ONE_01502.json", &subtreeReturnJson01);
    std::vector<std::string> reply(2);
    reply[0] = subtreeReturnJson;
    reply[1] = subtreeReturnJson01; 
    FetchRetCbParam01 param = {
        .step = 0,
        .stmt = g_stmt_sync_1,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_sync_1, &filters, NULL, AsyncFetchRetCb01, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subtreeReturnJson);
    free(subtreeReturnJson01);
    // 提交事务
    TransCommit(g_conn_async_big);
}
