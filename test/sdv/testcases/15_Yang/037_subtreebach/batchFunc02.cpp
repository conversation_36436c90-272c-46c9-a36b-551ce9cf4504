/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "TreeSubTree_common.h"

class batchFunc01 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void batchFunc01::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/start.sh");
    int ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void batchFunc01::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void batchFunc01::SetUp()
{
    int ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  异步链接继续申请句柄
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //  异步链接继续申请句柄
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 异步创建namespace
    // 设置namespace级别为：可重复读 + 乐观事务
    AsyncUserDataT userData = {0};
    const char *namespace1 = "Namespace37";
    const char *namespaceUserName = "abc";
    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = namespace1;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    //异步句柄使用namespace
    ret = GmcUseNamespaceAsync(g_stmt_async1, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    ret = GmcUseNamespaceAsync(g_stmt_async2, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // alloc all stmt
    TestYangAllocAllstmt();
    TestYangComplexAllocAllstmt();
    TestYangAllocAllstmt2();
    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt_sync, namespace1);
    EXPECT_EQ(GMERR_OK, ret);
    //  删边
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeT0T1);
    // 删表
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT0);
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT1);
    //  删边
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeComplexrootL1);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeL1L2);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeL2L3);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeL2L4);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeL2L5);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeComplexrootLC1);
    // 删表
    GmcDropVertexLabel(g_stmt_sync, g_complexroot);
    GmcDropVertexLabel(g_stmt_sync, g_l1);
    GmcDropVertexLabel(g_stmt_sync, g_l2);
    GmcDropVertexLabel(g_stmt_sync, g_l3);
    GmcDropVertexLabel(g_stmt_sync, g_l4);
    GmcDropVertexLabel(g_stmt_sync, g_l5);
    GmcDropVertexLabel(g_stmt_sync, g_lC1);

    //  全打散模型 删边
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeT0T12);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeT0T1container2);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeT0T1choice2);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabelT1ChoiceT2case12);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeT1T2Con2);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabeT1T2List2);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabetT1T2Choice2);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabelT2ChoiceT3case12);
    GmcDropEdgeLabel(g_stmt_sync, g_edgeLabelT2ChoiceT3case22);
    // 全打散模型 删表
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT02);
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT12);
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT1container2);
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT1choice2);
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT1choicecase2);
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT2Con2);
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT2List2);
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT2choice2);
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT3case12);
    GmcDropVertexLabel(g_stmt_sync, g_vertexLabelT3case22);
    //.普通模型建表 建边
    readJanssonFile("schema/SubTreeVertexLabel.gmjson", &g_vertexschema);
    ASSERT_NE((void *)NULL, g_vertexschema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema);

    readJanssonFile("schema/SubTreeEdgelLabel.gmjson", &g_edgeschema);
    ASSERT_NE((void *)NULL, g_edgeschema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema);
    // 复杂模型建表建边
    readJanssonFile("schema/SubTreeComplexVertexLabel.gmjson", &g_vertexschemacomplex);
    ASSERT_NE((void *)NULL, g_vertexschemacomplex);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschemacomplex, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschemacomplex);
    readJanssonFile("schema/SubTreeComplexEdgelLabel.gmjson", &g_edgeschemacomplex);
    ASSERT_NE((void *)NULL, g_edgeschemacomplex);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschemacomplex, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschemacomplex);

    //.全打散模型 建表 建边
    readJanssonFile("schema/SubTreeVertexLabel2.gmjson", &g_vertexschema2);
    ASSERT_NE((void *)NULL, g_vertexschema2);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_vertexschema2, g_labelconfig, create_vertex_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_vertexschema1);

    readJanssonFile("schema/SubTreeEdgelLabel2.gmjson", &g_edgeschema2);
    ASSERT_NE((void *)NULL, g_edgeschema2);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeschema2, g_labelconfig, create_edge_label_callback,
        &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    free(g_edgeschema2);

    AW_CHECK_LOG_BEGIN();
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
}

void batchFunc01::TearDown()
{
    AW_CHECK_LOG_END();
    const char *namespace1 = "Namespace37";
    AsyncUserDataT userData = {0};
    //  删表
    int ret = GmcUseNamespaceAsync(g_stmt_async, namespace1, use_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    
    testClearNsp(g_stmt_async, namespace1);
    // 释放all stmt
    TestYangFreeAllstmt();
    TestYangComplexFreeAllstmt();
    TestYangFreeAllstmt2();

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, namespace1, drop_namespace_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, userData.status);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeStmt(g_stmt_async1);
    GmcFreeStmt(g_stmt_async2);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 029.三张表进行内容过滤（含全打散表）
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(batchFunc01, Yang_037_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 普通模型预制数据
    testYangPresetAllDate(g_conn_async);
    // 复杂模型预制数据
    testYangComplexPresetAllDate(g_conn_async);
    // 普通模型1预制数据
    testYangPresetAllDate2(g_conn_async);
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表1
    GmcNodeT * root1 = NULL;
    const char *SubT0ConNode = "SubT0Con";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "SubT0Con", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = testYangSetField(root1, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表2
    GmcNodeT * root2 = NULL;
    const char *ComplexrootNode = "Complexroot";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async1, ComplexrootNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async1, &root2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 1;
    ret = testYangSetField(root2, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表3
    GmcNodeT * root3 = NULL;
    const char *SubT0ConNode1 = "SubT0Con2";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async2, SubT0ConNode1, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async2, &root3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 1;
    ret = testYangSetField(root3, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 两张表的查询条件格式
    GmcSubtreeFilterItemT filter = {0};
    // 表1的查询格式
    
    filter.subtree.obj = root1;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.maxDepth = 0;
    filter.isLocationFilter = 0;
    filter.defaultMode = 0;
    filter.configFlag = 0;
    // 表2的查询格式
    GmcSubtreeFilterItemT filter1 = {0};
    
    filter1.subtree.obj = root2;
    filter1.jsonFlag = GMC_JSON_INDENT(4);
    filter1.maxDepth = 0;
    filter1.isLocationFilter = 0;
    filter1.defaultMode = 0;
    filter1.configFlag = 0;
    filter.next = &filter1;
    // 表3的查询格式
    GmcSubtreeFilterItemT filter2 = {0};
    
    filter2.subtree.obj = root3;
    filter2.jsonFlag = GMC_JSON_INDENT(4);
    filter2.maxDepth = 0;
    filter2.isLocationFilter = 0;
    filter2.defaultMode = 0;
    filter2.configFlag = 0;
    filter1.next = &filter2;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson1 = NULL;
    int TimeZoneType = GetTimeZoneType();
    if (TimeZoneType == 0) {
        readJanssonFile("SubtreeReplyJson/Reply2901.json", &suntreeReturnJson1);
    } else {
        readJanssonFile("ARM32SubtreeReplyJson/Reply2901.json", &suntreeReturnJson1);
    }
    ASSERT_NE((void *)NULL, suntreeReturnJson1);
    char *suntreeReturnJson2 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply2902.json", &suntreeReturnJson2);
    ASSERT_NE((void *)NULL, suntreeReturnJson2);
    char *suntreeReturnJson3 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply2903.json", &suntreeReturnJson3);
    ASSERT_NE((void *)NULL, suntreeReturnJson3);
    std::vector<std::string> reply(3);
    reply[0] = suntreeReturnJson1;
    reply[1] = suntreeReturnJson2;
    reply[2] = suntreeReturnJson3;
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson1);
    free(suntreeReturnJson2);
    free(suntreeReturnJson3);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 030.三张表进行叶子过滤（含全打散表）
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(batchFunc01, Yang_037_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 普通模型预制数据
    testYangPresetAllDate(g_conn_async);
    // 复杂模型预制数据
    testYangComplexPresetAllDate(g_conn_async);
    // 普通模型1预制数据
    testYangPresetAllDate2(g_conn_async);
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表1
    GmcNodeT * root1 = NULL;
    const char *SubT0ConNode = "SubT0Con";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "SubT0Con", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = testsubtreeSetvalue(root1, GMC_DATATYPE_NULL, NULL, 0, "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表2
    GmcNodeT * root2 = NULL;
    const char *ComplexrootNode = "Complexroot";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async1, ComplexrootNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async1, &root2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 1;
    ret = testsubtreeSetvalue(root2, GMC_DATATYPE_NULL, NULL, 0, "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表3
    GmcNodeT * root3 = NULL;
    const char *SubT0ConNode1 = "SubT0Con2";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async2, SubT0ConNode1, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async2, &root3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 1;
    ret = testsubtreeSetvalue(root3, GMC_DATATYPE_NULL, NULL, 0, "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 两张表的查询条件格式
    GmcSubtreeFilterItemT filter = {0};
    // 表1的查询格式
    
    filter.subtree.obj = root1;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.maxDepth = 0;
    filter.isLocationFilter = 0;
    filter.defaultMode = 0;
    filter.configFlag = 0;
    // 表2的查询格式
    GmcSubtreeFilterItemT filter1 = {0};
    
    filter1.subtree.obj = root2;
    filter1.jsonFlag = GMC_JSON_INDENT(4);
    filter1.maxDepth = 0;
    filter1.isLocationFilter = 0;
    filter1.defaultMode = 0;
    filter1.configFlag = 0;
    filter.next = &filter1;
    // 表3的查询格式
    GmcSubtreeFilterItemT filter2 = {0};
    
    filter2.subtree.obj = root3;
    filter2.jsonFlag = GMC_JSON_INDENT(4);
    filter2.maxDepth = 0;
    filter2.isLocationFilter = 0;
    filter2.defaultMode = 0;
    filter2.configFlag = 0;
    filter1.next = &filter2;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson1 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply3001.json", &suntreeReturnJson1);
    ASSERT_NE((void *)NULL, suntreeReturnJson1);
    char *suntreeReturnJson2 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply3002.json", &suntreeReturnJson2);
    ASSERT_NE((void *)NULL, suntreeReturnJson2);
    char *suntreeReturnJson3 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply3003.json", &suntreeReturnJson3);
    ASSERT_NE((void *)NULL, suntreeReturnJson3);
    std::vector<std::string> reply(3);
    reply[0] = suntreeReturnJson1;
    reply[1] = suntreeReturnJson2;
    reply[2] = suntreeReturnJson3;
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson1);
    free(suntreeReturnJson2);
    free(suntreeReturnJson3);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 026.三张表进行容器过滤
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(batchFunc01, Yang_037_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 普通模型预制数据
    testYangPresetAllDate(g_conn_async);
    // 复杂模型预制数据
    testYangComplexPresetAllDate(g_conn_async);
    // 普通模型1预制数据
    testYangPresetAllDate2(g_conn_async);
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表1
    GmcNodeT * root1 = NULL;
    const char *SubT0ConNode = "SubT0Con";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "SubT0Con", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表2
    GmcNodeT * root2 = NULL;
    const char *ComplexrootNode = "Complexroot";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async1, ComplexrootNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async1, &root2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表3
    GmcNodeT * root3 = NULL;
    const char *SubT0ConNode1 = "SubT0Con2";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async2, SubT0ConNode1, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async2, &root3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 两张表的查询条件格式
    GmcSubtreeFilterItemT filter = {0};
    // 表1的查询格式
    
    filter.subtree.obj = root1;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.maxDepth = 0;
    filter.isLocationFilter = 0;
    filter.defaultMode = 0;
    filter.configFlag = 0;
    // 表2的查询格式
    GmcSubtreeFilterItemT filter1 = {0};
    
    filter1.subtree.obj = root2;
    filter1.jsonFlag = GMC_JSON_INDENT(4);
    filter1.maxDepth = 0;
    filter1.isLocationFilter = 0;
    filter1.defaultMode = 0;
    filter1.configFlag = 0;
    filter.next = &filter1;
    // 表3的查询格式
    GmcSubtreeFilterItemT filter2 = {0};
    
    filter2.subtree.obj = root3;
    filter2.jsonFlag = GMC_JSON_INDENT(4);
    filter2.maxDepth = 0;
    filter2.isLocationFilter = 0;
    filter2.defaultMode = 0;
    filter2.configFlag = 0;
    filter1.next = &filter2;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson1 = NULL;
    int TimeZoneType = GetTimeZoneType();
    if (TimeZoneType == 0) {
        readJanssonFile("SubtreeReplyJson/Reply3101.json", &suntreeReturnJson1);
    } else {
        readJanssonFile("ARM32SubtreeReplyJson/Reply3101.json", &suntreeReturnJson1);
    }
    ASSERT_NE((void *)NULL, suntreeReturnJson1);
    char *suntreeReturnJson2 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply3102.json", &suntreeReturnJson2);
    ASSERT_NE((void *)NULL, suntreeReturnJson2);
    char *suntreeReturnJson3 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply3103.json", &suntreeReturnJson3);
    ASSERT_NE((void *)NULL, suntreeReturnJson3);
    std::vector<std::string> reply(3);
    reply[0] = suntreeReturnJson1;
    reply[1] = suntreeReturnJson2;
    reply[2] = suntreeReturnJson3;
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson1);
    free(suntreeReturnJson2);
    free(suntreeReturnJson3);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 027.三张表进行list节点多条件或
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(batchFunc01, Yang_037_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 普通模型预制数据
    testYangPresetAllDate(g_conn_async);
    // 复杂模型预制数据
    testYangComplexPresetAllDate(g_conn_async);
    // 普通模型1预制数据
    testYangPresetAllDate2(g_conn_async);
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表1
    GmcNodeT * root1 = NULL;
    const char *SubT0ConNode = "SubT0Con";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "SubT0Con", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT * T1List = NULL;
    const char *SubT1ListNode = "SubT1List";
    ret = GmcYangEditChildNode(root1, SubT1ListNode, GMC_OPERATION_SUBTREE_FILTER, &T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = testYangSetField(T1List, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f1 = 1;
    ret = testYangSetField(T1List, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool f2 = true;
    ret = testYangSetField(T1List, GMC_DATATYPE_BOOL, &f2, sizeof(bool), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // list或关系
    ret = GmcYangEditChildNode(root1, SubT1ListNode, GMC_OPERATION_SUBTREE_FILTER, &T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 2;
    ret = testYangSetField(T1List, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f1 = 2;
    ret = testYangSetField(T1List, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f2 = true;
    ret = testYangSetField(T1List, GMC_DATATYPE_BOOL, &f2, sizeof(bool), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表2
    GmcNodeT * root2 = NULL;
    const char *ComplexrootNode = "Complexroot";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async1, ComplexrootNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async1, &root2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT * L1 = NULL;
    const char *SubL1 = "L1";
    ret = GmcYangEditChildNode(root2, SubL1, GMC_OPERATION_SUBTREE_FILTER, &L1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f1 = 1;
    ret = testYangSetField(L1, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 1;
    ret = testYangSetField(L1, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(L1, GMC_DATATYPE_BOOL, &f2, sizeof(bool), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // list或关系
    ret = GmcYangEditChildNode(root2, SubL1, GMC_OPERATION_SUBTREE_FILTER, &L1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 2;
    ret = testYangSetField(L1, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f1 = 2;
    ret = testYangSetField(L1, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f2 = true;
    ret = testYangSetField(L1, GMC_DATATYPE_BOOL, &f2, sizeof(bool), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表3
    GmcNodeT * root3 = NULL;
    const char *SubT0ConNode1 = "SubT0Con2";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async2, SubT0ConNode1, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async2, &root3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT * T1List1 = NULL;
    const char *SubT1ListNode1 = "SubT1List2";
    ret = GmcYangEditChildNode(root3, SubT1ListNode1, GMC_OPERATION_SUBTREE_FILTER, &T1List1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 1;
    ret = testYangSetField(T1List1, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f1 = 1;
    ret = testYangSetField(T1List1, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(T1List1, GMC_DATATYPE_BOOL, &f2, sizeof(bool), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // list或关系
    ret = GmcYangEditChildNode(root3, SubT1ListNode1, GMC_OPERATION_SUBTREE_FILTER, &T1List1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f0 = 2;
    ret = testYangSetField(T1List1, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f1 = 1;
    ret = testYangSetField(T1List1, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f2 = true;
    ret = testYangSetField(T1List1, GMC_DATATYPE_BOOL, &f2, sizeof(bool), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 两张表的查询条件格式
    GmcSubtreeFilterItemT filter = {0};
    // 表1的查询格式
    
    filter.subtree.obj = root1;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.maxDepth = 0;
    filter.isLocationFilter = 0;
    filter.defaultMode = 0;
    filter.configFlag = 0;
    // 表2的查询格式
    GmcSubtreeFilterItemT filter1 = {0};
    
    filter1.subtree.obj = root2;
    filter1.jsonFlag = GMC_JSON_INDENT(4);
    filter1.maxDepth = 0;
    filter1.isLocationFilter = 0;
    filter1.defaultMode = 0;
    filter1.configFlag = 0;
    filter.next = &filter1;
    // 表3的查询格式
    GmcSubtreeFilterItemT filter2 = {0};
    
    filter2.subtree.obj = root3;
    filter2.jsonFlag = GMC_JSON_INDENT(4);
    filter2.maxDepth = 0;
    filter2.isLocationFilter = 0;
    filter2.defaultMode = 0;
    filter2.configFlag = 0;
    filter1.next = &filter2;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson1 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply3201.json", &suntreeReturnJson1);
    ASSERT_NE((void *)NULL, suntreeReturnJson1);
    char *suntreeReturnJson2 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply3202.json", &suntreeReturnJson2);
    ASSERT_NE((void *)NULL, suntreeReturnJson2);
    char *suntreeReturnJson3 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply3203.json", &suntreeReturnJson3);
    ASSERT_NE((void *)NULL, suntreeReturnJson3);
    std::vector<std::string> reply(3);
    reply[0] = suntreeReturnJson1;
    reply[1] = suntreeReturnJson2;
    reply[2] = suntreeReturnJson3;
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson1);
    free(suntreeReturnJson2);
    free(suntreeReturnJson3);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 * Description  : 028.三张表进行list节点多条件与
 * Input        : None
 * Output       : None
 * Notes        :
 * Author       : wk/wwx1038088
 * Modification : 
 * *****************************************************************************/
TEST_F(batchFunc01, Yang_037_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    // 普通模型预制数据
    testYangPresetAllDate(g_conn_async);
    // 复杂模型预制数据
    testYangComplexPresetAllDate(g_conn_async);
    // 普通模型1预制数据
    testYangPresetAllDate2(g_conn_async);
    // 启动事务
    ret = testTransStartAsync(g_conn_async, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表1
    GmcNodeT * root1 = NULL;
    const char *SubT0ConNode = "SubT0Con";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, "SubT0Con", GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async, &root1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT * T1List = NULL;
    const char *SubT1ListNode = "SubT1List";
    ret = GmcYangEditChildNode(root1, SubT1ListNode, GMC_OPERATION_SUBTREE_FILTER, &T1List);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t f0 = 1;
    ret = testYangSetField(T1List, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t f1 = 1;
    ret = testYangSetField(T1List, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool f2 = true;
    ret = testYangSetField(T1List, GMC_DATATYPE_BOOL, &f2, sizeof(bool), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表2
    GmcNodeT * root2 = NULL;
    const char *ComplexrootNode = "Complexroot";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async1, ComplexrootNode, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async1, &root2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT * L1 = NULL;
    const char *SubL1 = "L1";
    ret = GmcYangEditChildNode(root2, SubL1, GMC_OPERATION_SUBTREE_FILTER, &L1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    f1 = 1;
    ret = testYangSetField(L1, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(L1, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(L1, GMC_DATATYPE_BOOL, &f2, sizeof(bool), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 使用接口完成subtree 查询 表3
    GmcNodeT * root3 = NULL;
    const char *SubT0ConNode1 = "SubT0Con2";
    ret = testGmcPrepareStmtByLabelName(g_stmt_async2, SubT0ConNode1, GMC_OPERATION_SUBTREE_FILTER);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_async2, &root3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT * T1List1 = NULL;
    const char *SubT1ListNode1 = "SubT1List2";
    ret = GmcYangEditChildNode(root3, SubT1ListNode1, GMC_OPERATION_SUBTREE_FILTER, &T1List1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(T1List1, GMC_DATATYPE_UINT32, &f0, sizeof(uint32_t), "F0", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(T1List1, GMC_DATATYPE_INT32, &f1, sizeof(int32_t), "F1", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetField(T1List1, GMC_DATATYPE_BOOL, &f2, sizeof(bool), "F2", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 两张表的查询条件格式
    GmcSubtreeFilterItemT filter = {0};
    // 表1的查询格式
    
    filter.subtree.obj = root1;
    filter.jsonFlag = GMC_JSON_INDENT(4);
    filter.maxDepth = 0;
    filter.isLocationFilter = 0;
    filter.defaultMode = 0;
    filter.configFlag = 0;
    // 表2的查询格式
    GmcSubtreeFilterItemT filter1 = {0};
    
    filter1.subtree.obj = root2;
    filter1.jsonFlag = GMC_JSON_INDENT(4);
    filter1.maxDepth = 0;
    filter1.isLocationFilter = 0;
    filter1.defaultMode = 0;
    filter1.configFlag = 0;
    filter.next = &filter1;
    // 表3的查询格式
    GmcSubtreeFilterItemT filter2 = {0};
    
    filter2.subtree.obj = root3;
    filter2.jsonFlag = GMC_JSON_INDENT(4);
    filter2.maxDepth = 0;
    filter2.isLocationFilter = 0;
    filter2.defaultMode = 0;
    filter2.configFlag = 0;
    filter1.next = &filter2;
    GmcSubtreeFilterT filters = {
        .filterMode = GMC_FETCH_OBJ,
        .filter = &filter,
    };
    char *suntreeReturnJson1 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply3301.json", &suntreeReturnJson1);
    ASSERT_NE((void *)NULL, suntreeReturnJson1);
    char *suntreeReturnJson2 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply3302.json", &suntreeReturnJson2);
    ASSERT_NE((void *)NULL, suntreeReturnJson2);
    char *suntreeReturnJson3 = NULL;
    readJanssonFile("SubtreeReplyJson/Reply3303.json", &suntreeReturnJson3);
    ASSERT_NE((void *)NULL, suntreeReturnJson3);
    std::vector<std::string> reply(3);
    reply[0] = suntreeReturnJson1;
    reply[1] = suntreeReturnJson2;
    reply[2] = suntreeReturnJson3;
    FetchRetCbParam123 param = {
        .step = 0,
        .stmt = g_stmt_async,
        .expectStatus = GMERR_OK,
        .filterMode = filters.filterMode,
        .lastExpectIdx = 0,
        .expectReply = reply,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmt_async, &filters, NULL, AsyncFetchRetCb123, &param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncSubtreeRecv_API(&param);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(suntreeReturnJson1);
    free(suntreeReturnJson2);
    free(suntreeReturnJson3);
    // 提交事务
    ret = testTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


