/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : 事务支持带savepoint基本功能测试（Yang场景--树模型）
 Notes        : 001.乐观事务内创建savepoint，vertex操作类型为create，属性操作类型分别为五原语操作
                002.乐观事务内创建savepoint，vertex操作类型为merge，属性操作类型分别为五原语操作
                003.乐观事务内创建savepoint，vertex操作类型为replace，属性操作类型分别为五原语操作
                004.乐观事务内创建savepoint，vertex操作类型为delete，属性操作类型分别为五原语操作
                005.乐观事务内创建savepoint，vertex操作类型为remove，属性操作类型分别为五原语操作
                006.乐观事务内创建savepoint，vertex操作类型为none，属性操作类型分别为五原语操作
                007.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行merge、replace操作
                008.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行merge、delete操作
                009.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行merge、remove操作
                010.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行replace、delete操作
                011.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行replace、remove操作
                012.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行delete、remove操作
                013.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；replace、查询diff数据
                014.乐观事务内创建两个不同的savepoint，分别执行replace、查询diff数据；delete、查询diff数据
                015.乐观事务内创建两个不同的savepoint，分别执行delete、查询diff数据；remove、查询diff数据
                016.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；delete、查询diff数据
                017.乐观事务内创建两个不同的savepoint，分别执行replace、查询diff数据；remove、查询diff数据
                018.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；remove、查询diff数据
                019.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_FIRST、GMC_YANG_LIST_POSITION_LAST
                020.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_FIRST、GMC_YANG_LIST_POSITION_REMAIN
                021.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_FIRST、GMC_YANG_LIST_POSITION_BEFORE
                022.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_FIRST、GMC_YANG_LIST_POSITION_AFTER
                023.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_LAST、GMC_YANG_LIST_POSITION_REMAIN
                024.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_LAST、GMC_YANG_LIST_POSITION_BEFORE
                025.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_LAST、GMC_YANG_LIST_POSITION_AFTER
                026.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_REMAIN、GMC_YANG_LIST_POSITION_BEFORE
                027.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_REMAIN、GMC_YANG_LIST_POSITION_AFTER
                028.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_BEFORE、GMC_YANG_LIST_POSITION_AFTER
                029.一个乐观事务内创建3个不同的savepoint，回滚第二个savepoint，SubTree查询
                030.一个乐观事务内创建3个不同的savepoint，释放第三个savepoint，SubTree查询
                031.乐观事务内创建一个savepoint，写操作失败，回滚savepoint，重新创建savepoint，再次执行六元语操作
                032.乐观事务内创建两个savepoint，第二个savepoint六元语操作失败，回滚第二个savepoint，重新创建savepoint，再次执行六元语操作
                033.创建两个乐观事务，每个事务分别创建一个savepoint，事务二提交失败后，事务二再次创建savepoint
                034.乐观事务内创建两个同名savepoint，分别执行六元语操作，连续回滚savepoint、SubTree查询
                035.乐观事务内创建两个匿名savepoint，分别执行六元语操作，连续释放savepoint、SubTree查询
                036.超长事务内创建savepoint，执行六元语操作，事务超时后SubTree查询
                037.超长事务内创建两个savepoint，分别执行六元语操作，回滚第二个savepoint，重新创建savepoint，再次执行六元语操作，事务超时后SubTree查询

 History      :
 Author       : liaoxiang lwx1036939
 Modification : 2022/9/8
*****************************************************************************/

#include "TransSavepoint.h"

class Savepoint_FunTr : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = createEpollOneThread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        closeEpollOneThread();
        testEnvClean();
    }
};

void Savepoint_FunTr::SetUp()
{
    int ret = 0;

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(g_stmt_async, g_namespace);

    // use namespace
    useNameSpace(g_stmt_async, g_namespace);
    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    AW_CHECK_LOG_BEGIN();

    // 添加日志白名单
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);

    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);

    char errorMsg4[128] = {};
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg4);
}

void Savepoint_FunTr::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    // drop namespace
    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    dropNameSpace(g_stmt_async, g_namespace);

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);
    GmcFreeStmt(g_stmt_list);

    // 释放异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

class Savepoint_FunTr_01 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=1,10\"");
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = createEpollOneThread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        closeEpollOneThread();
        GmcDetachAllShmSeg();
        testEnvClean();
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
        system("sh $TEST_HOME/tools/start.sh");
    }
};

void Savepoint_FunTr_01::SetUp()
{
    int ret = 0;

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(g_stmt_async, g_namespace);

    // use namespace
    useNameSpace(g_stmt_async, g_namespace);

    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void Savepoint_FunTr_01::TearDown()
{
    int ret = 0;
    // drop namespace
    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    dropNameSpace(g_stmt_async, g_namespace);

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);
    GmcFreeStmt(g_stmt_list);

    // 释放异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
class Savepoint_FunTr_02 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=1\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"messageSecurityCheck=0\"");
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = createEpollOneThread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        closeEpollOneThread();
        GmcDetachAllShmSeg();
        testEnvClean();
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
        system("sh $TEST_HOME/tools/start.sh");
    }
};

void Savepoint_FunTr_02::SetUp()
{
    int ret = 0;

    // 创建异步连接
    YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_con);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 异步创建namespace
    createNameSpace(g_stmt_async, g_namespace);

    // use namespace
    useNameSpace(g_stmt_async, g_namespace);

    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
}

void Savepoint_FunTr_02::TearDown()
{
    int ret = 0;
    // drop namespace
    AsyncUserDataT data = {0};
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    dropNameSpace(g_stmt_async, g_namespace);

    GmcFreeStmt(g_stmt_root);
    GmcFreeStmt(g_stmt_con);
    GmcFreeStmt(g_stmt_list);

    // 释放异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001.乐观事务内创建savepoint，vertex操作类型为create，属性操作类型分别为五原语操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_001)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 002.乐观事务内创建savepoint，vertex操作类型为merge，属性操作类型分别为五原语操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_002)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 merge 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 003.乐观事务内创建savepoint，vertex操作类型为replace，属性操作类型分别为五原语操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_003)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 replace 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 004.乐观事务内创建savepoint，vertex操作类型为delete，属性操作类型分别为五原语操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_004)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint，预置数据
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 delete 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ;

    // 对属性 P1 执行 create 操作
    GmcPropValueT propValue;
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_CREATE.\n\n");
    memcpy(propValue.propertyName, "P1", (strlen("P1") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P2 执行 merge 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_MERGE.\n\n");
    memcpy(propValue.propertyName, "P2", (strlen("P2") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P3 执行 replace 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_REPLACE.\n\n");
    memcpy(propValue.propertyName, "P3", (strlen("P3") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P4 执行 delete 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_REMOVE.\n\n");
    memcpy(propValue.propertyName, "P4", (strlen("P4") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P5 执行 remove 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_DELETE.\n\n");
    memcpy(propValue.propertyName, "P5", (strlen("P5") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 005.乐观事务内创建savepoint，vertex操作类型为remove，属性操作类型分别为五原语操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_005)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint，预置数据
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 remove 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对属性 P1 执行 create 操作
    GmcPropValueT propValue;
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_CREATE.\n\n");
    memcpy(propValue.propertyName, "P1", (strlen("P1") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P2 执行 merge 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_MERGE.\n\n");
    memcpy(propValue.propertyName, "P2", (strlen("P2") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P3 执行 replace 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_REPLACE.\n\n");
    memcpy(propValue.propertyName, "P3", (strlen("P3") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P4 执行 delete 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_REMOVE.\n\n");
    memcpy(propValue.propertyName, "P4", (strlen("P4") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_REMOVE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 对属性 P5 执行 remove 操作
    AW_FUN_Log(LOG_DEBUG, "GMC_YANG_PROPERTY_OPERATION_DELETE.\n\n");
    memcpy(propValue.propertyName, "P5", (strlen("P5") + 1));
    propValue.type = GMC_DATATYPE_UINT32;
    propValue.value = &fieldValue;
    propValue.size = sizeof(uint32_t);
    ret = GmcYangSetNodeProperty(g_root_node, &propValue, GMC_YANG_PROPERTY_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 006.乐观事务内创建savepoint，vertex操作类型为none，属性操作类型分别为五原语操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_006)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint，预置数据
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    // create savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 none 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 007.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行merge、replace操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_007)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container
    CreateVertexLabel(g_stmt_async, g_valueVerifiPath, g_yangTreeTabelConfig);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 merge_insert 操作  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 1;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** savepoint2 对container执行 replace 操作  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    int newvalue = 2;
    testYangSetNodeProperty(g_root_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 008.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行merge、delete操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_008)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container
    CreateVertexLabel(g_stmt_async, g_valueVerifiPath, g_yangTreeTabelConfig);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 merge_insert 操作  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 1;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** savepoint2 对container执行 delete 操作  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 009.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行merge、remove操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_009)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container
    CreateVertexLabel(g_stmt_async, g_valueVerifiPath, g_yangTreeTabelConfig);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 merge_insert 操作  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 1;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** savepoint2 对container执行 remove 操作  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 010.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行replace、delete操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_010)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container
    CreateVertexLabel(g_stmt_async, g_valueVerifiPath, g_yangTreeTabelConfig);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 replace_insert 操作  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 1;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** savepoint2 对container执行 delete 操作  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 011.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行replace、remove操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_011)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container
    CreateVertexLabel(g_stmt_async, g_valueVerifiPath, g_yangTreeTabelConfig);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 replace_insert 操作  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 1;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** savepoint2 对container执行 remove 操作  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 012.表json中整型定义range约束，String定义length，乐观事务内创建两个不同的savepoint，分别执行delete、remove操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_012)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container
    CreateVertexLabel(g_stmt_async, g_valueVerifiPath, g_yangTreeTabelConfig);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** 预置数据  *******************/
    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 1;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** savepoint1 对container执行 delete 操作  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** savepoint2 对container执行 remove 操作  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 准备批操作
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_graphConValueVerifiName, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 013.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；replace、查询diff数据
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_013)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 merge_insert 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, g_treeConConChildName, GMC_OPERATION_MERGE, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeMergeInsertBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 replace 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    int newvalue = 200;
    testYangSetNodeProperty(g_root_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, g_treeConConChildName, GMC_OPERATION_REPLACE_GRAPH, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeReplaceUpdateBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 014.乐观事务内创建两个不同的savepoint，分别执行replace、查询diff数据；delete、查询diff数据
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_014)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 replace_insert 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, g_treeConConChildName, GMC_OPERATION_REPLACE_GRAPH, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeMergeInsertBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 delete 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 015.乐观事务内创建两个不同的savepoint，分别执行delete、查询diff数据；remove、查询diff数据
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_015)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** 预置数据  *******************/
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, g_treeConConChildName, GMC_OPERATION_INSERT, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    /*************** savepoint1 对container执行 delete 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 remove 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveNotDataBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 016.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；delete、查询diff数据
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_016)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 merge_insert 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, g_treeConConChildName, GMC_OPERATION_MERGE, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeMergeInsertBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 delete 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 017.乐观事务内创建两个不同的savepoint，分别执行replace、查询diff数据；remove、查询diff数据
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_017)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 replace_insert 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, g_treeConConChildName, GMC_OPERATION_REPLACE_GRAPH, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeMergeInsertBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 remove 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 018.乐观事务内创建两个不同的savepoint，分别执行merge、查询diff数据；remove、查询diff数据
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_018)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对container执行 merge_insert 操作，查询diff数据  *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 设置 child 节点
    ret = GmcYangEditChildNode(g_root_node, g_treeConConChildName, GMC_OPERATION_MERGE, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    testYangSetNodeProperty(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffTreeMergeInsertBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    /*************** savepoint2 对container执行 remove 操作，查询diff数据  *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    DiffBatchExecute(batch, 1);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch, expectDiffRemoveBase, userData);

    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 019.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_FIRST、GMC_YANG_LIST_POSITION_LAST
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_019)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对list节点元素移动选择 GMC_YANG_LIST_POSITION_FIRST 支持 create 操作
     * *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的头部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 对list节点元素移动选择 GMC_YANG_LIST_POSITION_LAST 支持 replace 操作
     * *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的尾部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 020.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_FIRST、GMC_YANG_LIST_POSITION_REMAIN
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_020)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对list节点元素移动选择 GMC_YANG_LIST_POSITION_FIRST 支持 create 操作
     * *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的头部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 对list节点元素移动选择 GMC_YANG_LIST_POSITION_REMAIN 支持 replace 操作
     * *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 10; i < 20; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的当前位置插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_REMAIN, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 021.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_FIRST、GMC_YANG_LIST_POSITION_BEFORE
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_021)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对list节点元素移动选择 GMC_YANG_LIST_POSITION_FIRST 支持 create 操作
     * *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的头部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 对list节点元素移动选择 GMC_YANG_LIST_POSITION_BEFORE 支持 replace 操作
     * *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 0;
    GmcYangListLocatorT listProp2;
    GmcPropValueT refKey2;
    InitRefKeys(&refKey2, 1, &F0);

    for (uint32_t i = 1; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List字段值 0 前面插入）
        InitListProperty(&listProp2, GMC_YANG_LIST_POSITION_BEFORE, &refKey2);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp2);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 10, 10);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 022.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_FIRST、GMC_YANG_LIST_POSITION_AFTER
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_022)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对list节点元素移动选择 GMC_YANG_LIST_POSITION_FIRST 支持 create 操作
     * *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的头部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_FIRST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 对list节点元素移动选择 GMC_YANG_LIST_POSITION_AFTER 支持 replace 操作
     * *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 0;
    GmcYangListLocatorT listProp2;
    GmcPropValueT refKey2;
    InitRefKeys(&refKey2, 1, &F0);

    for (uint32_t i = 1; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List字段值 0 后面插入）
        InitListProperty(&listProp2, GMC_YANG_LIST_POSITION_AFTER, &refKey2);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp2);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 10, 10);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 023.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_LAST、GMC_YANG_LIST_POSITION_REMAIN
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_023)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对list节点元素移动选择 GMC_YANG_LIST_POSITION_LAST 支持 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的尾部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 对list节点元素移动选择 GMC_YANG_LIST_POSITION_REMAIN 支持 replace 操作
     * *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的当前位置插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_REMAIN, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 024.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_LAST、GMC_YANG_LIST_POSITION_BEFORE
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_024)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对list节点元素移动选择 GMC_YANG_LIST_POSITION_LAST 支持 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的尾部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 对list节点元素移动选择 GMC_YANG_LIST_POSITION_BEFORE 支持 replace 操作
     * *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 0;
    GmcYangListLocatorT listProp2;
    GmcPropValueT refKey2;
    InitRefKeys(&refKey2, 1, &F0);
    for (uint32_t i = 1; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List字段值 0 前面插入）
        InitListProperty(&listProp2, GMC_YANG_LIST_POSITION_BEFORE, &refKey2);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp2);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 10, 10);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 025.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_LAST、GMC_YANG_LIST_POSITION_AFTER
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_025)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对list节点元素移动选择 GMC_YANG_LIST_POSITION_LAST 支持 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的尾部插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_LAST, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 对list节点元素移动选择 GMC_YANG_LIST_POSITION_AFTER 支持 replace 操作
     * *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 0;
    GmcYangListLocatorT listProp2;
    GmcPropValueT refKey2;
    InitRefKeys(&refKey2, 1, &F0);

    for (uint32_t i = 1; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List字段值 0 后面插入）
        InitListProperty(&listProp2, GMC_YANG_LIST_POSITION_AFTER, &refKey2);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp2);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 10, 10);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 026.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_REMAIN、GMC_YANG_LIST_POSITION_BEFORE
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_026)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对list节点元素移动选择 GMC_YANG_LIST_POSITION_REMAIN 支持 create 操作
     * *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的当前位置插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_REMAIN, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 对list节点元素移动选择 GMC_YANG_LIST_POSITION_BEFORE 支持 replace 操作
     * *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 0;
    GmcYangListLocatorT listProp2;
    GmcPropValueT refKey2;
    InitRefKeys(&refKey2, 1, &F0);

    for (uint32_t i = 1; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List字段值 0 前面插入）
        InitListProperty(&listProp2, GMC_YANG_LIST_POSITION_BEFORE, &refKey2);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp2);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 10, 10);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 027.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_REMAIN、GMC_YANG_LIST_POSITION_AFTER
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_027)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 对list节点元素移动选择 GMC_YANG_LIST_POSITION_REMAIN 支持 create 操作
     * *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List的当前位置插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_REMAIN, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 对list节点元素移动选择 GMC_YANG_LIST_POSITION_AFTER 支持 replace 操作
     * *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 0;
    GmcYangListLocatorT listProp2;
    GmcPropValueT refKey2;
    InitRefKeys(&refKey2, 1, &F0);

    for (uint32_t i = 1; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List字段值 0 后面插入）
        InitListProperty(&listProp2, GMC_YANG_LIST_POSITION_AFTER, &refKey2);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp2);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 10, 10);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 028.container-list类型，乐观事务内创建两个不同的savepoint，list节点元素移动分别选择GMC_YANG_LIST_POSITION_BEFORE、GMC_YANG_LIST_POSITION_AFTER
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_028)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** 预置数据 *******************/
    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f0 = 0;
    GmcPropValueT refKey;
    InitRefKeys(&refKey, 1, &f0);
    GmcYangListLocatorT listProp;

    for (uint32_t i = 0; i < 10; i++) {
        // 设置child节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置child的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint1 对list节点元素移动选择 GMC_YANG_LIST_POSITION_BEFORE 支持 replace 操作
     * *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 1; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List字段值 0 前面插入）
        InitListProperty(&listProp, GMC_YANG_LIST_POSITION_BEFORE, &refKey);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 10, 10);

    /*************** savepoint2 对list节点元素移动选择 GMC_YANG_LIST_POSITION_AFTER 支持 replace 操作
     * *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t F0 = 0;
    GmcYangListLocatorT listProp2;
    GmcPropValueT refKey2;
    InitRefKeys(&refKey2, 1, &F0);

    for (uint32_t i = 1; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 赋值操作（在List字段值 0 后面插入）
        InitListProperty(&listProp2, GMC_YANG_LIST_POSITION_AFTER, &refKey2);
        ret = GmcYangSetListLocator(g_stmt_list, &listProp2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 资源回收
        UninitListProperty(&listProp2);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 10, 10);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 029.一个乐观事务内创建3个不同的savepoint，回滚第二个savepoint，SubTree查询
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_029)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 replace 操作 *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint3 delete 操作 *******************/
    // craete savepoint3
    CreateSavepoint(g_conn_async, g_savepointName3);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** 回滚savepoint2，SubTree查询 *******************/
    // 回滚savepoint2
    RollbackSavepoint(g_conn_async, g_savepointName2);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath = "SubtreeReplyJson/containernode_Listreply_001.json";

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 030.一个乐观事务内创建3个不同的savepoint，释放第三个savepoint，SubTree查询
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_030)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint2 replace 操作 *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint3 delete 操作 *******************/
    // craete savepoint3
    CreateSavepoint(g_conn_async, g_savepointName3);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** 释放savepoint3，SubTree查询 *******************/
    // 释放savepoint3
    ReleaseSavepoint(g_conn_async, g_savepointName3);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath = "SubtreeReplyJson/containernode_Listreply_002.json";

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 031.乐观事务内创建一个savepoint，写操作失败，回滚savepoint，再次执行六元语操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_031)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    /************** 创建一个savepoint，写操作失败 *************/
    // create savepoint，预置数据
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 delete 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 0, GMERR_DATA_EXCEPTION);

    /************** 回滚savepoint，再次执行六元语操作 *************/
    // 回滚savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 032.乐观事务内创建两个savepoint，第二个savepoint六元语操作失败，回滚第二个savepoint，再次执行六元语操作
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_032)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    // 创建乐观事务
    TransStart(g_conn_async);

    /********** savepoint1 create 操作 *********/
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 对属性分别执行五原语操作
    TreePropertyOperation(g_root_node);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /********** savepoint2 六元语操作失败 *********/
    // create savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetVertexProperty(g_stmt_root, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 0, GMERR_PRIMARY_KEY_VIOLATION);

    /********** 回滚savepoint2，再次执行六元语操作 *********/
    // 回滚savepoint2
    RollbackSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 replace 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    int newvalue = 200;
    testYangSetVertexProperty(g_stmt_root, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 033.创建两个乐观事务，每个事务分别创建一个savepoint
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_033)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    uint32_t fieldValue = 0;
    GmcBatchT *batch = NULL;

    // create container-container
    CreateYangTreeVertexLabel(g_stmt_async);

    /*************** 事务1内创建savepoint1 *******************/
    // 创建 乐观事务1
    TransStart(g_conn_async);

    // 在乐观事务1 内创建 savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    /*************** 事务2内创建savepoint2 *******************/
    // use namespace
    useNameSpace(g_stmt_async1, g_namespace);

    // 创建 乐观事务2
    TransStart(g_conn_async1);

    // 在乐观事务2 内创建 savepoint2
    CreateSavepoint(g_conn_async1, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async1, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对根节点执行 create 操作
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 200;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);

    // 提交事务1
    TransCommit(g_conn_async);

    // 提交事务2
    TransCommit(g_conn_async1, GMERR_RESTRICT_VIOLATION);
    TransRollback(g_conn_async1);

    AW_FUN_Log(LOG_STEP, "END");
}

// 034.乐观事务内创建两个同名savepoint，分别执行六元语操作，连续回滚savepoint、SubTree查询
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_034)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint1 replace 操作 *******************/
    // craete savepoint1 again
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** 回滚savepoint1，SubTree查询  *******************/
    // 回滚savepoint1
    RollbackSavepoint(g_conn_async, g_savepointName);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath = "SubtreeReplyJson/containernode_Listreply_003.json";

    /*************** 释放savepoint2，回滚savepoint1，SubTree查询  *******************/
    // 释放savepoint2
    ReleaseSavepoint(g_conn_async, g_savepointName);
    // 回滚savepoint1
    RollbackSavepoint(g_conn_async, g_savepointName);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath2 = "SubtreeReplyJson/containernode_Listreply_Null.json";

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 035.乐观事务内创建两个匿名savepoint，分别执行六元语操作，连续释放savepoint、SubTree查询
TEST_F(Savepoint_FunTr, Yang_030_FuncTree_035)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, NULL);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** savepoint1 replace 操作 *******************/
    // craete savepoint1 again
    CreateSavepoint(g_conn_async, NULL);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** 释放savepoint1，SubTree查询  *******************/
    // 释放savepoint1
    ReleaseSavepoint(g_conn_async, NULL);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath = "SubtreeReplyJson/containernode_Listreply_003.json";

    /*************** 释放savepoint1，SubTree查询  *******************/
    // 释放savepoint1
    ReleaseSavepoint(g_conn_async, NULL);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath2 = "SubtreeReplyJson/containernode_Listreply_Null.json";

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}

// 036.超长事务内创建savepoint，执行六元语操作，事务超时后SubTree查询
TEST_F(Savepoint_FunTr_01, Yang_030_FuncTree_036)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    // craete savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    // 构造事务超时
    usleep(10000000);

    // 回滚事务
    TransRollback(g_conn_async);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath = "SubtreeReplyJson/containernode_Listreply_Null.json";

    AW_FUN_Log(LOG_STEP, "END");
}

// 037.超长事务内创建两个savepoint，分别执行六元语操作，回滚第二个savepoint，再次执行六元语操作，事务超时后SubTree查询
TEST_F(Savepoint_FunTr_01, Yang_030_FuncTree_037)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    CreateYangTreeVertexLabel(g_stmt_async);

    // 开启乐观事务
    TransStart(g_conn_async);

    /*************** savepoint1 create 操作 *******************/
    // craete savepoint1
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        testYangSetNodePropertyWithoutF0(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    /*************** 创建savepoint2，执行 六元语操作失败 *******************/
    // craete savepoint2
    CreateSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 0, GMERR_PRIMARY_KEY_VIOLATION);

    /*************** 回滚savepoint2，再次执行 replace 操作 *******************/
    // 回滚savepoint2
    RollbackSavepoint(g_conn_async, g_savepointName2);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < 10; i++) {
        // 设置 child 节点
        ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list, &g_child_node);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置 child 节点的属性值
        fieldValue = i;
        testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
        int newvalue = i + 200;
        testYangSetNodePropertyWithoutF0(g_child_node, newvalue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 提交批处理
    BatchExecute(batch, 11, 11);

    // 构造事务超时
    usleep(10000000);

    // 回滚事务
    TransRollback(g_conn_async);

    // SubTree查询 -- 容器过滤  2022.9.20 迭代二暂不支持部分打散模式下SubTree
    const char *g_conNodeListReplyPath = "SubtreeReplyJson/containernode_Listreply_Null.json";

    // 提交事务
    TransCommit(g_conn_async);

    AW_FUN_Log(LOG_STEP, "END");
}
// 插入基础数据后提交事务，再次开启事务添加savepoint，编辑con_1/F0后获取diff，回滚到savepoint。编辑con_2/F0使得when校验时删除con_1
TEST_F(Savepoint_FunTr_01, Yang_030_FuncTree_038)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;
    GmcBatchT *batch1;

    // create container-list
    AsyncUserDataT data = {0};
    readJanssonFile("./schemafile/YangTree/con_list_vertex.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_yangTreeTabelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    readJanssonFile("./schemafile/YangTree/con_con_list_edge.gmjson", &g_edgeSchema);
    ASSERT_NE((void *)NULL, g_edgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeSchema, g_yangTreeTabelConfig, create_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ModelCheck(g_stmt_async, true);
    free(g_schema);
    free(g_edgeSchema);

    // 开启乐观事务
    TransStart(g_conn_async);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_2节点的值
    GmcNodeT *ConConChildNode = NULL;
    ret = GmcYangEditChildNode(g_root_node, "con_2", GMC_OPERATION_REPLACE_GRAPH, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetNodeProperty_PK(ConConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child-list 节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_1节点的值
    GmcNodeT *ListConChildNode = NULL;
    ret = GmcYangEditChildNode(g_child_node, "con_1", GMC_OPERATION_REPLACE_GRAPH, &ListConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodeProperty_PK(ListConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 2, 2);
    TransCommit(g_conn_async);
    // 再次开启乐观事务
    TransStart(g_conn_async);
    // craete savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新编辑con_1节点的值
    //  设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置 child-list 节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置 child 节点的属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_1节点的值
    ListConChildNode = NULL;
    ret = GmcYangEditChildNode(g_child_node, "con_1", GMC_OPERATION_REPLACE_GRAPH, &ListConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetNodeProperty_PK(ListConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交批处理
    DiffBatchExecute(batch1, 2);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch1, expectDiffTreeReplaceBase, userData);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 回滚savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);
    // 设置批处理
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 设置con_2节点的值
    ConConChildNode = NULL;
    ret = GmcYangEditChildNode(g_root_node, "con_2", GMC_OPERATION_MERGE, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 2;
    testYangSetNodeProperty_PK(ConConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    BatchExecute(batch1, 1, 1);
    DataCheck(g_stmt_root, true, GMC_YANG_VALIDATION_WHEN);
    TransCommit(g_conn_async);
    AW_FUN_Log(LOG_STEP, "END");
}
//修改配置项 compatibleV3=1 ，messageSecurityCheck=0后replace更新原有的pid数据，预期成功
TEST_F(Savepoint_FunTr_02, Yang_030_FuncTree_039)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0;
    GmcBatchT *batch;
    GmcBatchT *batch1;

    // create container
    AsyncUserDataT data = {0};
    readJanssonFile("./schemafile/YangTree/con_vertex.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_yangTreeTabelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(g_schema);

    // 开启乐观事务
    TransStart(g_conn_async);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "huawei-sacl:sacl", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    uint32_t valueID = 1;
    ret = testYangTreeSetField(
        g_root_node, GMC_DATATYPE_UINT32, &valueID, sizeof(uint32_t), "PID", GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 1, 1);
    TransCommit(g_conn_async);
    // 再次开启乐观事务
    TransStart(g_conn_async);

    // 设置批处理batch参数
    ret = testBatchPrepare(g_conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新编辑con_1节点的值
    //  设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "huawei-sacl:sacl", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    valueID = 2;
    ret = testYangTreeSetField(
        g_root_node, GMC_DATATYPE_UINT32, &valueID, sizeof(uint32_t), "PID", GMC_YANG_PROPERTY_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    BatchExecute(batch1, 1, 1);
    TransCommit(g_conn_async);
    AW_FUN_Log(LOG_STEP, "END");
}
// con_1/F0依赖con_2/F0，再次开启事务添加savepoint，编辑con_1/F0后获取diff，回滚到savepoint。must校验
TEST_F(Savepoint_FunTr_01, Yang_030_FuncTree_040)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;
    GmcBatchT *batch1;

    // create container-list
    AsyncUserDataT data = {0};
    readJanssonFile("./schemafile/YangTree/con_list_vertex.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ReplaceStr(g_schema, "\"type\":\"when\"", "\"type\":\"must\"");
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_yangTreeTabelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    readJanssonFile("./schemafile/YangTree/con_con_list_edge.gmjson", &g_edgeSchema);
    ASSERT_NE((void *)NULL, g_edgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeSchema, g_yangTreeTabelConfig, create_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ModelCheck(g_stmt_async, true);
    free(g_schema);
    free(g_edgeSchema);

    // 开启乐观事务
    TransStart(g_conn_async);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_2节点的值
    GmcNodeT *ConConChildNode = NULL;
    ret = GmcYangEditChildNode(g_root_node, "con_2", GMC_OPERATION_REPLACE_GRAPH, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetNodeProperty_PK(ConConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child-list 节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_1节点的值
    GmcNodeT *ListConChildNode = NULL;
    ret = GmcYangEditChildNode(g_child_node, "con_1", GMC_OPERATION_REPLACE_GRAPH, &ListConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodeProperty_PK(ListConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 2, 2);
    TransCommit(g_conn_async);
    // 再次开启乐观事务
    TransStart(g_conn_async);
    // craete savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新编辑con_1节点的值
    //  设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置 child-list 节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置 child 节点的属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_1节点的值
    ListConChildNode = NULL;
    ret = GmcYangEditChildNode(g_child_node, "con_1", GMC_OPERATION_REPLACE_GRAPH, &ListConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetNodeProperty_PK(ListConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交批处理
    DiffBatchExecute(batch1, 2);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch1, expectDiffTreeReplaceBase, userData);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 回滚savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);
    // 设置批处理
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 设置con_2节点的值
    ConConChildNode = NULL;
    ret = GmcYangEditChildNode(g_root_node, "con_2", GMC_OPERATION_MERGE, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 2;
    testYangSetNodeProperty_PK(ConConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    BatchExecute(batch1, 1, 1);
    DataCheck(g_stmt_root, false, GMC_YANG_VALIDATION_MUST);
    TransCommit(g_conn_async);
    AW_FUN_Log(LOG_STEP, "END");
}
// con_1/F0依赖con_2/F0，再次开启事务添加savepoint，编辑con_1/F0后获取diff，回滚到savepoint。mandatory校验
TEST_F(Savepoint_FunTr_01, Yang_030_FuncTree_041)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;
    GmcBatchT *batch1;

    // create container-list
    AsyncUserDataT data = {0};
    readJanssonFile("./schemafile/YangTree/con_list_vertex.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_yangTreeTabelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    readJanssonFile("./schemafile/YangTree/con_con_list_edge.gmjson", &g_edgeSchema);
    ASSERT_NE((void *)NULL, g_edgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeSchema, g_yangTreeTabelConfig, create_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ModelCheck(g_stmt_async, true);
    free(g_schema);
    free(g_edgeSchema);

    // 开启乐观事务
    TransStart(g_conn_async);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_2节点的值
    GmcNodeT *ConConChildNode = NULL;
    ret = GmcYangEditChildNode(g_root_node, "con_2", GMC_OPERATION_REPLACE_GRAPH, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetNodeProperty_PK(ConConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child-list 节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_1节点的值
    GmcNodeT *ListConChildNode = NULL;
    ret = GmcYangEditChildNode(g_child_node, "con_1", GMC_OPERATION_REPLACE_GRAPH, &ListConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodeProperty_PK(ListConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 2, 2);
    TransCommit(g_conn_async);
    // 再次开启乐观事务
    TransStart(g_conn_async);
    // craete savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新编辑con_1节点的值
    //  设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置 child-list 节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置 child 节点的属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_1节点的值
    ListConChildNode = NULL;
    ret = GmcYangEditChildNode(g_child_node, "con_1", GMC_OPERATION_REPLACE_GRAPH, &ListConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetNodeProperty_PK(ListConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交批处理
    DiffBatchExecute(batch1, 2);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch1, expectDiffTreeReplaceBase, userData);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 回滚savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);
    // 设置批处理
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 设置con_2节点的值
    ConConChildNode = NULL;
    ret = GmcYangEditChildNode(g_root_node, "con_2", GMC_OPERATION_MERGE, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 2;
    testYangSetNodeProperty_PK(ConConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    BatchExecute(batch1, 1, 1);
    DataCheck(g_stmt_root, true, GMC_YANG_VALIDATION_MANDATORY);
    TransCommit(g_conn_async);
    AW_FUN_Log(LOG_STEP, "END");
}
// con_1/F0依赖con_2/F0，再次开启事务添加savepoint，编辑con_1/F0后获取diff，回滚到savepoint。leafref校验
TEST_F(Savepoint_FunTr_01, Yang_030_FuncTree_042)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;
    GmcBatchT *batch1;

    // create container-list
    AsyncUserDataT data = {0};
    readJanssonFile("./schemafile/YangTree/con_list_vertex.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    const char *findstr = R"({"type":"when", "formula":"/root/con_2/F0 = 1"})";
    const char *replacestr = R"({"type":"leafref", "formula":"/root/con_2/F0"})";
    ReplaceStr(g_schema, findstr, replacestr);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_yangTreeTabelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    readJanssonFile("./schemafile/YangTree/con_con_list_edge.gmjson", &g_edgeSchema);
    ASSERT_NE((void *)NULL, g_edgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeSchema, g_yangTreeTabelConfig, create_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ModelCheck(g_stmt_async, true);
    free(g_schema);
    free(g_edgeSchema);

    // 开启乐观事务
    TransStart(g_conn_async);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_2节点的值
    GmcNodeT *ConConChildNode = NULL;
    ret = GmcYangEditChildNode(g_root_node, "con_2", GMC_OPERATION_REPLACE_GRAPH, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetNodeProperty_PK(ConConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child-list 节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_1节点的值
    GmcNodeT *ListConChildNode = NULL;
    ret = GmcYangEditChildNode(g_child_node, "con_1", GMC_OPERATION_REPLACE_GRAPH, &ListConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodeProperty_PK(ListConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 2, 2);
    TransCommit(g_conn_async);
    // 再次开启乐观事务
    TransStart(g_conn_async);
    // craete savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新编辑con_1节点的值
    //  设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置 child-list 节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置 child 节点的属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_1节点的值
    ListConChildNode = NULL;
    ret = GmcYangEditChildNode(g_child_node, "con_1", GMC_OPERATION_REPLACE_GRAPH, &ListConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetNodeProperty_PK(ListConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交批处理
    DiffBatchExecute(batch1, 2);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch1, expectDiffTreeReplaceBase, userData);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 回滚savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);
    // 设置批处理
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 设置con_2节点的值
    ConConChildNode = NULL;
    ret = GmcYangEditChildNode(g_root_node, "con_2", GMC_OPERATION_MERGE, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 2;
    testYangSetNodeProperty_PK(ConConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    BatchExecute(batch1, 1, 1);
    DataCheck(g_stmt_root, false, GMC_YANG_VALIDATION_LEAFREF);
    TransCommit(g_conn_async);
    AW_FUN_Log(LOG_STEP, "END");
}
// con_1/F0依赖con_2/F0，再次开启事务添加savepoint，编辑con_1/F0后获取diff，回滚到savepoint。min-max校验
TEST_F(Savepoint_FunTr_01, Yang_030_FuncTree_043)
{
    AW_FUN_Log(LOG_STEP, "START");

    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;
    GmcBatchT *batch1;

    // create container-list
    AsyncUserDataT data = {0};
    readJanssonFile("./schemafile/YangTree/con_list_vertex.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    g_schema = (char *)realloc(g_schema,1.5*strlen(g_schema));
    const char *replacestr = R"("name":"list_1",
            "min-elements":2,)";
    ReplaceStr(g_schema, "\"name\":\"list_1\",", replacestr);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_yangTreeTabelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    readJanssonFile("./schemafile/YangTree/con_con_list_edge.gmjson", &g_edgeSchema);
    ASSERT_NE((void *)NULL, g_edgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeSchema, g_yangTreeTabelConfig, create_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ModelCheck(g_stmt_async, true);
    free(g_schema);
    free(g_edgeSchema);

    // 开启乐观事务
    TransStart(g_conn_async);

    // 设置批处理
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_2节点的值
    GmcNodeT *ConConChildNode = NULL;
    ret = GmcYangEditChildNode(g_root_node, "con_2", GMC_OPERATION_REPLACE_GRAPH, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetNodeProperty_PK(ConConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child-list 节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置 child 节点的属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_1节点的值
    GmcNodeT *ListConChildNode = NULL;
    ret = GmcYangEditChildNode(g_child_node, "con_1", GMC_OPERATION_REPLACE_GRAPH, &ListConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 10;
    testYangSetNodeProperty_PK(ListConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    BatchExecute(batch, 2, 2);
    TransCommit(g_conn_async);
    // 再次开启乐观事务
    TransStart(g_conn_async);
    // craete savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 重新编辑con_1节点的值
    //  设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置 child-list 节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_list, g_treeConListRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, g_stmt_root, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list, &g_child_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置 child 节点的属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_child_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    // 设置con_1节点的值
    ListConChildNode = NULL;
    ret = GmcYangEditChildNode(g_child_node, "con_1", GMC_OPERATION_REPLACE_GRAPH, &ListConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 1;
    testYangSetNodeProperty_PK(ListConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_list);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 提交批处理
    DiffBatchExecute(batch1, 2);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmt_con, batch1, expectDiffTreeReplaceBase, userData);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    // 回滚savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);
    // 设置批处理
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 设置con_2节点的值
    ConConChildNode = NULL;
    ret = GmcYangEditChildNode(g_root_node, "con_2", GMC_OPERATION_MERGE, &ConConChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 2;
    testYangSetNodeProperty_PK(ConConChildNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch1, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    BatchExecute(batch1, 1, 1);
    DataCheck(g_stmt_root, true, GMC_YANG_VALIDATION_MIN);
    TransCommit(g_conn_async);
    AW_FUN_Log(LOG_STEP, "END");
}
//开启事务后先开diff，回滚后再关diff，预期报错
TEST_F(Savepoint_FunTr_01, Yang_030_FuncTree_044)
{
    AW_FUN_Log(LOG_STEP, "START");
    AddWhiteList(GMERR_INVALID_OPTION);
    int ret = 0, fieldValue = 0;
    GmcBatchT *batch;

    // create container-list
    AsyncUserDataT data = {0};
    readJanssonFile("./schemafile/YangTree/con_list_vertex.gmjson", &g_schema);
    ASSERT_NE((void *)NULL, g_schema);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, g_yangTreeTabelConfig, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    readJanssonFile("./schemafile/YangTree/con_con_list_edge.gmjson", &g_edgeSchema);
    ASSERT_NE((void *)NULL, g_edgeSchema);
    ret = GmcCreateEdgeLabelAsync(g_stmt_async, g_edgeSchema, g_yangTreeTabelConfig, create_edge_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ModelCheck(g_stmt_async, true);
    free(g_schema);
    free(g_edgeSchema);

    // 开启乐观事务
    TransStart(g_conn_async);
    CreateSavepoint(g_conn_async, g_savepointName);

    // 设置批处理，开diff
    ret = testBatchPrepareAndSetDiff(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_root_node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根的属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_root_node, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    BatchExecute(batch, 1, 1);
    // 回滚savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);
     // 设置批处理，关diff
    ret = testBatchPrepare(g_conn_async, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, g_treeConConRootName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交批处理
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_OPTION, userData.status);
    GmcBatchDestroy(batch);
    memset(&userData, 0, sizeof(AsyncUserDataT));
    TransCommit(g_conn_async);
    AW_FUN_Log(LOG_STEP, "END");
}
