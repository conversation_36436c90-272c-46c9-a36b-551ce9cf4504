/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: 030_TransSavepoint
 * Author: hanyang
 * Create: 2023-4-18
 */
#include "Reinforce.h"
#include "ReinforceDiff.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async1 = NULL;
GmcStmtT *g_stmt_async1 = NULL;
GmcConnT *g_conn_async_big = NULL;
GmcStmtT *g_stmt_async_big = NULL;
GmcStmtT *g_stmt_root = NULL;
GmcStmtT *g_stmt_list[10] = {0};
GmcNodeT *g_rootNode = NULL;
GmcNodeT *g_childNode[40] = {0};

class Reinforce : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void Reinforce::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/start.sh -f");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createEpollOneThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void Reinforce::TearDownTestCase()
{
        closeEpollOneThread();
    GmcDetachAllShmSeg();
    testEnvClean();
}

void Reinforce::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = TestYangGmcConnectWithEpollOneThread(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        YangConnOptionT connOptionsBig = {0};
        connOptionsBig.isLobConn = true;
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async_big, &g_stmt_async_big, GMC_CONN_TYPE_ASYNC, &connOptionsBig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async1, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmt_async_big, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建Vertex表
    TestCreateLabelAll(g_stmt_async);

    // 同步连接也使用namespace
    ret = GmcUseNamespace(g_stmt, g_namespace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);

    char errorMsg4[128] = {};
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg4);

    // 用例里的交互式事务持有DDL锁，导致服务端的后台缩容(memCompactEnable)、内存归还(enableReleaseDevice)的任务拿不到锁
    char errorMsg5[128] = {};
    (void)snprintf(errorMsg5, sizeof(errorMsg5), "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg5);

    AW_CHECK_LOG_BEGIN();
}

void Reinforce::TearDown()
{
    int ret;
    int i = 0;
    AsyncUserDataT data = {0};
    AW_CHECK_LOG_END();

    // 删除Vertex和Edge表
    ret = GmcClearNamespaceAsync(g_stmt_async, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }
    testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testGmcDisconnect(g_conn_async1, g_stmt_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testGmcDisconnect(g_conn_async_big, g_stmt_async_big);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async = NULL;
    g_stmt_async = NULL;
    g_stmt_root = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
}

/* container--list Yang模型示例
                           root(con)
                              |
    ┌------------┬------------┼------------┬------------┐
    |            |            |            |            |
 list_06      list_07      list_08      list_09      list_10
*/
/*****************************************************************************
 Description  : 001.开启事务，连续创建10个重名savepoint，中间没有dml操作，没有释放，
                查询DFX视图SAVE_POINT_NUM数量，提交事务；循环10次
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;

    for (i = 0; i < cycleNum; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 002.开启事务，连续创建10个重名savepoint，中间没有dml操作，没有释放，
                查询DFX视图SAVE_POINT_NUM数量，回滚事务；循环10次
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;

    for (i = 0; i < cycleNum; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 回滚事务
        ret = TestTransRollBackAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 003.开启事务，连续创建10个重名savepoint，中间没有dml操作，全部释放，
                查询DFX视图SAVE_POINT_NUM数量，回滚事务；循环10次
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;

    for (i = 0; i < cycleNum; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 释放savepoint
        for (j = 0; j < cycleNum; j++) {
            ReleaseSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = 0;
        TestQuerySPNum(expectSPNum);

        // 回滚事务
        ret = TestTransRollBackAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 004.开启事务，创建savepoint，没有dml操作，释放后再次创建重名savepoint，
                查询DFX视图SAVE_POINT_NUM数量，回滚事务；循环10次
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;

    for (i = 0; i < cycleNum; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 释放savepoint
        for (j = 0; j < cycleNum; j++) {
            ReleaseSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = 0;
        TestQuerySPNum(expectSPNum);

        // 再次创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 回滚事务
        ret = TestTransRollBackAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 005.开启事务，创建savepoint，没有dml操作，回滚释放后再次创建重名savepoint，
                查询DFX视图SAVE_POINT_NUM数量，回滚事务；循环10次
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;

    for (i = 0; i < cycleNum; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 回滚事务
        ret = TestTransRollBackAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 再次创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 回滚事务
        ret = TestTransRollBackAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 006.开启事务，写入2K条数据，连续创建10个重名savepoint，中间没有dml操作，
                没有释放，查询DFX视图SAVE_POINT_NUM数量，提交事务；循环10次，需要全量删除表数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    for (i = 0; i < cycleNum; i++) {
        TestInsertRoot(g_conn_async, "root", fieldValue);

        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // isnert
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

        // 创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        /***************************删除数据***********************************/
        TestRemoveRoot(g_conn_async, "root");
    }
}

/*****************************************************************************
 Description  : 007.开启事务，写入2K条数据，连续创建10个重名savepoint，中间没有dml操作，
                全部回滚并释放，查询DFX视图SAVE_POINT_NUM数量，回滚事务；循环10次，需要全量删除表数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    for (i = 0; i < cycleNum; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // isnert
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

        // 创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);
        }

        // 回滚savepoint
        for (j = 0; j < cycleNum; j++) {
            RollbackSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 释放savepoint
        for (j = 0; j < cycleNum; j++) {
            ReleaseSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = 0;
        TestQuerySPNum(expectSPNum);

        // 回滚事务
        ret = TestTransRollBackAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 Description  : 008.开启事务，写入2K条数据，连续创建10个重名savepoint，中间没有dml操作，
                释放回滚操作11次，查询DFX视图SAVE_POINT_NUM数量，回滚事务；循环10次，需要全量删除表数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    for (i = 0; i < cycleNum; i++) {
        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // isnert
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

        // 创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);
        }

        // 回滚savepoint
        for (j = 0; j < cycleNum; j++) {
            RollbackSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 释放savepoint
        for (j = 0; j < cycleNum; j++) {
            ReleaseSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = 0;
        TestQuerySPNum(expectSPNum);

        // 再次回滚
        RollbackSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);

        // 再次释放
        ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);

        // 回滚事务
        ret = TestTransRollBackAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 009.开启事务，写入2K条数据，连续创建10个重名savepoint，中间没有dml操作，
                释放回滚操作11次，查询DFX视图SAVE_POINT_NUM数量，提交事务；循环10次，需要全量删除表数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    for (i = 0; i < cycleNum; i++) {
        AW_FUN_Log(LOG_DEBUG_ALL, "start loop:%d", i);
        TestInsertRoot(g_conn_async, "root", fieldValue);

        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // isnert
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

        // 创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);
        }

        // 回滚savepoint
        for (j = 0; j < cycleNum; j++) {
            RollbackSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 释放savepoint
        for (j = 0; j < cycleNum; j++) {
            ReleaseSavepoint(g_conn_async, g_savepointName);
        }

        // 查询视图
        expectSPNum = 0;
        TestQuerySPNum(expectSPNum);

        // 再次回滚
        RollbackSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);

        // 再次释放
        ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // subtree查询
        TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_009");

        /***************************删除数据***********************************/
        TestRemoveRoot(g_conn_async, "root");
        AW_FUN_Log(LOG_DEBUG_ALL, "end loop:%d", i);
    }
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 010.开启事务，连续创建10个重名savepoint，savepoint之间dml操作同一张表10条数据，提交事务；
                循环10次，需要全量删除表数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    for (i = 0; i < cycleNum; i++) {
        TestInsertRoot(g_conn_async, "root", fieldValue);

        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);

            // isnert
            initValue = j * cycleNum;
            TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        /***************************删除数据***********************************/
        TestRemoveRoot(g_conn_async, "root");
    }
}

/*****************************************************************************
 Description  : 011.开启事务，连续创建10个重名savepoint，savepoint之间dml操作2张表各10条数据，提交事务；
                循环10次，需要全量删除表数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    for (i = 0; i < cycleNum; i++) {
        TestInsertRoot(g_conn_async, "root", fieldValue);

        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建savepoint
        for (j = 0; j < cycleNum; j++) {
            CreateSavepoint(g_conn_async, g_savepointName);

            // isnert
            initValue = j * cycleNum;
            TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
            TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
        }

        // 查询视图
        expectSPNum = cycleNum;
        TestQuerySPNum(expectSPNum);

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        /***************************删除数据***********************************/
        TestRemoveRoot(g_conn_async, "root");
    }
}

/*****************************************************************************
 Description  : 012.开启事务，创建savepoint，dml操作2张表各10条数据，释放再创建重名savepoint，提交事务；
                循环10次，需要全量删除表数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    for (i = 0; i < cycleNum; i++) {
        TestInsertRoot(g_conn_async, "root", fieldValue);

        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建savepoint
        CreateSavepoint(g_conn_async, g_savepointName);

        // isnert
        initValue = 0;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
        TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);

        // 查询视图
        expectSPNum = 1;
        TestQuerySPNum(expectSPNum);

        // 释放savepoint
        ReleaseSavepoint(g_conn_async, g_savepointName);

        // 查询视图
        expectSPNum = 0;
        TestQuerySPNum(expectSPNum);

        // 再创建savepoint
        CreateSavepoint(g_conn_async, g_savepointName);

        // 查询视图
        expectSPNum = 1;
        TestQuerySPNum(expectSPNum);

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        /***************************删除数据***********************************/
        TestRemoveRoot(g_conn_async, "root");
    }
}

/*****************************************************************************
 Description  : 013.开启事务，创建savepoint，dml操作2张表各10条数据，回滚再创建重名savepoint，提交事务；
                循环10次，需要全量删除表数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    for (i = 0; i < cycleNum; i++) {
        TestInsertRoot(g_conn_async, "root", fieldValue);

        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 创建savepoint
        CreateSavepoint(g_conn_async, g_savepointName);

        // isnert
        initValue = 0;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
        TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);

        // 查询视图
        expectSPNum = 1;
        TestQuerySPNum(expectSPNum);

        // 回滚savepoint
        RollbackSavepoint(g_conn_async, g_savepointName);

        // 查询视图
        expectSPNum = 1;
        TestQuerySPNum(expectSPNum);

        // 再创建savepoint
        CreateSavepoint(g_conn_async, g_savepointName);

        // 查询视图
        expectSPNum = 2;
        TestQuerySPNum(expectSPNum);

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        /***************************删除数据***********************************/
        TestRemoveRoot(g_conn_async, "root");
    }
}

/*****************************************************************************
 Description  : 014.开启事务，连续创建10个重名savepoint，savepoint之间dml操作2张表各10条数据，
                反复回滚释放11次，查询DIFF,提交事务；循环10次，需要全量删除表数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建savepoint
    for (j = 0; j < cycleNum; j++) {
        CreateSavepoint(g_conn_async, g_savepointName);

        // isnert
        initValue = j * cycleNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
        TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
    }

    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014);

    // 回滚和释放savepoint 10次, 查询diff
    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_001);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_002);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_003);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_004);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_005);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_006);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_007);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_008);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_009);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_010);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 再次回滚释放
    RollbackSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);
    ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除数据***********************************/
    TestRemoveRoot(g_conn_async, "root");

    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 015.开启事务，连续创建10个重名savepoint，savepoint之间dml操作2张表各10条数据，
                反复释放回滚11次，查询DIFF,回滚事务；循环10次，需要全量删除表数据。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建savepoint
    for (j = 0; j < cycleNum; j++) {
        CreateSavepoint(g_conn_async, g_savepointName);

        initValue = j * cycleNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
        TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
    }

    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014);

    // 回滚和释放savepoint 10次, 查询diff
    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_001);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_002);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_003);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_004);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_005);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_006);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_007);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_008);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_009);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_010);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 再次回滚释放
    RollbackSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);
    ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除数据***********************************/
    TestRemoveRoot(g_conn_async, "root");

    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 016.开启事务，多次创建savepoint，名称非法，失败；执行dml操作，查询DIFF，提交事务，成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建savepoint, 名称非法
    for (j = 0; j < cycleNum; j++) {
        CreateSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);

        initValue = j * cycleNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
        TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
    }

    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014);

    // 回滚和释放名称非法的sp 查询diff
    RollbackSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014);
    ReleaseSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);

    // 再次回滚释放不存在的sp
    RollbackSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);
    ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /***************************删除数据***********************************/
    TestRemoveRoot(g_conn_async, "root");

    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_INVALID_NAME);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 017.开启事务，多次创建savepoint，名称非法，失败；创建合法名称savepoint，
                执行dml操作10条数据，查询DIFF，创建重名savepoint，执行dml操作10条数据，
                查询DIFF，回滚savepoint，回滚事务，成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建savepoint, 名称非法
    for (j = 0; j < cycleNum; j++) {
        CreateSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);
    }

    // 创建savepoint DML操作
    CreateSavepoint(g_conn_async, g_savepointName);
    initValue = 0;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_009);

    CreateSavepoint(g_conn_async, g_savepointName);
    initValue = insertNum;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_008);

    // 回滚和释放名称非法的sp 查询diff
    RollbackSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_008);
    ReleaseSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);

    // 回滚释放存在的sp
    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff014_009);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_NAME);
}

/*****************************************************************************
 Description  : 018.事务处于ABORT状态后再次创建重名savepoint成功
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 开启事务，dml操作A表10条数据，违反唯一性约束，查询DIFF，创建savepointA失败，回滚和释放操作也失败；
    // 提交事务，失败；查询diff失败，回滚事务，成功。
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    initValue = 0;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

    // insert fail
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff018_001, GMERR_TRANSACTION_ROLLBACK);

    // 创建、回滚、释放savepoint
    CreateSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);
    ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // 提交事务失败
    AsyncUserDataT data = {0};
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff018_001, GMERR_TRANSACTION_ROLLBACK);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 开启事务，执行dml操作10条数据，查询DIFF，创建savepointA；
    // dml操作B表10条数据，违反唯一性约束，创建重名savepointA，查询DIFF失败，失败；
    // 回滚savepointA，成功，释放savepointA成功。查询DIFF成功。提交事务成功。
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // isnert
    initValue = 100;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff018_002);

    // 创建savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // insert fail
    initValue = 0;
    insertNum = 5;
    TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
    TestInsertListChildFail(g_conn_async, "root", "list_7", initValue, insertNum);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff018_002, GMERR_TRANSACTION_ROLLBACK);

    // 创建同名savepoint
    CreateSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // 回滚、释放savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff018_002);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 019.事务处于ABORT状态后重新ACTIVE，再次创建重名savepoint成功
    开启事务，创建savepointA，成功；执行dml操作10条数据，查询DIFF，创建重名savepointA；
    dml操作B表10条数据，违反唯一性约束，创建重名savepointA，查询DIFF失败，失败；
    回滚savepointA，成功，释放savepointA成功。查询DIFF成功。
    创建重名savepointA，成功；查询DIFF成功。释放savepointA成功。查询DIFF成功。SAVE_POINT_NUM数量=0
    提交事务，成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    initValue = 100;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff019_001);

    // 创建savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // insert fail
    initValue = 0;
    insertNum = 5;
    TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
    TestInsertListChildFail(g_conn_async, "root", "list_7", initValue, insertNum);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff019_001, GMERR_TRANSACTION_ROLLBACK);

    // 创建同名savepoint
    CreateSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // 回滚、释放savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff019_001);

    // 创建同名savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff019_001);

    // 释放savepoint
    ReleaseSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff019_001);

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
}

/*****************************************************************************
 Description  : 020.事务处于ABORT状态后重新ACTIVE（提交事务时失败），再次创建重名savepoint成功
    开启事务，创建savepointA，成功；执行dml操作10条数据，查询DIFF，创建重名savepointA；
    dml操作B表10条数据，违反唯一性约束，事务处于ABORT状态，创建重名savepointA，查询DIFF失败，提交事务，都失败；
    回滚savepointA，成功，释放savepointA成功。查询DIFF成功。
    创建重名savepointA；dml操作B表10条数据，违反唯一性约束，创建savepointB失败，回滚和释放savepointB都失败，操作两次；
    创建重名savepointA，查询DIFF失败；回滚savepointA，成功；查询DIFF成功。释放savepointA，提交事务成功。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    initValue = 100;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff019_001);

    // 创建savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // insert fail
    initValue = 0;
    insertNum = 5;
    TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
    TestInsertListChildFail(g_conn_async, "root", "list_7", initValue, insertNum);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff019_001, GMERR_TRANSACTION_ROLLBACK);

    // 创建同名savepoint
    CreateSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // 提交事务失败
    AsyncUserDataT data = {0};
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);

    // 回滚、释放savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff019_001);

    // 创建同名savepoint
    CreateSavepoint(g_conn_async, g_savepointName);

    // insert fail
    initValue = 0;
    insertNum = 5;
    TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
    TestInsertListChildFail(g_conn_async, "root", "list_7", initValue, insertNum);

    // 创建不同名savepoint
    CreateSavepoint(g_conn_async, g_savepointName1, GMERR_TRANSACTION_ROLLBACK);

    // 回滚、释放savepoint
    RollbackSavepoint(g_conn_async, g_savepointName1, GMERR_NO_DATA);
    ReleaseSavepoint(g_conn_async, g_savepointName1, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async, g_savepointName1, GMERR_NO_DATA);
    ReleaseSavepoint(g_conn_async, g_savepointName1, GMERR_TRANSACTION_ROLLBACK);

    // 创建同名savepoint
    CreateSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff019_001, GMERR_TRANSACTION_ROLLBACK);

    // 回滚savepoint
    RollbackSavepoint(g_conn_async, g_savepointName);

    // 查询diff
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff019_001);

    // 释放savepoint
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 021.循环创建释放savepoint（回滚不存在的savepoint）
    开启事务，创建释放匿名savepoint，循环1W次，查看视图，事务状态ACTIVE，SAVE_POINT_NUM数量=0，undo内存无明显增长；
    创建释放savepointA1 ... savepointA10000（1W次），查看视图，事务状态ACTIVE，SAVE_POINT_NUM数量=0，undo内存无明显增长；
    创建释放重名savepointA，循环1W次，查看视图，事务状态ACTIVE，SAVE_POINT_NUM数量=0，undo内存无明显增长；
    创建重名savepointA，执行dml操作，提交事务，成功。回滚savepointA，报错。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = RECORD_NUM_003;
    uint32_t expectSPNum = 0;
    uint64_t undoSizeBefore = 0;
    uint64_t undoSizeAfter = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建释放匿名savepoint，循环1W次
    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeBefore);
    for (i = 0; i < cycleNum; i++) {
        CreateSavepoint(g_conn_async, NULL);
        ReleaseSavepoint(g_conn_async, NULL);
    }

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeAfter);
    AW_FUN_Log(LOG_INFO, "TOTAL_UNDOLOG_SIZE before is %d, TOTAL_UNDOLOG_SIZE after is %d.",
        undoSizeBefore, undoSizeAfter);
    AW_MACRO_EXPECT_EQ_INT(undoSizeBefore, undoSizeAfter);

    // 创建释放savepointA1 ... savepointA10000，循环1W次
    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeBefore);
    for (i = 0; i < cycleNum; i++) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async, spName);
        ReleaseSavepoint(g_conn_async, spName);
    }

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeAfter);
    AW_FUN_Log(LOG_INFO, "TOTAL_UNDOLOG_SIZE before is %d, TOTAL_UNDOLOG_SIZE after is %d.",
        undoSizeBefore, undoSizeAfter);
    AW_MACRO_EXPECT_EQ_INT(undoSizeBefore, undoSizeAfter);

    // 创建释放重名savepointA，循环1W次
    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeBefore);
    for (i = 0; i < cycleNum; i++) {
        CreateSavepoint(g_conn_async, g_savepointName);
        ReleaseSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeAfter);
    AW_FUN_Log(LOG_INFO, "TOTAL_UNDOLOG_SIZE before is %d, TOTAL_UNDOLOG_SIZE after is %d.",
        undoSizeBefore, undoSizeAfter);
    EXPECT_GE(undoSizeBefore, undoSizeAfter);

    // 创建重名savepointA，执行dml操作，提交事务，成功。
    CreateSavepoint(g_conn_async, g_savepointName);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

    CreateSavepoint(g_conn_async, g_savepointName);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚savepointA，报错。
    ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_NO_ACTIVE_TRANSACTION);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 022.循环创建释放savepoint（释放不存在的savepoint）
    开启事务，创建释放匿名savepoint，savepointA，savepointAN，查看视图，事务状态ACTIVE，SAVE_POINT_NUM数量=0，
    undo内存无明显增长；循环N次（N=1W）；
    创建匿名savepoint，savepointA，savepointAN，依次释放匿名savepoint，savepointA，savepointAN，
    查看视图，事务状态ACTIVE，SAVE_POINT_NUM数量=0，undo内存无明显增长；循环N次（N=1W）；
    创建重名savepointA，执行dml操作，提交事务，成功。释放savepointA，报错。SAVE_POINT_NUM数量=0
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 1024; // 2023.06.05 sp上限为1024
    uint32_t expectSPNum = 0;
    uint64_t undoSizeBefore = 0;
    uint64_t undoSizeAfter = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建释放匿名savepoint，savepointA，savepointAN, 循环N次（N=1W）
    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeBefore);
    for (i = 0; i < cycleNum; i++) {
        CreateSavepoint(g_conn_async, NULL);
        ReleaseSavepoint(g_conn_async, NULL);

        CreateSavepoint(g_conn_async, g_savepointName);
        ReleaseSavepoint(g_conn_async, g_savepointName);

        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async, spName);
        ReleaseSavepoint(g_conn_async, spName);
    }

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeAfter);
    AW_FUN_Log(LOG_INFO, "TOTAL_UNDOLOG_SIZE before is %d, TOTAL_UNDOLOG_SIZE after is %d.",
        undoSizeBefore, undoSizeAfter);
    EXPECT_GE(undoSizeBefore, undoSizeAfter);

    // 创建匿名savepoint，savepointA，savepointAN，依次释放匿名savepoint，savepointA，savepointAN，，循环1W次
    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeBefore);
    for (i = 0; i < cycleNum / 3; i++) {
        CreateSavepoint(g_conn_async, NULL);

        CreateSavepoint(g_conn_async, g_savepointName);

        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async, spName);
    }

    for (i = 0; i < cycleNum / 3; i++) {
        ReleaseSavepoint(g_conn_async, NULL);

        ReleaseSavepoint(g_conn_async, g_savepointName);

        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        ReleaseSavepoint(g_conn_async, spName);
    }

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeAfter);
    AW_FUN_Log(LOG_INFO, "TOTAL_UNDOLOG_SIZE before is %d, TOTAL_UNDOLOG_SIZE after is %d.",
        undoSizeBefore, undoSizeAfter);
    EXPECT_GE(undoSizeBefore, undoSizeAfter);

    // 创建重名savepointA，执行dml操作，提交事务，成功。
    CreateSavepoint(g_conn_async, g_savepointName);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

    CreateSavepoint(g_conn_async, g_savepointName);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚savepointA，报错。
    ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_NO_ACTIVE_TRANSACTION);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建1025个不同名sp，查询视图, 只显示最近1024个
    for (i = 0; i < 1024; i++) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async, spName);
    }

    // 查询视图
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);

    ret = executeCommand(command, "SAVE_POINT_NAME: sp1023", "SAVE_POINT_NAME: sp1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 023.循环创建释放savepoint（包括回滚和释放）
    开启事务，重名savepointA，创建1W次,SAVE_POINT_NUM=10000
    释放1000次，创建1001次，SAVE_POINT_NUM=10001；释放1次，创建1次，SAVE_POINT_NUM=10001；
    回滚1001次，释放1000次，SAVE_POINT_NUM=9001；回滚1次，创建1次；SAVE_POINT_NUM=9002；
    回滚1000次，创建1000次，SAVE_POINT_NUM=10002；回滚1次，释放1次，循环1001次，SAVE_POINT_NUM=9001；
    回滚2次，创建1次，释放2次，循环1001次，SAVE_POINT_NUM=8000；
    创建1次，释放2次，回滚3次，循环1000次，SAVE_POINT_NUM=7000；
    创建1次，回滚2次，释放2次，循环1000次，SAVE_POINT_NUM=6000；
    创建2次，回滚3次，释放1次，循环1001次，SAVE_POINT_NUM=7001；
    查看视图，事务状态ACTIVE，SAVE_POINT_NUM数量=0，undo内存无明显增长；
    回滚事务，成功。释放savepointA，报错。SAVE_POINT_NUM数量=0。undo内存已归还。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 1000;
    uint32_t expectSPNum = 0;
    uint64_t undoSizeBefore = 0;
    uint64_t undoSizeAfter = 0;

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 重名savepointA，创建1W次,SAVE_POINT_NUM=10000
    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeBefore);
    for (i = 0; i < cycleNum; i++) {
        CreateSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = cycleNum;
    TestQuerySPNum(expectSPNum);

    // 释放1000次，创建1001次，SAVE_POINT_NUM=10001；释放1次，创建1次，SAVE_POINT_NUM=10001；
    for (i = 0; i < (cycleNum / 10); i++) {
        ReleaseSavepoint(g_conn_async, g_savepointName);
        CreateSavepoint(g_conn_async, g_savepointName);
    }
    CreateSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = cycleNum + 1;
    TestQuerySPNum(expectSPNum);

    ReleaseSavepoint(g_conn_async, g_savepointName);
    CreateSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = cycleNum + 1;
    TestQuerySPNum(expectSPNum);

    // 回滚1001次，释放1000次，SAVE_POINT_NUM=9001；回滚1次，创建1次；SAVE_POINT_NUM=9002；
    RollbackSavepoint(g_conn_async, g_savepointName);
    for (i = 0; i < (cycleNum / 10); i++) {
        RollbackSavepoint(g_conn_async, g_savepointName);
        ReleaseSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = cycleNum - (cycleNum / 10) + 1;
    TestQuerySPNum(expectSPNum);

    RollbackSavepoint(g_conn_async, g_savepointName);
    CreateSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = cycleNum - (cycleNum / 10) + 2;
    TestQuerySPNum(expectSPNum);

    // 回滚1000次，创建1000次，SAVE_POINT_NUM=10002；回滚1次，释放1次，循环1001次，SAVE_POINT_NUM=9001；
    for (i = 0; i < (cycleNum / 10); i++) {
        RollbackSavepoint(g_conn_async, g_savepointName);
        CreateSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = cycleNum + 2;
    TestQuerySPNum(expectSPNum);

    for (i = 0; i < ((cycleNum / 10) + 1); i++) {
        RollbackSavepoint(g_conn_async, g_savepointName);
        ReleaseSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = cycleNum - (cycleNum / 10) + 1;
    TestQuerySPNum(expectSPNum);

    // 回滚2次，创建1次，释放2次，循环1001次，SAVE_POINT_NUM=8000；
    for (i = 0; i < ((cycleNum / 10) + 1); i++) {
        RollbackSavepoint(g_conn_async, g_savepointName);
        RollbackSavepoint(g_conn_async, g_savepointName);
        CreateSavepoint(g_conn_async, g_savepointName);
        ReleaseSavepoint(g_conn_async, g_savepointName);
        ReleaseSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = cycleNum / 10 * 8;
    TestQuerySPNum(expectSPNum);

    // 创建1次，释放2次，回滚3次，循环1000次，SAVE_POINT_NUM=7000；
    for (i = 0; i < (cycleNum / 10); i++) {
        CreateSavepoint(g_conn_async, g_savepointName);
        ReleaseSavepoint(g_conn_async, g_savepointName);
        ReleaseSavepoint(g_conn_async, g_savepointName);
        RollbackSavepoint(g_conn_async, g_savepointName);
        RollbackSavepoint(g_conn_async, g_savepointName);
        RollbackSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = cycleNum / 10 * 7;
    TestQuerySPNum(expectSPNum);

    // 创建1次，回滚2次，释放2次，循环1000次，SAVE_POINT_NUM=6000；
    for (i = 0; i < (cycleNum / 10); i++) {
        CreateSavepoint(g_conn_async, g_savepointName);
        RollbackSavepoint(g_conn_async, g_savepointName);
        RollbackSavepoint(g_conn_async, g_savepointName);
        ReleaseSavepoint(g_conn_async, g_savepointName);
        ReleaseSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = cycleNum / 10 * 6;
    TestQuerySPNum(expectSPNum);

    // 创建2次，回滚3次，释放1次，循环1001次，SAVE_POINT_NUM=7001；
    for (i = 0; i < ((cycleNum / 10) + 1); i++) {
        CreateSavepoint(g_conn_async, g_savepointName);
        CreateSavepoint(g_conn_async, g_savepointName);
        RollbackSavepoint(g_conn_async, g_savepointName);
        RollbackSavepoint(g_conn_async, g_savepointName);
        RollbackSavepoint(g_conn_async, g_savepointName);
        ReleaseSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = cycleNum / 10 * 7 + 1;
    TestQuerySPNum(expectSPNum);

    // 查看视图，事务状态ACTIVE，SAVE_POINT_NUM数量=0，undo内存无明显增长；
    for (i = 0; i < (cycleNum / 10 * 7 + 1); i++) {
        ReleaseSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeAfter);
    AW_FUN_Log(LOG_INFO, "TOTAL_UNDOLOG_SIZE before is %d, TOTAL_UNDOLOG_SIZE after is %d.",
        undoSizeBefore, undoSizeAfter);
    EXPECT_GE(undoSizeBefore, undoSizeAfter);

    // 回滚事务，成功。释放savepointA，报错。SAVE_POINT_NUM数量=0。undo内存已归还。
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚savepointA，报错。
    ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_NO_ACTIVE_TRANSACTION);
    AddWhiteList(GMERR_NO_ACTIVE_TRANSACTION);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 024.连续创建多个重名savepoint并释放
1、开启事务，创建savepoint，名称非法，失败
2、执行dml操作，创建savepoint A，成功
3、多次执行步骤2，创建10个重名savepoint A，成功，查询DFX视图SAVE_POINT_NUM数量=10；
4、释放savepoint A，操作10次，成功释放。第11次释放savepoint A，报不存在。
校验点：1）查询Diff，结果符合修改内容。2）反复回滚savepoint A，都报不存在；
3）DFX视图可查看到savepoint A已全部被释放掉，查询DFX视图SAVE_POINT_NUM数量=0。
5、回滚到步骤1的savepoint，报不存在；释放步骤1的savepoint，报不存在。
6、继续创建重名savepoint A，成功，查询DFX视图SAVE_POINT_NUM数量=1；（前面savepoint回滚报错不影响继续创建）
7、事务提交，成功。subtree查询结果符合修改，查询DFX视图SAVE_POINT_NUM数量=0。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 1、开启事务，创建savepoint，名称非法，失败
    CreateSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);

    // 2、执行dml操作，创建savepoint A，成功
    // 3、多次执行步骤2，创建10个重名savepoint A，成功，查询DFX视图SAVE_POINT_NUM数量=10；
    for (i = 0; i < cycleNum; i++) {
        // isnert
        initValue = i * insertNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
        CreateSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = cycleNum;
    TestQuerySPNum(expectSPNum);

    // 4、释放savepoint A，操作10次，成功释放。第11次释放savepoint A，报不存在。
    for (i = 0; i < cycleNum; i++) {
        ReleaseSavepoint(g_conn_async, g_savepointName);
    }
    ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);

    // 5、回滚到步骤1的savepoint，报不存在；释放步骤1的savepoint，报不存在。
    RollbackSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);
    ReleaseSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);

    // 6、继续创建重名savepoint A，成功，查询DFX视图SAVE_POINT_NUM数量=1；（前面savepoint回滚报错不影响继续创建）
    CreateSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 7、事务提交，成功。subtree查询结果符合修改，查询DFX视图SAVE_POINT_NUM数量=0。
    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_024");

    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_NAME);
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 025.创建多个重名savepoint并回滚
    1、开启事务，创建savepoint，名称非法，失败
    2、执行dml操作，创建savepoint A，成功
    3、多次执行步骤2，创建10个重名savepoint A，成功
    4、回滚savepoint A，操作11次，都能成功回滚。数据都跟第10个savepoint之前一样。
    5、释放savepoint A，操作1次，成功。回滚savepoint A，操作1次，成功。数据都跟第9个savepoint之前一样。
    6、回滚savepoint A，释放savepoint A，操作9次，都成功。
        校验点：1）数据已被全部回滚。2）反复回滚savepoint A，都报不存在；
        3）DFX视图可查看到savepoint A已全部被释放掉。
    7、回滚到步骤1的savepoint，报不存在；释放步骤1的savepoint，报不存在。
    8、事务回滚，成功。subtree查询结果到数据未发生变化。
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 10;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 1、开启事务，创建savepoint，名称非法，失败
    CreateSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);

    // 2、执行dml操作，创建savepoint A，成功
    // 3、多次执行步骤2，创建10个重名savepoint A，成功
    for (i = 0; i < cycleNum; i++) {
        // isnert
        initValue = i * insertNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
        CreateSavepoint(g_conn_async, g_savepointName);
    }
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff025_001);

    // 查询视图
    expectSPNum = cycleNum;
    TestQuerySPNum(expectSPNum);

    // 4、回滚savepoint A，操作11次，都能成功回滚。数据都跟第10个savepoint之前一样。
    for (i = 0; i < cycleNum; i++) {
        RollbackSavepoint(g_conn_async, g_savepointName);
    }
    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff025_001);

    // 5、释放savepoint A，操作1次，成功。回滚savepoint A，操作1次，成功。数据都跟第9个savepoint之前一样。
    ReleaseSavepoint(g_conn_async, g_savepointName);
    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff025_002);

    // 6、回滚savepoint A，释放savepoint A，操作9次，都成功。
    //     校验点：1）数据已被全部回滚。2）反复回滚savepoint A，都报不存在；
    //     3）DFX视图可查看到savepoint A已全部被释放掉。
    for (i = 0; i < (cycleNum - 1); i++) {
        ReleaseSavepoint(g_conn_async, g_savepointName);
        if (i == (cycleNum - 2)) {
            RollbackSavepoint(g_conn_async, g_savepointName, GMERR_NO_DATA);
            break;
        }
        RollbackSavepoint(g_conn_async, g_savepointName);
    }

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);
    // 7、回滚到步骤1的savepoint，报不存在；释放步骤1的savepoint，报不存在。
    RollbackSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);
    ReleaseSavepoint(g_conn_async, "sp中文", GMERR_INVALID_NAME);

    // 8、事务回滚，成功。subtree查询结果到数据未发生变化。
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_025");
    AddWhiteList(GMERR_INVALID_NAME);
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 026.事务常规abort后创建savepoint
    1、开启事务，
    2、执行dml操作，中间创建3个savepoint1-3，成功，查询DFX视图SAVE_POINT_NUM数量=3
    3、插入违反list唯一性约束的数据，继续执行dml操作，过程中创建savepoint4-6，预期报错；
    4、创建匿名savepoint null，预期报错，查询DFX视图SAVE_POINT_NUM数量=3；回滚匿名savepoint null，预期报错，查询DFX视图SAVE_POINT_NUM数量=3；
    5、回滚到savepoint4，预期报错，查询DFX视图SAVE_POINT_NUM数量=3；提交事务，预期失败；
    6、回滚到savepoint3，预期成功，查询DFX视图SAVE_POINT_NUM数量=3；
    7、释放savepoint3，预期成功，查询DFX视图SAVE_POINT_NUM数量=2；
    8、回滚到savepoint2，预期成功，查询DFX视图SAVE_POINT_NUM数量=2；
    9、提交事务，预期成功，查询DFX视图SAVE_POINT_NUM数量=0；
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 2、执行dml操作，中间创建3个savepoint1-3，成功，查询DFX视图SAVE_POINT_NUM数量=3
    initValue = 0;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName1);
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName2);
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    // 3、插入违反list唯一性约束的数据，继续执行dml操作，过程中创建savepoint4-6，预期报错；
    // insert fail
    initValue = 0;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName4, GMERR_TRANSACTION_ROLLBACK);
    initValue = 0;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5, GMERR_TRANSACTION_ROLLBACK);
    initValue = 0;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName6, GMERR_TRANSACTION_ROLLBACK);

    // 4、创建匿名savepoint null，预期报错，查询DFX视图SAVE_POINT_NUM数量=3；
    // 回滚匿名savepoint null，预期报错，查询DFX视图SAVE_POINT_NUM数量=3；
    CreateSavepoint(g_conn_async, NULL, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async, NULL, GMERR_NO_DATA);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    // 5、回滚到savepoint4，预期报错，查询DFX视图SAVE_POINT_NUM数量=3；提交事务，预期失败；
    RollbackSavepoint(g_conn_async, g_savepointName4, GMERR_NO_DATA);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    // 提交事务失败
    AsyncUserDataT data = {0};
    ret = GmcTransCommitAsync(g_conn_async, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 3; // TRX_STATE_ABORT
    TestQueryTrxState(expectSPNum);

    // 6、回滚到savepoint3，预期成功，查询DFX视图SAVE_POINT_NUM数量=3；
    RollbackSavepoint(g_conn_async, g_savepointName3);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    // 7、释放savepoint3，预期成功，查询DFX视图SAVE_POINT_NUM数量=2；
    ReleaseSavepoint(g_conn_async, g_savepointName3);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // 8、回滚到savepoint2，预期成功，查询DFX视图SAVE_POINT_NUM数量=2；
    RollbackSavepoint(g_conn_async, g_savepointName2);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // 9、提交事务，预期成功，查询DFX视图SAVE_POINT_NUM数量=0；
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_026");

    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    AddWhiteList(GMERR_NO_DATA);
    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 028.两次savepoint之间没有任何dml操作
    1、开启事务，
    2、无任何操作，创建1025个匿名savepoint，查询DFX视图SAVE_POINT_NUM数量=1025，
    3、循环100次回滚匿名savepoint，不释放，预期成功，查询DFX视图SAVE_POINT_NUM数量=1025，
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 1024;
    uint32_t expectSPNum = 0;

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 2、无任何操作，创建1025个匿名savepoint，查询DFX视图SAVE_POINT_NUM数量=1025，
    for (i = 0; i < cycleNum; i++) {
        CreateSavepoint(g_conn_async, NULL);
    }

    // 查询视图
    expectSPNum = cycleNum;
    TestQuerySPNum(expectSPNum);

    // 3、循环100次回滚匿名savepoint，不释放，预期成功，查询DFX视图SAVE_POINT_NUM数量=1025，
    for (i = 0; i < 100; i++) {
        RollbackSavepoint(g_conn_async, NULL);
    }

    // 查询视图
    expectSPNum = cycleNum;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 029.全部回滚之后再创建
    1、开启事务，创建匿名savepoint
    2、执行dml操作，创建匿名savepoint；重复操作10轮；
    3、依次回滚并释放最近10个匿名savepoint，查询DFX视图SAVE_POINT_NUM数量=1；
    4、回滚并释放第一个savepoint，查询DFX视图SAVE_POINT_NUM数量=0；查询diff，数据无变更。
    5、创建匿名savepoint，查询DFX视图SAVE_POINT_NUM数量=1；
    6、更新2W条数据，再次创建匿名savepoint，查询DFX视图SAVE_POINT_NUM数量=2；
    7、回滚匿名savepoint，释放，查询DFX视图SAVE_POINT_NUM数量=1；
    8、再次回滚匿名savepoint，释放，查询DFX视图SAVE_POINT_NUM数量=0；
    9、事务提交
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 1、开启事务，创建匿名savepoint
    CreateSavepoint(g_conn_async, NULL);

    // 2、执行dml操作，创建匿名savepoint；重复操作10轮；
    for (i = 0; i < cycleNum; i++) {
        // isnert
        initValue = i * insertNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
        CreateSavepoint(g_conn_async, NULL);
    }

    // 3、依次回滚并释放最近10个匿名savepoint，查询DFX视图SAVE_POINT_NUM数量=1；
    for (i = 0; i < cycleNum; i++) {
        RollbackSavepoint(g_conn_async, NULL);
        ReleaseSavepoint(g_conn_async, NULL);
    }

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 4、回滚并释放第一个savepoint，查询DFX视图SAVE_POINT_NUM数量=0；查询diff，数据无变更。
    RollbackSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);

    TestFetchAndDeparseDiff(g_stmt_async, expectDiff029_001);

    // 5、创建匿名savepoint，查询DFX视图SAVE_POINT_NUM数量=1；
    CreateSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 6、更新2W条数据，再次创建匿名savepoint，查询DFX视图SAVE_POINT_NUM数量=2；
    initValue = 20000;
    insertNum = 10000;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    initValue = 30000;
    insertNum = 10000;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // 7、回滚匿名savepoint，释放，查询DFX视图SAVE_POINT_NUM数量=1；
    RollbackSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 8、再次回滚匿名savepoint，释放，查询DFX视图SAVE_POINT_NUM数量=0；
    RollbackSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 030.循环创建savepoint并编辑相同数据
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 1、开启事务，创建匿名savepoint，查询DFX视图SAVE_POINT_NUM数量=1；
    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 2、执行dml操作2K条数据，创建匿名savepoint；重复操作10轮；查询DFX视图SAVE_POINT_NUM数量=11；
    for (i = 0; i < cycleNum; i++) {
        // isnert
        initValue = i * insertNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
        CreateSavepoint(g_conn_async, NULL);
    }

    // 查询视图
    expectSPNum = 11;
    TestQuerySPNum(expectSPNum);

    // 3、依次回滚并释放最近10个匿名savepoint，查询DFX视图SAVE_POINT_NUM数量=1；
    for (i = 0; i < cycleNum; i++) {
        RollbackSavepoint(g_conn_async, NULL);
        ReleaseSavepoint(g_conn_async, NULL);
    }

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 4、回滚并释放第一个savepoint，查询DFX视图SAVE_POINT_NUM数量=0；查询diff，数据无变更。
    RollbackSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 0;
    TestQuerySPNum(expectSPNum);

    TestFetchAndDeparseDiff(g_stmt_async, expectDiff029_001);

    // 5、创建匿名savepoint，查询DFX视图SAVE_POINT_NUM数量=1；
    CreateSavepoint(g_conn_async, NULL);
    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 回滚事务
    ret = TestTransRollBackAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 031.创建5个不同名savepoint，按照创建顺序的反序回滚，
                并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName2);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName4);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName5);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    RollbackSavepoint(g_conn_async, g_savepointName4);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_002);

    RollbackSavepoint(g_conn_async, g_savepointName3);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_003);

    RollbackSavepoint(g_conn_async, g_savepointName2);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_004);

    RollbackSavepoint(g_conn_async, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_005);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 032.创建5个不同名savepoint，依次回滚第3个和第1个savepoint，
                并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName2);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName4);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName3);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_003);

    // 查询视图
    expectSPNum = 4;
    TestQuerySPNum(expectSPNum);

    RollbackSavepoint(g_conn_async, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_005);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 033.创建5个不同名savepoint，回滚第1个savepoint，并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName2);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName4);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_005);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 034.创建5个同名savepoint，依次回滚，并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 035.创建5个不指定名字savepoint，依次回滚，并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 回滚
    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 036.root下有两个list表，两个连接分别开启事务，
                分别创建多个不同名的savepoint，并分别回滚savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(g_conn_async1, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName1);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName2);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName2);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName4);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName4);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName5);

    // 查询视图
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 6", "SAVE_POINT_NUM: 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName5);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    RollbackSavepoint(g_conn_async1, g_savepointName5);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    RollbackSavepoint(g_conn_async, g_savepointName4);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_002);
    RollbackSavepoint(g_conn_async1, g_savepointName4);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_007);

    RollbackSavepoint(g_conn_async, g_savepointName3);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_003);
    RollbackSavepoint(g_conn_async1, g_savepointName3);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_008);

    RollbackSavepoint(g_conn_async, g_savepointName2);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_004);
    RollbackSavepoint(g_conn_async1, g_savepointName2);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_009);

    RollbackSavepoint(g_conn_async, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_005);
    RollbackSavepoint(g_conn_async1, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_010);

    // 查询视图
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 2", "SAVE_POINT_NUM: 2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(g_conn_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 037.root下有两个list表，两个连接分别开启事务，
                分别创建多个同名的savepoint，并分别回滚savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(g_conn_async1, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // 查询视图
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 6", "SAVE_POINT_NUM: 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    RollbackSavepoint(g_conn_async1, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    RollbackSavepoint(g_conn_async1, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    RollbackSavepoint(g_conn_async1, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    RollbackSavepoint(g_conn_async1, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    RollbackSavepoint(g_conn_async1, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    // 查询视图
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 6", "SAVE_POINT_NUM: 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(g_conn_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 038.root下有两个list表，两个连接分别开启事务，
                分别创建多个不指定名称的savepoint，并分别回滚savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(g_conn_async1, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, NULL);
    CreateSavepoint(g_conn_async1, NULL);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, NULL);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, NULL);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, NULL);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, NULL);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, NULL);

    // 查询视图
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 6", "SAVE_POINT_NUM: 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚
    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    RollbackSavepoint(g_conn_async1, NULL);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    RollbackSavepoint(g_conn_async1, NULL);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    RollbackSavepoint(g_conn_async1, NULL);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    RollbackSavepoint(g_conn_async1, NULL);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    RollbackSavepoint(g_conn_async1, NULL);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    // 查询视图
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 6", "SAVE_POINT_NUM: 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(g_conn_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 039.root下有多个list表，并发多个连接分别开启事务，
                分别创建多个不同名的savepoint，并分别回滚savepoint
 Author       : hanyang
*****************************************************************************/
void *client_thread_039_01(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE start==================");

    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName1, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName2, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName3, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName4, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName5, GMERR_OK, epollData.userEpollFd);

    // 回滚
    RollbackSavepoint(conn_async, g_savepointName5, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName4, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName3, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName2, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName1, GMERR_OK, epollData.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE end==================");
}

void *client_thread_039_02(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE start==================");

    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName1, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName2, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName3, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName4, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName5, GMERR_OK, epollData.userEpollFd);

    // 回滚
    RollbackSavepoint(conn_async, g_savepointName5, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName4, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName3, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName2, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName1, GMERR_OK, epollData.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE end==================");
}

// main
TEST_F(Reinforce, Yang_030_Reinf_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_HEARTBEAT_REGISTER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int ret;
    uint32_t fieldValue = 100;
    uint32_t threadNum = 2;

    ret = TestCheckNull(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    pthread_t tid[threadNum];

    pthread_create(&tid[0], NULL, client_thread_039_01, NULL);
    pthread_create(&tid[1], NULL, client_thread_039_02, NULL);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/*****************************************************************************
 Description  : 040.root下有多个list表，并发多个连接分别开启事务，
                分别创建多个同名的savepoint，并分别回滚savepoint
 Author       : hanyang
*****************************************************************************/
void *client_thread_040_01(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE start==================");
    EpollThreadDataT epollData1 = {0};
    ret = createEpollOneThread(&epollData1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData1.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData1.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData1.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData1.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData1.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData1.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData1.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData1.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData1.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData1.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData1.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData1.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData1.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData1.userEpollFd);

    // 回滚
    RollbackSavepoint(conn_async, g_savepointName, GMERR_OK, epollData1.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName, GMERR_OK, epollData1.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName, GMERR_OK, epollData1.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName, GMERR_OK, epollData1.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName, GMERR_OK, epollData1.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData1.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData1);
    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE end==================");
}

void *client_thread_040_02(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE start==================");
    EpollThreadDataT epollData2 = {0};
    ret = createEpollOneThread(&epollData2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData2.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData2.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData2.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData2.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData2.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData2.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData2.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData2.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData2.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData2.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData2.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData2.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData2.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData2.userEpollFd);

    // 回滚
    RollbackSavepoint(conn_async, g_savepointName, GMERR_OK, epollData2.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName, GMERR_OK, epollData2.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName, GMERR_OK, epollData2.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName, GMERR_OK, epollData2.userEpollFd);
    RollbackSavepoint(conn_async, g_savepointName, GMERR_OK, epollData2.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData2.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData2);
    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE end==================");
}

// main
TEST_F(Reinforce, Yang_030_Reinf_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_HEARTBEAT_REGISTER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int ret;
    uint32_t fieldValue = 100;
    uint32_t threadNum = 2;

    ret = TestCheckNull(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    pthread_t tid[threadNum];

    pthread_create(&tid[0], NULL, client_thread_040_01, NULL);
    pthread_create(&tid[1], NULL, client_thread_040_02, NULL);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/*****************************************************************************
 Description  : 041.root下有多个list表，并发多个连接分别开启事务，
                分别创建多个不指定名称的savepoint，并分别回滚savepoint
 Author       : hanyang
*****************************************************************************/
void *client_thread_041_01(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE start==================");

    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // 回滚
    RollbackSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE end==================");
}

void *client_thread_041_02(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE start==================");

    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // 回滚
    RollbackSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    RollbackSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE end==================");
}

// main
TEST_F(Reinforce, Yang_030_Reinf_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_HEARTBEAT_REGISTER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int ret;
    uint32_t fieldValue = 100;
    uint32_t threadNum = 2;

    ret = TestCheckNull(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    pthread_t tid[threadNum];

    pthread_create(&tid[0], NULL, client_thread_041_01, NULL);
    pthread_create(&tid[1], NULL, client_thread_041_02, NULL);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/*****************************************************************************
 Description  : 042.创建100个savepoint，每次操作2000条数据，依次回滚100个savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i;
    uint32_t cycleNum = 100;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;
    uint64_t undoSizeBefore = 0;
    uint64_t undoSizeAfter = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    for (int time = 0; time < 2; time++) {
        if (time != 0) {    // 首次执行的目的是防止undo seg缓存页对用例预期产生影响，因此非首次执行才进行数据校验
            getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeBefore);
        }

        // 启动事务
        ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (i = 0; i < cycleNum; i++) {
            char spName[1024] = {0};
            ret = sprintf(spName, "sp%d", i);
            ASSERT_LT(0, ret);
            CreateSavepoint(g_conn_async, spName);

            initValue = i * insertNum;
            TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
        }

        for (i = (cycleNum - 1); i >= 0; i--) {
            char spName[1024] = {0};
            ret = sprintf(spName, "sp%d", i);
            ASSERT_LT(0, ret);
            RollbackSavepoint(g_conn_async, spName);
        }

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        if (time != 0) {    // 首次执行的目的是防止undo seg缓存页对用例预期产生影响，因此非首次执行才进行数据校验
            getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeAfter);
            AW_FUN_Log(LOG_INFO, "TOTAL_UNDOLOG_SIZE before is %d, TOTAL_UNDOLOG_SIZE after is %d.",
                undoSizeBefore, undoSizeAfter);
            EXPECT_GE(undoSizeBefore, undoSizeAfter);
        }
    }

    // 查heap视图，回滚完预期heap不占用页
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_HEAP_STAT";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=\'list_6\'",
        g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", command);
    system(command);
    ret = executeCommand(command, "PAGE_COUNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char const *view_name1 = "V\\$STORAGE_HASH_INDEX_STAT";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=\'list_6\'",
        g_toolPath, g_connServer, view_name1);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", command);
    system(command);
    ret = executeCommand(command, "ENTRY_USED: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 043.创建100个savepoint，每次操作2000条数据，挑选10个savepoint回滚
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i;
    uint32_t cycleNum = 100;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = 0; i < cycleNum; i++) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async, spName);

        initValue = i * insertNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    }

    RollbackSavepoint(g_conn_async, "sp97");
    RollbackSavepoint(g_conn_async, "sp77");
    RollbackSavepoint(g_conn_async, "sp65");
    RollbackSavepoint(g_conn_async, "sp54");
    RollbackSavepoint(g_conn_async, "sp49");
    RollbackSavepoint(g_conn_async, "sp48");
    RollbackSavepoint(g_conn_async, "sp32");
    RollbackSavepoint(g_conn_async, "sp21");
    RollbackSavepoint(g_conn_async, "sp6");
    RollbackSavepoint(g_conn_async, "sp1");

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_043");
}

/*****************************************************************************
 Description  : 044.创建100个savepoint，每次操作2000条数据，直接回滚第1个savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i;
    uint32_t cycleNum = 100;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = 0; i < cycleNum; i++) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async, spName);

        initValue = i * insertNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    }

    RollbackSavepoint(g_conn_async, "sp0");

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_044");
}

/*****************************************************************************
 Description  : 045.创建100个savepoint，每次操作200条数据，每条数据超过40k，依次回滚100个savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i;
#ifdef ENV_RTOSV2X
    uint32_t cycleNum = 3;
#else
    uint32_t cycleNum = 100;
#endif
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 200;
    uint32_t initValue = 0;
    uint64_t undoSizeBefore = 0;
    uint64_t undoSizeAfter = 0;

    // isnert
    TestInsertRoot(g_conn_async_big, "root", fieldValue);

    for (int time = 0; time < 2; time++) {
        if (time != 0) {    // 首次执行的目的是防止undo seg缓存页对用例预期产生影响，因此非首次执行才进行数据校验
            getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeBefore);
        }
        // 启动事务
        ret = TestTransStartAsync(g_conn_async_big, g_mSTrxConfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (i = 0; i < cycleNum; i++) {
            char spName[1024] = {0};
            ret = sprintf(spName, "sp%d", i);
            ASSERT_LT(0, ret);
            CreateSavepoint(g_conn_async_big, spName);

            initValue = i * insertNum;
            TestInsertListChild(g_conn_async_big, "root", "list_6", initValue, insertNum, 1);
        }

        for (i = (cycleNum - 1); i >= 0; i--) {
            char spName[1024] = {0};
            ret = sprintf(spName, "sp%d", i);
            ASSERT_LT(0, ret);
            RollbackSavepoint(g_conn_async_big, spName);
        }

        // 提交事务
        ret = TestTransCommitAsync(g_conn_async_big);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (time != 0) {    // 首次执行的目的是防止undo seg缓存页对用例预期产生影响，因此非首次执行才进行数据校验
            getSysView("V\\$STORAGE_UNDO_STAT", "TOTAL_UNDOLOG_SIZE", &undoSizeAfter);
            AW_FUN_Log(LOG_INFO, "TOTAL_UNDOLOG_SIZE before is %d, TOTAL_UNDOLOG_SIZE after is %d.",
                undoSizeBefore, undoSizeAfter);
             EXPECT_GE(undoSizeBefore, undoSizeAfter);
        }
    }

    // 查heap视图，回滚完预期heap不占用页
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_HEAP_STAT";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=\'list_6\'",
        g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", command);
    system(command);
    ret = executeCommand(command, "PAGE_COUNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char const *view_name1 = "V\\$STORAGE_HASH_INDEX_STAT";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=\'list_6\'",
        g_toolPath, g_connServer, view_name1);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", command);
    system(command);
    ret = executeCommand(command, "ENTRY_USED: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 052.创建5个不同名savepoint，DML失败，新创建savepoint失败，
                回滚超时前的savepoint成功，继续写入数据成功，创建savepoint成功
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName2);

    // isnert fail
    initValue = 2;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    RollbackSavepoint(g_conn_async, g_savepointName2);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName4);

    // isnert fail
    initValue = 4;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 5;
    TestQuerySPNum(expectSPNum);

    RollbackSavepoint(g_conn_async, g_savepointName3);
    RollbackSavepoint(g_conn_async, g_savepointName1);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName5);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff052_001);

    RollbackSavepoint(g_conn_async, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff052_002);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_052");
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
}

/*****************************************************************************
 Description  : 053.创建5个同名savepoint，DML失败，新创建savepoint失败，
                回滚超时前的savepoint成功，继续写入数据成功，创建savepoint成功
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert fail
    initValue = 2;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    RollbackSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert fail
    initValue = 4;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 5;
    TestQuerySPNum(expectSPNum);

    RollbackSavepoint(g_conn_async, g_savepointName);
    RollbackSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = 5;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff053_001);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff053_001);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff053_001);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_053");
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
}

/*****************************************************************************
 Description  : 054.创建5个不指定名字savepoint，DML失败，新创建savepoint失败，
                回滚超时前的savepoint成功，继续写入数据成功，创建savepoint成功
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert fail
    initValue = 2;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    RollbackSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert fail
    initValue = 4;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 5;
    TestQuerySPNum(expectSPNum);

    RollbackSavepoint(g_conn_async, NULL);
    RollbackSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 5;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 回滚
    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff053_001);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff053_001);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff053_001);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_053");
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
}

/*****************************************************************************
 Description  : 055.创建5个不同名savepoint，按照创建顺序的反序释放，并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName2);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName4);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 释放
    ReleaseSavepoint(g_conn_async, g_savepointName5);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    ReleaseSavepoint(g_conn_async, g_savepointName4);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    ReleaseSavepoint(g_conn_async, g_savepointName3);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    ReleaseSavepoint(g_conn_async, g_savepointName2);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    ReleaseSavepoint(g_conn_async, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 056.创建5个不同名savepoint，依次释放第3个和第1个savepoint，
                并回滚第4个savepoint，并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName2);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName4);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 释放
    ReleaseSavepoint(g_conn_async, g_savepointName3);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    ReleaseSavepoint(g_conn_async, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_001);

    // 查询视图
    expectSPNum = 4;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 057.创建5个同名savepoint，释放2个，回滚1个，回滚后继续释放3个，
                并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 释放
    ReleaseSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_003);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_003);

    // 释放
    ReleaseSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_057");
}

/*****************************************************************************
 Description  : 058.创建5个不指定名字savepoint，释放2个，回滚1个，回滚后继续释放3个，
                并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    // 释放
    ReleaseSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);

    // 回滚
    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_003);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff031_003);

    // 释放
    ReleaseSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_057");
}

/*****************************************************************************
 Description  : 059.root下有两个list表，两个连接分别开启事务，
                分别创建多个不同名的savepoint，并分别释放savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(g_conn_async1, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName1);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName2);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName2);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName4);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName4);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName5);

    // 查询视图
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 6", "SAVE_POINT_NUM: 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放
    ReleaseSavepoint(g_conn_async, g_savepointName5);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    ReleaseSavepoint(g_conn_async1, g_savepointName5);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    ReleaseSavepoint(g_conn_async, g_savepointName4);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    ReleaseSavepoint(g_conn_async1, g_savepointName4);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    ReleaseSavepoint(g_conn_async, g_savepointName3);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    ReleaseSavepoint(g_conn_async1, g_savepointName3);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    ReleaseSavepoint(g_conn_async, g_savepointName2);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    ReleaseSavepoint(g_conn_async1, g_savepointName2);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    ReleaseSavepoint(g_conn_async, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_001);
    ReleaseSavepoint(g_conn_async1, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_006);

    // 查询视图
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 1", "SAVE_POINT_NUM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(g_conn_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 060.root下有两个list表，两个连接分别开启事务，
                分别创建多个同名的savepoint，并分别释放savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(g_conn_async1, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, g_savepointName);

    // 查询视图
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 6", "SAVE_POINT_NUM: 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放
    ReleaseSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async1, g_savepointName);
    ReleaseSavepoint(g_conn_async1, g_savepointName);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_003);
    RollbackSavepoint(g_conn_async1, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_008);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_003);
    RollbackSavepoint(g_conn_async1, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_008);

    // 释放
    ReleaseSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async1, g_savepointName);
    ReleaseSavepoint(g_conn_async1, g_savepointName);
    ReleaseSavepoint(g_conn_async1, g_savepointName);

    // 查询视图
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 1", "SAVE_POINT_NUM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(g_conn_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
/*****************************************************************************
 Description  : 061.root下有两个list表，两个连接分别开启事务，
                分别创建多个不指定名称的savepoint，并分别释放savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransStartAsync(g_conn_async1, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, NULL);
    CreateSavepoint(g_conn_async1, NULL);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, NULL);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, NULL);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, NULL);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, NULL);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);
    TestInsertListChild(g_conn_async1, "root1", "list_1", initValue, insertNum);
    CreateSavepoint(g_conn_async1, NULL);

    // 查询视图
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 6", "SAVE_POINT_NUM: 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放
    ReleaseSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async1, NULL);
    ReleaseSavepoint(g_conn_async1, NULL);

    // 回滚
    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_003);
    RollbackSavepoint(g_conn_async1, NULL);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_008);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff036_003);
    RollbackSavepoint(g_conn_async1, NULL);
    TestFetchAndDeparseDiff(g_stmt_async1, expectDiff036_008);

    // 释放
    ReleaseSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async1, NULL);
    ReleaseSavepoint(g_conn_async1, NULL);
    ReleaseSavepoint(g_conn_async1, NULL);

    // 查询视图
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "SAVE_POINT_NUM: 1", "SAVE_POINT_NUM: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestTransCommitAsync(g_conn_async1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 062.root下有多个list表，并发多个连接分别开启事务，
                分别创建多个不同名的savepoint，并分别释放savepoint
 Author       : hanyang
*****************************************************************************/
void *client_thread_062_01(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE start==================");

    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName1, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName2, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName3, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName4, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName5, GMERR_OK, epollData.userEpollFd);

    // 释放
    ReleaseSavepoint(conn_async, g_savepointName5, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName4, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName3, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName2, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName1, GMERR_OK, epollData.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE end==================");
}

void *client_thread_062_02(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE start==================");

    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName1, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName2, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName3, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName4, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName5, GMERR_OK, epollData.userEpollFd);

    // 释放
    ReleaseSavepoint(conn_async, g_savepointName5, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName4, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName3, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName2, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName1, GMERR_OK, epollData.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE end==================");
}

// main
TEST_F(Reinforce, Yang_030_Reinf_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_HEARTBEAT_REGISTER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int ret;
    uint32_t fieldValue = 100;
    uint32_t threadNum = 2;

    ret = TestCheckNull(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    pthread_t tid[threadNum];

    pthread_create(&tid[0], NULL, client_thread_062_01, NULL);
    pthread_create(&tid[1], NULL, client_thread_062_02, NULL);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/*****************************************************************************
 Description  : 063.root下有多个list表，并发多个连接分别开启事务，
                分别创建多个同名的savepoint，并分别释放savepoint
 Author       : hanyang
*****************************************************************************/
void *client_thread_063_01(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE start==================");

    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // 释放
    ReleaseSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE end==================");
}

void *client_thread_063_02(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE start==================");

    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // 释放
    ReleaseSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, g_savepointName, GMERR_OK, epollData.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE end==================");
}

// main
TEST_F(Reinforce, Yang_030_Reinf_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_HEARTBEAT_REGISTER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int ret;
    uint32_t fieldValue = 100;
    uint32_t threadNum = 2;

    ret = TestCheckNull(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    pthread_t tid[threadNum];

    pthread_create(&tid[0], NULL, client_thread_063_01, NULL);
    pthread_create(&tid[1], NULL, client_thread_063_02, NULL);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/*****************************************************************************
 Description  : 064.root下有多个list表，并发多个连接分别开启事务，
                分别创建多个不指定名称的savepoint，并分别释放savepoint
 Author       : hanyang
*****************************************************************************/
void *client_thread_064_01(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE start==================");

    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root", "list_6", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // 释放
    ReleaseSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
    AW_FUN_Log(LOG_INFO, "==============[1] Yang list_6 CREATE end==================");
}

void *client_thread_064_02(void *args)
{
    int ret;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_root = NULL;
    AsyncUserDataT data = {0};

    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE start==================");

    EpollThreadDataT epollData = {0};
    ret = createEpollOneThread(&epollData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &epollData.userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_root, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUseNamespaceAsync(stmt_root, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data, 1, true, -1, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 启动事务
    ret = TestTransStartAsync(conn_async, g_mSTrxConfig, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 1;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 2;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 3;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 4;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // isnert
    initValue = 5;
    TestInsertListChild(conn_async, "root1", "list_1", initValue, insertNum, 0, epollData.userEpollFd);
    CreateSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // 释放
    ReleaseSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);
    ReleaseSavepoint(conn_async, NULL, GMERR_OK, epollData.userEpollFd);

    // 提交事务
    ret = TestTransCommitAsync(conn_async, epollData.userEpollFd);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    closeEpollOneThread(&epollData);
    AW_FUN_Log(LOG_INFO, "==============[2] Yang list_1 CREATE end==================");
}

// main
TEST_F(Reinforce, Yang_030_Reinf_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_HEARTBEAT_REGISTER);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    int ret;
    uint32_t fieldValue = 100;
    uint32_t threadNum = 2;

    ret = TestCheckNull(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestInsertRoot(g_conn_async, "root", fieldValue);
    TestInsertRoot(g_conn_async, "root1", fieldValue);

    pthread_t tid[threadNum];

    pthread_create(&tid[0], NULL, client_thread_064_01, NULL);
    pthread_create(&tid[1], NULL, client_thread_064_02, NULL);

    pthread_join(tid[0], NULL);
    pthread_join(tid[1], NULL);
}

/*****************************************************************************
 Description  : 065.创建100个savepoint，每次操作2000条数据，依次释放100个savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i;
    uint32_t cycleNum = 100;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = 0; i < cycleNum; i++) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async, spName);

        initValue = i * insertNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    }

    for (i = (cycleNum - 1); i >= 0; i--) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        ReleaseSavepoint(g_conn_async, spName);
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 066.创建100个savepoint，每次操作2000条数据，挑选10个savepoint释放
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建的savepoint很多
    AW_ADD_TRUNCATION_WHITE_LIST(1, "savePointInfo:count");

    int ret;
    int i;
    uint32_t cycleNum = 100;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = 0; i < cycleNum; i++) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async, spName);

        initValue = i * insertNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    }

    RollbackSavepoint(g_conn_async, "sp97");

    // 查询视图
    expectSPNum = 98;
    TestQuerySPNum(expectSPNum);

    ReleaseSavepoint(g_conn_async, "sp77");
    RollbackSavepoint(g_conn_async, "sp77", GMERR_NO_DATA);
    RollbackSavepoint(g_conn_async, "sp76");

    // 查询视图
    expectSPNum = 77;
    TestQuerySPNum(expectSPNum);

    ReleaseSavepoint(g_conn_async, "sp90", GMERR_NO_DATA);
    ReleaseSavepoint(g_conn_async, "sp70");
    RollbackSavepoint(g_conn_async, "sp65");
    RollbackSavepoint(g_conn_async, "sp54");
    ReleaseSavepoint(g_conn_async, "sp49");
    RollbackSavepoint(g_conn_async, "sp49", GMERR_NO_DATA);
    RollbackSavepoint(g_conn_async, "sp48");
    RollbackSavepoint(g_conn_async, "sp32");
    ReleaseSavepoint(g_conn_async, "sp33", GMERR_NO_DATA);
    RollbackSavepoint(g_conn_async, "sp21");
    RollbackSavepoint(g_conn_async, "sp6");
    RollbackSavepoint(g_conn_async, "sp1");

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_043");

    AddWhiteList(GMERR_DATA_EXCEPTION);
}

/*****************************************************************************
 Description  : 067.创建100个savepoint，每次操作2000条数据，直接释放第1个savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i;
    uint32_t cycleNum = 100;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 2000;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = 0; i < cycleNum; i++) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async, spName);

        initValue = i * insertNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    }

    // 释放
    ReleaseSavepoint(g_conn_async, "sp0");
    // 回滚
    RollbackSavepoint(g_conn_async, "sp1");

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_067");
}

/*****************************************************************************
 Description  : 068.创建100个savepoint，每次操作200条数据，每条数据超过40k，依次释放100个savepoint
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i;
#ifdef ENV_RTOSV2X
    uint32_t cycleNum = 3;
#else
    uint32_t cycleNum = 100;
#endif
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 200;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async_big, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_big, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = 0; i < cycleNum; i++) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async_big, spName);

        initValue = i * insertNum;
        TestInsertListChild(g_conn_async_big, "root", "list_6", initValue, insertNum, 1);
    }

    for (i = (cycleNum - 1); i >= 0; i--) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        ReleaseSavepoint(g_conn_async_big, spName);
    }

    // 再次创建
    for (i = 0; i < cycleNum; i++) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async_big, spName);
    }

    for (i = (cycleNum - 1); i >= 0; i--) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        RollbackSavepoint(g_conn_async_big, spName);
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async_big);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 075.创建5个不同名savepoint，DML失败，新创建savepoint失败，
                释放超时前的savepoint成功，继续写入数据成功，创建savepoint成功
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName2);

    // isnert fail
    initValue = 2;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    ReleaseSavepoint(g_conn_async, g_savepointName1, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async, g_savepointName2);
    ReleaseSavepoint(g_conn_async, g_savepointName1);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName4);

    // isnert fail
    initValue = 4;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 4;
    TestQuerySPNum(expectSPNum);

    ReleaseSavepoint(g_conn_async, g_savepointName3, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async, g_savepointName4);
    ReleaseSavepoint(g_conn_async, g_savepointName3);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName5);

    // 查询视图
    expectSPNum = 4;
    TestQuerySPNum(expectSPNum);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName5);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff075_001);

    RollbackSavepoint(g_conn_async, g_savepointName2);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff075_002);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_075");
}

/*****************************************************************************
 Description  : 076.创建5个同名savepoint，DML失败，新创建savepoint失败，
                释放超时前的savepoint成功，继续写入数据成功，创建savepoint成功
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert fail
    initValue = 2;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert fail
    initValue = 4;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 4;
    TestQuerySPNum(expectSPNum);

    ReleaseSavepoint(g_conn_async, g_savepointName, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = 4;
    TestQuerySPNum(expectSPNum);

    // 回滚
    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff076_001);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    RollbackSavepoint(g_conn_async, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff076_002);
    ReleaseSavepoint(g_conn_async, g_savepointName);
    ReleaseSavepoint(g_conn_async, g_savepointName);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_076");
}

/*****************************************************************************
 Description  : 077.创建5个不指定名字savepoint，DML失败，新创建savepoint失败，
                释放超时前的savepoint成功，继续写入数据成功，创建savepoint成功
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert fail
    initValue = 2;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    ReleaseSavepoint(g_conn_async, NULL, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // isnert fail
    initValue = 4;
    TestInsertListChildFail(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    expectSPNum = 4;
    TestQuerySPNum(expectSPNum);

    ReleaseSavepoint(g_conn_async, NULL, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 3;
    TestQuerySPNum(expectSPNum);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 4;
    TestQuerySPNum(expectSPNum);

    // 回滚
    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff076_001);
    ReleaseSavepoint(g_conn_async, NULL);

    RollbackSavepoint(g_conn_async, NULL);
    TestFetchAndDeparseDiff(g_stmt_async, expectDiff076_002);
    ReleaseSavepoint(g_conn_async, NULL);
    ReleaseSavepoint(g_conn_async, NULL);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree查询
    TestSubtreeFilter(g_conn_async, g_stmt_async, "root", "Yang_030_Reinf_076");
    AddWhiteList(GMERR_UNIQUE_VIOLATION);
    AddWhiteList(GMERR_PRIMARY_KEY_VIOLATION);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
}

/*****************************************************************************
 Description  : 078.问题单DTS2023040714837场景用例补充
                设备上 调用savepoint接口 GmcTransRollBackSavepointAsync 导致服务端core掉
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML表1, 创建savepoint
    initValue = 0;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // DML表2, 创建savepoint
    initValue = 0;
    TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // DML表1, 创建savepoint
    initValue = 10;
    TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async, g_savepointName);

    // DML表2, rollback savepoint
    TestInsertListChild(g_conn_async, "root", "list_7", initValue, insertNum);
    RollbackSavepoint(g_conn_async, g_savepointName);

    TestFetchAndDeparseDiff(g_stmt_async, expectDiff078);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 079.问题单DTS2023041001651、DTS2023041106505场景用例补充
                savePoint 回滚触发断言，导致core掉
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce, Yang_030_Reinf_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 50;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 500;
    uint32_t initValue = 0;

    // isnert
    TestInsertRoot(g_conn_async, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = 0; i < cycleNum; i++) {
        // DML表1, 500条数据, 创建savepoint g_savepointName
        CreateSavepoint(g_conn_async, g_savepointName);
        initValue = i * insertNum * 2;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

        // DML表1, 再500条数据, 创建savepoint g_savepointName1
        CreateSavepoint(g_conn_async, g_savepointName1);
        initValue = i * insertNum * 2 + insertNum;
        TestInsertListChild(g_conn_async, "root", "list_6", initValue, insertNum);

        // rollback savepoint g_savepointName1
        RollbackSavepoint(g_conn_async, g_savepointName1);

        // 重复rollback savepoint g_savepointName
        RollbackSavepoint(g_conn_async, g_savepointName);
        RollbackSavepoint(g_conn_async, g_savepointName);
    }

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查heap视图，回滚完预期heap不占用页
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_HEAP_STAT";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=\'list_6\'",
        g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", command);
    system(command);
    ret = executeCommand(command, "PAGE_COUNT: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char const *view_name1 = "V\\$STORAGE_HASH_INDEX_STAT";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s -f LABEL_NAME=\'list_6\'",
        g_toolPath, g_connServer, view_name1);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    AW_FUN_Log(LOG_INFO, "%s\n", command);
    system(command);
    ret = executeCommand(command, "ENTRY_USED: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}


GmcConnT *g_conn_async_long = NULL;
GmcStmtT *g_stmt_async_long = NULL;
GmcStmtT *g_stmt_root_long = NULL;
GmcStmtT *g_stmt_list_long[10] = {0};
GmcNodeT *g_rootNode_long = NULL;
GmcNodeT *g_childNode_long[40] = {0};
class Reinforce01 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void Reinforce01::SetUpTestCase()
{
    int ret;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorEnable=1\" ");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"trxMonitorThreshold=\"5,10\"\" ");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"workerHungThreshold=6,200,300\"");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createEpollOneThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void Reinforce01::TearDownTestCase()
{
        closeEpollOneThread();
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
}

void Reinforce01::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

        YangConnOptionT connOptions = {0};
    ret = TestYangGmcConnectWithEpollOneThread(&g_conn_async_long, &g_stmt_async_long, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(g_conn_async_long, &g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int i = 0;
    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_conn_async_long, &g_stmt_list_long[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 异步创建namespace
    GmcDropNamespace(g_stmt, g_namespace);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    // 设置namespace级别为：可重复读 + 乐观事务
    GmcNspCfgT nspCfg;
    nspCfg.namespaceName = g_namespace;
    nspCfg.tablespaceName = NULL;
    nspCfg.userName = g_namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async_long, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // Yang事务配置为乐观+可重复读，需要和namespace保持一致
    g_mSTrxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_mSTrxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_mSTrxConfig.readOnly = false;
    g_mSTrxConfig.trxType = GMC_OPTIMISTIC_TRX;

    ret = GmcUseNamespaceAsync(g_stmt_async_long, g_namespace, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcClearNamespaceAsync(g_stmt_async_long, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 创建Vertex表
    TestCreateLabelAll(g_stmt_async_long);


    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_TRANSACTION_ROLLBACK);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    AW_CHECK_LOG_BEGIN();
}

void Reinforce01::TearDown()
{
    int ret;
    int i = 0;
    AsyncUserDataT data = {0};
    AW_CHECK_LOG_END();

    // 删除Vertex和Edge表
    ret = GmcClearNamespaceAsync(g_stmt_async_long, g_namespace, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 异步删除namespace
    ret = GmcDropNamespaceAsync(g_stmt_async_long, g_namespace, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    GmcFreeStmt(g_stmt_root_long);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list_long[i]);
    }
    testGmcDisconnect(g_conn_async_long, g_stmt_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_conn_async_long = NULL;
    g_stmt_async_long = NULL;
    g_stmt_root_long = NULL;
    for (i = 0; i < 10; i++) {
        g_stmt_list_long[i] = NULL;
    }

    g_rootNode_long = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode_long[i] = NULL;
    }
}

/*****************************************************************************
 Description  : 027.事务超时abort后创建savepoint
1、设置事务超时时间，
2、开启事务，创建多个savepoint1-N，成功，查询DFX视图SAVE_POINT_NUM数量=N；
3、达到事务超时时间后等10秒，创建savepointN+1，预期报错；
4、创建匿名savepoint null，预期报错，查询DFX视图无结果；回滚匿名savepoint null，预期报错，查询DFX视图无结果；
5、回滚到savepointN+1，预期报错，查询DFX视图无结果；提交事务，预期失败；
6、回滚到savepointN，预期失败；
7、释放savepointN，预期失败，查询DFX视图无结果；
8、释放到savepoint2，预期失败，查询DFX视图无结果；
9、提交事务，预期失败，查询DFX视图无结果；
10、回滚事务，预期成功，查询DFX视图无结果；
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 200;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 2、开启事务，创建多个savepoint1-N，成功，查询DFX视图SAVE_POINT_NUM数量=N；
    for (i = 0; i < cycleNum; i++) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        CreateSavepoint(g_conn_async_long, spName);
    }

    // 查询视图
    expectSPNum = cycleNum;
    TestQuerySPNum(expectSPNum);

    // 3、达到事务超时时间后等10秒，创建savepointN+1，预期报错；
    sleep(13);
    CreateSavepoint(g_conn_async_long, "sp200", GMERR_TRANSACTION_ROLLBACK);

    // 4、创建匿名savepoint null，预期报错，查询DFX视图SAVE_POINT_NUM数量=N；
    CreateSavepoint(g_conn_async_long, NULL, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    char command[MAX_CMD_SIZE];
    char const *view_name = "V\\$STORAGE_TRX_DETAIL";
    ret = snprintf(command, MAX_CMD_SIZE, "%s/gmsysview -s %s -q %s", g_toolPath, g_connServer, view_name);
    AW_MACRO_EXPECT_NE_INT(0, ret);
    system(command);
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 回滚匿名savepoint null，预期报错，查询DFX视图SAVE_POINT_NUM数量=N；
    RollbackSavepoint(g_conn_async_long, NULL, GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 5、回滚到savepointN+1，预期报错，查询DFX视图SAVE_POINT_NUM数量=N；提交事务，预期失败；
    RollbackSavepoint(g_conn_async_long, "sp200", GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 提交事务失败
    AsyncUserDataT data = {0};
    ret = GmcTransCommitAsync(g_conn_async_long, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);

    // 6、回滚到savepointN，预期失败；
    RollbackSavepoint(g_conn_async_long, "sp199", GMERR_TRANSACTION_ROLLBACK);

    // 7、释放savepointN，预期失败，查询DFX视图SAVE_POINT_NUM数量=N-1；
    ReleaseSavepoint(g_conn_async_long, "sp199", GMERR_TRANSACTION_ROLLBACK);

    // 查询视图
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 8、释放到savepoint2，预期失败，查询DFX视图SAVE_POINT_NUM数量=2；
    for (i = (cycleNum - 2); i > 1; i--) {
        char spName[1024] = {0};
        ret = sprintf(spName, "sp%d", i);
        ASSERT_LT(0, ret);
        ReleaseSavepoint(g_conn_async_long, spName, GMERR_TRANSACTION_ROLLBACK);
    }

    // 查询视图
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 9、提交事务，预期失败，查询DFX视图SAVE_POINT_NUM数量=2；
    // 提交事务失败
    ret = GmcTransCommitAsync(g_conn_async_long, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);

    // 查询视图
    ret = executeCommand(command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 10、回滚事务，预期成功，查询DFX视图SAVE_POINT_NUM数量=0；
    ret = TestTransRollBackAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 046.长事务但未超过超时门限，创建5个不同名savepoint，
                按照创建顺序的反序回滚，并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(6);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName2);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName4);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName5);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 回滚
    RollbackSavepoint(g_conn_async_long, g_savepointName5);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    RollbackSavepoint(g_conn_async_long, g_savepointName4);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_002);

    RollbackSavepoint(g_conn_async_long, g_savepointName3);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_003);

    RollbackSavepoint(g_conn_async_long, g_savepointName2);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_004);

    RollbackSavepoint(g_conn_async_long, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_005);

    // 查询视图
    expectSPNum = 2;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 047.长事务但未超过超时门限，创建5个同名savepoint，依次回滚，
                并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(6);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 回滚
    RollbackSavepoint(g_conn_async_long, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    RollbackSavepoint(g_conn_async_long, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    RollbackSavepoint(g_conn_async_long, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    RollbackSavepoint(g_conn_async_long, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    RollbackSavepoint(g_conn_async_long, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 048.长事务但未超过超时门限，创建5个不指定名字savepoint，
                依次回滚，并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(6);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 回滚
    RollbackSavepoint(g_conn_async_long, NULL);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    RollbackSavepoint(g_conn_async_long, NULL);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    RollbackSavepoint(g_conn_async_long, NULL);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    RollbackSavepoint(g_conn_async_long, NULL);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    RollbackSavepoint(g_conn_async_long, NULL);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 049.创建5个不同名savepoint，长事务超时，新创建savepoint失败，
                回滚超时前的savepoint失败，继续写入数据失败，创建savepoint失败
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName2);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName4);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName5);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    sleep(15);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 新建savepoint
    CreateSavepoint(g_conn_async_long, g_savepointName6, GMERR_TRANSACTION_ROLLBACK);

    // 回滚
    RollbackSavepoint(g_conn_async_long, g_savepointName5, GMERR_TRANSACTION_ROLLBACK);

    // insert fail
    initValue = 6;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async_long, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root_long, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root_long, &g_rootNode_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list_long[0], "list_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root_long, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list_long[0], &g_childNode_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        uint32_t fieldValue = initValue + i;
        TestYangSetNodeProperty_PK(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyWithoutF0(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    CreateSavepoint(g_conn_async_long, g_savepointName6, GMERR_TRANSACTION_ROLLBACK);

    // 提交事务
    ret = TestTransRollBackAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 050.创建5个同名savepoint，长事务超时，新创建savepoint失败，
                回滚超时前的savepoint失败，继续写入数据失败，创建savepoint失败
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    sleep(15);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 新建savepoint
    CreateSavepoint(g_conn_async_long, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // 回滚
    RollbackSavepoint(g_conn_async_long, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // insert fail
    initValue = 6;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async_long, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root_long, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root_long, &g_rootNode_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list_long[0], "list_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root_long, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list_long[0], &g_childNode_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        uint32_t fieldValue = initValue + i;
        TestYangSetNodeProperty_PK(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyWithoutF0(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    CreateSavepoint(g_conn_async_long, g_savepointName6, GMERR_TRANSACTION_ROLLBACK);

    // 提交事务
    ret = TestTransRollBackAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 051.创建5个不指定名字savepoint，长事务超时，新创建savepoint失败，
                回滚超时前的savepoint失败，继续写入数据失败，创建savepoint失败
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    sleep(15);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 新建savepoint
    CreateSavepoint(g_conn_async_long, NULL, GMERR_TRANSACTION_ROLLBACK);

    // 回滚
    RollbackSavepoint(g_conn_async_long, NULL, GMERR_TRANSACTION_ROLLBACK);

    // insert fail
    initValue = 6;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async_long, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root_long, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root_long, &g_rootNode_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list_long[0], "list_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root_long, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list_long[0], &g_childNode_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        uint32_t fieldValue = initValue + i;
        TestYangSetNodeProperty_PK(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyWithoutF0(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    CreateSavepoint(g_conn_async_long, g_savepointName6, GMERR_TRANSACTION_ROLLBACK);

    // 提交事务
    ret = TestTransRollBackAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
}

/*****************************************************************************
 Description  : 069.长事务但未超过超时门限，创建5个不同名savepoint，
                按照创建顺序的反序释放，并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(6);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName2);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName4);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName5);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 释放
    ReleaseSavepoint(g_conn_async_long, g_savepointName5);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    ReleaseSavepoint(g_conn_async_long, g_savepointName4);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    ReleaseSavepoint(g_conn_async_long, g_savepointName3);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    ReleaseSavepoint(g_conn_async_long, g_savepointName2);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    ReleaseSavepoint(g_conn_async_long, g_savepointName1);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 070.长事务但未超过超时门限，创建5个同名savepoint，依次释放，
                并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(6);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 释放
    RollbackSavepoint(g_conn_async_long, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);
    ReleaseSavepoint(g_conn_async_long, g_savepointName);

    RollbackSavepoint(g_conn_async_long, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_002);
    ReleaseSavepoint(g_conn_async_long, g_savepointName);

    RollbackSavepoint(g_conn_async_long, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_003);
    ReleaseSavepoint(g_conn_async_long, g_savepointName);

    RollbackSavepoint(g_conn_async_long, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_004);
    ReleaseSavepoint(g_conn_async_long, g_savepointName);

    RollbackSavepoint(g_conn_async_long, g_savepointName);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_005);
    ReleaseSavepoint(g_conn_async_long, g_savepointName);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 071.长事务但未超过超时门限，创建5个不指定名字savepoint，
                依次释放，并查询diff，查询视图STORAGE_TRX_DETAIL
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(6);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 回滚
    RollbackSavepoint(g_conn_async_long, NULL);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_001);
    ReleaseSavepoint(g_conn_async_long, NULL);

    RollbackSavepoint(g_conn_async_long, NULL);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_002);
    ReleaseSavepoint(g_conn_async_long, NULL);

    RollbackSavepoint(g_conn_async_long, NULL);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_003);
    ReleaseSavepoint(g_conn_async_long, NULL);

    RollbackSavepoint(g_conn_async_long, NULL);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_004);
    ReleaseSavepoint(g_conn_async_long, NULL);

    RollbackSavepoint(g_conn_async_long, NULL);
    TestFetchAndDeparseDiff(g_stmt_async_long, expectDiff031_005);
    ReleaseSavepoint(g_conn_async_long, NULL);

    // 查询视图
    expectSPNum = 1;
    TestQuerySPNum(expectSPNum);
    expectSPNum = 2; // TRX_STATE_ACTIVE
    TestQueryTrxState(expectSPNum);

    // 提交事务
    ret = TestTransCommitAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 072.创建5个不同名savepoint，长事务超时，新创建savepoint失败，
                释放超时前的savepoint失败，继续写入数据失败，创建savepoint失败
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName1);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName2);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName3);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName4);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName5);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    sleep(15);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 新建savepoint
    CreateSavepoint(g_conn_async_long, g_savepointName6, GMERR_TRANSACTION_ROLLBACK);

    // 回滚和释放
    ReleaseSavepoint(g_conn_async_long, g_savepointName5, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async_long, g_savepointName5, GMERR_TRANSACTION_ROLLBACK);

    // insert fail
    initValue = 6;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async_long, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root_long, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root_long, &g_rootNode_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list_long[0], "list_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root_long, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list_long[0], &g_childNode_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        uint32_t fieldValue = initValue + i;
        TestYangSetNodeProperty_PK(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyWithoutF0(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    CreateSavepoint(g_conn_async_long, g_savepointName6, GMERR_TRANSACTION_ROLLBACK);

    // 提交事务
    ret = TestTransRollBackAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 073.创建5个同名savepoint，长事务超时，新创建savepoint失败，
                释放超时前的savepoint失败，继续写入数据失败，创建savepoint失败
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, g_savepointName);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    sleep(15);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 新建savepoint
    CreateSavepoint(g_conn_async_long, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // 回滚和释放
    ReleaseSavepoint(g_conn_async_long, g_savepointName, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async_long, g_savepointName, GMERR_TRANSACTION_ROLLBACK);

    // insert fail
    initValue = 6;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async_long, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root_long, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root_long, &g_rootNode_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list_long[0], "list_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root_long, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list_long[0], &g_childNode_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        uint32_t fieldValue = initValue + i;
        TestYangSetNodeProperty_PK(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyWithoutF0(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    CreateSavepoint(g_conn_async_long, g_savepointName6, GMERR_TRANSACTION_ROLLBACK);

    // 提交事务
    ret = TestTransRollBackAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 Description  : 074.创建5个不指定名字savepoint，长事务超时，新创建savepoint失败，
                释放超时前的savepoint失败，继续写入数据失败，创建savepoint失败
 Author       : hanyang
*****************************************************************************/
TEST_F(Reinforce01, Yang_030_Reinf_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int i, j;
    uint32_t cycleNum = 10;
    uint32_t expectSPNum = 0;
    uint32_t fieldValue = 100;
    uint32_t insertNum = 1;
    uint32_t initValue = 0;

    TestInsertRoot(g_conn_async_long, "root", fieldValue);

    // 启动事务
    ret = TestTransStartAsync(g_conn_async_long, g_mSTrxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    CreateSavepoint(g_conn_async_long, g_savepointName);

    // isnert
    initValue = 1;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 2;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 3;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 4;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // isnert
    initValue = 5;
    TestInsertListChild(g_conn_async_long, "root", "list_6", initValue, insertNum);
    CreateSavepoint(g_conn_async_long, NULL);

    // 查询视图
    expectSPNum = 6;
    TestQuerySPNum(expectSPNum);

    sleep(15);

    ret = AW_CHECK_LOG_EXIST(SERVER, 2, "Long Transaction", "runs timeout but still active");
    AW_MACRO_EXPECT_EQ_BOOL(true, ret);

    // 新建savepoint
    CreateSavepoint(g_conn_async_long, NULL, GMERR_TRANSACTION_ROLLBACK);

    // 回滚和释放
    ReleaseSavepoint(g_conn_async_long, NULL, GMERR_TRANSACTION_ROLLBACK);
    RollbackSavepoint(g_conn_async_long, NULL, GMERR_TRANSACTION_ROLLBACK);

    // insert fail
    initValue = 6;
    GmcBatchT *batch = NULL;

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_conn_async_long, &batch, GMC_BATCH_YANG, GMC_YANG_DIFF_OFF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root_long, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root_long, &g_rootNode_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点
    for (uint32_t i = 0; i < insertNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_list_long[0], "list_6", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmt_root_long, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmt_list_long[0], &g_childNode_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 设置属性值
        uint32_t fieldValue = initValue + i;
        TestYangSetNodeProperty_PK(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
        TestYangSetNodePropertyWithoutF0(g_childNode_long[0], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmt_list_long[0]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_TRANSACTION_ROLLBACK, data.status);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    CreateSavepoint(g_conn_async_long, g_savepointName6, GMERR_TRANSACTION_ROLLBACK);

    // 提交事务
    ret = TestTransRollBackAsync(g_conn_async_long);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
}
