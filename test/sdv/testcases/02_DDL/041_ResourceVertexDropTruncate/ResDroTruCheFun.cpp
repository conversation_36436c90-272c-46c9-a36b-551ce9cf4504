extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>

#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "resource_drop_truncate.h"
//可以update资源表，但是不允许更新资源字段，不可以merge资源表 --QE,2021.12.21
//资源表不允许更新资源字段，merge走insert流程 --ST,2021.12.21
using namespace std;

class ResDroTruCheFun : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\" ");
        system("sh $TEST_HOME/tools/start.sh");
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);

        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);

        ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);

        ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        ret = testGmcDisconnect(g_conn_async, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
        g_conn_sync = NULL;
        g_stmt_sync = NULL;
        g_conn_async = NULL;
        g_stmt_async = NULL;

        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\" ");
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void ResDroTruCheFun::SetUp()
{
    AW_CHECK_LOG_BEGIN();

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_BATCH_BUFFER_FULL);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);

    char errorMsg4[128] = {};
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg4);

    char errorMsg5[128] = {};
    (void)snprintf(errorMsg5, sizeof(errorMsg5), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg5);

    char errorMsg6[128] = {};
    (void)snprintf(errorMsg6, sizeof(errorMsg6), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg6);

    char errorMsg7[128] = {};
    (void)snprintf(errorMsg7, sizeof(errorMsg7), "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg7);
}
void ResDroTruCheFun::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001.同步连接下，创建资源池，创建1024张资源表绑定后进行删除资源表
TEST_F(ResDroTruCheFun, DDL_041_02_001)
{
    const char *ResPoolName = "CheckFuncResDelTruc";
    const char *ResPool =
        R"({
        "name" : "CheckFuncResDelTruc",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 0,
        "alloc_type" : 1
    })";

    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    int record = 1024;
    char labelName[64] = {};
    char schema_path[64] = {};
    CreateVertexLabelFile();
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    int i;
    for (i = 1; i <= record; i++) {
        char *schema_json = NULL;
        sprintf(labelName, "batch_set_res_cfg%d", i);
        sprintf(schema_path, "./batch_file/%s.gmjson", labelName);
        readJanssonFile(schema_path, &schema_json);
        ASSERT_NE((void *)NULL, schema_json);

        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, schema_json, LableConfig);
        if (ret != GMERR_OK) {
            EXPECT_EQ(GMERR_BATCH_BUFFER_FULL, ret);
            printf("GmcBatchAddDDL Create:i=%d, ret=%d ,schema_path=%s\n", i, ret, schema_path);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            free(schema_json);
            schema_json = NULL;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
        free(schema_json);
        schema_json = NULL;
    }

    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);

    for (int j = 1; j <= i - 1; j++) {
        memset(labelName, 0, sizeof(labelName));
        sprintf(labelName, "batch_set_res_cfg%d", j);

        ret = GmcCreateResPool(g_stmt_sync, ResPool);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcTruncateVertexLabel(g_stmt_sync, labelName);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcBindResPoolToLabel(g_stmt_sync, ResPoolName, labelName);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcTruncateVertexLabel(g_stmt_sync, labelName);
        EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
        if (j == i - 2) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
        }

        ret = GmcUnbindResPoolFromLabel(g_stmt_sync, labelName);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcDestroyResPool(g_stmt_sync, ResPoolName);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcDropVertexLabel(g_stmt_sync, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 002.同步连接下，创建资源池，创建多张资源表解绑后反复清空10W次然后删除
TEST_F(ResDroTruCheFun, DDL_041_02_002)
{
    uint64_t respoolId = 0xFFFF;
    uint64_t count = 1000000;
    uint64_t startIndex = 0xFFFFFFFF;
    uint64_t tmpResIdx = 0;

    const char *ResPoolName = "CheckFuncResDelTruc";
    const char *ResPool =
        R"({
        "name" : "CheckFuncResDelTruc",
        "pool_id" : 10002,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 1,
        "alloc_type" : 1
    })";
    const char *LabelNameSub = "ResVtxDelTrucSub";
    const char *LabelSchemaSub =
        R"([{
        "type":"record",
        "name":"ResVtxDelTrucSub",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
                {"name":"F4", "type":"resource", "nullable":false},
                {"name":"F5", "type":"resource", "nullable":false},
                {"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T36_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateVertexLabel(g_stmt_sync, LabelSchemaSub, LableConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateResPool(g_stmt_sync, ResPool);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(g_stmt_sync, LabelSchema, LableConfig);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelNameSub);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(g_stmt_sync, ResPoolName, LabelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, LabelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t val = 99999999;
    ret = GmcSetVertexProperty(g_stmt_sync, "F0", GMC_DATATYPE_INT32, &val, sizeof(val));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_sync, "F1", GMC_DATATYPE_INT32, &val, sizeof(val));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_sync, "F2", GMC_DATATYPE_INT32, &val, sizeof(val));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_sync, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_RESTRICT_VIOLATION, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);
#if defined ENV_RTOSV2X
    int record = 10000;
#else
    int record = 100000;
#endif
    for (uint32_t i; i < record; i++) {
        ret = GmcTruncateVertexLabel(g_stmt_sync, LabelName);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcTruncateVertexLabel(g_stmt_sync, LabelNameSub);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDestroyResPool(g_stmt_sync, ResPoolName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, LabelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt_sync, LabelNameSub);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.同步连接下，创建资源池和tree类型资源表，truncate表后绑定资源池，对表进行insert和update操作，解绑后drop表
TEST_F(ResDroTruCheFun, DDL_041_02_003)
{
    int32_t ret = 0;
    uint64_t count = 40;
    int start_num = 0;
    int end_num = 1024;
    const char *resPoolNameString = "CheckRespoolDropTrunc";
    const char *resPoolDelTrunc =
        R"({
        "name" : "CheckRespoolDropTrunc",
        "pool_id" : 10003,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 0
    })";

    char *test_schema = NULL;
    GmcDropVertexLabel(g_stmt_sync, gLabelName);
    // 创建资源表
    readJanssonFile("./schema_file/resource_drop_trunc_vtx.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    char Label_config[] = "{\"max_record_num\":1000000}";

    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema, Label_config);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE);
    if (ret != GMERR_OK) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(test_schema);

    // 创建资源池
    ret = GmcCreateResPool(g_stmt_sync, resPoolDelTrunc);
    EXPECT_EQ(GMERR_OK, ret);

    // truncate资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 绑定资源池
    ret = GmcBindResPoolToLabel(g_stmt_sync, resPoolNameString, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    TestGmcInsertVertex(g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, 0,
        count, 0xFFFFFFFF, GMC_OPERATION_INSERT, 1);

    // scan
    TestGmcDirectFetchVertex(
        g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, true);

    // update
    TestGmcUpdateVertexByIndexKey(
        g_stmt_sync, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, gLabelName, 0, count, 0);

    // scan
    TestGmcDirectFetchVertex(
        g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, true);

    // 根据主键删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, gLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt_sync, 0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 解绑资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 销毁资源池
    ret = GmcDestroyResPool(g_stmt_sync, resPoolNameString);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.同步连接下，创建资源池和tree类型资源表，绑定资源池，对表进行insert和merge操作，解绑后truncate表，然后drop表
TEST_F(ResDroTruCheFun, DDL_041_02_004)
{
    int32_t ret = 0;
    uint64_t count = 40;
    int start_num = 0;
    int end_num = 1024;
    const char *resPoolNameString = "CheckRespoolDropTrunc";
    const char *resPoolDelTrunc =
        R"({
        "name" : "CheckRespoolDropTrunc",
        "pool_id" : 10004,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 0,
        "alloc_type" : 1
    })";

    char *test_schema = NULL;
    GmcDropVertexLabel(g_stmt_sync, gLabelName);
    // 创建资源表
    readJanssonFile("./schema_file/resource_drop_trunc_vtx.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    char Label_config[] = "{\"max_record_num\":1000000}";

    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema, Label_config);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE);
    if (ret != GMERR_OK) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(test_schema);

    // 创建资源池
    ret = GmcCreateResPool(g_stmt_sync, resPoolDelTrunc);
    EXPECT_EQ(GMERR_OK, ret);

    // 绑定资源池
    ret = GmcBindResPoolToLabel(g_stmt_sync, resPoolNameString, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    TestGmcInsertVertex(g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, 0,
        count, 0xFFFFFFFF, GMC_OPERATION_INSERT, 1);

// merge
#if RESOURCE_DROP_TRUNC_DEBUG2
    TestGmcMergeVertexByIndexKey(g_stmt_sync, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num,
        gLabelName, 0, count, 0xFFFFFFFF);
#endif

    // scan
    TestGmcDirectFetchVertex(
        g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, true);

    // 根据主键删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, gLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt_sync, 0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 解绑资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 销毁资源池
    ret = GmcDestroyResPool(g_stmt_sync, resPoolNameString);
    EXPECT_EQ(GMERR_OK, ret);

    // truncate资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.同步连接下，创建资源池和tree类型资源表并绑定，对表进行insert，update和merge操作后scan资源表，再解绑后truncate表和drop表
TEST_F(ResDroTruCheFun, DDL_041_02_005)
{
    int32_t ret = 0;
    uint64_t count = 40;
    int start_num = 0;
    int end_num = 1024;
    const char *resPoolNameString = "CheckRespoolDropTrunc";
    const char *resPoolDelTrunc =
        R"({
        "name" : "CheckRespoolDropTrunc",
        "pool_id" : 10005,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 0,
        "alloc_type" : 1
    })";

    char *test_schema = NULL;
    GmcDropVertexLabel(g_stmt_sync, gLabelName);
    // 创建资源表
    readJanssonFile("./schema_file/resource_drop_trunc_vtx.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    char Label_config[] = "{\"max_record_num\":1000000}";

    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema, Label_config);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE);
    if (ret != GMERR_OK) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(test_schema);

    // 创建资源池
    ret = GmcCreateResPool(g_stmt_sync, resPoolDelTrunc);
    EXPECT_EQ(GMERR_OK, ret);

    // 绑定资源池
    ret = GmcBindResPoolToLabel(g_stmt_sync, resPoolNameString, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    TestGmcInsertVertex(g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, 0,
        count, 0xFFFFFFFF, GMC_OPERATION_INSERT, 1);

    // update
    TestGmcUpdateVertexByIndexKey(
        g_stmt_sync, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, gLabelName, 0, count, 0);
#if RESOURCE_DROP_TRUNC_DEBUG2
    // merge
    TestGmcMergeVertexByIndexKey(
        g_stmt_sync, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, gLabelName, 0, count, 0);
#endif
    // scan
    TestGmcDirectFetchVertex(
        g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, true);

    // 根据主键删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, gLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt_sync, 0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 解绑资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    // 销毁资源池
    ret = GmcDestroyResPool(g_stmt_sync, resPoolNameString);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006.同步连接下，创建资源池和tree类型资源表并绑定，对表进行insert，update和merge操作后解绑，truncate表后scan资源表，再drop表
TEST_F(ResDroTruCheFun, DDL_041_02_006)
{
    int32_t ret = 0;
    uint64_t count = 40;
    int start_num = 0;
    int end_num = 1024;
    const char *resPoolNameString = "CheckRespoolDropTrunc";
    const char *resPoolDelTrunc =
        R"({
        "name" : "CheckRespoolDropTrunc",
        "pool_id" : 10006,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";

    char *test_schema = NULL;
    GmcDropVertexLabel(g_stmt_sync, gLabelName);
    // 创建资源表
    readJanssonFile("./schema_file/resource_drop_trunc_vtx.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    char Label_config[] = "{\"max_record_num\":1000000}";

    ret = GmcCreateVertexLabel(g_stmt_sync, test_schema, Label_config);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE);
    if (ret != GMERR_OK) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(test_schema);

    // 创建资源池
    ret = GmcCreateResPool(g_stmt_sync, resPoolDelTrunc);
    EXPECT_EQ(GMERR_OK, ret);

    // 绑定资源池
    ret = GmcBindResPoolToLabel(g_stmt_sync, resPoolNameString, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    TestGmcInsertVertex(g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, 0,
        count, 0xFFFFFFFF, GMC_OPERATION_INSERT, 1);

    // update
    TestGmcUpdateVertexByIndexKey(
        g_stmt_sync, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, gLabelName, 0, count, 0);

// merge
#if RESOURCE_DROP_TRUNC_DEBUG2
    TestGmcMergeVertexByIndexKey(
        g_stmt_sync, 2, 1, (char *)"string2", start_num, end_num, array_num, vector_num, gLabelName, 0, count, 0);
#endif

    // 根据主键删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, gLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt_sync, 0);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_sync);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 解绑资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

// scan
#if RESOURCE_DROP_TRUNC_DEBUG3
    TestGmcDirectFetchVertex(
        g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, true);
#endif
    // 销毁资源池
    ret = GmcDestroyResPool(g_stmt_sync, resPoolNameString);
    EXPECT_EQ(GMERR_OK, ret);
    // truncate资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.异步连接下，创建资源池和tree类型资源表，truncate表后绑定资源池，对表进行insert和update操作，解绑后drop表
TEST_F(ResDroTruCheFun, DDL_041_02_007)
{
    const char *resPoolNameString = "ResourcePoolTruncDrop";
    const char *resPoolDelTrunc =
        R"({
        "name" : "ResourcePoolTruncDrop",
        "pool_id" : 10007,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 0,
        "alloc_type" : 0
    })";

    int32_t ret = 0;
    uint64_t count = 40;
    int start_num = 0;
    int end_num = 2;
    char *test_schema = NULL;
    readJanssonFile("./schema_file/resource_drop_trunc_vtx.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    GmcDropVertexLabel(g_stmt_sync, gLabelName);
    // 异步插入准备
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE);
    if (ret != GMERR_OK) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);


    free(test_schema);

    // 创建资源池
    ret = GmcCreateResPool(g_stmt_sync, resPoolDelTrunc);
    EXPECT_EQ(GMERR_OK, ret);

    // 绑定资源池
    ret = GmcBindResPoolToLabel(g_stmt_sync, resPoolNameString, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 异步插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i, 0, (char *)"string", 0xFFFF, count, 0xFFFFFFFF);
        TestGmcSetNodePropertyByName_P(t1, i, 0, (char *)"string");
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 0, (char *)"string");
            ret = GmcNodeGetNextElement(t2, &t2);

            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 0, (char *)"string");
        }

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // scan须用同步
    TestGmcDirectFetchVertex(
        g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, true);

    // 异步update
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_P(t1, i, 2, (char *)"string2");

        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 2, (char *)"string2");
            ret = GmcNodeGetNextElement(t2, &t2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 2, (char *)"string2");
        }
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 异步根据主键删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.insertCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 解绑资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, resPoolNameString);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 008.异步连接下，创建资源池和tree类型资源表，绑定资源池，对表进行insert和merge操作，解绑后truncate表，然后drop表
TEST_F(ResDroTruCheFun, DDL_041_02_008)
{
    const char *resPoolNameString = "ResourcePoolTruncDrop";
    const char *resPoolDelTrunc =
        R"({
        "name" : "ResourcePoolTruncDrop",
        "pool_id" : 10008,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 0,
        "alloc_type" : 1
    })";

    int32_t ret = 0;
    uint64_t count = 40;
    int start_num = 0;
    int end_num = 2;
    char *test_schema = NULL;
    readJanssonFile("./schema_file/resource_drop_trunc_vtx.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    GmcDropVertexLabel(g_stmt_sync, gLabelName);
    // 异步插入准备
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE);
    if (ret != GMERR_OK) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);


    free(test_schema);

    // 创建资源池
    ret = GmcCreateResPool(g_stmt_sync, resPoolDelTrunc);
    EXPECT_EQ(GMERR_OK, ret);

    // 绑定资源池
    ret = GmcBindResPoolToLabel(g_stmt_sync, resPoolNameString, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 异步插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i, 0, (char *)"string", 0xFFFF, count, 0xFFFFFFFF);
        TestGmcSetNodePropertyByName_P(t1, i, 0, (char *)"string");
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 0, (char *)"string");
            ret = GmcNodeGetNextElement(t2, &t2);

            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 0, (char *)"string");
        }

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // scan须用同步
    TestGmcDirectFetchVertex(
        g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, true);

    // 异步merge
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_P(t1, i, 2, (char *)"string2");

        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 2, (char *)"string2");
            ret = GmcNodeGetNextElement(t2, &t2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 2, (char *)"string2");
        }
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(true, ret == GMERR_FEATURE_NOT_SUPPORTED || ret == GMERR_OK);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, data.status == GMERR_FEATURE_NOT_SUPPORTED || data.status == GMERR_OK);
        if (data.status != GMERR_OK) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.insertCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 异步merge走insert流程
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcSetNodePropertyByName_PK(root, i);
        // printf("i=%d\n", i);
        TestGmcSetNodePropertyByName_R(root, i, 0, (char *)"string", 0xFFFF, count, 0xFFFFFFFF);
        TestGmcSetNodePropertyByName_P(t1, i, 0, (char *)"string");
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 0, (char *)"string");
            ret = GmcNodeGetNextElement(t2, &t2);

            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 0, (char *)"string");
        }

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 异步根据主键删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.insertCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 解绑资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, resPoolNameString);
    EXPECT_EQ(GMERR_OK, ret);

    // truncate资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 009.异步连接下，创建资源池和tree类型资源表并绑定，对表进行insert，update和merge操作后scan资源表，再解绑后truncate表和drop表
TEST_F(ResDroTruCheFun, DDL_041_02_009)
{
    const char *resPoolNameString = "ResourcePoolTruncDrop";
    const char *resPoolDelTrunc =
        R"({
        "name" : "ResourcePoolTruncDrop",
        "pool_id" : 10009,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 0
    })";

    int32_t ret = 0;
    uint64_t count = 40;
    int start_num = 0;
    int end_num = 2;
    char *test_schema = NULL;
    readJanssonFile("./schema_file/resource_drop_trunc_vtx.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    GmcDropVertexLabel(g_stmt_sync, gLabelName);
    // 异步插入准备
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE);
    if (ret != GMERR_OK) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);


    free(test_schema);

    // 创建资源池
    ret = GmcCreateResPool(g_stmt_sync, resPoolDelTrunc);
    EXPECT_EQ(GMERR_OK, ret);

    // 绑定资源池
    ret = GmcBindResPoolToLabel(g_stmt_sync, resPoolNameString, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 异步插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i, 0, (char *)"string", 0xFFFF, count, 0xFFFFFFFF);
        TestGmcSetNodePropertyByName_P(t1, i, 0, (char *)"string");
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 0, (char *)"string");
            ret = GmcNodeGetNextElement(t2, &t2);

            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 0, (char *)"string");
        }

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 异步update
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_P(t1, i, 2, (char *)"string2");

        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 2, (char *)"string2");
            ret = GmcNodeGetNextElement(t2, &t2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 2, (char *)"string2");
        }
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 异步merge
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_P(t1, i, 2, (char *)"string2");

        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 2, (char *)"string2");
            ret = GmcNodeGetNextElement(t2, &t2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 2, (char *)"string2");
        }
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(true, ret == GMERR_FEATURE_NOT_SUPPORTED || ret == GMERR_OK);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, data.status == GMERR_FEATURE_NOT_SUPPORTED || data.status == GMERR_OK);
        if (data.status != GMERR_OK) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.insertCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 异步merge走insert流程
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i, 0, (char *)"string", 0xFFFF, count, 0xFFFFFFFF);
        TestGmcSetNodePropertyByName_P(t1, i, 0, (char *)"string");
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 0, (char *)"string");
            ret = GmcNodeGetNextElement(t2, &t2);

            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 0, (char *)"string");
        }

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

#if RESOURCE_DROP_TRUNC_DEBUG3
    TestGmcDirectFetchVertex(
        g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, true);
#endif
    // 异步根据主键删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.insertCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 解绑资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, resPoolNameString);
    EXPECT_EQ(GMERR_OK, ret);

    // truncate资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 010.异步连接下，创建资源池和tree类型资源表并绑定，对表进行insert，update和merge操作后解绑，truncate表后scan资源表，再drop表
TEST_F(ResDroTruCheFun, DDL_041_02_010)
{
    const char *resPoolNameString = "ResourcePoolTruncDrop";
    const char *resPoolDelTrunc =
        R"({
        "name" : "ResourcePoolTruncDrop",
        "pool_id" : 10010,
        "start_id" : 0,
        "capacity" : 90000,
        "order" : 1,
        "alloc_type" : 1
    })";

    int32_t ret = 0;
    uint64_t count = 40;
    int start_num = 0;
    int end_num = 2;
    char *test_schema = NULL;
    readJanssonFile("./schema_file/resource_drop_trunc_vtx.gmjson", &test_schema);
    ASSERT_NE((void *)NULL, test_schema);
    GmcDropVertexLabel(g_stmt_sync, gLabelName);
    // 异步插入准备
    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema, NULL, create_vertex_label_callback, &data);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE);
    if (ret != GMERR_OK) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);


    free(test_schema);

    // 创建资源池
    ret = GmcCreateResPool(g_stmt_sync, resPoolDelTrunc);
    EXPECT_EQ(GMERR_OK, ret);

    // 绑定资源池
    ret = GmcBindResPoolToLabel(g_stmt_sync, resPoolNameString, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 异步插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i, 0, (char *)"string", 0xFFFF, count, 0xFFFFFFFF);
        TestGmcSetNodePropertyByName_P(t1, i, 0, (char *)"string");
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 0, (char *)"string");
            ret = GmcNodeGetNextElement(t2, &t2);

            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 0, (char *)"string");
        }

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // scan须用同步
    TestGmcDirectFetchVertex(
        g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, true);

    // 异步update
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_P(t1, i, 2, (char *)"string2");

        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 2, (char *)"string2");
            ret = GmcNodeGetNextElement(t2, &t2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 2, (char *)"string2");
        }
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 异步merge
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_P(t1, i, 2, (char *)"string2");

        // 插入array节点
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 2, (char *)"string2");
            ret = GmcNodeGetNextElement(t2, &t2);
            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(t3, j, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 2, (char *)"string2");
        }
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(true, ret == GMERR_FEATURE_NOT_SUPPORTED || ret == GMERR_OK);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(true, data.status == GMERR_FEATURE_NOT_SUPPORTED || data.status == GMERR_OK);
        if (data.status != GMERR_OK) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.insertCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 异步merge走insert流程
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        GmcNodeT *root, *t1, *t2, *t3;
        ret = GmcGetRootNode(g_stmt_async, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &t1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &t3);
        EXPECT_EQ(GMERR_OK, ret);

        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_R(root, i, 0, (char *)"string", 0xFFFF, count, 0xFFFFFFFF);
        TestGmcSetNodePropertyByName_P(t1, i, 0, (char *)"string");
        ret = GmcNodeGetChild(t1, "T2", &t2);
        EXPECT_EQ(GMERR_OK, ret);

        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(t2, i, 0, (char *)"string");
            ret = GmcNodeGetNextElement(t2, &t2);

            if (j < array_num - 1) {
                EXPECT_EQ(GMERR_OK, ret);
            } else {
                EXPECT_EQ(GMERR_NO_DATA, ret);
            }
        }
        // 插入vector节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(t3, &t3);
            EXPECT_EQ(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(t3, i, 0, (char *)"string");
        }

        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 异步根据主键删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyId(g_stmt_async, 0);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.insertCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    // 解绑资源池
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // truncate资源表
    ret = GmcTruncateVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除资源池
    ret = GmcDestroyResPool(g_stmt_sync, resPoolNameString);
    EXPECT_EQ(GMERR_OK, ret);

// scan须用同步
#if RESOURCE_DROP_TRUNC_DEBUG3
    TestGmcDirectFetchVertex(
        g_stmt_sync, 1, 0, (char *)"string", start_num, end_num, array_num, vector_num, gLabelName, true);
#endif
    // 删除资源表
    ret = GmcDropVertexLabel(g_stmt_sync, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
}
