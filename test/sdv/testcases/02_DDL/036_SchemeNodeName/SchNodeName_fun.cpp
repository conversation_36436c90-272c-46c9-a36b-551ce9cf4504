/* ****************************************************************************
 Description  : 支持schema内node name唯一及基于node name访问(基本功能)
 Node         : 001.array的单个条件同步更新
                002.array的单个条件同步删除
                003.array的单个条件同步查询
                004.vector的单个条件同步删除
                005.vector的单个条件同步查询
                006.record节点的单个条件同步更新
                007.record节点的单个条件同步删除
                008.record节点的单个条件同步查询
                009.订阅record子节点的单个字段
                010.订阅record子节点的多个字段，条件为“and”
                011.订阅record子节点的多个字段，条件为“or”
                012.订阅根节点的多个字段
                013.订阅根节点、record子节点的多个字段

 Author       : lia<PERSON>iang lwx1036939
 Modification :
 Date         : 2021/09/11
**************************************************************************** */

extern "C" {}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "schemanodename.h"

GmcConnT *g_conn = NULL;
GmcConnT *g_conn_sub = NULL;
GmcStmtT *g_stmt = NULL, *g_stmt_sub = NULL;
char *schema1 = NULL;
char *schema2 = NULL;
char *sub_info = NULL;
char *g_sub_info = NULL;
char *pk_name1 = (char *)"OP_PK";
char labelname1[16] = "OP_TX";
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";

class SchemaNodeName_function : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;

    virtual void SetUp();
    virtual void TearDown();
};

void SchemaNodeName_function::SetUpTestCase()
{
    system(
        "sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\"");  // 2022.01.14 v5表带有superfield字段的需要修改配置项为0
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;

    ret = testEnvInit();
    ASSERT_EQ(GMERR_OK, ret);

    // 创建监听线程
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void SchemaNodeName_function::TearDownTestCase()
{
    int ret;

    // 关闭监听线程
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    GmcDetachAllShmSeg();
    testEnvClean();
}

void SchemaNodeName_function::SetUp()
{
    int ret = 0;
    user_data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(user_data, 0, sizeof(SnUserDataT));

    user_data->new_value = (int *)malloc(sizeof(int) * g_end_num * 10);
    memset(user_data->new_value, 0, sizeof(int) * g_end_num * 10);

    user_data->old_value = (int *)malloc(sizeof(int) * g_end_num * 10);
    memset(user_data->old_value, 0, sizeof(int) * g_end_num * 10);

    user_data->isReplace_insert = (bool *)malloc(sizeof(bool) * g_end_num * 10);
    memset(user_data->isReplace_insert, 0, sizeof(bool) * g_end_num * 10);

    // 建连
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    //创建订阅连接
    int chanRingLen = 256;
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void SchemaNodeName_function::TearDown()
{
    int32_t ret = 0;
    AW_CHECK_LOG_END();

    // 断链
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 断订阅链接
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);

    free(user_data->new_value);
    free(user_data->old_value);
    free(user_data->isReplace_insert);
    free(user_data);
}

// 001.array的单个条件同步更新
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_001)
{
    // 定义过滤条件 ( in 是有一个元素满足条件就成立，all 是所有元素都要满足条件。当只有一个元素的时候，in 和 all
    // 是一样的。多个元素之间用 “，”分隔)
    const char *cond =
        (const char *)"200 in(OP_TX.T2/A1)";  // A1字段有一个等于200就会过滤出来（过滤出来的是一个vertex）

    int32_t ret = 0;
    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    int i = 1;
    int affectRows = 0;
    bool bool_value = false;
    bool bool_value2 = true;
    bool isFinish = false;
    char *pk_name = (char *)"OP_PK";
    int update_value = 100;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL, *T3 = NULL;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data1
    TestGmcInsertVertex(g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1);
    // check data1
    TestGmcDirectFetchVertex(
        g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1, pk_name, true);

    // insert data2
    TestGmcInsertVertex(g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1);
    // check data2
    TestGmcDirectFetchVertex(
        g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1, pk_name, true);

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelname1, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);

    //获取根节点和子节点
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);

    TestGmcSetNodePropertyByName_P(
        T1, update_value, bool_value, test1);  // 过滤出来的是一个vertex，想更新什么节点就更新什么节点
    ret = GmcSetFilter(g_stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(1, affectRows);  // 判断是否更新成功

    // check更新
    TestGmcDirectFetchVertex(
        g_stmt, bool_value, test1, start_num1, end_num1, array_num, vector_num, labelname1, pk_name, true);

    // 读取更新的顶点
    int64_t f0_value = 199;

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelname1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);

    // 主键读
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, pk_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcFetch(g_stmt, &isFinish);
    ASSERT_EQ(GMERR_OK, ret);

    //获取根节点和子节点
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);

    // 读取普通节点
    TestGmcGetNodePropertyByName_R(root, f0_value, bool_value2, test2);
    TestGmcGetNodePropertyByName_P(T1, update_value, bool_value, test1);  // 值 已更新

    // 读取array节点
    for (uint32_t j = 0; j < array_num; j++) {
        int ret = GmcNodeGetElementByIndex(T2, j, &T2);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_A(T2, f0_value, bool_value2, test2);
    }

    // 读取 vector 节点
    for (uint32_t j = 0; j < vector_num; j++) {
        int ret = GmcNodeGetElementByIndex(T3, j, &T3);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcGetNodePropertyByName_V(T3, f0_value, bool_value2, test2);
    }

    TestGmcDirectFetchVertex(
        g_stmt, bool_value2, test2, start_num2, f0_value, array_num, vector_num, labelname1, pk_name, true);

    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 002.array的单个条件同步删除
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_002)
{
    // 定义过滤条件
    const char *cond = (const char *)"150.670 >= all(OP_TX.T2/A10)";

    int32_t ret = 0;
    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    int affectRows = 0;
    uint32_t len = 0;
    bool bool_value = false;
    bool bool_value2 = true;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data1
    TestGmcInsertVertex(g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1);
    // check data1
    TestGmcDirectFetchVertex(
        g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1, pk_name, true);

    // insert data2
    TestGmcInsertVertex(g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1);
    // check data2
    TestGmcDirectFetchVertex(
        g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1, pk_name, true);

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelname1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(140, affectRows);  // 判断是否删除成功

    // check
    TestGmcDirectFetchVertex(
        g_stmt, bool_value2, test2, 140, end_num2, array_num, vector_num, labelname1, pk_name, true);  // 读取成功
    TestGmcDirectFetchVertex(
        g_stmt, bool_value2, test2, 100, 140, array_num, vector_num, labelname1, pk_name, false);  // 读取失败
    TestGmcDirectFetchVertex(g_stmt, bool_value, test1, start_num1, end_num1, array_num, vector_num, labelname1,
        pk_name, false);  // 读取失败

    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 003.array的单个条件同步查询
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_003)
{
    // 定义过滤条件
    const char *cond = (const char *)"150.1 >= all(OP_TX.T2/A10)";  // A10属性值全都小于等于150.1 才会过滤出来

    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    int32_t ret = 0;
    int affectRows = 0;
    int i = 1;
    bool bool_value1 = false;
    bool bool_value2 = true;
    int fetchtimes = 0;
    bool isFinish = false;
    bool isNull = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL, *T3 = NULL;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data1
    TestGmcInsertVertex(g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1);
    // check data1
    TestGmcDirectFetchVertex(
        g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1, pk_name, true);

    // insert data2
    TestGmcInsertVertex(g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1);
    // check data2
    TestGmcDirectFetchVertex(
        g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1, pk_name, true);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelname1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond);  // 设置查询过滤条件。
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(g_stmt, NULL);  // 设置查询操作输出格式。
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // check
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);

        if (isFinish == true) {
            break;
        }

        fetchtimes++;
        int64_t f0_query = 0;

        //获取根节点和子节点
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_query, sizeof(int64_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ((unsigned int)0, isNull);

        if (f0_query >= start_num1 && f0_query < end_num1) {
            TestQueryPropertyByCond(g_stmt, f0_query, bool_value1, test1);
        } else if (f0_query >= start_num2 && f0_query < 140) {
            TestQueryPropertyByCond(g_stmt, f0_query, bool_value2, test2);
        } else {
            printf("%ld\n", f0_query);
            ASSERT_EQ(1, 0);
        }
    }

    ASSERT_EQ(140, fetchtimes);

    TestGmcDirectFetchVertex(
        g_stmt, bool_value2, test2, 140, end_num2, array_num, vector_num, labelname1, pk_name, true);

    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 004.vector的单个条件同步删除
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_004)
{
    // 定义过滤条件
    const char *cond = (const char *)"150.670 >= all(OP_TX.T3/V10)";

    int32_t ret = 0;
    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    int affectRows = 0;
    uint32_t len = 0;
    bool bool_value = false;
    bool bool_value2 = true;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data1
    TestGmcInsertVertex(g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1);
    // check data1
    TestGmcDirectFetchVertex(
        g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1, pk_name, true);

    // insert data2
    TestGmcInsertVertex(g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1);
    // check data2
    TestGmcDirectFetchVertex(
        g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1, pk_name, true);

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelname1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(140, affectRows);  // 判断是否删除成功

    // check
    TestGmcDirectFetchVertex(
        g_stmt, bool_value2, test2, 140, end_num2, array_num, vector_num, labelname1, pk_name, true);  // 读取成功
    TestGmcDirectFetchVertex(
        g_stmt, bool_value2, test2, 100, 140, array_num, vector_num, labelname1, pk_name, false);  // 读取失败
    TestGmcDirectFetchVertex(g_stmt, bool_value, test1, start_num1, end_num1, array_num, vector_num, labelname1,
        pk_name, false);  // 读取失败

    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.vector的单个条件同步查询
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_005)
{
    // 定义过滤条件
    const char *cond = (const char *)"150.670 >= all(OP_TX.T3/V10)";

    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    int affectRows = 0;
    int32_t ret = 0;
    int i = 1;
    bool bool_value1 = false;
    bool bool_value2 = true;
    int fetchtimes = 0;
    bool isFinish = false;
    bool isNull = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL, *T3 = NULL;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data1
    TestGmcInsertVertex(g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1);
    // check data1
    TestGmcDirectFetchVertex(
        g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1, pk_name, true);

    // insert data2
    TestGmcInsertVertex(g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1);
    // check data2
    TestGmcDirectFetchVertex(
        g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1, pk_name, true);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelname1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond);  // 设置查询过滤条件。
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(g_stmt, NULL);  // 设置查询操作输出格式。
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // check
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);

        if (isFinish == true) {
            break;
        }

        fetchtimes++;
        int64_t f0_query = 0;

        //获取根节点和子节点
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_query, sizeof(int64_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ((unsigned int)0, isNull);

        if (f0_query >= start_num1 && f0_query < end_num1) {
            TestQueryPropertyByCond(g_stmt, f0_query, bool_value1, test1);
        } else if (f0_query >= start_num2 && f0_query < 140) {
            TestQueryPropertyByCond(g_stmt, f0_query, bool_value2, test2);
        } else {
            printf("%ld\n", f0_query);
            ASSERT_EQ(1, 0);
        }
    }

    ASSERT_EQ(140, fetchtimes);

    TestGmcDirectFetchVertex(
        g_stmt, bool_value2, test2, 140, end_num2, array_num, vector_num, labelname1, pk_name, true);

    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 006.record节点的单个条件同步更新
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_006)
{
    // 定义过滤条件
    const char *cond = (const char *)"OP_TX.T1/P10 > 148.51";

    int32_t ret = 0;
    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    int i = 1;
    int affectRows = 0;
    bool bool_value = false;
    bool bool_value2 = true;
    bool isFinish = false;
    char *pk_name = (char *)"OP_PK";
    int update_value = 100;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL, *T3 = NULL;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data1
    TestGmcInsertVertex(g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1);
    // check data1
    TestGmcDirectFetchVertex(
        g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1, pk_name, true);

    // insert data2
    TestGmcInsertVertex(g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1);
    // check data2
    TestGmcDirectFetchVertex(
        g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1, pk_name, true);

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelname1, GMC_OPERATION_UPDATE);
    ASSERT_EQ(GMERR_OK, ret);

    //获取根节点和子节点
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(T1, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &T3);
    EXPECT_EQ(GMERR_OK, ret);

    TestGmcSetNodePropertyByName_P(T1, update_value, bool_value, test1);
    // 更新 array 节点
    for (uint32_t j = 0; j < array_num; j++) {
        ret = GmcNodeGetElementByIndex(T2, j, &T2);
        ASSERT_EQ(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_A(T2, update_value, bool_value, test1);
    }
    ret = GmcSetFilter(g_stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(62, affectRows);  // 判断是否更新成功

    // check更新
    TestGmcDirectFetchVertex(
        g_stmt, bool_value, test1, start_num1, end_num1, array_num, vector_num, labelname1, pk_name, true);

    // 读取更新的顶点
    for (int i = 138; i < end_num2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelname1, GMC_OPERATION_SCAN);
        ASSERT_EQ(GMERR_OK, ret);

        int64_t f0_value = i;

        // 主键读
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, pk_name);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        ASSERT_EQ(GMERR_OK, ret);

        ret = GmcFetch(g_stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);

        //获取根节点和子节点
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        // 读取普通节点
        TestGmcGetNodePropertyByName_R(root, f0_value, bool_value2, test2);
        TestGmcGetNodePropertyByName_P(T1, update_value, bool_value, test1);  // 值 已更新

        // 读取array节点
        for (uint32_t j = 0; j < array_num; j++) {
            int ret = GmcNodeGetElementByIndex(T2, j, &T2);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_A(T2, update_value, bool_value, test1);  // 值 已更新
        }

        // 读取 vector 节点
        for (uint32_t j = 0; j < vector_num; j++) {
            int ret = GmcNodeGetElementByIndex(T3, j, &T3);
            ASSERT_EQ(GMERR_OK, ret);
            TestGmcGetNodePropertyByName_V(T3, f0_value, bool_value2, test2);
        }
    }
    TestGmcDirectFetchVertex(
        g_stmt, bool_value2, test2, start_num2, 138, array_num, vector_num, labelname1, pk_name, true);

    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 007.record节点的单个条件同步删除
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_007)
{
    // 定义过滤条件
    const char *cond = (const char *)"OP_TX.T1/P14 <= 'testvf'";

    int32_t ret = 0;
    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    int affectRows = 0;
    uint32_t len = 0;
    bool bool_value = false;
    bool bool_value2 = true;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data1
    TestGmcInsertVertex(g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1);
    // check data1
    TestGmcDirectFetchVertex(
        g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1, pk_name, true);

    // insert data2
    TestGmcInsertVertex(g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1);
    // check data2
    TestGmcDirectFetchVertex(
        g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1, pk_name, true);

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelname1, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(100, affectRows);  // 判断是否删除成功

    // check
    TestGmcDirectFetchVertex(g_stmt, bool_value2, test2, start_num2, end_num2, array_num, vector_num, labelname1,
        pk_name, true);  // 读取成功
    TestGmcDirectFetchVertex(g_stmt, bool_value, test1, start_num1, end_num1, array_num, vector_num, labelname1,
        pk_name, false);  // 读取失败

    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 008.record节点的单个条件同步查询
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_008)
{
    // 定义过滤条件
    const char *cond = (const char *)"142 >= all(OP_TX.T1/P2)";

    char *test1 = (char *)"testve";
    char *test2 = (char *)"vetest";
    int affectRows = 0;
    int i = 1;
    int32_t ret = 0;
    bool bool_value1 = false;
    bool bool_value2 = true;
    int fetchtimes = 0;
    bool isFinish = false;
    bool isNull = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL, *T3 = NULL;

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data1
    TestGmcInsertVertex(g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1);
    // check data1
    TestGmcDirectFetchVertex(
        g_stmt, false, (char *)"testve", start_num1, end_num1, array_num, vector_num, labelname1, pk_name, true);

    // insert data2
    TestGmcInsertVertex(g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1);
    // check data2
    TestGmcDirectFetchVertex(
        g_stmt, true, (char *)"vetest", start_num2, end_num2, array_num, vector_num, labelname1, pk_name, true);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelname1, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetFilter(g_stmt, cond);  // 设置查询过滤条件。
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetOutputFormat(g_stmt, NULL);  // 设置查询操作输出格式。
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // check
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);

        if (isFinish == true) {
            break;
        }

        fetchtimes++;
        int64_t f0_query = 0;

        //获取根节点和子节点
        ret = GmcGetRootNode(g_stmt, &root);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &T1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(T1, "T2", &T2);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &T3);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_query, sizeof(int64_t), &isNull);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ((unsigned int)0, isNull);

        if (f0_query >= start_num1 && f0_query < end_num1) {
            TestQueryPropertyByCond(g_stmt, f0_query, bool_value1, test1);
        } else if (f0_query >= start_num2 && f0_query < 141) {
            TestQueryPropertyByCond(g_stmt, f0_query, bool_value2, test2);
        } else {
            printf("%ld\n", f0_query);
            ASSERT_EQ(1, 0);
        }
    }

    ASSERT_EQ(141, fetchtimes);

    TestGmcDirectFetchVertex(
        g_stmt, bool_value2, test2, 141, end_num2, array_num, vector_num, labelname1, pk_name, true);

    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 009.订阅record子节点的单个字段
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_009)
{
    int32_t ret = 0, i, userDataIdx = 0;
    char string1[] = "string1";
    char string2[] = "1234567";

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建订阅条件
    readJanssonFile("./schema_file/TreeModel_subInfo_001_001.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);

    GmcSubConfigT tmp_g_sub_info;  // 订阅关系的配置Json的结构体。
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = sub_info;
    // 创建订阅关系
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data
    for (int i = start_num1; i < end_num1; i++) {
        if (isMatchPushCond(i)) {
            ((int *)(user_data->new_value))[userDataIdx] = i;  // 在回调里校验推送的数据值
            userDataIdx++;
        }
    }
    TestGmcInsertVertex(g_stmt, false, string1, start_num1, end_num1, array_num, vector_num, labelname1);

    // query
    TestGmcDirectFetchVertex(
        g_stmt, false, string1, start_num1, end_num1, array_num, vector_num, labelname1, pk_name1, true);

    // 等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    free(sub_info);
    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 010.订阅record子节点的多个字段，条件为“and”
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_010)
{
    int32_t ret = 0, i, userDataIdx = 0;
    char string1[] = "string1";
    char string2[] = "1234567";

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建订阅条件
    readJanssonFile("./schema_file/TreeModel_subInfo_001_002.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);

    GmcSubConfigT tmp_g_sub_info;  // 订阅关系的配置Json的结构体。
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = sub_info;
    // 创建订阅关系
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data
    for (int i = start_num1; i < end_num1; i++) {
        if (isMatchPushCond_001(i)) {
            ((int *)(user_data->new_value))[userDataIdx] = i;  // 在回调里校验推送的数据值
            userDataIdx++;
        }
    }
    TestGmcInsertVertex(g_stmt, false, string1, start_num1, end_num1, array_num, vector_num, labelname1);

    // query
    TestGmcDirectFetchVertex(
        g_stmt, false, string1, start_num1, end_num1, array_num, vector_num, labelname1, pk_name1, true);

    // 等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    free(sub_info);
    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 011.订阅record子节点的多个字段，条件为“or”
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_011)
{
    int32_t ret = 0, i, userDataIdx = 0;
    char string1[] = "string1";
    char string2[] = "1234567";

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建订阅条件
    readJanssonFile("./schema_file/TreeModel_subInfo_001_003.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);

    GmcSubConfigT tmp_g_sub_info;  // 订阅关系的配置Json的结构体。
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = sub_info;
    // 创建订阅关系
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data
    for (int i = start_num1; i < end_num1; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }
    TestGmcInsertVertex(g_stmt, false, string1, start_num1, end_num1, array_num, vector_num, labelname1);

    // query
    TestGmcDirectFetchVertex(
        g_stmt, false, string1, start_num1, end_num1, array_num, vector_num, labelname1, pk_name1, true);

    // 等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, end_num1);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    free(sub_info);
    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 012.订阅根节点的多个字段
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_012)
{
    int32_t ret = 0, i, userDataIdx = 0;
    char string1[] = "string1";
    char string2[] = "1234567";

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建订阅条件
    readJanssonFile("./schema_file/TreeModel_subInfo_001_004.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);

    GmcSubConfigT tmp_g_sub_info;  // 订阅关系的配置Json的结构体。
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = sub_info;
    // 创建订阅关系
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data
    TestGmcInsertVertex(g_stmt, false, string1, start_num1, end_num1, array_num, vector_num, labelname1);

    // query
    TestGmcDirectFetchVertex(
        g_stmt, false, string1, start_num1, end_num1, array_num, vector_num, labelname1, pk_name1, true);

    // 等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 1);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    free(sub_info);
    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 013.订阅根节点、record子节点的多个字段
TEST_F(SchemaNodeName_function, DDL_036_SchNodeName_fun_013)
{
    int32_t ret = 0, i, userDataIdx = 0;
    char string1[] = "string1";
    char string2[] = "1234567";

    // create vertexlabel
    GmcDropVertexLabel(g_stmt, labelname1);
    readJanssonFile("./schema_file/TreeModelSchema_vector.gmjson", &schema1);
    ASSERT_NE((void *)NULL, schema1);
    ret = GmcCreateVertexLabel(g_stmt, schema1, NULL);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建订阅条件
    readJanssonFile("./schema_file/TreeModel_subInfo_001_005.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);

    GmcSubConfigT tmp_g_sub_info;  // 订阅关系的配置Json的结构体。
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = sub_info;
    // 创建订阅关系
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback, user_data);
    ASSERT_EQ(GMERR_OK, ret);

    // insert data
    TestGmcInsertVertex(g_stmt, false, string1, start_num1, end_num1, array_num, vector_num, labelname1);

    // query
    TestGmcDirectFetchVertex(
        g_stmt, false, string1, start_num1, end_num1, array_num, vector_num, labelname1, pk_name1, true);

    // 等待insert事件推送完成
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除订阅关系
    ret = GmcUnSubscribe(g_stmt, g_subName);
    EXPECT_EQ(GMERR_OK, ret);

    free(sub_info);
    free(schema1);

    // drop vertexlabel
    ret = GmcDropVertexLabel(g_stmt, labelname1);
    EXPECT_EQ(GMERR_OK, ret);
}
