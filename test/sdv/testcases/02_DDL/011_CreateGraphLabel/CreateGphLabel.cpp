extern "C" {
}
#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "t_datacom_lite.h"

/*****************************************************************************
 Description  : 支持Yang转化的模型创建lable的能力，接口GmcCreateGraphLabel
 Notes        :
 History      :
 Author       : qinqianbao qwx995465
 Modification :
*****************************************************************************/
GmcConnT *conn;
GmcStmtT *stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;
#define SCHEMA_JSON_SIZE 1024
int affectRows = 0;
unsigned int len = 0;
const char *g_label_name = "vsys";
const char *g_label_name1 = "vsys/rule";
const char *g_label_name2 = "vsys/rule/source_ip";
const char *g_cfgJson = R"({"max_record_count":1000000})";
char *labelJson_040 = NULL;
char *normal_graph_edge_label_schema = NULL;
char *g_labelJson = NULL;
char *g_labelJsonTest = NULL;
char *g_labelJsonTestEdge = NULL;
char Label_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
class CreateGphLabel : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ChangeGmserverCfg((char *)"recover", NULL));
        system("sh $TEST_HOME/tools/stop.sh -f");
    }
    virtual void SetUp();
    virtual void TearDown();
};

void CreateGphLabel::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    // 创建客户端连接
    g_labelJsonTest = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    // char *labelJson_040 = NULL;
    // char *edgelabeljson_040 = NULL;
    readJanssonFile("schemaFile/thread_040_GraphVertexLabel.gmjson", &labelJson_040);
    EXPECT_NE((void *)NULL, labelJson_040);
    readJanssonFile("schemaFile/thread1_GraphVertexLabel.gmjson", &g_labelJsonTest);
    EXPECT_NE((void *)NULL, g_labelJsonTest);
    readJanssonFile("schemaFile/thread1_EdgeLabel.gmjson", &g_labelJsonTestEdge);
    EXPECT_NE((void *)NULL, g_labelJsonTestEdge);
    AW_CHECK_LOG_BEGIN();
}

void CreateGphLabel::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();
    free(labelJson_040);
    free(g_labelJsonTest);
    free(g_labelJsonTestEdge);
    // 关闭 client connection
    int ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

void normal_insert_layer1(GmcStmtT *stmtVsys, int v1_id)
{
    void *vsysLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtVsys, "vsys", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert
    ret = GmcSetVertexProperty(stmtVsys, "id", GMC_DATATYPE_INT32, &v1_id, sizeof(v1_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtVsys, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}
void normal_insert_layer2(GmcStmtT *stmtRule, int v2_vsys_id, int v2_id, void *v2_name)
{
    void *ruleLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtRule, "vsys::rule", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // insert
    ret = GmcSetVertexProperty(stmtRule, "vsys::id", GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(v2_vsys_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "id", GMC_DATATYPE_INT32, &v2_id, sizeof(v2_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "name", GMC_DATATYPE_STRING, v2_name, strlen((const char *)v2_name));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtRule, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}
void normal_insert_layer3(GmcStmtT *stmtS_ip, int v3_vsys_id, int v3_rule_id, int v3_ipLen, int v3_masklen)
{
    void *s_ipLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtS_ip, "vsys::rule::source_ip", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入insert
    ret = GmcSetVertexProperty(stmtS_ip, "rule::vsys::id", GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(v3_vsys_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "rule::id", GMC_DATATYPE_INT32, &v3_rule_id, sizeof(v3_rule_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "ipLen", GMC_DATATYPE_INT32, &v3_ipLen, sizeof(v3_ipLen));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "maskLen", GMC_DATATYPE_INT32, &v3_masklen, sizeof(v3_masklen));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtS_ip, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}
void normal_delete_layer1(GmcStmtT *stmtVsys, int v1_id)
{
    void *vsysLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtVsys, "vsys", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmtVsys, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(v1_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtVsys, "id");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtVsys, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}
void normal_delete_layer2(GmcStmtT *stmtRule, int v2_vsys_id, int v2_id)
{
    void *ruleLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtRule, "vsys::rule", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtRule, 0, GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(v2_vsys_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtRule, 1, GMC_DATATYPE_INT32, &v2_id, sizeof(v2_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtRule, "vsys::id_id");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtRule, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

void normal_delete_layer3(GmcStmtT *stmtS_ip, int v3_vsys_id, int v3_rule_id, int v3_ipLen, int v3_masklen)
{
    void *s_ipLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtS_ip, "vsys::rule::source_ip", GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(v3_vsys_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 1, GMC_DATATYPE_INT32, &v3_rule_id, sizeof(v3_rule_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 2, GMC_DATATYPE_INT32, &v3_ipLen, sizeof(v3_ipLen));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 3, GMC_DATATYPE_INT32, &v3_masklen, sizeof(v3_masklen));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtS_ip, "vsys.rule.source_ip_K0");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtS_ip, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 01.GmcCreateGraphLabel接口入参正常；预期创建成功，drop成功
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_001)
{
    void *vertexLabel1 = NULL;
    void *vertexLabel2 = NULL;
    void *vertexLabel3 = NULL;
    GmcStmtT *stmtVsys = NULL;
    int ret = GmcAllocStmt(conn, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);

    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_labelJson);

    // 创建批量边
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(normal_graph_edge_label_schema);
    /* *构造数据v1 ~ v7* */
    // 先查询元数据
    void *vsysLabel;
    ret = testGmcPrepareStmtByLabelName(stmtVsys, "vsys", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    void *ruleLabel;
    ret = testGmcPrepareStmtByLabelName(stmtRule, "vsys::rule", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    void *s_ipLabel;
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, "vsys::rule::source_ip", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert V1 V2 V3
    int v1_id = 1;
    normal_insert_layer1(stmtVsys, v1_id);
    int v2_vsys_id = 1;
    int v2_id = 1;
    char v2_name[] = "hei";
    normal_insert_layer2(stmtRule, v2_vsys_id, v2_id, &v2_name);
    int v3_vsys_id = 1;
    int v3_rule_id = 1;
    int v3_ipLen = 1;
    int v3_masklen = 1;
    normal_insert_layer3(stmtS_ip, v3_vsys_id, v3_rule_id, v3_ipLen, v3_masklen);

    // 插入v5的值
    int v5_vsys_id = v1_id;
    int v5_id = 3;
    char v5_name[] = "hahaha";
    normal_insert_layer2(stmtRule, v5_vsys_id, v5_id, &v5_name);

    // 插入v4的值
    int v4_vsys_id = v1_id;
    int v4_rule_id = v2_id;
    int v4_ipLen = 24;
    int v4_masklen = 24;
    normal_insert_layer3(stmtS_ip, v4_vsys_id, v4_rule_id, v4_ipLen, v4_masklen);

    // 插入v6的值
    int v6_vsys_id = v1_id;
    int v6_rule_id = v5_id;
    int v6_ipLen = 25;
    int v6_masklen = 25;
    normal_insert_layer3(stmtS_ip, v6_vsys_id, v6_rule_id, v6_ipLen, v6_masklen);

    // 插入v7的值
    int v7_vsys_id = v1_id;
    int v7_rule_id = v5_id;
    int v7_ipLen = 26;
    int v7_masklen = 26;
    normal_insert_layer3(stmtS_ip, v7_vsys_id, v7_rule_id, v7_ipLen, v7_masklen);

    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);

    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
}
// 02.GmcCreateGraphLabel接口参数labelJson为NULL；数据插入失败
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_002)
{
    char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    int ret;
    void *vertexLabel1 = NULL;

    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);

    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphVertexLabel(stmt, NULL, Label_config);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建批量边
    ret = GmcCreateGraphEdgeLabel(stmt, NULL, NULL);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_labelJson);
    free(normal_graph_edge_label_schema);

    // 加以验证创建失败
    ret = testGmcPrepareStmtByLabelName(stmt, "T0", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 03.GmcCreateGraphLabel接口参数stmt为NULL；
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_003)
{
    char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    int ret;
    void *vertexLabel1 = NULL;

    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);

    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphVertexLabel(NULL, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    free(normal_graph_edge_label_schema);
    // 加以验证创建label失败
    ret = testGmcPrepareStmtByLabelName(stmt, "vsys", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_004_01(void *args)
{
    int ret;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtVsys = NULL;
    ret = GmcAllocStmt(conn_async, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn_async, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn_async, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);

    AsyncUserDataT data = {0};
    for (int i = 0; i < 1000; i++) {
        //
        int v1_id = i + 1;
        ret = testGmcPrepareStmtByLabelName(stmtVsys, "vsys", GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        // replace 第一层
        ret = GmcSetVertexProperty(stmtVsys, "id", GMC_DATATYPE_INT32, &v1_id, sizeof(v1_id));
        EXPECT_EQ(GMERR_OK, ret);
        data = {0};
        GmcAsyncRequestDoneContextT replaceRequestCtx;
        replaceRequestCtx.replaceCb = replace_vertex_callback;
        replaceRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmtVsys, &replaceRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int i = 0; i < 1000; i++) {
        int v2_vsys_id = i + 1;
        int v2_id = i + 1;
        char v2_name[128] = "";
        sprintf(v2_name, "hei_%i", i);
        int ret = testGmcPrepareStmtByLabelName(stmtRule, "vsys::rule", GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        // merge 第二层
        ret = GmcSetIndexKeyValue(stmtRule, 0, GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtRule, 1, GMC_DATATYPE_INT32, &v2_id, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtRule, "vsys::id_id");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtRule, "name", GMC_DATATYPE_STRING, v2_name, strlen((const char *)v2_name));
        EXPECT_EQ(GMERR_OK, ret);
        data = {0};
        GmcAsyncRequestDoneContextT mergeRequestCtx;
        mergeRequestCtx.mergeCb = merge_vertex_callback;
        mergeRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmtRule, &mergeRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }
    for (int i = 0; i < 1000; i++) {
        // data = {0};
        // int v3_vsys_id = 1;
        // int v3_rule_id = 1;
        // int v3_ipLen = 1;
        // int v3_masklen = 1;
        // int i = 1;
        ret = testGmcPrepareStmtByLabelName(stmtS_ip, "vsys::rule::source_ip", GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        // update 第三层
        ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtS_ip, 1, GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtS_ip, 2, GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtS_ip, 3, GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtS_ip, "vsys.rule.source_ip_K0");
        EXPECT_EQ(GMERR_OK, ret);
        data = {0};
        GmcAsyncRequestDoneContextT updateRequestCtx;
        updateRequestCtx.updateCb = update_vertex_callback;
        updateRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmtS_ip, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
    }

    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 04.创建graphLabel, 异步连接下多线程replace、merge、update数据
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_004)
{
    void *vertexLabel1 = NULL;
    void *vertexLabel2 = NULL;
    void *vertexLabel3 = NULL;
    GmcStmtT *stmtVsys = NULL;
    int ret = GmcAllocStmt(conn, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);

    // readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    // EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    // 创建批量边
    // ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, NULL);
    // EXPECT_EQ(GMERR_OK, ret);  // 备注：B020版本解决建边死锁问题
    // free(normal_graph_edge_label_schema);
    /* *构造数据v1 ~ v7* */
    // 先查询元数据
    void *vsysLabel;
    ret = testGmcPrepareStmtByLabelName(stmtVsys, "vsys", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    void *ruleLabel;
    ret = testGmcPrepareStmtByLabelName(stmtRule, "vsys::rule", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    void *s_ipLabel;
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, "vsys::rule::source_ip", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert V1 V2 V3
    int v1_id = 1;
    normal_insert_layer1(stmtVsys, v1_id);
    int v2_vsys_id = 1;
    int v2_id = 1;
    char v2_name[] = "hei";
    normal_insert_layer2(stmtRule, v2_vsys_id, v2_id, &v2_name);
    for (int i = 0; i < 1000; i++) {
        int v3_vsys_id = i;
        int v3_rule_id = i;
        int v3_ipLen = i;
        int v3_masklen = i;
        normal_insert_layer3(stmtS_ip, v3_vsys_id, v3_rule_id, v3_ipLen, v3_masklen);
    }

    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);

    int tdNum = 2;
    pthread_t thr_arr[tdNum];
    for (int i = 0; i < tdNum; i++) {
        ret = pthread_create(&thr_arr[i], NULL, thread_004_01, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < tdNum; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    ret = GmcDropGraphLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule::source_ip");
    EXPECT_EQ(GMERR_OK, ret);
}

// 05.GmcCreateGraphLabel重复创建, 预期create成功,第二次失败, drop成功
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_005)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    int ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_labelJson);

    ret = GmcDropGraphLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule::source_ip");
    EXPECT_EQ(GMERR_OK, ret);
}

// 06.创建graphLabel, 更新三层数据
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_006)
{
    void *vertexLabel1 = NULL;
    void *vertexLabel2 = NULL;
    void *vertexLabel3 = NULL;
    GmcStmtT *stmtVsys = NULL;
    int ret = GmcAllocStmt(conn, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/NormalGraphVertexLabel006.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // printf("%s\n", g_labelJson);
    readJanssonFile("schemaFile/NormalGraphEdgeLabel006.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    // 创建label
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    // 创建批量边
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);  // 可以先不建边 B020版本有自动建边需求
    free(normal_graph_edge_label_schema);

    int count = 100;
    // insert V1
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(stmtVsys, "a1", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtVsys, "F0", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtVsys, "F1", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtVsys, "F2", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtVsys);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // insert V2
    for (int i = 0; i < count; i++) {
        char a2_F2[128] = "";
        snprintf(a2_F2, 128, "hei_%d", i);
        ret = testGmcPrepareStmtByLabelName(stmtRule, "a2", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtRule, "F0", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtRule, "F1", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtRule, "F2", GMC_DATATYPE_STRING, a2_F2, strlen(a2_F2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtRule);
        EXPECT_EQ(GMERR_OK, ret);
        // free(a2_F2);
    }
    // insert V3
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(stmtS_ip, "a3", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtS_ip, "F0", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtS_ip, "F1", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtS_ip, "F2", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtS_ip);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("insert value: %d\n", i);
    }

    // update V1
    for (int i = 0; i < count; i++) {
        int new_value = count + i;
        ret = testGmcPrepareStmtByLabelName(stmtVsys, "a1", GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtVsys, 0, GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtVsys, "F1", GMC_DATATYPE_INT32, &new_value, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtVsys, "F2", GMC_DATATYPE_INT32, &new_value, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtVsys, "pk1");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtVsys);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // update V2
    for (int i = 0; i < count; i++) {
        int new_value = count + i;
        char a2_F2[128] = "";
        snprintf(a2_F2, 128, "hei_%d", new_value);
        ret = testGmcPrepareStmtByLabelName(stmtRule, "a2", GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtRule, 0, GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtRule, "F1", GMC_DATATYPE_INT32, &new_value, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtRule, "F2", GMC_DATATYPE_STRING, a2_F2, strlen(a2_F2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtRule, "pk2");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtRule);
        EXPECT_EQ(GMERR_OK, ret);
        // free(a2_F2);
    }
    // update V3
    for (int i = 0; i < count; i++) {
        int new_value = count + i;
        ret = testGmcPrepareStmtByLabelName(stmtS_ip, "a3", GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtS_ip, "F1", GMC_DATATYPE_INT32, &new_value, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtS_ip, "F2", GMC_DATATYPE_INT32, &new_value, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtS_ip, "pk3");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtS_ip);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("update value: %d\n", new_value);
    }

    // query V1
    for (int i = 0; i < count; i++) {
        int new_value = count + i;
        ret = testGmcPrepareStmtByLabelName(stmtVsys, "a1", GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtVsys, 0, GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtVsys, "pk1");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtVsys);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmtVsys, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        int f1;
        bool isNull = 1;
        ret = GmcGetVertexPropertyByName(stmtVsys, "F1", &f1, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, 0);
        EXPECT_EQ(new_value, f1);
    }
    // query V2
    for (int i = 0; i < count; i++) {
        int new_value = count + i;
        ret = testGmcPrepareStmtByLabelName(stmtRule, "a2", GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtRule, 0, GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtRule, "pk2");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtRule);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmtRule, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        bool isNull = 1;
        char v2nameExpectValue[128] = "";
        // memset(v2nameExpectValue, 0, sizeof(v2nameExpectValue));
        snprintf(v2nameExpectValue, 128, "hei_%d", new_value);
        unsigned int size_v2name;
        ret = GmcGetVertexPropertySizeByName(stmtRule, "F2", &size_v2name);
        EXPECT_EQ(GMERR_OK, ret);
        char *v2nameValue = (char *)malloc(size_v2name);
        ret = GmcGetVertexPropertyByName(stmtRule, "F2", v2nameValue, size_v2name, &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ((unsigned int)0, isNull);
        ret = strncmp(v2nameExpectValue, v2nameValue, strlen(v2nameExpectValue));  // 检测更新后的
        EXPECT_EQ(GMERR_OK, ret);
        // printf("1:%s\n 2:%s\n", v2nameExpectValue, v2nameValue);
        free(v2nameValue);
        // free(v2nameExpectValue);
    }
    // query V3
    for (int i = 0; i < count; i++) {
        int new_value = count + i;
        ret = testGmcPrepareStmtByLabelName(stmtS_ip, "a3", GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtS_ip, "pk3");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtS_ip);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmtS_ip, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        int f1;
        bool isNull = 1;
        ret = GmcGetVertexPropertyByName(stmtS_ip, "F1", &f1, sizeof(int), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, 0);
        EXPECT_EQ(new_value, f1);  //
        // printf("query V3 new_value %d\n GetVertexPropertyByName: %d\n", new_value, f1);
    }

    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);

    ret = GmcDropGraphLabel(stmt, "a1");
    EXPECT_EQ(GMERR_OK, ret);

    // ret = GmcDropGraphLabel(stmt, "a1");
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcDropGraphLabel(stmt, "a2");
    // EXPECT_EQ(GMERR_OK, ret);
    // ret = GmcDropGraphLabel(stmt, "a3");
    // EXPECT_EQ(GMERR_OK, ret);
}

// 07.设置pk不为空, 设置"nullable":true 也不影响; 预期label创建成功
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_007)
{
    char *labelJson_nopk = NULL;

    readJanssonFile("schemaFile/NullabePK_GraphVertexLabel.gmjson", &labelJson_nopk);
    EXPECT_NE((void *)NULL, labelJson_nopk);

    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    int ret = GmcCreateGraphVertexLabel(stmt, labelJson_nopk, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);  //
    free(labelJson_nopk);
    free(normal_graph_edge_label_schema);

    ret = GmcDropGraphLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 08.设置非PK、MK的其他形式keys；比如type Unique key,预期失创建败
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_008)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *labelJson_nopk = NULL;
    void *vertexLabel1 = NULL;
    void *vertexLabel2 = NULL;
    void *vertexLabel3 = NULL;
    GmcStmtT *stmtVsys = NULL;
    int ret = GmcAllocStmt(conn, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/UniquePK_GraphVertexLabel.gmjson", &labelJson_nopk);
    EXPECT_NE((void *)NULL, labelJson_nopk);
    // printf("%s\n", labelJson_nopk);
    ret = GmcCreateGraphVertexLabel(stmt, labelJson_nopk, NULL);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(labelJson_nopk);
}

// 09 schema包含全类型, 创建graphVertex
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_009)
{
    char *labelJson = NULL;
    readJanssonFile("schemaFile/GraphVertexLabelAllType.gmjson", &labelJson);
    EXPECT_NE((void *)NULL, labelJson);
    readJanssonFile("schemaFile/GraphEdgeLabel009.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    int ret = GmcCreateGraphVertexLabel(stmt, labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);  // 暂不加入resource字段，原因droplabel时返回5001
    // ret=testGmcGetLastError(NULL);
    // EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);  //

    free(labelJson);
    free(normal_graph_edge_label_schema);
    ret = GmcDropGraphLabel(stmt, "a1");
    EXPECT_EQ(GMERR_OK, ret);
}

// 10.设置根节点到子节点路径上的name的长度之和等于128的字节, 预期创建成功、drop成功
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_010)
{
    char *labelJson_128 = NULL;
    readJanssonFile("schemaFile/len128_GraphVertexLabel.gmjson", &labelJson_128);
    EXPECT_NE((void *)NULL, labelJson_128);
    readJanssonFile("schemaFile/len128_NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    int ret = GmcCreateGraphVertexLabel(stmt, labelJson_128, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);  //

    free(labelJson_128);
    free(normal_graph_edge_label_schema);
    ret = GmcDropGraphLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
}

// 11.设置根节点到子节点路径上的name的长度之和等于129的字节,  预期创建失败
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_011)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    // schema设置的name过长
    AW_ADD_TRUNCATION_WHITE_LIST(1, "name len");

    char *labelJson_129 = NULL;
    readJanssonFile("schemaFile/Len129_GraphVertexLabel.gmjson", &labelJson_129);
    EXPECT_NE((void *)NULL, labelJson_129);

    int ret = GmcCreateGraphVertexLabel(stmt, labelJson_129, NULL);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(labelJson_129);
}

// 12.查询vertexLabel的名称为“父节点/子节点”,其中’/’表示label的层次间隔：使用&或者其他符号链接
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_012)
{
    char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    char *labelJson_sep = NULL;
    GmcStmtT *stmtVsys = NULL;
    int ret = GmcAllocStmt(conn, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/invaidNode__GraphVertexLabel.gmjson", &labelJson_sep);
    EXPECT_NE((void *)NULL, labelJson_sep);
    ret = GmcCreateGraphVertexLabel(stmt, labelJson_sep, NULL);
    EXPECT_EQ(GMERR_INVALID_NAME, ret);  //
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(labelJson_sep);
    /* *构造数据v1 ~ v7* */
    // 先查询元数据
    void *vsysLabel;
    ret = testGmcPrepareStmtByLabelName(stmtVsys, "vsys", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmtVsys);
}

// 13 创建label、数据插入成功、drop成功、重新创建label ok, 最后执行drop  ok
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_013)
{
    char *All_type_json = NULL;
    GmcStmtT *stmtVsys = NULL;
    int ret = GmcAllocStmt(conn, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建批量点
    readJanssonFile("schemaFile/AllTypeGraphVertexLabel.gmjson", &All_type_json);
    EXPECT_NE((void *)NULL, All_type_json);

    ret = GmcCreateGraphVertexLabel(stmt, All_type_json, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(All_type_json);
    /* *构造数据v1 ~ v7* */
    // 先查询元数据
    void *vsysLabel;
    ret = testGmcPrepareStmtByLabelName(stmtVsys, "vsys", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    void *ruleLabel;
    ret = testGmcPrepareStmtByLabelName(stmtRule, "vsys::rule", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    void *s_ipLabel;
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, "vsys::rule::source_ip", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // insert V1 V2 V3
    int v1_id = 1;
    normal_insert_layer1(stmtVsys, v1_id);
    int v2_vsys_id = 1;
    int v2_id = 1;
    char v2_name[] = "hei";
    normal_insert_layer2(stmtRule, v2_vsys_id, v2_id, &v2_name);

    // 插入v3的值
    int v3_vsys_id = v1_id;
    int v3_rule_id = v2_id;
    int v3_ipLen = 23;
    int v3_masklen = 23;
    ret = GmcSetVertexProperty(stmtS_ip, "rule::vsys::id", GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(v3_vsys_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "rule::id", GMC_DATATYPE_INT32, &v3_rule_id, sizeof(v3_rule_id));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "ipLen", GMC_DATATYPE_INT32, &v3_ipLen, sizeof(v3_ipLen));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "maskLen", GMC_DATATYPE_INT32, &v3_masklen, sizeof(v3_masklen));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr1 = 'a';
    ret = GmcSetVertexProperty(stmtS_ip, "H0", GMC_DATATYPE_CHAR, &teststr1, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr2 = 'b';
    ret = GmcSetVertexProperty(stmtS_ip, "H1", GMC_DATATYPE_UCHAR, &teststr2, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t vaule1 = 1;
    ret = GmcSetVertexProperty(stmtS_ip, "H2", GMC_DATATYPE_INT8, &vaule1, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value2 = 10;
    ret = GmcSetVertexProperty(stmtS_ip, "H3", GMC_DATATYPE_UINT8, &value2, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value3 = 100;
    ret = GmcSetVertexProperty(stmtS_ip, "H4", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value4 = 1000;
    ret = GmcSetVertexProperty(stmtS_ip, "H5", GMC_DATATYPE_UINT16, &value4, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value5 = 1000;
    ret = GmcSetVertexProperty(stmtS_ip, "H6", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value6 = 1000;
    ret = GmcSetVertexProperty(stmtS_ip, "H7", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool value7 = 1;
    ret = GmcSetVertexProperty(stmtS_ip, "H8", GMC_DATATYPE_BOOL, &value7, sizeof(value7));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value8 = 1000;
    ret = GmcSetVertexProperty(stmtS_ip, "H9", GMC_DATATYPE_INT64, &value8, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value9 = 1000;
    ret = GmcSetVertexProperty(stmtS_ip, "H10", GMC_DATATYPE_UINT64, &value9, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value10 = 1.2;
    ret = GmcSetVertexProperty(stmtS_ip, "H11", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value11 = 10.86;
    ret = GmcSetVertexProperty(stmtS_ip, "H12", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value12 = 1000;
    ret = GmcSetVertexProperty(stmtS_ip, "H13", GMC_DATATYPE_TIME, &value12, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr3[] = "testver";
    ret = GmcSetVertexProperty(stmtS_ip, "H14", GMC_DATATYPE_STRING, teststr3, (strlen(teststr3)));
    EXPECT_EQ(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmtS_ip, "H15", GMC_DATATYPE_BYTES, F15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmtS_ip, "H16", GMC_DATATYPE_FIXED, F16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    //插入顶点
    ret = GmcExecute(stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtS_ip, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);

    ret = GmcDropGraphLabel(stmt, g_label_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule::source_ip");
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_sub_view_1(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    char *labelJson1 = NULL;

    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/thread1_GraphVertexLabel.gmjson", &labelJson1);
    EXPECT_NE((void *)NULL, labelJson1);

    for (int i = 0; i < 100; i++) {
        ret = GmcCreateGraphVertexLabel(stmt1, labelJson1, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropGraphLabel(stmt1, "vsys1");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropGraphLabel(stmt1, "vsys1::rule1");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropGraphLabel(stmt1, "vsys1::rule1::source_ip1");
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(labelJson1);
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_sub_view_2(void *args)
{
    int ret;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    char *labelJson2 = NULL;

    ret = testGmcConnect(&conn2, &stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/thread2_GraphVertexLabel.gmjson", &labelJson2);
    EXPECT_NE((void *)NULL, labelJson2);
    for (int i = 0; i < 100; i++) {
        ret = GmcCreateGraphVertexLabel(stmt2, labelJson2, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropGraphLabel(stmt2, "vsys");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropGraphLabel(stmt2, "vsys::rule");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropGraphLabel(stmt2, "vsys::rule::source_ip");
        EXPECT_EQ(GMERR_OK, ret);
    }
    free(labelJson2);

    ret = testGmcDisconnect(conn2, stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 14.多个连接多线程并发创建多个graphlabel; 预期create成功,
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_014)
{
    pthread_t thr_arr[2];
    pthread_create(&thr_arr[0], NULL, thread_sub_view_1, NULL);
    pthread_create(&thr_arr[1], NULL, thread_sub_view_2, NULL);
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
}

// 16.创建graphVertex, 开启事务, 插入数据，提交事务
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_016)
{
    void *vertexLabel1 = NULL;
    void *vertexLabel2 = NULL;
    void *vertexLabel3 = NULL;
    GmcStmtT *stmtVsys = NULL;
    int ret = GmcAllocStmt(conn, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/NormalGraphVertexLabel006.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // printf("%s\n", g_labelJson);
    readJanssonFile("schemaFile/NormalGraphEdgeLabel006.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    // 创建label
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    // 创建批量边
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);  // 可以先不建边 B020版本有自动建边需求
    free(normal_graph_edge_label_schema);

    // 开启一个事务(cs模式)
    GmcTxConfigT config;  // 备注：是否支持事务操作
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(conn, &config);
    EXPECT_EQ(GMERR_OK, ret);

    // batch insert v1
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmtVsys, "a1", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int count = 1024;
    // insert V1
    for (int i = 0; i < count; i++) {
        // ret = testGmcPrepareStmtByLabelName(stmtVsys, "a1", GMC_OPERATION_INSERT);
        // EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtVsys, "F0", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtVsys, "F1", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtVsys, "F2", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmtVsys);
        EXPECT_EQ(GMERR_OK, ret);
        // ret=testGmcGetLastError(NULL);
        // EXPECT_EQ(GMERR_OK, ret);
        // ret = GmcExecute(stmtVsys);
        // EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    EXPECT_EQ(count, totalNum);
    EXPECT_EQ(count, successNum);
    // insert V2
    for (int i = 0; i < count; i++) {
        char a2_F2[128] = "";
        snprintf(a2_F2, 128, "hei_%d", i);
        ret = testGmcPrepareStmtByLabelName(stmtRule, "a2", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtRule, "F0", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtRule, "F1", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtRule, "F2", GMC_DATATYPE_STRING, a2_F2, strlen(a2_F2));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtRule);
        EXPECT_EQ(GMERR_OK, ret);
        // free(a2_F2);
    }
    // insert V3
    for (int i = 0; i < count; i++) {
        ret = testGmcPrepareStmtByLabelName(stmtS_ip, "a3", GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtS_ip, "F0", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtS_ip, "F1", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmtS_ip, "F2", GMC_DATATYPE_INT32, &i, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtS_ip);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("insert value: %d\n", i);
    }
    GmcBatchDestroy(batch);
    //提交事务
    ret = GmcTransCommit(conn);
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcDropGraphLabel(stmt, "a1");
    EXPECT_EQ(GMERR_OK, ret);
}

// 17.创建多个hashcluster：false的graphlabel; 预期create成功
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_017)
{
    int ret;
    readJanssonFile("schemaFile/superfields_GraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // printf("%s\n", g_labelJson);
    GmcDropGraphLabel(stmt, "a1");
    GmcDropGraphLabel(stmt, "a2");
    GmcDropGraphLabel(stmt, "a3");
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_labelJson);
    char edgeLabelSchema[1024] = "[{\"name\":\"a1_a2\",\"source_vertex_label\":\"a1\",\"dest_vertex_label\":\"a2\","
                                 "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": "
                                 "\"F0\",\"dest_property\": \"F0\"},"
                                 "{\"source_property\": \"F1\",\"dest_property\": \"F3\"}]}},"
                                 "{\"name\":\"a2_a3\",\"source_vertex_label\":\"a2\",\"dest_vertex_label\":\"a3\","
                                 "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": "
                                 "\"F0\",\"dest_property\": \"F1\"}]}}]";
    ret = GmcCreateGraphEdgeLabel(stmt, edgeLabelSchema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "a1");
    EXPECT_EQ(GMERR_OK, ret);
    GmcDropGraphLabel(stmt, "a1");
    GmcDropGraphLabel(stmt, "a2");
    GmcDropGraphLabel(stmt, "a3");
}

void *thread_create_008_01(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    char *labelJson1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = GmcCreateGraphVertexLabel(stmt1, g_labelJsonTest, NULL);
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_DUPLICATE_TABLE);
        if (ret != GMERR_OK && ret != GMERR_DUPLICATE_TABLE) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]GMC_RET: %d", ret);
            testGmcGetLastError(NULL);
        }
    }
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_create_008_02(void *args)
{
    int ret;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn2, &stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 100; i++) {
        ret = GmcDropGraphLabel(stmt2, "vsys1");
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
        if (ret != GMERR_OK && ret != GMERR_UNDEFINED_TABLE) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]GMC_RET: %d", ret);
            testGmcGetLastError(NULL);
        }
        ret = GmcDropGraphLabel(stmt2, "vsys1::rule1");
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
        if (ret != GMERR_OK && ret != GMERR_UNDEFINED_TABLE) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]GMC_RET: %d", ret);
            testGmcGetLastError(NULL);
        }
        ret = GmcDropGraphLabel(stmt2, "vsys1::rule1::source_ip1");
        EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
        if (ret != GMERR_OK && ret != GMERR_UNDEFINED_TABLE) {
            AW_FUN_Log(LOG_DEBUG, "[INFO]GMC_RET: %d", ret);
            testGmcGetLastError(NULL);
        }
    }
    ret = testGmcDisconnect(conn2, stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 18 Yang特性, 并发创建或删除同一个label
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_018)
{
    int ret;
    int tdNum = 2;
#ifdef ENV_RTOSV2X
    tdNum = 2;
#else
    tdNum = 49;
#endif
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    pthread_t thr_arr1[tdNum];
    pthread_t thr_arr2[tdNum];
    // 创建删除同一张label
    for (int i = 0; i < tdNum; i++) {
        ret = pthread_create(&thr_arr1[i], NULL, thread_create_008_01, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        ret = pthread_create(&thr_arr2[i], NULL, thread_create_008_02, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(thr_arr1[i], NULL);
        pthread_join(thr_arr2[i], NULL);
    }
    GmcDropGraphLabel(stmt, "vsys1");
    GmcDropGraphLabel(stmt, "vsys1::rule1");
    GmcDropGraphLabel(stmt, "vsys1::rule1::source_ip1");
}

// 19.GmcCreateGraphVertexLabel和GmcDropGraphLabel接口入参stmt为异步，预期失败
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_019)
{
    int ret;
    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // 创建vertex失败
    ret = GmcCreateGraphVertexLabel(g_stmt_async, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建vertex成功
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    // 创建边失败
    ret = GmcCreateGraphEdgeLabel(g_stmt_async, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建边成功
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_labelJson);
    free(normal_graph_edge_label_schema);
    // 删除失败
    ret = GmcDropGraphLabel(g_stmt_async, "vsys");
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 删除成功
    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
}

// 20.创建包含命名相同的fields的graphlabel(其中一层); 预期create成功
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_020)
{
    int ret;
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DUPLICATE_COLUMN);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    readJanssonFile("schemaFile/same_fields_GraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_COLUMN, ret);  // 预期GMERR_SYNTAX_ERROR，实际GMERR_DUPLICATE_COLUMN
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule");
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule::source_ip");
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 21.创建多个不同名的graphVertex和edgeLabel
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_021)
{
    int ret;
    int count = 300;
    char label_schema[SCHEMA_JSON_SIZE] = "";
    char edgeLabelJson[SCHEMA_JSON_SIZE] = "";
    for (int i = 0; i < count; i++) {
        sprintf(label_schema,
            "[{\"type\":\"record\", \"name\":\"a%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"a%d\", \"name\":\"pk1\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}, "
            "{\"type\":\"record\", \"name\":\"a%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"a%d\", \"name\":\"pk2\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]},"
            "{\"type\":\"record\", \"name\":\"a%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"a%d\", \"name\":\"pk3\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            i * 3, i * 3, i * 3 + 1, i * 3 + 1, i * 3 + 2, i * 3 + 2);

        ret = GmcCreateGraphVertexLabel(stmt, label_schema, Label_config);  // 多个不同名的vertex
        if (ret < 299) {
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
            break;
        }

        sprintf(edgeLabelJson,
            "[{\"name\":\"a%d_a%d\",\"source_vertex_label\":\"a%d\",\"dest_vertex_label\":\"a%d\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F0\",\"dest_property\": "
            "\"F0\"}]}},"
            "{\"name\":\"a%d_a%d\",\"source_vertex_label\":\"a%d\",\"dest_vertex_label\":\"a%d\","
            "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": \"F0\",\"dest_property\": "
            "\"F0\"}]}}]",
            i * 3, i * 3 + 1, i * 3, i * 3 + 1, i * 3 + 1, i * 3 + 2, i * 3 + 1, i * 3 + 2);

        ret = GmcCreateGraphEdgeLabel(stmt, edgeLabelJson, Label_config);  // 多个不同名的edgeLabel
        EXPECT_EQ(GMERR_OK, ret);
        memset(label_schema, 0, SCHEMA_JSON_SIZE);
        memset(edgeLabelJson, 0, SCHEMA_JSON_SIZE);
    }

    for (int i = 0; i < count - 1; i++) {
        char name1[8] = "";
        sprintf(name1, "a%d", i * 3);
        ret = GmcDropGraphLabel(stmt, name1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcDropGraphLabel(stmt, "a897");
    EXPECT_EQ(GMERR_OK, ret);
}

// 22.创建schema中某些字段含有默认值的graphlabel; 预期create成功
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_022)
{
    int ret;
    readJanssonFile("schemaFile/default_fields_GraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // printf("%s\n", g_labelJson);
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_labelJson);
    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule::source_ip");
    EXPECT_EQ(GMERR_OK, ret);
}

// 23.根据yang模型创建label后，建边affectRows=1，删除src、dst点成功 场景应增加关联查询
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_023)
{
    GmcStmtT *stmtVsys = NULL;
    int ret = GmcAllocStmt(conn, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);
    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    // 创建批量点
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建批量边
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    free(normal_graph_edge_label_schema);
    /* *构造数据v1 ~ v3* */
    // 先查询元数据
    void *vsysLabel;
    ret = testGmcPrepareStmtByLabelName(stmtVsys, "vsys", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    void *ruleLabel;
    ret = testGmcPrepareStmtByLabelName(stmtRule, "vsys::rule", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    void *s_ipLabel;
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, "vsys::rule::source_ip", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // 插入一个三层结构
    int i = 100;
    int v1_id = i;
    normal_insert_layer1(stmtVsys, v1_id);
    int v2_vsys_id = i;
    int v2_id = i;
    char v2_name[] = "hei";
    normal_insert_layer2(stmtRule, v2_vsys_id, v2_id, &v2_name);
    int v3_vsys_id = i;
    int v3_rule_id = i;
    int v3_ipLen = i;
    int v3_masklen = i;
    normal_insert_layer3(stmtS_ip, v3_vsys_id, v3_rule_id, v3_ipLen, v3_masklen);

    void *edgeLabel1 = NULL;
    void *edgeLabel2 = NULL;
    char PkName1[32] = "id";
    char PkName2[32] = "vsys::id_id";
    char PkName3[32] = "vsys.rule.source_ip_K0";
    char EdgeLabelName1[32] = "vsys_rule";
    char EdgeLabelName2[32] = "ruleAndsource_ip";

    //建边vsys->rule
    ret = GmcOpenEdgeLabelByName(stmt, EdgeLabelName1, &edgeLabel1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, PkName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(GMC_DATATYPE_INT32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, PkName2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(GMC_DATATYPE_INT32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &v2_id, sizeof(GMC_DATATYPE_INT32));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcInsertEdge(stmt, edgeLabel1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgeLabel1);
    EXPECT_EQ(GMERR_OK, ret);

    // 删除src dest点
    normal_delete_layer1(stmtVsys, v1_id);
    normal_delete_layer2(stmtRule, v2_vsys_id, v2_id);
    normal_delete_layer3(stmtS_ip, v3_vsys_id, v3_rule_id, v3_ipLen, v3_masklen);

    // 建边
    ret = GmcOpenEdgeLabelByName(stmt, EdgeLabelName1, &edgeLabel1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexName(stmt, PkName1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeSrcVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(GMC_DATATYPE_INT32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexName(stmt, PkName2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(GMC_DATATYPE_INT32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetEdgeDstVertexIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &v2_id, sizeof(GMC_DATATYPE_INT32));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcInsertEdge(stmt, edgeLabel1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCloseEdgeLabel(stmt, edgeLabel1);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);

    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);
}

// 24.创建携带16个unique true hashlocal并且字段都不相同的graphlabel
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_024)
{
    int ret;
    readJanssonFile("schemaFile/true_hashlocal_GraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);  // 主键与hash同一字段也可成功
    free(g_labelJson);
    char edgeLabelSchema[1024] = "[{\"name\":\"a1_a2\",\"source_vertex_label\":\"a1\",\"dest_vertex_label\":\"a2\","
                                 "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": "
                                 "\"F0\",\"dest_property\": \"F0\"},"
                                 "{\"source_property\": \"F1\",\"dest_property\": \"F3\"}]}},"
                                 "{\"name\":\"a2_a3\",\"source_vertex_label\":\"a2\",\"dest_vertex_label\":\"a3\","
                                 "\"constraint\":{\"operator_type\":\"and\",\"conditions\":[{\"source_property\": "
                                 "\"F0\",\"dest_property\": \"F1\"}]}}]";
    ret = GmcCreateGraphEdgeLabel(stmt, edgeLabelSchema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "a1");
    EXPECT_EQ(GMERR_OK, ret);
}

// 25.创建携带17个unique false hashlocal的graphlabel, 预期失败
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_025)
{
    int ret;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    readJanssonFile("schemaFile/false_hashlocal_GraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, NULL);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 26.创建 schema json定义两个PK的graphlabel
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_026)
{
    int ret;
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(2, errorCode1, errorCode2);
    readJanssonFile("schemaFile/PK_2fileds_GraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, NULL);
    EXPECT_EQ(GMERR_DUPLICATE_OBJECT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule");
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "vsys::rule::source_ip");
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
}

// 27 schema层级验证, 每一层都包含子节点,然后创建graphVertex
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_027)
{
    char *All_type_json = NULL;
    char *edge_label_schema = NULL;
    // 创建批量点
    readJanssonFile("schemaFile/GraphVertexLabelIncludeNode.gmjson", &All_type_json);
    EXPECT_NE((void *)NULL, All_type_json);
    readJanssonFile("schemaFile/GraphEdgeLabelIncludeNode.gmjson", &edge_label_schema);
    EXPECT_NE((void *)NULL, edge_label_schema);

    int ret = GmcCreateGraphVertexLabel(stmt, All_type_json, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建批量边
    ret = GmcCreateGraphEdgeLabel(stmt, edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(All_type_json);
    free(edge_label_schema);

    ret = GmcDropGraphLabel(stmt, "a1");
    EXPECT_EQ(GMERR_OK, ret);
}

// 28 schema中field个数超过上限验证1025
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_028)
{
    char *All_type_json = NULL;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    // char *edge_label_schema = NULL;
    // 创建批量点
    readJanssonFile("schemaFile/GraphVertexLabelFields1024.gmjson", &All_type_json);
    EXPECT_NE((void *)NULL, All_type_json);
    int ret = GmcCreateGraphVertexLabel(stmt, All_type_json, NULL);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);  // 1024个可成功，vertex已有用例验证
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建批量边

    free(All_type_json);
}

void *thread_replace_029_01(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtVsys = NULL;
    ret = GmcAllocStmt(conn1, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 1000; i++) {
        // insert V1 V2 V3
        int F0_value = 1;
        ret = testGmcPrepareStmtByLabelName(stmtVsys, "a1", GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        // printf("%d\n", i);
        // insert 第一层
        ret = GmcSetVertexProperty(stmtVsys, "F0", GMC_DATATYPE_INT32, &F0_value, sizeof(F0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtVsys);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            while (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = GmcExecute(stmtVsys);
            }
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    GmcFreeStmt(stmtVsys);
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
void *thread_merge_029_02(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn1, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 1000; i++) {
        int F0_value = 1;
        int ret = testGmcPrepareStmtByLabelName(stmtRule, "a2", GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        // insert 第二层
        ret = GmcSetIndexKeyValue(stmtRule, 0, GMC_DATATYPE_INT32, &F0_value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtRule, "pk2");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtRule);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            while (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = GmcExecute(stmtRule);
            }
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    GmcFreeStmt(stmtRule);
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
void *thread_update_029_03(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn1, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 1000; i++) {
        int F0_value = 1;
        ret = testGmcPrepareStmtByLabelName(stmtS_ip, "a3", GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &F0_value, sizeof(int));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtS_ip, "pk3");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtS_ip);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            while (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = GmcExecute(stmtS_ip);
            }
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    GmcFreeStmt(stmtS_ip);
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
void *thread_query_029_04(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtVsys = NULL;
    ret = GmcAllocStmt(conn1, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn1, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn1, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 1000; i++) {
        // insert V1 V2 V3
        int F0_A1 = 1;
        ret = testGmcPrepareStmtByLabelName(stmtVsys, "a1", GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtVsys, 0, GMC_DATATYPE_INT32, &F0_A1, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtVsys, "pk1");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtVsys);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            while (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = GmcExecute(stmtVsys);
            }
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }

        int F0_A2 = 1;
        ret = testGmcPrepareStmtByLabelName(stmtRule, "a2", GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtRule, 0, GMC_DATATYPE_INT32, &F0_A2, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtRule, "pk2");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtRule);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            while (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = GmcExecute(stmtRule);
            }
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }

        int F0_A3 = 1;
        ret = testGmcPrepareStmtByLabelName(stmtS_ip, "a3", GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &F0_A3, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtS_ip, "pk3");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmtS_ip);
        if (ret == GMERR_LOCK_NOT_AVAILABLE) {
            while (ret == GMERR_LOCK_NOT_AVAILABLE) {
                ret = GmcExecute(stmtS_ip);
            }
            EXPECT_EQ(GMERR_OK, ret);
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 29. 同一graphVertex下并发做不一样的DML操作(先写多条记录，后查询、replace、merge、update相同的记录)
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_029)
{
    int ret;
    GmcStmtT *stmtVsys = NULL;
    ret = GmcAllocStmt(conn, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);

    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建VertexLabel
    readJanssonFile("schemaFile/hashlocal_GraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // printf("%s\n", g_labelJson);
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // ret=testGmcGetLastError(NULL);
    // EXPECT_EQ(GMERR_OK, ret);

    // insert V1 V2 V3
    int F0_A1 = 1;
    ret = testGmcPrepareStmtByLabelName(stmtVsys, "a1", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtVsys, "F0", GMC_DATATYPE_INT32, &F0_A1, sizeof(F0_A1));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);

    int F0_A2 = 1;
    ret = testGmcPrepareStmtByLabelName(stmtRule, "a2", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "F0", GMC_DATATYPE_INT32, &F0_A2, sizeof(F0_A2));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtRule);
    EXPECT_EQ(GMERR_OK, ret);

    int F0_A3 = 1;
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, "a3", GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "F0", GMC_DATATYPE_INT32, &F0_A3, sizeof(F0_A3));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);

    int tdNum;
    uint32_t currConnNum;
#if defined ENV_RTOSV2X
    testGetConnNum(&currConnNum);
    // 除以4是因为tdNum=1就对应4个线程
    tdNum = (MAX_CONN_SIZE - currConnNum - 2) / 4;
#else
    tdNum = 16;
#endif
    pthread_t thr_arr1[tdNum];
    pthread_t thr_arr2[tdNum];
    pthread_t thr_arr3[tdNum];
    pthread_t thr_arr4[tdNum];
    // 并发DML操作
    for (int i = 0; i < tdNum; i++) {
        // query
        ret = pthread_create(&thr_arr1[i], NULL, thread_replace_029_01, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        // replace
        ret = pthread_create(&thr_arr2[i], NULL, thread_merge_029_02, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        // merge
        ret = pthread_create(&thr_arr3[i], NULL, thread_update_029_03, NULL);
        EXPECT_EQ(GMERR_OK, ret);
        // update
        ret = pthread_create(&thr_arr4[i], NULL, thread_query_029_04, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(thr_arr1[i], NULL);
        pthread_join(thr_arr2[i], NULL);
        pthread_join(thr_arr3[i], NULL);
        pthread_join(thr_arr4[i], NULL);
    }

    ret = GmcDropGraphLabel(stmt, "a1");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "a2");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropGraphLabel(stmt, "a3");
    free(g_labelJson);
}

// 30 GmcCreateGraphVertexLabel接口入参schema非法；
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_030)
{
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *invaid_type_json = NULL;
    // 创建label
    readJanssonFile("schemaFile/invaidTypeGraphVertexLabel.gmjson", &invaid_type_json);
    EXPECT_NE((void *)NULL, invaid_type_json);

    int ret = GmcCreateGraphVertexLabel(stmt, invaid_type_json, NULL);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);  // 预期GMERR_SYNTAX_ERROR，实际GMERR_DATATYPE_MISMATCH
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(invaid_type_json);
}

// 31.GmcCreateGraphEdgeLabel接口入参stmt为空；
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_031)
{
    int ret;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    // 创建批量点
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建批量边
    ret = GmcCreateGraphEdgeLabel(NULL, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, g_cfgJson);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    free(normal_graph_edge_label_schema);

    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
}

// 32.GmcCreateGraphEdgeLabel接口入参schema为空；
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_032)
{
    int ret;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    // 创建批量点
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // 创建批量边
    ret = GmcCreateGraphEdgeLabel(stmt, NULL, Label_config);
    EXPECT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    free(normal_graph_edge_label_schema);

    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
}

// 33.graph_edge_label_schema中缺少必要字段
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_033)
{
    int ret;
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    char errorCode3[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_UNDEFINE_COLUMN);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    (void)snprintf(errorCode3, 128, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(3, errorCode1, errorCode2, errorCode3);
    char *edge_label_lack_source_vertex_schema = NULL;
    char *edge_label_lack_dest_vertex_schema = NULL;
    char *edge_label_lack_constraint_schema = NULL;
    char *edge_label_lack_operator_type_schema = NULL;
    char *edge_label_lack_property_schema = NULL;

    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // 创建批量点
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/EdgeLabelLackSource.gmjson", &edge_label_lack_source_vertex_schema);
    EXPECT_NE((void *)NULL, edge_label_lack_source_vertex_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, edge_label_lack_source_vertex_schema, g_cfgJson);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);  // 预期GMERR_DATATYPE_MISMATCH，实际GMERR_UNDEFINE_COLUMN
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/EdgeLabelLackDest.gmjson", &edge_label_lack_dest_vertex_schema);
    EXPECT_NE((void *)NULL, edge_label_lack_dest_vertex_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, edge_label_lack_dest_vertex_schema, g_cfgJson);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);  // 预期GMERR_DATATYPE_MISMATCH，实际GMERR_UNDEFINE_COLUMN
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/EdgeLabelLackConstraint.gmjson", &edge_label_lack_constraint_schema);
    EXPECT_NE((void *)NULL, edge_label_lack_constraint_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, edge_label_lack_constraint_schema, g_cfgJson);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/EdgeLabelLackOperatorType.gmjson", &edge_label_lack_operator_type_schema);
    EXPECT_NE((void *)NULL, edge_label_lack_operator_type_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, edge_label_lack_operator_type_schema, g_cfgJson);
    EXPECT_EQ(GMERR_UNDEFINE_COLUMN, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/EdgeLabelLackProperty.gmjson", &edge_label_lack_property_schema);
    EXPECT_NE((void *)NULL, edge_label_lack_property_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, edge_label_lack_property_schema, g_cfgJson);
    EXPECT_EQ(GMERR_ARRAY_SUBSCRIPT_ERROR, ret);  // 预期GMERR_DATATYPE_MISMATCH，实际GMERR_ARRAY_SUBSCRIPT_ERROR
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建批量边(正常)
    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    free(edge_label_lack_source_vertex_schema);
    free(edge_label_lack_dest_vertex_schema);
    free(edge_label_lack_constraint_schema);
    free(edge_label_lack_operator_type_schema);
    free(edge_label_lack_property_schema);
    free(normal_graph_edge_label_schema);

    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
}

// 34.edgelabelJson:sourcevertex和destvertex value为空
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_034)
{
    int ret;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *edge_label_source_value_null_schema = NULL;
    char *edge_label_dest_value_null_schema = NULL;

    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // 创建批量点
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/EdgeLabelLackSourceValueNull.gmjson", &edge_label_source_value_null_schema);
    EXPECT_NE((void *)NULL, edge_label_source_value_null_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, edge_label_source_value_null_schema, Label_config);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/EdgeLabelLackDestValueNull.gmjson", &edge_label_dest_value_null_schema);
    EXPECT_NE((void *)NULL, edge_label_dest_value_null_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, edge_label_dest_value_null_schema, Label_config);
    EXPECT_EQ(GMERR_INVALID_JSON_CONTENT, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建批量边(正常)
    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    free(edge_label_source_value_null_schema);
    free(edge_label_dest_value_null_schema);
    free(normal_graph_edge_label_schema);

    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
}

// 35.edgelabelJson:operator_type value valid
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_035)
{
    int ret;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    char *edge_label_operator_type_value_valid_schema = NULL;

    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // 创建批量点
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/EdgeLabelOperatorTypeValueValid.gmjson", &edge_label_operator_type_value_valid_schema);
    EXPECT_NE((void *)NULL, edge_label_operator_type_value_valid_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, edge_label_operator_type_value_valid_schema, Label_config);
    EXPECT_EQ(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建批量边(正常)
    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    free(edge_label_operator_type_value_valid_schema);
    free(normal_graph_edge_label_schema);

    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
}

// 36.edgelabelJson:对应的vertexlabel未创建
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_036)
{
    int ret;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, g_cfgJson);
    EXPECT_EQ(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    free(normal_graph_edge_label_schema);
}

// 37.edgelabelJson:dst src property type nomatch
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_037)
{
    int ret;
    char *edge_label_property_type_nomatch_schema = NULL;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // 创建批量点
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/EdgeLabelPropertyTypeNoMatch.gmjson", &edge_label_property_type_nomatch_schema);
    EXPECT_NE((void *)NULL, edge_label_property_type_nomatch_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, edge_label_property_type_nomatch_schema, Label_config);
    EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建批量边(正常)
    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    free(edge_label_property_type_nomatch_schema);
    free(normal_graph_edge_label_schema);

    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
}

// 38.edgelabel重复创建
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_038)
{
    int ret;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // 创建批量点
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建批量边(正常)
    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_DUPLICATE_TABLE, ret);
    ret = testGmcGetLastError("Duplicate table. Edge label name:vsys_rule.");
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    free(normal_graph_edge_label_schema);

    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
}

// 39.edgelabelJson:dst or src property name 不存在
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_039)
{
    int ret;
    char *edge_label_src_property_name_valid_schema = NULL;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    readJanssonFile("schemaFile/NormalGraphVertexLabel.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);
    // 创建批量点
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/EdgeLabelSrcPropertyNameValid.gmjson", &edge_label_src_property_name_valid_schema);
    EXPECT_NE((void *)NULL, edge_label_src_property_name_valid_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, edge_label_src_property_name_valid_schema, Label_config);
    EXPECT_EQ(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);

    // 创建批量边(正常)
    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    free(edge_label_src_property_name_valid_schema);
    free(normal_graph_edge_label_schema);

    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_create_1(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    // 不建边
    for (int i = 0; i < 100; i++) {
        GmcCreateGraphVertexLabel(stmt1, labelJson_040, NULL);
    }
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 40.多个连接多线程并发创建删除多个不同名graphlabel、edgelabel
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_040)
{
    int err;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    int tdNum = 2;
    uint32_t currConnNum;
#ifdef ENV_RTOSV2X
    testGetConnNum(&currConnNum);
    tdNum = MAX_CONN_SIZE - currConnNum - 2;
#else
    tdNum = 49;
#endif
    pthread_t thr_arr_test[tdNum];

    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&thr_arr_test[i], NULL, thread_create_1, NULL);
        EXPECT_EQ(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(thr_arr_test[i], NULL);
    }
    for (int i = 0; i < 49; i++) {  // 避免多线程导致出现未删除的vertexLabel
        GmcDropGraphLabel(stmt, "vsys040");
        GmcDropGraphLabel(stmt, "vsys040::rule040");
        GmcDropGraphLabel(stmt, "vsys040::rule040::source_ip040");
    }
}

void normal_insert_layer1_test(GmcStmtT *stmtVsys, int v1_id, int i)
{
    void *vsysLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtVsys, "vsys", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // insert
    ret = GmcSetVertexProperty(stmtVsys, "id", GMC_DATATYPE_INT32, &v1_id, sizeof(v1_id));
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F0Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F0", GMC_DATATYPE_INT64, &F0Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t F1Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t F2Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F3Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t F6Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t F7Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmtVsys, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    float F9Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double F10Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t F11Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char F12Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char F13Value = i;
    ret = GmcSetVertexProperty(stmtVsys, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(stmtVsys, "F14", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    ASSERT_EQ(GMERR_OK, ret);

    char T15_field_value[10];
    snprintf(T15_field_value, 10, "s_%d", i);
    ret = GmcSetVertexProperty(stmtVsys, "F15", GMC_DATATYPE_BYTES, T15_field_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtVsys, "F16", GMC_DATATYPE_STRING, T15_field_value, (strlen(T15_field_value)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    ASSERT_EQ(GMERR_OK, ret);
    // ret=testGmcGetLastError(NULL);
    // ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtVsys, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}
void normal_insert_layer2_test(GmcStmtT *stmtRule, int v2_vsys_id, int v2_id, void *v2_name, int i)
{
    void *ruleLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtRule, "vsys::rule", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    // insert
    ret = GmcSetVertexProperty(stmtRule, "vsys::id", GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(v2_vsys_id));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "id", GMC_DATATYPE_INT32, &v2_id, sizeof(v2_id));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "name", GMC_DATATYPE_STRING, v2_name, strlen((const char *)v2_name));
    EXPECT_EQ(GMERR_OK, ret);
    // if(ret != 0) {
    //     printf("%s\n", v2_name);
    // }
    // ret=testGmcGetLastError(NULL);
    // EXPECT_EQ(GMERR_OK, ret);

    int64_t F0Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F0", GMC_DATATYPE_INT64, &F0Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t F1Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t F2Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F3Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t F6Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t F7Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmtRule, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    float F9Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double F10Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t F11Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char F12Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char F13Value = i;
    ret = GmcSetVertexProperty(stmtRule, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(stmtRule, "F15", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    ASSERT_EQ(GMERR_OK, ret);

    char T15_field_value[10];
    snprintf(T15_field_value, 10, "s_%d", i);
    ret = GmcSetVertexProperty(stmtRule, "F16", GMC_DATATYPE_BYTES, T15_field_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "F14", GMC_DATATYPE_STRING, T15_field_value, (strlen(T15_field_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmtRule);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtRule, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}
void normal_insert_layer3_test(GmcStmtT *stmtS_ip, int v3_vsys_id, int v3_rule_id, int v3_ipLen, int v3_masklen, int i)
{
    void *s_ipLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtS_ip, "vsys::rule::source_ip", GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    // 插入insert
    ret = GmcSetVertexProperty(stmtS_ip, "rule::vsys::id", GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(v3_vsys_id));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "rule::id", GMC_DATATYPE_INT32, &v3_rule_id, sizeof(v3_rule_id));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "ipLen", GMC_DATATYPE_INT32, &v3_ipLen, sizeof(v3_ipLen));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "maskLen", GMC_DATATYPE_INT32, &v3_masklen, sizeof(v3_masklen));
    ASSERT_EQ(GMERR_OK, ret);

    int64_t F0Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F0", GMC_DATATYPE_INT64, &F0Value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t F1Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t F2Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t F3Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t F6Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);
    uint8_t F7Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmtS_ip, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);
    float F9Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);
    double F10Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);
    uint64_t F11Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char F12Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);
    unsigned char F13Value = i;
    ret = GmcSetVertexProperty(stmtS_ip, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    // bitmap值的设置
    GmcBitMapT bitMap = {0, 127, NULL};
    uint8_t bits[128 / 8];
    memset(bits, 0xff, 128 / 8);
    bits[128 / 8 - 1] = '\0';
    bitMap.bits = bits;
    ret = GmcSetVertexProperty(stmtS_ip, "F16", GMC_DATATYPE_BITMAP, (void *)&bitMap, sizeof(bitMap));
    ASSERT_EQ(GMERR_OK, ret);

    char T15_field_value[10];
    snprintf(T15_field_value, 10, "s_%d", i);
    ret = GmcSetVertexProperty(stmtS_ip, "F15", GMC_DATATYPE_BYTES, T15_field_value, 7);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "F14", GMC_DATATYPE_STRING, T15_field_value, (strlen(T15_field_value)));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(stmtS_ip);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtS_ip, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

// 41.全类型创建graphLabel，insert数据
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_041)
{
    GmcStmtT *stmtVsys = NULL;
    int ret = GmcAllocStmt(conn, &stmtVsys);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    EXPECT_EQ(GMERR_OK, ret);
    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/NormalGraphVertexLabel_test.gmjson", &g_labelJson);
    EXPECT_NE((void *)NULL, g_labelJson);

    readJanssonFile("schemaFile/NormalGraphEdgeLabel.gmjson", &normal_graph_edge_label_schema);
    EXPECT_NE((void *)NULL, normal_graph_edge_label_schema);
    ret = GmcCreateGraphVertexLabel(stmt, g_labelJson, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    // ret=testGmcGetLastError(NULL);
    // EXPECT_EQ(GMERR_OK, ret);

    free(g_labelJson);
    // 创建批量边
    ret = GmcCreateGraphEdgeLabel(stmt, normal_graph_edge_label_schema, Label_config);
    EXPECT_EQ(GMERR_OK, ret);
    free(normal_graph_edge_label_schema);

    // insert V1 V2 V3
    for (int i = 0; i < 1000; i++) {
        int v1_id = i;
        normal_insert_layer1_test(stmtVsys, v1_id, i);

        int v2_vsys_id = i;
        int v2_id = i;
        char v2_name[128];
        sprintf(v2_name, "hei_%d", i);
        normal_insert_layer2_test(stmtRule, v2_vsys_id, v2_id, &v2_name, i);
        int v3_vsys_id = i;
        int v3_rule_id = i;
        int v3_ipLen = i;
        int v3_masklen = i;
        normal_insert_layer3_test(stmtS_ip, v3_vsys_id, v3_rule_id, v3_ipLen, v3_masklen, i);
    }

    ret = GmcDropGraphLabel(stmt, "vsys");
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_create_042(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    char *labelJson1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        GmcCreateGraphVertexLabel(stmt1, g_labelJsonTest, Label_config);
        GmcCreateGraphEdgeLabel(stmt1, g_labelJsonTestEdge, NULL);
    }
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_drop_042(void *args)
{
    int ret;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    ret = testGmcConnect(&conn2, &stmt2);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < 100; i++) {
        GmcDropGraphLabel(stmt2, "vsys1");
        GmcDropGraphLabel(stmt2, "vsys1::rule1");
        GmcDropGraphLabel(stmt2, "vsys1::rule1::source_ip1");
    }
    ret = testGmcDisconnect(conn2, stmt2);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 42.并发创建或删除同一个带边的GraphLabel
TEST_F(CreateGphLabel, DDL_011_GmcCreateGraphLabel_test_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    int tdNum = 2;
#ifdef ENV_RTOSV2X
    tdNum = 5;
#else
    tdNum = 10;
#endif
	char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    char errorCode3[128] = {0};
    char errorCode4[128] = {0};
    char errorCode5[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_UNDEFINED_TABLE);
    (void)snprintf(errorCode3, 128, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    (void)snprintf(errorCode4, 128, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(errorCode5, 128, "GMERR-%d", GMERR_DUPLICATE_OBJECT);
    AW_ADD_ERR_WHITE_LIST(5, errorCode1, errorCode2, errorCode3, errorCode4, errorCode5);
    pthread_t thr_arr1[tdNum];
    pthread_t thr_arr2[tdNum];
    // 创建删除同一张label
    for (int i = 0; i < tdNum; i++) {
        ret = pthread_create(&thr_arr1[i], NULL, thread_create_042, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = pthread_create(&thr_arr2[i], NULL, thread_drop_042, NULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(thr_arr1[i], NULL);
        pthread_join(thr_arr2[i], NULL);
    }
    GmcDropGraphLabel(stmt, "vsys1");
    GmcDropGraphLabel(stmt, "vsys1::rule1");
    GmcDropGraphLabel(stmt, "vsys1::rule1::source_ip1");
    AW_FUN_Log(LOG_STEP, "test end.");
}
