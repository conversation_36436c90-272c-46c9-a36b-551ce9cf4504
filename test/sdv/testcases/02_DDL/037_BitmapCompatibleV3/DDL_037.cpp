#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "jansson.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
int ret = 0;

class DDL_037 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh ");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        ASSERT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};
void DDL_037 ::SetUp()
{
    int ret = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void DDL_037 ::TearDown()
{
    AW_CHECK_LOG_END();
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 001 设定一个包含变长字段和定长字段的schema，schema中的superfield 包含bitmap字段 ，建表
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_001)
{
    printf("****************************DDL_037_001 START*******************\n");
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_001.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_001_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_001";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_001 END*******************\n");
}
/*****************************************************************************
 * Description  : 002.设定一个包含变长字段和定长字段的schema，schema中的superfield包含bitmap字段，
                （bitmap 在superfield 的开头）写入数据包含bitmap字段，将数据json导出。
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
typedef struct sp_test_1 {
    // int MapBeginPos;
    // int MapEndPos;
    uint8_t Mapbuf[128 / 8];
    uint32_t F3value;
} sp_test_t;  // BitmapforSuperfieldStart格式    Superfield 用到的结构体
TEST_F(DDL_037, DDL_037_002)
{
    printf("****************************DDL_037_002 START*******************\n");

    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_BitmapforSuperfieldStart.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_BitmapforSuperfieldStart_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_BitmapforSuperfieldStart";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    // 向表中写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F0_value = 10;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    sp_test_t Sp_test_t_inertvalue;
    // Sp_test_t_inertvalue.MapBeginPos = 0;
    // Sp_test_t_inertvalue.MapEndPos = 127;
    memset(Sp_test_t_inertvalue.Mapbuf, 0xff, 128 / 8);
    Sp_test_t_inertvalue.F3value = 256;
    ret = GmcSetSuperfieldByName(
        g_stmt, "BitmapCompatibleV3_BitmapforSuperfieldStart", &Sp_test_t_inertvalue, sizeof(Sp_test_t_inertvalue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 10;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    sp_test_t Sp_test_t_Qvalue;
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcGetSuperfieldByName(
            g_stmt, "BitmapCompatibleV3_BitmapforSuperfieldStart", sizeof(Sp_test_t_Qvalue), &Sp_test_t_Qvalue);
        EXPECT_EQ(256, Sp_test_t_Qvalue.F3value);
        for (int i = 0; i < 128 / 8; i++) {
            ASSERT_EQ(true, 0xFF == Sp_test_t_Qvalue.Mapbuf[i]);
        }
    }
    // 调用superfield 的查询接口

    // 将查到的结果 导出json
    uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
    char *itemJson = NULL;
    ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("%s\n", itemJson);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_002 END*******************\n");
}
/*****************************************************************************
 * Description  : 003.设定一个包含变长字段和定长字段的schema，schema中的superfield包含bitmap字段，
                （bitmap 在superfield 的开头），将这种json格式的数据导入，查看表中的数据
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
// json 导入函数
int affectRows;

void TestInsertVertexByJson(GmcStmtT *stmt, const char *jsonFile)
{
    int ret = 0;
    json_t *data_json;
    json_error_t data_json_error;
    data_json = json_load_file(jsonFile, 0, &data_json_error);
    if (json_is_array(data_json)) {
        size_t array_size = json_array_size(data_json);
        size_t i;
        printf("Insert %d vertex by json.\n", array_size);
        for (i = 0; i < array_size; i++) {
            json_t *data_json_item = json_array_get(data_json, i);
            char *jStr = json_dumps(data_json_item, JSON_INDENT(0));
            ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            EXPECT_EQ(GMERR_OK, ret);
            free(jStr);
            ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
            EXPECT_EQ(GMERR_OK, ret);
        }
    } else if (json_is_object(data_json)) {
        printf("Insert 1 vertex by json.\n");
        char *jStr = json_dumps(data_json, JSON_INDENT(0));
        printf("%s\n", jStr);
        ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, jStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        free(jStr);
        ret = testGmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
        EXPECT_EQ(GMERR_OK, ret);
    }
    json_decref(data_json);
}

TEST_F(DDL_037, DDL_037_003)
{
    printf("****************************DDL_037_003 START*******************\n");
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_BitmapforSuperfieldStart.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_BitmapforSuperfieldStart_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_BitmapforSuperfieldStart";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    TestInsertVertexByJson(g_stmt, "schema_file/JsonData/BitmapCompatibleV3_BitmapforSuperfieldStart.gmdata");

    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t priK = 18;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    sp_test_t Sp_test_t_Qvalue;
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetSuperfieldByName(
            g_stmt, "BitmapCompatibleV3_BitmapforSuperfieldStart", sizeof(Sp_test_t_Qvalue), &Sp_test_t_Qvalue);
        EXPECT_EQ(4294967295, Sp_test_t_Qvalue.F3value);
        for (int i = 0; i < 128 / 8; i++) {
            if (i == 128 / 8 - 1) {
                ASSERT_EQ(true, 0xFE == Sp_test_t_Qvalue.Mapbuf[i]);
            } else {
                ASSERT_EQ(true, 0xFF == Sp_test_t_Qvalue.Mapbuf[i]);
            }
        }
    }
    // 调用superfield 的查询接口

    // 将查到的结果 导出json
    uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);
    char *itemJson = NULL;
    ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("%s\n", itemJson);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_003 END*******************\n");
}
/*****************************************************************************
 * Description  : 004.设定一个包含变长字段和定长字段的schema，schema中的superfield包含bitmap字段，
                 （bitmap 在superfield 的中间）写入数据包含bitmap字段，对定长字段和bitmap进行全量更新，进行删除操作
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_004)
{
    printf("****************************DDL_037_004 START*******************\n");
    typedef struct sp_test_1 {
        uint32_t F2value;
        // int MapBeginPos;
        // int MapEndPos;
        uint8_t Mapbuf[128 / 8];
        uint32_t F4value;
    } BitmapforSuperfieldMiddle_type;  // BitmapforSuperfieldMiddle格式    Superfield 用到的结构体
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_BitmapforSuperfieldMiddle.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_BitmapforSuperfieldMiddle_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_BitmapforSuperfieldMiddle";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    BitmapforSuperfieldMiddle_type BitmapforSuperfieldMiddle_insert_value;

    BitmapforSuperfieldMiddle_insert_value.F2value = 128;
    // BitmapforSuperfieldMiddle_insert_value.MapBeginPos = 0;
    // BitmapforSuperfieldMiddle_insert_value.MapEndPos = 127;
    memset(BitmapforSuperfieldMiddle_insert_value.Mapbuf, 0xff, 128 / 8);
    BitmapforSuperfieldMiddle_insert_value.F4value = 256;
    // 向表中写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F0_value = 10;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t F5_value = 100;
    ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_INT32, &F5_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_BitmapforSuperfieldMiddle",
        &BitmapforSuperfieldMiddle_insert_value, sizeof(BitmapforSuperfieldMiddle_insert_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 10;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    BitmapforSuperfieldMiddle_type BitmapforSuperfieldMiddle_Qvalue;
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetSuperfieldByName(g_stmt, "BitmapCompatibleV3_BitmapforSuperfieldMiddle",
            sizeof(BitmapforSuperfieldMiddle_Qvalue), &BitmapforSuperfieldMiddle_Qvalue);
        EXPECT_EQ(128, BitmapforSuperfieldMiddle_Qvalue.F2value);
        EXPECT_EQ(256, BitmapforSuperfieldMiddle_Qvalue.F4value);
        for (int i = 0; i < 128 / 8; i++) {
            ASSERT_EQ(true, 0xFF == BitmapforSuperfieldMiddle_Qvalue.Mapbuf[i]);
        }
    }
    // 调用superfield 的查询接口

    // 更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    //更新定长字段
    int32_t Update_F5value = 99;
    ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_INT32, &Update_F5value, sizeof(Update_F5value));
    ASSERT_EQ(GMERR_OK, ret);
    //更新superfield
    BitmapforSuperfieldMiddle_type BitmapforSuperfieldMiddle_update_value;

    BitmapforSuperfieldMiddle_update_value.F2value = 128;
    // BitmapforSuperfieldMiddle_update_value.MapBeginPos = 0;
    // BitmapforSuperfieldMiddle_update_value.MapEndPos = 127;
    memset(BitmapforSuperfieldMiddle_update_value.Mapbuf, 0xEE, 128 / 8);
    BitmapforSuperfieldMiddle_update_value.F4value = 256;
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_BitmapforSuperfieldMiddle",
        &BitmapforSuperfieldMiddle_update_value, sizeof(BitmapforSuperfieldMiddle_update_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    priK = 10;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    BitmapforSuperfieldMiddle_type BitmapforSuperfieldMiddle_updateQ_value;
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetSuperfieldByName(g_stmt, "BitmapCompatibleV3_BitmapforSuperfieldMiddle",
            sizeof(BitmapforSuperfieldMiddle_updateQ_value), &BitmapforSuperfieldMiddle_updateQ_value);
        EXPECT_EQ(128, BitmapforSuperfieldMiddle_updateQ_value.F2value);
        EXPECT_EQ(256, BitmapforSuperfieldMiddle_updateQ_value.F4value);
        for (int i = 0; i < 128 / 8; i++) {
            ASSERT_EQ(true, 0xEE == BitmapforSuperfieldMiddle_updateQ_value.Mapbuf[i]);
        }
    }
    // 调用superfield 的查询接口

    // 将查到的结果 导出json
    uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
    char *itemJson = NULL;
    ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("%s\n", itemJson);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_004 END*******************\n");
}
/*****************************************************************************
 * Description  : 005. 设定一个包含变长字段和定长字段的schema，schema中的superfield包含bitmap字段，
     （bitmap 在superfield 的结尾）写入数据包含bitamp字段，对变长字段和bitmap进行全量更新，进行删除操作
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_005)
{
    printf("****************************DDL_037_005 START*******************\n");
    typedef struct sp_test_1 {
        uint32_t F2value;
        // int MapBeginPos;
        // int MapEndPos;
        uint8_t Mapbuf[128 / 8];
    } BitmapforSuperfieldEnd_type;  // BitmapforSuperfieldEnd格式    Superfield 用到的结构体
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_BitmapforSuperfieldEnd.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_BitmapforSuperfieldEnd_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_BitmapforSuperfieldEnd";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    BitmapforSuperfieldEnd_type BitmapforSuperfieldEnd_insertdate_value;
    BitmapforSuperfieldEnd_insertdate_value.F2value = 128;
    // BitmapforSuperfieldEnd_insertdate_value.MapBeginPos = 0;
    // BitmapforSuperfieldEnd_insertdate_value.MapEndPos = 127;
    memset(BitmapforSuperfieldEnd_insertdate_value.Mapbuf, 0xAA, 128 / 8);

    // 向表中写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F0_value = 10;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char insert_string[] = "string";
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, insert_string, (strlen(insert_string)));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_BitmapforSuperfieldEnd",
        &BitmapforSuperfieldEnd_insertdate_value, sizeof(BitmapforSuperfieldEnd_insertdate_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 10;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    // 调用superfield 的查询接口
    BitmapforSuperfieldEnd_type BitmapforSuperfieldEnd_insertdateQ_value;
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetSuperfieldByName(g_stmt, "BitmapCompatibleV3_BitmapforSuperfieldEnd",
            sizeof(BitmapforSuperfieldEnd_insertdateQ_value), &BitmapforSuperfieldEnd_insertdateQ_value);
        EXPECT_EQ(128, BitmapforSuperfieldEnd_insertdateQ_value.F2value);
        for (int i = 0; i < 128 / 8; i++) {
            ASSERT_EQ(true, 0xAA == BitmapforSuperfieldEnd_insertdateQ_value.Mapbuf[i]);
        }
    }

    // 对变长字段和bitmap进行全量更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    //更新变长字段
    char update_string[] = "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAstring";
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, &update_string, (strlen(update_string)));
    ASSERT_EQ(GMERR_OK, ret);
    //更新superfield
    BitmapforSuperfieldEnd_type BitmapforSuperfieldEnd_update_value;

    BitmapforSuperfieldEnd_update_value.F2value = 128;
    // BitmapforSuperfieldEnd_update_value.MapBeginPos = 0;
    // BitmapforSuperfieldEnd_update_value.MapEndPos = 127;
    memset(BitmapforSuperfieldEnd_update_value.Mapbuf, 0xEE, 128 / 8);

    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_BitmapforSuperfieldEnd",
        &BitmapforSuperfieldEnd_update_value, sizeof(BitmapforSuperfieldEnd_update_value));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 将查到的结果 导出json
    uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
    char *itemJson = NULL;
    ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("%s\n", itemJson);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_005 END*******************\n");
}
/*****************************************************************************
 * Description  : 006. 设定一个包含变长字段和定长字段的schema，schema中的superfield包含bitmap字段
           （只包含bitmap 没有其他字段且数量为1），写入数据包含bitamp字段，对定长字段和bitmap进行全量更新
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_006)
{
    printf("****************************DDL_037_006 START*******************\n");
    typedef struct sp_test_1 {
        // int MapBeginPos;
        // int MapEndPos;
        uint8_t Mapbuf[128 / 8];
    } BitmapforSuperfield_type;  // justBitmapSuperfield格式    Superfield 用到的结构体

    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_justBitmapSuperfield.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_justBitmapSuperfield_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_justBitmapSuperfield";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    BitmapforSuperfield_type BitmapforSuperfield_insertdate_value;
    // BitmapforSuperfield_insertdate_value.MapBeginPos = 0;
    // BitmapforSuperfield_insertdate_value.MapEndPos = 127;
    memset(BitmapforSuperfield_insertdate_value.Mapbuf, 0xAA, 128 / 8);
    // 向表中写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F0_value = 10;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    int32_t F2_value = 18;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_justBitmapSuperfield",
        &BitmapforSuperfield_insertdate_value, sizeof(BitmapforSuperfield_insertdate_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 10;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    BitmapforSuperfield_type BitmapforSuperfield_insertdateQ_value;
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetSuperfieldByName(g_stmt, "BitmapCompatibleV3_justBitmapSuperfield",
            sizeof(BitmapforSuperfield_insertdateQ_value), &BitmapforSuperfield_insertdateQ_value);
        for (int i = 0; i < 128 / 8; i++) {
            ASSERT_EQ(true, 0xAA == BitmapforSuperfield_insertdateQ_value.Mapbuf[i]);
        }
    }
    // 调用superfield 的查询接口

    // 对定长字段和bitmap进行全量更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    //更新定长字段
    int32_t F2_UpdateValue = 28;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2_UpdateValue, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);
    //全量更新bitmap
    BitmapforSuperfield_type BitmapforSuperfield_Updatedate_value;
    // BitmapforSuperfield_Updatedate_value.MapBeginPos = 0;
    // BitmapforSuperfield_Updatedate_value.MapEndPos = 127;
    memset(BitmapforSuperfield_Updatedate_value.Mapbuf, 0xDD, 128 / 8);
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_justBitmapSuperfield",
        &BitmapforSuperfield_Updatedate_value, sizeof(BitmapforSuperfield_Updatedate_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 将查到的结果 导出json
    uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
    char *itemJson = NULL;
    ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("%s\n", itemJson);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_006 END*******************\n");
}
/*****************************************************************************
 * Description  : 007.设定一个包含变长字段和定长字段的schema，schema中的superfield包含bitmap字段，
                      写入数据包含bitamp字段，对变长字段和bitmap进行全量更新
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
typedef struct bitmapforSuperfield001_type {
    uint32_t F2value;
    // int MapBeginPos;
    // int MapEndPos;
    uint8_t Mapbuf[128 / 8];
} BitmapforSuperfield001_type;  // BitmapforSuperfield001_type    Superfield 用到的结构体
TEST_F(DDL_037, DDL_037_007)
{
    printf("****************************DDL_037_007 START*******************\n");

    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_001.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_001_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_001";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 向表中写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F0_value = 10;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    BitmapforSuperfield001_type BitmapforSuperfield001_insertvalue;
    BitmapforSuperfield001_insertvalue.F2value = 100;
    // BitmapforSuperfield001_insertvalue.MapBeginPos = 0;
    // BitmapforSuperfield001_insertvalue.MapEndPos = 127 ;
    memset(BitmapforSuperfield001_insertvalue.Mapbuf, 0xBB, 128 / 8);
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_001_superfields", &BitmapforSuperfield001_insertvalue,
        sizeof(BitmapforSuperfield001_insertvalue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 10;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 调用superfield 的查询接口
    BitmapforSuperfield001_type BitmapforSuperfield001_insertvalueQ;
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetSuperfieldByName(g_stmt, "BitmapCompatibleV3_001_superfields",
            sizeof(BitmapforSuperfield001_insertvalueQ), &BitmapforSuperfield001_insertvalueQ);
        EXPECT_EQ(100, BitmapforSuperfield001_insertvalueQ.F2value);
        for (int i = 0; i < 128 / 8; i++) {
            ASSERT_EQ(true, 0xBB == BitmapforSuperfield001_insertvalueQ.Mapbuf[i]);
        }
    }

    // 对变长字段和bitmap进行全量更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    //更新变长字段
    char update_string[] = "xxxxxxxxxxxxxxxxxstring";
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, &update_string, (strlen(update_string)));
    ASSERT_EQ(GMERR_OK, ret);
    //全量更新Superfield
    BitmapforSuperfield001_type BitmapforSuperfield001_Updatevalue;
    BitmapforSuperfield001_Updatevalue.F2value = 99;
    // BitmapforSuperfield001_Updatevalue.MapBeginPos = 0;
    // BitmapforSuperfield001_Updatevalue.MapEndPos = 127;
    memset(BitmapforSuperfield001_Updatevalue.Mapbuf, 0xDD, 128 / 8);
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_001_superfields", &BitmapforSuperfield001_Updatevalue,
        sizeof(BitmapforSuperfield001_Updatevalue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 将查到的结果 导出json
    uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
    char *itemJson = NULL;
    ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("%s\n", itemJson);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_007 END*******************\n");
}
/*****************************************************************************
 * Description  : 008.设定一个包含变长字段和定长字段的schema，schema中的superfield包含bitmap字段，
                       写入数据包含bitmap字段，对数据进行replace 操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_008)
{
    printf("****************************DDL_037_008 START*******************\n");
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_001.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_001_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_001";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 向表中写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F0_value = 10;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    BitmapforSuperfield001_type BitmapforSuperfield001_insertvalue;
    BitmapforSuperfield001_insertvalue.F2value = 100;
    // BitmapforSuperfield001_insertvalue.MapBeginPos = 0;
    // BitmapforSuperfield001_insertvalue.MapEndPos = 127 ;
    memset(BitmapforSuperfield001_insertvalue.Mapbuf, 0x99, 128 / 8);
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_001_superfields", &BitmapforSuperfield001_insertvalue,
        sizeof(BitmapforSuperfield001_insertvalue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 10;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 对数据进行replace操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F0_Replacevalue = 299;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0_Replacevalue, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    char update_string[] = "xxxxxxxxxxxxxxxxxstring";
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, &update_string, (strlen(update_string)));
    ASSERT_EQ(GMERR_OK, ret);
    BitmapforSuperfield001_type BitmapforSuperfield001_replacevalue;
    BitmapforSuperfield001_replacevalue.F2value = 300;
    // BitmapforSuperfield001_replacevalue.MapBeginPos = 0;
    // BitmapforSuperfield001_replacevalue.MapEndPos = 127 ;
    memset(BitmapforSuperfield001_replacevalue.Mapbuf, 0x66, 128 / 8);
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_001_superfields", &BitmapforSuperfield001_replacevalue,
        sizeof(BitmapforSuperfield001_replacevalue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, ret);
    // 将查到的结果 导出json
    uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
    char *itemJson = NULL;
    ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("%s\n", itemJson);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_008 END*******************\n");
}
/*****************************************************************************
 * Description  : 009.设定一个包含变长字段和定长字段的schema，schema中的superfield包含bitmap字段，
                      写入数据包含bitmap字段，对数据进行merge 操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_009)
{
    printf("****************************DDL_037_009 START*******************\n");
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_001.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_001_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_001";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    // 向表中写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F0_value = 10;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    BitmapforSuperfield001_type BitmapforSuperfield001_insertvalue;
    BitmapforSuperfield001_insertvalue.F2value = 100;
    // BitmapforSuperfield001_insertvalue.MapBeginPos = 0;
    // BitmapforSuperfield001_insertvalue.MapEndPos = 127 ;
    memset(BitmapforSuperfield001_insertvalue.Mapbuf, 0xBB, 128 / 8);
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_001_superfields", &BitmapforSuperfield001_insertvalue,
        sizeof(BitmapforSuperfield001_insertvalue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 10;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 对数据进行merge操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char update_string[] = "xxxxxxxxxxxxxxxxxstring";
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, &update_string, (strlen(update_string)));
    ASSERT_EQ(GMERR_OK, ret);
    BitmapforSuperfield001_type BitmapforSuperfield001_Mergevalue;
    BitmapforSuperfield001_Mergevalue.F2value = 300;
    // BitmapforSuperfield001_Mergevalue.MapBeginPos = 0;
    // BitmapforSuperfield001_Mergevalue.MapEndPos = 127 ;
    memset(BitmapforSuperfield001_Mergevalue.Mapbuf, 0x33, 128 / 8);
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_001_superfields", &BitmapforSuperfield001_Mergevalue,
        sizeof(BitmapforSuperfield001_Mergevalue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 将查到的结果 导出json
    uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);
    char *itemJson = NULL;
    ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
    EXPECT_EQ(GMERR_OK, ret);
    printf("%s\n", itemJson);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_009 END*******************\n");
}
/*****************************************************************************
 * Description  : 011.创建简单表，表的schema包含bitmap字段，对应的nullable 是true，
     bitmap不设置值，然后进行查询，然后进行更新bitmap的值，之后再进行查询bitmap的值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_011)
{
    printf("****************************DDL_037_011 START*******************\n");
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_BitmapNullableTrue.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_BitmapNullableTrue_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_BitmapNullableTrue";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    //向表中插入数据  bitmap不写入
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int64_t F0_value = 10;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 10;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
    }

    // 对变长字段和bitmap进行全量更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    //更新变长字段
    char update_string[] = "xxxxxxxxxxxxxxxxxstring";
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_STRING, &update_string, (strlen(update_string)));
    ASSERT_EQ(GMERR_OK, ret);
    //全量更新Superfield
    BitmapforSuperfield001_type BitmapforSuperfield001_Updatevalue;
    BitmapforSuperfield001_Updatevalue.F2value = 99;
    // BitmapforSuperfield001_Updatevalue.MapBeginPos = 0;
    // BitmapforSuperfield001_Updatevalue.MapEndPos = 127;
    memset(BitmapforSuperfield001_Updatevalue.Mapbuf, 0x33, 128 / 8);
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_BitmapNullableTrue", &BitmapforSuperfield001_Updatevalue,
        sizeof(BitmapforSuperfield001_Updatevalue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_011 END*******************\n");
}
/*****************************************************************************
 * Description  : 012.创建TREE模式表， bitmap字段，在Fix_Array中，bitmap不设置值，
                     然后进行查询，然后进行更新bitmap的值，之后再进行查询bitmap的值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_012)
{
    printf("****************************DDL_037_012 START*******************\n");
    char g_errorCode01[1024] = {0};
    char g_errorCode02[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    (void)snprintf(g_errorCode02, 1024, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    typedef struct bitmapforSuperfieldAndFix_Array_type {
        // int MapBeginPos;
        // int MapEndPos;
        uint8_t Mapbuf[128 / 8];
    } BitmapforSuperfieldAndFix_Array_type;  // BitmapforSuperfieldAndFix_Array_type    Superfield 用到的结构体
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_BitmapforSuperfieldAndFix_Array.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_BitmapforSuperfieldAndFix_Array_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_BitmapforSuperfieldAndFix_Array";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    // 写入数据 bitmap不设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1;
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F0_value = 100;
    ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        int64_t A0_value = 120 + i;
        ret = GmcNodeSetPropertyByName(T1, "A0", GMC_DATATYPE_INT64, &A0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(T1, &T1);
        if (i < 2) {
            ASSERT_EQ(GMERR_OK, ret);
        } else {
            ASSERT_EQ(GMERR_NO_DATA, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 100;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
    }
    // 更新数据 bitmap设置值
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootUpdate, *T1Update;
    ret = GmcGetRootNode(g_stmt, &rootUpdate);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootUpdate, "T1", &T1Update);
    EXPECT_EQ(GMERR_OK, ret);
    // int64_t F0_valueUpdate = 120;
    // ret = GmcNodeSetPropertyByName(rootUpdate, "F0", GMC_DATATYPE_INT64, &F0_valueUpdate, sizeof(int64_t));
    // ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        BitmapforSuperfieldAndFix_Array_type BitmapforSuperfieldAndFix_Array_insertvalue;
        // BitmapforSuperfieldAndFix_Array_insertvalue.MapBeginPos = 0;
        // BitmapforSuperfieldAndFix_Array_insertvalue.MapEndPos = 127;
        memset(BitmapforSuperfieldAndFix_Array_insertvalue.Mapbuf, 0x55, 128 / 8);
        int64_t A0_value = 120 + i * 10;
        ret = GmcNodeSetPropertyByName(T1Update, "A0", GMC_DATATYPE_INT64, &A0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetSuperfieldByName(T1Update, "BitmapCompatibleV3_BitmapforSuperfieldAndFix_Array1",
            &BitmapforSuperfieldAndFix_Array_insertvalue, sizeof(BitmapforSuperfieldAndFix_Array_insertvalue));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeGetNextElement(T1Update, &T1Update);
        if (i < 2) {
            ASSERT_EQ(GMERR_OK, ret);
        } else {
            ASSERT_EQ(GMERR_NO_DATA, ret);
            ret = testGmcGetLastError(NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priKUpdate = 100;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priKUpdate, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priKUpdate);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
    }
    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_012 END*******************\n");
}
/*****************************************************************************
 * Description  : 013.创建TREE模式表， bitmap字段，在vector中，bitmap不设置值，
                    然后进行查询，然后进行更新bitmap的值，之后再进行查询bitmap的值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_013)
{
    printf("****************************DDL_037_013 START*******************\n");
    typedef struct bitmapforSuperfieldAndVector_type {
        // int MapBeginPos;
        // int MapEndPos;
        uint8_t Mapbuf[128 / 8];
    } BitmapforSuperfieldAndVector_type;  // BitmapforSuperfieldAndFix_Array_type    Superfield 用到的结构体

    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_BitmapforSuperfieldAndVector.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_BitmapforSuperfieldAndVector_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_BitmapforSuperfieldAndVector";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    // 插入数据 其中vector下的bitmap 不写入数据 默认值全为0
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1;
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F0_value = 100;
    ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t V0_value = 120 + i;
        ret = GmcNodeSetPropertyByName(T1, "V0", GMC_DATATYPE_INT64, &V0_value, sizeof(V0_value));
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 100;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
    }
    //  对数据做更新操作  bitmap做全量更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootUpdate, *T1Update;
    ret = GmcGetRootNode(g_stmt, &rootUpdate);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootUpdate, "T1", &T1Update);
    EXPECT_EQ(GMERR_OK, ret);
    // int64_t F0_valueUpdate = 120;
    // ret = GmcNodeSetPropertyByName(rootUpdate, "F0", GMC_DATATYPE_INT64, &F0_valueUpdate, sizeof(int64_t));
    // ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcNodeGetElementByIndex(T1Update, i, &T1Update);
        ASSERT_EQ(GMERR_OK, ret);
        // ret = GmcNodeAppendElement(T1Update, &T1Update);
        // EXPECT_EQ(GMERR_OK, ret);

        BitmapforSuperfieldAndVector_type BitmapforSuperfieldAndVector_insertvalue;
        // BitmapforSuperfieldAndVector_insertvalue.MapBeginPos = 0;
        // BitmapforSuperfieldAndVector_insertvalue.MapEndPos = 127;
        memset(BitmapforSuperfieldAndVector_insertvalue.Mapbuf, 0x55, 128 / 8);
        int64_t V0_value = 120 + i * 10;
        ret = GmcNodeSetPropertyByName(T1Update, "V0", GMC_DATATYPE_INT64, &V0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetSuperfieldByName(T1Update, "BitmapCompatibleV3_BitmapforSuperfieldAndVector1",
            &BitmapforSuperfieldAndVector_insertvalue, sizeof(BitmapforSuperfieldAndVector_insertvalue));
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priKUpdate = 100;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priKUpdate, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priKUpdate);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_013 END*******************\n");
}
/*****************************************************************************
 * Description  : 014.创建TREE模式表， bitmap字段，在array中，bitmap不设置值，
                 然后进行查询，然后进行全量更新bitmap的值，之后再进行查询bitmap的值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_014)
{
    printf("****************************DDL_037_014 START*******************\n");
    typedef struct bitmapforSuperfieldAndArray_type {
        // int MapBeginPos;
        // int MapEndPos;
        uint8_t Mapbuf[128 / 8];
    } BitmapforSuperfieldAndArray_type;  // BitmapforSuperfieldAndArray_type    Superfield 用到的结构体
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_BitmapforSuperfieldAndArray.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_BitmapforSuperfieldAndArray_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_BitmapforSuperfieldAndArray";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    // ret=testGmcGetLastError(NULL);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    // 插入数据 其中vector下的bitmap 不写入数据 默认值全为0
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1;
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F0_value = 100;
    ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t A0_value = 120 + i;
        ret = GmcNodeSetPropertyByName(T1, "A0", GMC_DATATYPE_INT64, &A0_value, sizeof(A0_value));
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 100;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
    }
    //  对数据做更新操作  bitmap做全量更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootUpdate, *T1Update;
    ret = GmcGetRootNode(g_stmt, &rootUpdate);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootUpdate, "T1", &T1Update);
    EXPECT_EQ(GMERR_OK, ret);
    // int64_t F0_valueUpdate = 120;
    // ret = GmcNodeSetPropertyByName(rootUpdate, "F0", GMC_DATATYPE_INT64, &F0_valueUpdate, sizeof(int64_t));
    // ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcNodeGetElementByIndex(T1Update, i, &T1Update);
        ASSERT_EQ(GMERR_OK, ret);
        // ret = GmcNodeAppendElement(T1Update, &T1Update);
        // EXPECT_EQ(GMERR_OK, ret);

        BitmapforSuperfieldAndArray_type BitmapforSuperfieldAndArray_insertvalue;
        // BitmapforSuperfieldAndArray_insertvalue.MapBeginPos = 0;
        // BitmapforSuperfieldAndArray_insertvalue.MapEndPos = 127;
        memset(BitmapforSuperfieldAndArray_insertvalue.Mapbuf, 0x55, 128 / 8);
        int64_t A0_value = 120 + i * 10;
        ret = GmcNodeSetPropertyByName(T1Update, "A0", GMC_DATATYPE_INT64, &A0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetSuperfieldByName(T1Update, "BitmapCompatibleV3_BitmapforSuperfieldAndArray1",
            &BitmapforSuperfieldAndArray_insertvalue, sizeof(BitmapforSuperfieldAndArray_insertvalue));
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priKUpdate = 100;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priKUpdate, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priKUpdate);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_014 END*******************\n");
}
/*****************************************************************************
 * Description  : 015.创建TREE模式表， bitmap字段，在array和vector中均存在，
   bitmap不设置值，然后进行查询，然后进行全量更新bitmap的值，之后再进行查询bitmap的值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_015)
{
    printf("****************************DDL_037_015 START*******************\n");
    typedef struct bitmapforSuperfieldAndArray_type {
        // int MapBeginPos;
        // int MapEndPos;
        uint8_t Mapbuf[128 / 8];
    } BitmapforSuperfieldAndArray_Vector_type;  // BitmapforSuperfieldAndArray_type    Superfield 用到的结构体

    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_BitmapforSuperfieldAndArray_Vector.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_BitmapforSuperfieldAndArray_Vector_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_BitmapforSuperfieldAndArray_Vector";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    // 现在建表 表中的bitmap给改成了 其他定长字段
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    GmcNodeT *root, *T1, *T2;
    ret = GmcGetRootNode(g_stmt, &root);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &T1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T2", &T2);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F0_value = 100;
    ret = GmcNodeSetPropertyByName(root, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcNodeAppendElement(T1, &T1);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t A0_value = 120 + i;
        ret = GmcNodeSetPropertyByName(T1, "A0", GMC_DATATYPE_INT64, &A0_value, sizeof(A0_value));
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 3; i++) {
        ret = GmcNodeAppendElement(T2, &T2);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t V0_value = 120 + i * 2;
        ret = GmcNodeSetPropertyByName(T2, "V0", GMC_DATATYPE_INT64, &V0_value, sizeof(V0_value));
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 100;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
    }
    //  对数据做更新操作  bitmap做全量更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    GmcNodeT *rootUpdate, *T1Update, *T2Update;
    ret = GmcGetRootNode(g_stmt, &rootUpdate);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootUpdate, "T1", &T1Update);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcNodeGetChild(rootUpdate, "T2", &T2Update);
    EXPECT_EQ(GMERR_OK, ret);
    // int64_t F0_valueUpdate = 120;
    // ret = GmcNodeSetPropertyByName(rootUpdate, "F0", GMC_DATATYPE_INT64, &F0_valueUpdate, sizeof(int64_t));
    // ASSERT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 3; i++) {
        ret = GmcNodeGetElementByIndex(T1Update, i, &T1Update);
        ASSERT_EQ(GMERR_OK, ret);
        BitmapforSuperfieldAndArray_Vector_type BitmapforSuperfieldAndArray_insertvalue;
        // BitmapforSuperfieldAndArray_insertvalue.MapBeginPos = 0;
        // BitmapforSuperfieldAndArray_insertvalue.MapEndPos = 127;
        memset(BitmapforSuperfieldAndArray_insertvalue.Mapbuf, 0x55, 128 / 8);
        int64_t A0_value = 120 + i * 10;
        ret = GmcNodeSetPropertyByName(T1Update, "A0", GMC_DATATYPE_INT64, &A0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetSuperfieldByName(T1Update, "BitmapCompatibleV3_BitmapforSuperfieldAndArray_Vector1",
            &BitmapforSuperfieldAndArray_insertvalue, sizeof(BitmapforSuperfieldAndArray_insertvalue));
        ASSERT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 3; i++) {
        ret = GmcNodeGetElementByIndex(T2Update, i, &T2Update);
        ASSERT_EQ(GMERR_OK, ret);
        BitmapforSuperfieldAndArray_Vector_type BitmapforSuperfieldAndVector_insertvalue;
        // BitmapforSuperfieldAndVector_insertvalue.MapBeginPos = 0;
        // BitmapforSuperfieldAndVector_insertvalue.MapEndPos = 127;
        memset(BitmapforSuperfieldAndVector_insertvalue.Mapbuf, 0x55, 128 / 8);
        int64_t V0_value = 120 + i * 10;
        ret = GmcNodeSetPropertyByName(T2Update, "V0", GMC_DATATYPE_INT64, &V0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcNodeSetSuperfieldByName(T2Update, "BitmapCompatibleV3_BitmapforSuperfieldAndArray_Vector2",
            &BitmapforSuperfieldAndVector_insertvalue, sizeof(BitmapforSuperfieldAndVector_insertvalue));
        ASSERT_EQ(GMERR_OK, ret);
    }
    ret = GmcExecute(g_stmt);
    ASSERT_EQ(GMERR_OK, ret);
    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priKUpdate = 100;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priKUpdate, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priKUpdate);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_015 END*******************\n");
}
/*****************************************************************************
 * Description  : 016.设定一个scheme 包含bitmap字段，建表，批量写入数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_016)
{
    printf("****************************DDL_037_016 START*******************\n");
    // 适配 批量接口修改
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;

    typedef struct bitmapforSuperfieldPrepare {
        uint32_t F2value;
        // int MapBeginPos;
        // int MapEndPos;
        uint8_t Mapbuf[128 / 8];
    } BitmapforSuperfieldPrepare;  // BitmapforSuperfieldStart格式    Superfield 用到的结构体
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_001.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_001_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_001";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    // 批量写入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 10;
    // 批量准备
    ret = GmcBatchPrepare(g_conn, NULL, &batch);  // 第二个参数设置为NULL 设置为batchOption 有其他变化
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = start_num; i < end_num; i++) {
        int64_t F0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
        ASSERT_EQ(GMERR_OK, ret);

        BitmapforSuperfieldPrepare BitmapforSuperfieldPrepare_inertvalue;
        // BitmapforSuperfieldPrepare_inertvalue.MapBeginPos = 0;
        // BitmapforSuperfieldPrepare_inertvalue.MapEndPos = 127;
        memset(BitmapforSuperfieldPrepare_inertvalue.Mapbuf, 0xff, 128 / 8);
        BitmapforSuperfieldPrepare_inertvalue.F2value = 256;
        ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_001_superfields",
            &BitmapforSuperfieldPrepare_inertvalue, sizeof(BitmapforSuperfieldPrepare_inertvalue));
        EXPECT_EQ(GMERR_OK, ret);

        // 新的add DML接口
        ret = GmcBatchAddDML(batch, g_stmt);  // ret = GmcBatchAddVertexDML(stmt,GMC_CMD_INSERT_VERTEX);
        EXPECT_EQ(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    // 新的Execute 接口
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    ASSERT_EQ(GMERR_OK, ret);
    ASSERT_EQ(end_num, totalNum);
    ASSERT_EQ(end_num, successNum);

    // 查询插入的数据   查询数据之前先做 Prepare操作
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        int64_t priKUpdate = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priKUpdate, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // 检查读取的数据是否无误
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priKUpdate);
            EXPECT_EQ(GMERR_OK, ret);
            uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
            char *itemJson = NULL;
            ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
            EXPECT_EQ(GMERR_OK, ret);
            printf("%s\n", itemJson);
        }
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_016 END*******************\n");
}
/*****************************************************************************
 * Description  : 017 设定一个scheme 包含bitmap字段，bitmap的size超过约束的最大长度 ，是8 * 4 * 1024+8，进行建表
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_017)
{
    printf("****************************DDL_037_017 START*******************\n");
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_BitmapOverMaxSize.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_BitmapOverMaxSize_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_BitmapOverMaxSize";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);  // bitmap size 超过最大限制 返回错误码
    free(schema_json);
    printf("****************************DDL_037_017 END*******************\n");
}
/*****************************************************************************
 * Description  : 018 设定一个scheme 包含bitmap字段，bitmap的size不是8的倍数，进行建表
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
TEST_F(DDL_037, DDL_037_018)
{
    printf("****************************DDL_037_018 START*******************\n");
    char g_errorCode01[1024] = {0};
    (void)snprintf(g_errorCode01, 1024, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_BitmapIncompatibleEightTimes.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_BitmapIncompatibleEightTimes_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_BitmapIncompatibleEightTimes";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, ret);  // bitmap size 不是8的倍数  返回错误码
    free(schema_json);
    printf("****************************DDL_037_018 END*******************\n");
}
/*****************************************************************************
 * Description  : 019 多线程，建表表中包含bitmap，且bitmap在superfield中，向表中写数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : wk/wwx1038088
 * Modification : Create function
 * *****************************************************************************/
void *thread_InsertVertex(void *args)
{
    typedef struct bitmapforSuperfieldthread {
        uint32_t F2value;
        // int MapBeginPos;
        // int MapEndPos;
        uint8_t Mapbuf[128 / 8];
    } BitmapforSuperfieldthread;  // BitmapforSuperfieldStart格式    Superfield 用到的结构体

    int ret = 0;
    GmcStmtT *stmt;
    GmcConnT *conn;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    int count = (int)(*(int *)args);  // 通过count构造来使并发插入不出现相同的主键值
    void *vertexLabel;
    char labelName[128] = "BitmapCompatibleV3_001";   //表名
    char keyName[128] = "BitmapCompatibleV3_001_PK";  // 主键名
    char superfiledName[128] = "BitmapCompatibleV3_001_superfields";
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        int64_t F0_value = count + i;
        // printf("******************** insert balue is %d *********************\n",i);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        BitmapforSuperfieldthread BitmapforSuperfieldthread_inertvalue;
        // BitmapforSuperfieldthread_inertvalue.MapBeginPos = 0;
        // BitmapforSuperfieldthread_inertvalue.MapEndPos = 127;
        memset(BitmapforSuperfieldthread_inertvalue.Mapbuf, 0xff, 128 / 8);
        BitmapforSuperfieldthread_inertvalue.F2value = 256;
        ret = GmcSetSuperfieldByName(stmt, "BitmapCompatibleV3_001_superfields", &BitmapforSuperfieldthread_inertvalue,
            sizeof(BitmapforSuperfieldthread_inertvalue));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    // 查询插入的数据   查询数据之前先做 Prepare操作
    for (int i = 0; i < 10; i++) {
        // printf(" ****************** i is %d ****************\n",i);
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        int64_t priK = count + i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        // 检查读取的数据是否无误
        while (true) {
            bool isFinish;
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &priK);
            EXPECT_EQ(GMERR_OK, ret);
            uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
            char *itemJson = NULL;
            ret = GmcDumpVertexToJson(stmt, type, &itemJson);
            EXPECT_EQ(GMERR_OK, ret);
            printf("%s\n", itemJson);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
TEST_F(DDL_037, DDL_037_019)
{
    printf("****************************DDL_037_019 START*******************\n");
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_001.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_001_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_001";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    //创建20个线程 同步向一个表中写10条数据 都包含superfield
    pthread_t thr_arr[20];
    void *thr_ret[20];
    int i;
    int index[20] = {0};
    for (i = 0; i < 20; i++) {
        index[i] = i * 100;

        pthread_create(&thr_arr[i], NULL, thread_InsertVertex, (void *)&index[i]);
    }
    for (i = 0; i < 20; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    printf("****************************DDL_037_019 END*******************\n");
}

TEST_F(DDL_037, DDL_037_020)
{
    printf("****************************DDL_037_020 START*******************\n");
    typedef struct bitmapforSuperfield1024 {
        uint32_t F2value;
        // int MapBeginPos;
        // int MapEndPos;
        uint8_t Mapbuf[128 / 8];
    } BitmapforSuperfield1024;  // BitmapforSuperfieldStart格式    Superfield 用到的结构体

// 建立1023个链接
#define MAX_NUM MAX_CONN_SIZE  //适配IOT和euler最大链接
    uint32_t CurentconnNum = 0;
    ret = testGetConnNum(&CurentconnNum);
    EXPECT_EQ(GMERR_OK, ret);
    GmcConnT *conn_t[MAX_NUM - 1] = {0};
    int i;
    int ret;
    for (i = 0; i < MAX_NUM - 4 - CurentconnNum; i++) {
        ret = testGmcConnect(&conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("connect_1024_001 Alloc success: %d\n", i);
    }
    char *schema_json = NULL;
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    readJanssonFile("schema_file/BitmapCompatibleV3_001.gmjson", &schema_json);
    EXPECT_NE((void *)NULL, schema_json);
    char keyName[128] = "BitmapCompatibleV3_001_PK";  // 主键名
    char labelName[128] = "BitmapCompatibleV3_001";   //表名
    ret = GmcCreateVertexLabel(g_stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t F0_value = 100;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT64, &F0_value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    BitmapforSuperfield1024 BitmapforSuperfield1024_inertvalue;
    // BitmapforSuperfield1024_inertvalue.MapBeginPos = 0;
    // BitmapforSuperfield1024_inertvalue.MapEndPos = 127;
    memset(BitmapforSuperfield1024_inertvalue.Mapbuf, 0xff, 128 / 8);
    BitmapforSuperfield1024_inertvalue.F2value = 256;
    ret = GmcSetSuperfieldByName(g_stmt, "BitmapCompatibleV3_001_superfields", &BitmapforSuperfield1024_inertvalue,
        sizeof(BitmapforSuperfield1024_inertvalue));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    //查询数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    int64_t priK = 100;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT64, &priK, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    // 检查读取的数据是否无误
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(g_stmt, "F0", GMC_DATATYPE_INT64, &priK);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t type = GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(0);  // 定义了数据类型
        char *itemJson = NULL;
        ret = GmcDumpVertexToJson(g_stmt, type, &itemJson);
        EXPECT_EQ(GMERR_OK, ret);
        printf("%s\n", itemJson);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    //断开1023个链接
    for (i = 0; i < MAX_NUM - 4 - CurentconnNum; i++) {
        ret = testGmcDisconnect(conn_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        printf("connect_1024_001 Free success: %d\n", i);
    }

    printf("****************************DDL_037_020 END*******************\n");
}
