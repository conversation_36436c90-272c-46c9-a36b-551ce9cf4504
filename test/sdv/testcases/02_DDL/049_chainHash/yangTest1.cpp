/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2012-2018. All rights reserved.
 Description  :chainHash能力补充--yang表测试
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2023/08/11
**************************************************************************** */
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"

#include "chainHashTest.h"

class yangTest1 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void yangTest1::SetUpTestCase()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 创建epoll
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void yangTest1::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void yangTest1::SetUp()
{
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connAsync1, &g_stmtAsync1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        ret = GmcAllocStmt(g_connAsync, &g_stmtAsync2[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // 避免其他用例失败影响下一个执行的用例
    AsyncUserDataT tdata = {0};
    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namesapceName, ClearNSCallbak, &tdata);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropNamespaceAsync(g_stmtAsync, g_namesapceName, drop_namespace_callback, &tdata);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_trxConfig.readOnly = false;
    g_trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    AsyncUserDataT data = {0};
    // 配置ns级别，RR+乐观事务模式
    GmcNspCfgT yangNspCfg = {};
    yangNspCfg.tablespaceName = NULL;
    yangNspCfg.namespaceName = g_namesapceName;
    yangNspCfg.userName = "userC";
    yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &yangNspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcUseNamespaceAsync(g_stmtAsync, g_namesapceName, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 64;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

void yangTest1::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开连接
    for (int i = 0; i < 30; i++) {
        GmcFreeStmt(g_stmtAsync2[i]);
    }
    AsyncUserDataT data = {0};
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 删表以及namespace
    ret = GmcClearNamespaceAsync(g_stmtAsync, g_namesapceName, ClearNSCallbak, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcDropNamespaceAsync(g_stmtAsync, g_namesapceName, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync1, g_stmtAsync1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 001.全打散模型下create操作，diff查询，subTree查询
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateYangComplex(g_stmtAsync, data);
    // 批量插入，查询diff
    GmcStmtT *stmtAsync[3];
    for (int i = 0; i < 3; i++) {
        ret = GmcAllocStmt(g_connAsync, &stmtAsync[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    GmcNodeT *nodeAsync[11];
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); 

    testSetRootProperty(g_stmtAsync, batch, 100, &nodeAsync[0]);
    testSetChildProperty(nodeAsync[0], g_stmtAsync, batch, 1, 100, "T1", false, &nodeAsync[1]);
    testSetChildProperty(nodeAsync[0], g_stmtAsync, batch, 1, 100, "T2", true, &nodeAsync[2]);
    testSetChildProperty(nodeAsync[2], g_stmtAsync, batch, 1, 100, "T21", false, &nodeAsync[4]);
    testSetChildProperty(nodeAsync[4], g_stmtAsync, batch, 1, 100, "T211", false, &nodeAsync[5]);
    testSetChildProperty(nodeAsync[4], g_stmtAsync, batch, 1, 100, "T213", true, &nodeAsync[6]);
    testSetChildProperty(nodeAsync[6], g_stmtAsync, batch, 1, 100, "T2131", false, &nodeAsync[7]);
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetChildProperty1(g_stmtAsync, stmtAsync[1], batch, 5, 100, "T212", false);
    testSetChildProperty2(g_stmtAsync, stmtAsync[0], batch, 5, 100, "T3", false, &nodeAsync[3]);
    testSetChildProperty(nodeAsync[3], g_stmtAsync, batch, 1, 100, "T31", false, &nodeAsync[8]);
    testSetChildProperty(nodeAsync[3], g_stmtAsync, batch, 1, 100, "T33", true, &nodeAsync[9]);
    testSetChildProperty(nodeAsync[9], g_stmtAsync, batch, 1, 100, "T331", false, &nodeAsync[10]);
    ret = GmcBatchAddDML(batch, stmtAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetChildProperty1(stmtAsync[0], stmtAsync[2], batch, 5, 100, "T32", false);
    // 批处理提交
    testBatchExecuteAndWait(batch, data, 16, 16);
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffMix62, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeNode(nodeAsync[0]);
    GmcFreeNode(nodeAsync[3]);
    for (int i = 0; i < 3; i++) {
        GmcFreeStmt(stmtAsync[i]);
    }

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/containerReply001.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = "T0",
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.全打散模型下replace操作，diff查询，subTree查询
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateYangComplex(g_stmtAsync, data);
    // 批量插入，查询diff
    GmcStmtT *stmtAsync[3];
    for (int i = 0; i < 3; i++) {
        ret = GmcAllocStmt(g_connAsync, &stmtAsync[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    GmcNodeT *nodeAsync[11];
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); 

    testSetRootPropertyReplace(g_stmtAsync, batch, 100, &nodeAsync[0]);
    testSetChildPropertyReplace(nodeAsync[0], g_stmtAsync, batch, 1, 100, "T1", false, &nodeAsync[1]);
    testSetChildPropertyReplace(nodeAsync[0], g_stmtAsync, batch, 1, 100, "T2", true, &nodeAsync[2]);
    testSetChildPropertyReplace(nodeAsync[2], g_stmtAsync, batch, 1, 100, "T21", false, &nodeAsync[4]);
    testSetChildPropertyReplace(nodeAsync[4], g_stmtAsync, batch, 1, 100, "T211", false, &nodeAsync[5]);
    testSetChildPropertyReplace(nodeAsync[4], g_stmtAsync, batch, 1, 100, "T213", true, &nodeAsync[6]);
    testSetChildPropertyReplace(nodeAsync[6], g_stmtAsync, batch, 1, 100, "T2131", false, &nodeAsync[7]);
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetChildPropertyReplace1(g_stmtAsync, stmtAsync[1], batch, 5, 100, "T212", false);
    testSetChildPropertyReplace2(g_stmtAsync, stmtAsync[0], batch, 5, 100, "T3", false, &nodeAsync[3]);
    testSetChildPropertyReplace(nodeAsync[3], g_stmtAsync, batch, 1, 100, "T31", false, &nodeAsync[8]);
    testSetChildPropertyReplace(nodeAsync[3], g_stmtAsync, batch, 1, 100, "T33", true, &nodeAsync[9]);
    testSetChildPropertyReplace(nodeAsync[9], g_stmtAsync, batch, 1, 100, "T331", false, &nodeAsync[10]);
    ret = GmcBatchAddDML(batch, stmtAsync[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetChildPropertyReplace1(stmtAsync[0], stmtAsync[2], batch, 5, 100, "T32", false);
    // 批处理提交
    testBatchExecuteAndWait(batch, data, 16, 16);
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffMix62, data);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcFreeNode(nodeAsync[0]);
    GmcFreeNode(nodeAsync[3]);
    for (int i = 0; i < 3; i++) {
        GmcFreeStmt(stmtAsync[i]);
    }

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/containerReply001.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = "T0",
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.全打散模型下merge操作，diff查询，subTree查询
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateYangComplex(g_stmtAsync, data);
    // 预置数据
    testYangInsertComplex(g_connAsync, g_stmtAsync, batch);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "T0", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmtAsync, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(g_stmtAsync, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置choice节点
    GmcNodeT *ChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "T2", GMC_OPERATION_NONE, &ChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置case节点
    GmcNodeT *ChildChildNode = NULL;
    ret = GmcYangEditChildNode(ChildNode, "T21", GMC_OPERATION_MERGE, &ChildChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    testYangSetVertexProperty(ChildChildNode, 20, GMC_YANG_PROPERTY_OPERATION_MERGE);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    testBatchExecuteAndWait(batch, data, 1, 1);
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffMix63, data);
    // 提交事务
    ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/containerReply.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = "T0",
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.全打散模型下delete操作，diff查询，subTree查询
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateYangComplex(g_stmtAsync, data);
    // 预置数据
    testYangInsertComplex(g_connAsync, g_stmtAsync, batch);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "T0", GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmtAsync, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    testBatchExecuteAndWait(batch, data, 1, 1);
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffMix004, data);
    // 提交事务
    ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = (char*)"{}";
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = "T0",
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(containerContentJson);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.全打散模型下remove操作，diff查询，subTree查询
 Input        : None
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateYangComplex(g_stmtAsync, data);
    // 预置数据
    testYangInsertComplex(g_connAsync, g_stmtAsync, batch);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "T0", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmtAsync, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *conRootNode = NULL;
    ret = GmcGetRootNode(g_stmtAsync, &conRootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置contain节点
    GmcNodeT *ChildNode = NULL;
    ret = GmcYangEditChildNode(conRootNode, "T1", GMC_OPERATION_REMOVE_GRAPH, &ChildNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    testBatchExecuteAndWait(batch, data, 1, 1);
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffMix005, data);
    // 提交事务
    ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/containerReply005.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = "T0",
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :006.全打散模型下none操作，diff查询，subTree查询
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateYangComplex(g_stmtAsync, data);
    // 预置数据
    testYangInsertComplex(g_connAsync, g_stmtAsync, batch);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "T0", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmtAsync, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static vector<string> expectDiff = {};
    // 批处理提交
    testBatchExecuteAndWait(batch, data, 1, 1);
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiff, data);
    // 提交事务
    ret = GmcTransCommitAsync(g_connAsync, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/containerReply001.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = "T0",
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :007.部分打散模型下create操作，diff查询，subTree查询
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateTreeChoiceCaseList(g_stmtAsync, data);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, g_containRootName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(g_stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode, "P1", GMC_OPERATION_INSERT, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置子节点T1
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[1], "T1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[1], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A1", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置子节点T2
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[2], "T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[2], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A2", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置子节点T3
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[3], "T3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[3], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A3", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置子节点T4
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[4], "T4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[4], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A4", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置子节点T5
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[5], "T5", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[5], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A5", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, 26, 26);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffBaseChoiceCaseList, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/containerReply007.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = g_containRootName2,
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :008.部分打散模型下replace操作，diff查询，subTree查询
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateTreeChoiceCaseList(g_stmtAsync, data);
    // 预置数据
    testYangPresetDataList(g_connAsync, g_stmtAsync, batch, data, true);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, g_containRootName2, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    ret = GmcGetRootNode(g_stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_REPLACE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, 1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffBase008, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/containerReply008.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = g_containRootName2,
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :009.部分打散模型下merge操作，diff查询，subTree查询
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateTreeChoiceCaseList(g_stmtAsync, data);
    // 预置数据
    testYangPresetDataList(g_connAsync, g_stmtAsync, batch, data, true);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, g_containRootName2, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    ret = GmcGetRootNode(g_stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_NONE, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode, "P1", GMC_OPERATION_MERGE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode1, 200, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, 1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffBase009, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/containerReply009.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = g_containRootName2,
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :010.部分打散模型下delete操作，diff查询，subTree查询
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateTreeChoiceCaseList(g_stmtAsync, data);
    // 预置数据
    testYangPresetDataList(g_connAsync, g_stmtAsync, batch, data, true);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, g_containRootName2, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(g_stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T0Node = NULL;
    GmcNodeT *P1Node = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_NONE, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, "P1", GMC_OPERATION_NONE, &P1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[1], "T1", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[1], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtAsync2[1], 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtAsync2[1], g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[2], "T2", GMC_OPERATION_DELETE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[2], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtAsync2[2], 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtAsync2[2], g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, 11, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffBase010, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/containerReply010.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = g_containRootName2,
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :011.部分打散模型下remove操作，diff查询，subTree查询
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateTreeChoiceCaseList(g_stmtAsync, data);
    // 预置数据
    testYangPresetDataList(g_connAsync, g_stmtAsync, batch, data, true);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, g_containRootName2, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(g_stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T0Node = NULL;
    GmcNodeT *P1Node = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_NONE, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, "P1", GMC_OPERATION_NONE, &P1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[1], "T1", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[1], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtAsync2[1], 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtAsync2[1], g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[2], "T2", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[2], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmtAsync2[2], 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmtAsync2[2], g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, 11, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffBase010, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/containerReply010.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = g_containRootName2,
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :012.部分打散模型下none操作，diff查询，subTree查询
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateTreeChoiceCaseList(g_stmtAsync, data);
    // 预置数据
    testYangPresetDataList(g_connAsync, g_stmtAsync, batch, data, true);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, g_containRootName2, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(g_stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T0Node = NULL;
    GmcNodeT *P1Node = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_NONE, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, "P1", GMC_OPERATION_NONE, &P1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, 1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    static vector<string> expectDiffBase012 = {};
    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffBase012, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/containerReply007.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = g_containRootName2,
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

void *createTransThread(void *args)
{
    int id = *(int *)args;
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    memset(&data, 0, sizeof(data));
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    uint32_t keyValue = 100;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcStmtT *stmtAsync2[30] = {0};
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 10; j++) {
        ret = GmcAllocStmt(connAsync, &stmtAsync2[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespaceAsync(stmtAsync, g_namesapceName, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(data));

    ret = GmcTransStartAsync(connAsync, &trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmtAsync, g_containRootName2, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T0Node = NULL;
    GmcNodeT *P1Node = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_NONE, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, "P1", GMC_OPERATION_NONE, &P1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置子节点T1
    for (int i = 6; i < 12; i++) {
        ret = testGmcPrepareStmtByLabelName(stmtAsync2[1], "T1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtAsync, stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtAsync2[1], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A1", GMC_OPERATION_INSERT, &childNode);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置子节点T2
    for (int i = 6; i < 12; i++) {
        ret = testGmcPrepareStmtByLabelName(stmtAsync2[2], "T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtAsync, stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtAsync2[2], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A2", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    if (data.status == GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(13, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(13, data.succNum);

        ret = GmcBatchDestroy(batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));
        // 提交事务
        ret = GmcTransCommitAsync(connAsync, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (data.status == GMERR_RESTRICT_VIOLATION) {
            memset(&data, 0, sizeof(AsyncUserDataT));
            ret = GmcTransRollBackAsync(connAsync, trans_commit_callback, &data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        }
    } else {
        ret = GmcBatchDestroy(batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcTransRollBackAsync(connAsync, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }
    // 断开连接
    for (int j = 0; j < 10; j++) {
        GmcFreeStmt(stmtAsync2[j]);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *replaceTransThread(void *args)
{
    int id = *(int *)args;
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    memset(&data, 0, sizeof(data));
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    uint32_t keyValue = 100;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcStmtT *stmtAsync2[30] = {0};

    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 10; j++) {
        ret = GmcAllocStmt(connAsync, &stmtAsync2[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespaceAsync(stmtAsync, g_namesapceName, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(data));

    ret = GmcTransStartAsync(connAsync, &trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmtAsync, g_containRootName2, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    ret = GmcGetRootNode(stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_REPLACE_GRAPH, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = GmcTransCommitAsync(connAsync, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcTransRollBackAsync(connAsync, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }
    // 断开连接
    for (int j = 0; j < 10; j++) {
        GmcFreeStmt(stmtAsync2[j]);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *mergeTransThread(void *args)
{
    int id = *(int *)args;
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    memset(&data, 0, sizeof(data));
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    uint32_t keyValue = 100;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcStmtT *stmtAsync2[30] = {0};
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 10; j++) {
        ret = GmcAllocStmt(connAsync, &stmtAsync2[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespaceAsync(stmtAsync, g_namesapceName, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(data));

    ret = GmcTransStartAsync(connAsync, &trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmtAsync, g_containRootName2, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    ret = GmcGetRootNode(stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_NONE, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode, "P1", GMC_OPERATION_MERGE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode1, 200, GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = GmcTransCommitAsync(connAsync, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcTransRollBackAsync(connAsync, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }
    // 断开连接
    for (int j = 0; j < 10; j++) {
        GmcFreeStmt(stmtAsync2[j]);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *removeTransThread(void *args)
{
    int id = *(int *)args;
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    memset(&data, 0, sizeof(data));
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    uint32_t keyValue = 100;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcStmtT *stmtAsync2[30] = {0};
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 10; j++) {
        ret = GmcAllocStmt(connAsync, &stmtAsync2[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespaceAsync(stmtAsync, g_namesapceName, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(data));

    ret = GmcTransStartAsync(connAsync, &trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmtAsync, g_containRootName2, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T0Node = NULL;
    GmcNodeT *P1Node = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_NONE, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, "P1", GMC_OPERATION_NONE, &P1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(stmtAsync2[1], "T1", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtAsync, stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtAsync2[1], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtAsync2[1], 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtAsync2[1], g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(stmtAsync2[2], "T2", GMC_OPERATION_REMOVE_GRAPH);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, stmtAsync, stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmtAsync2[2], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmtAsync2[2], 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmtAsync2[2], g_keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(11, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(11, data.succNum);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = GmcTransCommitAsync(connAsync, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcTransRollBackAsync(connAsync, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }
    // 断开连接
    for (int j = 0; j < 10; j++) {
        GmcFreeStmt(stmtAsync2[j]);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *noneTransThread(void *args)
{
    int id = *(int *)args;
    int ret = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    memset(&data, 0, sizeof(data));
    GmcTxConfigT trxConfig;
    trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    trxConfig.readOnly = false;
    uint32_t keyValue = 100;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    GmcStmtT *stmtAsync2[30] = {0};
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int j = 0; j < 10; j++) {
        ret = GmcAllocStmt(connAsync, &stmtAsync2[j]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUseNamespaceAsync(stmtAsync, g_namesapceName, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(data));

    ret = GmcTransStartAsync(connAsync, &trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmtAsync, g_containRootName2, GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *T0Node = NULL;
    GmcNodeT *P1Node = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_NONE, &T0Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(T0Node, "P1", GMC_OPERATION_NONE, &P1Node);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    static vector<string> expectDiffBase012 = {};
    // 获取diffTree并校验
    testFetchAndDeparseDiff(stmtAsync, batch, expectDiffBase012, data);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    // 提交事务
    ret = GmcTransCommitAsync(connAsync, trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (data.status == GMERR_RESTRICT_VIOLATION) {
        memset(&data, 0, sizeof(AsyncUserDataT));
        ret = GmcTransRollBackAsync(connAsync, trans_commit_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }
    // 断开连接
    for (int j = 0; j < 10; j++) {
        GmcFreeStmt(stmtAsync2[j]);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  :013.部分打散多线程并发六原语操作，最后check数据
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 64;
    char errorMsg1[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};
    int threadNum = 5;
    pthread_t wrth[threadNum];
    int a[threadNum] = {1, 2, 3, 4, 5};
    // 建表
    CreateTreeChoiceCaseList(g_stmtAsync, data);
    // 预置数据
    testYangPresetDataList(g_connAsync, g_stmtAsync, batch, data, true);

    int err = pthread_create(&wrth[0], NULL, createTransThread, &a[0]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, err);
    err = pthread_create(&wrth[1], NULL, replaceTransThread, &a[1]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, err);
    err = pthread_create(&wrth[2], NULL, mergeTransThread, &a[2]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, err);
    err = pthread_create(&wrth[3], NULL, removeTransThread, &a[3]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, err);
    err = pthread_create(&wrth[4], NULL, noneTransThread, &a[4]);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, err);
    for (int i = 0; i < threadNum; i++) {
        pthread_join(wrth[i], NULL);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :014.数据插入后再删除，查chainHash相关视图
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    char *vertexSchema = NULL;
    char *edgeSchema = NULL;
    readJanssonFile("schemaFile/choice_case_list2.gmjson", &vertexSchema);
    AW_MACRO_ASSERT_NOTNULL(vertexSchema);
    readJanssonFile("schemaFile/choice_case_list_edge.gmjson", &edgeSchema);
    AW_MACRO_ASSERT_NOTNULL(edgeSchema);

    ret = GmcCreateVertexLabelAsync(g_stmtAsync, vertexSchema, g_yangConfig2, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, edgeSchema, g_yangConfig2, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    free(edgeSchema);
    free(vertexSchema);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, g_containRootName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(g_stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode, "P1", GMC_OPERATION_INSERT, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置子节点T1
    for (int i = 1; i < 5; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[1], "T1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[1], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A1", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置子节点T2
    for (int i = 1; i < 1000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[2], "T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[2], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A2", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, 1004, 1004);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    char command[512] = {0};
    char const *viewname1 = "V\\$STORAGE_HASH_INDEX_STAT";
    char const *viewname2 = "V\\$STORAGE_INDEX_GLOBAL_STAT";
    (void)snprintf(command, 512, "%s/gmsysview -s %s -q %s > output2.txt", g_toolPath, g_connServer, viewname2);
    system(command);
    TestGmsysviewLabelStorageIndexGlobal();
    if (g_pkAvgCollisionRate == 0 || g_localhashAvgCollisionRate == 0) {
        AW_FUN_Log(LOG_ERROR, "pk or localhash index collision rate has error!!\n");
        AW_FUN_Log(LOG_INFO, "g_pkAvgCollisionRate: %d \n", g_pkAvgCollisionRate);
        AW_FUN_Log(LOG_INFO, "g_localhashAvgCollisionRate: %d \n", g_localhashAvgCollisionRate);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
    }

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, g_containRootName2, GMC_OPERATION_DELETE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmtAsync, g_keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, 1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = (char *)"{}";

    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = g_containRootName2,
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(containerContentJson);

    // 查视图
    memset(command, 0, 512);
    (void)snprintf(command, 512, "%s/gmsysview -s %s -q %s > output1.txt", g_toolPath, g_connServer, viewname1);
    system(command);
    // 校验root
    TestGmsysviewLabelStorageHashIndex((char *)"root", (char *)"PK", NULL);
#if defined CPU_BIT_32
    uint32_t actValue = 0x7FFFFFFF;   //arm32: 2147483647
#else
    uint32_t actValue = 0xffffffff;
#endif
    // 主键校验
    for (int i = 0; i < 4; i++) {
        if (g_valuePk[i] != actValue) {
            AW_FUN_Log(LOG_ERROR, "primary index chainHash has error!! g_valuePk[%d]=%u\n", i, g_valuePk[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            break;
        }
    }
    // 校验list
    TestGmsysviewLabelStorageHashIndex((char *)"T2", (char *)"PK", (char *)"UK");
    // 主键校验
    for (int i = 0; i < 4; i++) {
        if (g_valuePk[i] != actValue) {
            AW_FUN_Log(LOG_ERROR, "primary index chainHash has error!! g_valuePk[%d]=%u\n", i, g_valuePk[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            break;
        }
    }
    // listlocalhash校验
    for (int i = 0; i < 4; i++) {
        if (g_valueLocalhash[i] != actValue) {
            AW_FUN_Log(LOG_ERROR, "localhash index chainHash has error!! localhash[%d]=%u\n", i, g_valueLocalhash[i]);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, 1);
            break;
        }
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :015.数据插入后再rollback回滚
 Output       : None
 Return Value : None
 Notes        : None
 History      : None
 Author       : None
 Modification : None
**************************************************************************** */
TEST_F(yangTest1, DDL_049_002_002_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t keyvalue = 100;
    uint32_t PID = 0;
    GmcBatchT *batch = NULL;
    AsyncUserDataT data = {0};

    // 建表
    CreateTreeChoiceCaseList(g_stmtAsync, data);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, g_containRootName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode = NULL;
    GmcNodeT *rootNode1 = NULL;
    ret = GmcGetRootNode(g_stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置属性值
    ret = testYangSetNodeProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    // 设置node节点T0
    ret = GmcYangEditChildNode(rootNode, "T0", GMC_OPERATION_INSERT, &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangEditChildNode(childNode, "P1", GMC_OPERATION_INSERT, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(childNode1, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置子节点T1
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[1], "T1", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[1], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A1", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[1]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置子节点T2
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[2], "T2", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[2], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A2", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[2]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置子节点T3
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[3], "T3", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[3], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A3", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[3]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置子节点T4
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[4], "T4", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[4], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A4", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[4]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 设置子节点T5
    for (int i = 1; i < 6; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[5], "T5", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(g_stmtAsync2[5], &rootNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(rootNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangEditChildNode(rootNode1, "A5", GMC_OPERATION_INSERT, &childNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(childNode1, i, GMC_YANG_PROPERTY_OPERATION_CREATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch, g_stmtAsync2[5]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = testBatchExecuteAndWait(batch, data, 26, 26);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffBaseChoiceCaseList, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransRollBackAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = (char *)"{}";

    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/container_content.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = g_containRootName2,
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(containerContentJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

void testCreateLabel(GmcStmtT *stmt, char *vertexJson, char *edgeJson)
{
    int ret = 0;
    AsyncUserDataT data = {0};

    ret = GmcCreateVertexLabelAsync(stmt, vertexJson, g_yangConfig2, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateEdgeLabelAsync(stmt, edgeJson, g_yangConfig2, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int testYangTreeSetNodeField(GmcNodeT *node, GmcDataTypeE type, void *value, uint32_t size,
    const char *fieldName, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    int ret1 = 0;

    GmcPropValueT propValue;
    memcpy(propValue.propertyName, fieldName, (strlen(fieldName) + 1));
    propValue.type = type;
    propValue.value = value;
    propValue.size = size;
    ret = GmcYangSetNodeProperty(node, &propValue, opType);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_DEBUG, "SetProperty failed fieldName(%s)\n", fieldName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret1 = testGmcGetLastError();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret1);
        return ret;
    }

    return ret;
}
// 只有list类型需要设置主键
void testYangSetNodeProperty_PK(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    char pkValue[8] = "string";
    ret = testYangTreeSetNodeField(node, GMC_DATATYPE_STRING, pkValue, (strlen(pkValue)), "name", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetNodeProperty_PK1(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    char pkValue[8] = "strings";
    ret = testYangTreeSetNodeField(node, GMC_DATATYPE_STRING, pkValue, (strlen(pkValue)), "name", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetNodeProperty1(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    char valuename[8] = "string";
    ret = testYangTreeSetNodeField(node, GMC_DATATYPE_STRING, valuename, (strlen(valuename)), "name", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testYangSetNodePropertyWithoutF0(GmcNodeT *node, uint32_t i, GmcYangPropOpTypeE opType)
{
    int ret = 0;
    uint32_t value = i;

    uint32_t valueF1 = value;
    ret = testYangTreeSetNodeField(node, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangTreeSetNodeField(node, GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangTreeSetNodeField(node, GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = value;
    ret = testYangTreeSetNodeField(node, GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4", opType);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*******************************************************************************
Description  : 016 replace一条记录B，修改null值为非null值；写入一条数据和修改的相冲突，
                再次修改数据B回null后，再次写入刚才的数据，预期最后写入成功
Author		 : 
 *******************************************************************************/
TEST_F(yangTest1, DDL_049_002_002_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;
    char *vLabelSchema = NULL;
    char *edgeSchema = NULL;
    readJanssonFile("schemaFile/yang_tree_vertex_full.gmjson", &vLabelSchema);
    ASSERT_NE((void *)NULL, vLabelSchema);

    readJanssonFile("schemaFile/yang_tree_edge_full.gmjson", &edgeSchema);
    ASSERT_NE((void *)NULL, edgeSchema);
    testCreateLabel(g_stmtAsync, vLabelSchema, edgeSchema);
    free(vLabelSchema);
    free(edgeSchema);
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数和开启diff
    ret = testBatchPrepareAndSetDiff(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcNodeT *rootNode = NULL;
    GmcNodeT *childNode = NULL;
    GmcNodeT *childNode1 = NULL;
    GmcNodeT *childNode2 = NULL;
    GmcNodeT *childNode3 = NULL;

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync, "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync, &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty1(rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点1
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync2[1], &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(childNode, "c0", GMC_OPERATION_MERGE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF2[8] = "string";
    ret = testYangTreeSetNodeField(childNode1, GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char valueF3[8] = "string";
    ret = testYangTreeSetNodeField(childNode1, GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF4 = 20;
    ret = testYangTreeSetNodeField(childNode1, GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(childNode1, "c0_0", GMC_OPERATION_MERGE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(childNode2, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(childNode2, "c0_0_0", GMC_OPERATION_MERGE, &childNode3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(childNode3, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(childNode1, "c0_1", GMC_OPERATION_MERGE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(childNode2, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(childNode2, "c0_1_0", GMC_OPERATION_MERGE, &childNode3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 6;
    testYangSetNodePropertyWithoutF0(childNode3, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtAsync2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点2
    ret = testGmcPrepareStmtByLabelName(g_stmtAsync2[1], "root::L0", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmtAsync, g_stmtAsync2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmtAsync2[1], &childNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 2;
    testYangSetNodeProperty_PK(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    testYangSetNodePropertyWithoutF0(childNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    // 编辑子节点
    ret = GmcYangEditChildNode(childNode, "c0", GMC_OPERATION_MERGE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t valueF1 = 100;
    ret = testYangTreeSetNodeField(childNode1, GMC_DATATYPE_UINT32, &valueF1, sizeof(uint32_t), "F1",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangTreeSetNodeField(childNode1, GMC_DATATYPE_STRING, valueF2, (strlen(valueF2)), "F2",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testYangTreeSetNodeField(childNode1, GMC_DATATYPE_STRING, valueF3, (strlen(valueF3)), "F3",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    valueF4 = 200;
    ret = testYangTreeSetNodeField(childNode1, GMC_DATATYPE_UINT32, &valueF4, sizeof(uint32_t), "F4",
        GMC_YANG_PROPERTY_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(childNode1, "c0_0", GMC_OPERATION_MERGE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 3;
    testYangSetNodePropertyWithoutF0(childNode2, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(childNode2, "c0_0_0", GMC_OPERATION_MERGE, &childNode3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 4;
    testYangSetNodePropertyWithoutF0(childNode3, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    ret = GmcYangEditChildNode(childNode1, "c0_1", GMC_OPERATION_MERGE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 5;
    testYangSetNodePropertyWithoutF0(childNode2, fieldValue, GMC_YANG_PROPERTY_OPERATION_REPLACE);

    ret = GmcYangEditChildNode(childNode2, "c0_1_0", GMC_OPERATION_MERGE, &childNode3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    fieldValue = 50;
    testYangSetNodePropertyWithoutF0(childNode3, fieldValue, GMC_YANG_PROPERTY_OPERATION_MERGE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmtAsync2[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    // 获取diffTree并校验
    testFetchAndDeparseDiff(g_stmtAsync, batch, expectDiffMix016, data);

    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 启动事务
    ret = testTransStartAsync(g_connAsync, g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // subtree 查询
    char *subtreeReturnJson = NULL;
    readJanssonFile("schemaFile/Yang_021_Reply.json", &subtreeReturnJson);
    ASSERT_NE((void *)NULL, subtreeReturnJson);
    // 进行subtree查询
    char *containerContentJson = NULL;
    readJanssonFile("schemaFile/Yang_018_Func.json", &containerContentJson);
    ASSERT_NE((void *)NULL, containerContentJson);
    SubtreeFilterCbParam Data = {0};
    Data.expectReplyJson = subtreeReturnJson; // 预期要的subtree返回结果
    Data.expectStatus = GMERR_OK; // 预期服务端处理结果
    Data.step = 0; //回调执行次数
    GmcSubtreeFilterItemT filter = {
        filter.rootName = "root",
        filter.subtree.json = containerContentJson,
        filter.jsonFlag =  GMC_JSON_INDENT(4),
    };
    GmcSubtreeFilterT filters = {
        filters.filterMode = GMC_FETCH_JSON,
        filters.filter = &filter,
    };
    ret =GmcYangSubtreeFilterExecuteAsync(g_stmtAsync, &filters, NULL, AsyncSubtreeFilterCb, &Data);
    ret = testWaitAsyncSubtreeRecv(&Data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, Data.expectStatus);

    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(subtreeReturnJson);
    free(containerContentJson);
}
