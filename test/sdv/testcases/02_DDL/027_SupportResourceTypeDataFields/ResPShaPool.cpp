/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: 申请及释放资源索引测试
 * Author: guopanpan
 * Create: 2021-04-06
 * History:
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <errno.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

using namespace std;

#define TEST_ERROR(log, args...)                                               \
    do {                                                                       \
        fprintf(stdout, "Error: %s:%d " log "\n", __FILE__, __LINE__, ##args); \
    } while (0)

#define CHECK_AND_BREAK(ret, log, args...)                                                             \
    if ((ret) != GMERR_OK) {                                                                           \
        fprintf(stdout, "Error: %s:%d " log " failed, ret = %d\n", __FILE__, __LINE__, ##args, (ret)); \
        break;                                                                                         \
    }

bool isBatch = false;
// 通用全局变量
GmcConnT *gConnection = NULL;
GmcStmtT *gStmt = NULL;
static const char *gLabelName = "ResourceLable";
static const char *gLabelConfig = R"({"max_record_num":1000})";
static const char *gLabelSchemaJson =
    R"([{
        "type":"record",
        "name":"ResourceLable",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"resource", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"ResourceLable",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

static const char *gSecondLabelName = "SecondResourceLable";
static const char *gSecondLabelConfig = R"({"max_record_num":1000})";
static const char *gSecondLabelSchemaJson =
    R"([{
        "type":"record",
        "name":"SecondResourceLable",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"resource", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"SecondResourceLable",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

// 资源池全局变量
static const char *gResPoolName = "ResourcePool";
static const char *gExtendResPoolName = "ExtendResourcePool";
static const uint64_t gExtendResPoolId = 10001;
const char *expect = NULL;
class ResourcePoolApplyAndRelease : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh -f");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcConnect(&gConnection, &gStmt);
        EXPECT_EQ(GMERR_OK, ret);

        char errorMsg[128] = {};
        (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_OBJECT);
        AW_ADD_ERR_WHITE_LIST(1, errorMsg);


        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfig);
        if (ret == GMERR_DUPLICATE_OBJECT) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = (ret == GMERR_DUPLICATE_OBJECT ? GMERR_OK : ret);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateVertexLabel(gStmt, gSecondLabelSchemaJson, gSecondLabelConfig);
        if (ret == GMERR_DUPLICATE_OBJECT) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = (ret == GMERR_DUPLICATE_OBJECT ? GMERR_OK : ret);
        EXPECT_EQ(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = GmcDropVertexLabel(gStmt, gLabelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcDropVertexLabel(gStmt, gSecondLabelName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testGmcDisconnect(gConnection, gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

    virtual void SetUp()
    {
    GmcDestroyResPool(gStmt, gResPoolName);
    GmcDestroyResPool(gStmt, gExtendResPoolName);
    AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
    }
};

// 多表共享资源池场景, 创建order属性为cycle的资源池, 且不绑定拓展资源池, 每次申请1个资源索引测试
TEST_F(ResourcePoolApplyAndRelease, DDL_027_038)
{   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int64_t capacity = 200;
    const uint16_t resPoolId = 10038;
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10038,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gSecondLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gSecondLabelName);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 150;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        // 资源索引耗尽后写入数据失败
        ret = GmcExecute(gStmt);
        if (i >= capacity && ret != GMERR_RESOURCE_POOL_ERROR) {
            TEST_ERROR("expect insert failed when dataNum = %d, actual ret = %d", i, ret);
            ret = FAILED;
            break;
        }
        if (ret == GMERR_RESOURCE_POOL_ERROR) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
        }

        if (i >= capacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        // uint64_t* resIdInfo;
        // uint32_t resIdInfoLen;
        // ret = GmcGetResIdInfo(gStmt, &resIdInfo, &resIdInfoLen);

        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);

        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if (i < capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (i < capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 超过资源池总容量的数据写入失败，预期读取失败报错
        ret = GmcSetIndexKeyName(gStmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (i >= capacity && !isFinish) {
            ret = FAILED;
            TEST_ERROR("expect alloc and get vertex by index failed when index = %d, actual ret = %d", i, ret);
            break;
        }
        if (i >= capacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "alloc and get vertex");

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if (i < capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (i < capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除第100~120条数据
    for (int32_t i = 100; i < 120; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 申请10个资源索引
    for (int32_t i = dataNum; ret == GMERR_OK && i < dataNum + 10; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
        // uint64_t* resIdInfo;
        // uint32_t resIdInfoLen;
        // ret = GmcGetResIdInfo(gStmt, &resIdInfo, &resIdInfoLen);

        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i * resCount, tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除所有数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum + 10; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gSecondLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多表共享资源池场景, 创建order属性为sequence的资源池, 且不绑定拓展资源池, 每次申请1个资源索引测试
TEST_F(ResourcePoolApplyAndRelease, DDL_027_039)
{   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int64_t capacity = 200;
    const uint16_t resPoolId = 10039;
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10039,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gSecondLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gSecondLabelName);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 150;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        // 资源索引耗尽后写入数据失败
        ret = GmcExecute(gStmt);
        if (i >= capacity && ret != GMERR_RESOURCE_POOL_ERROR) {
            TEST_ERROR("expect insert failed when dataNum = %d, actual ret = %d", i, ret);
            ret = FAILED;
            break;
        }
        if (ret == GMERR_RESOURCE_POOL_ERROR) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
        }

        if (i >= capacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        // uint64_t* resIdInfo;
        // uint32_t resIdInfoLen;
        // ret = GmcGetResIdInfo(gStmt, &resIdInfo, &resIdInfoLen);

        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if (i < capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (i < capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 超过资源池总容量的数据写入失败，预期读取失败报错
        ret = GmcSetIndexKeyName(gStmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (i >= capacity && !isFinish) {
            ret = FAILED;
            TEST_ERROR("expect alloc and get vertex by index failed when index = %d, actual ret = %d", i, ret);
            break;
        }
        if (i >= capacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "alloc and get vertex");

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if (i < capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (i < capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除第100~120条数据
    for (int32_t i = 100; i < 120; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 申请10个资源索引
    for (int32_t i = dataNum; ret == GMERR_OK && i < dataNum + 10; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
        // uint64_t* resIdInfo;
        // uint32_t resIdInfoLen;
        // ret = GmcGetResIdInfo(gStmt, &resIdInfo, &resIdInfoLen);

        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);

        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        // sequency模式优先分配较小的资源索引
        if (tmpStartIndex != (i - 50) * resCount) {
            TEST_ERROR(
                "checek res start index failed, expect is %lu, actual is %lu", (i - 50) * resCount, tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除所有数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum + 10; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gSecondLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多表共享资源池场景, 创建order属性为cycle的资源池, 且绑定拓展资源池, 每次申请1个资源索引测试
TEST_F(ResourcePoolApplyAndRelease, DDL_027_040)
{   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int64_t capacity = 200;
    const uint16_t resPoolId = 10040;
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10040,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    int64_t extendCapacity = 200;
    const char *extendResPoolConfigJson =
        R"({
        "name" : "ExtendResourcePool",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, extendResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, gExtendResPoolName);
        CHECK_AND_BREAK(ret, "bind extend res pool");

        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gSecondLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gSecondLabelName);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 300;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        // 资源索引耗尽后写入数据失败
        ret = GmcExecute(gStmt);
        if (i >= capacity + extendCapacity && ret != GMERR_RESOURCE_POOL_ERROR) {
            TEST_ERROR("expect insert failed when dataNum = %d, actual ret = %d", i, ret);
            ret = FAILED;
            break;
        }
        if (ret == GMERR_RESOURCE_POOL_ERROR) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
        }

        if (i >= capacity + extendCapacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        // uint64_t* resIdInfo;
        // uint32_t resIdInfoLen;
        // ret = GmcGetResIdInfo(gStmt, &resIdInfo, &resIdInfoLen);

        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if (i < capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (i >= capacity && tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gExtendResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (i < capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        if (i >= capacity && tmpStartIndex != ((i - capacity) * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", (i - capacity) * resCount,
                tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 超过资源池总容量的数据写入失败，预期读取失败报错
        ret = GmcSetIndexKeyName(gStmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (i >= capacity + extendCapacity && !isFinish) {
            ret = FAILED;
            TEST_ERROR("expect alloc and get vertex by index failed when index = %d, actual ret = %d", i, ret);
            break;
        }
        if (i >= capacity + extendCapacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "alloc and get vertex");

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if (i < capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (i >= capacity && tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gExtendResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (i < capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        if (i >= capacity && tmpStartIndex != ((i - capacity) * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", (i - capacity) * resCount,
                tmpStartIndex);
            ret = FAILED;
            break;
        }

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除第210~230条数据
    for (int32_t i = 210; i < 230; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 申请10个资源索引
    for (int32_t i = dataNum; ret == GMERR_OK && i < dataNum + 10; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        // 资源索引耗尽后写入数据失败
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
        // uint64_t* resIdInfo;
        // uint32_t resIdInfoLen;
        // ret = GmcGetResIdInfo(gStmt, &resIdInfo, &resIdInfoLen);

        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != ((i - capacity) * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", (i - capacity) * resCount,
                tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除所有数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum + 10; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gSecondLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gExtendResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多表共享资源池场景, 创建order属性为sequence的资源池, 且绑定拓展资源池, 每次申请1个资源索引测试
TEST_F(ResourcePoolApplyAndRelease, DDL_027_041)
{   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int64_t capacity = 200;
    const uint16_t resPoolId = 10041;
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10041,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";

    int64_t extendCapacity = 200;
    const char *extendResPoolConfigJson =
        R"({
        "name" : "ExtendResourcePool",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, extendResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, gExtendResPoolName);
        CHECK_AND_BREAK(ret, "bind extend res pool");

        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gSecondLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gSecondLabelName);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 300;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        // 资源索引耗尽后写入数据失败
        ret = GmcExecute(gStmt);
        if (i >= capacity + extendCapacity && ret != GMERR_RESOURCE_POOL_ERROR) {
            TEST_ERROR("expect insert failed when dataNum = %d, actual ret = %d", i, ret);
            ret = FAILED;
            break;
        }
        if (ret == GMERR_RESOURCE_POOL_ERROR) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
        }

        if (i >= capacity + extendCapacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        // uint64_t* resIdInfo;
        // uint32_t resIdInfoLen;
        // ret = GmcGetResIdInfo(gStmt, &resIdInfo, &resIdInfoLen);
        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);

        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if (i < capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (i >= capacity && tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gExtendResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (i < capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        if (i >= capacity && tmpStartIndex != ((i - capacity) * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", (i - capacity) * resCount,
                tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 超过资源池总容量的数据写入失败，预期读取失败报错
        ret = GmcSetIndexKeyName(gStmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (i >= capacity + extendCapacity && !isFinish) {
            ret = FAILED;
            TEST_ERROR("expect alloc and get vertex by index failed when index = %d, actual ret = %d", i, ret);
            break;
        }
        if (i >= capacity + extendCapacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "alloc and get vertex");

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if (i < capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (i >= capacity && tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gExtendResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (i < capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        if (i >= capacity && tmpStartIndex != ((i - capacity) * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", (i - capacity) * resCount,
                tmpStartIndex);
            ret = FAILED;
            break;
        }

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除第210~230条数据
    for (int32_t i = 210; i < 230; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 申请10个资源索引， 预期从扩展资源池第10个开始分配
    for (int32_t i = dataNum; ret == GMERR_OK && i < dataNum + 10; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
        // uint64_t* resIdInfo;
        // uint32_t resIdInfoLen;
        // ret = GmcGetResIdInfo(gStmt, &resIdInfo, &resIdInfoLen);

        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != ((i - dataNum + 10) * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", (i - dataNum + 10) * resCount,
                tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除所有数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum + 10; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gSecondLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gExtendResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多表共享资源池场景, 创建order属性为cycle的资源池, 且不绑定拓展资源池, 每次申请3个资源索引测试
TEST_F(ResourcePoolApplyAndRelease, DDL_027_042)
{   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int64_t capacity = 200;
    const uint16_t resPoolId = 10042;
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10042,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gSecondLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gSecondLabelName);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 50;
    uint64_t resCount = 3;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        // 资源索引耗尽后写入数据失败
        ret = GmcExecute(gStmt);
        if (i >= capacity && ret != GMERR_RESOURCE_POOL_ERROR) {
            TEST_ERROR("expect insert failed when dataNum = %d, actual ret = %d", i, ret);
            ret = FAILED;
            break;
        }
        if (ret == GMERR_RESOURCE_POOL_ERROR) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
        }

        if (i >= capacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        // uint64_t* resIdInfo;
        // uint32_t resIdInfoLen;
        // ret = GmcGetResIdInfo(gStmt, &resIdInfo, &resIdInfoLen);
        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);

        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 超过资源池总容量的数据写入失败，预期读取失败报错
        ret = GmcSetIndexKeyName(gStmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (i >= capacity && !isFinish) {
            ret = FAILED;
            TEST_ERROR("expect alloc and get vertex by index failed when index = %d, actual ret = %d", i, ret);
            break;
        }
        if (i >= capacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "alloc and get vertex");

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        if (tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除第20~40条数据
    for (int32_t i = 100; i < 120; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 申请10个资源索引
    for (int32_t i = dataNum; ret == GMERR_OK && i < dataNum + 10; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
        // uint64_t* resIdInfo;
        // uint32_t resIdInfoLen;
        // ret = GmcGetResIdInfo(gStmt, &resIdInfo, &resIdInfoLen);
        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i * resCount, tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除所有数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum + 10; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gSecondLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多表共享资源池场景, 创建order属性为sequence的资源池, 且不绑定拓展资源池, 每次申请3个资源索引测试
TEST_F(ResourcePoolApplyAndRelease, DDL_027_043)
{   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int64_t capacity = 200;
    const uint16_t resPoolId = 10043;
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10043,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gSecondLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gSecondLabelName);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 50;
    uint64_t resCount = 3;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        // 资源索引耗尽后写入数据失败
        ret = GmcExecute(gStmt);
        if (i >= capacity && ret != GMERR_RESOURCE_POOL_ERROR) {
            TEST_ERROR("expect insert failed when dataNum = %d, actual ret = %d", i, ret);
            ret = FAILED;
            break;
        }
        if (ret == GMERR_RESOURCE_POOL_ERROR) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
        }

        if (i >= capacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if (i < capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (i < capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 超过资源池总容量的数据写入失败，预期读取失败报错
        ret = GmcSetIndexKeyName(gStmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (i >= capacity && !isFinish) {
            ret = FAILED;
            TEST_ERROR("expect alloc and get vertex by index failed when index = %d, actual ret = %d", i, ret);
            break;
        }
        if (i >= capacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "alloc and get vertex");

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        if (tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除第20~40条数据
    for (int32_t i = 20; i < 40; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 申请10个资源索引
    for (int32_t i = dataNum; ret == GMERR_OK && i < dataNum + 10; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        // sequency模式优先分配较小的资源索引
        if (tmpStartIndex != (i - dataNum + 20) * resCount) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", (i - dataNum + 20) * resCount,
                tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除所有数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum + 10; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gSecondLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多表共享资源池场景, 创建order属性为cycle的资源池, 且绑定拓展资源池, 每次申请3个资源索引测试
TEST_F(ResourcePoolApplyAndRelease, DDL_027_044)
{   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int64_t capacity = 200;
    const uint16_t resPoolId = 10044;
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10044,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    int64_t extendCapacity = 200;
    const char *extendResPoolConfigJson =
        R"({
        "name" : "ExtendResourcePool",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, extendResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, gExtendResPoolName);
        CHECK_AND_BREAK(ret, "bind extend res pool");

        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gSecondLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gSecondLabelName);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 100;
    uint64_t resCount = 3;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        // 资源索引耗尽后写入数据失败
        ret = GmcExecute(gStmt);
        if (i >= capacity + extendCapacity && ret != GMERR_RESOURCE_POOL_ERROR) {
            TEST_ERROR("expect insert failed when dataNum = %d, actual ret = %d", i, ret);
            ret = FAILED;
            break;
        }
        if (ret == GMERR_RESOURCE_POOL_ERROR) {
            expect = NULL;
            ret = testGmcGetLastError(expect);
            EXPECT_EQ(GMERR_OK, ret);
        }
        if (i >= capacity + extendCapacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "insert vertex");
        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if ((i + 1) * resCount <= capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if ((i + 1) * resCount > capacity && tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gExtendResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if ((i + 1) * resCount <= capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        if ((i + 1) * resCount > capacity && tmpStartIndex != (i - (capacity / resCount)) * resCount) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu",
                (i - (capacity / resCount)) * resCount, tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 超过资源池总容量的数据写入失败，预期读取失败报错
        ret = GmcSetIndexKeyName(gStmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (i >= capacity + extendCapacity && !isFinish) {
            TEST_ERROR("expect alloc and get vertex by index failed when index = %d, actual ret = %d", i, ret);
            ret = FAILED;
            break;
        }
        if (i >= capacity + extendCapacity) {
            ret = GMERR_OK;
            continue;
        }
        CHECK_AND_BREAK(ret, "alloc and get vertex");

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if ((i + 1) * resCount <= capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if ((i + 1) * resCount > capacity && tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gExtendResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if ((i + 1) * resCount <= capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        if ((i + 1) * resCount > capacity && tmpStartIndex != (i - (capacity / resCount)) * resCount) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu",
                (i - (capacity / resCount)) * resCount, tmpStartIndex);
            ret = FAILED;
            break;
        }

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除第70~90条数据
    for (int32_t i = 70; i < 90; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 申请10个资源索引
    for (int32_t i = dataNum; ret == GMERR_OK && i < dataNum + 10; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i - (capacity / resCount)) * resCount) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu",
                (i - (capacity / resCount)) * resCount, tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除所有数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum + 10; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gSecondLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gExtendResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
}

// 多表共享资源池场景, 创建order属性为sequence的资源池, 且绑定拓展资源池, 每次申请3个资源索引测试
TEST_F(ResourcePoolApplyAndRelease, DDL_027_045)
{
    int64_t capacity = 200;
    const uint16_t resPoolId = 10045;
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10045,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";

    int64_t extendCapacity = 200;
    const char *extendResPoolConfigJson =
        R"({
        "name" : "ExtendResourcePool",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, extendResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, gExtendResPoolName);
        CHECK_AND_BREAK(ret, "bind extend res pool");

        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gSecondLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gSecondLabelName);
    } while (0);
    EXPECT_EQ(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 100;
    uint64_t resCount = 3;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        // 资源索引耗尽后写入数据失败
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);

        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if ((i + 1) * resCount <= capacity && tmpPoolId != resPoolId) {
            TEST_ERROR(
                "checek res pool id failed when index = %d, expect is %lu, actual is %lu", i, resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if ((i + 1) * resCount > capacity && tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gExtendResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if ((i + 1) * resCount <= capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        // 注意默认资源池会剩2个空闲资源索引
        if ((i + 1) * resCount > capacity && tmpStartIndex != ((i - (capacity / resCount)) * resCount)) {
            TEST_ERROR("checek res start index failed when index = %d, expect is %lu, actual is %lu", i,
                ((i - (capacity / resCount)) * resCount), tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_SCAN);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 超过资源池总容量的数据写入失败，预期读取失败报错
        ret = GmcSetIndexKeyName(gStmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "alloc and get vertex");
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        ASSERT_EQ(GMERR_OK, ret);
        ASSERT_EQ(isFinish, false);

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        // 默认资源池内资源索引耗尽后向拓展资源池申请资源索引
        if ((i + 1) * resCount <= capacity && tmpPoolId != resPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if ((i + 1) * resCount > capacity && tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gExtendResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if ((i + 1) * resCount <= capacity && tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
        if ((i + 1) * resCount > capacity && tmpStartIndex != ((i - (capacity / resCount)) * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu",
                ((i - (capacity / resCount)) * resCount), tmpStartIndex);
            ret = FAILED;
            break;
        }

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除第70 - 90条数据
    for (int32_t i = 70; i < 90; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 申请10个资源索引， 预期从扩展资源池第10个开始分配
    for (int32_t i = dataNum; ret == GMERR_OK && i < dataNum + 10; i++) {
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_INSERT);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
        }

        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");

        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        EXPECT_EQ(ret, GMERR_OK);
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != gExtendResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", resPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != ((70 - (capacity / 3)) + (i - dataNum)) * resCount) {
            TEST_ERROR("checek res start index failed when index = %i, expect is %lu, actual is %lu", i,
                ((70 - (capacity / 3)) + (i - dataNum)) * resCount, tmpStartIndex);
            ret = FAILED;
            break;
        }
        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    // 删除所有数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum + 10; i++) {
        char condStr[128] = {0};
        if (i % 2 == 0) {
            ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        } else {
            ret = testGmcPrepareStmtByLabelName(gStmt, gSecondLabelName, GMC_OPERATION_DELETE);
            CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gSecondLabelName);
            ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gSecondLabelName, i);
        }
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");

        EXPECT_EQ(GMERR_OK, ret);
    }
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gSecondLabelName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gExtendResPoolName);
    EXPECT_EQ(GMERR_OK, ret);
}
