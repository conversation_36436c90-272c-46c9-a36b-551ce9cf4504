/*
 *  Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * @Author: t<PERSON><PERSON><PERSON>
 * @Date: 2024-01-10 17:07:57
 * @FilePath: \GMDBV5\test\sdv\testcases_rd\001_fes\020_PATH_DDL_Support_Logical_View\logical_view.h
 * @Description:path_ddl_support_logic_view
 * @LastEditors: tianyi<PERSON>
 * @LastEditTime: 2024-03-06 15:12:47
 */

#ifndef LOGICAL_VIEW_H
#define LOGICAL_VIEW_H
#include "gtest/gtest.h"
#include "adpt_define.h"
#include "gmc_gql.h"
#include "t_rd_sn.h"
#include "clt_stmt.h"
#include "gmc_types.h"


#pragma pack(1)
typedef struct {
    uint32_t property1;
    uint16_t property2;
} FesTestTableWith2PropT;
#pragma pack()

#pragma pack(1)
typedef struct {
    uint32_t property1;
    uint32_t property2;
    uint16_t property3;
} FesTestTableWith3PropT;
#pragma pack()

#pragma pack(1)
typedef struct {
    uint32_t property1;
    uint32_t property2;
    uint32_t property3;
    uint16_t property4;
} FesTestTableWith4PropT;
#pragma pack()

typedef struct {
    uint16_t encapType;
    uint16_t vertexLen;
    uint8_t *vertex;  // len is vertexLen
    uint16_t sonIndex;
    uint16_t siblingIndex;
} VertexTlvT;

typedef struct {
    uint32_t bufferLen;  // path instance报文总长度
    uint16_t pathType;
    uint16_t pathLen;  // buffer len - 4
    uint32_t pathIdInApp;
    uint32_t vertexNum;  // 实体点个数
    VertexTlvT *vertexs;
} PathTlvT;

typedef struct {
    uint32_t pathNum;
    PathTlvT *paths;
} TlvT;

static DbMemCtxT *g_basicMem = NULL;
TlvT g_tempTlv = {0};
const uint32_t WAIT_TIME = 1000;  // ms
bool isReplaceTlv = true;
bool hasTrigSource = false;
#define BEGIN_TLV 204
#define END_TLV 205

// 小端序转Uint32
void Bytes2Uint32(uint8_t *start, uint32_t *number)
{
    uint8_t step = 8;
    *number = start[0];
    for (uint32_t i = 1; i < 4; i++) {
        *number |= start[i] << (i * step);
    }
}

// 小端序转Uint16
void Bytes2Uint16(uint8_t *start, uint16_t *number)
{
    uint8_t step = 8;
    *number = start[0];
    for (uint32_t i = 1; i < 2; i++) {
        *number |= start[i] << (i * step);
    }
}

// 小端序转Uint8
void Bytes2Uint8(uint8_t *start, uint8_t *number)
{
    *number = start[0];
}

// 字节对齐，遵守FixBuf规则
static uint32_t roundUp(uint32_t num)
{
    uint32_t align = 4;
    return ((num + sizeof(uint32_t) + align - 1) / align) * align;
}


static Status DeSerializeVertexTlv(uint32_t offset, uint8_t *data, PathTlvT *path, DbMemCtxT *memCtx)
{
    // 使用后用户自觉释放;最后delete
    path->vertexs = (VertexTlvT *)DbDynMemCtxAlloc(memCtx, sizeof(VertexTlvT) * path->vertexNum);
    if (path->vertexs == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc path->vertexs.");
        return GMERR_OUT_OF_MEMORY;
    }
    VertexTlvT *vertexs = path->vertexs;
    for (uint32_t i = 0; i < path->vertexNum; i++) {
        Bytes2Uint16(data + offset, &vertexs[i].encapType);
        offset += sizeof(uint16_t);
        Bytes2Uint16(data + offset, &vertexs[i].vertexLen);
        offset += sizeof(uint16_t);
        // 使用后用户自觉释放;最后delete
        vertexs[i].vertex = (uint8_t *)DbDynMemCtxAlloc(memCtx, sizeof(uint8_t) * vertexs[i].vertexLen);
        if (vertexs[i].vertex == NULL) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc vertex.");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memcpy_s(vertexs[i].vertex, vertexs[i].vertexLen, data + offset, vertexs[i].vertexLen);

        uint32_t temp = offset;
        for (uint32_t j = 0; j < vertexs[i].vertexLen/sizeof(uint32_t); j++) {
            uint32_t subField = 0;
            Bytes2Uint32(data + temp, &subField);
            temp += sizeof(uint32_t);
        }
        offset += vertexs[i].vertexLen;
        Bytes2Uint16(data + offset, &vertexs[i].sonIndex);
        offset += sizeof(uint16_t);
        Bytes2Uint16(data + offset, &vertexs[i].siblingIndex);
        offset += sizeof(uint16_t);
    }
    return GMERR_OK;
}

void DeSerializePathTlv(uint8_t *data, TlvT *tlv, DbMemCtxT *memCtx)
{
    uint32_t offset = 0;
    Bytes2Uint32(data + offset, &tlv->pathNum);
    offset += sizeof(uint32_t);
    // 使用后用户自觉释放;最后delete
    tlv->paths = (PathTlvT *)DbDynMemCtxAlloc(memCtx, sizeof(PathTlvT) * tlv->pathNum);
    if (tlv->paths == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Unable to alloc tlv->paths.");
        return;
    }
    PathTlvT *paths = tlv->paths;
    for (uint32_t i = 0; i < tlv->pathNum; i++) {
        Bytes2Uint32(data + offset, &paths[i].bufferLen);
        offset += sizeof(uint32_t);
        Bytes2Uint16(data + offset, &paths[i].pathType);
        offset += sizeof(uint16_t);
        Bytes2Uint16(data + offset, &paths[i].pathLen);
        offset += sizeof(uint16_t);
        Bytes2Uint32(data + offset, &paths[i].pathIdInApp);
        offset += sizeof(uint32_t);
        Bytes2Uint32(data + offset, &paths[i].vertexNum);
        offset += sizeof(uint32_t);
        Status ret = DeSerializeVertexTlv(offset, data, &paths[i], memCtx);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Unable to DeSerializeVertexTlv.");
            return;
        }
        offset += paths[i].pathLen - sizeof(uint32_t) - sizeof(uint32_t);
        offset += roundUp(paths[i].bufferLen) - sizeof(uint32_t) - paths[i].bufferLen;  // 越过fixbuffer填充的补齐字节
    }
}

void checkPathInstance(uint32_t pathNum, uint32_t vertexNum)
{
    AW_MACRO_ASSERT_EQ_INT(pathNum, g_tempTlv.pathNum);
    PathTlvT path = g_tempTlv.paths[0];
    AW_MACRO_ASSERT_EQ_INT(vertexNum, path.vertexNum);
}

void PathUserCb2(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    TlvT tlv = {0};
    DeSerializePathTlv(data, &tlv, g_basicMem);
    g_tempTlv = tlv;
    *(uint32_t *)userData += tlv.pathNum;
    return;
}

// 通过数组的形式向表中插入数据
void BatchReplaceVertexForPathWith3Properties(GmcConnT *conn, GmcStmtT *stmt, const char *vertexLabelName,
                                              uint32_t replaceNum, uint32_t startIdx, uint32_t *c1PropArr,
                                              uint32_t *c2PropArr, uint32_t *c3PropArr)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;  // set model type to router into path service
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_REPLACE));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    FesTestTableWith3PropT tmpVertex = {0};
    for (uint32_t i = startIdx; i < startIdx + replaceNum; i++) {
        tmpVertex.property1 = c1PropArr[i];  // primary key
        tmpVertex.property2 = c2PropArr[i];
        tmpVertex.property3 = c3PropArr[i];
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetVertexPropertyAll(stmt, &tmpVertex, sizeof(tmpVertex)));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    GmcBatchRetT batchRet = {};
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchDestroy(batch));
    // check the result of Batch Replace
    // 1.check total num
    // 2.check record value
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecute(stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(replaceNum + startIdx, cnt);
    printf("\nInsert %u data into %s successfully !\n\n", cnt, vertexLabelName);
}

void BatchReplaceVertexForPathWith2Properties(GmcConnT *conn, GmcStmtT *stmt, const char *vertexLabelName,
                                              uint32_t replaceNum, uint32_t startIdx, uint32_t *c1PropArr,
                                              uint32_t *c2PropArr)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;  // set model type to router into path service
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_REPLACE));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    FesTestTableWith2PropT tmpVertex = {0};
    for (uint32_t i = startIdx; i < startIdx + replaceNum; i++) {
        tmpVertex.property1 = c1PropArr[i];  // primary key
        tmpVertex.property2 = c2PropArr[i];
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetVertexPropertyAll(stmt, &tmpVertex, sizeof(tmpVertex)));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    GmcBatchRetT batchRet = {};
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchDestroy(batch));
    // check the result of Batch Replace
    // 1.check total num
    // 2.check record value
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecute(stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(replaceNum + startIdx, cnt);
    printf("\nInsert %u data into %s successfully !\n\n", cnt, vertexLabelName);
}

void CheckMergeTableDataWith4Properties(GmcStmtT *stmt, uint32_t datanum, const char *vertexLabelName,
                                        uint32_t *c1PropArr, uint32_t *c2PropArr, uint32_t *c3PropArr)
{
    // check data
    bool isFinish = true;
    uint cnt = 0;
    while (true) {
        uint32_t pk = cnt + 1;
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(pk)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt));
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        uint32_t ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t property1 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c1PropArr[cnt], property1);

        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t property2 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c2PropArr[cnt], property2);

        ret = GmcGetVertexPropertySizeByName(stmt, "property3", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint16_t property3 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property3", &property3, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c2PropArr[cnt], property3);

        ret = GmcGetVertexPropertySizeByName(stmt, "property4", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint16_t property4 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property4", &property4, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c3PropArr[cnt], property4);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(datanum, cnt);
}

void CheckMergeTableDataWith3Properties(GmcStmtT *stmt, uint32_t datanum, const char *vertexLabelName,
                                        uint32_t *c1PropArr, uint32_t *c2PropArr, uint32_t *c3PropArr)
{
    // check data
    bool isFinish = true;
    uint cnt = 0;
    while (true) {
        uint32_t pk = cnt + 1;
        ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        ASSERT_EQ(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(pk)));
        ASSERT_EQ(GMERR_OK, GmcExecute(stmt));
        ASSERT_EQ(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        uint32_t ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t property1 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c1PropArr[cnt], property1);

        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t property2 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c2PropArr[cnt], property2);

        ret = GmcGetVertexPropertySizeByName(stmt, "property3", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t property3 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property3", &property3, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c3PropArr[cnt], property3);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(datanum, cnt);
}

void CheckReplaceTableDataWith3Properties(GmcConnT *conn, GmcStmtT *stmt, uint32_t datanum,
                                          const char *vertexLabelName, uint32_t *c1PropArr,
                                          uint32_t *c2PropArr, uint32_t *c3PropArr)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    GmcBatchRetT batchRet = {};
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // check data
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecute(stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        uint32_t ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t property1 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c1PropArr[cnt], property1);

        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t property2 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c2PropArr[cnt], property2);

        ret = GmcGetVertexPropertySizeByName(stmt, "property3", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t property3 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property3", &property3, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c3PropArr[cnt], property3);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(datanum, cnt);
}

void CheckReplaceTableDataWith2Properties(GmcConnT *conn, GmcStmtT *stmt, uint32_t datanum,
                                          const char *vertexLabelName, uint32_t *c1PropArr,
                                          uint32_t *c2PropArr)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    GmcBatchRetT batchRet = {};
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // check data
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecute(stmt));
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        uint32_t ret = GmcGetVertexPropertySizeByName(stmt, "property1", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t property1 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property1", &property1, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c1PropArr[cnt], property1);

        ret = GmcGetVertexPropertySizeByName(stmt, "property2", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t property2 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "property2", &property2, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c2PropArr[cnt], property2);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(datanum, cnt);
}

// 检查数据
void checkData(GmcConnT *conn, GmcStmtT *stmt, const char *vertexLabelName, uint32_t expectNum)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    GmcBatchRetT batchRet = {};
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // check data
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecute(stmt));
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(expectNum, cnt);
}

// 通过主键删除数据
void BatchDeleteDataVertexByPK(GmcConnT *conn, GmcStmtT *stmt, const char *vertexLabelName,
                               uint32_t replaceNum, uint32_t startIdx)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    GmcBatchRetT batchRet = {};
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_DELETE));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (uint32_t i = startIdx; i < (startIdx + replaceNum); i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        int value = i + 1;
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value, sizeof(uint32_t)));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchDestroy(batch));
}

void Bytes2Uint(uint8_t *start, uint32_t len, uint32_t *number)
{
    ASSERT_GT(len, 0u);
    uint8_t step = 8;
    *number = start[0];
    for (uint32_t i = 1; i < len; i++) {
        *number |= start[i] << (i * step);
    }
}

void PrintDeleteModeTlvDefault(uint8_t *tlvData)
{
    // for each SubPathDataT
    uint32_t offset = 0;
    uint32_t pathNum = 0;
    Bytes2Uint(tlvData + offset, 4, &pathNum);
    offset += 4;
    // for each PathPushWrapT

    uint32_t totalLen = (uint32_t)sizeof(uint32_t);
    for (uint32_t pathIdx = 0; pathIdx < pathNum; ++pathIdx) {
    uint32_t bufferLen = 0;
    Bytes2Uint(tlvData + offset, 4, &bufferLen);
    offset += 4;
    totalLen += bufferLen + sizeof(uint32_t);

    uint32_t pathType = 0;
    Bytes2Uint(tlvData + offset, 2, &pathType);
    offset += 2;

    uint32_t pathLength = 0;
    Bytes2Uint(tlvData + offset, 2, &pathLength);
    offset += 2;

    uint32_t pathIdInApp = 0;
    Bytes2Uint(tlvData + offset, 4, &pathIdInApp);
    offset += 4;

    uint32_t vertexNum = 0;
    Bytes2Uint(tlvData + offset, 4, &vertexNum);
    offset += 4;

    uint32_t i = 0;
    for (i = 0; i < vertexNum; i++) {
        uint32_t encapType = 0;
        Bytes2Uint(tlvData + offset, 2, &encapType);
        offset += 2;

        if (vertexNum == 1 && hasTrigSource) {
 	    // currently the trigger source is on v2 only
            ASSERT_EQ(20u, encapType);
        } else if (i == 0 && !isReplaceTlv) {
            ASSERT_EQ(10u, encapType); // the type for v1 of path1
        } else if (i == 0 && isReplaceTlv) {
            ASSERT_EQ(100u, encapType);  // the type for v1 of path1
        } else if (i != 0 && !isReplaceTlv) {
            ASSERT_EQ(20u, encapType);  // the type for v2 of path1
        } else if (i == vertexNum - 1  && hasTrigSource) {
            ASSERT_EQ(20u, encapType);  // the type for v2 of path1
        } else if (i != 0 && isReplaceTlv) {
            ASSERT_EQ(200u, encapType);  // the type for v2 of path1
        }

        uint32_t vertexLen = 0;
        Bytes2Uint(tlvData + offset, 2, &vertexLen);
        offset += 2;

        uint32_t subField1 = 0;
        Bytes2Uint(tlvData + offset, 4, &subField1);
        offset += 4;

        uint32_t subField2 = 0;
        Bytes2Uint(tlvData + offset, 4, &subField2);
        offset += 4;

        uint32_t subField3 = 0;
        Bytes2Uint(tlvData + offset, 4, &subField3);
        offset += 4;
        
        if (i == vertexNum - 1 && hasTrigSource) {
            break;
        }

        uint32_t sonIndex = 0;
        Bytes2Uint(tlvData + offset, 2, &sonIndex);
        offset += 2;

        uint32_t siblingIndex = 0;
        Bytes2Uint(tlvData + offset, 2, &siblingIndex);
        offset += 2;
    }
    ASSERT_EQ(offset, totalLen);
}
}

// callback of sub for default tlv of Delete Mode path1
void PathUserCb4DeleteModeTlvDefault(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    if (dataType == PATH_TYPE) {
        // 解析第一个4字节数目
        uint32_t pathNum = 0;
        Bytes2Uint(data, 4, &pathNum);
        *(uint32_t *)userData += pathNum;
    }
    PrintDeleteModeTlvDefault(data);
}

#endif /* LOGICAL_VIEW_H  */
