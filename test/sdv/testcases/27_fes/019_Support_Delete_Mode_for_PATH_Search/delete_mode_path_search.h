/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * @Author: t<PERSON><PERSON><PERSON>
 * @Date: 2023-12-13 15:44:33
 * @FilePath: \GMDBV5\test\sdv\testcases_rd\001_fes\019_Support_Delete_Mode_for_PATH_Search_test\019_fes_util.h
 * @Description: support_delete_mode
 * @LastEditors: tian<PERSON><PERSON>
 * @LastEditTime: 2023-12-20 15:41:31
 */

#ifndef DELETE_MODE_PATH_SEARCH_H
#define DELETE_MODE_PATH_SEARCH_H
#include "gtest/gtest.h"
#include "adpt_define.h"
#include "gmc_gql.h"
#include "t_rd_sn.h"
#include "clt_stmt.h"
#include "gmc_types.h"

const uint32_t WAIT_TIME = 1000; // ms
bool isReplaceTlv = true;
bool hasTrigSource = false;
#define BEGIN_TLV 204
#define END_TLV 205


typedef struct {
    uint32_t c1;
    uint32_t c2;
} TableT;

void InitAllLabels(GmcStmtT *stmt)
{
    const char *createVertexLabel = R"(
        CREATE VERTEXLABEL v1 (
            c1 uint32,
            c2 uint32
            PRIMARY INDEX pk(c1)
            HAC_HASH INDEX index1(c1) UNIQUE,
            MULTI_HASH INDEX index2(c2)
        );
        CREATE VERTEXLABEL v2 (
            c1 uint32,
            c2 uint32
            PRIMARY INDEX pk(c1)
            HAC_HASH INDEX index1(c1) UNIQUE,
            MULTI_HASH INDEX index2(c2)
        );
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createVertexLabel));

    const char *createEdgeLabel = R"(
        CREATE EDGE e12
            FROM v1 TO v2
            WHERE v1.c2 == v2.c2;
        CREATE EDGE onewaye12
            FROM v1 TO v2
            WHERE v1.c2 == v2.c2
            ONEWAY;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createEdgeLabel));
}

void RemoveAllLabels(GmcStmtT *stmt)
{
    const char *dropEdge = R"(
        DROP EDGE e12;
        DROP EDGE onewaye12;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, dropEdge));

    const char *dropVertexLabel = R"(
        DROP VERTEXLABEL v1;
        DROP VERTEXLABEL v2;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, dropVertexLabel));
}


void Bytes2Uint(uint8_t *start, uint32_t len, uint32_t *number)
{
    ASSERT_GT(len, 0u);
    uint8_t step = 8;
    *number = start[0];
    for (uint32_t i = 1; i < len; i++) {
        *number |= start[i] << (i * step);
    }
}

void PrintDeleteModeTlvDefaultOfPath1(uint8_t *tlvData)
{
    // for each SubPathDataT
    printf("====path data:begin===\n");
    uint32_t offset = 0;
    uint32_t pathNum = 0;
    Bytes2Uint(tlvData + offset, 4, &pathNum);
    printf("pathNum(4): %u\n", pathNum);
    offset += 4;
    // for each PathPushWrapT

    uint32_t totalLen = (uint32_t)sizeof(uint32_t);
    for (uint32_t pathIdx = 0; pathIdx < pathNum; ++pathIdx) {
        uint32_t bufferLen = 0;
        Bytes2Uint(tlvData + offset, 4, &bufferLen);
        printf("bufferLen(4): %u\n", bufferLen);
        offset += 4;
        totalLen += bufferLen + sizeof(uint32_t);

        uint32_t pathType = 0;
        Bytes2Uint(tlvData + offset, 2, &pathType);
        printf("pathType(2): %u\n", pathType);
        offset += 2;

        uint32_t pathLength = 0;
        Bytes2Uint(tlvData + offset, 2, &pathLength);
        printf("pathLength(2): %u\n", pathLength);
        offset += 2;

        uint32_t pathIdInApp = 0;
        Bytes2Uint(tlvData + offset, 4, &pathIdInApp);
        printf("pathIdInApp(4): %u\n", pathIdInApp);
        offset += 4;

        uint32_t vertexNum = 0;
        Bytes2Uint(tlvData + offset, 4, &vertexNum);
        printf("vertexNum(4): %u\n", vertexNum);
        offset += 4;

        uint32_t i = 0;
        printf("vertexnum %u", vertexNum);
        for (i = 0; i < vertexNum; i++) {
            uint32_t encapType = 0;
            Bytes2Uint(tlvData + offset, 2, &encapType);
            printf("encapType(2): %u\n", encapType);
            offset += 2;

            // here we have to differentiate between replace/delete to check the appropriate encapType
            if (vertexNum == 1 && hasTrigSource) {
                // currently the trigger source is on v2 only
                EXPECT_EQ(20u, encapType);
            } else if (i == 0 && !isReplaceTlv) {
                EXPECT_EQ(10u, encapType); // the type for v1 of path1
            } else if (i == 0 && isReplaceTlv) {
                EXPECT_EQ(100u, encapType); // the type for v1 of path1
            } else if (i != 0 && !isReplaceTlv) {
                EXPECT_EQ(20u, encapType); // the type for v2 of path1
            } else if (i == vertexNum - 1 && hasTrigSource) {
                EXPECT_EQ(20u, encapType); // the type for v2 of path1
            } else if (i != 0 && isReplaceTlv) {
                EXPECT_EQ(200u, encapType); // the type for v2 of path1
            }

            uint32_t vertexLen = 0;
            Bytes2Uint(tlvData + offset, 2, &vertexLen);
            printf("vertexLen(2): %u\n", vertexLen);
            offset += 2;

            uint32_t subField1 = 0;
            Bytes2Uint(tlvData + offset, 4, &subField1);
            printf("subField1(4): %u\n", subField1);
            offset += 4;

            uint32_t subField2 = 0;
            Bytes2Uint(tlvData + offset, 4, &subField2);
            printf("subField2(4): %u\n", subField2);
            offset += 4;

            if (i == vertexNum - 1 && hasTrigSource) {
                break;
            }

            uint32_t sonIndex = 0;
            Bytes2Uint(tlvData + offset, 2, &sonIndex);
            printf("sonIndex(2): %u\n", sonIndex);
            offset += 2;

            uint32_t siblingIndex = 0;
            Bytes2Uint(tlvData + offset, 2, &siblingIndex);
            printf("siblingIndex(2): %u\n", siblingIndex);
            offset += 2;
        }
        EXPECT_EQ(offset, totalLen);
    }
    printf("====path data:end===\n\n");
}

// callback of sub for default tlv of Delete Mode path1
void PathUserCb4DeleteModeTlvDefaultOfPath1(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    AW_MACRO_ASSERT_NE_BOOL(data, nullptr);
    if (dataType == PATH_TYPE) {
        // 解析第一个4字节数目
        uint32_t pathNum = 0;
        Bytes2Uint(data, 4, &pathNum);
        *(uint32_t *)userData += pathNum;
    }
    ASSERT_EQ(PATH_TYPE, dataType);
    PrintDeleteModeTlvDefaultOfPath1(data);
}


// 通过数组的形式向表中插入数据
void BatchReplaceVertexForPathWithProperties(GmcConnT *conn, GmcStmtT *stmt, const char *vertexLabelName,
    uint32_t replaceNum, uint32_t startIdx, uint32_t *c1PropArr, uint32_t *c2PropArr)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL; // set model type to router into path service
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_REPLACE));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TableT tmpVertex = { 0 };
    for (uint32_t i = startIdx; i < startIdx + replaceNum; i++) {
        tmpVertex.c1 = c1PropArr[i]; // primary key
        tmpVertex.c2 = c2PropArr[i];
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetVertexPropertyAll(stmt, &tmpVertex, sizeof(tmpVertex)));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    GmcBatchRetT batchRet = {};
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchDestroy(batch));
    // check the result of Batch Replace
    // 1.check total num
    // 2.check record value
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecute(stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(stmt, "c1", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c1 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c1", &c1, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c1PropArr[cnt], c1);
        ret = GmcGetVertexPropertySizeByName(stmt, "c2", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c2 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c2", &c2, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c2PropArr[cnt], c2);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(replaceNum + startIdx, cnt);
}


void checkData(GmcConnT *conn, GmcStmtT *stmt, const char *vertexLabelName, uint32_t *c1PropArr, uint32_t *c2PropArr,
    uint32_t expectNum)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    GmcBatchRetT batchRet = {};
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // check data
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(stmt));
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t cnt = 0;
    bool isFinish = false;
    while (true) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(stmt, "c1", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c1 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c1", &c1, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c1PropArr[cnt], c1);
        ret = GmcGetVertexPropertySizeByName(stmt, "c2", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        uint32_t c2 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c2", &c2, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c2PropArr[cnt], c2);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(expectNum, cnt);
}

// 通过主键删除数据
void BatchDeleteDataVertexByPK(GmcConnT *conn, GmcStmtT *stmt, const char *vertexLabelName, uint32_t replaceNum,
    uint32_t startIdx, uint32_t *c1PropArr)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    GmcBatchRetT batchRet = {};
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_DELETE));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = startIdx; i < (startIdx + replaceNum); i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetIndexKeyName(stmt, "pk"));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK,
            GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &c1PropArr[i], sizeof(uint32_t)));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchDestroy(batch));
}

#endif /* DELETE_MODE_PATH_SEARCH_H  */
