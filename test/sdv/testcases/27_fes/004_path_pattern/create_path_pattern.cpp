/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 File Name: 05_CreatePath.cpp
 Description: CreatePath测试
 Author: zhaoningning
 Create: 2023-07-29
 *****************************************************************************/

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "gtest/gtest.h"

#include "gmc_gql.h"
#include "t_rd_common.h"
#include "t_common.h"
#include "t_rd_sn.h"
#include "gmc.h"
#include "gmc_errno.h"
#include "gmc_types.h"
#include "../001_vertexlabel/vertex_util.h"


#define DSL_TEMPLATE_STR_LEN 1024*1024

GmcConnT *conn;
GmcStmtT *stmt;
Status ret;
char *schema_json = NULL;
const uint32_t STR_MAX_LEN = 1024*1024;

class Gmcgql_CreatePath : public testing::Test {
public:
    static void SetUpTestCase()
    {
        //初始化配置文件
        InitCfg();
        
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh  recover");
    };
    virtual void SetUp();
    virtual void TearDown();
};

void Gmcgql_CreatePath::SetUp()
{
    // 创建同步客户端连接
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void Gmcgql_CreatePath::TearDown()
{
    // 关闭 client connection
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void create_edgelabel_vertexlabel(GmcStmtT *stmt)
{
    const char *createVertexLabel = R"(CREATE VERTEXLABEL v01 (
        uiVsIndex UINT32,
        uiLdpMode UINT32
        MULTI_HASH INDEX index01(uiVsIndex,uiLdpMode),
        MULTI_HASH INDEX index02(uiLdpMode),
        MULTI_HASH INDEX index03(uiVsIndex)
    );CREATE VERTEXLABEL v02 (
        uiVsIndex UINT32,
        uiLdpMode UINT32
        MULTI_HASH INDEX index01(uiVsIndex)
    );CREATE VERTEXLABEL v03 (
        uiVsIndex UINT32, 
        uiLdpMode UINT32
        MULTI_HASH INDEX index01(uiVsIndex)
    );)";
    int ret = GmcExecGql(stmt, createVertexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createEdgeLabel = R"(CREATE EDGE e0102
        FROM v01 TO v02
        WHERE v01.uiVsIndex == v02.uiVsIndex
        AND v01.uiLdpMode NOT IN 1, 2
    ;CREATE EDGE e01en1
        FROM v01 TO NULL
    ;CREATE EDGE e0103
        FROM v01 TO v03
        WHERE v01.uiVsIndex == v03.uiVsIndex
    ;)";
    ret = GmcExecGql(stmt, createEdgeLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void drop_edgelabel_vertexlabel(GmcStmtT *stmt)
{
    const char *dropedgelabel = R"(
        DROP EDGE e0102;
        DROP EDGE e0103;
        DROP EDGE e01en1;
    )";
    int ret = GmcExecGql(stmt, dropedgelabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *dropvertexlabel = R"(
        DROP VERTEXLABEL v01;
        DROP VERTEXLABEL v02;
        DROP VERTEXLABEL v03;
    )";
    ret = GmcExecGql(stmt, dropvertexlabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 语句传空字符
TEST_F(Gmcgql_CreatePath, GQL_interfacetest01)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ret = GmcExecGql(stmt, "");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 语句传参NULL
TEST_F(Gmcgql_CreatePath, GQL_interfacetest02)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ret = GmcExecGql(stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 传参（NULL, NULL）
TEST_F(Gmcgql_CreatePath, GQL_interfacetest03)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ret = GmcExecGql(NULL, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 语句只有一个分号
TEST_F(Gmcgql_CreatePath, GQL_interfacetest04)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ret = GmcExecGql(stmt, ";");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 语句句柄传NULL 
TEST_F(Gmcgql_CreatePath, GQL_interfacetest05)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ret = GmcExecGql(NULL, "drop edge e0102");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 传入换行符
TEST_F(Gmcgql_CreatePath, GQL_interfacetest06)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = ret = GmcExecGql(NULL, "\n");
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建path，包含带谓词的一般边和空边，存在或组，携带订阅字段
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    /* path1图示：
     *            v01
     *        / or |   \    
     *       v02  NULL  v03  
     */
    create_edgelabel_vertexlabel(stmt);
    const char *createPath1 = R"(CREATE PATH pathtest1(
        MATCH 
        (v01:v01)-[:e0103]->(:v03),
        (v01:v01)-[:e0102|:e01en1]->(
            (:v02)|
            (NULL)
        )
        RETURN
        (
            REPLACE v01.uiVsIndex 100, v02.uiVsIndex 200
        )
    ))";
    ret = GmcExecGql(stmt, createPath1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP PATH pathtest1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建path，存在不带谓词的一般边和空边，无或组；
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    /* path图示：
     *         v01
     *        /   \    
     *       v02   v03  
     */
    
    create_edgelabel_vertexlabel(stmt);
    const char *createPathInfo = R"(CREATE PATH pathtest2 (
        MATCH 
        (v01:v01)-[:e0102]->(:v02),
        (v01)-[:e0103]->(:v03)
         RETURN
        (
            REPLACE v01.uiVsIndex 100, v02.uiVsIndex 200
        )
    ))";
    ret = GmcExecGql(stmt, createPathInfo);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP PATH pathtest2");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建path，不携带订阅字段
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    /* path图示：
     *            v01
     *        / or |   \    
     *       v02  NULL  v03  
     */
    create_edgelabel_vertexlabel(stmt);
    const char *createPathInfo = R"(CREATE PATH pathtest3 (
        MATCH 
        (v01:v01)-[:e0102|:e01en1]->(
                (:v02) | (NULL)
            ),
        (v01)-[:e0103]->(:v03)
    );)";
    ret = GmcExecGql(stmt, createPathInfo);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP PATH pathtest3");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// path name分为前后缀，前缀不能超过31个字符([a-zA-Z_]*)，后缀是uint32_t，且不能以0开头
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_01)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath1 = R"(CREATE PATH abcdefghijklmnopqrstuvwxyzABCD_4294967295 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    AW_FUN_Log(LOG_STEP, createPath1);
    ret = GmcExecGql(stmt, createPath1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // path name字符数42
    const char *createPath2 = R"(CREATE PATH abcdefghijklmnopqrstuvwxyzABCDE_4294967295 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_NAME_TOO_LONG, ret);

    // 前缀32字符
    const char *createPath3 = R"(CREATE PATH abcdefghijklmnopqrstuvwxyzABCDE_123 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);

    // 前缀包括数字
    const char *createPath4 = R"(CREATE PATH abcdefghijklmnopqrstuvwxyz1234_123 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);

    // 后缀0开头
    const char *createPath6 = R"(CREATE PATH abcdefghijklmnopqrstuvwxyz0123 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath6);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);

    // 后缀大于uint32_t
    const char *createPath7 = R"(CREATE PATH abcdefghijklmnopqrstuvwxy4294967296 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);

    // 后缀0
    const char *createPath8 = R"(CREATE PATH abcd0 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath8);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecGql(stmt, "DROP PATH abcdefghijklmnopqrstuvwxyzABCD_4294967295");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP PATH abcd0");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

 // NAME前缀包括其他特殊字符
 TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_02)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // @
    create_edgelabel_vertexlabel(stmt);
    const char *createPath1 = R"(CREATE PATH abcdefghijklmnopqrstuvwxyz@_123 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 单引号
    const char *createPath2 = R"(CREATE PATH 'abcdefghijklmnopqrstuvwxyz'_123 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 双引号
    const char *createPath3 = R"(CREATE PATH "abcdefghijklmnopqrstuvwxyz"@"_123 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // #
    const char *createPath4 = R"(CREATE PATH abcdefghijklmn#opqrstuvwxyz_123 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // ！
    const char *createPath5 = R"(CREATE PATH abcdefghijklmnopqrstuvwxyz_!123 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath5);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // - =
    const char *createPath6 = R"(CREATE PATH abcdefghijklmn=opqrstuvwxyz-123 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath6);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// variable字符数---不做限制
 TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_03)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath1 = R"(CREATE PATH path_301 (
        MATCH 
        (v012345678901234567890123456789:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // variable32字符
    const char *createPath2 = R"(CREATE PATH path_302 (
        MATCH 
        (v_0123456789012345678901234567890:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP PATH path_301");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP PATH path_302");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 头节点唯一
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_04)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createEdgeLabel = R"(CREATE EDGE e03n1
        FROM v03 TO NULL
    ;)";
    ret = GmcExecGql(stmt, createEdgeLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 头节点不唯一
    const char *createPath1 = R"(CREATE PATH path_4 (
        MATCH 
        (:v01)-[:e0102]->(:v02),
        (:v03)-[:e03n1]->(NULL)
    ))";
    ret = GmcExecGql(stmt, createPath1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);
    ret = GmcExecGql(stmt, "DROP EDGE e03n1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// path包含202个点201条边
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_05)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *templateDsl = R"(
        CREATE VERTEXLABEL vtest%d (
            uiVsIndex UINT32,
            uiLdpMode UINT32
        );
    )";
    char VertexLabel[DSL_TEMPLATE_STR_LEN];
    memset_s(VertexLabel, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    for (uint32_t i = 1; i <= 202; ++i) {
        sprintf_s(VertexLabel, DSL_TEMPLATE_STR_LEN, templateDsl, i);
        ret = GmcExecGql(stmt, VertexLabel);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
 
    const char *templateDsl2 = R"(
        CREATE EDGE edge1%d FROM vtest1 TO vtest%d
        WHERE vtest1.uiVsIndex == vtest%d.uiVsIndex;)";
    char EdgeLabel[DSL_TEMPLATE_STR_LEN];
    memset_s(EdgeLabel, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    for (uint32_t i = 2; i <= 201; ++i) {
        sprintf_s(EdgeLabel, DSL_TEMPLATE_STR_LEN, templateDsl2, i, i, i);
        ret = GmcExecGql(stmt, EdgeLabel);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
      //  path包含201个点200条边
    const char *templateDsl3 = R"(CREATE PATH path_501 (
        MATCH 
        (vtest1:vtest1)-[:edge12]->(:vtest2),
        %s
        (vtest1)-[:edge1201]->(:vtest201)
    ))";
    const char *templateDsl4 = "(vtest1)-[:edge1%d]->(:vtest%d),\n";
    char CreatePath[DSL_TEMPLATE_STR_LEN];
    memset_s(CreatePath, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    char temp[DSL_TEMPLATE_STR_LEN];
    for (uint32_t i = 3; i <= 200; ++i) {
        sprintf_s(temp, DSL_TEMPLATE_STR_LEN, templateDsl4, i, i);
        strcat_s(CreatePath, DSL_TEMPLATE_STR_LEN, temp);   
    }
    char CreatePath01[DSL_TEMPLATE_STR_LEN];
    memset_s(CreatePath01, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    sprintf_s(CreatePath01, DSL_TEMPLATE_STR_LEN, templateDsl3, CreatePath);
    ret = GmcExecGql(stmt, CreatePath01);

    // path包含202个点201条边
    const char *templateDsl5 = ",(vtest1)-[:edge1202]->(:vtest202)";
    sprintf_s(temp, DSL_TEMPLATE_STR_LEN, templateDsl5);
    strcat_s(CreatePath, DSL_TEMPLATE_STR_LEN, temp);
    memset_s(CreatePath01, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    sprintf_s(CreatePath01, DSL_TEMPLATE_STR_LEN, templateDsl3, CreatePath);
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// path的最大深度为15
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_06)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *templateDsl = R"(
        CREATE VERTEXLABEL vtest0%d (
            uiVsIndex UINT32,
            uiLdpMode UINT32
            MULTI_HASH INDEX index01(uiVsIndex)
        );
    )";
    char VertexLabel[DSL_TEMPLATE_STR_LEN];
    memset_s(VertexLabel, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    for (uint32_t i = 1; i <= 17; ++i) {
        sprintf_s(VertexLabel, DSL_TEMPLATE_STR_LEN, templateDsl, i);
        ret = GmcExecGql(stmt, VertexLabel);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    const char *templateDsl2 = R"(
        CREATE EDGE edge05_%d FROM vtest0%d TO vtest0%d
        WHERE vtest0%d.uiVsIndex == vtest0%d.uiVsIndex;)";
    char EdgeLabel[DSL_TEMPLATE_STR_LEN];
    memset_s(EdgeLabel, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    for (uint32_t i = 1; i <= 16; ++i) {
        sprintf_s(EdgeLabel, DSL_TEMPLATE_STR_LEN, templateDsl2, i, i, i+1, i, i+1);
        ret = GmcExecGql(stmt, EdgeLabel);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    const char *templateDsl3 = R"(CREATE PATH path_5 (
        MATCH 
        (:vtest01)%s
    ))";
    const char *templateDsl4 = "-[:edge05_%d]->(:vtest0%d)\n";
    char CreatePath[DSL_TEMPLATE_STR_LEN];
    memset_s(CreatePath, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    char temp[DSL_TEMPLATE_STR_LEN];
    for (uint32_t i = 1; i <= 14; ++i) {
        sprintf_s(temp, DSL_TEMPLATE_STR_LEN, templateDsl4, i, i+1);
        strcat_s(CreatePath, DSL_TEMPLATE_STR_LEN, temp);   
    }
    char CreatePath01[DSL_TEMPLATE_STR_LEN];
    memset_s(CreatePath01, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    sprintf_s(CreatePath01, DSL_TEMPLATE_STR_LEN, templateDsl3, CreatePath);
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 深度16
    const char *templateDsl5 = "-[:edge05_15]->(:vtest016)";
    sprintf_s(temp, DSL_TEMPLATE_STR_LEN, templateDsl5);
    strcat_s(CreatePath, DSL_TEMPLATE_STR_LEN, temp);
    memset_s(CreatePath01, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    sprintf_s(CreatePath01, DSL_TEMPLATE_STR_LEN, templateDsl3, CreatePath);
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  07最多创建500个path--放在删除测试
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_08_1)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *templateDsl = R"(
        CREATE VERTEXLABEL vtest08_%d (
            uiVsIndex UINT32,
            uiLdpMode UINT32
            MULTI_HASH INDEX index01(uiVsIndex)
        );
    )";
    char VertexLabel[DSL_TEMPLATE_STR_LEN];
    memset_s(VertexLabel, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    for (uint32_t i = 1; i <= 202; ++i) {
        sprintf_s(VertexLabel, DSL_TEMPLATE_STR_LEN, templateDsl, i);
        ret = GmcExecGql(stmt, VertexLabel);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    const char *templateDsl2 = R"(
        CREATE EDGE e081%d FROM vtest08_1 TO vtest08_%d
        WHERE vtest08_1.uiVsIndex == vtest08_%d.uiVsIndex;)";
    char EdgeLabel[DSL_TEMPLATE_STR_LEN];
    memset_s(EdgeLabel, DSL_TEMPLATE_STR_LEN, '\0', DSL_TEMPLATE_STR_LEN);
    for (uint32_t i = 2; i <= 201; ++i) {
        sprintf_s(EdgeLabel, DSL_TEMPLATE_STR_LEN, templateDsl2, i, i, i);
        ret = GmcExecGql(stmt, EdgeLabel);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    //  或组中边标签个数为0
    const char *CreatePath01 = R"(CREATE PATH path_801 (
        MATCH 
        (:vtest08_1)-[|]->(:vtest08_2)
    ))";
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    //  或组中边标签个数为1
    const char *CreatePath02 = R"(CREATE PATH path_802 (
        MATCH 
        (:vtest08_1)-[:e0812|]->(:vtest08_2)
    ))";
    ret = GmcExecGql(stmt, CreatePath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    // 一个或组中最多包含160条边
    readJanssonFile("path_limit_160or_createpath.txt", &schema_json);
    ret = GmcExecGql(stmt, schema_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //161条边
    readJanssonFile("path_limit_161or_createpath.txt", &schema_json);
    ret = GmcExecGql(stmt, schema_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 一个variable对应多个label
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_09_1)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *CreatePath01 = R"(CREATE PATH path_901 (
        MATCH 
        (v01:v01)-[:e0102]->(:v02),
        (v2:v01)-[:e0103]->(:v03)
    ))";
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 使用未定义关联label的variable
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_09_2)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *CreatePath02 = R"(CREATE PATH path_902 (
        MATCH 
        (v01:v01)-[:e0102]->(:v02),
        (v02)-[:e0103]->(:v03)
    ))";
    ret = GmcExecGql(stmt, CreatePath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 使用未定义的点、边
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_10)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    // 使用未定义的点
    const char *CreatePath01 = R"(CREATE PATH path_1001 (
        MATCH 
        (:invalidvertex)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    // 使用未定义的边
    const char *CreatePath02 = R"(CREATE PATH path_1002 (
        MATCH 
        (:v01)-[:invalidedge]->(:v02)
    ))";
    ret = GmcExecGql(stmt, CreatePath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    // 或组中存在未定义边
    const char *CreatePath03 = R"(CREATE PATH path_1003 (
        MATCH 
        (v01:v01)-[:e0102|:invalidedge]->((:v02) | (NULL))
    );)";
    ret = GmcExecGql(stmt, CreatePath03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);  
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 拓扑结构与实际的边标签的源点目的点不一致
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_11)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    // 使用定义的边，但不是边定义的点
    const char *CreatePath01 = R"(CREATE PATH path_1101 (
        MATCH 
        (:v01)-[:e0103]->(:v02)
    );)";
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);
    // 空边的目的点不为NULL
    const char *CreatePath02 = R"(CREATE PATH path_1102 (
        MATCH 
        (:v01)-[:e01en1]->(:v02)
    );)";
    ret = GmcExecGql(stmt, CreatePath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);
    // 或组边点不匹配
    const char *CreatePath03 = R"(CREATE PATH path_1103 (
        MATCH 
        (v01:v01)-[:e0102|:e01en1]->((:v02)|(:v03))
    );)";
    ret = GmcExecGql(stmt, CreatePath03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);  
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  同一边标签不能出现多次
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_12)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *CreatePath01 = R"(CREATE PATH path12 (
        MATCH 
        (v01:v01)-[:e0102]->(:v02),
        (v01)-[:e0102]->(:v02)
    );)";
    ret = GmcExecGql(stmt, CreatePath01);
    // 变更边重复错误码统一为GMERR_INVALID_OBJECT_DEFINITION
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret); 
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// path中有环，无根节点    
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_13)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createEdgeLabel = R"(   
        CREATE EDGE e0203 FROM v02 TO v03 WHERE v02.uiVsIndex == v03.uiVsIndex;
        CREATE EDGE e0301 FROM v03 TO v01 WHERE v03.uiVsIndex == v01.uiVsIndex;
        CREATE EDGE e0302 FROM v03 TO v02 WHERE v03.uiVsIndex == v02.uiVsIndex;
        CREATE EDGE e0201 FROM v02 TO v01 WHERE v02.uiVsIndex == v01.uiVsIndex;
        )";
    ret = GmcExecGql(stmt, createEdgeLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 逆时针环
    const char *CreatePath01 = R"(CREATE PATH path1301 (
        MATCH (v01:v01)-[:e0102]->(:v02)-[:e0203]->(:v03)-[:e0301]->(v01)
    );)";
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret); 
    // 顺时针环
    const char *CreatePath02 = R"(CREATE PATH path1302 (
        MATCH 
        (v01:v01)-[:e0103]->(:v03)-[:e0302]->(:v02)-[:e0201]->(v01)
    );)";
    ret = GmcExecGql(stmt, CreatePath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret); 
    const char *dropedgelabel = R"(
        DROP EDGE e0203;
        DROP EDGE e0301;
        DROP EDGE e0302;
        DROP EDGE e0201;
    )";
    ret = GmcExecGql(stmt, dropedgelabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// path不连通，根节点大于1
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_14)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createEdgeLabel = R"(   
        CREATE EDGE e0203 FROM v02 TO v03 WHERE v02.uiVsIndex == v03.uiVsIndex;
        )";
    ret = GmcExecGql(stmt, createEdgeLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *CreatePath01 = R"(CREATE PATH path14 (
        MATCH (:v01)-[:e0103]->(:v03),
              (:v02)-[:e0203]->(:v03)
    );)";
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret); 
    ret = GmcExecGql(stmt, "DROP EDGE e0203");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 或组嵌套
/*
     *            v01
     *         /  |   or  \
     *       v02  v03    NULL
     *            | or \    
     *           v04   NULL
     */
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_15)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
     const char *createVertexLabel = R"(CREATE VERTEXLABEL v04 (
        uiVsIndex UINT32,
        uiLdpMode UINT32
        MULTI_HASH INDEX index01(uiVsIndex,uiLdpMode),
        MULTI_HASH INDEX index02(uiLdpMode),
        MULTI_HASH INDEX index03(uiVsIndex)
    );)";
    int ret = GmcExecGql(stmt, createVertexLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *createEdgeLabel = R"(   
        CREATE EDGE e0304 FROM v03 TO v04 WHERE v03.uiVsIndex == v04.uiVsIndex;
        CREATE EDGE e03en1  FROM v03 TO NULL;
        CREATE EDGE e0203 FROM v02 TO v03 WHERE v03.uiVsIndex == v02.uiVsIndex;
        )";
    ret = GmcExecGql(stmt, createEdgeLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *CreatePath01 = R"(CREATE PATH path1501 (
        MATCH (v01:v01)-[:e0102]->(:v02),
              (v01)-[:e0103|:e01en1]->((:v03)-[:e0304|:e03en1]->((:v04)|(NULL))|(NULL))
    );)";
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret); 
    // 或组空写前面
    const char *CreatePath02 = R"(CREATE PATH path1502 (
        MATCH (v01:v01)-[:e0102]->(:v02),
              (v01)-[:e01en1|:e0103]->((NULL)|(:v03)-[:e0304|:e03en1]->((:v04)|(NULL)))
    );)";
    ret = GmcExecGql(stmt, CreatePath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret); 
    // 或组底部串联
    const char *CreatePath03 = R"(CREATE PATH path1503 (
        MATCH (:v01)-[:e0102|:e0103]->((:v02)-[:e0203]->(:v03)|(:v03))
    );)";
    ret = GmcExecGql(stmt, CreatePath03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret); 
    const char *droppath = R"(
        DROP PATH path1501;
        DROP PATH path1502;
        DROP path path1503;
    )";
    ret = GmcExecGql(stmt, droppath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *dropedge = R"(
        DROP EDGE e0304;
        DROP EDGE e03en1;
        DROP edge e0203;
    )";
    ret = GmcExecGql(stmt, dropedge);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP VERTEXLABEL v04");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 当前不支持入边或组
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_16)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createEdgeLabel = R"(   
        CREATE EDGE e0203 FROM v02 TO v03 WHERE v03.uiVsIndex == v02.uiVsIndex;
        )";
    ret = GmcExecGql(stmt, createEdgeLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 同一或组的边，使用不同节点的入边，指向同一节点
    const char *CreatePath01 = R"(CREATE PATH path1601 (
        MATCH (:v01)-[:e0102]->(:v02),
            (:v01|:v02)-[:e0103|:e0203]->(:v03)
    );)";
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret); 
    // 同一或组的边，使用不同源点，指向不同节点
    const char *CreatePath02 = R"(CREATE PATH path1602 (
        MATCH (:v01)-[:e0102]->(:v02),
              (:v01|:v02)-[:e0203|:e01en1]->((:v03)|(NULL))
    );)";
    ret = GmcExecGql(stmt, CreatePath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret); 
    ret = GmcExecGql(stmt, "DROP edge e0203");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 或组内使用相同边标签
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_17)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createEdgeLabel = R"(   
        CREATE EDGE e12 FROM v01 TO v02 WHERE v01.uiVsIndex == v02.uiVsIndex;
        )";
    ret = GmcExecGql(stmt, createEdgeLabel);
    const char *CreatePath01 = R"(CREATE PATH path1701 (
        MATCH 
        (:v01)-[:e0102|:e0102]->((:v02)|(:v02))
    );)";
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret); 
    // 相同源点目的点不同边标签名，创建或组
    const char *CreatePath02 = R"(CREATE PATH path1702 (
        MATCH 
        (:v01)-[:e12|:e0102]->((:v02)|(:v02))
    );)";
    ret = GmcExecGql(stmt, CreatePath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret); 
    ret = GmcExecGql(stmt, "DROP path path1702");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP edge e12");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 或组间边标签有交集
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_18)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *CreatePath01 = R"(CREATE PATH path18 (
        MATCH 
        (v01:v01)-[:e0102|:e01en1]->((:v02)|(NULL)),
            (v01)-[:e0103|:e01en1]->((:v03)|(NULL))
    );)";
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret); 
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 重复创建同一或组
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_19)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *CreatePath01 = R"(CREATE PATH path19 (
        MATCH 
        (v01:v01)-[:e0102|:e01en1]->((:v02)|(NULL)),
            (v01)-[:e0102|:e01en1]->((:v02)|(NULL))
    );)";
    ret = GmcExecGql(stmt, CreatePath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret); 
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 订阅中点标签不属于path中已定义的点标签
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_20)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath = R"(CREATE PATH path20(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100, v03.uiVsIndex 200
        )
    ))";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 订阅字段的属性不属于path中已定义的点标签属性
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_21)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath = R"(CREATE PATH path21 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100, v02.ui 200
        )
    ))";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNDEFINE_COLUMN, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 相同点标签使用不同的encap_type
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_22)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath = R"(CREATE PATH path221 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100, v01.uiVsIndex 200
        )
    ))";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 存在相同订阅字段
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_23)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath = R"(CREATE PATH path23 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100, v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 当前event只支持replace
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_24)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath = R"(CREATE PATH path24 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            UPDATE v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 订阅字段中每个event最多包含50个field——>300个
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_25)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *templateDsl1 = R"(CREATE VERTEXLABEL test51 (
        %sfield51 UINT32
        MULTI_HASH INDEX index01(field1)
    );)";
    const char *templateDsl2 = "field%d UINT32,\n";
    uint32_t fieldNum = 50;
    char createVertexLabel[STR_MAX_LEN];
    memset_s(createVertexLabel, STR_MAX_LEN, '\0', STR_MAX_LEN);
    char temp[STR_MAX_LEN];
    for (uint32_t i = 1; i <= fieldNum; ++i) {
        sprintf_s(temp, STR_MAX_LEN, templateDsl2, i);
        strcat_s(createVertexLabel, STR_MAX_LEN, temp);
    }
    char createVertexLabel01[STR_MAX_LEN];
    memset_s(createVertexLabel01, STR_MAX_LEN, '\0', STR_MAX_LEN);
    sprintf_s(createVertexLabel01, STR_MAX_LEN, templateDsl1, createVertexLabel);
    int ret = GmcExecGql(stmt, createVertexLabel01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createEdgeLabel = R"(CREATE EDGE e5102
        FROM test51 TO v02
        WHERE test51.field1 == v02.uiVsIndex
    ;)";
    ret = GmcExecGql(stmt, createEdgeLabel);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 50 filed
    const char*createPath = R"(CREATE PATH path251(
        MATCH 
        (:test51)-[:e5102]->(:v02)
        RETURN
        (
            REPLACE test51.field1 100,test51.field2 100,test51.field3 100,test51.field4 100,test51.field5 100,test51.field6 100,
            test51.field7 100,test51.field8 100,test51.field9 100,test51.field10 100,test51.field11 100,test51.field12 100,test51.field13 100,
            test51.field14 100,test51.field15 100,test51.field16 100,test51.field17 100,test51.field18 100,test51.field19 100,test51.field20 100,
            test51.field21 100,test51.field22 100,test51.field23 100,test51.field24 100,test51.field25 100,test51.field26 100,test51.field27 100,
            test51.field28 100,test51.field29 100,test51.field30 100,test51.field31 100,test51.field32 100,test51.field33 100,test51.field34 100,
            test51.field35 100,test51.field36 100,test51.field37 100,test51.field38 100,test51.field39 100,test51.field40 100,test51.field41 100,
            test51.field42 100,test51.field43 100,test51.field44 100,test51.field45 100,test51.field46 100,test51.field47 100,test51.field48 100,test51.field49 100,test51.field50 100));
    )";   
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 51 field
    const char*createPath02 = R"(CREATE PATH path252(
        MATCH 
        (:test51)-[:e5102]->(:v02)
        RETURN
        (
            REPLACE test51.field1 100,test51.field2 100,test51.field3 100,test51.field4 100,test51.field5 100,test51.field6 100,
            test51.field7 100,test51.field8 100,test51.field9 100,test51.field10 100,test51.field11 100,test51.field12 100,test51.field13 100,
            test51.field14 100,test51.field15 100,test51.field16 100,test51.field17 100,test51.field18 100,test51.field19 100,test51.field20 100,
            test51.field21 100,test51.field22 100,test51.field23 100,test51.field24 100,test51.field25 100,test51.field26 100,test51.field27 100,
            test51.field28 100,test51.field29 100,test51.field30 100,test51.field31 100,test51.field32 100,test51.field33 100,test51.field34 100,
            test51.field35 100,test51.field36 100,test51.field37 100,test51.field38 100,test51.field39 100,test51.field40 100,test51.field41 100,test51.field42 100,
            test51.field43 100,test51.field44 100,test51.field45 100,test51.field46 100,test51.field47 100,test51.field48 100,test51.field49 100,test51.field50 100,test51.field51 100));
    )";   
    ret = GmcExecGql(stmt, createPath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP path path251");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP path path252");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP edge e5102");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建一条空边，path只有空边
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_26)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char*createPath = R"(CREATE PATH path26(
        MATCH 
        (:v01)-[:e01en1]->(NULL)
        ))";   
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP path path26");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// encap type必须为unit32_t
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_27)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath = R"(CREATE PATH path2701(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex -1
        )
    ))";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    const char *createPath02 = R"(CREATE PATH path2702(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex '1'
        )
    ))";
    ret = GmcExecGql(stmt, createPath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    const char *createPath03 = R"(CREATE PATH path2703(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 4294967296
        )
    ))";
    ret = GmcExecGql(stmt, createPath03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);  
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 语句缺失
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_28)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    // encap type缺失
    const char *createPath01 = R"(CREATE PATH path2801(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex
        )
    ))";
    ret = GmcExecGql(stmt, createPath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 关键字缺失
    const char *CreatePath02 = R"(CREATE path2802(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, CreatePath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    const char *createPath03 = R"(CREATE PATH path2803(
         
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    const char *createPath04 = R"(CREATE PATH path2804(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        
        (
            REPLACE v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath04);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 变量缺失
    const char *createPath05 = R"(CREATE PATH (
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath05);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    const char *createPath06 = R"(CREATE PATH path2806(
        MATCH 
        (:v01)-[]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath06);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    const char *createPath07 = R"(CREATE PATH path2807(
        MATCH 
        (:v01)-[:e0102]->()
        RETURN
        (
            REPLACE v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath07);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // return为空
    const char *createPath08 = R"(CREATE PATH path2808(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (

        )
    ))";
    ret = GmcExecGql(stmt, createPath08);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 逗号冗余
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_29)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    // create path末尾有逗号
    const char *createPath01 = R"(CREATE PATH path2901(
        MATCH 
        (:v01)-[:e0102]->(:v02),
        RETURN
        (
            REPLACE v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // return括号内语句末尾有逗号
    const char *createPath02 = R"(CREATE PATH path2902(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100,
        )
    ))";
    ret = GmcExecGql(stmt, createPath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//  括号缺失
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_30)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    // 左括号缺失
    const char *createPath01 = R"(CREATE PATH path3001(
        MATCH 
        :v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 或组左括号缺失
    const char *createPath02 = R"(CREATE PATH path3002(
        MATCH 
        (:v01)-:e010|e01en1]->((:v02) |(NULL)) 
    ))";
    ret = GmcExecGql(stmt, createPath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 右括号缺失
    const char *createPath03 = R"(CREATE PATH path3003(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100
        
    ))";
    ret = GmcExecGql(stmt, createPath03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 分号缺失
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_31)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath01 = R"(CREATE PATH path3101(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100
        )
    )
    CREATE PATH path3102(
        MATCH 
        (:v01)-[:e0102]->(:v02))";
    ret = GmcExecGql(stmt, createPath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 括号冗余 
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_32)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    // 括号多一对
    const char *createPath01 = R"(CREATE PATH path3201(
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ));CREATE PATH path3202(
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    const char *createPath02 = R"(CREATE PATH path3203(
        MATCH 
        ((:v01)-[:e0102]->(:v02))
    ))";
    ret = GmcExecGql(stmt, createPath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 整条语句多加了一对括号
    const char *createPath03 = R"((CREATE PATH path3204(
        MATCH 
        (:v01)-[:e0102]->(:v02)
    )))";
    ret = GmcExecGql(stmt, createPath03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 不支持的括号类型
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_33)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath01 = R"(CREATE PATH path3301(
        MATCH 
        (:v01)-[:e0102]->[:v02]
    ))";
    ret = GmcExecGql(stmt, createPath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    const char *createPath02 = R"(CREATE PATH path3302(
        MATCH 
        (:v01)-(:e0102)->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 关键字大小写
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_34)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath01 = R"(CREATE path path3401(
        match
        (:v01)-[:e0102]->(:v02)
        return
        (
            REPLACE v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *createPath02 = R"(CREATE Path path3402(
        MATCH 
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    const char *createPath03 = R"(CREATE PATH path3403(
        Match
        (:v01)-[:e0102]->(:v02)
    ))";
    ret = GmcExecGql(stmt, createPath03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    const char *createPath04 = R"(CREATE PATH path3404(
        MATCH 
        (:v01)-[:e0102]->(:v02)
        Return
        (
            REPLACE v01.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath04);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = GmcExecGql(stmt, "DROP PATH path3401");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建单个path，存在带谓词的空边，在或组中使用
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_35)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createEdge = R"(CREATE EDGE e02en1
        FROM v02 TO NULL
        WHERE v02.uiVsIndex NOT IN 1, 2, 3;
    )";
    ret = GmcExecGql(stmt, createEdge);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *createPath = R"(CREATE PATH path35(
        MATCH 
        (:v01)-[:e0102]->(:v02)-[:e02en1]->(NULL)
    ))";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP path path35");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP edge e02en1");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 大量执行创建不同名的path
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_36)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath = R"(CREATE PATH path3601(
        MATCH 
        (:v01)-[:e0102]->(:v02)
    );CREATE PATH path3602(
        MATCH 
        (:v01)-[:e0102]->(:v02)
    );CREATE PATH path3603(
        MATCH 
        (:v01)-[:e0102]->(:v02)
    );)";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const uint32_t MAX_TEST_TIME = 10;
    const char *templateGql = R"(CREATE PATH path360%d (
        MATCH 
        (:v01)-[:e0102]->(:v02)
    );)";
    char createDiffPath[STR_MAX_LEN];
    for (uint32_t i = 4; i < MAX_TEST_TIME; ++i) {
        sprintf(createDiffPath, templateGql, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createDiffPath));
    }
    char dropDiffPath[STR_MAX_LEN];
    const char *templateGql2 = R"(DROP PATH path360%d;)";
    for (uint32_t i = 1; i < MAX_TEST_TIME; ++i) {
        sprintf(dropDiffPath, templateGql2, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, dropDiffPath));
    }
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 大量执行创建相同名称的path
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_37)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath = R"(CREATE PATH path37(
        MATCH 
        (:v01)-[:e0102]->(:v02)
    );CREATE PATH path37(
        MATCH 
        (:v01)-[:e0102]->(:v02)
    );CREATE PATH path37(
        MATCH 
        (:v01)-[:e0102]->(:v02)
    );)";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);
    ret = GmcExecGql(stmt, "DROP path path37");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多个订阅event
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_38)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath = R"(CREATE PATH path38 (
        MATCH 
        (:v01)-[:e0102]->(:v02)
        RETURN
        (
            REPLACE v01.uiVsIndex 100,
            REPLACE v02.uiVsIndex 100
        )
    ))";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 或组多一个字符 "-"
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_39)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath = R"(CREATE PATH path39 (
        MATCH 
        (:v01)--[:e0102|:e01en1]->((:v02)|(NULL))
    ))";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 或组多一个字符 "|"
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_40)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    const char *createPath = R"(CREATE PATH path40 (
        MATCH 
        (:v01)-[:e0102||:e01en1]->((:v02)|(NULL))
    ))";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 或组中有空格
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_41)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    // ->之间有空格
    const char *createPath01 = R"(CREATE PATH path4101 (
        MATCH 
        (:v01)-[:e0102|:e01en1]- >((:v02)|(NULL))
    ))";
    ret = GmcExecGql(stmt, createPath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // ：和点之间有空格
    const char *createPath02 = R"(CREATE PATH path4102 (
        MATCH 
        (: v01)-[:e0102|:e01en1]- >((:v02)|(NULL))
    ))";
    ret = GmcExecGql(stmt, createPath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP path path4101");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecGql(stmt, "DROP path path4102");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 冒号缺失
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_42)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    create_edgelabel_vertexlabel(stmt);
    // 源点没有：
    const char *createPath01 = R"(CREATE PATH path4201 (
        MATCH 
        (v01)-[:e0102|:e01en1]->((:v02)|(NULL))
    ))";
    ret = GmcExecGql(stmt, createPath01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);
    // 边没有：
    const char *createPath02 = R"(CREATE PATH path4202 (
        MATCH 
        (:v01)-[e0102|:e01en1]->((:v02)|(NULL))
    ))";
    ret = GmcExecGql(stmt, createPath02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    // 目的点没有：
    const char *createPath03 = R"(CREATE PATH path4203 (
        MATCH 
        (:v01)-[:e0102|:e01en1]->((v02)|(NULL))
    ))";
    ret = GmcExecGql(stmt, createPath03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);
    drop_edgelabel_vertexlabel(stmt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建path89
TEST_F(Gmcgql_CreatePath, GQL_004_CREATE_43)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建path89
    const char *CreateEdgeandVertex = R"(CREATE VERTEXLABEL T7 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T3452 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T4171 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T36 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T2153 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T2726 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T3378 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T2169 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T3860 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T1 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T359 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T357 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T37 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T10 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T31 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T279 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T813 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T2009 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T2010 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T2011 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T2337 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T2563 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T2745 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T2896 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE VERTEXLABEL T2336 (uiVsIndex UINT32 primary index pv1(uiVsIndex));
        CREATE EDGE R84300 FROM T7 TO T3452 WHERE T7.uiVsIndex == T3452.uiVsIndex;
        CREATE EDGE R84 FROM T7 TO T37 WHERE T7.uiVsIndex == T37.uiVsIndex;
        CREATE EDGE R4 FROM T7 TO T10 WHERE T7.uiVsIndex == T10.uiVsIndex;
        CREATE EDGE R83016 FROM T7 TO T1 WHERE T7.uiVsIndex == T1.uiVsIndex;
        CREATE EDGE R83017 FROM T7 TO T813 WHERE T7.uiVsIndex == T813.uiVsIndex;
        CREATE EDGE R87275 FROM T7 TO T2009 WHERE T7.uiVsIndex == T2009.uiVsIndex;
        CREATE EDGE R203319 FROM T3452 TO T4171 WHERE T3452.uiVsIndex == T4171.uiVsIndex;
        CREATE EDGE R4301 FROM T3452 TO T36 WHERE T3452.uiVsIndex == T36.uiVsIndex;
        CREATE EDGE R84110 FROM T36 TO T2153 WHERE T36.uiVsIndex == T2153.uiVsIndex;
        CREATE EDGE R203423 FROM T36 TO T359 WHERE T36.uiVsIndex == T359.uiVsIndex;
        CREATE EDGE R82498 FROM T36 TO T2726 WHERE T36.uiVsIndex == T2726.uiVsIndex;
        CREATE EDGE R83557 FROM T36 TO T3378 WHERE T36.uiVsIndex == T3378.uiVsIndex;
        CREATE EDGE R83169 FROM T36 TO T2169 WHERE T36.uiVsIndex == T2169.uiVsIndex;
        CREATE EDGE R86614 FROM T36 TO T3860 WHERE T36.uiVsIndex == T3860.uiVsIndex;
        CREATE EDGE R108 FROM T36 TO T1 WHERE T36.uiVsIndex == T1.uiVsIndex;
        CREATE EDGE R83013 FROM T359 TO T357 WHERE T359.uiVsIndex == T357.uiVsIndex;
        CREATE EDGE R85640 FROM T37 TO T10 WHERE T37.uiVsIndex == T10.uiVsIndex;
        CREATE EDGE R83030 FROM T1 TO T31 WHERE T1.uiVsIndex == T31.uiVsIndex;
        CREATE EDGE R83031 FROM T1 TO T279 WHERE T1.uiVsIndex == T279.uiVsIndex;
        CREATE EDGE R81151 FROM T2009 TO T2010 WHERE T2009.uiVsIndex == T2010.uiVsIndex;
        CREATE EDGE R81152 FROM T2009 TO T2011 WHERE T2009.uiVsIndex == T2011.uiVsIndex;
        CREATE EDGE R81132 FROM T2009 TO T2337 WHERE T2009.uiVsIndex == T2337.uiVsIndex;
        CREATE EDGE R81401 FROM T2009 TO T2563 WHERE T2009.uiVsIndex == T2563.uiVsIndex;
        CREATE EDGE R82449 FROM T2009 TO T2745 WHERE T2009.uiVsIndex == T2745.uiVsIndex;
        CREATE EDGE R83240 FROM T2009 TO T2896 WHERE T2009.uiVsIndex == T2896.uiVsIndex;
        CREATE EDGE R81133 FROM T2337 TO T2336 WHERE T2337.uiVsIndex == T2336.uiVsIndex;)";
    ret = GmcExecGql(stmt, CreateEdgeandVertex);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createPath = R"(CREATE PATH path89 (    
        MATCH    
        (:T7)-[:R84300|:R84|:R4|:R83016|:R83017|:R87275]->(        
            (T3452:T3452)-[:R203319]->(:T4171),        
            (T3452)-[:R4301]->(T36:T36)-[:R84110|:R82498|:R83557|:R83169|:R86614]->(            
                (:T2153)|            
                (:T2726)|            
                (:T3378)|            
                (:T2169)|            
                (:T3860)       
            ),       
            (T36)-[:R203423|:R108]->(            
                (:T359)-[:R83013]->(:T357)|            
                (T1:T1)-[:R83030]->(:T31),            
                (T1)-[:R83031]->(:T279)        
            )|        
            (:T37)-[:R85640]->(:T10)|        
            (:T10)|        
            (:T1)|        
            (:T813)|        
            (:T2009)-[:R81151|:R81152|:R81132|:R81401|:R82449|:R83240]->(            
                (:T2010)|            
                (:T2011)|            
                (:T2337)-[:R81133]->(:T2336)|            
                (:T2563)|            
                (:T2745)|            
                (:T2896)        
            )     
        )    
        );)";
    ret = GmcExecGql(stmt, createPath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
