/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 File Name: 005_create_index.cpp
 Description: 【Path Trigger - Support trigger path search & Publish path result】测试
 Author: tianyihui t30050699
 Create: 2023-09-04
 *****************************************************************************/

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include <pthread.h>

#include "gtest/gtest.h"
#include "gmc_errno.h"
#include "gmc_gql.h"

#include "t_rd_adapt.h"
#include "t_rd_assert.h"
#include "t_rd_sn.h"
#include "t_rd_common.h"
#include "adpt_sleep.h"

#include "fes_util.h"
#include "../001_vertexlabel/vertex_util.h"

GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
using namespace std;

class PathSearch : public testing::Test {
protected:
    static void SetUpTestCase()
    {

        //初始化配置文件
        InitCfg();

        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
       
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testEnvClean();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh  recover");
        GmcDetachAllShmSeg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void PathSearch::SetUp()
{
    // 创建同步客户端连接
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "同步客户端连接启动成功.");

    //预置待订阅的path
    InitAllLabels(stmt);

    AW_FUN_Log(LOG_STEP, "path创建完成.");

}

void PathSearch::TearDown()
{
    // 释放被定义的复杂path/边/点, 与InitAllLabels配对使用
    RemoveAllLabels(stmt);

    // 关闭同步客户端连接
    int ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "同步客户端连接关闭成功.");
}

/*
 * 用例001：一个Path只被一个订阅者通过一个订阅通道订阅，1个同步连接发送使生成2个path实例的Batch DML
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、1个订阅者、1个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. v1插入(1,1),(2,2)
 * 5. v2插入(1,1),(2,2)
 * 6. 接收pathNum,应为2
 */
TEST_F(PathSearch,GQL_010_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
    int ret = testSubConnect(&testSubConn, &stmt_sub, 2, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

     // 发送订阅请求
    const char *subGQL = R"(CREATE SUBSCRIPTION subByUser1OnPath2 ON PATH path2 BY CHANNEL channel_01;)";
    uint32_t pathNum = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum};

    ret = GmcSubscribeComplexPath(stmt, subGQL, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t1_1 ON REPLACE v1 WHEN TRUE DO SEARCH path2;
        CREATE TRIGGER t1_2 ON REPLACE v2 WHEN TRUE DO SEARCH path2;
    )";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));

    BatchReplaceDataVertexForPath01(conn, stmt, "v1", 2);
    BatchReplaceDataVertexForPath01(conn, stmt, "v2", 2);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM = 2;
    while (pathNum != EXPECT_PATH_NUM) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathName = %d\n", pathNum);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM, pathNum);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t1_1;
        DROP TRIGGER t1_2;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath2;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例002：一个Path只被多个订阅者通过一个订阅通道订阅，1个同步连接发送使生成2个path实例的Batch DML
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、2个订阅者、2个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. v1插入(1,1),(2,2)
 * 5. v2插入(1,1),(2,2)
 * 6. 接收pathNum01应为3->2, pathNum02应为4
 */ 
TEST_F(PathSearch,GQL_010_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
    int ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 用户1发送订阅请求subByUser1OnPath1
    const char *subGQL01 = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum01 = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum01};

    ret = GmcSubscribeComplexPath(stmt, subGQL01, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 用户2发送订阅请求subByUser2OnPath1
    const char *subGQL02 = R"(CREATE SUBSCRIPTION subByUser2OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum02 = 0;
    GmcSubUdfT user02 = {.userCb = PathUserCb, .userData = &pathNum02};

    ret = GmcSubscribeComplexPath(stmt, subGQL02, &user02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t2_1 ON REPLACE v1 WHEN TRUE DO SEARCH path1;
        CREATE TRIGGER t2_2 ON REPLACE v2 WHEN TRUE DO SEARCH path1;
    )";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));

    BatchReplaceDataVertexForPath01(conn, stmt, "v1", 2);
    BatchReplaceDataVertexForPath01(conn, stmt, "v2", 2);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM01 = 2;
    const uint32_t EXPECT_PATH_NUM02 = 2;
    while ((pathNum01 != EXPECT_PATH_NUM01) || (pathNum02 != EXPECT_PATH_NUM02)) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 10 * 1000 * 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum01 = %d, pathNum02 = %d\n", pathNum01, pathNum02);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum01 = %d pathNum02 = %d\n", pathNum01, pathNum02);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM01, pathNum01);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM02, pathNum02);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t2_1;
        DROP TRIGGER t2_2;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除用户1发送订阅请求subByUser1OnPath1
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除用户1发送订阅请求subByUser2OnPath1
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser2OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例003：多个Path只被一个订阅者通过一个订阅通道订阅，1个同步连接发送使生成5个path实例的Batch DML

 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、1个订阅者、3个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. v1插入(1,1)   
 * 5. v2插入(1,1),(2,2)  
 * 6. v3插入(1,1)   
 * 7. v4插入(1,1)
 * 8. 接收pathNum,应为5
 */ 
TEST_F(PathSearch,GQL_010_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
     int ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

     // 分别发送3个订阅请求：分别订阅path1、path2、path3
    const char *subGQL01 = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum};
    ret = GmcSubscribeComplexPath(stmt, subGQL01, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subGQL02 = R"(CREATE SUBSCRIPTION subByUser1OnPath2 ON PATH path2 BY CHANNEL channel_01;)";
    ret = GmcSubscribeComplexPath(stmt, subGQL02, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subGQL03 = R"(CREATE SUBSCRIPTION subByUser1OnPath3 ON PATH path3 BY CHANNEL channel_01;)";
    ret = GmcSubscribeComplexPath(stmt, subGQL03, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t3_1 ON REPLACE v1
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t3_2 ON REPLACE v2
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t3_3 ON REPLACE v3
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t3_4 ON REPLACE v4
        WHEN TRUE DO SEARCH path3;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
   
    BatchReplaceDataVertexForPath01(conn, stmt, "v1", 1);
    BatchReplaceDataVertexForPath01(conn, stmt, "v2", 2);
    BatchReplaceDataVertexForPath01(conn, stmt, "v3", 1);
    BatchReplaceDataVertexForPath01(conn, stmt, "v4", 1);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM = 5;
    while (pathNum != EXPECT_PATH_NUM) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 1s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum = %d\n", pathNum);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM, pathNum);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t3_1;
        DROP TRIGGER t3_2;
        DROP TRIGGER t3_3;
        DROP TRIGGER t3_4;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath2;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath3;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例004：多个Path被多个订阅者通过一个订阅通道订阅，1个同步连接发送使生成5个path实例的Batch DML
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、3个订阅者、3个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. v1插入(1,1)
 * 5. v2插入(1,1),(2,2)  
 * 6. v3插入(1,1)   
 * 7. v4插入(1,1)
 * 8. 接收pathNum,应分别为1,3,5
 */ 
TEST_F(PathSearch,GQL_010_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

     // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
     int ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

     // 分别发送3个订阅请求：用户1订阅path1、用户2订阅path2、用户3订阅path3
    // 用户1发送订阅请求subByUser1OnPath1
    const char *subGQL01 = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum01 = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum01};

    ret = GmcSubscribeComplexPath(stmt, subGQL01, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 用户2发送订阅请求subByUser2OnPath2
    const char *subGQL02 = R"(CREATE SUBSCRIPTION subByUser2OnPath2 ON PATH path2 BY CHANNEL channel_01;)";
    uint32_t pathNum02 = 0;
    GmcSubUdfT user02 = {.userCb = PathUserCb, .userData = &pathNum02};

    ret = GmcSubscribeComplexPath(stmt, subGQL02, &user02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 用户2发送订阅请求subByUser3OnPath3
    const char *subGQL03 = R"(CREATE SUBSCRIPTION subByUser3OnPath3 ON PATH path3 BY CHANNEL channel_01;)";
    uint32_t pathNum03 = 0;
    GmcSubUdfT user03 = {.userCb = PathUserCb, .userData = &pathNum03};

    ret = GmcSubscribeComplexPath(stmt, subGQL03, &user03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t4_1 ON REPLACE v1
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t4_2 ON REPLACE v2
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t4_3 ON REPLACE v3
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t4_4 ON REPLACE v4
        WHEN TRUE DO SEARCH path3;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    BatchReplaceDataVertexForPath01(conn, stmt, "v1", 1);
    BatchReplaceDataVertexForPath01(conn, stmt, "v2", 2);
    BatchReplaceDataVertexForPath01(conn, stmt, "v3", 1);
    BatchReplaceDataVertexForPath01(conn, stmt, "v4", 1);
    

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM01 = 1;
    const uint32_t EXPECT_PATH_NUM02 = 2;
    const uint32_t EXPECT_PATH_NUM03 = 2;
    while ((pathNum01 != EXPECT_PATH_NUM01) || (pathNum02 != EXPECT_PATH_NUM02) || (pathNum03 != EXPECT_PATH_NUM03)) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum01 = %d, pathNum02 = %d, pathNum03 = %d\n", pathNum01,pathNum02,pathNum03);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum01 = %d  pathNum02 = %d pathNum03 = %d\n", pathNum01, pathNum02, pathNum03);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM01, pathNum01);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM02, pathNum02);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM03, pathNum03);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t4_1;
        DROP TRIGGER t4_2;
        DROP TRIGGER t4_3;
        DROP TRIGGER t4_4;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser2OnPath2;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser3OnPath3;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例005：一个Path只被一个订阅者通过一个订阅通道订阅，2个同步连接发送使生成2个path实例的Batch DML
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、1个订阅者、1个订阅关系
 * 3. 定义2个同步连接发送Batch DML
 * 4. 一个同步连接在v1插入(1,1),(2,2)
 * 5. 另一个同步连接在v2插入(1,1),(2,2)
 * 6. 接收pathNum,应为2
 */
TEST_F(PathSearch,GQL_010_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

     // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
     int ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

     // 发送订阅请求
    const char *subGQL = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    SnUserDataT *user_data = NULL;
    uint32_t pathNum = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum};

    ret = GmcSubscribeComplexPath(stmt, subGQL, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t5_1 ON REPLACE v1
        WHEN TRUE DO SEARCH path1;
        CREATE TRIGGER t5_2 ON REPLACE v2
        WHEN TRUE DO SEARCH path1;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));

    ReplaceTablesParallely();

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM = 2;
    while (pathNum != EXPECT_PATH_NUM) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            AW_MACRO_EXPECT_EQ_INT(1, 0);
           break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum = %d\n", pathNum);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM, pathNum);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t5_1;
        DROP TRIGGER t5_2;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例006：一个Path只被多个订阅者通过一个订阅通道订阅，多个同步连接发送使生成2个path实例的Batch DML
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、2个订阅者、2个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. 一个同步连接在v1插入(1,1),(2,2)
 * 5. 另一个同步连接在v2插入(1,1),(2,2)
 * 6. 接收pathNum01应为3->2，接收pathNum02应为4
 */ 
TEST_F(PathSearch,GQL_010_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
    int ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 用户1发送订阅请求subByUser1OnPath1
    const char *subGQL01 = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum01 = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum01};

    ret = GmcSubscribeComplexPath(stmt, subGQL01, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 用户2发送订阅请求subByUser2OnPath1
    const char *subGQL02 = R"(CREATE SUBSCRIPTION subByUser2OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum02 = 0;
    GmcSubUdfT user02 = {.userCb = PathUserCb, .userData = &pathNum02};

    ret = GmcSubscribeComplexPath(stmt, subGQL02, &user02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t6_1 ON REPLACE v1
        WHEN TRUE DO SEARCH path1;
        CREATE TRIGGER t6_2 ON REPLACE v2
        WHEN TRUE DO SEARCH path1;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));

    //两个同步连接插入数据
    ReplaceTablesParallely();

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM01 = 2;
    const uint32_t EXPECT_PATH_NUM02 = 2;
    while ((pathNum01 != EXPECT_PATH_NUM01) || (pathNum02 != EXPECT_PATH_NUM02)) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum01 = %d, pathNum2 = %d\n", pathNum01, pathNum02);
            AW_MACRO_EXPECT_EQ_INT(1, 0);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum01 = %d  pathNum02 = %d\n", pathNum01, pathNum02);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM01, pathNum01);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM02, pathNum02);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t6_1;
        DROP TRIGGER t6_2;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //用户1发送订阅请求subByUser1OnPath1
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //用户1发送订阅请求subByUser2OnPath1
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser2OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例007：多个Path只被一个订阅者通过一个订阅通道订阅，多个同步连接发送使生成path实例的Batch DML
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、1个订阅者、3个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. 一个同步连接在v1插入(1,1), v2插入(1,1),(2,2)  
 * 5. 另一个同步连接在 v3插入(1,1),  v4插入(1,1)
 * 6. 接收pathNum, 应为3/4/5
 */ 
TEST_F(PathSearch,GQL_010_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

     // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
     int ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

     // 分别发送3个订阅请求：分别订阅path1、path2、path3
    const char *subGQL01 = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum};
    ret = GmcSubscribeComplexPath(stmt, subGQL01, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subGQL02 = R"(CREATE SUBSCRIPTION subByUser1OnPath2 ON PATH path2 BY CHANNEL channel_01;)";
    ret = GmcSubscribeComplexPath(stmt, subGQL02, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *subGQL03 = R"(CREATE SUBSCRIPTION subByUser1OnPath3 ON PATH path3 BY CHANNEL channel_01;)";
    ret = GmcSubscribeComplexPath(stmt, subGQL03, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t7_1 ON REPLACE v1
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t7_2 ON REPLACE v2
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t7_3 ON REPLACE v3
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t7_4 ON REPLACE v4
        WHEN TRUE DO SEARCH path3;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));


    //两个同步连接插入数据
    ReplaceTablesParallely01();

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM01 = 3;
    const uint32_t EXPECT_PATH_NUM02 = 4;
    const uint32_t EXPECT_PATH_NUM03 = 5;
    while (pathNum != EXPECT_PATH_NUM01 && pathNum != EXPECT_PATH_NUM02 && pathNum != EXPECT_PATH_NUM03) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            AW_MACRO_EXPECT_EQ_INT(1, 0);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum = %d\n", pathNum);
    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t7_1;
        DROP TRIGGER t7_2;
        DROP TRIGGER t7_3;
        DROP TRIGGER t7_4;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath2;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath3;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例008：多个Path只被多个订阅者通过一个订阅通道订阅，多个同步连接发送使生成5个path实例的Batch DML
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、3个订阅者、3个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. 一个同步连接在v1插入(1,1), v2插入(1,1),(2,2)  
 * 5. 另一个同步连接在 v3插入(1,1),  v4插入(1,1)
 * 6. 接收pathNum,应分别为1,2,3; 1,2,4; 1,3,5;
 */ 
TEST_F(PathSearch,GQL_010_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

     // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
     int ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 分别发送3个订阅请求：用户1订阅path1、用户2订阅path2、用户3订阅path3
    // 用户1发送订阅请求subByUser1OnPath1
    const char *subGQL01 = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum01 = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum01};

    ret = GmcSubscribeComplexPath(stmt, subGQL01, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 用户2发送订阅请求subByUser2OnPath2
    const char *subGQL02 = R"(CREATE SUBSCRIPTION subByUser2OnPath2 ON PATH path2 BY CHANNEL channel_01;)";
    uint32_t pathNum02 = 0;
    GmcSubUdfT user02 = {.userCb = PathUserCb, .userData = &pathNum02};

    ret = GmcSubscribeComplexPath(stmt, subGQL02, &user02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 用户2发送订阅请求subByUser3OnPath3
    const char *subGQL03 = R"(CREATE SUBSCRIPTION subByUser3OnPath3 ON PATH path3 BY CHANNEL channel_01;)";
    uint32_t pathNum03 = 0;
    GmcSubUdfT user03 = {.userCb = PathUserCb, .userData = &pathNum03};

    ret = GmcSubscribeComplexPath(stmt, subGQL03, &user03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t8_1 ON REPLACE v1
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t8_2 ON REPLACE v2
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t8_3 ON REPLACE v3
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t8_4 ON REPLACE v4
        WHEN TRUE DO SEARCH path3;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));

    //两个同步连接插入数据
    ReplaceTablesParallely01();
    

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    while (!(pathNum01 >= 1 && pathNum02 >= 1 && pathNum03 >= 1)) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 10*1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum01 = %d, pathNum02 = %d, pathNum03 = %d\n", pathNum01,pathNum02,pathNum03);
            AW_MACRO_EXPECT_EQ_INT(1, 0);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum01 = %d  pathNum02 = %d pathNum03 = %d\n", pathNum01, pathNum02, pathNum03);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t8_1;
        DROP TRIGGER t8_2;
        DROP TRIGGER t8_3;
        DROP TRIGGER t8_4;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser2OnPath2;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser3OnPath3;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例009：多个Path只被多个订阅者通过多个订阅通道订阅，多个同步连接发送使生成3个path实例的Batch DML
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义2个订阅channel、3个订阅者、3个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. 一个同步连接在v1插入(1,1), v2插入(1,1),(2,2)  
 * 5. 另一个同步连接在 v3插入(1,1),  v4插入(1,1)
 * 6. 接收pathNum,应分别为1,1,2; 1,1,3; 1,2,4;
 */ 
TEST_F(PathSearch,GQL_010_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

     // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
     int ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    int chanRingLen2 = 256;
    const char *subConnName02 = (const char *)"channel_02";
    GmcConnT *testSubConn02 = NULL;
    GmcStmtT *stmt_sub02 = NULL;
    ret = testSubConnect(&testSubConn02, &stmt_sub02, 1, g_epoll_reg_info, subConnName02, &chanRingLen2);
    TEST_EXPECT_INT32(GMERR_OK, ret);

    // 分别发送3个订阅请求：用户1订阅path1、用户2订阅path2、用户3订阅path3
    // 用户1发送订阅请求subByUser1OnPath1
    const char *subGQL01 = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum01 = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum01};

    ret = GmcSubscribeComplexPath(stmt, subGQL01, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 用户2发送订阅请求subByUser2OnPath2
    const char *subGQL02 = R"(CREATE SUBSCRIPTION subByUser2OnPath2 ON PATH path2 BY CHANNEL channel_02;)";
    uint32_t pathNum02 = 0;
    GmcSubUdfT user02 = {.userCb = PathUserCb, .userData = &pathNum02};

    ret = GmcSubscribeComplexPath(stmt, subGQL02, &user02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 用户2发送订阅请求subByUser3OnPath3
    const char *subGQL03 = R"(CREATE SUBSCRIPTION subByUser3OnPath3 ON PATH path3 BY CHANNEL channel_02;)";
    uint32_t pathNum03 = 0;
    GmcSubUdfT user03 = {.userCb = PathUserCb, .userData = &pathNum03};

    ret = GmcSubscribeComplexPath(stmt, subGQL03, &user03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t9_1 ON REPLACE v1
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t9_2 ON REPLACE v2
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t9_3 ON REPLACE v3
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t9_4 ON REPLACE v4
        WHEN TRUE DO SEARCH path3;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));

    //两个同步连接插入数据
    ReplaceTablesParallely01();
    

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    // 如果V2在V4后插入，pathNum03=1；否则，pathNum03=2
        while (!(pathNum01 >= 1 && pathNum02 >= 1 && pathNum03 >= 1)) {
        DbUsleep(1000);
        sleepCnt++;
        // wait 10s
        if (sleepCnt == 10*1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum01 = %d, pathNum02 = %d, pathNum03 = %d\n", pathNum01,pathNum02,pathNum03);
            AW_MACRO_EXPECT_EQ_INT(1, 0);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum01 = %d  pathNum02 = %d pathNum03 = %d\n", pathNum01, pathNum02, pathNum03);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t9_2;
        DROP TRIGGER t9_3;
        DROP TRIGGER t9_4;
        DROP TRIGGER t9_1;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser2OnPath2;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser3OnPath3;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例010：多个Path被多个订阅者通过一个订阅通道订阅，1个同步连接发送使无法生成path实例的Batch DML
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、3个订阅者、3个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. v2插入(1,1),(2,2)
 * 5. v3插入(1,1)
 * 6. v4插入(1,1)
 * 7. 接收pathNum,应分别为0,0,0 
 */ 
TEST_F(PathSearch,GQL_010_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

     // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
     int ret = testSubConnect(&testSubConn, &stmt_sub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

     // 分别发送3个订阅请求：用户1订阅path1、用户2订阅path2、用户3订阅path3
    // 用户1发送订阅请求subByUser1OnPath1
    const char *subGQL01 = R"(CREATE SUBSCRIPTION subByUser1OnPath1 ON PATH path1 BY CHANNEL channel_01;)";
    uint32_t pathNum01 = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum01};

    ret = GmcSubscribeComplexPath(stmt, subGQL01, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 用户2发送订阅请求subByUser2OnPath2
    const char *subGQL02 = R"(CREATE SUBSCRIPTION subByUser2OnPath2 ON PATH path2 BY CHANNEL channel_01;)";
    uint32_t pathNum02 = 0;
    GmcSubUdfT user02 = {.userCb = PathUserCb, .userData = &pathNum02};

    ret = GmcSubscribeComplexPath(stmt, subGQL02, &user02);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 用户2发送订阅请求subByUser3OnPath3
    const char *subGQL03 = R"(CREATE SUBSCRIPTION subByUser3OnPath3 ON PATH path3 BY CHANNEL channel_01;)";
    uint32_t pathNum03 = 0;
    GmcSubUdfT user03 = {.userCb = PathUserCb, .userData = &pathNum03};

    ret = GmcSubscribeComplexPath(stmt, subGQL03, &user03);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t10_1 ON REPLACE v1
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t10_2 ON REPLACE v2
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t10_3 ON REPLACE v3
        WHEN TRUE DO SEARCH path2
        WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER t10_4 ON REPLACE v4
        WHEN TRUE DO SEARCH path3;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));
    BatchReplaceDataVertexForPath01(conn, stmt, "v2", 2);
    BatchReplaceDataVertexForPath01(conn, stmt, "v3", 1);
    BatchReplaceDataVertexForPath01(conn, stmt, "v4", 1);
    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM01 = 0;
    const uint32_t EXPECT_PATH_NUM02 = 0;
    const uint32_t EXPECT_PATH_NUM03 = 0;
    while ((pathNum01 != EXPECT_PATH_NUM01) || (pathNum02 != EXPECT_PATH_NUM02) || (pathNum03 != EXPECT_PATH_NUM03)) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum01 = %d, pathNum02 = %d, pathNum03 = %d\n", pathNum01,pathNum02,pathNum03);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathNum01 = %d  pathNum02 = %d pathNum03 = %d\n", pathNum01, pathNum02, pathNum03);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM01, pathNum01);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM02, pathNum02);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM03, pathNum03);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t10_1;
        DROP TRIGGER t10_2;
        DROP TRIGGER t10_3;
        DROP TRIGGER t10_4;

    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath1;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser2OnPath2;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser3OnPath3;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*
 * 用例011：一个Path只被一个订阅者通过一个订阅通道订阅，1个同步连接发送使生成2个path实例的Batch DML
 * 步骤：
 * 1. 定义2个点表、7条边、3条path pattern
 * 2. 定义1个订阅channel、1个订阅者、1个订阅关系
 * 3. 定义1个同步连接发送Batch DML
 * 4. v1插入(1,1),(2,2)
 * 5. v2插入(1,1),(2,2)
 * 6. 接收pathNum,应为2
 */
TEST_F(PathSearch,GQL_010_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 创建一个订阅连接
    int chanRingLen = 256;
    const char *subConnName = (const char *)"channel_01";
    GmcConnT *testSubConn = NULL;
    GmcStmtT *stmt_sub = NULL;
    int ret = testSubConnect(&testSubConn, &stmt_sub, 2, g_epoll_reg_info, subConnName, &chanRingLen);
    TEST_EXPECT_INT32(GMERR_OK, ret);

     // 发送订阅请求
    const char *subGQL = R"(CREATE SUBSCRIPTION subByUser1OnPath2 ON PATH path2 BY CHANNEL channel_01;)";
    uint32_t pathNum = 0;
    GmcSubUdfT user01 = {.userCb = PathUserCb, .userData = &pathNum};

    ret = GmcSubscribeComplexPath(stmt, subGQL, &user01);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *createTriggerRule = R"(
        CREATE TRIGGER t11_1 ON REPLACE v1 WHEN TRUE DO SEARCH path2;
        CREATE TRIGGER t11_2 ON REPLACE v2 WHEN TRUE DO SEARCH path2;
    )";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createTriggerRule));

    BatchReplaceDataVertexForPath01(conn, stmt, "v1", 2);
    BatchReplaceDataVertexForPath01(conn, stmt, "v2", 2);

    // 检查返回的pathName是否符合预期
    uint32_t sleepCnt = 0;
    const uint32_t EXPECT_PATH_NUM = 2;
    while (pathNum != EXPECT_PATH_NUM) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 1000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            break;
        }
    }
    AW_FUN_Log(LOG_STEP, "Path received: pathName = %d\n", pathNum);
    AW_MACRO_EXPECT_EQ_INT(EXPECT_PATH_NUM, pathNum);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER t11_1;
        DROP TRIGGER t11_2;

    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除订阅
    ret = GmcUnSubscribeComplexPath(stmt, "DROP SUBSCRIPTION subByUser1OnPath2;");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 释放订阅连接
    ret = testSubDisConnect(testSubConn, stmt_sub);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
