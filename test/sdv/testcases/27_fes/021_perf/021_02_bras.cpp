/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: st file for BRAS scenario
 * Author: GQL
 * Create: 2023-10-09
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include <pthread.h>

#include "gtest/gtest.h"
#include "gmc_errno.h"
#include "gmc_gql.h"

#include "t_rd_adapt.h"
#include "t_rd_assert.h"
#include "t_rd_sn.h"
#include "t_rd_common.h"
#include "adpt_sleep.h"

#include "fes_perf_utils.h"
#include "../001_vertexlabel/vertex_util.h"

const uint32_t BUF_SIZE_TWO = 2;
const uint32_t BUF_SIZE_SIX = 6;
const uint32_t WAIT_TIME = 1000;  // ms
static uint64_t cpuFrequency = 2400000000;
#define FOR_TEST 1
#if FOR_TEST
static uint32_t dmlNum4OneTable = 64000;
static uint32_t ip4forwardInitSizeAsync = dmlNum4OneTable * 5;

static uint32_t syncReplaceNum = 1000;
static uint32_t syncBatchCnt = dmlNum4OneTable / syncReplaceNum;

static uint32_t replaceNum = 500;
static uint32_t batchCnt = (int)(dmlNum4OneTable / replaceNum);
#else
static uint32_t dmlNum4OneTable = 64000;
static uint32_t ip4forwardInitSizeAsync = dmlNum4OneTable * 5;
uint64_t cpuFrequency2 = 2400000000;

static uint32_t syncReplaceNum = 800;
static uint32_t syncBatchCnt = dmlNum4OneTable / syncReplaceNum;

static uint32_t replaceNum = 200;
static uint32_t batchCnt = (int)(dmlNum4OneTable / replaceNum);
#endif

#define PERF_TIME_START()              \
    struct timeval timeBegin, timeEnd; \
    uint64_t sec;                      \
    uint64_t usec;                     \
    uint64_t allTime;                  \
    float ops;                         \
    uint32_t cycles;                   \
    gettimeofday(&timeBegin, NULL);

#define PERF_TIME_END()                                                  \
    gettimeofday(&timeEnd, NULL);                                        \
    sec = timeEnd.tv_sec - timeBegin.tv_sec;                             \
    usec = timeEnd.tv_usec - timeBegin.tv_usec;                          \
    allTime = sec * 1000000 + usec;                                      \
    ops = (float)dmlNum4OneTable / ((float)allTime / 1000000);           \
    cycles = (uint32_t)(cpuFrequency / ops);                             \
    printf("Workload execution times = %u . ", ip4forwardInitSizeAsync); \
    printf("Execution time = %.3f (s), ops = %.3f , cycles = %u .\n", (float)allTime / 1000000, ops, cycles);

class PathBras : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        //初始化配置文件
        InitCfg4BrasPerf();
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
       
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testEnvClean();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh  recover");
        GmcDetachAllShmSeg();
    }

public:
    virtual void SetUp(){};
    virtual void TearDown(){};
};

static void CreatePaths(GmcStmtT *stmt)
{
    char *data = ReadFile("./bras/tables.txt");
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, data));
    free(data);
    data = ReadFile("./bras/edges.txt");
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, data));
    free(data);
    data = ReadFile("./bras/paths.txt");
    ASSERT_EQ(GMERR_OK, GmcExecGql(stmt, data));
    free(data);
}

static void PathUserCb(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    if (dataType == PATH_TYPE) {
        // 解析第一个4字节数目
        uint32_t pathNum = 0;
        Bytes2Uint32(data, &pathNum);
        *(uint32_t *)userData += pathNum;
    }
}

static Status CheckPrepareData(GmcStmtT *stmt, const char *vertexLabelName, uint32_t dmlNum)
{
    Status ret = GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        printf("exectue error.\n");
        return ret;
    }

    bool isFinish = true;
    uint cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            printf("fetch error.\n");
            return ret;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }
    printf("table %s replaced %u data\n", vertexLabelName, cnt);
    if (cnt != dmlNum) {
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

static void prepareDataOfT31(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T31", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table31T obj = {0};
    uint32_t ctrlFieldOfEnIfPhyType = 15;
    uint32_t valT31 = 0;

    for (uint32_t i = 0; i < syncBatchCnt; i++) {  // 64
        ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
        ASSERT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < syncReplaceNum; j++) {  // 1000
            obj.uiIfIndex = valT31;                      // primary key
            obj.enIfPhyType = ctrlFieldOfEnIfPhyType;
            obj.uiIfSubPhyType = valT31;
            obj.uiIfLinkType = valT31;
            obj.uiIfVr = valT31;
            obj.uiIfVrf = valT31;
            obj.uiPortgroup = valT31;
            obj.uiIfGroupID = valT31;
            obj.uiIfDf = valT31;
            obj.uiIfIsSubif = valT31;
            obj.uiIfStatiEnable = valT31;
            obj.uiIfEncapType = valT31;
            obj.uiNodeGrpID = valT31;
            obj.hSrcPid = valT31;
            obj.uiVerNo = valT31;
            obj.uiFVrf = valT31;
            obj.usflag = valT31;
            obj.usflag11 = valT31;

            ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            valT31++;
            ret = GmcBatchAddDML(batch, stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ASSERT_EQ(GMERR_OK, CheckPrepareData(stmt, "T31", dmlNum4OneTable));

    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT279(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T279", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table279T obj = {0};
    uint32_t valT279 = 0;

    for (uint32_t i = 0; i < syncBatchCnt; i++) {
        ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
        ASSERT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < syncReplaceNum; j++) {
            obj.uiIfIndex = valT279;  // primary
            obj.usflag = valT279;
            obj.usRes = valT279;
            obj.uiIfPhyType = valT279;
            obj.uiIfSubPhyType = valT279;
            obj.uiIfLinkType = valT279;
            obj.uiIfEncaptype = valT279;
            (void)sprintf_s(obj.unfifinfo, 12, "%u", valT279);
            obj.uiIfUcmpEn = valT279;
            (void)sprintf_s(obj.ullBandwidth, 8, "%u", valT279);
            obj.hSrcPid = valT279;
            obj.uiVerNo = valT279;
            obj.usFlagPlus = valT279;
            obj.usResPlus = valT279;

            ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            valT279++;
            ret = GmcBatchAddDML(batch, stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ASSERT_EQ(GMERR_OK, CheckPrepareData(stmt, "T279", dmlNum4OneTable));

    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT51(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T51", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    Table51T obj = {0};
    uint32_t valT51 = 0;

    for (uint32_t i = 0; i < syncBatchCnt; i++) {
        ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
        ASSERT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < syncReplaceNum; j++) {
            obj.uiVrIndex = valT51;   // primary
            obj.uiVrfIndex = valT51;  // primary
            obj.uiVrVrfIndex = valT51;
            obj.hSrcPid = valT51;
            obj.uiVerNo = valT51;
            obj.hSrcController = valT51;
            ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            valT51++;
            ret = GmcBatchAddDML(batch, stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ASSERT_EQ(GMERR_OK, CheckPrepareData(stmt, "T51", dmlNum4OneTable));

    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT301(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T301", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    Table301T obj = {0};
    uint32_t ctrlFieldOfUsTB = 251;
    uint32_t valT301 = 0;

    for (uint32_t i = 0; i < syncBatchCnt; i++) {
        ASSERT_EQ(GMERR_OK, GmcBatchPrepare(stmt->conn, &batchOption, &batch));
        for (uint32_t j = 0; j < syncReplaceNum; j++) {
            obj.uiIfIndex = valT301;
            obj.usTB = ctrlFieldOfUsTB;  // primary
            obj.usTP = valT301;          // primary
            obj.uiPhyIfIndex = valT301;  // primary
            obj.hSrcPid = valT301;
            obj.uiVerNo = valT301;
            ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            valT301++;
            ret = GmcBatchAddDML(batch, stmt);
            ASSERT_EQ(GMERR_OK, ret);
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ASSERT_EQ(GMERR_OK, CheckPrepareData(stmt, "T301", dmlNum4OneTable));

    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT803(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T803", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table803T obj = {0};
    uint32_t ctrlFieldOfUiMatchType = 11;
    uint32_t specialValue = 0;
    uint32_t valT803 = 0;

    for (uint32_t i = 0; i < syncBatchCnt; i++) {
        ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
        ASSERT_EQ(GMERR_OK, ret);
        for (uint32_t j = 0; j < syncReplaceNum; j++) {
            obj.uiFlowIndex = valT803;  // primary
            obj.uiIfIndex = valT803;
            obj.uiVrId = valT803;
            obj.uiMatchType = ctrlFieldOfUiMatchType;
            obj.ucIsLocalSwitch = specialValue;
            obj.ucIsRtProtocol = specialValue;
            obj.ucIsDynamic = specialValue;
            obj.ucIsSymmetric = specialValue;
            obj.uiSubIfType = valT803;
            obj.ucIsUserMode = specialValue;
            obj.ucIsTransMode = specialValue;
            (void)memcpy_s(obj.aucReserved, 2, "ok", 2);
            obj.uiToken = valT803;
            obj.uiNodeID = valT803;
            obj.hSrcPid = valT803;
            obj.uiVerNo = valT803;
            ASSERT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            ASSERT_EQ(GMERR_OK, ret);
            valT803++;
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ASSERT_EQ(GMERR_OK, CheckPrepareData(stmt, "T803", dmlNum4OneTable));

    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT7(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T7", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table7T obj = {0};
    uint32_t valT7 = 0;
    uint32_t specialValue = 0;
    uint32_t ctrlFieldOfUiPathFlags = 524288;
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmt->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT7;
            obj.uiVrIndex = valT7;   // primary
            obj.uiVrfIndex = valT7;  // primary
            obj.uiVerNo = valT7;
            obj.uiFVrfIndex = valT7;
            obj.uiDestAddr = valT7;        // primary
            obj.ucMaskLen = specialValue;  // primary
            obj.ucIIDGFlag = specialValue;
            obj.usRouteFlags = valT7;
            obj.uiPathFlags = ctrlFieldOfUiPathFlags;
            obj.uiIIDindex = valT7;
            obj.uiPrimaryLabel = valT7;
            obj.uiAttributeId = valT7;
            obj.usQosid = valT7;
            obj.usRes = valT7;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
            valT7++;
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT2438(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2438", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table2438T obj = {0};
    uint32_t valT2438 = 0;
    uint32_t specialValue = 0;
    uint8_t specialData = 1;
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmt->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.uiSessionID = valT2438;  // primary
            obj.uiFamilyID = specialData;
            obj.uiMagic = valT2438;  // primary
            obj.uiVersion = valT2438;
            obj.uiIfIndex = valT2438;
            obj.uiOutIfindex = valT2438;
            obj.uiMainIfindex = valT2438;
            obj.usPeVlanId = valT2438;
            obj.usCeVlanId = valT2438;
            obj.ucDirection = specialValue;  // primary
            obj.ucFamilyResType = specialValue;
            obj.ucResBehavior = specialValue;
            obj.ucIsFamilyMem = specialValue;
            obj.ucIsVcpeflag = specialValue;
            obj.ucLayerExclude = specialValue;
            obj.ucPWifFlag = specialValue;
            obj.ucQoSLabelFlag = specialValue;
            obj.uiUCMPid = valT2438;
            obj.uiTrunkid = valT2438;
            obj.ucVLinkMode = specialValue;
            obj.ucScheduleType = specialValue;
            obj.usTunnelId = valT2438;
            obj.uiChannelid = valT2438;
            obj.hSrcPid = valT2438;
            obj.uiVerNo = valT2438;
            obj.ucResNoChange = specialValue;
            obj.ucOutifChFlag = specialValue;
            obj.ucBasUserType = specialValue;
            obj.ucIfType = specialValue;
            obj.ucNpId = specialValue;
            obj.uclaclns = specialValue;
            obj.ucPriCarEnable = specialValue;
            obj.ucAfterCarFlag = specialValue;
            obj.ucEtmFlag = specialValue;
            obj.ucOldBehavior = specialValue;
            obj.usResv2 = valT2438;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
            valT2438++;
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT2172(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2172", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table2172T obj = {0};
    uint32_t valT2172 = 0;
    uint32_t specialValue = 0;
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmt->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT2172;
            obj.uiVerNo = valT2172;
            obj.uiFlowIndex = valT2172;
            obj.uiIfIndex = valT2172;  // primary
            obj.uiSegIndex = valT2172;
            obj.usPeVlan = valT2172;  // primary
            obj.usCeVlan = valT2172;  // primary
            obj.uiVrID = valT2172;    // primary
            obj.uiMainIfIndex = valT2172;
            obj.ucIfBrasFlag = specialValue;
            obj.ucReserved = specialValue;
            obj.ucReserved1 = specialValue;
            obj.ucReserved2 = specialValue;
            obj.uiNodeGrpID = valT2172;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
            valT2172++;
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT2011(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2011", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table2011T obj = {0};
    uint32_t valT2011 = 0;
    uint32_t specialValue = 0;
    uint32_t ctrlFieldOfUcUserIpType = 1;
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmt->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT2011;
            obj.uiVerNo = valT2011;
            obj.uiUserCid = valT2011;  // primary
            obj.uiUserMagic = valT2011;
            obj.uiIfIndex = valT2011;
            obj.uiMainIfIndex = valT2011;
            obj.uiFamilyid = valT2011;
            obj.uiUserGroupId = valT2011;
            obj.ucUserIpType = ctrlFieldOfUcUserIpType;
            obj.ucAccessType = specialValue;
            obj.ucAccessMode = specialValue;
            obj.ucPvcEncap = specialValue;
            obj.ucIfCOAModify = specialValue;
            obj.ucEncapeType = specialValue;
            obj.ucTriggerType = specialValue;
            obj.ucAccountType = specialValue;
            obj.uc4CAR8CARFlag = specialValue;
            obj.ucRuiRoleFlag = specialValue;
            obj.ucDaaSeperFlag = specialValue;
            obj.ucRuiFlag = specialValue;
            obj.usInnerVlan = valT2011;
            obj.usOuterVlan = valT2011;
            obj.usVpi = valT2011;
            obj.usVci = valT2011;
            obj.ucInnerVlanPri = specialValue;
            obj.ucOuterVlanPri = specialValue;
            (void)memcpy_s(obj.aucRuiSrcMac, BUF_SIZE_TWO, "ok", 2);
            (void)sprintf_s(obj.aucMAC, BUF_SIZE_SIX, "%u", valT2011);
            (void)sprintf_s(obj.aucPeerMAC, BUF_SIZE_TWO, "%u", valT2011);
            obj.uiIfGroup = valT2011;
            obj.usDetectTimes = valT2011;
            obj.usDetectInter = valT2011;
            obj.uiVrID = valT2011;
            obj.ucKeepOnline = specialValue;
            obj.ucUpnpEn = specialValue;
            obj.usDomainId = valT2011;
            obj.ucForceFlag = specialValue;
            obj.ucMssEnable = specialValue;
            obj.usMssValue = valT2011;
            obj.uiSn = valT2011;
            obj.ucL3CAR = specialValue;
            obj.ucStatVlanEx = specialValue;
            obj.uc8021pIf = specialValue;
            obj.ucIfTerminate = specialValue;
            obj.ucBlockFlow = specialValue;
            obj.ucInSesGrpExcl = specialValue;
            obj.ucOutSesGrpExcl = specialValue;
            obj.ucMacSessionEn = specialValue;
            obj.uiUcVrfID = valT2011;
            obj.uiOutFamilyid = valT2011;
            obj.uiResv = valT2011;
            obj.uiLLUserCid = valT2011;
            obj.uiLLUserMagic = valT2011;
            obj.uiObjectID = valT2011;
            obj.hBtrcPid = valT2011;
            obj.ucVpnAccFlag = specialValue;
            obj.ucReportSqFlg = specialValue;
            obj.ucNatHashIndex = specialValue;
            obj.ucFeInfo = specialValue;
            obj.ucSoftGreFlag = specialValue;
            obj.ucOperType = specialValue;
            obj.usPathFlag = valT2011;
            obj.ucFlags = specialValue;
            obj.hostcarflag = specialValue;
            (void)memcpy_s(obj.aucResv, BUF_SIZE_TWO, "ok", 2);

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
            valT2011++;
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT2009(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2009", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table2009T obj = {0};
    uint32_t valT2009 = 0;
    uint32_t specialValue = 0;
    uint32_t ctrlFieldOfUiRingIndex = DB_INVALID_UINT32;  // 0xFFFFFFFF
    uint32_t ctrlFieldOfUcUserIpType = 1;
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmt->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT2009;
            obj.uiVerNo = valT2009;
            obj.uiUserCid = valT2009;    // primary
            obj.uiUserMagic = valT2009;  // primary
            obj.uiIfIndex = valT2009;
            obj.uiRingIndex = ctrlFieldOfUiRingIndex;  // 0xFFFFFFFF
            obj.uiToken = valT2009;
            obj.ucUserIpType = ctrlFieldOfUcUserIpType;  // primary 1
            obj.ucUserl2tpType = specialValue;
            obj.usUserRtType = valT2009;
            obj.uiNodeGrpID = valT2009;
            obj.hUcmPid = valT2009;
            obj.uiUserGrId = valT2009;
            obj.uiPwLogicId = valT2009;
            obj.ucBA_Tid = specialValue;
            obj.ucLnsInBA_Tid = specialValue;
            obj.ucBA_Enable = specialValue;
            obj.ucPHB_TID = specialValue;
            obj.ucUserPriority = specialValue;
            obj.ucDownPriority = specialValue;
            obj.ucSetDscp = specialValue;
            obj.ucSetV6Dscp = specialValue;
            obj.ucSetExp = specialValue;
            obj.ucSetInnerVlan = specialValue;
            obj.ucSetOutVlan = specialValue;
            obj.ucLnsBaEn = specialValue;
            obj.ucL2UserPri = specialValue;
            obj.BrasUnicastFlag = specialValue;
            obj.L2NatEnable = specialValue;
            obj.deiEnable = specialValue;
            obj.uiNodeGrpID2 = valT2009;
            obj.ucRuiFlag = specialValue;
            obj.ucRbsId = specialValue;
            obj.usRbpId = valT2009;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
            valT2009++;
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT2434(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2434", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table2434T obj = {0};
    uint32_t valT2434 = 0;
    uint32_t specialValue = 0;
    uint8_t specialData = 1;
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmt->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.uiKeyID = valT2434;  // primary
            obj.uiMagic = valT2434;  // primary
            obj.uiCir = valT2434;
            obj.uiPir = valT2434;
            obj.uiCbs = valT2434;
            obj.uiPbs = valT2434;
            obj.ucGreenAction = specialValue;
            obj.ucYellowAction = specialValue;
            obj.ucRedAction = specialValue;
            obj.ucColorAware = specialValue;
            obj.ucGreenSerCls = specialValue;
            obj.ucYellowSerCls = specialValue;
            obj.ucRedSerCls = specialValue;
            obj.ucGreenColor = specialValue;
            obj.ucYellowColor = specialValue;
            obj.ucRedColor = specialValue;
            obj.ucDirection = specialData;  // primary 0
            obj.ucLayerExclude = specialValue;
            obj.ucLayerType = specialValue;
            obj.ucAutoOverHead = specialValue;
            (void)memcpy_s(obj.aucReserved, BUF_SIZE_TWO, "ok", 2);
            obj.hSrcPid = valT2434;
            obj.uiVerNo = valT2434;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
            valT2434++;
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT2433(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2433", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table2433T obj = {0};
    uint32_t valT2433 = 0;
    uint32_t specialValue = 0;
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmt->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.uiKeyID = valT2433;  // primary
            obj.uiMagic = valT2433;  // primary
            obj.uiCir = valT2433;
            obj.uiCbs = valT2433;
            obj.uiPir = valT2433;
            obj.uiPbs = valT2433;
            obj.uiServiceTempId = valT2433;
            obj.uiFlowMappingID = valT2433;
            obj.ucDirection = specialValue;  // primary 0
            obj.ucWeight = specialValue;
            obj.ucStBaseAdjPkt = specialValue;
            obj.cLinkOverHead = specialValue;
            obj.ucLinkMode = specialValue;
            obj.ucAutoOverHead = specialValue;
            obj.ucUserQueueType = specialValue;
            obj.ucOverrideEn = specialValue;
            obj.ucTmSchdEn = specialValue;
            obj.ucGQIdfType = specialValue;
            obj.usGQGroupID = valT2433;
            obj.hSrcPid = valT2433;
            obj.uiVerNo = valT2433;
            obj.uiGQTempID = valT2433;
            obj.uiQueueMapID = valT2433;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
            valT2433++;
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT2214(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2214", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table2214T obj = {0};
    uint32_t valT2214 = 0;
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmt->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT2214;
            obj.uiVerNo = valT2214;
            obj.uiUserID = valT2214;
            obj.uiUserMagic = valT2214;
            obj.uiVrID = valT2214;  // primary
            obj.hUcmPid = valT2214;
            obj.uiIfIndex = valT2214;  // primary
            obj.usPeVlan = valT2214;   // primary
            obj.usCeVlan = valT2214;   // primary
            obj.uiNodeGrpID = valT2214;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
            valT2214++;
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void prepareDataOfT2012(GmcStmtT *stmt)
{
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, "T2012", GMC_OPERATION_INSERT));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));

    Table2012T obj = {0};
    uint32_t valT2012 = 0;
    uint32_t specialValue = 0;  // uint8字段
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmt->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT2012;
            obj.uiVerNo = valT2012;
            obj.uiUserCid = valT2012;  // primary
            obj.uiUserMagic = valT2012;
            obj.uiIpAddr = valT2012;
            obj.uiGateWayIP = valT2012;
            obj.uiUserVrIndex = valT2012;
            obj.uiUserVrfIndex = valT2012;
            obj.uiUserFVrfIndex = valT2012;
            obj.uiCGNInLabel = valT2012;
            obj.uiMaskLen = valT2012;
            obj.ucFramedRoute = specialValue;
            obj.ucUrpfFlag = specialValue;
            obj.ucTTLBase = specialValue;
            obj.ucMssEnable = specialValue;
            obj.ucL2NATAware = specialValue;
            obj.ucForceFlag = specialValue;
            obj.ucEchoEnable = specialValue;
            obj.ucEchoCos = specialValue;
            obj.ucRes1 = specialValue;
            obj.ucRes2 = specialValue;
            obj.usMssValue = valT2012;
            obj.usMTU = valT2012;
            obj.usRes2 = valT2012;
            obj.uiInboundVrfid = valT2012;
            obj.uiVTIfIndex = valT2012;
            obj.uiIntfVrfIndex = valT2012;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmt, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
            valT2012++;
        }
        GmcBatchRetT batchRet = {};
        uint32_t totalNum = 0;
        uint32_t successNum = 0;
        ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
        ASSERT_EQ(GMERR_OK, GmcBatchDeparseRet(&batchRet, &totalNum, &successNum));
    }
    ret = GmcBatchDestroy(batch);
    ASSERT_EQ(GMERR_OK, ret);
}

static void TruncateTable(GmcStmtT *stmt, const char *tableName)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    GmcBatchRetT batchRet = {};
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, ret);
    // delete all
    ret = GmcBatchPrepare(stmt->conn, &batchOption, &batch);
    ASSERT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmt, tableName, GMC_OPERATION_DELETE));
    ASSERT_EQ(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    ASSERT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmt));
    batchRet = {};
    ASSERT_EQ(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    ASSERT_EQ(GMERR_OK, GmcBatchDestroy(batch));
}

static void PrepareData(GmcStmtT *stmt)
{
    prepareDataOfT31(stmt);
    prepareDataOfT51(stmt);
    prepareDataOfT279(stmt);
    prepareDataOfT301(stmt);
    prepareDataOfT803(stmt);
    // warm table
    prepareDataOfT7(stmt);
    prepareDataOfT2438(stmt);
    prepareDataOfT2172(stmt);
    prepareDataOfT2011(stmt);
    prepareDataOfT2009(stmt);
    prepareDataOfT2434(stmt);
    prepareDataOfT2433(stmt);
    prepareDataOfT2214(stmt);
    prepareDataOfT2012(stmt);
    TruncateTable(stmt, "T7");
    TruncateTable(stmt, "T2438");
    TruncateTable(stmt, "T2172");
    TruncateTable(stmt, "T2011");
    TruncateTable(stmt, "T2009");
    TruncateTable(stmt, "T2434");
    TruncateTable(stmt, "T2433");
    TruncateTable(stmt, "T2214");
    TruncateTable(stmt, "T2012");
    sleep(2);
}

typedef struct UserBatchData {
    uint32_t expectedTotalNum;
    uint32_t expectedSuccessNum;
    uint32_t received;
} UserBatchDataT;

static void BatchCb(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    ASSERT_EQ(GMERR_OK, status);
    ASSERT_NE(nullptr, userData);
    UserBatchDataT *data = (UserBatchDataT *)userData;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    auto ret = GmcBatchDeparseRet(batchRet, &totalNum, &successNum);
    ASSERT_EQ(ret, 0);
    ASSERT_EQ(data->expectedTotalNum, totalNum);
    ASSERT_EQ(data->expectedSuccessNum, successNum);
    data->received++;
}

static Status CheckAsyncReplaceData(const char *vertexLabelName, uint32_t dmlNum)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        ret = testGmcDisconnect(conn, stmt);
        return ret;
    }

    ret = GmcExecute(stmt);
    if (ret != GMERR_OK) {
        printf("exectue error.\n");
        return ret;
    }

    bool isFinish = true;
    uint32_t cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);
        if (ret != GMERR_OK) {
            printf("fetch error.\n");
            ret = testGmcDisconnect(conn, stmt);
            return ret;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }
    printf("table %s replaced %u data\n", vertexLabelName, cnt);
    if (cnt != dmlNum) {
        ret = testGmcDisconnect(conn, stmt);
        return GMERR_DATA_EXCEPTION;
    }
    ret = testGmcDisconnect(conn, stmt);
    return ret;
}

static void *InsertDataOfT2012(void *args)
{
    Status ret = GMERR_OK;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtAsync, "T2012", GMC_OPERATION_INSERT));
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    EXPECT_EQ(GMERR_OK, GmcSetStmtAttr(stmtAsync, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U));

    Table2012T obj = {0};
    uint32_t valT2012 = 0;
    uint32_t specialValue = 0;  // uint8字段
    UserBatchDataT userData = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = 0};
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmtAsync->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT2012;
            obj.uiVerNo = valT2012;
            obj.uiUserCid = valT2012;  // primary
            obj.uiUserMagic = valT2012;
            obj.uiIpAddr = valT2012;
            obj.uiGateWayIP = valT2012;
            obj.uiUserVrIndex = valT2012;
            obj.uiUserVrfIndex = valT2012;
            obj.uiUserFVrfIndex = valT2012;
            obj.uiCGNInLabel = valT2012;
            obj.uiMaskLen = valT2012;
            obj.ucFramedRoute = specialValue;
            obj.ucUrpfFlag = specialValue;
            obj.ucTTLBase = specialValue;
            obj.ucMssEnable = specialValue;
            obj.ucL2NATAware = specialValue;
            obj.ucForceFlag = specialValue;
            obj.ucEchoEnable = specialValue;
            obj.ucEchoCos = specialValue;
            obj.ucRes1 = specialValue;
            obj.ucRes2 = specialValue;
            obj.usMssValue = valT2012;
            obj.usMTU = valT2012;
            obj.usRes2 = valT2012;
            obj.uiInboundVrfid = valT2012;
            obj.uiVTIfIndex = valT2012;
            obj.uiIntfVrfIndex = valT2012;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmtAsync, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmtAsync));
            valT2012++;
        }
        do {
            ret = GmcBatchExecuteAsync(batch, BatchCb, &userData);
        } while (ret == GMERR_CONNECTION_SEND_BUFFER_FULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    while (userData.received != batchCnt) {
        DbUsleep(10000);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return nullptr;
}

static void *InsertDataOfT2214(void *args)
{
    Status ret = GMERR_OK;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtAsync, "T2214", GMC_OPERATION_INSERT));
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    EXPECT_EQ(GMERR_OK, GmcSetStmtAttr(stmtAsync, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U));

    Table2214T obj = {0};
    uint32_t valT2214 = 0;
    UserBatchDataT userData = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = 0};
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmtAsync->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT2214;
            obj.uiVerNo = valT2214;
            obj.uiUserID = valT2214;
            obj.uiUserMagic = valT2214;
            obj.uiVrID = valT2214;  // primary
            obj.hUcmPid = valT2214;
            obj.uiIfIndex = valT2214;  // primary
            obj.usPeVlan = valT2214;   // primary
            obj.usCeVlan = valT2214;   // primary
            obj.uiNodeGrpID = valT2214;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmtAsync, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmtAsync));
            valT2214++;
        }
        do {
            ret = GmcBatchExecuteAsync(batch, BatchCb, &userData);
        } while (ret == GMERR_CONNECTION_SEND_BUFFER_FULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    while (userData.received != batchCnt) {
        DbUsleep(10000);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return nullptr;
}

static void *InsertDataOfT2433(void *args)
{
    Status ret = GMERR_OK;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtAsync, "T2433", GMC_OPERATION_INSERT));
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    EXPECT_EQ(GMERR_OK, GmcSetStmtAttr(stmtAsync, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U));

    Table2433T obj = {0};
    uint32_t valT2433 = 0;
    uint32_t specialValue = 0;
    UserBatchDataT userData = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = 0};
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmtAsync->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.uiKeyID = valT2433;  // primary
            obj.uiMagic = valT2433;  // primary
            obj.uiCir = valT2433;
            obj.uiCbs = valT2433;
            obj.uiPir = valT2433;
            obj.uiPbs = valT2433;
            obj.uiServiceTempId = valT2433;
            obj.uiFlowMappingID = valT2433;
            obj.ucDirection = specialValue;  // primary 0
            obj.ucWeight = specialValue;
            obj.ucStBaseAdjPkt = specialValue;
            obj.cLinkOverHead = specialValue;
            obj.ucLinkMode = specialValue;
            obj.ucAutoOverHead = specialValue;
            obj.ucUserQueueType = specialValue;
            obj.ucOverrideEn = specialValue;
            obj.ucTmSchdEn = specialValue;
            obj.ucGQIdfType = specialValue;
            obj.usGQGroupID = valT2433;
            obj.hSrcPid = valT2433;
            obj.uiVerNo = valT2433;
            obj.uiGQTempID = valT2433;
            obj.uiQueueMapID = valT2433;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmtAsync, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmtAsync));
            valT2433++;
        }
        do {
            ret = GmcBatchExecuteAsync(batch, BatchCb, &userData);
        } while (ret == GMERR_CONNECTION_SEND_BUFFER_FULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    while (userData.received != batchCnt) {
        DbUsleep(10000);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return nullptr;
}

static void *InsertDataOfT2434(void *args)
{
    Status ret = GMERR_OK;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtAsync, "T2434", GMC_OPERATION_INSERT));
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    EXPECT_EQ(GMERR_OK, GmcSetStmtAttr(stmtAsync, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U));

    Table2434T obj = {0};
    uint32_t valT2434 = 0;
    uint32_t specialValue = 0;
    uint8_t specialData = 1;
    UserBatchDataT userData = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = 0};
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmtAsync->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.uiKeyID = valT2434;  // primary
            obj.uiMagic = valT2434;  // primary
            obj.uiCir = valT2434;
            obj.uiPir = valT2434;
            obj.uiCbs = valT2434;
            obj.uiPbs = valT2434;
            obj.ucGreenAction = specialValue;
            obj.ucYellowAction = specialValue;
            obj.ucRedAction = specialValue;
            obj.ucColorAware = specialValue;
            obj.ucGreenSerCls = specialValue;
            obj.ucYellowSerCls = specialValue;
            obj.ucRedSerCls = specialValue;
            obj.ucGreenColor = specialValue;
            obj.ucYellowColor = specialValue;
            obj.ucRedColor = specialValue;
            obj.ucDirection = specialData;  // primary 0
            obj.ucLayerExclude = specialValue;
            obj.ucLayerType = specialValue;
            obj.ucAutoOverHead = specialValue;
            (void)memcpy_s(obj.aucReserved, 2, "ok", 2);
            obj.hSrcPid = valT2434;
            obj.uiVerNo = valT2434;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmtAsync, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmtAsync));
            valT2434++;
        }
        do {
            ret = GmcBatchExecuteAsync(batch, BatchCb, &userData);
        } while (ret == GMERR_CONNECTION_SEND_BUFFER_FULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    while (userData.received != batchCnt) {
        DbUsleep(10000);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return nullptr;
}

static void *InsertDataOfT2009(void *args)
{
    Status ret = GMERR_OK;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtAsync, "T2009", GMC_OPERATION_INSERT));
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    EXPECT_EQ(GMERR_OK, GmcSetStmtAttr(stmtAsync, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U));

    Table2009T obj = {0};
    uint32_t valT2009 = 0;
    uint32_t specialValue = 0;
    UserBatchDataT userData = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = 0};
    uint32_t ctrlFieldOfUiRingIndex = DB_INVALID_UINT32;  // 0xFFFFFFFF
    uint32_t ctrlFieldOfUcUserIpType = 1;
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmtAsync->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT2009;
            obj.uiVerNo = valT2009;
            obj.uiUserCid = valT2009;    // primary
            obj.uiUserMagic = valT2009;  // primary
            obj.uiIfIndex = valT2009;
            obj.uiRingIndex = ctrlFieldOfUiRingIndex;  // 0xFFFFFFFF
            obj.uiToken = valT2009;
            obj.ucUserIpType = ctrlFieldOfUcUserIpType;  // primary 1
            obj.ucUserl2tpType = specialValue;
            obj.usUserRtType = valT2009;
            obj.uiNodeGrpID = valT2009;
            obj.hUcmPid = valT2009;
            obj.uiUserGrId = valT2009;
            obj.uiPwLogicId = valT2009;
            obj.ucBA_Tid = specialValue;
            obj.ucLnsInBA_Tid = specialValue;
            obj.ucBA_Enable = specialValue;
            obj.ucPHB_TID = specialValue;
            obj.ucUserPriority = specialValue;
            obj.ucDownPriority = specialValue;
            obj.ucSetDscp = specialValue;
            obj.ucSetV6Dscp = specialValue;
            obj.ucSetExp = specialValue;
            obj.ucSetInnerVlan = specialValue;
            obj.ucSetOutVlan = specialValue;
            obj.ucLnsBaEn = specialValue;
            obj.ucL2UserPri = specialValue;
            obj.BrasUnicastFlag = specialValue;
            obj.L2NatEnable = specialValue;
            obj.deiEnable = specialValue;
            obj.uiNodeGrpID2 = valT2009;
            obj.ucRuiFlag = specialValue;
            obj.ucRbsId = specialValue;
            obj.usRbpId = valT2009;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmtAsync, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmtAsync));
            valT2009++;
        }
        do {
            ret = GmcBatchExecuteAsync(batch, BatchCb, &userData);
        } while (ret == GMERR_CONNECTION_SEND_BUFFER_FULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    while (userData.received != batchCnt) {
        DbUsleep(10000);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return nullptr;
}

static void *InsertDataOfT2011(void *args)
{
    Status ret = GMERR_OK;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtAsync, "T2011", GMC_OPERATION_INSERT));
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    EXPECT_EQ(GMERR_OK, GmcSetStmtAttr(stmtAsync, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U));

    Table2011T obj = {0};
    uint32_t valT2011 = 0;
    uint32_t specialValue = 0;
    UserBatchDataT userData = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = 0};
    uint32_t ctrlFieldOfUcUserIpType = 1;
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmtAsync->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT2011;
            obj.uiVerNo = valT2011;
            obj.uiUserCid = valT2011;  // primary
            obj.uiUserMagic = valT2011;
            obj.uiIfIndex = valT2011;
            obj.uiMainIfIndex = valT2011;
            obj.uiFamilyid = valT2011;
            obj.uiUserGroupId = valT2011;
            obj.ucUserIpType = ctrlFieldOfUcUserIpType;
            obj.ucAccessType = specialValue;
            obj.ucAccessMode = specialValue;
            obj.ucPvcEncap = specialValue;
            obj.ucIfCOAModify = specialValue;
            obj.ucEncapeType = specialValue;
            obj.ucTriggerType = specialValue;
            obj.ucAccountType = specialValue;
            obj.uc4CAR8CARFlag = specialValue;
            obj.ucRuiRoleFlag = specialValue;
            obj.ucDaaSeperFlag = specialValue;
            obj.ucRuiFlag = specialValue;
            obj.usInnerVlan = valT2011;
            obj.usOuterVlan = valT2011;
            obj.usVpi = valT2011;
            obj.usVci = valT2011;
            obj.ucInnerVlanPri = specialValue;
            obj.ucOuterVlanPri = specialValue;
            (void)sprintf_s(obj.aucRuiSrcMac, 2, "ok", 2);
            (void)sprintf_s(obj.aucMAC, 6, "%u", valT2011);
            (void)sprintf_s(obj.aucPeerMAC, 6, "%u", valT2011);
            obj.uiIfGroup = valT2011;
            obj.usDetectTimes = valT2011;
            obj.usDetectInter = valT2011;
            obj.uiVrID = valT2011;
            obj.ucKeepOnline = specialValue;
            obj.ucUpnpEn = specialValue;
            obj.usDomainId = valT2011;
            obj.ucForceFlag = specialValue;
            obj.ucMssEnable = specialValue;
            obj.usMssValue = valT2011;
            obj.uiSn = valT2011;
            obj.ucL3CAR = specialValue;
            obj.ucStatVlanEx = specialValue;
            obj.uc8021pIf = specialValue;
            obj.ucIfTerminate = specialValue;
            obj.ucBlockFlow = specialValue;
            obj.ucInSesGrpExcl = specialValue;
            obj.ucOutSesGrpExcl = specialValue;
            obj.ucMacSessionEn = specialValue;
            obj.uiUcVrfID = valT2011;
            obj.uiOutFamilyid = valT2011;
            obj.uiResv = valT2011;
            obj.uiLLUserCid = valT2011;
            obj.uiLLUserMagic = valT2011;
            obj.uiObjectID = valT2011;
            obj.hBtrcPid = valT2011;
            obj.ucVpnAccFlag = specialValue;
            obj.ucReportSqFlg = specialValue;
            obj.ucNatHashIndex = specialValue;
            obj.ucFeInfo = specialValue;
            obj.ucSoftGreFlag = specialValue;
            obj.ucOperType = specialValue;
            obj.usPathFlag = valT2011;
            obj.ucFlags = specialValue;
            obj.hostcarflag = specialValue;
            (void)sprintf_s(obj.aucResv, 2, "ok", 2);

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmtAsync, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmtAsync));
            valT2011++;
        }
        do {
            ret = GmcBatchExecuteAsync(batch, BatchCb, &userData);
        } while (ret == GMERR_CONNECTION_SEND_BUFFER_FULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    while (userData.received != batchCnt) {
        DbUsleep(10000);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return nullptr;
}

static void *InsertDataOfT2172(void *args)
{
    Status ret = GMERR_OK;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtAsync, "T2172", GMC_OPERATION_INSERT));
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    EXPECT_EQ(GMERR_OK, GmcSetStmtAttr(stmtAsync, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U));

    Table2172T obj = {0};
    uint32_t valT2172 = 0;
    uint32_t specialValue = 0;
    UserBatchDataT userData = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = 0};
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmtAsync->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT2172;
            obj.uiVerNo = valT2172;
            obj.uiFlowIndex = valT2172;
            obj.uiIfIndex = valT2172;  // primary
            obj.uiSegIndex = valT2172;
            obj.usPeVlan = valT2172;  // primary
            obj.usCeVlan = valT2172;  // primary
            obj.uiVrID = valT2172;    // primary
            obj.uiMainIfIndex = valT2172;
            obj.ucIfBrasFlag = specialValue;
            obj.ucReserved = specialValue;
            obj.ucReserved1 = specialValue;
            obj.ucReserved2 = specialValue;
            obj.uiNodeGrpID = valT2172;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmtAsync, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmtAsync));
            valT2172++;
        }
        do {
            ret = GmcBatchExecuteAsync(batch, BatchCb, &userData);
        } while (ret == GMERR_CONNECTION_SEND_BUFFER_FULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    while (userData.received != batchCnt) {
        DbUsleep(10000);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return nullptr;
}

static void *InsertDataOfT7(void *args)
{
    Status ret = GMERR_OK;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtAsync, "T7", GMC_OPERATION_INSERT));
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    EXPECT_EQ(GMERR_OK, GmcSetStmtAttr(stmtAsync, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U));

    Table7T obj = {0};
    uint32_t valT7 = 0;
    uint32_t specialValue = 0;
    UserBatchDataT userData = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = 0};
    uint32_t ctrlFieldOfUiPathFlags = 524288;
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmtAsync->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.hSrcPid = valT7;
            obj.uiVrIndex = valT7;   // primary
            obj.uiVrfIndex = valT7;  // primary
            obj.uiVerNo = valT7;
            obj.uiFVrfIndex = valT7;
            obj.uiDestAddr = valT7;        // primary
            obj.ucMaskLen = specialValue;  // primary
            obj.ucIIDGFlag = specialValue;
            obj.usRouteFlags = valT7;
            obj.uiPathFlags = ctrlFieldOfUiPathFlags;
            obj.uiIIDindex = valT7;
            obj.uiPrimaryLabel = valT7;
            obj.uiAttributeId = valT7;
            obj.usQosid = valT7;
            obj.usRes = valT7;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmtAsync, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmtAsync));
            valT7++;
        }
        do {
            ret = GmcBatchExecuteAsync(batch, BatchCb, &userData);
        } while (ret == GMERR_CONNECTION_SEND_BUFFER_FULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    while (userData.received != batchCnt) {
        DbUsleep(10000);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return nullptr;
}

static void *InsertDataOfT2438(void *args)
{
    Status ret = GMERR_OK;
    GmcConnT *connAsync = NULL;
    GmcStmtT *stmtAsync = NULL;
    ret = testGmcConnect(&connAsync, &stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, GmcPrepareStmtByLabelName(stmtAsync, "T2438", GMC_OPERATION_INSERT));
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;
    EXPECT_EQ(GMERR_OK, GmcSetStmtAttr(stmtAsync, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    EXPECT_EQ(GMERR_OK, GmcBatchOptionInit(&batchOption));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufLimitSize(&batchOption, 2048U));
    EXPECT_EQ(GMERR_OK, GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U));

    Table2438T obj = {0};
    uint32_t valT2438 = 0;
    uint32_t specialValue = 0;
    uint8_t specialData = 1;
    UserBatchDataT userData = {.expectedTotalNum = 1, .expectedSuccessNum = 1, .received = 0};
    for (uint32_t k = 0; k < batchCnt; k++) {
        EXPECT_EQ(GMERR_OK, GmcBatchPrepare(stmtAsync->conn, &batchOption, &batch));
        for (uint32_t i = 0; i < replaceNum; i++) {
            obj.uiSessionID = valT2438;  // primary
            obj.uiFamilyID = specialData;
            obj.uiMagic = valT2438;  // primary
            obj.uiVersion = valT2438;
            obj.uiIfIndex = valT2438;
            obj.uiOutIfindex = valT2438;
            obj.uiMainIfindex = valT2438;
            obj.usPeVlanId = valT2438;
            obj.usCeVlanId = valT2438;
            obj.ucDirection = specialValue;  // primary
            obj.ucFamilyResType = specialValue;
            obj.ucResBehavior = specialValue;
            obj.ucIsFamilyMem = specialValue;
            obj.ucIsVcpeflag = specialValue;
            obj.ucLayerExclude = specialValue;
            obj.ucPWifFlag = specialValue;
            obj.ucQoSLabelFlag = specialValue;
            obj.uiUCMPid = valT2438;
            obj.uiTrunkid = valT2438;
            obj.ucVLinkMode = specialValue;
            obj.ucScheduleType = specialValue;
            obj.usTunnelId = valT2438;
            obj.uiChannelid = valT2438;
            obj.hSrcPid = valT2438;
            obj.uiVerNo = valT2438;
            obj.ucResNoChange = specialValue;
            obj.ucOutifChFlag = specialValue;
            obj.ucBasUserType = specialValue;
            obj.ucIfType = specialValue;
            obj.ucNpId = specialValue;
            obj.uclaclns = specialValue;
            obj.ucPriCarEnable = specialValue;
            obj.ucAfterCarFlag = specialValue;
            obj.ucEtmFlag = specialValue;
            obj.ucOldBehavior = specialValue;
            obj.usResv2 = valT2438;

            EXPECT_EQ(GMERR_OK, GmcSetVertexPropertyAll(stmtAsync, &obj, sizeof(obj)));
            EXPECT_EQ(GMERR_OK, GmcBatchAddDML(batch, stmtAsync));
            valT2438++;
        }
        do {
            ret = GmcBatchExecuteAsync(batch, BatchCb, &userData);
        } while (ret == GMERR_CONNECTION_SEND_BUFFER_FULL);
        EXPECT_EQ(GMERR_OK, ret);
    }
    while (userData.received != batchCnt) {
        DbUsleep(10000);
    }
    ret = testGmcDisconnect(connAsync, stmtAsync);
    EXPECT_EQ(GMERR_OK, ret);
    return nullptr;
}

static void InsertTriggerData()
{
    int err = 0;
    int num = 9;
    pthread_t InsertT[num];

    bool warmFlag = false;
    err = pthread_create(&InsertT[0], NULL, InsertDataOfT2012, (void *)&warmFlag);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&InsertT[1], NULL, InsertDataOfT2214, (void *)&warmFlag);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&InsertT[2], NULL, InsertDataOfT2433, (void *)&warmFlag);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&InsertT[3], NULL, InsertDataOfT2434, (void *)&warmFlag);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&InsertT[5], NULL, InsertDataOfT2009, (void *)&warmFlag);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&InsertT[6], NULL, InsertDataOfT2011, (void *)&warmFlag);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&InsertT[7], NULL, InsertDataOfT2172, (void *)&warmFlag);
    EXPECT_EQ(GMERR_OK, err);

    pthread_join(InsertT[2], NULL);
    pthread_join(InsertT[3], NULL);
    pthread_join(InsertT[5], NULL);
    pthread_join(InsertT[6], NULL);

    err = pthread_create(&InsertT[4], NULL, InsertDataOfT7, (void *)&warmFlag);
    EXPECT_EQ(GMERR_OK, err);
    err = pthread_create(&InsertT[8], NULL, InsertDataOfT2438, (void *)&warmFlag);
    EXPECT_EQ(GMERR_OK, err);

    pthread_join(InsertT[0], NULL);
    pthread_join(InsertT[1], NULL);
    pthread_join(InsertT[4], NULL);
    pthread_join(InsertT[7], NULL);
    pthread_join(InsertT[8], NULL);
}

/*
 * 用例001：BRAS场景性能用例
 */
TEST_F(PathBras, PathBrasScenario)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // create paths
    CreatePaths(stmt);

    // create subscription
    int chanRingLen = 256;
    const char *channelName = (const char *)"channel1";
    GmcConnT *channel1 = NULL;
    GmcStmtT *stmt_sub = NULL;
    ret = testSubConnect(&channel1, &stmt_sub, 200, g_epoll_reg_info, channelName, &chanRingLen);
    EXPECT_EQ(GMERR_OK, ret);

    const char *pathArray[] = {"P30601", "P30603", "P81036", "P30605", "P80654", "P89357", "P89361", "P201720",
        "P89380", "P63269", "P63272", "P80300", "P80301", "P88624", "P89909", "P89910", "P89911", "P89912", "P89401",
        "P80318", "P80323", "P80324", "P84", "P88", "P89", "P87517", "P87518", "P80227", "P63215", "P63218", "P63220",
        "P80248", "P201722"};
    uint32_t pathNum = 0;
    GmcSubUdfT udf = {.userCb = PathUserCb, .userData = &pathNum};
    for (uint32_t i = 0; i < sizeof(pathArray) / sizeof(char *); i++) {
        std::string subName = "sub";
        subName += std::to_string(i);
        std::string createSub = "CREATE SUBSCRIPTION ";
        createSub += subName;
        createSub += " ON PATH ";
        createSub += pathArray[i];
        createSub += " BY CHANNEL channel1;";
        EXPECT_EQ(GMERR_OK, GmcSubscribeComplexPath(stmt, createSub.c_str(), &udf));
    }

    // prepare data
    PrepareData(stmt);

    // create triggers
    const char *createTriggerRule = R"(
        CREATE TRIGGER bras_1 ON REPLACE T31 WHEN TRUE DO SEARCH P30601, P30605, P88624, P89, P88, P87517;
        CREATE TRIGGER bras_2 ON REPLACE T51 WHEN TRUE DO SEARCH P30601, P30605, P80654, P89380, P88, P80248;
        CREATE TRIGGER bras_3 ON REPLACE T279 WHEN TRUE DO SEARCH P30601, P30603, P81036, P30605, P80654, P89357, P201720, P88624, P89909, P89910, P89911, P89912, P80318, P84, P88, P89, P87517, P87518, P201722;
        CREATE TRIGGER bras_4 ON REPLACE T301 WHEN TRUE DO SEARCH P80300, P88624, P89910, P80323, P80324, P87517;
        CREATE TRIGGER bras_5 ON REPLACE T803 WHEN TRUE DO SEARCH P88624, P87517;
        CREATE TRIGGER bras_6 ON REPLACE T2012 WHEN TRUE DO SEARCH P80654, P80227, P63220, P80248;
        CREATE TRIGGER bras_7 ON REPLACE T2214 WHEN TRUE DO SEARCH P88624;
        CREATE TRIGGER bras_8 ON REPLACE T2433 WHEN TRUE DO SEARCH P80300, P80301;
        CREATE TRIGGER bras_9 ON REPLACE T2434 WHEN TRUE DO SEARCH P80300, P80301;
        CREATE TRIGGER bras_10 ON REPLACE T2009 WHEN TRUE DO SEARCH P30601, P30603, P81036, P30605, P80654, P89357, P201720, P63269, P89909, P89910, P89911, P89912, P80318, P84, P88, P89, P80227, P63215, P201722;
        CREATE TRIGGER bras_11 ON REPLACE T2011 WHEN TRUE DO SEARCH P30601, P30603, P30605, P80654, P89357, P89361, P63272, P89909, P89910, P89911, P89912, P89401, P80318, P84, P88, P89, P87518, P63218, P80248;
        CREATE TRIGGER bras_12 ON REPLACE T2172 WHEN TRUE DO SEARCH P88624, P89401, P80323, P80324, P87517;
        CREATE TRIGGER bras_13 ON REPLACE T7 WHEN TRUE DO SEARCH P89380, P89, P84;
        CREATE TRIGGER bras_14 ON REPLACE T2438 WHEN TRUE DO SEARCH P80300, P80301;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, createTriggerRule));

    // replace data to trigger pvc
    PERF_TIME_START();  // perf start
    // system("sh getflame.sh collect gmserver 10 &");
    InsertTriggerData();
    uint32_t sleepCnt = 0;
    while (pathNum != ip4forwardInitSizeAsync) {
        DbUsleep(1000);
        sleepCnt++;
        if (sleepCnt == 20000) {
            AW_FUN_Log(LOG_STEP, "===========!!!ERROR, time out for wait, pathNum = %d\n", pathNum);
            ASSERT_TRUE(false);
        }
    }
    PERF_TIME_END();  // perf end
    ASSERT_EQ(pathNum, ip4forwardInitSizeAsync);
    AW_FUN_Log(LOG_STEP, "Path received: pathNum = %d\n", pathNum);

    // drop subscription
    for (uint32_t i = 0; i < sizeof(pathArray) / sizeof(char *); i++) {
        std::string subName = "sub";
        subName += std::to_string(i);
        std::string dropSub = "DROP SUBSCRIPTION ";
        dropSub += subName;
        dropSub += ";";
        EXPECT_EQ(GMERR_OK, GmcUnSubscribeComplexPath(stmt, dropSub.c_str()));
    }
    ret = testSubDisConnect(channel1, stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
    const char *dropTriggers = R"(
        DROP TRIGGER bras_1;
        DROP TRIGGER bras_2;
        DROP TRIGGER bras_3;
        DROP TRIGGER bras_4;
        DROP TRIGGER bras_5;
        DROP TRIGGER bras_6;
        DROP TRIGGER bras_7;
        DROP TRIGGER bras_8;
        DROP TRIGGER bras_9;
        DROP TRIGGER bras_10;
        DROP TRIGGER bras_11;
        DROP TRIGGER bras_12;
        DROP TRIGGER bras_13;
        DROP TRIGGER bras_14;
    )";
    EXPECT_EQ(GMERR_OK, GmcExecGql(stmt, dropTriggers));

    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
