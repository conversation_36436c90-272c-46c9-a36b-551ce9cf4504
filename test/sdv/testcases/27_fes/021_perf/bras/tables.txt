CREATE VERTEXLABEL T31 (
	uiIfIndex UINT32,
	enIfPhyType UINT32,
	uiIfSubPhyType UINT32,
	uiIfLinkType UINT32,
	uiIfVr UINT32,
	uiIfVrf UINT32,
	uiPortgroup UINT32,
	uiIfGroupID UINT32,
	uiIfDf UINT32,
	uiIfIsSubif UINT32,
	uiIfStatiEnable UINT32,
	uiIfEncapType UINT32,
	uiNodeGrpID UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiFVrf UINT32,
	usflag UINT16,
	usflag11 UINT16
	PRIMARY INDEX IDX_FWIFINFONEW_0(uiIfIndex)
	HAC_HASH INDEX IDX_FWIFINFONE1_1(uiPortgroup) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_FWIFINFONE2_2(uiIfVr, uiIfVrf) UNIQUE HASH_CAP 114688,
	<PERSON><PERSON>_HASH INDEX IDX_FWIFINFONE3_3(uiIfIndex, uiIfVr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_FWIFINFONE4_4(uiIfVr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_FWIFINFONE5_5(uiNodeGrpID) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3986 (
	uiVrId UINT32,
	uiNodeGrpID UINT32,
	ucMode UINT8,
	ucRes1 UINT8,
	ucRes2 UINT8,
	ucRes3 UINT8,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_uiNodeGrpID_0(uiNodeGrpID)
);
CREATE VERTEXLABEL T48 (
	auiIpAddr FIXED 16,
	uiIfIndex UINT32,
	aucMacAddr FIXED 6,
	ucNdFlg UINT8,
	ucLinkType UINT8,
	uiWorkIfIndex UINT32,
	uiAtmIfIndex UINT32,
	uiQatIndex UINT32,
	usPevid UINT16,
	usCevid UINT16,
	uiVcd UINT32,
	ucState UINT8,
	ucReserved1 UINT8,
	usFwifType UINT16,
	uiVrId UINT32,
	uiVrfId UINT32,
	uiIfPhyType UINT32,
	uiMainIfPhyType UINT32,
	uiIfLinkType UINT32,
	uiIfEncapType UINT32,
	uiFVrfId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiFlowId UINT32,
	ucMaskLen UINT8,
	ucReserved2 UINT8,
	usReserved1 UINT16
	PRIMARY INDEX IDX_NDIndex0_0(auiIpAddr, uiIfIndex)
	HAC_HASH INDEX IDX_NDIndex1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_NDIndex2_2(hSrcPid, uiVerNo) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_NDIndex3_3(uiWorkIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_NDIndex4_4(auiIpAddr, uiVrId, uiVrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_NDIndex5_5(uiWorkIfIndex, usPevid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T51 (
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiVrVrfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	hSrcController UINT32
	PRIMARY INDEX IDX_VRVRFINDEXInd0_0(uiVrIndex, uiVrfIndex)
	HAC_HASH INDEX IDX_VRVRFINDEXInd1_1(uiVrVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VRVRFINDEXInd2_2(uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VRVRFINDEXInd3_3(uiVrfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T81 (
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiTopologyID UINT32,
	uiFVrfIndex UINT32,
	auiDestAddr FIXED 16,
	ucMaskLen UINT8,
	ucIIDGFlag UINT8,
	usRouteFlags UINT16,
	uiPathFlags UINT32,
	uiIIDindex UINT32,
	uiPrimaryLabel UINT32,
	uiAttributeId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	usQosid UINT16,
	usRes UINT16
	PRIMARY INDEX IDX_PP6UCRTIndex0_0(auiDestAddr, ucMaskLen, uiVrfIndex, uiVrIndex)
	HAC_HASH INDEX IDX_PP6UCRTIndex1_1(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP6UCRTIndex2_2(usQosid, uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP6UCRTIndex3_3(uiAttributeId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP6UCRTIndex4_4(auiDestAddr, uiVrIndex, uiVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP6UCRTIndex5_5(uiIIDindex, uiVrIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T84 (
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiTopologyID UINT32,
	uiFVrfIndex UINT32,
	uiIIDindex UINT32,
	auiOriginNhp FIXED 16,
	ucNhpNum UINT8,
	ucNhpFlag UINT8,
	usCtrlFlag UINT16,
	uiIIDFlag UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiOriginNhp1 UINT32,
	uiOriginNhp2 UINT32,
	uiOriginNhp3 UINT32,
	uiOriginNhp4 UINT32
	PRIMARY INDEX IDX_PP6UCIIDIndex0_0(uiIIDindex)
	HAC_HASH INDEX IDX_PP6UCIIDIndex1_1(hSrcPid, uiVerNo) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP6UCIIDIndex2_2(uiVrIndex, uiVrfIndex, auiOriginNhp) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP6UCIIDIndex3_3(uiVrIndex, uiVrfIndex, uiOriginNhp4) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP6UCIIDIndex4_4(auiOriginNhp) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP6UCIIDIndex5_5(uiVrIndex, uiIIDindex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T279 (
	uiIfIndex UINT32,
	usflag UINT16,
	usRes UINT16,
	uiIfPhyType UINT32,
	uiIfSubPhyType UINT32,
	uiIfLinkType UINT32,
	uiIfEncaptype UINT32,
	unfifinfo FIXED 12,
	uiIfUcmpEn UINT32,
	ullBandwidth FIXED 8,
	hSrcPid UINT32,
	uiVerNo UINT32,
	usFlagPlus UINT16,
	usResPlus UINT16
	PRIMARY INDEX IDX_FIFINFOIndex0_0(uiIfIndex)
	HAC_HASH INDEX IDX_FIFINFOIndex1_1(uiIfPhyType, uiIfSubPhyType, unfifinfo) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T320 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiTopologyID UINT32,
	usPathFlag UINT16,
	ucBfdFlag UINT8,
	ucReserve1 UINT8,
	uiNhlfeID UINT32,
	auiAddress FIXED 16,
	uiLspXcIndex UINT32,
	ucLspXcType UINT8,
	ucLspXcRole UINT8,
	usMtu UINT16,
	uiOutIfIndex UINT32,
	auiNextHop FIXED 16,
	uiOutLabel UINT32,
	ucMaskLen UINT8,
	ucReserve2 UINT8,
	ucFrrFlag UINT8,
	ucTunnelType UINT8,
	uiTunnelID UINT32,
	uiBandwidth UINT32,
	uiFVrfIndex UINT32,
	uiOutAtIndex UINT32,
	uiIngressToken UINT32,
	uiBackUpToken UINT32,
	uiTransitToken UINT32,
	uiTBackupToken UINT32,
	uiNodeGrpID UINT32,
	usOutIfType UINT16,
	usTB UINT16,
	usTP UINT16,
	usVlanID UINT16,
	uiRingIndex UINT32
	PRIMARY INDEX IDX_LDPNHLFEIndex0_0(uiVrIndex, uiVrfIndex, uiNhlfeID, uiLspXcIndex, ucLspXcType)
	HAC_HASH INDEX IDX_LDPNHLFEIndex1_1(uiVrIndex, uiVrfIndex, uiLspXcIndex, ucLspXcType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex2_2(uiVrIndex, uiVrfIndex, auiAddress, ucMaskLen) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex3_3(uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex4_4(uiOutIfIndex, auiNextHop) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex5_5(ucLspXcType, uiLspXcIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex6_6(uiVrIndex, uiVrfIndex, uiLspXcIndex, ucLspXcType, uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex7_7(auiNextHop) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1199 (
	uiVrId UINT32,
	uiVrfId UINT32,
	stIpAddr FIXED 16,
	usPevid UINT16,
	usCevid UINT16,
	uiIfIndex UINT32,
	uiSubIfType UINT32,
	uiToken UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiNodeId UINT32
	PRIMARY INDEX IDX_EumQinqNdUser_0(uiVrId, uiVrfId, stIpAddr, uiIfIndex)
	HAC_HASH INDEX IDX_EumQinqNdUse1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_EumQinqNdUse2_2(stIpAddr, uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_EumQinqNdUse3_3(uiVrId, uiVrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_EumQinqNdUse4_4(uiVrId, uiVrfId, stIpAddr) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2009 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiIfIndex UINT32,
	uiRingIndex UINT32,
	uiToken UINT32,
	ucUserIpType UINT8,
	ucUserl2tpType UINT8,
	usUserRtType UINT16,
	uiNodeGrpID UINT32,
	hUcmPid UINT32,
	uiUserGrId UINT32,
	uiPwLogicId UINT32,
	ucBA_Tid UINT8,
	ucLnsInBA_Tid UINT8,
	ucBA_Enable UINT8,
	ucPHB_TID UINT8,
	ucUserPriority UINT8,
	ucDownPriority UINT8,
	ucSetDscp UINT8,
	ucSetV6Dscp UINT8,
	ucSetExp UINT8,
	ucSetInnerVlan UINT8,
	ucSetOutVlan UINT8,
	ucLnsBaEn UINT8,
	ucL2UserPri UINT8,
	BrasUnicastFlag UINT8,
	L2NatEnable UINT8,
	deiEnable UINT8,
	uiNodeGrpID2 UINT32,
	ucRuiFlag UINT8,
	ucRbsId UINT8,
	usRbpId UINT16
	PRIMARY INDEX IDX_BRAS_USER_GLOBAL_INFOIn_0(uiUserCid, uiUserMagic, ucUserIpType) HASH_CAP 114688
	HAC_HASH INDEX IDX_BRAS_USER_GLOBAL_INFOIn_1(uiUserCid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_GLOBAL_INFOIn_2(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_GLOBAL_INFOIn_3(uiUserCid, uiUserMagic) UNIQUE HASH_CAP 114688,
	MULTI_HASH INDEX IDX_BRAS_USER_GLOBAL_INFOIn_4(uiRingIndex) HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_GLOBAL_INFOIn_5(uiPwLogicId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2010 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiSubIfIndex UINT32,
	uiIfIndex UINT32,
	uiRingIndex UINT32,
	uiNRBIndex UINT32,
	ucFsFlag UINT8,
	ucResv FIXED 3
	PRIMARY INDEX IDX_BRAS_USER_NRB_INFOIndex_0(uiRingIndex)
	HAC_HASH INDEX IDX_BRAS_USER_NRB_INFOIndex_1(uiSubIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2011 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiIfIndex UINT32,
	uiMainIfIndex UINT32,
	uiFamilyid UINT32,
	uiUserGroupId UINT32,
	ucUserIpType UINT8,
	ucAccessType UINT8,
	ucAccessMode UINT8,
	ucPvcEncap UINT8,
	ucIfCOAModify UINT8,
	ucEncapeType UINT8,
	ucTriggerType UINT8,
	ucAccountType UINT8,
	uc4CAR8CARFlag UINT8,
	ucRuiRoleFlag UINT8,
	ucDaaSeperFlag UINT8,
	ucRuiFlag UINT8,
	usInnerVlan UINT16,
	usOuterVlan UINT16,
	usVpi UINT16,
	usVci UINT16,
	ucInnerVlanPri UINT8,
	ucOuterVlanPri UINT8,
	aucRuiSrcMac FIXED 2,
	aucMAC FIXED 6,
	aucPeerMAC FIXED 6,
	uiIfGroup UINT32,
	usDetectTimes UINT16,
	usDetectInter UINT16,
	uiVrID UINT32,
	ucKeepOnline UINT8,
	ucUpnpEn UINT8,
	usDomainId UINT16,
	ucForceFlag UINT8,
	ucMssEnable UINT8,
	usMssValue UINT16,
	uiSn UINT32,
	ucL3CAR UINT8,
	ucStatVlanEx UINT8,
	uc8021pIf UINT8,
	ucIfTerminate UINT8,
	ucBlockFlow UINT8,
	ucInSesGrpExcl UINT8,
	ucOutSesGrpExcl UINT8,
	ucMacSessionEn UINT8,
	uiUcVrfID UINT32,
	uiOutFamilyid UINT32,
	uiResv UINT32,
	uiLLUserCid UINT32,
	uiLLUserMagic UINT32,
	uiObjectID UINT32,
	hBtrcPid UINT32,
	ucVpnAccFlag UINT8,
	ucReportSqFlg UINT8,
	ucNatHashIndex UINT8,
	ucFeInfo UINT8,
	ucSoftGreFlag UINT8,
	ucOperType UINT8,
	usPathFlag UINT16,
	ucFlags UINT8,
	hostcarflag UINT8,
	aucResv FIXED 2
	PRIMARY INDEX IDX_BRAS_USER_BASIC_INFOInd_11(uiUserCid)
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_0(uiUserCid, uiUserMagic) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_1(uiIfIndex, usInnerVlan, usOuterVlan) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_2(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_3(uiLLUserCid, uiLLUserMagic) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_4(uiVrID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_5(uiVrID, uiUcVrfID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_6(uiIfIndex, uiVrID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_7(uiMainIfIndex, uiVrID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_8(uiMainIfIndex, uiVrID, uiUcVrfID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_9(uiMainIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_10(uiMainIfIndex, uiUcVrfID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_12(uiIfIndex, usOuterVlan) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_13(usInnerVlan, usOuterVlan, aucMAC) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2336 (
	uiCid UINT32,
	uiUserMagic UINT32,
	ucUserIpType UINT8,
	ucResv UINT8,
	usUserRtType UINT16,
	uiNodeGrpID UINT32,
	uiNodeGrpID2 UINT32,
	uiToken UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_USER_TRUNK_NODE_IN_0(uiCid, uiUserMagic, ucUserIpType, ucResv, uiNodeGrpID)
	HAC_HASH INDEX IDX_BRAS_USER_TRUNK_NODE_IN_1(uiCid, uiUserMagic, uiNodeGrpID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_TRUNK_NODE_IN_2(uiCid, uiUserMagic) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_TRUNK_NODE_IN_3(uiUserMagic, uiCid, ucUserIpType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_TRUNK_NODE_IN_4(uiNodeGrpID) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2337 (
	uiUserMagic UINT32,
	uiCid UINT32,
	uiSelNodeId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_USER_TRUNK_SELECTE_0(uiUserMagic, uiCid)
	HAC_HASH INDEX IDX_BRAS_USER_TRUNK_SELECTE_1(uiUserMagic, uiCid, uiSelNodeId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2563 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiPhyIfIndex UINT32,
	uiIfIndex UINT32,
	uiPwLogicId UINT32,
	uiPwToken UINT32,
	uiGlobalIndex UINT32,
	ucFsFlag UINT8,
	ucLinkRole UINT8,
	ucIsTrunk UINT8,
	ucLBMode UINT8,
	usTrunkID UINT16,
	usflag UINT16,
	hUcmPid UINT32,
	uiVrID UINT32,
	uiBasMainIf UINT32
	PRIMARY INDEX IDX_BRAS_USER_PWIF_NHP_INFO_0(uiPwLogicId)
	HAC_HASH INDEX IDX_BRAS_USER_PWIF_NHP_INFO_1(uiPhyIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_PWIF_NHP_INFO_2(uiBasMainIf) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2745 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	UserCid UINT32,
	UserMagic UINT32,
	LocalTunnelId UINT16,
	LocalSessionId UINT16,
	PeerTunnelId UINT16,
	PeerSessionId UINT16,
	NodeGroupID UINT32,
	PortGroupID UINT32,
	IPTos UINT8,
	Qindex UINT8,
	ChannelID UINT8,
	ToTmFlag UINT8,
	ScheduleType UINT8,
	IfParseTCPAck UINT8,
	LnsBakFlag UINT8,
	ChannelID2 UINT8,
	NodeGroupID2 UINT32,
	PortGroupID2 UINT32,
	CUFlag UINT8,
	ucRes UINT8,
	usRes UINT16
	PRIMARY INDEX IDX_BRAS_USER_LNS_INFOIndex_0(UserCid, UserMagic)
	HAC_HASH INDEX IDX_BRAS_USER_LNS_INFOIndex_1(LocalTunnelId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_LNS_INFOIndex_2(UserCid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2869 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiNhpLogicIndex UINT32,
	uiTnlNhpToken UINT32,
	uiOutIfIndex UINT32,
	uacAddr FIXED 16,
	uiIpv4 UINT32,
	uiGlobalIndex UINT32,
	uiPwveIfIndex UINT32,
	uiRouteIfIndex UINT32,
	ucFsFlag UINT8,
	ucResv FIXED 3,
	usResv UINT16,
	usSegmtFlag UINT16,
	hUcmPid UINT32,
	uiVrID UINT32
	PRIMARY INDEX IDX_BRAS_USER_PWVE_NHP_INFO_0(uiNhpLogicIndex)
	HAC_HASH INDEX IDX_BRAS_USER_PWVE_NHP_INFO_1(uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_PWVE_NHP_INFO_2(uiPwveIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_PWVE_NHP_INFO_3(uiVrID, uiRouteIfIndex, uiIpv4) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_PWVE_NHP_INFO_4(uiVrID, uiRouteIfIndex, uacAddr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_PWVE_NHP_INFO_5(uiVrID, uiPwveIfIndex, uiOutIfIndex, uiIpv4) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T4598 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrId UINT32,
	aucDestAddr FIXED 16,
	uiVpnIndex UINT32,
	ucMaskLen UINT8,
	ucReserved1 UINT8,
	usReserved2 UINT16,
	uiAttrId UINT32
	PRIMARY INDEX IDX_BRAS_UNR6_LINKADDRIndex_0(uiVrId, aucDestAddr, uiVpnIndex, ucMaskLen)
	HAC_HASH INDEX IDX_BRAS_UNR6_LINKADDRIndex_1(uiVrId, uiVpnIndex, aucDestAddr) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T83 (
	uiGIIDIndex UINT32,
	uiAttributeID UINT32,
	uiPrimaryIID UINT32,
	uiPrimaryLabel UINT32,
	uiBackupIID UINT32,
	uiBackupLabel UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	usFlag UINT16,
	usReserved UINT16
	PRIMARY INDEX IDX_IIDGNodeIndex0_0(uiGIIDIndex, uiAttributeID, uiPrimaryIID, uiPrimaryLabel, uiBackupIID, uiBackupLabel, uiVrIndex)
	HAC_HASH INDEX IDX_IIDGNodeIndex1_1(uiGIIDIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IIDGNodeIndex2_2(uiVrIndex, uiVrfIndex, hSrcPid, uiVerNo) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IIDGNodeIndex3_3(uiGIIDIndex, uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IIDGNodeIndex4_4(uiPrimaryIID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IIDGNodeIndex5_5(uiBackupIID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IIDGNodeIndex6_6(uiAttributeID) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2742 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	LocalTunnelId UINT16,
	LocalSessionId UINT16,
	PeerTunnelId UINT16,
	PeerSessionId UINT16,
	QindexEnable UINT8,
	ucRuiFlag UINT8,
	ucRuiEcapIndex UINT8,
	ucRes UINT8,
	uiRbpId UINT32
	PRIMARY INDEX IDX_BRAS_USER_LAC_INFOIndex_0(LocalTunnelId, LocalSessionId)
	HAC_HASH INDEX IDX_BRAS_USER_LAC_INFOIndex_1(uiUserCid, uiUserMagic) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_LAC_INFOIndex_2(LocalTunnelId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T85 (
	uiIIDindex UINT32,
	auiNextHop FIXED 16,
	uiOutIfIndex UINT32,
	uiAidIndex UINT32,
	uiVerNo UINT32,
	hSrcPid UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiNodeID UINT32,
	uiIIDFlag UINT32,
	uiRingIndex UINT32
	PRIMARY INDEX IDX_P6UCSTDIIDIdx0_0(uiIIDindex, auiNextHop, uiOutIfIndex)
	HAC_HASH INDEX IDX_P6UCSTDIIDIdx1_1(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCSTDIIDIdx2_2(uiAidIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCSTDIIDIdx3_3(uiVerNo, hSrcPid, uiVrIndex, uiVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCSTDIIDIdx4_4(uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCSTDIIDIdx5_5(auiNextHop, uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCSTDIIDIdx6_6(auiNextHop) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T86 (
	uiIIDindex UINT32,
	uiVrIndex UINT32,
	uiTVrfIndex UINT32,
	uiFVrfIndex UINT32,
	ucTunnelType UINT8,
	ucReserved0 UINT8,
	usReserved1 UINT16,
	uiTunnelId UINT32,
	uiVerNo UINT32,
	hSrcPid UINT32,
	uiVrfIndex UINT32
	PRIMARY INDEX IDX_P6UCEXTIIDIdx0_0(uiIIDindex, uiVrIndex, uiTVrfIndex, ucTunnelType, uiTunnelId)
	HAC_HASH INDEX IDX_P6UCEXTIIDIdx1_1(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCEXTIIDIdx2_2(uiVrIndex, uiTVrfIndex, uiVerNo, hSrcPid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCEXTIIDIdx3_3(uiVrIndex, uiTVrfIndex, ucTunnelType, uiTunnelId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCEXTIIDIdx4_4(uiIIDindex, uiVrIndex, uiTVrfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T121 (
	uiMyDiscr UINT32,
	usCtlTabId UINT16,
	usLnkTabId UINT16,
	usPktTabId UINT16,
	usAuthTabId UINT16,
	usLAddrTabId UINT16,
	usMatchTabId UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_TabMapIdx_0(uiMyDiscr)
);
CREATE VERTEXLABEL T122 (
	uiMyDiscr UINT32,
	ucState UINT8,
	ucPosType UINT8,
	ucTosExp UINT8,
	ucBfdFlag UINT8,
	uiNodeGrpID UINT32,
	uiActRcvInt UINT32,
	uiActXmtInt UINT32,
	uiActDetMult UINT32,
	uiOutIfindex UINT32,
	ucBfdDiag UINT8,
	ucSessMode UINT8,
	usSessType UINT16,
	uiPhyIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiGlobalIndex UINT32,
	uiNodeId UINT32,
	uiSeq UINT32,
	uiCtrlVerNo UINT32,
	uiSessNameIndex UINT32,
	uiRsv0 UINT32,
	uiBfdFlagExt UINT32,
	uiTrackIfIndex UINT32,
	uiTriggerIf UINT32,
	uiCfgFlag UINT32,
	usDownCount UINT16,
	usReserve UINT16,
	uiMainSessMD UINT32,
	ullUpTimestamp UINT64
	PRIMARY INDEX IDX_CtlIdx_0(uiMyDiscr)
	HAC_HASH INDEX IDX_CtlIdx1_1(uiOutIfindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_CtlIdx2_2(uiTrackIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_CtlIdx3_3(uiSessNameIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T124 (
	uiMyDiscr UINT32,
	uiVrId UINT32,
	uiVrfId UINT32,
	uiOutIfindex UINT32,
	auiPeerIp FIXED 16,
	ucAssocPst UINT8,
	ucRsv0 UINT8,
	usRsv1 UINT16,
	uiFwdVrfId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiRingIndex UINT32,
	usReserve UINT16,
	usVlanId UINT16,
	uiVlanIfIndex UINT32
	PRIMARY INDEX IDX_LnkIp6Idx_0(uiMyDiscr)
	HAC_HASH INDEX IDX_LnkIp6Idx1_1(uiOutIfindex, auiPeerIp) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIp6Idx2_2(uiOutIfindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIp6Idx3_3(uiVrId, uiVrfId, auiPeerIp) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIp6Idx4_4(uiVlanIfIndex, auiPeerIp) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIp6Idx5_5(uiVlanIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIp6Idx6_6(uiVrId, uiOutIfindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIp6Idx7_7(uiVrId, uiOutIfindex, auiPeerIp) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T357 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrId UINT32,
	usVlanId UINT16,
	usReserve UINT16,
	uiNodeId UINT32,
	uiPdtInfo UINT32,
	uiMemIfIndex UINT32
	PRIMARY INDEX IDX_VLAN_OUTIF_INDEX0_0(uiVrId, usVlanId)
	HAC_HASH INDEX IDX_VLAN_OUTIF_INDEX1_1(uiMemIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T359 (
	uiVlanIfIndex UINT32,
	uiVrId UINT32,
	usVlanId UINT16,
	usReserve UINT16,
	uiRingIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_VLANIF_INDEX0_0(uiVlanIfIndex)
	HAC_HASH INDEX IDX_VLANIF_INDEX1_1(uiVrId, usVlanId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VLANIF_INDEX2_2(uiVlanIfIndex, uiVrId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VLANIF_INDEX3_3(usVlanId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1713 (
	uiIfIndex UINT32,
	uiGreType UINT32,
	uiNodeGrpID UINT32,
	uiInterfaceNum UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_PDTTARGETIdx0_0(uiIfIndex, uiGreType)
	HAC_HASH INDEX IDX_PDTTARGETIdx1_1(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2153 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiTopologyID UINT32,
	uiNhlfeID UINT32,
	uiAddress UINT32,
	uiLspXcIndex UINT32,
	usTunnelType UINT16,
	usMtu UINT16,
	uiOutIfIndex UINT32,
	uiNextHop UINT32,
	uiOutLabel UINT32,
	uiFVrfIndex UINT32,
	uiIngressToken UINT32,
	uiNodeGrpID UINT32,
	usOutIfType UINT16,
	usTB UINT16,
	usTP UINT16,
	usPathFlag UINT16,
	uiRingIfIndex UINT32,
	ucLspXcType UINT8,
	ucLspXcRole UINT8,
	usReserved2 UINT16,
	uiLsIndex UINT32,
	ucProtoType UINT8,
	aucReserved3 FIXED 3
	PRIMARY INDEX IDX_RLFANHLFEIndex0_0(uiNhlfeID, uiLspXcIndex, uiVrfIndex, uiVrIndex, ucLspXcType)
	HAC_HASH INDEX IDX_RLFANHLFEIndex1_1(uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_RLFANHLFEIndex2_2(uiVrIndex, uiVrfIndex, uiAddress, uiOutIfIndex, uiNextHop) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_RLFANHLFEIndex3_3(uiOutIfIndex, uiLsIndex, uiVrIndex, uiVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_RLFANHLFEIndex4_4(uiVrIndex, uiVrfIndex, uiNhlfeID, ucLspXcType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_RLFANHLFEIndex5_5(uiVrIndex, uiVrfIndex, uiNhlfeID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_RLFANHLFEIndex6_6(uiOutIfIndex, uiNextHop) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_RLFANHLFEIndex7_7(uiVrIndex, uiVrfIndex, uiFVrfIndex, uiLsIndex, ucProtoType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_RLFANHLFEIndex8_8(uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_RLFANHLFEIndex9_9(uiVrIndex, uiVrfIndex, uiLspXcIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2169 (
	uiIfIndex UINT32,
	uiIfUcmpEn UINT32,
	ullBandwidth FIXED 8,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucTnlWeight UINT8,
	ucReserve UINT8,
	usReserve UINT16
	PRIMARY INDEX IDX_UcmpIfBwIndex0_0(uiIfIndex)
);
CREATE VERTEXLABEL T2562 (
	auiNextHop FIXED 16,
	uiOutIfIndex UINT32,
	uiAidIndex UINT32,
	uiNodeGrpID UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_IP6UC_NDINDEX_INDEX_0(auiNextHop, uiOutIfIndex)
	HAC_HASH INDEX IDX_IP6UC_NDINDEX_INDE1_1(auiNextHop) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3027 (
	uiIIDindex UINT32,
	uiVrId UINT32,
	uiTVrfIndex UINT32,
	ucTunnelType UINT8,
	ucReserved0 UINT8,
	usReserved1 UINT16,
	uiTunnelID UINT32,
	uiVnId UINT32,
	aucMac FIXED 6,
	usPathFlag UINT16,
	uiNexthop UINT32,
	uiToken UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	auiNexthopV6 FIXED 16
	PRIMARY INDEX IDX_VXLANVNIIdx0_0(uiIIDindex, uiVrId, uiTVrfIndex, ucTunnelType, uiTunnelID)
	HAC_HASH INDEX IDX_VXLANVNIIdx1_1(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VXLANVNIIdx2_2(uiVrId, uiTunnelID, uiVnId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VXLANVNIIdx3_3(uiVrId, uiTunnelID, ucTunnelType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VXLANVNIIdx4_4(uiVrId, uiVnId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VXLANVNIIdx5_5(uiVrId, uiTunnelID, ucTunnelType, uiVnId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VXLANVNIIdx6_6(uiVrId, uiTVrfIndex, ucTunnelType, uiTunnelID, aucMac) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3183 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiIIDindex UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiDstVpnId UINT32,
	uiFDstVpnId UINT32
	PRIMARY INDEX IDX_P6UCVPNIIDIdx0_0(uiIIDindex, uiVrIndex, uiVrfIndex, uiDstVpnId)
	HAC_HASH INDEX IDX_P6UCVPNIIDIdx1_1(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCVPNIIDIdx2_2(uiVrIndex, uiDstVpnId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3606 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiFVrfIndex UINT32,
	uiPortGroup UINT32,
	usSidFlag UINT16,
	usIpv6HeaderNum UINT16,
	uiToken UINT32,
	auiIpv6Addr1 FIXED 16,
	auiIpv6Addr2 FIXED 16,
	auiIpv6Addr3 FIXED 16,
	auiIpv6Addr4 FIXED 16,
	auiIpv6Addr5 FIXED 16,
	auiIpv6Addr6 FIXED 16,
	auiIpv6Addr7 FIXED 16,
	auiIpv6Addr8 FIXED 16,
	auiIpv6Addr9 FIXED 16,
	auiIpv6Addr10 FIXED 16,
	ucUloopFlag UINT8,
	ucReserved UINT8,
	usReserved UINT16
	PRIMARY INDEX IDX_SRV6SRLISTIndex0_0(uiVrIndex, uiVrfIndex, uiPortGroup, usSidFlag)
	HAC_HASH INDEX IDX_SRV6SRLISTIndex1_1(uiPortGroup) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SRV6SRLISTIndex2_2(uiVrIndex, uiVrfIndex, uiPortGroup) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3952 (
	uiIIDindex UINT32,
	auiNextHop FIXED 16,
	uiOutIfIndex UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiIIDFlags UINT32,
	uiUserInfo UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_P6UCBRASIIDIdx0_0(uiIIDindex, auiNextHop, uiOutIfIndex)
	HAC_HASH INDEX IDX_P6UCBRASIIDIdx1_1(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCBRASIIDIdx2_2(uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCBRASIIDIdx3_3(auiNextHop, uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCBRASIIDIdx4_4(uiUserInfo) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3988 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiFVrfIndex UINT32,
	auiXSid FIXED 16,
	ucXSidMaskLen UINT8,
	ucState UINT8,
	ucFlag UINT8,
	ucNhpType UINT8,
	uiPrefixIid UINT32,
	auiNhp FIXED 16,
	usPathFlags UINT16,
	ucType UINT8,
	ucResv UINT8,
	primaryIID UINT32
	PRIMARY INDEX IDX_VPNSIDNHP0_0(uiVrIndex, uiVrfIndex, auiXSid, ucXSidMaskLen, auiNhp, ucType)
	HAC_HASH INDEX IDX_VPNSIDNHP1_1(uiVrIndex, uiVrfIndex, auiXSid, ucXSidMaskLen, ucType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPNSIDNHP2_2(uiPrefixIid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPNSIDNHP3_3(uiVrIndex, uiPrefixIid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPNSIDNHP4_4(uiVrIndex, auiXSid, ucXSidMaskLen) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPNSIDNHP5_5(uiVrIndex, uiVrfIndex, auiXSid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPNSIDNHP6_6(uiVrIndex, uiVrfIndex, auiNhp) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPNSIDNHP7_7(uiVrIndex, uiVrfIndex, ucXSidMaskLen, auiXSid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T4003 (
	uiIIDindex UINT32,
	uiNextHop UINT32,
	uiOutIfIndex UINT32,
	uiDestAddr UINT32,
	ucTunnelType UINT8,
	ucReserved0 UINT8,
	usReserved1 UINT16,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_Idx0_0(uiIIDindex, uiNextHop, uiOutIfIndex, uiDestAddr, ucTunnelType)
	HAC_HASH INDEX IDX_Idx1_1(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_Idx2_2(uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_Idx3_3(uiVrIndex, uiVrfIndex, uiDestAddr, uiOutIfIndex, uiNextHop) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T4011 (
	uiVrIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucSrv6Enable UINT8,
	aucReserved1 FIXED 3,
	ucSrTeFrrEnable UINT8,
	aucReserved2 FIXED 3,
	auiSourceAddr FIXED 16,
	ucIpTtl UINT8,
	aucReserved3 FIXED 3,
	auiSecondarySid FIXED 16,
	ucSecondaryFlag UINT8,
	aucReserved FIXED 3,
	uiSecSidToken UINT32,
	ucMirrorEnable UINT8,
	aucReserved4 FIXED 3,
	usPathMtu UINT16,
	usPathMtuComp UINT16,
	ucReduceEnable UINT8,
	aucReserved5 FIXED 3
	PRIMARY INDEX IDX_SRV6COMMONIndex0_0(uiVrIndex)
);
CREATE VERTEXLABEL T4378 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiIIDindex UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiFVrfIndex UINT32,
	auiVpnSid FIXED 16,
	auiXSid FIXED 16,
	ucVpnSidMaskLen UINT8,
	ucXSidMaskLen UINT8,
	ucType UINT8,
	ucReserved UINT8,
	uiToken UINT32,
	uiToken2 UINT32,
	uiTVrfIndex UINT32
	PRIMARY INDEX IDX_IID_VPNSID6_INDEX0_0(uiIIDindex, uiVrIndex, uiVrfIndex, auiVpnSid)
	HAC_HASH INDEX IDX_IID_VPNSID6_INDEX1_1(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IID_VPNSID6_INDEX2_2(uiVrIndex, uiTVrfIndex, auiXSid, ucXSidMaskLen, ucType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IID_VPNSID6_INDEX3_3(uiVrIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T4479 (
	uiIIDindex UINT32,
	uiVrIndex UINT32,
	uiTVrfIndex UINT32,
	uiFVrfIndex UINT32,
	ucTunnelType UINT8,
	ucFlag UINT8,
	usPathFlag UINT16,
	uiTunnelId UINT32,
	uiVrfIndex UINT32,
	auiVpnSid FIXED 16,
	uiToken UINT32,
	uiToken2 UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_P6UCEXTIIDSRV6TEIdx0_0(uiIIDindex, uiVrIndex, uiTVrfIndex, ucTunnelType, uiTunnelId, auiVpnSid)
	HAC_HASH INDEX IDX_P6UCEXTIIDSRV6TEIdx1_1(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCEXTIIDSRV6TEIdx2_2(uiVrIndex, uiTVrfIndex, hSrcPid, uiVerNo) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCEXTIIDSRV6TEIdx3_3(uiVrIndex, uiTVrfIndex, ucTunnelType, uiTunnelId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCEXTIIDSRV6TEIdx4_4(uiVrIndex, uiTVrfIndex, uiTunnelId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P6UCEXTIIDSRV6TEIdx5_5(uiVrIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T4492 (
	uiVsId UINT32,
	uiNextHopAddr UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiPeerIndex UINT32,
	auiMapAddr FIXED 16
	PRIMARY INDEX IDX_NexthopKey_0(uiVsId, uiNextHopAddr)
	HAC_HASH INDEX IDX_NexthopKe1_1(uiNextHopAddr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_NexthopKe2_2(auiMapAddr) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T4526 (
	uivrIndex UINT32,
	uiTTLModeType UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_SRV6TTLIndex0_0(uivrIndex)
);
CREATE VERTEXLABEL T4682 (
	uiIfindex UINT32,
	ucFlag UINT8,
	ucResv UINT8,
	usRes UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_uiIfindex_0(uiIfindex)
);
CREATE VERTEXLABEL T16 (
	uiIfIndex UINT32,
	aucMAC FIXED 6,
	usReserved UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_MACIndex0_0(uiIfIndex)
);
CREATE VERTEXLABEL T261 (
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	ucTtlMode UINT8,
	aucReserved FIXED 3,
	uiFVrfIndex UINT32,
	uiVerNo UINT32,
	hSrcPid UINT32
	PRIMARY INDEX IDX_TTLIndex0_0(uiVrIndex, uiVrfIndex)
);
CREATE VERTEXLABEL T408 (
	uidevID UINT32,
	uiNodeID UINT32,
	uiNodeType UINT32,
	uiNodeGrpID UINT32,
	uiNodeGrpType UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_NODE_INFO_Index_0(uiNodeID)
	HAC_HASH INDEX IDX_NODEGRP_Index_1(uiNodeGrpID) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T419 (
	ucLigIndex UINT8,
	aucResLig FIXED 3,
	aucSrcAddr FIXED 16,
	usSrcAddrLen UINT16,
	usSrcAddrType UINT16,
	aucDestAddr FIXED 16,
	usDestAddrLen UINT16,
	usDestAddrType UINT16,
	usLigSrcPort UINT16,
	usLigDstPort UINT16,
	ucLigDscp UINT8,
	aucRes FIXED 3,
	uiVrId UINT32,
	uiLigVrfIndex UINT32,
	uiFvrfId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	aucLabel FIXED 10,
	ucGreKeyFlag UINT8,
	ucGreFlag UINT8,
	uiGreKey UINT32,
	uiAIBMEToken UINT32,
	uiAIBUDPToken UINT32,
	uiAIBIPToken UINT32,
	usIsPostfixSent UINT16,
	usReserve UINT16
	PRIMARY INDEX IDX_INDEX0_0(ucLigIndex)
	HAC_HASH INDEX IDX_INDEX1_1(uiGreKey) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1477 (
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	ucTtlMode UINT8,
	aucReserved FIXED 3,
	uiFVrfIndex UINT32,
	uiVerNo UINT32,
	hSrcPid UINT32
	PRIMARY INDEX IDX_TTLIndex0_0(uiVrIndex, uiVrfIndex)
);
CREATE VERTEXLABEL T1999 (
	uiUserMagic UINT32,
	uiCid UINT32,
	uiHalIndex UINT32,
	ucDirection UINT8,
	ucIpFamily UINT8,
	ucLigIndex UINT8,
	ucIsDftDeny UINT8,
	uiStreamLiId UINT32,
	uiSessionId UINT32,
	aucAccountID FIXED 44,
	uiVrId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiGroupId UINT32,
	uiChnKey UINT32
	PRIMARY INDEX IDX_StreamBas_Index0_0(uiHalIndex, uiVrId, uiChnKey)
	HAC_HASH INDEX IDX_StreamBas_Index1_1(uiCid, uiUserMagic) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_StreamBas_Index2_2(uiCid, uiUserMagic, ucIpFamily) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_StreamBas_Index3_3(ucLigIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2012 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiIpAddr UINT32,
	uiGateWayIP UINT32,
	uiUserVrIndex UINT32,
	uiUserVrfIndex UINT32,
	uiUserFVrfIndex UINT32,
	uiCGNInLabel UINT32,
	uiMaskLen UINT32,
	ucFramedRoute UINT8,
	ucUrpfFlag UINT8,
	ucTTLBase UINT8,
	ucMssEnable UINT8,
	ucL2NATAware UINT8,
	ucForceFlag UINT8,
	ucEchoEnable UINT8,
	ucEchoCos UINT8,
	ucRes1 UINT8,
	ucRes2 UINT8,
	usMssValue UINT16,
	usMTU UINT16,
	usRes2 UINT16,
	uiInboundVrfid UINT32,
	uiVTIfIndex UINT32,
	uiIntfVrfIndex UINT32
	PRIMARY INDEX IDX_BRAS_USER_IPV4_INFOInde_6(uiUserCid)
	HAC_HASH INDEX IDX_BRAS_USER_IPV4_INFOInde_0(uiUserCid, uiUserMagic) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV4_INFOInde_1(uiUserVrIndex, uiUserVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV4_INFOInde_2(uiUserVrIndex, uiInboundVrfid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV4_INFOInde_3(uiUserVrIndex, uiIntfVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV4_INFOInde_4(uiVTIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV4_INFOInde_5(uiUserVrfIndex, uiVTIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV4_INFOInde_7(uiUserVrIndex, uiUserVrfIndex, uiIpAddr) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2013 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiUserVrIndex UINT32,
	uiUserVrfIndex UINT32,
	aucLanAddr FIXED 16,
	aucWanAddr FIXED 16,
	ucLanMaskLen UINT8,
	ucWanMaskLen UINT8,
	ucFramedRoute UINT8,
	ucCheckIpv6 UINT8,
	ucNdEchoEnable UINT8,
	ucNdEchoCos UINT8,
	usMTU UINT16,
	szInterfaceID FIXED 8,
	PrefixUnshared UINT8,
	ucRes FIXED 3,
	uiResv UINT32,
	uiInboundVrfid UINT32,
	uiVTIfIndex UINT32
	PRIMARY INDEX IDX_BRAS_USER_IPV6_INFOInde_5(uiUserCid)
	HAC_HASH INDEX IDX_BRAS_USER_IPV6_INFOInde_0(uiUserCid, uiUserMagic) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV6_INFOInde_1(uiUserVrIndex, uiUserVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV6_INFOInde_2(uiUserVrIndex, uiInboundVrfid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV6_INFOInde_3(uiVTIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV6_INFOInde_4(uiUserVrfIndex, uiVTIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV6_INFOInde_6(uiUserVrIndex, uiUserVrfIndex, aucLanAddr, ucLanMaskLen) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_USER_IPV6_INFOInde_7(uiUserVrIndex, uiUserVrfIndex, aucWanAddr, ucWanMaskLen) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2234 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiVTIndex UINT32,
	uiMagicNum UINT32,
	uiSessionId UINT32,
	ucEchoEnable UINT8,
	ucEchoCos UINT8,
	ucFlowCheckFlag UINT8,
	ucRes UINT8,
	usResponseTime UINT16,
	usRes UINT16,
	uiCliMagNum UINT32,
	uiVTIfIndex UINT32
	PRIMARY INDEX IDX_BRAS_USER_PPPOE_INFOInd_0(uiUserCid, uiUserMagic)
);
CREATE VERTEXLABEL T2358 (
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiNextHopIp UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_USER_IPV4_POLICY_R_0(uiUserCid, uiUserMagic)
);
CREATE VERTEXLABEL T2359 (
	uiUserCid UINT32,
	uiUserMagic UINT32,
	aucAddr FIXED 16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_USER_IPV6_POLICY_R_0(uiUserCid, uiUserMagic)
);
CREATE VERTEXLABEL T2741 (
	LocalTunnelID UINT32,
	TVrIndex UINT32,
	TVrfIndex UINT32,
	TFVrfIndex UINT32,
	LocalIP UINT32,
	PeerIp UINT32,
	IpTos UINT8,
	Res1 UINT8,
	Mtu UINT16,
	SrcUDPPort UINT16,
	DstUDPPort UINT16,
	PeerTunnelID UINT16,
	Res2 UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32,
	TunnelIDNew UINT16,
	Res3 UINT16
	PRIMARY INDEX IDX_LAC_TUNNEL_ENCAP_INDEX0_0(LocalTunnelID, TVrIndex)
	HAC_HASH INDEX IDX_LAC_TUNNEL_ENCAP_INDEX1_1(TunnelIDNew) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LAC_TUNNEL_ENCAP_INDEX2_2(TVrIndex, TVrfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2746 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	LocalTunnelID UINT16,
	Res1 UINT16,
	TVrIndex UINT32,
	TVrfIndex UINT32,
	TFVrfIndex UINT32,
	LocalIP UINT32,
	PeerIp UINT32,
	SrcUDPPort UINT16,
	DstUDPPort UINT16,
	PeerTunnelID UINT16,
	LnsMcV4StatEn UINT8,
	CUFlag UINT8
	PRIMARY INDEX IDX_LNS_TUNNEL_INDEX0_0(LocalTunnelID)
	HAC_HASH INDEX IDX_LNS_TUNNEL_INDEX1_1(TVrIndex, TVrfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2855 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiIfIndex UINT32,
	uiUserGroupId UINT32,
	uiVrID UINT32,
	ucAccessMode UINT8,
	ucAccountType UINT8,
	uc4CAR8CARFlag UINT8,
	ucDaaSeperFlag UINT8,
	ucL3CAR UINT8,
	ucVlanType UINT8,
	ucReportSqFlg UINT8,
	ucStatVlanEx UINT8,
	uiSn UINT32,
	ucFeInfo UINT8,
	isTrunkorPwif UINT8,
	ucOperType UINT8,
	ucUserIpType UINT8,
	usOutVlan UINT16,
	usInnerVlan UINT16,
	usflag UINT16,
	ucBlockFlow UINT8,
	ucResv UINT8
	PRIMARY INDEX IDX_BRAS_LLINE_USER_BASICIn_0(uiUserCid, uiUserMagic)
	HAC_HASH INDEX IDX_BRAS_LLINE_USER_BASICIn_1(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3407 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiSrcIP UINT32,
	uiDstIP UINT32,
	usVpnId UINT16,
	ucRes UINT16
	PRIMARY INDEX IDX_SOFTGRE_TUNNEL_INDEX0_0(uiUserCid, uiUserMagic)
);
CREATE VERTEXLABEL T28 (
	uiIfIndex UINT32,
	usTrunkId UINT16,
	ucIsTrunk UINT8,
	ucHashType UINT8,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucWorkMode UINT8,
	ucWorkState UINT8,
	ucRvtMode UINT8,
	ucRemoteTrunk UINT8,
	uiMinActiveNum UINT32,
	usResFlag UINT16,
	usReserved UINT16
	PRIMARY INDEX IDX_TRUNKINFIndex0_0(uiIfIndex)
	HAC_HASH INDEX IDX_TRUNKINFIndex1_1(usTrunkId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T950 (
	uiVrId UINT32,
	uivrfId UINT32,
	uiFvrfId UINT32,
	auiSourceV6Addr FIXED 16,
	auiGroupV6addr FIXED 16,
	uiTmGid UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_MFIB6_PDT_INDEX_0(uiVrId, uivrfId, auiSourceV6Addr, auiGroupV6addr)
	HAC_HASH INDEX IDX_MFIB6_PDT_INDE1_1(uiVrId, uivrfId, auiGroupV6addr, auiSourceV6Addr, uiFvrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_MFIB6_PDT_INDE2_2(uiTmGid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2615 (
	uiVrId UINT32,
	uiIfIndex UINT32,
	ucCopyByMode UINT8,
	ucRes0 UINT8,
	ucRes1 UINT8,
	ucRes2 UINT8,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_L3MC_BAS_COPY_MODEIndex_0(uiVrId, uiIfIndex)
	HAC_HASH INDEX IDX_L3MC_BAS_COPY_MODEIndex_1(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2696 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	auiGroupAddr FIXED 16,
	auiSourceAddr FIXED 16,
	ucGrpMaskLen UINT8,
	ucSrcMaskLen UINT8,
	usRsv1 UINT16,
	uiIfIndex UINT32,
	uiUserId UINT32,
	uiNodeId UINT32,
	uiFVrfIndex UINT32
	PRIMARY INDEX IDX_L3MC_BAS_USERID_ELB6Ind_0(uiVrIndex, uiVrfIndex, auiGroupAddr, auiSourceAddr, uiIfIndex, uiUserId)
	HAC_HASH INDEX IDX_L3MC_BAS_USERID_ELB6Ind_1(uiVrIndex, uiVrfIndex, auiGroupAddr, auiSourceAddr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_L3MC_BAS_USERID_ELB6Ind_2(uiUserId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_L3MC_BAS_USERID_ELB6Ind_3(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2775 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	ucMultIsForward UINT8,
	ucIgmpEnable UINT8,
	usResv UINT16,
	uiMultiMinPir UINT32,
	uiMultiMinCir UINT32,
	uiMcScheFvrfId UINT32,
	OutFamilyid UINT32,
	OutFamilyMagic UINT32
	PRIMARY INDEX IDX_BRAS_L3MCIndex0_0(uiUserCid, uiUserMagic)
	HAC_HASH INDEX IDX_BRAS_L3MCIndex1_1(uiUserCid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1872 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiGroupAddr UINT32,
	uiSourceAddr UINT32,
	uiIfIndex UINT32,
	uiUserId UINT32,
	uiNodeId UINT32,
	uiFVrfIndex UINT32
	PRIMARY INDEX IDX_L3MC_BAS_USERID_ELB4Ind_0(uiVrIndex, uiVrfIndex, uiGroupAddr, uiSourceAddr, uiIfIndex, uiUserId)
	HAC_HASH INDEX IDX_L3MC_BAS_USERID_ELB4Ind_1(uiUserId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_L3MC_BAS_USERID_ELB4Ind_2(uiVrIndex, uiVrfIndex, uiGroupAddr, uiSourceAddr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_L3MC_BAS_USERID_ELB4Ind_3(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2761 (
	uiVrId UINT32,
	uiVrfindex UINT32,
	uiGroupIpAddr UINT32,
	uiSourceIpAddr UINT32,
	ullBandWidth FIXED 8,
	ucFlag UINT8,
	ucResv UINT8,
	usResv1 UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_USER_MC_BANDIndex0_0(uiVrId, uiVrfindex, uiGroupIpAddr, uiSourceIpAddr)
	HAC_HASH INDEX IDX_BRAS_USER_MC_BANDIndex1_1(uiVrId, uiVrfindex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T301 (
	uiIfIndex UINT32,
	usTB UINT16,
	usTP UINT16,
	uiPhyIfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_PORTIFIndex0_0(usTB, usTP, uiPhyIfIndex)
	HAC_HASH INDEX IDX_PORTIFIndex1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PORTIFIndex2_2(uiIfIndex, uiPhyIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PORTIFIndex3_3(uiPhyIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PORTIFIndex4_4(usTB, usTP) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PORTIFIndex5_5(uiIfIndex, usTB, usTP) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T410 (
	uiDevID UINT32,
	uiFeNodeID UINT32,
	uiPortGrpID UINT32,
	usPortTP UINT16,
	ucFWEID UINT8,
	ucTMID UINT8,
	ucResFlag UINT8,
	ucRsv1 UINT8,
	usTB UINT16,
	usTP UINT16,
	usRsv2 UINT16,
	uiFeNodeGrpID UINT32,
	ucCardNo UINT8,
	ucPortNo UINT8,
	usPortType UINT16,
	usIngressIndex UINT16,
	usOutChannel UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32,
	usLocalTB UINT16,
	usLocalTP UINT16,
	usChassisID UINT16,
	usReserve UINT16,
	uiFe2TmIL UINT32,
	uiCardType UINT32,
	uiSerialNo UINT32,
	ucSubChannel UINT8,
	ucRsv3 UINT8,
	usRsv4 UINT16,
	usInChannel UINT16,
	usTMIL UINT16,
	usPhyTB UINT16,
	usPhyTP UINT16,
	uiTag UINT32,
	uiIgresInMainChl UINT32,
	uiEgresOutMainChl UINT32
	PRIMARY INDEX IDX_PORT_PDTINFO_INDEX0_0(uiFeNodeID, usPortTP, ucFWEID, ucTMID, usTB, usTP, ucCardNo, ucPortNo)
	HAC_HASH INDEX IDX_PORT_PDTINFO_INDEX1_1(usTB, usTP) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PORT_PDTINFO_INDEX2_2(uiCardType, ucCardNo, ucPortNo) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PORT_PDTINFO_INDEX3_3(usPhyTB, usPhyTP) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PORT_PDTINFO_INDEX4_4(usLocalTB, usLocalTP, usChassisID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PORT_PDTINFO_INDEX5_5(uiFeNodeID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PORT_PDTINFO_INDEX6_6(uiCardType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PORT_PDTINFO_INDEX7_7(ucFWEID) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T803 (
	uiFlowIndex UINT32,
	uiIfIndex UINT32,
	uiVrId UINT32,
	uiMatchType UINT32,
	ucIsLocalSwitch UINT8,
	ucIsRtProtocol UINT8,
	ucIsDynamic UINT8,
	ucIsSymmetric UINT8,
	uiSubIfType UINT32,
	ucIsUserMode UINT8,
	ucIsTransMode UINT8,
	aucReserved FIXED 2,
	uiToken UINT32,
	uiNodeID UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_QVFLOW_INDEX0_0(uiFlowIndex)
	HAC_HASH INDEX IDX_QVFLOW_INDEX1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QVFLOW_INDEX2_2(uiFlowIndex, uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QVFLOW_INDEX3_3(uiVrId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2172 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiFlowIndex UINT32,
	uiIfIndex UINT32,
	uiSegIndex UINT32,
	usPeVlan UINT16,
	usCeVlan UINT16,
	uiVrID UINT32,
	uiMainIfIndex UINT32,
	ucIfBrasFlag UINT8,
	ucReserved UINT8,
	ucReserved1 UINT8,
	ucReserved2 UINT8,
	uiNodeGrpID UINT32
	PRIMARY INDEX IDX_BRAS_QVCT_INFOIndex0_0(uiIfIndex, usPeVlan, usCeVlan, uiVrID)
	HAC_HASH INDEX IDX_BRAS_QVCT_INFOIndex1_1(uiIfIndex, usPeVlan, usCeVlan) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_QVCT_INFOIndex2_2(uiMainIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_QVCT_INFOIndex3_3(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2799 (
	uiIfIndex UINT32,
	usTB UINT16,
	usTP UINT16,
	uiNodeGrpID UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_PWIF_NODE_CHOICE_INFO_I_0(usTB, usTP, uiNodeGrpID)
	HAC_HASH INDEX IDX_PWIF_NODE_CHOICE_INFO_I_1(usTB, usTP) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2866 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiMainIfIndex UINT32,
	uiIfIndex UINT32,
	uiVrId UINT32,
	uiVrfId UINT32,
	uiNodeGrpID UINT32,
	uiGWIpAddr UINT32,
	ucPimV4RdEn UINT8,
	aucRes FIXED 3
	PRIMARY INDEX IDX_BRAS_PIMV4_USER_INFOInd_0(uiUserCid, uiUserMagic, uiNodeGrpID)
	HAC_HASH INDEX IDX_BRAS_PIMV4_USER_INFOInd_1(uiUserCid, uiUserMagic) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_PIMV4_USER_INFOInd_2(uiIfIndex, uiVrId, uiVrfId, uiGWIpAddr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_PIMV4_USER_INFOInd_3(uiMainIfIndex, uiVrId, uiVrfId, uiGWIpAddr) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2893 (
	uiVrId UINT32,
	uiVrfId UINT32,
	uiIfIndex UINT32,
	uiSourceIp UINT32,
	usUserType UINT16,
	aucRes FIXED 2,
	uiTmgid UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_HWMCAST_PDT_INDEX0_0(uiVrId, uiVrfId, uiIfIndex, uiSourceIp, usUserType)
	HAC_HASH INDEX IDX_HWMCAST_PDT_INDEX1_1(uiIfIndex, uiVrId, uiVrfId, uiSourceIp) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_HWMCAST_PDT_INDEX2_2(uiIfIndex, uiVrId, uiVrfId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T7 (
	hSrcPid UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiVerNo UINT32,
	uiFVrfIndex UINT32,
	uiDestAddr UINT32,
	ucMaskLen UINT8,
	ucIIDGFlag UINT8,
	usRouteFlags UINT16,
	uiPathFlags UINT32,
	uiIIDindex UINT32,
	uiPrimaryLabel UINT32,
	uiAttributeId UINT32,
	usQosid UINT16,
	usRes UINT16
	PRIMARY INDEX IDX_PP4UCRTIndex0_0(uiDestAddr, ucMaskLen, uiVrfIndex, uiVrIndex)
	HAC_HASH INDEX IDX_PP4UCRTIndex1_1(uiIIDindex, uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UCRTIndex2_2(uiDestAddr, uiVrIndex, uiVrfIndex, ucMaskLen, uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UCRTIndex3_3(uiAttributeId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UCRTIndex4_4(uiVrIndex, uiAttributeId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UCRTIndex5_5(usQosid, uiVrIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T10 (
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiTopologyID UINT32,
	uiFVrfIndex UINT32,
	uiIIDindex UINT32,
	uiNHop UINT32,
	ucNhpNum UINT8,
	ucNhpFlag UINT8,
	usValidFlag UINT16,
	uiIIDFlags UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	usValidFlag1 UINT16,
	usReserved UINT16
	PRIMARY INDEX IDX_PP4UCIIDIndex0_0(uiVrIndex, uiIIDindex)
	HAC_HASH INDEX IDX_PP4UCIIDIndex1_1(uiVrIndex, uiVrfIndex, uiNHop) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UCIIDIndex2_2(uiVrIndex, uiVrfIndex, uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UCIIDIndex3_3(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UCIIDIndex4_4(uiNHop) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T246 (
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiDestAddr UINT32,
	ucMaskLen UINT8,
	ucReserved1 UINT8,
	usReserved2 UINT16,
	uiIIDindex UINT32,
	uiPathflag UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_IP4HOSTRTIdx0_0(uiVrIndex, uiVrfIndex, uiDestAddr, ucMaskLen)
	HAC_HASH INDEX IDX_IP4HOSTRTIdx1_1(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IP4HOSTRTIdx2_2(uiVrIndex, uiVrfIndex, uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IP4HOSTRTIdx3_3(uiVrIndex, uiVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IP4HOSTRTIdx4_4(uiVrIndex, uiVrfIndex, uiDestAddr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IP4HOSTRTIdx5_5(uiIIDindex, uiVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IP4HOSTRTIdx6_6(uiVrIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2747 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	TVrIndex UINT32,
	TVrfIndex UINT32,
	TFVrfIndex UINT32,
	IpAddr UINT32,
	LnsGroupID UINT8,
	LnsFlag UINT8,
	Res1 UINT16
	PRIMARY INDEX IDX_LNS_FIB_INDEX0_0(TVrIndex, TVrfIndex, IpAddr)
);
CREATE VERTEXLABEL T2756 (
	uiVrId UINT32,
	uiGroupId UINT32,
	uiGroupType UINT32,
	usReserve UINT16,
	usFlag UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_Index0_0(uiVrId, uiGroupId, uiGroupType)
	HAC_HASH INDEX IDX_Index1_1(uiGroupId, uiGroupType) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2837 (
	InstanceIndex UINT8,
	InstanceType UINT8,
	MaskLength UINT8,
	IIdIdx UINT8,
	IpAddr UINT32,
	VpnIndex UINT32,
	uiVrId UINT32,
	IIdIndex UINT32,
	ResS UINT16,
	PathFlag UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_CGN_EasyIpInfo_0(InstanceIndex, InstanceType)
	HAC_HASH INDEX IDX_CGN_EasyIpInf1_1(uiVrId, VpnIndex, IpAddr, MaskLength) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_CGN_EasyIpInf2_2(uiVrId, VpnIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_CGN_EasyIpInf3_3(InstanceIndex, InstanceType, IIdIdx) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3059 (
	uiVrId UINT32,
	uiIfIndex UINT32,
	szPolicyName FIXED 16,
	uiTunnelIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_SMSTATEIndex0_0(uiVrId, uiIfIndex, szPolicyName)
	HAC_HASH INDEX IDX_SMSTATEIndex1_1(uiVrId, uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3105 (
	uiVrId UINT32,
	uiLocalIpAddr UINT32,
	uiInVrfId UINT32,
	uiLocalIpIndex UINT32,
	ucLocalIpFlag UINT8,
	aucRsv FIXED 3,
	uiIPSecInstanceId UINT32,
	uiGroupId UINT32,
	uiGroupType UINT32,
	uiTunnelIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_SMSTATEIndex0_0(uiVrId, uiLocalIpAddr, uiInVrfId)
	HAC_HASH INDEX IDX_SMSTATEIndex1_1(uiGroupId, uiGroupType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SMSTATEIndex2_2(uiVrId, uiLocalIpIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3281 (
	uiVrID UINT32,
	uiVpnIndex UINT32,
	uiDestAddr UINT32,
	ucMaskLen UINT8,
	ucReserved UINT8,
	usOportInf UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32,
	SASERVIPFlag UINT16,
	usReserved UINT16
	PRIMARY INDEX IDX_ServIPInfo_0(uiVrID, uiVpnIndex, uiDestAddr)
	HAC_HASH INDEX IDX_ServIPInf1_1(uiVrID, uiVpnIndex, uiDestAddr, ucMaskLen) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_ServIPInf2_2(uiDestAddr) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3375 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrId UINT32,
	uiVpnIndex UINT32,
	uiDestAddr UINT32,
	ucMaskLen UINT8,
	ucReserved1 UINT8,
	usReserved2 UINT16,
	uiAttributeID UINT32
	PRIMARY INDEX IDX_BROKER_UNR_GATEWAY_INFO_0(uiVrId, uiVpnIndex, uiDestAddr, ucMaskLen)
	HAC_HASH INDEX IDX_BROKER_UNR_GATEWAY_INFO_1(uiVrId, uiVpnIndex, uiDestAddr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BROKER_UNR_GATEWAY_INFO_2(uiVrId, uiVpnIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3406 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	TVrIndex UINT32,
	TVrfIndex UINT32,
	TFVrfIndex UINT32,
	IpAddr UINT32,
	SoftGreGroupID UINT8,
	SoftGreFlag UINT8,
	Res1 UINT16
	PRIMARY INDEX IDX_SOFTGRE_FIB_INDEX0_0(TVrIndex, TVrfIndex, IpAddr)
);
CREATE VERTEXLABEL T2739 (
	hSubCompPid UINT32,
	uiUserCid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_Index0_0(hSubCompPid, uiUserCid)
	HAC_HASH INDEX IDX_Index1_1(uiUserCid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2362 (
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiIpv4Addr UINT32,
	uiSessGroupCId UINT32,
	uiSessGrpMag UINT32,
	ucUserIpType UINT8,
	ucRes1 UINT8,
	aucMAC FIXED 6,
	usInnerVlan UINT16,
	usOutVlan UINT16,
	ucInVlanPri UINT8,
	ucOutVlanPri UINT8,
	usRes UINT16,
	uiSn UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_TERM_BASIC_INFOInd_0(uiUserCid, uiUserMagic)
	HAC_HASH INDEX IDX_BRAS_TERM_BASIC_INFOInd_1(uiUserCid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_TERM_BASIC_INFOInd_2(uiSessGroupCId, uiSessGrpMag) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_TERM_BASIC_INFOInd_3(usInnerVlan, usOutVlan, aucMAC) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2777 (
	hSubCompPid UINT32,
	usInnerVlan UINT16,
	usOuterVlan UINT16,
	aucMac FIXED 6,
	usReserv UINT16,
	uiPortGroup UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_Index0_0(hSubCompPid, usInnerVlan, usOuterVlan, aucMac, uiPortGroup)
	HAC_HASH INDEX IDX_Index1_1(usInnerVlan, usOuterVlan, aucMac) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T917 (
	uiVrID UINT32,
	uiTemplateID UINT32,
	ucDirection UINT8,
	aucReserved FIXED 3,
	uiSlotID UINT32,
	iValue UINT32,
	uiFeNodeID UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiNodeGrpID UINT32,
	ucIsconfig UINT8,
	ucReserved UINT8,
	usReserved UINT16,
	iEtmValue UINT32
	PRIMARY INDEX IDX_QOSSevTemplateIdx0_0(uiTemplateID, ucDirection)
	HAC_HASH INDEX IDX_QOSSevTemplateIdx1_1(uiTemplateID) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T985 (
	uiFlowWredID UINT32,
	uiColor UINT32,
	ucLowLimitPct UINT8,
	ucHighLimitPct UINT8,
	ucDiscardPct UINT8,
	ucReserved UINT8,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_QOSFWIdx0_0(uiFlowWredID, uiColor)
	HAC_HASH INDEX IDX_QOSFWIdx1_1(uiFlowWredID) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T986 (
	uiFlowWredID UINT32,
	uiQueueDepth UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_QOSQDEPTHIdx0_0(uiFlowWredID)
);
CREATE VERTEXLABEL T987 (
	uiFlowMappingID UINT32,
	uiFQSrClass UINT32,
	uiCQSrClass UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_QOSFMIdx0_0(uiFlowMappingID, uiFQSrClass)
	HAC_HASH INDEX IDX_QOSFMIdx1_1(uiFlowMappingID) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T989 (
	uiGQID UINT32,
	uiPir UINT32,
	uiPbs UINT32,
	ucDirection UINT8,
	ucShapEnable UINT8,
	usReserved UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucGqMode UINT8,
	ucPriId UINT8,
	ucWeight UINT8,
	ucCirEnable UINT8,
	uiCirValue UINT32,
	uiCbsSize UINT32
	PRIMARY INDEX IDX_QOSGQSHAPIdx0_0(uiGQID, ucDirection, ucGqMode, ucPriId)
	HAC_HASH INDEX IDX_QOSGQSHAPIdx1_1(uiGQID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QOSGQSHAPIdx2_2(uiGQID, ucDirection) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2433 (
	uiKeyID UINT32,
	uiMagic UINT32,
	uiCir UINT32,
	uiCbs UINT32,
	uiPir UINT32,
	uiPbs UINT32,
	uiServiceTempId UINT32,
	uiFlowMappingID UINT32,
	ucDirection UINT8,
	ucWeight UINT8,
	ucStBaseAdjPkt UINT8,
	cLinkOverHead UINT8,
	ucLinkMode UINT8,
	ucAutoOverHead UINT8,
	ucUserQueueType UINT8,
	ucOverrideEn UINT8,
	ucTmSchdEn UINT8,
	ucGQIdfType UINT8,
	usGQGroupID UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiGQTempID UINT32,
	uiQueueMapID UINT32
	PRIMARY INDEX IDX_BRAS_QOS_SQIndex0_0(uiKeyID, uiMagic, ucDirection)
	HAC_HASH INDEX IDX_BRAS_QOS_SQIndex1_1(uiFlowMappingID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_QOS_SQIndex2_2(uiServiceTempId, ucDirection) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_QOS_SQIndex3_3(uiGQTempID, ucDirection) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2434 (
	uiKeyID UINT32,
	uiMagic UINT32,
	uiCir UINT32,
	uiPir UINT32,
	uiCbs UINT32,
	uiPbs UINT32,
	ucGreenAction UINT8,
	ucYellowAction UINT8,
	ucRedAction UINT8,
	ucColorAware UINT8,
	ucGreenSerCls UINT8,
	ucYellowSerCls UINT8,
	ucRedSerCls UINT8,
	ucGreenColor UINT8,
	ucYellowColor UINT8,
	ucRedColor UINT8,
	ucDirection UINT8,
	ucLayerExclude UINT8,
	ucLayerType UINT8,
	ucAutoOverHead UINT8,
	aucReserved FIXED 2,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_QOS_CARIndex0_0(uiKeyID, uiMagic, ucDirection)
);
CREATE VERTEXLABEL T2435 (
	uiKeyID UINT32,
	uiMagic UINT32,
	uiFqWredId UINT32,
	uiPir UINT32,
	uiPbs UINT32,
	ucDirection UINT8,
	ucServiceClass UINT8,
	ucScheduleType UINT8,
	ucWeight UINT8,
	ucShpEnable UINT8,
	ucLowLatency UINT8,
	ucLowJitte UINT8,
	ucFqCarFlag UINT8,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucFqMode UINT8,
	ucPriId UINT8,
	ucPriWeight UINT8,
	uc4CosMode UINT8,
	ucSQSubPri0 UINT8,
	ucSQSubPri1 UINT8,
	ucSQSubPri2 UINT8,
	ucSQSubPri3 UINT8,
	uiCir UINT32,
	uiCbs UINT32,
	ucCirEnable UINT8,
	ucResered1 UINT8,
	ucResered2 UINT8,
	ucResered3 UINT8
	PRIMARY INDEX IDX_BRAS_QOS_FLOWQUEUEIndex_0(uiKeyID, uiMagic, ucDirection, ucServiceClass)
	HAC_HASH INDEX IDX_BRAS_QOS_FLOWQUEUEIndex_1(uiKeyID, uiMagic, ucDirection) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_QOS_FLOWQUEUEIndex_2(uiFqWredId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2436 (
	uiKeyID UINT32,
	uiMagic UINT32,
	uiPir UINT32,
	uiPbs UINT32,
	ucDirection UINT8,
	ucFQBitMap UINT8,
	ucReserved UINT8,
	ucFQBitMap1 UINT8,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiPir1 UINT32,
	uiPbs1 UINT32,
	ucScheduleType UINT8,
	ucWeight UINT8,
	ucScheduleType1 UINT8,
	ucWeight1 UINT8
	PRIMARY INDEX IDX_BRAS_QOS_SHARESHAPINGIn_0(uiKeyID, uiMagic, ucDirection)
);
CREATE VERTEXLABEL T2438 (
	uiSessionID UINT32,
	uiFamilyID UINT32,
	uiMagic UINT32,
	uiVersion UINT32,
	uiIfIndex UINT32,
	uiOutIfindex UINT32,
	uiMainIfindex UINT32,
	usPeVlanId UINT16,
	usCeVlanId UINT16,
	ucDirection UINT8,
	ucFamilyResType UINT8,
	ucResBehavior UINT8,
	ucIsFamilyMem UINT8,
	ucIsVcpeflag UINT8,
	ucLayerExclude UINT8,
	ucPWifFlag UINT8,
	ucQoSLabelFlag UINT8,
	uiUCMPid UINT32,
	uiTrunkid UINT32,
	ucVLinkMode UINT8,
	ucScheduleType UINT8,
	usTunnelId UINT16,
	uiChannelid UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucResNoChange UINT8,
	ucOutifChFlag UINT8,
	ucBasUserType UINT8,
	ucIfType UINT8,
	ucNpId UINT8,
	uclaclns UINT8,
	ucPriCarEnable UINT8,
	ucAfterCarFlag UINT8,
	ucEtmFlag UINT8,
	ucOldBehavior UINT8,
	usResv2 UINT16
	PRIMARY INDEX IDX_BRAS_QOS_SESSIONIndex0_0(uiSessionID, uiMagic, ucDirection)
	HAC_HASH INDEX IDX_BRAS_QOS_SESSIONIndex1_1(uiMainIfindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_QOS_SESSIONIndex2_2(uiOutIfindex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2509 (
	uiKeyID UINT32,
	uiMagic UINT32,
	uiCir UINT32,
	uiPir UINT32,
	uiCbs UINT32,
	uiPbs UINT32,
	ucGreenAction UINT8,
	ucYellowAction UINT8,
	ucRedAction UINT8,
	ucColorAware UINT8,
	ucGreenSerCls UINT8,
	ucYellowSerCls UINT8,
	ucRedSerCls UINT8,
	ucGreenColor UINT8,
	ucYellowColor UINT8,
	ucRedColor UINT8,
	ucDirection UINT8,
	ucLayerExclude UINT8,
	ucLayerType UINT8,
	aucReserved FIXED 3,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_VCPE_CARIndex0_0(uiKeyID, uiMagic, ucDirection, ucLayerType)
	HAC_HASH INDEX IDX_BRAS_VCPE_CARIndex1_1(uiKeyID, uiMagic, ucDirection) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T176 (
	uiIfIndex UINT32,
	uiNodeID UINT32,
	ucObserveIndex UINT8,
	ucChassisID UINT8,
	usSlotID UINT16,
	ucIfType UINT8,
	aucReserved FIXED 3,
	usTb UINT16,
	usTp UINT16,
	uiFEIIftype UINT32,
	uiToken UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	PopLabel UINT8,
	WithLinkLayer UINT8,
	Reserved2 FIXED 2,
	uiVrid UINT32,
	aucDestMacAddr FIXED 6,
	DestMacAddrFlag UINT8,
	WithoutFilter FIXED 1
	PRIMARY INDEX IDX_QOSPortObsvIdx0_0(ucObserveIndex)
	HAC_HASH INDEX IDX_QOSPortObsvIdx1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QOSPortObsvIdx2_2(ucObserveIndex, uiVrid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T186 (
	uiIfIndex UINT32,
	ucDirection UINT8,
	ucIfType UINT8,
	ucCpuPacket UINT8,
	ucFilterId UINT8,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucFragment UINT8,
	ucReserved FIXED 3,
	uiVrid UINT32
	PRIMARY INDEX IDX_QOSPortMirrIdx0_0(uiIfIndex, ucDirection, ucCpuPacket, ucFragment)
	HAC_HASH INDEX IDX_QOSPortMirrIdx1_1(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T188 (
	uiFeNodeID UINT32,
	acSlotIDStr FIXED 32,
	ucObserveIndex UINT8,
	ucChassisID UINT8,
	usSlotID UINT16,
	uiObserveNodeID UINT32,
	usTb UINT16,
	usTp UINT16,
	uiLocalTb UINT32,
	uiDevID UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiNodeGrpID UINT32
	PRIMARY INDEX IDX_QOSSlotObsvIdx0_0(uiDevID)
	HAC_HASH INDEX IDX_QOSSlotObsvIdx1_1(ucObserveIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QOSSlotObsvIdx2_2(usSlotID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QOSSlotObsvIdx3_3(uiNodeGrpID) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T912 (
	uiIfIndex UINT32,
	ucObserveIndex UINT8,
	ucIfType UINT8,
	aucReserved FIXED 2,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrid UINT32
	PRIMARY INDEX IDX_QOSPortMirToIdx0_0(uiIfIndex)
	HAC_HASH INDEX IDX_QOSPortMirToIdx1_1(ucObserveIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QOSPortMirToIdx2_2(ucObserveIndex, uiVrid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T992 (
	uiIfIndex UINT32,
	uiProfileID UINT32,
	usAppPeVlan UINT16,
	usAppCeVlanBeg UINT16,
	usAppCeVlanEnd UINT16,
	usIsTrunk UINT16,
	uiProGroupID UINT32,
	ucAppVlanType UINT8,
	usIdentifyType UINT16,
	ucDirection UINT8,
	uiGroupID UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucIfType UINT8,
	aucReserved FIXED 3,
	uiNodeGrpID UINT32,
	uibandtype UINT8,
	aucReserved1 FIXED 3
	PRIMARY INDEX IDX_QOSAPPIdx0_0(uiIfIndex, uiProfileID, usAppPeVlan, usAppCeVlanBeg, usAppCeVlanEnd, ucAppVlanType, usIdentifyType, ucDirection)
	HAC_HASH INDEX IDX_QOSAPPIdx1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QOSAPPIdx2_2(uiProfileID, ucDirection) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T993 (
	uiProfileID UINT32,
	uiCarType UINT32,
	uiCir UINT32,
	uiCbs UINT32,
	uiPir UINT32,
	uiPbs UINT32,
	ucDirection UINT8,
	ucGreenAction UINT8,
	ucYellowAction UINT8,
	ucRedAction UINT8,
	uiGreenSerCls UINT32,
	uiYellowSerCls UINT32,
	uiRedSerCls UINT32,
	uiGreenColor UINT32,
	uiYellowColor UINT32,
	uiRedColor UINT32,
	ucColoraware UINT8,
	aucReserved FIXED 3,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucCarLengthType UINT8,
	ucCarLenth UINT8,
	ucPriorityEn UINT8,
	aucReserved1 FIXED 1,
	uiPriTmpltID UINT32,
	ucCirPercent UINT8,
	ucPirPercent UINT8,
	ucIsCbsConfig UINT8,
	ucIsPbsConfig UINT8
	PRIMARY INDEX IDX_QOSGQTEMPIdx0_0(uiProfileID, uiCarType, ucDirection)
	HAC_HASH INDEX IDX_QOSGQTEMPIdx1_1(uiProfileID, ucDirection) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1010 (
	uiIfindex UINT32,
	uiMirInstId UINT32,
	ucDirection UINT8,
	ucCpuPacket UINT8,
	ucFilterId UINT8,
	ucReserved UINT8,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_SEC_MirrorInf0_0(uiIfindex, ucDirection, ucCpuPacket)
	HAC_HASH INDEX IDX_SEC_MirrorInf1_1(uiIfindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SEC_MirrorInf2_2(uiMirInstId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1164 (
	uiIfIndex UINT32,
	ucObserveIndex UINT8,
	ucIfType UINT8,
	aucReserved FIXED 2,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiToken UINT32,
	uiVrid UINT32
	PRIMARY INDEX IDX_QOSMultMirToIdx0_0(uiIfIndex, ucObserveIndex)
	HAC_HASH INDEX IDX_QOSMultMirToIdx1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QOSMultMirToIdx2_2(ucObserveIndex, uiVrid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1165 (
	uiVrId UINT32,
	uiIfIndex UINT32,
	ucReserved1 UINT8,
	ucReserved2 UINT8,
	usReserved UINT16,
	aucPdtInfo FIXED 48,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiTmGid UINT32
	PRIMARY INDEX IDX_MIRRORMGIDIFINDEX_0(uiIfIndex)
	HAC_HASH INDEX IDX_MIRRORMGIDIFINDE1_1(uiTmGid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1323 (
	uiIfIndex UINT32,
	ucPhyState UINT8,
	ucIpv4Enable UINT8,
	ucIpv6Enable UINT8,
	ucReserved UINT8,
	usIPv4MTU UINT16,
	usIPv6MTU UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_MTUIndex0_0(uiIfIndex)
);
CREATE VERTEXLABEL T1791 (
	uiTmGid UINT32,
	uiChassisId UINT32,
	uiFabricmid UINT32,
	auiTbmask FIXED 32,
	uiVcIcBitmap UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_VC_DC_TBMASKINDEX0_0(uiTmGid, uiChassisId)
	HAC_HASH INDEX IDX_VC_DC_TBMASKINDEX1_1(uiTmGid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2079 (
	uiIfIndex UINT32,
	uiFeNodeID UINT32,
	uiBdId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_QOSMIRNODEIdx0_0(uiIfIndex, uiFeNodeID, uiBdId)
	HAC_HASH INDEX IDX_QOSMIRNODEIdx1_1(uiFeNodeID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QOSMIRNODEIdx2_2(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QOSMIRNODEIdx3_3(uiIfIndex, uiBdId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2214 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserID UINT32,
	uiUserMagic UINT32,
	uiVrID UINT32,
	hUcmPid UINT32,
	uiIfIndex UINT32,
	usPeVlan UINT16,
	usCeVlan UINT16,
	uiNodeGrpID UINT32
	PRIMARY INDEX IDX_BRAS_QVCT_USERINFOIndex_0(uiIfIndex, usPeVlan, usCeVlan, uiVrID)
);
CREATE VERTEXLABEL T2432 (
	uiVrId UINT32,
	uiifindex UINT32,
	ucdirection UINT8,
	ucRev UINT8,
	usRev UINT16,
	usceVid UINT16,
	uspeVid UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_QvctMirrorIdx0_0(uiVrId, uiifindex, ucdirection, usceVid, uspeVid)
	HAC_HASH INDEX IDX_QvctMirrorIdx1_1(uiifindex, usceVid, uspeVid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_QvctMirrorIdx2_2(uiifindex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2510 (
	uiIfIndex UINT32,
	uiServIndex UINT32,
	usPeVlanId UINT16,
	usCeVlanId UINT16,
	ucDirection UINT8,
	aucReserved FIXED 3,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiTrunkID UINT32
	PRIMARY INDEX IDX_BRAS_QOS_USERVLAN_PROFI_0(uiIfIndex, usPeVlanId, usCeVlanId, ucDirection, uiTrunkID)
	HAC_HASH INDEX IDX_BRAS_QOS_USERVLAN_PROFI_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_QOS_USERVLAN_PROFI_2(uiIfIndex, usPeVlanId, usCeVlanId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_QOS_USERVLAN_PROFI_3(uiIfIndex, uiServIndex, ucDirection) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_QOS_USERVLAN_PROFI_4(uiIfIndex, usPeVlanId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2515 (
	uiVrID UINT32,
	uiIfIndex UINT32,
	uiServIndex UINT32,
	uiProfileID UINT32,
	ucEachVlan UINT8,
	ucDirection UINT8,
	aucReserved FIXED 2,
	uiMainIfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_QOS_USERVLAN_APPLY_0(uiIfIndex, uiServIndex)
	HAC_HASH INDEX IDX_BRAS_QOS_USERVLAN_APPLY_1(uiIfIndex, uiServIndex, ucDirection) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_QOS_USERVLAN_APPLY_2(uiProfileID, ucDirection) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_QOS_USERVLAN_APPLY_3(uiMainIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T4132 (
	uiIfIndex UINT32,
	ucIpLayer UINT8,
	ucReserved FIXED 3,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrid UINT32
	PRIMARY INDEX IDX_Index_0(uiIfIndex)
);
CREATE VERTEXLABEL T4611 (
	uiIfIndex UINT32,
	uiVrID UINT32,
	uiSliceSize UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_PortMirAttrIdx0_0(uiIfIndex, uiVrID)
	HAC_HASH INDEX IDX_PortMirAttrIdx1_1(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T949 (
	uiVrId UINT32,
	uivrfId UINT32,
	uiFvrfId UINT32,
	uiSourceV4Addr UINT32,
	uiGroupV4Addr UINT32,
	uiTmGid UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiIIfindex UINT32,
	uiFlag UINT32
	PRIMARY INDEX IDX_MFIB4_PDT_INDEX_0(uiVrId, uivrfId, uiSourceV4Addr, uiGroupV4Addr)
	HAC_HASH INDEX IDX_MFIB4_PDT_INDEX1_1(uiFvrfId, uiSourceV4Addr, uiGroupV4Addr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_MFIB4_PDT_INDEX2_2(uiIIfindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_MFIB4_PDT_INDEX3_3(uiVrId, uivrfId, uiGroupV4Addr, uiSourceV4Addr, uiFvrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_MFIB4_PDT_INDEX4_4(uiTmGid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_MFIB4_PDT_INDEX5_5(uiVrId, uivrfId, uiGroupV4Addr, uiSourceV4Addr, uiTmGid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2566 (
	uiIfIndex UINT32,
	uiSrvtype UINT32,
	usPevid UINT16,
	usCevid UINT16,
	uiToken UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_uiIfIndex_0(uiIfIndex, usPevid, usCevid)
	HAC_HASH INDEX IDX_uiIfInde1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_uiIfInde2_2(uiIfIndex, usPevid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2698 (
	uiVrId UINT32,
	uiIfIndex UINT32,
	usPeId UINT16,
	usCeId UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_L3MC_BAS_MVLANIndex0_0(uiVrId, uiIfIndex)
	HAC_HASH INDEX IDX_L3MC_BAS_MVLANIndex1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_L3MC_BAS_MVLANIndex2_2(uiIfIndex, usPeId, usCeId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_L3MC_BAS_MVLANIndex3_3(uiIfIndex, usPeId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3756 (
	uiVrId UINT32,
	uiOutIfIndex UINT32,
	uiInIfIndex UINT32,
	usNvtagId UINT16,
	usReserved UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_Index0_0(uiOutIfIndex)
	HAC_HASH INDEX IDX_Index1_1(uiInIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T17 (
	uiIfIndex UINT32,
	uiMainIfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_MAINSUBIFIndex0_0(uiIfIndex)
	HAC_HASH INDEX IDX_MAINSUBIFIndex1_1(uiMainIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T14 (
	uiIfIndex UINT32,
	uiTBTP UINT32,
	usTB UINT16,
	usTP UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucDwnldToLdm UINT8,
	aucReserved FIXED 3
	PRIMARY INDEX IDX_PHYFWIFIndex0_0(uiIfIndex)
	HAC_HASH INDEX IDX_PHYFWIFIndex1_1(uiTBTP) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PHYFWIFIndex2_2(usTB, usTP) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1 (
	uiIpAddr UINT32,
	uiIfIndex UINT32,
	aucMacAddr FIXED 6,
	usBridgeVlanId UINT16,
	ucArpFlg UINT8,
	ucLinkType UINT8,
	usFwdIfType UINT16,
	uiWorkIfIndex UINT32,
	uiAtmIfIndex UINT32,
	usTargetBlade UINT16,
	usTargetPort UINT16,
	usPevid UINT16,
	usCevid UINT16,
	uiVcd UINT32,
	uiTimeStamp UINT32,
	uiAidIndex UINT32,
	uiQatIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	usVlanId UINT16,
	usReserve UINT16,
	uiVlanVrId UINT32,
	uiIfPhyType UINT32,
	uiMainIfType UINT32,
	uiIfLinkType UINT32,
	uiIfEncapType UINT32,
	uiVrId UINT32,
	uiVrfId UINT32,
	uiFVrfId UINT32,
	uiNodeGrpID UINT32,
	ucTulType UINT8,
	ucResv UINT8,
	usResv UINT16,
	uiTulVrfId UINT32,
	uiTulId UINT32,
	uiTulEncapId UINT32,
	uiFlowId UINT32,
	uiTnlSip UINT32,
	uiTnlDip UINT32,
	ucIpMask UINT8,
	ucResvN UINT8,
	usResvN UINT16
	PRIMARY INDEX IDX_ARPIndex0_0(uiIpAddr, uiIfIndex)
	HAC_HASH INDEX IDX_ARPIndex1_1(aucMacAddr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_ARPIndex2_2(uiWorkIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_ARPIndex3_3(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_ARPIndex4_4(uiVrId, uiVrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_ARPIndex5_5(uiIpAddr, uiVrId, uiVrfId, ucIpMask) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_ARPIndex6_6(uiIpAddr, uiIfIndex, uiVrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_ARPIndex7_7(uiWorkIfIndex, usPevid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T9 (
	uiGIIDIndex UINT32,
	uiAttributeID UINT32,
	uiPrimaryIID UINT32,
	uiPrimaryLable UINT32,
	uiBackupIID UINT32,
	uiBackupLabel UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	usFlag UINT16,
	usReserved UINT16
	PRIMARY INDEX IDX_IIDGNodeIndex0_0(uiGIIDIndex, uiAttributeID, uiPrimaryIID, uiPrimaryLable, uiBackupIID, uiBackupLabel, uiVrIndex)
	HAC_HASH INDEX IDX_IIDGNodeIndex1_1(uiGIIDIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IIDGNodeIndex2_2(uiGIIDIndex, uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IIDGNodeIndex3_3(uiPrimaryIID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IIDGNodeIndex4_4(uiBackupIID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IIDGNodeIndex5_5(uiAttributeID) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T36 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiTopologyID UINT32,
	usPathFlag UINT16,
	ucFtnSearch UINT8,
	ucMaskLen UINT8,
	uiNhlfeID UINT32,
	uiAddress UINT32,
	uiLspXcIndex UINT32,
	ucLspXcType UINT8,
	ucLspXcRole UINT8,
	usMtu UINT16,
	uiOutIfIndex UINT32,
	uiNextHop UINT32,
	uiOutLabel UINT32,
	usReserve1 UINT16,
	ucLdpFlag UINT8,
	ucTunnelType UINT8,
	uiTunnelID UINT32,
	uiBandwidth UINT32,
	uiFVrfIndex UINT32,
	uiOutAtIndex UINT32,
	uiIngressToken UINT32,
	uiBackUpToken UINT32,
	uiTransitToken UINT32,
	uiTBackupToken UINT32,
	uiNodeGrpID UINT32,
	usOutIfType UINT16,
	usTB UINT16,
	usTP UINT16,
	usVlanID UINT16,
	uiRingIndex UINT32,
	uiLdpOverTeIdx UINT32,
	uiSRULoopLabel UINT32,
	uiFrrOutIfIndex UINT32,
	uiFrrNextHop UINT32
	PRIMARY INDEX IDX_LDPNHLFEIndex0_0(uiNhlfeID, uiLspXcIndex, ucLspXcType, uiVrfIndex, uiVrIndex)
	HAC_HASH INDEX IDX_LDPNHLFEIndex1_1(uiLspXcIndex, ucLspXcType, uiVrfIndex, uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex2_2(uiAddress, ucMaskLen, uiVrfIndex, uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex3_3(uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex4_4(uiOutIfIndex, uiNextHop) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex5_5(uiLspXcIndex, ucLspXcType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex1_6(uiVrIndex, uiVrfIndex, uiLspXcIndex, ucLspXcType, uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex7_7(uiVrIndex, uiVrfIndex, uiAddress, ucMaskLen, ucLspXcType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex8_8(uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex9_9(uiVrIndex, uiVrfIndex, uiTunnelID, ucLspXcType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex10_10(uiVrIndex, uiVrfIndex, uiNhlfeID, uiLspXcIndex, ucLspXcType, uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex111_11(uiVrIndex, uiNhlfeID, uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex1112_12(uiVrIndex, uiNhlfeID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex11113_13(uiAddress) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPNHLFEIndex111114_14(uiVrIndex, uiLspXcIndex, uiNhlfeID) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T37 (
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiTopologyID UINT32,
	uiLspXcIndex UINT32,
	ucLspXcType UINT8,
	ucLspXcRole UINT8,
	ucBgpFlag UINT8,
	ucMaskLen UINT8,
	uiAddress UINT32,
	uiOutLabel UINT32,
	uiIIDIndex UINT32,
	usMtu UINT16,
	usReserve UINT16,
	uiVerNo UINT32,
	hSrcPid UINT32,
	uiFvrfIndex UINT32,
	uiIngressToken UINT32,
	uiInBkToken UINT32,
	uiTransitToken UINT32,
	uiNodeID UINT32,
	uiTranBkToken UINT32,
	uiNewToken UINT32,
	entropyLFlag UINT8,
	reserve2 UINT8,
	reserve3 UINT16
	PRIMARY INDEX IDX_BGPNHLFEIndex0_0(uiVrIndex, uiVrfIndex, uiLspXcIndex, ucLspXcType, uiIIDIndex)
	HAC_HASH INDEX IDX_BGPNHLFEIndex1_1(uiVrIndex, uiVrfIndex, uiLspXcIndex, ucLspXcType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BGPNHLFEIndex2_2(uiAddress, ucMaskLen) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BGPNHLFEIndex3_3(uiIIDIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BGPNHLFEIndex4_4(uiVrIndex, uiVrfIndex, uiAddress, ucMaskLen) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BGPNHLFEIndex5_5(uiVrIndex, uiVrfIndex, uiLspXcIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BGPNHLFEIndex6_6(uiVrIndex, uiVrfIndex, uiLspXcIndex, ucLspXcType, ucLspXcRole) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BGPNHLFEIndex7_7(uiAddress, uiVrIndex, uiVrfIndex, ucMaskLen, uiIIDIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BGPNHLFEIndex8_8(uiIIDIndex, uiVrIndex, uiVrfIndex, uiLspXcIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2726 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiTopologyID UINT32,
	uiLspXcIndex UINT32,
	ucLspXcType UINT8,
	ucTnlFlag UINT8,
	ucReserve1 UINT8,
	ucReserve2 UINT8
	PRIMARY INDEX IDX_LDP_FLOW_STATISTICSInde_0(uiVrIndex, uiVrfIndex, uiLspXcIndex, ucLspXcType)
);
CREATE VERTEXLABEL T3378 (
	uiVrIndex UINT32,
	ucSRPrefer UINT8,
	aucReserved FIXED 3,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucSrhEnalbe UINT8,
	aucReserved1 FIXED 3,
	ucSrTeFrrEnable UINT8,
	aucReserved2 FIXED 3
	PRIMARY INDEX IDX_SRBEPREFERIndex0_0(uiVrIndex)
);
CREATE VERTEXLABEL T3452 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiTopologyID UINT32,
	uiIIDG UINT32,
	uiAddress UINT32,
	ucMaskLen UINT8,
	ucIIDGFlag UINT8,
	ucLspXcType UINT8,
	ucReserve UINT8,
	uiLspXcIndex UINT32,
	usPathSegment UINT16,
	usReserved UINT16,
	uiFVrfIndex UINT32
	PRIMARY INDEX IDX_LDPFECIndex0_0(uiVrIndex, uiVrfIndex, uiAddress, ucMaskLen, ucLspXcType)
	HAC_HASH INDEX IDX_LDPFECIndex1_1(uiIIDG) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPFECIndex2_2(uiAddress, uiVrIndex, uiVrfIndex, ucMaskLen, uiIIDG) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPFECIndex3_3(uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPFECIndex4_4(uiVrIndex, uiVrfIndex, uiLspXcIndex, ucLspXcType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LDPFECIndex5_5(uiVrIndex, uiVrfIndex, uiLspXcIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3860 (
	uiIfIndex UINT32,
	uiVrId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_CgnOutIfIndex_0(uiIfIndex)
);
CREATE VERTEXLABEL T4171 (
	uiVsIndex UINT32,
	uiLdpMode UINT32,
	uiSuffixFlag UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_uiVsIndex_0(uiVsIndex)
);
CREATE VERTEXLABEL T42 (
	uiIIDindex UINT32,
	uiNextHop UINT32,
	uiOutIfIndex UINT32,
	uiAidIndex UINT32,
	usFwdIfType UINT16,
	usTargetBlade UINT16,
	usTargetPort UINT16,
	usChannelId UINT16,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiRingIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiNodeID UINT32,
	uiIIDFlags UINT32
	PRIMARY INDEX IDX_P4UCSTDIIDIdx0_0(uiIIDindex, uiNextHop, uiOutIfIndex)
	HAC_HASH INDEX IDX_P4UCSTDIIDIdx1_1(uiVrIndex, uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCSTDIIDIdx2_2(uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCSTDIIDIdx3_3(uiNextHop, uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCSTDIIDIdx4_4(uiAidIndex, uiNodeID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCSTDIIDIdx5_5(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCSTDIIDIdx6_6(uiNextHop, uiOutIfIndex, uiVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCSTDIIDIdx7_7(uiVrIndex, uiVrfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T43 (
	uiIIDindex UINT32,
	uiVrIndex UINT32,
	uiTVrfIndex UINT32,
	uiFVrfIndex UINT32,
	ucTunnelType UINT8,
	ucReserved0 UINT8,
	usReserved1 UINT16,
	uiTunnelId UINT32,
	uiVrfIndex UINT32,
	usReserved3 UINT16,
	usReserved4 UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_P4UCEXTIIDIdx0_0(uiIIDindex, uiVrIndex, uiTVrfIndex, ucTunnelType, uiTunnelId)
	HAC_HASH INDEX IDX_P4UCEXTIIDIdx1_1(uiVrIndex, uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCEXTIIDIdx2_2(uiVrIndex, uiTVrfIndex, ucTunnelType, uiTunnelId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCEXTIIDIdx3_3(uiIIDindex, uiVrIndex, uiVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCEXTIIDIdx4_4(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCEXTIIDIdx5_5(uiVrIndex, uiVrfIndex, ucTunnelType, uiTunnelId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T123 (
	uiMyDiscr UINT32,
	uiVrId UINT32,
	uiVrfId UINT32,
	uiOutIfindex UINT32,
	uiPeerIp UINT32,
	ucAssocPst UINT8,
	ucRsv0 UINT8,
	usLinkFlag UINT16,
	uiFwdVrfId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiRingIndex UINT32,
	usCevid UINT16,
	usPevid UINT16,
	auiPeerIp FIXED 16,
	usReserve UINT16,
	usVlanId UINT16,
	uiVlanIfIndex UINT32,
	uiSourceIp UINT32
	PRIMARY INDEX IDX_LnkIpIdx_0(uiMyDiscr)
	HAC_HASH INDEX IDX_LnkIpIdx1_1(uiOutIfindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIpIdx2_2(uiOutIfindex, uiPeerIp) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIpIdx3_3(uiVrId, uiVrfId, uiPeerIp) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIpIdx4_4(uiVrId, uiVrfId, auiPeerIp) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIpIdx5_5(uiVrId, uiFwdVrfId, uiOutIfindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIpIdx6_6(uiVlanIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIpIdx7_7(uiVlanIfIndex, uiPeerIp) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIpIdx8_8(uiVrId, uiOutIfindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_LnkIpIdx9_9(uiVrId, uiOutIfindex, uiPeerIp) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T346 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiDownLoad UINT32,
	uiVrIndex UINT32,
	uiIfIndex UINT32,
	uiInstanceId UINT32,
	ucL2vpnType UINT8,
	ucAcFlag UINT8,
	usEncapType UINT16,
	uiIfPhyType UINT32,
	uiIfSubPhyType UINT32,
	uiIfLinkType UINT32,
	uiIfEncaptype UINT32,
	unfifinfo FIXED 12,
	ucMflpState UINT8,
	aucReserved FIXED 3,
	usPwTag UINT16,
	usACAttrFlag UINT16
	PRIMARY INDEX IDX_VPLS_AC_INDEX0_0(uiIfIndex)
	HAC_HASH INDEX IDX_VPLS_AC_INDEX1_1(uiVrIndex, uiInstanceId, ucL2vpnType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPLS_AC_INDEX2_2(uiVrIndex, uiInstanceId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPLS_AC_INDEX3_3(uiVrIndex, uiInstanceId, uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPLS_AC_INDEX4_4(uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPLS_AC_INDEX5_5(uiVrIndex, uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPLS_AC_INDEX6_6(uiInstanceId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T348 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiDownLoad UINT32,
	uiVrIndex UINT32,
	uiInstanceId UINT32,
	ucL2vpnType UINT8,
	ucInstFlag UINT8,
	usVsiMode UINT16,
	uiVsiIndex UINT32,
	uiNodeID UINT32,
	ucServiceMode UINT8,
	ucWorkMode UINT8,
	usInstExtFlag UINT16,
	uiEvcVsiId UINT32
	PRIMARY INDEX IDX_VPLS_INST_INDEX0_0(uiVrIndex, uiInstanceId)
	HAC_HASH INDEX IDX_VPLS_INST_INDEX1_1(uiVrIndex, uiInstanceId, ucL2vpnType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPLS_INST_INDEX2_2(uiVrIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T442 (
	uiIfIndex UINT32,
	uiIpAddr UINT32,
	uiNodeGrpID UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_IPADDRIndex0_0(uiIfIndex)
	HAC_HASH INDEX IDX_IPADDRIndex1_1(uiIpAddr) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T446 (
	uiIfIndex UINT32,
	usServValue UINT16,
	usReserved UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrId UINT32
	PRIMARY INDEX IDX_CBTS_PDT0_0(uiIfIndex)
);
CREATE VERTEXLABEL T813 (
	uiVrId UINT32,
	uiVrfId UINT32,
	uiIpAddr UINT32,
	ucMaskLen UINT8,
	aucReserve FIXED 3,
	usPevid UINT16,
	usCevid UINT16,
	uiIfIndex UINT32,
	uiSubIfType UINT32,
	uiToken UINT32,
	uiNodeID UINT32,
	uiFvrfId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_EumQinqUser_0(uiVrId, uiVrfId, uiIpAddr, ucMaskLen, uiIfIndex)
	HAC_HASH INDEX IDX_EumQinqUse1_1(uiIpAddr, uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_EumQinqUse2_2(uiVrId, uiVrfId, uiIpAddr, ucMaskLen) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_EumQinqUse3_3(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1112 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiIfIndex UINT32,
	uiPhyIfIndex UINT32,
	uiLabel UINT32,
	aucMacAddr FIXED 6,
	usVlanId UINT16,
	ucFwdMode UINT8,
	aucReserve FIXED 3,
	uiNeId UINT32,
	uiToken UINT32,
	ucLabelVer UINT8,
	ucReserve UINT8,
	usReserve UINT16
	PRIMARY INDEX IDX_DCNSERIALINTFIndex0_0(uiIfIndex)
	HAC_HASH INDEX IDX_DCNSERIALINTFIndex1_1(uiPhyIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1176 (
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiTunnelID UINT32,
	ucType UINT8,
	ucTnlStatus UINT8,
	ucDFFlag UINT8,
	ucKeyFlag UINT8,
	uiBindVrfIndex UINT32,
	uiSrcAddr UINT32,
	uiDstAddr UINT32,
	uiSrcIfIndex UINT32,
	uiPathMTU UINT32,
	ucKeepAliveEn UINT8,
	ucVpnDiff UINT8,
	usPathflag UINT16,
	uiFeNodeGroupId UINT32,
	uiCheckSum UINT32,
	uiIfIndex UINT32,
	acKey FIXED 33,
	ucCardId UINT8,
	usResv UINT16,
	uiGrePid UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	acKeyNew FIXED 49,
	ucStatEnable UINT8,
	acResv2 FIXED 2,
	stSrcIpv6Addr FIXED 16,
	stDstIpv6Addr FIXED 16,
	acKeyNew1 FIXED 129,
	ucRedundancyEn UINT8,
	acResv3 FIXED 2
	PRIMARY INDEX IDX_GREIdx0_0(uiVrIndex, uiVrfIndex, uiTunnelID, ucType)
	HAC_HASH INDEX IDX_GREIdx1_1(uiSrcAddr, uiDstAddr) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_GREIdx2_2(uiVrIndex, uiVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_GREIdx3_3(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_GREIdx4_4(uiVrIndex, uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_GREIdx5_5(uiSrcIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_GREIdx6_6(ucType) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1195 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiIIDindex UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiDstVpnId UINT32,
	uiFDstVpnId UINT32
	PRIMARY INDEX IDX_P4UCVPNIIDIdx0_0(uiIIDindex, uiVrIndex, uiVrfIndex, uiDstVpnId)
	HAC_HASH INDEX IDX_P4UCVPNIIDIdx1_1(uiVrIndex, uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCVPNIIDIdx2_2(uiVrIndex, uiDstVpnId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCVPNIIDIdx3_3(uiIIDindex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1752 (
	uiIfIndex UINT32,
	uiIfPerPacketEn UINT32,
	uiVrID UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_uiIfIndex_0(uiIfIndex, uiVrID)
	HAC_HASH INDEX IDX_uiIfInde1_1(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1986 (
	uiVrId UINT32,
	uiNextHop UINT32,
	uiInVrfId UINT32,
	uiIfIndex UINT32,
	usPolSeqNum UINT16,
	usServiceLocId UINT16,
	uiTunnelIndex UINT32,
	uiPolicyIndex UINT32,
	uiIPSecInstId UINT32,
	uiGroupId UINT32,
	uiGroupType UINT32,
	uiPortGroupId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_SMSTATEIndex0_0(uiVrId, uiNextHop, uiInVrfId, uiIfIndex, usPolSeqNum)
	HAC_HASH INDEX IDX_SMSTATEIndex1_1(uiNextHop, uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SMSTATEIndex2_2(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SMSTATEIndex3_3(uiIPSecInstId, uiGroupId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SMSTATEIndex4_4(uiGroupId, uiGroupType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SMSTATEIndex5_5(uiVrId, uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SMSTATEIndex6_6(uiVrId, uiInVrfId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2273 (
	uiIIDindex UINT32,
	uiNextHop UINT32,
	uiOutIfIndex UINT32,
	uiDestAddr UINT32,
	ucTunnelType UINT8,
	ucReserved0 UINT8,
	usReserved1 UINT16,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_P4UCSTDIIDIdx0_0(uiIIDindex, uiNextHop, uiOutIfIndex, uiDestAddr, ucTunnelType)
	HAC_HASH INDEX IDX_P4UCSTDIIDIdx1_1(uiVrIndex, uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCSTDIIDIdx2_2(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCSTDIIDIdx3_3(uiVrIndex, uiVrfIndex, uiDestAddr, uiOutIfIndex, uiNextHop) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCSTDIIDIdx4_4(uiOutIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2693 (
	uiIIDIndex UINT32,
	uiNextHop UINT32,
	uiOutIfIndex UINT32,
	uiVrId UINT32,
	uiVrfId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_PP4UC_NHPVXLAN_INDEX_0(uiIIDIndex, uiNextHop, uiOutIfIndex)
	HAC_HASH INDEX IDX_PP4UC_NHPVXLAN_INDE1_1(uiIIDIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UC_NHPVXLAN_INDE2_2(uiNextHop, uiOutIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2694 (
	uiNextHop UINT32,
	uiOutIfIndex UINT32,
	uiAidIndex UINT32,
	aucMacAddr FIXED 6,
	aucResv FIXED 2,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_PP4UC_ATVXLAN_INDEX_0(uiNextHop, uiOutIfIndex)
	HAC_HASH INDEX IDX_PP4UC_ATVXLAN_INDE1_1(uiOutIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2755 (
	uiVrId UINT32,
	uiGreIfIndex UINT32,
	uiIpsecIfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_GRETnlMapIpsecIndex0_0(uiVrId, uiGreIfIndex, uiIpsecIfIndex)
	HAC_HASH INDEX IDX_GRETnlMapIpsecIndex1_1(uiGreIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_GRETnlMapIpsecIndex2_2(uiIpsecIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_GRETnlMapIpsecIndex3_3(uiVrId, uiIpsecIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2759 (
	uiVrId UINT32,
	uiIPSecInstId UINT32,
	uiSrcIp UINT32,
	uiDstIp UINT32,
	uiVrfId UINT32,
	uiIfIndex UINT32,
	usFwdStartPort UINT16,
	usReserve UINT16,
	uiReserve UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_SMSTATEIndex0_0(uiVrId, uiIPSecInstId)
	HAC_HASH INDEX IDX_SMSTATEIndex1_1(uiVrId, uiVrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SMSTATEIndex2_2(uiSrcIp) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2772 (
	uiVrId UINT32,
	InstanceId UINT32,
	ServiceId UINT32,
	usFlag UINT16,
	usResv UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_Index1_1(uiVrId, InstanceId)
	HAC_HASH INDEX IDX_Index0_0(InstanceId, ServiceId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_Index2_2(ServiceId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2852 (
	uiVrIndex UINT32,
	uiStatus UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVaIndex UINT32
	PRIMARY INDEX IDX_MANAGEISOLATEIdx0_0(uiVrIndex)
);
CREATE VERTEXLABEL T3019 (
	ucInstIndex UINT8,
	ucInstType UINT8,
	ucIIDIdx UINT8,
	ucResv UINT8,
	uiIIDVal UINT32,
	uiVrId UINT32,
	usFlag UINT16,
	usResv UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_Cgn_Instance_Iid_0(ucInstIndex, ucInstType, ucIIDIdx)
	HAC_HASH INDEX IDX_Cgn_Instance_Ii1_1(uiIIDVal) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_Cgn_Instance_Ii2_2(ucInstIndex, ucInstType) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3391 (
	uiIIDindex UINT32,
	uiNextHop UINT32,
	uiOutIfIndex UINT32,
	uiAidIndex UINT32,
	usFwdIfType UINT16,
	usTargetBlade UINT16,
	usTargetPort UINT16,
	usChannelId UINT16,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiRingIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiNodeID UINT32,
	uiIIDFlags UINT32,
	uiUserInfo UINT32
	PRIMARY INDEX IDX_P4UCBRASIIDIdx0_0(uiIIDindex, uiNextHop, uiOutIfIndex)
	HAC_HASH INDEX IDX_P4UCBRASIIDIdx1_1(uiVrIndex, uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCBRASIIDIdx2_2(uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCBRASIIDIdx3_3(uiNextHop, uiOutIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCBRASIIDIdx4_4(uiAidIndex, uiNodeID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCBRASIIDIdx5_5(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCBRASIIDIdx6_6(uiUserInfo) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3450 (
	uiIIDindex UINT32,
	uiNexthop UINT32,
	uiOutIfIndex UINT32,
	uiLsIndex UINT32,
	ucTunnelType UINT8,
	ucProtocolType UINT8,
	usReserved UINT16,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_PP4TLFANHPIdx0_0(uiIIDindex, uiNexthop, uiOutIfIndex, uiLsIndex, ucTunnelType, ucProtocolType)
	HAC_HASH INDEX IDX_PP4TLFANHPIdx1_1(uiVrIndex, uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4TLFANHPIdx2_2(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4TLFANHPIdx3_3(uiOutIfIndex, uiLsIndex, uiVrIndex, uiVrfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3476 (
	uiNextHop UINT32,
	uiOutIfIndex UINT32,
	uiAidIndex UINT32,
	uiIIDFlags UINT32,
	uiNodeGrpID UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_IP4UC_ATINDEX_INDEX_0(uiNextHop, uiOutIfIndex)
	HAC_HASH INDEX IDX_IP4UC_ATINDEX_INDE1_1(uiOutIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3888 (
	VrId UINT32,
	RuleName FIXED 32,
	RuleIndex UINT32,
	IPv6PrefLen UINT8,
	IPv4PrefLen UINT8,
	res UINT16,
	IPv6Prefix FIXED 16,
	IPv4Prefix UINT32,
	VrfIndex UINT32,
	EALen UINT8,
	PSIDOffset UINT8,
	PSIDLen UINT8,
	ResC UINT8,
	IIDValue UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BMRRuleIndex_0(RuleIndex)
	HAC_HASH INDEX IDX_BMRRuleInde1_1(IIDValue) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BMRRuleInde2_2(VrId, VrfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3979 (
	uiIfIndex UINT32,
	uiNextHop UINT32,
	uiToken UINT32,
	uiVrId UINT32,
	uiVrfId UINT32,
	uiIIDFlags UINT32,
	usValidFlag UINT16,
	ucShortcut UINT8,
	ucRedirect UINT8,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_DSVPNNHPTOKENIdx0_0(uiIfIndex, uiNextHop)
	HAC_HASH INDEX IDX_DSVPNNHPTOKENIdx1_1(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3987 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiIIDindex UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiFVrfIndex UINT32,
	auiVpnSid FIXED 16,
	auiXSid FIXED 16,
	ucVpnSidMaskLen UINT8,
	ucXSidMaskLen UINT8,
	ucType UINT8,
	ucReserved UINT8,
	uiToken UINT32,
	uiToken2 UINT32,
	uiTVrfIndex UINT32
	PRIMARY INDEX IDX_VPNSIDNHP0_0(uiIIDindex, uiVrIndex, uiVrfIndex, auiVpnSid)
	HAC_HASH INDEX IDX_VPNSIDNHP1_1(uiVrIndex, uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPNSIDNHP2_2(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPNSIDNHP3_3(uiVrIndex, uiTVrfIndex, auiXSid, ucXSidMaskLen, ucType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_VPNSIDNHP4_4(uiVrIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T4089 (
	uiIfIndex UINT32,
	uiIPv4DscpLowValue UINT32,
	uiIPv4DscpHighValue UINT32,
	uiIPv6DscpLowValue UINT32,
	ullIPv6DscpHighValue UINT32,
	usIpv4Default UINT16,
	usIpv6Default UINT16,
	uiVrId UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_DSCP_PDT0_0(uiIfIndex)
);
CREATE VERTEXLABEL T4092 (
	uiIfindex UINT32,
	ucFlag UINT8,
	ucResv UINT8,
	usRes UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_uiIfindex_0(uiIfindex)
);
CREATE VERTEXLABEL T4466 (
	uiIIDindex UINT32,
	uiVrIndex UINT32,
	uiTVrfIndex UINT32,
	uiFVrfIndex UINT32,
	ucTunnelType UINT8,
	ucIIDType UINT8,
	usPathFlag UINT16,
	uiTunnelId UINT32,
	uiVrfIndex UINT32,
	auiVpnSid FIXED 16,
	auiOriginNhp FIXED 16,
	uiToken UINT32,
	uiToken2 UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_P4UCEXTSRV6TEIIDIdx0_0(uiIIDindex, uiVrIndex, uiTVrfIndex, ucTunnelType, uiTunnelId, auiVpnSid)
	HAC_HASH INDEX IDX_P4UCEXTSRV6TEIIDIdx1_1(uiVrIndex, uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_P4UCEXTSRV6TEIIDIdx2_2(uiVrIndex, uiTVrfIndex, ucTunnelType, uiTunnelId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UCEXTSRV6TEIIDIdx3_3(uiIIDindex, uiVrIndex, uiVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UCEXTSRV6TEIIDIdx4_4(uiIIDindex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UCEXTSRV6TEIIDIdx5_5(uiVrIndex, uiTVrfIndex, uiTunnelId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PP4UCEXTSRV6TEIIDIdx6_6(uiVrIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T11 (
	uiIfIndex UINT32,
	uiVrIndex UINT32,
	uiVrfIndex UINT32,
	uiFwifVrf UINT32,
	uiNodeGrpID UINT32,
	uiGroupID UINT32,
	uiBindifindex UINT32,
	uiTBTP UINT32,
	usVlan UINT16,
	usInterfaceType UINT16,
	usLinkType UINT16,
	ucMainSubFlag UINT8,
	ucUpflag UINT8,
	usFwdIfType UINT16,
	usReserve UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiPortGrpID UINT32,
	usFlag UINT16,
	usReserved UINT16
	PRIMARY INDEX IDX_FWIFINFOIndex0_0(uiIfIndex)
	HAC_HASH INDEX IDX_FWIFINFOIndex1_1(uiNodeGrpID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_FWIFINFOIndex2_2(usInterfaceType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_FWIFINFOIndex3_3(uiVrIndex, uiVrfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_FWIFINFOIndex4_4(uiVrIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T807 (
	uiFlowIndex UINT32,
	uiIfIndex UINT32,
	uiActType UINT32,
	usPeVid UINT16,
	usCeVid UINT16,
	uiVrId UINT32,
	ucPeCosFlag UINT8,
	ucPeCosValue UINT8,
	ucCeCosFlag UINT8,
	ucCeCosValue UINT8,
	ucOffsetFlag UINT8,
	ucRsv UINT8,
	usOffsetValue UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_FLOWACTION_IDX0_0(uiFlowIndex)
	HAC_HASH INDEX IDX_FLOWACTION_IDX1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_FLOWACTION_IDX2_2(uiFlowIndex, uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_FLOWACTION_IDX3_3(uiIfIndex, usPeVid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_FLOWACTION_IDX4_4(usPeVid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_FLOWACTION_IDX5_5(usPeVid, uiVrId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T913 (
	uiIfIndex UINT32,
	uiCir UINT32,
	uiPir UINT32,
	uiCbs UINT32,
	uiPbs UINT32,
	ucIfType UINT8,
	aucReserved FIXED 3,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_QOSPortMirCarIdx0_0(uiIfIndex)
);
CREATE VERTEXLABEL T1011 (
	uiMirInstId UINT32,
	stDestAddr UINT32,
	uiIdentifierId UINT32,
	uiVrId UINT32,
	uiTunnelPolicyIndex UINT32,
	uiTVrIndex UINT32,
	ucMaskLenth UINT8,
	ucCompatibleMode UINT8,
	encapType UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32,
	l2vcID UINT32,
	srcAddr UINT32,
	vniID UINT32,
	tunnelID UINT32
	PRIMARY INDEX IDX_SEC_MirrorTunl0_0(uiMirInstId)
	HAC_HASH INDEX IDX_SEC_MirrorTunl1_1(stDestAddr, ucMaskLenth, uiTunnelPolicyIndex, uiVrId, uiTVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SEC_MirrorTunl2_2(uiTunnelPolicyIndex, uiVrId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SEC_MirrorTunl3_3(stDestAddr, uiVrId, l2vcID, encapType) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SEC_MirrorTunl4_4(uiVrId, vniID, tunnelID) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_SEC_MirrorTunl5_5(stDestAddr, uiVrId, srcAddr) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T1012 (
	uiMirInstId UINT32,
	uiFlowClass UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_SEC_MirrorClass0_0(uiMirInstId)
);
CREATE VERTEXLABEL T1013 (
	uiMirInstId UINT32,
	ucLinkFlag UINT8,
	aucReserved FIXED 3,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_SEC_MirrorLink0_0(uiMirInstId)
);
CREATE VERTEXLABEL T1014 (
	uiMirInstId UINT32,
	uiSliceSize UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_SEC_MirrorSlice0_0(uiMirInstId)
);
CREATE VERTEXLABEL T2343 (
	uiCid UINT32,
	uiUserMagic UINT32,
	uiTemplateId UINT32,
	uiFamilyId UINT32,
	uiGateWayIp UINT32,
	ucQindex UINT8,
	ucColor UINT8,
	aucResv FIXED 2,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_USER_UPNP_CONFIG_I_0(uiCid, uiUserMagic)
);
CREATE VERTEXLABEL T2346 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	ucBrasService1 UINT8,
	ucBrasService2 UINT8,
	usServiceIndex UINT16,
	ucRdtIndex UINT8,
	ucBrasService3 UINT8,
	usSerGroupId UINT16,
	ucBrasService4 UINT8,
	HttpSerIndex UINT8,
	usRev UINT16
	PRIMARY INDEX IDX_BRAS_EDSG_SERVICE_INFO_0(uiUserCid, uiUserMagic, usServiceIndex)
	HAC_HASH INDEX IDX_BRAS_EDSG_SERVICE_INF1_1(uiUserCid, uiUserMagic) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2347 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	ucBrasSerCar UINT8,
	ucreserved UINT8,
	usServiceIndex UINT16,
	uiUpAverage UINT32,
	uiUpPeak UINT32,
	uiUpBasic UINT32,
	uiUpEBS UINT32,
	uiDnAverage UINT32,
	uiDnPeak UINT32,
	uiDnBasic UINT32,
	uiDnEBS UINT32
	PRIMARY INDEX IDX_BRAS_EDSG_SERVICE_CAR_I_0(uiUserCid, uiUserMagic, usServiceIndex)
);
CREATE VERTEXLABEL T2348 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	usServGroupId UINT16,
	usRev UINT16
	PRIMARY INDEX IDX_BRAS_PORTAL_POLICY_MAP__0(uiUserCid, uiUserMagic)
);
CREATE VERTEXLABEL T2349 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	stIPv6Web FIXED 16,
	stIPv6Portal FIXED 16,
	uiPortalIP UINT32,
	uiPushAgeTime UINT32,
	uiWebServerIP UINT32,
	usMss UINT16,
	ucPortalTimes UINT8,
	ucWebUrlPara UINT8,
	ucPorUrlPara UINT8,
	ucForceFlag UINT8,
	ucForcePushFlag UINT8,
	ucQindex UINT8,
	szURLKeyName FIXED 32,
	szAcName FIXED 32,
	ucWebUrl FIXED 201,
	ucType UINT8,
	aucPortalUrl FIXED 201,
	szMsisdn FIXED 17,
	szWebServerIP FIXED 16,
	aucMacDes FIXED 49,
	aucLogiSyName FIXED 31,
	szPortalURLKey FIXED 32,
	aucSSID FIXED 36,
	szMSCGIntf FIXED 28,
	szPortalMacKey FIXED 33,
	aucPortalMacDes FIXED 49,
	szApMac FIXED 49,
	ucIdentType UINT8,
	szRID FIXED 64,
	ucDsLiteFlag UINT8,
	aucRes FIXED 3,
	uiDNSRedirect UINT32,
	szWebServerIPv6 FIXED 47,
	aucResv UINT8,
	dnsRedirectIPv6 FIXED 16
	PRIMARY INDEX IDX_BRAS_USERPORTALREDIRECT_0(uiUserCid, uiUserMagic)
);
CREATE VERTEXLABEL T2351 (
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiObjectID UINT32,
	hBtrcPid UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_USERTRACEMAINIndex_0(uiUserCid, uiUserMagic)
);
CREATE VERTEXLABEL T2634 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiSn UINT32,
	ucVasGrpBitMap1 UINT8,
	aucRes FIXED 3
	PRIMARY INDEX IDX_EdsgGroupInfoIndex0_0(uiUserCid, uiUserMagic)
);
CREATE VERTEXLABEL T3011 (
	uiUserCid UINT32,
	uiUserMagic UINT32,
	ucFlag UINT8,
	ucFqBitmapIn UINT8,
	ucFqBitmapOut UINT8,
	ucDaaFlag UINT8,
	ucFqIpType UINT8,
	aucResv FIXED 3,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_DAA_INFO_INDEX0_0(uiUserCid, uiUserMagic)
);
CREATE VERTEXLABEL T3012 (
	uiUserCid UINT32,
	uiUserMagic UINT32,
	ucFlag UINT8,
	ucLevel UINT8,
	ucResv FIXED 2,
	ucUpGreenAct UINT8,
	ucUpYellowAct UINT8,
	ucUpRedAct UINT8,
	ucUpGreenDscp UINT8,
	ucUpYellowDscp UINT8,
	ucUpRedDscp UINT8,
	ucDnGreenAct UINT8,
	ucDnYellowAct UINT8,
	ucDnRedAct UINT8,
	ucDnGreenDscp UINT8,
	ucDnYellowDscp UINT8,
	ucDnRedDscp UINT8,
	uiUpAverage UINT32,
	uiUpPeak UINT32,
	uiUpBasic UINT32,
	uiUpEBS UINT32,
	uiDnAverage UINT32,
	uiDnPeak UINT32,
	uiDnBasic UINT32,
	uiDnEBS UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_DAA_CAR_INFO_INDEX_0(uiUserCid, uiUserMagic, ucLevel)
	HAC_HASH INDEX IDX_BRAS_DAA_CAR_INFO_INDEX_1(uiUserCid, uiUserMagic) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3016 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiIfIndex UINT32,
	uiFamilyid UINT32,
	uiUserGroupId UINT32,
	uiVrID UINT32,
	usDomainId UINT16,
	ucAccessType UINT8,
	ucUserIpType UINT8,
	ucFeInfo UINT8,
	ucOperType UINT8,
	usMirSerGrp UINT16,
	uiLLineCid UINT32,
	uiLLineMagic UINT32,
	ucIsDsliteUser UINT8,
	aucRes FIXED 3
	PRIMARY INDEX IDX_BRAS_USER_BASIC_INFOInd_0(uiUserCid, uiUserMagic)
	HAC_HASH INDEX IDX_BRAS_USER_BASIC_INFOInd_1(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T3017 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	ucUserIpType UINT8,
	ucRes UINT8,
	usUserRtType UINT16,
	uiPwLogicId UINT32
	PRIMARY INDEX IDX_BRAS_USER_GLOBAL_INFOIn_0(uiUserCid, uiUserMagic, ucUserIpType)
	HAC_HASH INDEX IDX_BRAS_USER_GLOBAL_INFOIn_1(uiUserCid, uiUserMagic) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T4113 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiVrId UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiTrafPolicy UINT32,
	ucDirection UINT8,
	ucFeInfo UINT8,
	usResv UINT16
	PRIMARY INDEX IDX_BRAS_PUPP_INFO_0(uiUserCid, uiUserMagic, ucDirection)
	HAC_HASH INDEX IDX_BRAS_PUPP_INF1_1(uiUserCid, uiUserMagic) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2193 (
	hSubCompPid UINT32,
	uiUserCid UINT32,
	uiIpType UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_CSUB_BRAS_USER_TRUNK_IN_0(hSubCompPid, uiUserCid, uiIpType)
	HAC_HASH INDEX IDX_CSUB_BRAS_USER_TRUNK_IN_1(uiUserCid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2363 (
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiIfIndex UINT32,
	uiRingIndex UINT32,
	uiToken UINT32,
	ucUserIpType UINT8,
	ucSqNum UINT8,
	usUserRtType UINT16,
	uiNodeGrpID UINT32,
	uiTermPid UINT32,
	uiResv UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_TERM_GLOBAL_INFOIn_0(uiUserCid, uiUserMagic, ucUserIpType)
	HAC_HASH INDEX IDX_BRAS_TERM_GLOBAL_INFOIn_1(uiUserCid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_TERM_GLOBAL_INFOIn_2(uiRingIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2662 (
	uiUserCid UINT32,
	usUserType UINT16,
	usRes UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_BRAS_CACHEM_USER_INFOIn_0(uiUserCid)
);
CREATE VERTEXLABEL T2181 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiFamilyId UINT32,
	uiIfIndex UINT32,
	usPvlan UINT16,
	usCvlan UINT16,
	ucFlag UINT8,
	ucDirection UINT8,
	usRev UINT16,
	uiCir UINT32,
	uiPir UINT32,
	uiCbs UINT32,
	uiPbs UINT32,
	uiGreenSerCls UINT32,
	uiYellowSerCls UINT32,
	uiRedSerCls UINT32,
	uiGreenColor UINT32,
	uiYellowColor UINT32,
	uiRedColor UINT32,
	ucGreenAction UINT8,
	ucYellowAction UINT8,
	ucRedAction UINT8,
	ucColorAware UINT8
	PRIMARY INDEX IDX_BRASQOS_FAMILYCARIndex0_0(uiUserCid, uiUserMagic, ucDirection)
	HAC_HASH INDEX IDX_BRASQOS_FAMILYCARIndex1_1(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2194 (
	hSubCompPid UINT32,
	uiUserCid UINT32,
	uiCtrInfo UINT32,
	uiCtrFlag UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_CSUB_BRAS_USER_BASIC_IN_0(hSubCompPid, uiUserCid, uiCtrInfo, uiCtrFlag)
	HAC_HASH INDEX IDX_CSUB_BRAS_USER_BASIC_IN_1(uiUserCid) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T29 (
	uiIfIndex UINT32,
	uiMembIfIndex UINT32,
	uiPreMemIfindex UINT32,
	ucFlag UINT8,
	ucMembWeight UINT8,
	ucTrunkState UINT8,
	ucMemState UINT8,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_TRUNKMEMIndex0_0(uiIfIndex, uiMembIfIndex)
	HAC_HASH INDEX IDX_TRUNKMEMIndex1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_TRUNKMEMIndex1_2(uiMembIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2195 (
	hSubCompPid UINT32,
	uiUserCid UINT32,
	uiVr UINT32,
	uiVrf UINT32,
	uiDstIp UINT32,
	ucMaskLen UINT8,
	ucFlag UINT8,
	usCtlFlag UINT16,
	uiIfindex UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_CSUB_BRAS_USER_IPV4_INF_0(hSubCompPid, uiUserCid, uiVr, uiVrf, uiDstIp, ucMaskLen)
	HAC_HASH INDEX IDX_CSUB_BRAS_USER_IPV4_INF_1(uiUserCid) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_CSUB_BRAS_USER_IPV4_INF_2(uiVr, uiVrf, uiDstIp) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T306 (
	uiVrId UINT32,
	uiVrfId UINT32,
	uiIfIndex UINT32,
	uiAddrFamily UINT32,
	uiIgmpEnable UINT32,
	uiIgmpVersion UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_IgmpEnId0_0(uiVrId, uiVrfId, uiIfIndex, uiAddrFamily)
	HAC_HASH INDEX IDX_IgmpEnId1_1(uiIfIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IgmpEnId2_2(uiIfIndex, uiVrId, uiVrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IgmpEnId3_3(uiVrId, uiVrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_IgmpEnId4_4(uiIfIndex, uiVrId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2699 (
	uiVrId UINT32,
	uiVrfId UINT32,
	uiAddrFamily UINT32,
	uiIfIndex UINT32,
	ucExistBas UINT8,
	ucMcBind UINT8,
	ucRes1 UINT8,
	ucRes2 UINT8,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_L3MC_BAS_MAIN_IFIndex0_0(uiVrId, uiVrfId, uiAddrFamily, uiIfIndex)
	HAC_HASH INDEX IDX_L3MC_BAS_MAIN_IFIndex1_1(uiIfIndex, uiVrId, uiVrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_L3MC_BAS_MAIN_IFIndex2_2(uiVrfId, uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2964 (
	uiVrIndex UINT32,
	uiVrfId UINT32,
	uiAddrfmly UINT32,
	uiIfIndex UINT32,
	MainIfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32,
	ucPimSnpEn UINT8,
	ucReserved UINT8,
	usReserved UINT16
	PRIMARY INDEX IDX_PIMSNPEN_Index0_0(uiVrIndex, uiVrfId, uiAddrfmly, uiIfIndex)
	HAC_HASH INDEX IDX_PIMSNPEN_Index1_1(uiIfIndex, uiVrIndex) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_PIMSNPEN_Index2_2(uiIfIndex) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2965 (
	uiVrIndex UINT32,
	uiVrfId UINT32,
	uiAddrfmly UINT32,
	uiIfIndex UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_PIMSNPEN_Index0_0(uiVrIndex, uiVrfId, uiAddrfmly, uiIfIndex)
	HAC_HASH INDEX IDX_PIMSNPEN_Index1_1(uiIfIndex, uiVrfId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2875 (
	hSrcPid UINT32,
	uiVerNo UINT32,
	uiUserCid UINT32,
	uiUserMagic UINT32,
	uiMainIfIndex UINT32,
	uiIfIndex UINT32,
	uiVrId UINT32,
	uiVrfId UINT32,
	uiNodeGrpID UINT32,
	ucPimV6RdEn UINT8,
	aucRes FIXED 3
	PRIMARY INDEX IDX_BRAS_PIMV6_USER_INFOInd_0(uiUserCid, uiUserMagic, uiNodeGrpID)
	HAC_HASH INDEX IDX_BRAS_PIMV6_USER_INFOInd_1(uiIfIndex, uiVrId, uiVrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_PIMV6_USER_INFOInd_2(uiMainIfIndex, uiVrId, uiVrfId) UNIQUE HASH_CAP 114688,
	HAC_HASH INDEX IDX_BRAS_PIMV6_USER_INFOInd_3(uiUserCid, uiUserMagic) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2757 (
	uiVrId UINT32,
	uiGroupId UINT32,
	uiGroupType UINT32,
	uiGroupMemberId UINT32,
	uiMemberType UINT32,
	uiMemberStatus UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_Index0_0(uiVrId, uiGroupId, uiGroupType, uiGroupMemberId, uiMemberType)
	HAC_HASH INDEX IDX_Index1_1(uiGroupId, uiGroupType) UNIQUE HASH_CAP 114688 ,
	HAC_HASH INDEX IDX_Index2_2(uiGroupMemberId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2758 (
	uiVrId UINT32,
	uiGroupId UINT32,
	uiGroupType UINT32,
	uiGroupMemberId UINT32,
	uiMemberType UINT32,
	uiMemberRole UINT32,
	uiMemberStatus UINT32,
	uiMemHAStatus UINT32,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_Index0_0(uiVrId, uiGroupId, uiGroupType, uiGroupMemberId, uiMemberType)
	HAC_HASH INDEX IDX_Index1_1(uiGroupMemberId) UNIQUE HASH_CAP 114688 ,
	HAC_HASH INDEX IDX_Index2_2(uiGroupId, uiGroupType) UNIQUE HASH_CAP 114688 ,
	HAC_HASH INDEX IDX_Index3_3(uiGroupId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T2773 (
	uiIfIndex UINT32,
	uiFeGroupId UINT32,
	acSlotIdStr FIXED 32,
	usCpuType UINT16,
	usCpuId UINT16,
	uiPortGrpId UINT32,
	uiVrId UINT32,
	ucBoardMode UINT8,
	acRes FIXED 3,
	hSrcPid UINT32,
	uiVerNo UINT32
	PRIMARY INDEX IDX_IPSEC_IFIndexMapIndex_0(uiIfIndex, uiVrId)
	HAC_HASH INDEX IDX_IPSEC_IFIndexMapInde1_1(uiIfIndex) UNIQUE HASH_CAP 114688 ,
	HAC_HASH INDEX IDX_IPSEC_IFIndexMapInde2_2(uiFeGroupId) UNIQUE HASH_CAP 114688
);
CREATE VERTEXLABEL T409 (
	uiDevID UINT32,
	uiFeNodeID UINT32,
	usOperCode UINT16,
	ucTBTPType UINT8,
	ucTB UINT8,
	ucTP UINT8,
	ucFWEID UINT8,
	usPortTP UINT16,
	ucNpIsolate UINT8,
	ucRsv1 UINT8,
	usRsv2 UINT16,
	hSrcPid UINT32,
	uiVerNo UINT32,
	usIngressIndex UINT16,
	ucLocalTB UINT8,
	ucLocalTP UINT8,
	usChassisID UINT16,
	usOutChannel UINT16,
	ucTMID UINT8,
	ucRsv3 UINT8,
	usRsv4 UINT16,
	uiPortRate UINT32,
	usTB1 UINT16,
	usTP1 UINT16,
	uiFe2TmIL UINT32,
	ucSubChannel UINT8,
	ucRsv5 UINT8,
	usInChannel UINT16,
	usLocalTB1 UINT16,
	usLocalTP1 UINT16,
	uiIgresInMainChl UINT32,
	uiEgresOutMainChl UINT32
	PRIMARY INDEX IDX_RESPORT_INDEX0_0(uiFeNodeID, usOperCode)
	HAC_HASH INDEX IDX_RESPORT_INDEX1_1(uiFeNodeID) UNIQUE HASH_CAP 114688 ,
	HAC_HASH INDEX IDX_RESPORT_INDEX2_2(usLocalTB1, usLocalTP1, usChassisID) UNIQUE HASH_CAP 114688 ,
	HAC_HASH INDEX IDX_RESPORT_INDEX3_3(usTB1, usTP1, ucTMID) UNIQUE HASH_CAP 114688 ,
	HAC_HASH INDEX IDX_RESPORT_INDEX4_4(usTB1, usTP1) UNIQUE HASH_CAP 114688 ,
	HAC_HASH INDEX IDX_RESPORT_INDEX5_5(ucFWEID) UNIQUE HASH_CAP 114688
);
