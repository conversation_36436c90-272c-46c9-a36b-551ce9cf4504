/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 File Name: GQL_DDL.cpp
 Description: fes util
 Author: tianyihui t30050699
 Create: 2023-09-04
 *****************************************************************************/

#ifndef PATH_SEARCH_H
#define PATH_SEARCH_H
#include "gtest/gtest.h"
#include "adpt_define.h"
#include "gmc_gql.h"
#include "t_rd_sn.h"
#include "clt_stmt.h"
#include "gmc_types.h"


typedef struct {
    uint8_t c1;
    uint8_t c2;
    uint8_t c3;
    uint8_t c4;
    uint8_t c5;
    uint8_t c6;
    uint8_t c7;
} TableT;

void InitAllLabels(GmcStmtT *stmt)
{
    const char *createVertexLabel = R"(
        CREATE VERTEXLABEL v1 (
            c1 uint8,
            c2 uint8,
            c3 uint8,
            c4 uint8,
            c5 uint8,
            c6 uint8,
            c7 uint8
            PRIMARY INDEX pk(c1)
            HAC_HASH INDEX index1(c1) UNIQUE
        );
        CREATE VERTEXLABEL v2 (
            c1 uint8,
            c2 uint8,
            c3 uint8,
            c4 uint8,
            c5 uint8,
            c6 uint8,
            c7 uint8
            PRIMARY INDEX pk(c1)
            HAC_HASH INDEX index1(c1) UNIQUE
        );
        CREATE VERTEXLABEL v3 (
            c1 uint8,
            c2 uint8,
            c3 uint8,
            c4 uint8,
            c5 uint8,
            c6 uint8,
            c7 uint8
            PRIMARY INDEX pk(c1)
            HAC_HASH INDEX index1(c1) UNIQUE
        );
        CREATE VERTEXLABEL v4 (
            c1 uint8,
            c2 uint8,
            c3 uint8,
            c4 uint8,
            c5 uint8,
            c6 uint8,
            c7 uint8
            PRIMARY INDEX pk(c1)
            HAC_HASH INDEX index1(c1) UNIQUE
        );
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createVertexLabel));

    const char *createEdgeLabel = R"(
        CREATE EDGE e12
            FROM v1 TO v2
            WHERE v1.c1 == v2.c1;
        CREATE EDGE e13
            FROM v1 TO v3
            WHERE v1.c1 == v3.c1;
        CREATE EDGE e14
            FROM v1 TO v4
            WHERE v1.c1 == v4.c1;
        CREATE EDGE e32
            FROM v3 TO v2
            WHERE v3.c1 == v2.c1;
        CREATE EDGE e34
            FROM v3 TO v4
            WHERE v3.c1 == v4.c1;
        CREATE EDGE e10
            FROM v1 TO NULL
            WHERE v1.c1 IN 6;
        CREATE EDGE e20
            FROM v2 TO NULL
            WHERE v2.c1 IN 6;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createEdgeLabel));

    /*   1. 定义path1
     *        v1:v1
     *       e12|
     *        v2:v2
     */
    const char *createPath1 = R"(
        CREATE PATH path1(
            MATCH
            (v1:v1)-[:e12]->(v2:v2)
            RETURN
            (
                REPLACE v1.c1 100, v2.c2 200
            )
        )
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createPath1));

    /*   2. 定义path2
     *            v1:v1
     *    e12/  e13|  or  e10\
     *    v2:v2  v3:v3     NULL
     */
    const char *createPath2 = R"(
        CREATE PATH path2(
            MATCH
            (v1:v1)-[:e12]->(v2:v2),
            (v1:v1)-[:e13|:e10]->((v3:v3)|(NULL))
            RETURN
            (
                REPLACE v1.c1 100, v2.c1 200
            )
        )
    )";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createPath2));

    /*   3. 定义path3
    *        v1:v1
    *   e12/     e13\
    *   v21:v2     v3:v3
    *          e32/ or e34\
    *         v22:v2   v4:v4
    *         e20|
    *           NULL
    */
    const char *createPath3 = R"(
        CREATE PATH path3 (
            MATCH
            (v1:v1)-[:e12]->(v21:v2),
            (v1:v1)-[:e13]->(v3:v3)-[:e32|:e34]->((v22:v2)-[:e20]->(NULL) | (v4:v4))
            RETURN
            (
                REPLACE v1.c1 100, v2.c2 200
            )
        );
    )";

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createPath3));
}

void RemoveAllLabels(GmcStmtT *stmt)
{
    const char *dropPath = R"(
        DROP PATH path1;
        DROP PATH path2;
        DROP PATH path3;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, dropPath));

    const char *dropEdge = R"(
        DROP EDGE e12;
        DROP EDGE e13;
        DROP EDGE e14;
        DROP EDGE e32;
        DROP EDGE e34;
        DROP EDGE e10;
        DROP EDGE e20;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, dropEdge));

    const char *dropVertexLabel = R"(
        DROP VERTEXLABEL v1;
        DROP VERTEXLABEL v2;
        DROP VERTEXLABEL v3;
        DROP VERTEXLABEL v4;
    )";
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecGql(stmt, dropVertexLabel));
}

static void Bytes2Uint(uint8_t *start, uint32_t len, uint32_t *number)
{
    ASSERT_GT(len, 0u);
    uint8_t step = 8;
    *number = start[0];
    for (uint32_t i = 1; i < len; i++) {
        *number |= start[i] << (i * step);
    }
}

// 订阅的回调函数
void PathUserCb(GmcStmtT *stmt, const GmcSubMsgInfoT *info, void *userData)
{
    GmcRecvDataTypeE dataType;
    uint8_t *data = NULL;
    Status ret = GmcGetRecvData(stmt, &dataType, &data);
    if (ret != GMERR_OK) {
        printf("GmcGetRecvData failed!\n");
        return;
    }
    ASSERT_NE(data, nullptr);
    if (dataType == PATH_TYPE) {
        // 解析第一个4字节数目
        uint32_t pathNum = 0;
        Bytes2Uint(data, 4, &pathNum);
        *(uint32_t *)userData += pathNum;
    }
}

// 向表中插入数据
void BatchReplaceDataVertexForPath(GmcConnT *conn, GmcStmtT *stmt, const char *vertexLabelName, uint32_t replaceNum)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;  // set model type to router into path service
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_REPLACE));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TableT tmpVertex = {0};
    for (uint32_t i = 1; i <= replaceNum; i++) {
        tmpVertex.c1 = i;  // primary key
        tmpVertex.c2 = i;
        tmpVertex.c3 = i;
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetVertexPropertyAll(stmt, &tmpVertex, sizeof(tmpVertex)));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    GmcBatchRetT batchRet = {};
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchDestroy(batch));
    // check the result of Batch Replace
    // 1.check total num
    // 2.check record value
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecute(stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(stmt, "c1", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c1 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c1", &c1, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(cnt+1, c1);

        ret = GmcGetVertexPropertySizeByName(stmt, "c2", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c2 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c2", &c2, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(cnt+1, c2);

        ret = GmcGetVertexPropertySizeByName(stmt, "c3", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c3 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c3", &c3, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(cnt+1, c3);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(replaceNum, cnt);
}

// 通过数组的形式向表中插入数据修改c2
void BatchReplaceVertexForPathWithC2(GmcConnT *conn, GmcStmtT *stmt, const char *vertexLabelName,
                                     uint32_t replaceNum, uint32_t startIdx, uint32_t *c1PropArr,
                                     uint32_t *changePropArr)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;  // set model type to router into path service
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_REPLACE));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TableT tmpVertex = {0};
    for (uint32_t i = startIdx; i < startIdx + replaceNum; i++) {
        tmpVertex.c1 = c1PropArr[i];  // primary key
        tmpVertex.c2 = changePropArr[i];
        tmpVertex.c3 = c1PropArr[i];
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetVertexPropertyAll(stmt, &tmpVertex, sizeof(tmpVertex)));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    GmcBatchRetT batchRet = {};
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchDestroy(batch));
    // check the result of Batch Replace
    // 1.check total num
    // 2.check record value
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecute(stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(stmt, "c1", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c1 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c1", &c1, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c1PropArr[cnt], c1);
        ret = GmcGetVertexPropertySizeByName(stmt, "c2", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c2 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c2", &c2, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(changePropArr[cnt], c2);

        ret = GmcGetVertexPropertySizeByName(stmt, "c3", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c3 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c3", &c3, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c1PropArr[cnt], c3);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(replaceNum + startIdx, cnt);
}

// 通过数组的形式向表中插入数据修改c3
void BatchReplaceVertexForPathWithC3(GmcConnT *conn, GmcStmtT *stmt, const char *vertexLabelName,
                                     uint32_t replaceNum, uint32_t startIdx, uint32_t *c1PropArr,
                                     uint32_t *changePropArr)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;  // set model type to router into path service
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_REPLACE));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TableT tmpVertex = {0};
    for (uint32_t i = startIdx; i < startIdx + replaceNum; i++) {
        tmpVertex.c1 = c1PropArr[i];  // primary key
        tmpVertex.c2 = c1PropArr[i];
        tmpVertex.c3 = changePropArr[i];
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetVertexPropertyAll(stmt, &tmpVertex, sizeof(tmpVertex)));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    GmcBatchRetT batchRet = {};
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchDestroy(batch));
    // check the result of Batch Replace
    // 1.check total num
    // 2.check record value
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecute(stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(stmt, "c1", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c1 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c1", &c1, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c1PropArr[cnt], c1);
        ret = GmcGetVertexPropertySizeByName(stmt, "c2", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c2 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c2", &c2, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c1PropArr[cnt], c2);

        ret = GmcGetVertexPropertySizeByName(stmt, "c3", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c3 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c3", &c3, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(changePropArr[cnt], c3);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(replaceNum + startIdx, cnt);
}

// 通过数组的形式向表中插入数据修改c2和c3
void BatchReplaceVertexForPathWithC2AndC3(GmcConnT *conn, GmcStmtT *stmt, const char *vertexLabelName,
                                          uint32_t replaceNum, uint32_t startIdx, uint32_t *c1PropArr,
                                          uint32_t *changePropArr)
{
    uint32_t modelType = GMC_MODEL_TYPE_DEF_GQL;  // set model type to router into path service
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_REPLACE));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetStmtAttr(stmt, GMC_STMT_ATTR_MODEL_TYPE, &modelType, sizeof(modelType)));
    GmcBatchT *batch = NULL;
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_HYBRID);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TableT tmpVertex = {0};
    for (uint32_t i = startIdx; i < startIdx + replaceNum; i++) {
        tmpVertex.c1 = c1PropArr[i];  // primary key
        tmpVertex.c2 = changePropArr[i];
        tmpVertex.c3 = changePropArr[i];
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcSetVertexPropertyAll(stmt, &tmpVertex, sizeof(tmpVertex)));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchAddDML(batch, stmt));
    }
    GmcBatchRetT batchRet = {};
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchExecute(batch, &batchRet));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcBatchDestroy(batch));
    // check the result of Batch Replace
    // 1.check total num
    // 2.check record value
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcPrepareStmtByLabelName(stmt, vertexLabelName, GMC_OPERATION_SCAN));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcExecute(stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, GmcFetch(stmt, &isFinish));
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(stmt, "c1", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c1 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c1", &c1, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(c1PropArr[cnt], c1);
        ret = GmcGetVertexPropertySizeByName(stmt, "c2", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c2 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c2", &c2, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(changePropArr[cnt], c2);

        ret = GmcGetVertexPropertySizeByName(stmt, "c3", &size);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        uint32_t c3 = 0;
        ret = GmcGetVertexPropertyByName(stmt, "c3", &c3, size, &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(changePropArr[cnt], c3);
        cnt++;
    }
    AW_MACRO_ASSERT_EQ_INT(replaceNum + startIdx, cnt);
}

#endif /* PATH_SEARCH_H */
