/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 File Name: 005_create_index.cpp
 Description: CreatePathTriggerRule测试
 Author: tianyihui t30050699
 Create: 2023-07-31
 *****************************************************************************/
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>

#include "gtest/gtest.h"
#include "gmc_errno.h"

#include "t_rd_adapt.h"
#include "t_rd_assert.h"
#include "t_rd_sn.h"
#include "t_rd_common.h"
#include "gmc_gql.h"
#include "FES_DDL.h"
#include "../001_vertexlabel/vertex_util.h"


GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
using namespace std;

class CreatePathTriggerRule : public testing::Test {
protected:
    static void SetUpTestCase()
    {

        //初始化配置文件
        InitCfg();

        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
       
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        GmcDetachAllShmSeg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void CreatePathTriggerRule::SetUp()
{
    // 创建同步客户端连接
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //预置待订阅的path
    InitAllLabels(stmt);

    AW_FUN_Log(LOG_STEP, "path创建完成.");

}

void CreatePathTriggerRule::TearDown()
{
    // 释放被定义的复杂path/边/点, 与InitAllLabels009配对使用
    RemoveAllLabels(stmt);

    // 关闭同步客户端连接
    int ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 用例001：创建触发器，trigger_name以[a-zA-Z]开头，只包含大小写字母
TEST_F(CreatePathTriggerRule,GQL_009_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER Trigger
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER Trigger;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例002：创建触发器，trigger_name以数字开头，包含字母大小写和_
TEST_F(CreatePathTriggerRule,GQL_009_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER 01_triggerName
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";
    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例003：创建触发器，trigger_name以数字开头，只包含数字
TEST_F(CreatePathTriggerRule,GQL_009_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER 12345
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例004：创建触发器，trigger_name以_开头，包含大小写字母、_和数字
TEST_F(CreatePathTriggerRule,GQL_009_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER _Trigger01
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER _Trigger01;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例005：创建触发器，trigger_name包含[0-9a-zA-Z_]和其他字符 
TEST_F(CreatePathTriggerRule,GQL_009_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER Trigger$_01
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例006：创建触发器时，关联的顶点标签未创建
TEST_F(CreatePathTriggerRule,GQL_009_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName06
        ON REPLACE v5
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例007：创建触发器时，关联path未创建
TEST_F(CreatePathTriggerRule,GQL_009_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName07
        ON REPLACE v1
        WHEN TRUE DO SEARCH path4
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_OBJECT, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例008：创建触发器，触发器包含多个{WHEN TRUE DO SEARCH path_name}语句
TEST_F(CreatePathTriggerRule,GQL_009_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName08
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
        WHEN TRUE DO SEARCH path2
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER triggerName08;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例009：创建触发器，一个创建语句包含多个trigger_name
TEST_F(CreatePathTriggerRule,GQL_009_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName09_01,triggerName09_02
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例010：创建触发器，一个创建语句包含多个table_name
TEST_F(CreatePathTriggerRule,GQL_009_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName10
        ON REPLACE v1,v2
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例011：创建触发器，一个创建语句包含多个path_name
TEST_F(CreatePathTriggerRule,GQL_009_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName11
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1,path2
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER triggerName11;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例014：创建触发器，trigger_name长度31字符
TEST_F(CreatePathTriggerRule,GQL_009_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName11112222233333444446
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER triggerName11112222233333444446;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例015：创建触发器，trigger_name长度32字符
TEST_F(CreatePathTriggerRule,GQL_009_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName1111222223333344444667777
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NAME_TOO_LONG, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例016：创建触发器， trigger_name字段缺失
TEST_F(CreatePathTriggerRule,GQL_009_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例017：创建触发器， table_name字段缺失
TEST_F(CreatePathTriggerRule,GQL_009_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName17
        ON REPLACE
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例018：创建触发器，DML字段缺失
TEST_F(CreatePathTriggerRule,GQL_009_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName18
        ON v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例019：创建触发器， 创建触发器，action字段缺失
TEST_F(CreatePathTriggerRule,GQL_009_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName19
        ON REPLACE v1
        DO
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例020：创建触发器，整个条件语句缺失
TEST_F(CreatePathTriggerRule,GQL_009_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName20
        ON REPLACE v1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例021：批量创建触发器的多个语句，其中几句末尾分号缺失
TEST_F(CreatePathTriggerRule,GQL_009_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger01 = R"(
        CREATE TRIGGER triggerName21_01 ON REPLACE v1 WHEN TRUE DO SEARCH path1
        CREATE TRIGGER triggerName21_02 ON REPLACE v2 WHEN TRUE DO SEARCH path1;
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger02 = R"(
        CREATE TRIGGER triggerName21_03 ON REPLACE v1 WHEN TRUE DO SEARCH path1
        CREATE TRIGGER triggerName21_04 ON REPLACE v2 WHEN TRUE DO SEARCH path2
    )";

    //创建触发器
    ret = GmcExecGql(stmt, createTrigger02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    const char *createTrigger03 = R"(
        CREATE TRIGGER triggerName21_05 ON REPLACE v1 WHEN TRUE DO SEARCH path2;
        CREATE TRIGGER triggerName21_06 ON REPLACE v2 WHEN TRUE DO SEARCH path2
    )";

    //创建触发器
    ret = GmcExecGql(stmt, createTrigger03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *createTrigger04 = R"(
        CREATE TRIGGER triggerName21_07 ON REPLACE v3 WHEN TRUE DO SEARCH path1;
        CREATE TRIGGER triggerName21_08 ON REPLACE v4 WHEN TRUE DO SEARCH path2
        CREATE TRIGGER triggerName21_09 ON REPLACE v3 WHEN TRUE DO SEARCH path3;
        CREATE TRIGGER triggerName21_10 ON REPLACE v4 WHEN TRUE DO SEARCH path2
    )";

    //创建触发器
    ret = GmcExecGql(stmt, createTrigger04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER triggerName21_05;
        DROP TRIGGER triggerName21_06;
        DROP TRIGGER triggerName21_07;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例022：创建触发器，关键字CREATE/REPLACE/TRIGGER/ON/DO拼写错误
TEST_F(CreatePathTriggerRule,GQL_009_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    //CREATE拼写错误
    const char *createTrigger01 = R"(
        CREATEE TRIGGER triggerName22_01
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";
    int ret = GmcExecGql(stmt, createTrigger01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    //TRIGGER拼写错误
    const char *createTrigger02 = R"(
        CREATE TRIGGERR triggerName22_01
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";
    ret = GmcExecGql(stmt, createTrigger02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    //REPLACE拼写错误
    const char *createTrigger03 = R"(
        CREATE TRIGGER triggerName22_03
        ON REPLACEE v1
        WHEN TRUE DO SEARCH path1
    )";
    ret = GmcExecGql(stmt, createTrigger03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    //ON拼写错误
    const char *createTrigger04 = R"(
        CREATE TRIGGER triggerName22_04
        ONN REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";
    ret = GmcExecGql(stmt, createTrigger04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    //DO拼写错误
    const char *createTrigger05 = R"(
        CREATE TRIGGER triggerName22_05
        ON REPLACE v1
        DOO path1
    )";
    ret = GmcExecGql(stmt, createTrigger05);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例023：创建触发器，关键字CREATE/REPLACE/TRIGGER/ON/DO缺失
TEST_F(CreatePathTriggerRule,GQL_009_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    //CREATE缺失
    const char *createTrigger01 = R"(
        TRIGGER triggerName23_01
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";
    int ret = GmcExecGql(stmt, createTrigger01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    //TRIGGER缺失
    const char *createTrigger02 = R"(
        CREATE triggerName23_02
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";
    ret = GmcExecGql(stmt, createTrigger02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    //REPLACE缺失
    const char *createTrigger03 = R"(
        CREATE TRIGGER triggerName23_03
        ON v1
        WHEN TRUE DO SEARCH path1
    )";
    ret = GmcExecGql(stmt, createTrigger03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    //ON缺失
    const char *createTrigger04 = R"(
        CREATE TRIGGER triggerName23_04
        REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";
    ret = GmcExecGql(stmt, createTrigger04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    //DO缺失
    const char *createTrigger05 = R"(
        CREATE TRIGGER triggerName23_05
        ON REPLACE v1
        path1
    )";
    ret = GmcExecGql(stmt, createTrigger05);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例024：创建触发器，单个关键字大小写混合
TEST_F(CreatePathTriggerRule,GQL_009_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        Create TRIGGER triggerName24
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例025：创建一个触发器，再在相同顶点标签上创建一个同名、action相同的触发器
TEST_F(CreatePathTriggerRule,GQL_009_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger01 = R"(
        CREATE TRIGGER triggerName25_01
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *createTrigger02 = R"(
        CREATE TRIGGER triggerName25_02
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //再创建一个同名且action相同的触发器
    ret = GmcExecGql(stmt, createTrigger02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER triggerName25_01;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例026：创建一个触发器，再在相同顶点标签上创建一个不同名、action相同的触发器
//Create a trigger, and then create a trigger with a different name and actions on the same vertex label.
TEST_F(CreatePathTriggerRule,GQL_009_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger01 = R"(
        CREATE TRIGGER triggerName26_01
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *createTrigger02 = R"(
        CREATE TRIGGER triggerName26_02
        ON REPLACE v2
        WHEN TRUE DO SEARCH path1
    )";

    //再创建一个不同名、action相同的触发器
    ret = GmcExecGql(stmt, createTrigger02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER triggerName26_01;
        DROP TRIGGER triggerName26_02;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例027：创建一个触发器，再在相同顶点标签上创建一个同名、action不同的触发器
TEST_F(CreatePathTriggerRule,GQL_009_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger01 = R"(
        CREATE TRIGGER triggerName27_01
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *createTrigger02 = R"(
        CREATE TRIGGER triggerName27_02
        ON REPLACE v1
        WHEN TRUE DO SEARCH path2
    )";

    //再创建一个同名、action不同的触发器
    ret = GmcExecGql(stmt, createTrigger02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER triggerName27_01;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例028：创建一个触发器，再在不同顶点标签上创建一个同名、actions相同的触发器
TEST_F(CreatePathTriggerRule,GQL_009_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    //创建触发器
    const char *createTrigger01 = R"(
        CREATE TRIGGER triggerName28
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    int ret = GmcExecGql(stmt, createTrigger01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //再创建一个同名、action不同的触发器
    const char *createTrigger02 = R"(
        CREATE TRIGGER triggerName28
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";
    ret = GmcExecGql(stmt, createTrigger02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER triggerName28;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例029：创建一个触发器，再在不同顶点标签上创建一个同名、actions不同的触发器
TEST_F(CreatePathTriggerRule,GQL_009_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger01 = R"(
        CREATE TRIGGER triggerName29
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *createTrigger02 = R"(
        CREATE TRIGGER triggerName29
        ON REPLACE v2
        WHEN TRUE DO SEARCH path2
    )";

    //再创建一个同名、action不同的触发器
    ret = GmcExecGql(stmt, createTrigger02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DUPLICATE_OBJECT, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER triggerName29;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例030：创建一个触发器，删除这个触发器关联的顶点标签关联的path和边，再创建同名触发器
TEST_F(CreatePathTriggerRule,GQL_009_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createVertexLabel = R"(
        CREATE VERTEXLABEL v502 (
            uiVsIndex UINT32
            MULTI_HASH INDEX index05(uiVsIndex)
        );
    )";
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createVertexLabel));

    const char *createEdgeLabel = R"(
        CREATE EDGE e1502
            FROM v1 TO v502
            WHERE v1.uiVsIndex == v502.uiVsIndex;
    )";

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createEdgeLabel));

    //创建path30
    const char *createPath30 = R"(
        CREATE PATH path30(
            MATCH 
            (v1:v1)-[:e1502]->(v502:v502)
            RETURN
            (
                REPLACE v502.uiVsIndex 100
            )
        )
    )";

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecGql(stmt, createPath30));

    //创建触发器
    const char *createTrigger30 = R"(
        CREATE TRIGGER triggerName30
        ON REPLACE v502
        WHEN TRUE DO SEARCH path30
    )";
    int ret = GmcExecGql(stmt, createTrigger30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //删除触发器
    const char *dropTrigger = R"(
        DROP TRIGGER triggerName30;
    )";
    ret = GmcExecGql(stmt, dropTrigger);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除v5关联的边和path，再删除v5
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecGql(stmt, "DROP PATH path30;"));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecGql(stmt,"DROP EDGE e1502;"));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecGql(stmt, "DROP VERTEXLABEL v502;"));

    //再创建一个同名触发器
    const char *createTrigger_30 = R"(
        CREATE TRIGGER triggerName30
        ON REPLACE v1
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    ret = GmcExecGql(stmt, createTrigger_30);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); 

    //删除触发器
    const char *dropTrigger1 = R"(
        DROP TRIGGER triggerName30;
    )";
    ret = GmcExecGql(stmt, dropTrigger1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例031：创建触发器时，关联的table不存在关联的Path中
TEST_F(CreatePathTriggerRule,GQL_009_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *createTrigger = R"(
        CREATE TRIGGER triggerName07
        ON REPLACE v4
        WHEN TRUE DO SEARCH path1
    )";

    //创建触发器
    int ret = GmcExecGql(stmt, createTrigger);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_OBJECT_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例012：创建500个触发器
TEST_F(CreatePathTriggerRule,GQL_009_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    //创建顶点标签
    char createTable[1025];
    for (int i = 5; i <= 500; i++) {
        sprintf(createTable, "CREATE VERTEXLABEL v%d ( uiVsIndex UINT32 MULTI_HASH INDEX index01(uiVsIndex));", i);
        int ret = GmcExecGql(stmt, createTable);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //创建PATH
    char createPath[1025];
    for (int i = 3; i <= 500; i++) {
        sprintf(createPath, "CREATE PATH path%d (MATCH (:v%d) RETURN (REPLACE v%d.uiVsIndex %d));", i, i, i, i);
        int ret = GmcExecGql(stmt, createPath);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //创建触发器
    char createTrigger[1025];
    for (int i = 1; i <= 500; i++) {
        sprintf(createTrigger, "CREATE TRIGGER triggerName12_%d ON REPLACE v%d WHEN TRUE DO SEARCH path%d;", i, i, i);
        int ret = GmcExecGql(stmt, createTrigger);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //删除触发器
    char dropTrigger[1025];
    for (int i = 1; i <= 500; i++) {
        sprintf(dropTrigger, "DROP TRIGGER triggerName12_%d;", i);
        int ret = GmcExecGql(stmt, dropTrigger);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //删除path
    char dropPath[1025];
    for (int i = 3; i <= 500; i++) {
        sprintf(dropPath, "DROP PATH path%d;", i);
        int ret = GmcExecGql(stmt, dropPath);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //删除顶点标签
    char dropTable[1025];
    for (int i = 5; i <= 500; i++) {
        sprintf(dropTable, "DROP VERTEXLABEL v%d;", i);
        int ret = GmcExecGql(stmt, dropTable);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例013：创建501个触发器
TEST_F(CreatePathTriggerRule,GQL_009_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    //创建顶点标签
    char createTable[1025];
    for (int i = 5; i <= 500; i++) {
        sprintf(createTable, "CREATE VERTEXLABEL v%d ( uiVsIndex UINT32 MULTI_HASH INDEX index01(uiVsIndex));", i);
        int ret = GmcExecGql(stmt, createTable);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //创建PATH
    char createPath[1025];
    for (int i = 3; i <= 500; i++) {
        sprintf(createPath, "CREATE PATH path%d (MATCH (:v%d) RETURN (REPLACE v%d.uiVsIndex %d));", i, i, i, i);
        int ret = GmcExecGql(stmt, createPath);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //创建触发器
    char createTrigger[1025];
    for (int i = 1; i <= 500; i++) {
        sprintf(createTrigger, "CREATE TRIGGER triggerName12_%d ON REPLACE v%d WHEN TRUE DO SEARCH path%d;", i, i, i);
        int ret = GmcExecGql(stmt, createTrigger);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //再创建1个触发器
    const char *createTrigger501 = R"(
        CREATE TRIGGER triggerName13_501
        ON REPLACE v2
        WHEN TRUE DO SEARCH path1
    )";
    int ret = GmcExecGql(stmt, createTrigger501);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    //删除触发器
    char dropTrigger[1025];
    for (int i = 1; i <= 500; i++) {
        sprintf(dropTrigger, "DROP TRIGGER triggerName12_%d;", i);
        int ret = GmcExecGql(stmt, dropTrigger);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //删除path
    char dropPath[1025];
    for (int i = 3; i <= 500; i++) {
        sprintf(dropPath, "DROP PATH path%d;", i);
        int ret = GmcExecGql(stmt, dropPath);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //删除顶点标签
    char dropTable[1025];
    for (int i = 5; i <= 500; i++) {
        sprintf(dropTable, "DROP VERTEXLABEL v%d;", i);
        int ret = GmcExecGql(stmt, dropTable);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    

    AW_FUN_Log(LOG_STEP, "test end.");
}

