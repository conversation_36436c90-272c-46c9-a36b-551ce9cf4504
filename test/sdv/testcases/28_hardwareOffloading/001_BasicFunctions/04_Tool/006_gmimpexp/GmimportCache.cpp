#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "../../common/hash_util.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
int ret = 0;

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
char compareMess[MAX_CMD_SIZE];
char compareMess1[MAX_CMD_SIZE];
char compareMess2[MAX_CMD_SIZE];
char *vertex_src_fileds_label_schema = NULL;
class GmimportCache : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcConnect(&g_conn, &g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcUseNamespace(g_stmt, "public");
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        readJanssonFile("schema_file/vertexlabel_src_schema.gmjson", &vertex_src_fileds_label_schema);
        ASSERT_NE((void *)NULL, vertex_src_fileds_label_schema);
    }
    static void TearDownTestCase()
    {
        ret = testGmcDisconnect(g_conn, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        //恢复配置文件
        RecoverCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void GmimportCache::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void GmimportCache::TearDown()
{
    AW_CHECK_LOG_END();
}

void TreeReadVertex(GmcStmtT *stmt, int index, int start_num, int end_num, int array_num, int vector_num,
    const char *labelName, const char *keyName)
{

    void *label = NULL;
    bool isNull;

    GmcNodeT *root, *T1, *T2, *T3;

    // 查询顶点
    for (int i = start_num; i < end_num; i++) {
        uint32_t f0_value = index + i;
        uint32_t f1_value = f0_value + 1, f2_value = f0_value + 2, f3_value = f0_value + 3, f4_value = f0_value + 4,
                 f5_value = f0_value + 5, f6_value = f0_value + 6;
        uint32_t expectValue0, expectValue1, expectValue2, expectValue3, expectValue4, expectValue5, expectValue6;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        unsigned int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            ret = GmcGetRootNode(stmt, &root);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "a1", &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "a2", &T2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "a3", &T3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcNodeGetPropertyByName(root, (char *)"a0", &expectValue0, sizeof(uint32_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
            AW_MACRO_ASSERT_EQ_INT(f0_value, expectValue0);
            ret = GmcNodeGetPropertyByName(T1, (char *)"b1", &expectValue1, sizeof(uint32_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
            AW_MACRO_ASSERT_EQ_INT(f1_value, expectValue1);
            ret = GmcNodeGetPropertyByName(T1, (char *)"b2", &expectValue2, sizeof(uint32_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
            AW_MACRO_ASSERT_EQ_INT(f2_value, expectValue2);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetPropertyByName(T2, (char *)"b3", &expectValue3, sizeof(uint32_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                AW_MACRO_ASSERT_EQ_INT(f3_value, expectValue3);

                ret = GmcNodeGetPropertyByName(T2, (char *)"b4", &expectValue4, sizeof(uint32_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                AW_MACRO_ASSERT_EQ_INT(f4_value, expectValue4);
                if (j < array_num - 1) {
                    ret = GmcNodeGetNextElement(T2, &T2);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                }
            }
            // 读取vector节点
            for (uint32_t j = 0; j < vector_num; j++) {
                ret = GmcNodeGetPropertyByName(T3, (char *)"b5", &expectValue5, sizeof(uint32_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                AW_MACRO_ASSERT_EQ_INT(f5_value, expectValue5);
                ret = GmcNodeGetPropertyByName(T3, (char *)"b6", &expectValue6, sizeof(uint32_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                AW_MACRO_ASSERT_EQ_INT(f6_value, expectValue6);
            }
        }
    }
}

// 001 gmimport -c cache –f 指定路径的vschema文件 ，预期导入成功，创建label
TEST_F(GmimportCache, HardWare_Offloading_001_Tool_006_GmimportCache_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "tree_schema";
    const char *keyName = "tree_schema_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/cache_schemafile";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c cache -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    
    ret = executeCommand(g_command, "Total 4 files were found, 4 files OK, 0 files skip, 0 files failed. "
        "0 folder were found, 0 folder OK, 0 folder failed.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TreeReadVertex(g_stmt, 1, 0, 2, 2, 3, labelName, keyName);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "tree_new_schema");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 gmimport -c cache –f 指定路径（只有data文件）
TEST_F(GmimportCache, HardWare_Offloading_001_Tool_006_GmimportCache_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "sys";
    const char *keyName = "table_pk";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/schema_filetest";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c cache -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    
    snprintf(compareMess, MAX_CMD_SIZE, "insert data unsucc. totalNum: 2, successNum: 0, duplicateNum: 0, ret = %d",
        GMERR_UNDEFINED_TABLE);
    snprintf(compareMess1, MAX_CMD_SIZE,
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/schema_filetest/test_cache.gmdata\" unsucc. ret = "
        "%d", GMERR_UNDEFINED_TABLE);
    ret = executeCommand(g_command, compareMess, compareMess1,
        "Total 1 files were found, 0 files OK, 0 files skip, 1 files failed. 0 folder were found,"
        " 0 folder OK, 0 folder failed.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003 导入多条数据，第一条数据错误
TEST_F(GmimportCache, HardWare_Offloading_001_Tool_006_GmimportCache_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "repeat_gmimport_label";
    const char *keyName = "repeat_gmimport_label_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/repeat_test/repeat_gmimport_schema.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    
    ret = executeCommand(g_command, "Command type: import_vschema, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema.gmjson\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *g_dataPath = "./schema_file/repeat_test/gmimport_faildata_first.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -t %s -s %s", g_toolPath, g_dataPath,
        labelName, g_connServer);
    
    snprintf(compareMess, MAX_CMD_SIZE, "insert partial data unsucc. totalNum: 10, successNum: 9, duplicateNum: 0");
    snprintf(compareMess1, MAX_CMD_SIZE,
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/gmimport_faildata_first.gmdata\" "
        "successfully");
    ret = executeCommand(g_command, compareMess, compareMess1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 导入多条数据，中间的数据错误
TEST_F(GmimportCache, HardWare_Offloading_001_Tool_006_GmimportCache_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "repeat_gmimport_label";
    const char *keyName = "repeat_gmimport_label_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/repeat_test/repeat_gmimport_schema.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    
    ret = executeCommand(g_command, "Command type: import_vschema, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema.gmjson\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *g_dataPath = "./schema_file/repeat_test/gmimport_faildata_middle.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -t %s -s %s", g_toolPath, g_dataPath,
        labelName, g_connServer);
    
    snprintf(compareMess, MAX_CMD_SIZE, "insert partial data unsucc. totalNum: 10, successNum: 9, duplicateNum: 0");
    snprintf(compareMess1, MAX_CMD_SIZE,
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/gmimport_faildata_middle.gmdata\" "
        "successfully");
    ret = executeCommand(g_command, compareMess, compareMess1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 导入多条数据，最后一条数据错误
TEST_F(GmimportCache, HardWare_Offloading_001_Tool_006_GmimportCache_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "repeat_gmimport_label";
    const char *keyName = "repeat_gmimport_label_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/repeat_test/repeat_gmimport_schema.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    
    ret = executeCommand(g_command, "Command type: import_vschema, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema.gmjson\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *g_dataPath = "./schema_file/repeat_test/gmimport_faildata_final.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -t %s -s %s", g_toolPath, g_dataPath,
        labelName, g_connServer);
    
    snprintf(compareMess, MAX_CMD_SIZE, "insert partial data unsucc. totalNum: 10, successNum: 9, duplicateNum: 0");
    snprintf(compareMess2, MAX_CMD_SIZE,"insert partial data unsucc when parse json object, ret = %d, index = 9",
        GMERR_DATATYPE_MISMATCH);
    ret = executeCommand(g_command, compareMess2, compareMess);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 重复导入数据，最后一条数据错误
TEST_F(GmimportCache, HardWare_Offloading_001_Tool_006_GmimportCache_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "repeat_gmimport_label";
    const char *keyName = "repeat_gmimport_label_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/repeat_test/repeat_gmimport_schema.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    
    ret = executeCommand(g_command, "Command type: import_vschema, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema.gmjson\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *g_dataPath = "./schema_file/repeat_test/repeat_gmimport_schema.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -t %s -s %s", g_toolPath, g_dataPath,
        labelName, g_connServer);
    
    snprintf(compareMess, MAX_CMD_SIZE, "insert partial data unsucc. totalNum: 20, successNum: 19, duplicateNum: 0");
    
    snprintf(compareMess2, MAX_CMD_SIZE,"insert partial data unsucc when parse json object, ret = %d, index = 19",
        GMERR_DATATYPE_MISMATCH);

    ret = executeCommand(g_command, compareMess2, compareMess);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 007 重复导入数据，最后一条数据不重复
TEST_F(GmimportCache, HardWare_Offloading_001_Tool_006_GmimportCache_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "repeat_gmimport_label";
    const char *keyName = "repeat_gmimport_label_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/repeat_test/repeat_gmimport_schema.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    
    ret = executeCommand(g_command, "Command type: import_vschema, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema.gmjson\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *g_dataPath = "./schema_file/repeat_test/repeat_gmimport_schema_01.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -t %s -s %s", g_toolPath, g_dataPath,
        labelName, g_connServer);
    
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 20, successNum: 20",
        "Command type: import_vdata, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema_01.gmdata\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008 重复导入数据，重复导入的中间几条数据不重复
TEST_F(GmimportCache, HardWare_Offloading_001_Tool_006_GmimportCache_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "repeat_gmimport_label";
    const char *keyName = "repeat_gmimport_label_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/repeat_test/repeat_gmimport_schema.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    
    ret = executeCommand(g_command, "Command type: import_vschema, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema.gmjson\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *g_dataPath = "./schema_file/repeat_test/repeat_gmimport_schema_02.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -t %s -s %s", g_toolPath, g_dataPath,
        labelName, g_connServer);
    
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 20, successNum: 20",
        "Command type: import_vdata, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema_02.gmdata\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009 重复导入数据，重复的第一条数据错误
TEST_F(GmimportCache, HardWare_Offloading_001_Tool_006_GmimportCache_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "repeat_gmimport_label";
    const char *keyName = "repeat_gmimport_label_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/repeat_test/repeat_gmimport_schema.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    
    ret = executeCommand(g_command, "Command type: import_vschema, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema.gmjson\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *g_dataPath = "./schema_file/repeat_test/repeat_gmimport_schema_03.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -t %s -s %s", g_toolPath, g_dataPath,
        labelName, g_connServer);
    
    snprintf(compareMess, MAX_CMD_SIZE, "insert partial data unsucc when parse json object, ret = %d",
        GMERR_DATATYPE_MISMATCH);
    ret = executeCommand(g_command, compareMess,
        "insert partial data unsucc. totalNum: 20, successNum: 19, duplicateNum: 0", "import_vdata, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema_03.gmdata\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010 重复导入数据，重复导入的中间几条数据错误
TEST_F(GmimportCache, HardWare_Offloading_001_Tool_006_GmimportCache_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "repeat_gmimport_label";
    const char *keyName = "repeat_gmimport_label_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/repeat_test/repeat_gmimport_schema.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    
    ret = executeCommand(g_command, "Command type: import_vschema, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema.gmjson\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *g_dataPath = "./schema_file/repeat_test/repeat_gmimport_schema_04.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -t %s -s %s", g_toolPath, g_dataPath,
        labelName, g_connServer);
    
    snprintf(compareMess, MAX_CMD_SIZE, "insert partial data unsucc when parse json object, ret = %d",
        GMERR_DATATYPE_MISMATCH);
    ret = executeCommand(g_command, compareMess,
        "insert partial data unsucc. totalNum: 20, successNum: 19, duplicateNum: 0", "import_vdata, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema_04.gmdata\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 011 多次重复导入数据
TEST_F(GmimportCache, HardWare_Offloading_001_Tool_006_GmimportCache_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "repeat_gmimport_label";
    const char *keyName = "repeat_gmimport_label_PK";
    GmcDropVertexLabel(g_stmt, labelName);
    char const *g_filePath = "./schema_file/repeat_test/repeat_gmimport_schema.gmjson";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath,
        g_connServer);
    
    ret = executeCommand(g_command, "Command type: import_vschema, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema.gmjson\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char const *g_dataPath = "./schema_file/repeat_test/repeat_gmimport_schema_05.gmdata";
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -t %s -s %s", g_toolPath, g_dataPath,
        labelName, g_connServer);
    
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 30, successNum: 30",
        "Command type: import_vdata, Import file from",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/04_Tool/006_gmimpexp/schema_file/repeat_test/repeat_gmimport_schema_05.gmdata\" "
        "successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
