/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2017-2022. All rights reserved.
 * File Name: tableLock.cpp
 * Author: yaosiyuan
 * Date: 2022-03-24
 * Describle: 支持表锁配置能力
 */

#include "tableLock.h"
#include "../../common/hash_util.h"

class tableLock : public testing::Test {
public:
    virtual void SetUp()
    {
        uint32_t sizeMalloc = 1000;
        mallocSubData(&g_userData, sizeMalloc);
        AW_CHECK_LOG_BEGIN();
    };
    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        freeMallocSqace(g_userData);
    };
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
        int ret = 0;
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    };
    static void TearDownTestCase()
    {
        int ret = 0;
        ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        
        //恢复配置文件
        RecoverCfg();
    };
};

void restartServerLock()
{
    system("sh $TEST_HOME/tools/stop.sh");
    GmcDetachAllShmSeg();
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTableLock=1\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// enableTableLock设置为2，启动server
TEST_F(tableLock, HardWare_Offloading_001_Other_038_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTableLock=2\"");
    system("sh $TEST_HOME/tools/start.sh");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 写读更新删除10000数据（主键）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"PK";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 写读更新删除10000数据（hashcluster）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype1.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"hashcluster";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 写读更新删除10000数据（localhash）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"localhash";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 写读更新删除10000数据（local）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"local";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

int vertexSetLpmIndex(GmcStmtT *stmt, uint32_t i)
{
    int ret = 0;
    uint32_t vr_id = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t vrf_index = 1;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t dest_ip_addr = i;
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(dest_ip_addr));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t mask_len = 32;
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int lpmScan(GmcStmtT *stmt, uint32_t i, const char *key, uint32_t expect_count = 1)
{
    int ret = 0;
    bool isFinish = false;
    uint32_t scan_count = 0;
    ret = vertexSetLpmIndex(stmt, i);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    while (isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish) {
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        scan_count++;
    }
    GmcFreeIndexKey(stmt);
    return ret;
}

// 写读更新删除10000数据（lpm4）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"lpm4";
    // 根据lpm索引获取记录数
    uint64_t result = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = RECORDCOUNTSTART; i < RECORDCOUNTEND; i++) {
        ret = vertexSetLpmIndex(stmt, i);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetVertexCount(stmt, labelName, key, &result);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(result, RECORDCOUNTEND);
        ret = lpmScan(stmt, i, key);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

int vertexSetLpm6Index(GmcStmtT *stmt, uint32_t i, const char *string1)
{
    int ret = 0;
    uint32_t vr_id = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t vrf_index = 1;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, string1, 16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t mask_len = 32;
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int lpm6Scan(GmcStmtT *stmt, uint32_t i, const char *key, const char *string, uint32_t expect_count = 1)
{
    int ret = 0;
    bool isFinish = false;
    uint32_t scan_count = 0;
    ret = vertexSetLpm6Index(stmt, i, string);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    while (isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish) {
            break;
        } else if (ret != 0) {
            break;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        scan_count++;
    }
    AW_MACRO_EXPECT_EQ_INT(expect_count, scan_count);
    GmcFreeIndexKey(stmt);
    return ret;
}

void vertexLpm6Write(int recordCountStart, int recordCountEnd, GmcStmtT *stmt, const char *labelName,
    const char *f14_value)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = recordCountStart; i < recordCountEnd; i++) {
        uint32_t F1 = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t F6 = i;
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &F6, sizeof(F6));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t vr_id = 1;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t vrf_index = 1;
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_FIXED, f14_value, 16);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t mask_len = 32;
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}

// 写读更新删除10000数据（lpm6）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    const char string[] = "string1string123";
    const char *key = (char *)"lpm6";
    const char *labelName = (char *)"schema_datatype";

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema/schema_lpm6.gmjson", stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexLpm6Write(RECORDCOUNTSTART, 1, stmt, labelName, string);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isAbnormal = false;
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(1);
    // 根据lpm索引获取记录数
    uint64_t result = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = RECORDCOUNTSTART; i < 1; i++) {
        ret = vertexSetLpm6Index(stmt, i, string);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetVertexCount(stmt, labelName, "lpm6", &result);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(result, 0);
        GmcFreeIndexKey(stmt);
        ret = vertexSetLpm6Index(stmt, i, string);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = lpm6Scan(stmt, i, key, string, RECORDCOUNTSTART);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 写读更新删除10000数据（member key）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype1.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"hashcluster";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多线程对同一个张表执行写读更新删除10000数据(开启轻量化事务与系统级表锁)
TEST_F(tableLock, HardWare_Offloading_001_Other_038_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
 
#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 6;
#endif 
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, threadDml, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    const char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级别和系统级表锁，多线程对同一个张表执行写读更新删除10000数据
TEST_F(tableLock, HardWare_Offloading_001_Other_038_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 6;
#endif 
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, threadDml, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    const char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级别表锁，多线程对同一个张表执行写读更新删除10000数据
TEST_F(tableLock, HardWare_Offloading_001_Other_038_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":1}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 6;
#endif 
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, threadDml, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    const char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表锁模式，执行写操作（不开启轻量化事务）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTableLock=0\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":1}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"PK";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 不开启表级表锁模式，执行写操作（不开启轻量化事务）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTableLock=0\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"PK";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表锁模式，执行写操作（不开启轻量化事务，开启系统级表锁）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTableLock=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTableLock=1\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":1}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"PK";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 不开启表级表锁模式，执行写操作（不开启轻量化事务，开启系统级表锁）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTableLock=0\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"enableTableLock=1\"");
    system("sh $TEST_HOME/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"PK";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表锁模式，执行写操作（开启轻量化事务）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":1}";
    ret = createVertexLabel((char *)"schema_datatype1.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"hashcluster";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 不开启表级表锁模式，执行写操作（开启轻量化事务）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype1.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"hashcluster";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表锁模式，执行写操作(开启轻量化事务，开启系统级表锁)
TEST_F(tableLock, HardWare_Offloading_001_Other_038_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":1}";
    ret = createVertexLabel((char *)"schema_datatype1.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"hashcluster";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 不开启表级表锁模式，执行写操作(开启轻量化事务，开启系统级表锁)
TEST_F(tableLock, HardWare_Offloading_001_Other_038_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype1.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"hashcluster";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 带表级表锁配置项建表失败，再重新建表
TEST_F(tableLock, HardWare_Offloading_001_Other_038_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *schemaERR =
    R"([{
        "version":"2.0",
        "type":"record",
        "name":"schema_datatype",
        "fields":[
            { "name":"F1", "type":"uint32", "default":1 },
            { "name":"F2", "type":"uint8", "default":1 }
        ]
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PK",
                    "fields":["F1"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }]
    )";
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = GmcCreateVertexLabel(stmt, schemaERR, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// Gmimport导入错误的配置项enableTableLock，再修改正确后导入（开启系统级表锁）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelConfig = (char *)"{\"enableTableLock\":}";
    FILE *fptr;
    fptr = fopen("schema_datatype.gmconfig", "w");
    fprintf(fptr, "%s", labelConfig);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *cmdType = (char *)"vschema";
    const char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ToolModelOperation(GMIMPORT, cmdType, (char *)"schema_datatype.gmjson", NULL);
    EXPECT_NE(GMERR_OK, ret);
    const char *labelConfig1 = (char *)"{\"enableTableLock\":1}";
    fptr = fopen("schema_datatype.gmconfig", "w");
    fprintf(fptr, "%s", labelConfig1);
    fclose(fptr);
    ret = ToolModelOperation(GMIMPORT, cmdType, (char *)"schema_datatype.gmjson", NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 轻量化事务带表锁创建事务
TEST_F(tableLock, HardWare_Offloading_001_Other_038_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = syTransStart(conn, GMC_TRANS_USED_IN_CS);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"PK";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransCommit(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 客户端kill掉再重新拉起
TEST_F(tableLock, HardWare_Offloading_001_Other_038_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"PK";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，批量写读更新删除10000数据（主键）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"PK";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，批量写读更新删除10000数据（hashcluster）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype1.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"hashcluster";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，批量写读更新删除10000数据（localhash）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"localhash";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，批量写读更新删除10000数据（local）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"local";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，订阅所有事件，批量写读更新删除10000数据（主键）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *subAllType =
    R"({
        "name": "subVertexLabel",
        "label_name": "schema_datatype",
        "comment": "VertexLabel subscription",
        "events":
            [
                { "type": "insert", "msgTypes": [ "new object", "old object" ]},
                { "type": "delete", "msgTypes": [ "new object", "old object" ]},
                { "type": "age", "msgTypes": [ "new object", "old object" ]},
                { "type": "update", "msgTypes": [ "new object", "old object" ]},
                { "type": "replace", "msgTypes": [ "new object", "old object" ]},
                { "type": "initial_load", "msgTypes": [ "new object", "old object" ]}
            ],
        "retry": true,
        "is_reliable": true
    })";

    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *subConnName = (char *)"subConnName";
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *subName = (char *)"subVertexLabel";
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = subAllType;
    ret = GmcSubscribe(stmt, &tmp_schema, connSub, vertexSnCallback, g_userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"PK";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_REPLACE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// kv表，开启表级表锁，订阅所有事件，批量写删除10000数据
TEST_F(tableLock, HardWare_Offloading_001_Other_038_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *subKvType =
        R"({
        "name": "subKv",
        "label_name": "kv",
        "events":
            [
                { "type": "set", "msgTypes": [ "new object", "old object" ]},
                { "type": "delete", "msgTypes": [ "new object", "old object" ]},
                { "type": "initial_load", "msgTypes": [ "new object", "old object" ]}
            ],
        "retry": true,
        "is_reliable": true
    })";
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *subConnName = (char *)"subConnName";
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *tableName = (char *)"kv";
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = GmcKvCreateTable(stmt, tableName, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *subName = (char *)"subKv";
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = subKvType;
    ret = GmcSubscribe(stmt, &tmp_schema, connSub, snKvCallback, g_userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = subKvWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_KV_SET, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = subKvRemove(RECORDCOUNTSTART, RECORDCOUNTEND, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，写10000数据truncate数据
TEST_F(tableLock, HardWare_Offloading_001_Other_038_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，写10000数据对账
TEST_F(tableLock, HardWare_Offloading_001_Other_038_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，大对象，写更新读删除10000数据
TEST_F(tableLock, HardWare_Offloading_001_Other_038_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWriteBigObj(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"PK";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁(不开启系统级)，批量写读更新删除10000数据（主键）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"PK";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁(不开启系统级)，批量写读更新删除10000数据（hashcluster）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype1.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"hashcluster";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁(不开启系统级)，批量写读更新删除10000数据（localhash）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"localhash";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁(不开启系统级)，批量写读更新删除10000数据（local）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"local";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁(不开启系统级)，订阅所有事件，批量写读更新删除10000数据（主键）
TEST_F(tableLock, HardWare_Offloading_001_Other_038_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *subAllType =
    R"({
        "name": "subVertexLabel",
        "label_name": "schema_datatype",
        "comment": "VertexLabel subscription",
        "events":
            [
                { "type": "insert", "msgTypes": [ "new object", "old object" ]},
                { "type": "delete", "msgTypes": [ "new object", "old object" ]},
                { "type": "age", "msgTypes": [ "new object", "old object" ]},
                { "type": "update", "msgTypes": [ "new object", "old object" ]},
                { "type": "replace", "msgTypes": [ "new object", "old object" ]},
                { "type": "initial_load", "msgTypes": [ "new object", "old object" ]}
            ],
        "retry": true,
        "is_reliable": true
    })";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *subConnName = (char *)"subConnName";
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *subName = (char *)"subVertexLabel";
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = subAllType;
    ret = GmcSubscribe(stmt, &tmp_schema, connSub, vertexSnCallback, g_userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"PK";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_REPLACE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// kv表，开启表级表锁(不开启系统级)，订阅所有事件，批量写读删除10000数据
TEST_F(tableLock, HardWare_Offloading_001_Other_038_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *subKvType =
        R"({
        "name": "subKv",
        "label_name": "kv",
        "events":
            [
                { "type": "set", "msgTypes": [ "new object", "old object" ]},
                { "type": "delete", "msgTypes": [ "new object", "old object" ]},
                { "type": "initial_load", "msgTypes": [ "new object", "old object" ]}
            ],
        "retry": true,
        "is_reliable": true
    })";
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *subConnName = (char *)"subConnName";
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *tableName = (char *)"kv";
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = GmcKvCreateTable(stmt, tableName, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *subName = (char *)"subKv";
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = subKvType;
    ret = GmcSubscribe(stmt, &tmp_schema, connSub, snKvCallback, g_userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = subKvWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_KV_SET, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = subKvRemove(RECORDCOUNTSTART, RECORDCOUNTEND, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁(不开启系统级)，写10000数据truncate数据
TEST_F(tableLock, HardWare_Offloading_001_Other_038_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁(不开启系统级)，写10000数据对账
TEST_F(tableLock, HardWare_Offloading_001_Other_038_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁(不开启系统级)，大对象，写更新读删除10000数据
TEST_F(tableLock, HardWare_Offloading_001_Other_038_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWriteBigObj(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"PK";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 批量写读更新删除10000数据（主键），开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"PK";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 批量写读更新删除10000数据（hashcluster），开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype1.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"hashcluster";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 批量写读更新删除10000数据（localhash），开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"localhash";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 批量写读更新删除10000数据（local），开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"local";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 订阅所有事件，批量写读更新删除10000数据（主键），开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *subAllType =
    R"({
        "name": "subVertexLabel",
        "label_name": "schema_datatype",
        "comment": "VertexLabel subscription",
        "events":
            [
                { "type": "insert", "msgTypes": [ "new object", "old object" ]},
                { "type": "delete", "msgTypes": [ "new object", "old object" ]},
                { "type": "age", "msgTypes": [ "new object", "old object" ]},
                { "type": "update", "msgTypes": [ "new object", "old object" ]},
                { "type": "replace", "msgTypes": [ "new object", "old object" ]},
                { "type": "initial_load", "msgTypes": [ "new object", "old object" ]}
            ],
        "retry": true,
        "is_reliable": true
    })";

    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *subConnName = (char *)"subConnName";
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *subName = (char *)"subVertexLabel";
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = subAllType;
    ret = GmcSubscribe(stmt, &tmp_schema, connSub, vertexSnCallback, g_userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    const char *key = (char *)"PK";
    uint32_t batchNum = 1000;
    for (int i = 0; i < RECORDCOUNTEND/batchNum; i++) {
        ret = vertexWriteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexUpdateBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = vertexDeleteBatch((i * batchNum), ((i + 1) * batchNum), stmt, conn, labelName, key);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_REPLACE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_UPDATE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// kv表，订阅所有事件，批量写读删除10000数据，开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *subKvType =
        R"({
        "name": "subKv",
        "label_name": "kv",
        "events":
            [
                { "type": "set", "msgTypes": [ "new object", "old object" ]},
                { "type": "delete", "msgTypes": [ "new object", "old object" ]},
                { "type": "initial_load", "msgTypes": [ "new object", "old object" ]}
            ],
        "retry": true,
        "is_reliable": true
    })";
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *subConnName = (char *)"subConnName";
    int chanRingLen = 256;
    GmcConnT *connSub = NULL;
    GmcStmtT *stmtSub = NULL;
    ret = testSubConnect(&connSub, &stmtSub, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *tableName = (char *)"kv";
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = GmcKvCreateTable(stmt, tableName, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, tableName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *subName = (char *)"subKv";
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = subKvType;
    ret = GmcSubscribe(stmt, &tmp_schema, connSub, snKvCallback, g_userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = subKvWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_KV_SET, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = subKvRemove(RECORDCOUNTSTART, RECORDCOUNTEND, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_DELETE, RECORDCOUNTEND);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcKvDropTable(stmt, tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(connSub, stmtSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 写10000数据truncate数据，开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 写10000数据对账，开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDeleteAllFast(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 大对象，写更新读删除10000数据，开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWriteBigObj(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"PK";
    ret = vertexUpdate(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，多线程，单表，每个线程执行写操作，开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
  
#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 6;
#endif
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, threadWrite, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    const char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，多线程，单表，每个线程执行写或读操作，开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 20;
#endif
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    ThreadFunc thr_func[2] = {threadWrite, threadRead};
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, thr_func[i % 2], NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    const char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，多线程，单表，每个线程执行建表删除表操作，开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    restartServerLock();
    int ret = 0;
    
#if (defined TEST_STATIC_ASAN) || (defined ENV_RTOSV2X)
int thr_num = 6;
// 光启持久化下会存在系统表记录表元数据多线程建相同表存在并发抢锁
#elif defined FEATURE_PERSISTENCE
int thr_num = 2;
#else
int thr_num = 100;
#endif 
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, threadCreateDrop, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，多线程，多表，每个线程对一张表执行建表删除表操作，开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
 
#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 20;
#endif 
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    int index[thr_num] = {0};
    for (int i = 0; i < thr_num; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr[i], NULL, threadCreateDrop1, (void *)&index[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，多线程，单表，每个线程执行写更新删除1000条数据，开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 20;
#endif 
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, threadDml, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    const char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，多线程，多表，每个线程执对一张表行写更新删除1000条数据，开启系统级表锁
TEST_F(tableLock, HardWare_Offloading_001_Other_038_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;
    
#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 20;
#endif 
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    int index[thr_num] = {0};
    for (int i = 0; i < thr_num; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr[i], NULL, threadTableDml, (void *)&index[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，多线程，单表，每个线程执行建表删除表操作
TEST_F(tableLock, HardWare_Offloading_001_Other_038_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    // 2023.10.30 （光启）并发删除同名表 系统表键值重复GMERR_UNIQUE_VIOLATION
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);

    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    restartServerLock();
    int ret = 0;
    
#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 20;
#endif 
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, threadCreateDrop, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，多线程，多表，每个线程对一张表执行建表删除表操作
TEST_F(tableLock, HardWare_Offloading_001_Other_038_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    
#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 20;
#endif 
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    int index[thr_num] = {0};
    for (int i = 0; i < thr_num; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr[i], NULL, threadCreateDrop1, (void *)&index[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，多线程，单表，每个线程执行写更新删除10000条数据
TEST_F(tableLock, HardWare_Offloading_001_Other_038_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 20;
#endif 
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, threadDml, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    const char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 开启表级表锁，多线程，多表，每个线程对一张表执行写更新删除1000条数据
TEST_F(tableLock, HardWare_Offloading_001_Other_038_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 20;
#endif 
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, threadDml, NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    const char *labelName = (char *)"schema_datatype";
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多线程，每个线程创建一张表，部分表开启表锁，执行增删改查
TEST_F(tableLock, HardWare_Offloading_001_Other_038_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    restartServerLock();
    int ret = 0;

#if defined TEST_STATIC_ASAN
int thr_num = 6;
#else
int thr_num = 20;
#endif
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    int index[thr_num] = {0};
    for (int i = 0; i < thr_num; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr[i], NULL, threadTableDml, (void *)&index[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 一般复杂表（开启表级表锁）、大对象表（开启表级表锁）、kv表（开启表级表锁）、
// 简单表（不开启表级表锁）4张表，多线程，分别执行订阅、批量、dml、ddl、truncate、老化等操作
TEST_F(tableLock, HardWare_Offloading_001_Other_038_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg1, errorMsg2);
    const char *schemaBigObj =
    R"([{
        "version": "2.0",
        "type": "record",
        "name": "schemaBigObj",
        "fields": [
            { "name": "F1", "type": "uint32", "default": 1 },
            { "name": "string", "type": "string", "default": "1111" },
            { "name": "string1", "type": "string", "default": "1111" },
            { "name": "string2", "type": "string", "default": "1111" }
        ],
        "keys":
            [
                {
                    "node": "schemaBigObj",
                    "name": "PK",
                    "fields": [ "F1" ],
                    "index": { "type": "primary" },
                    "constraints": { "unique": true }
                }
            ]
        }]
    )";

    const char *schemaSimple =
        R"([{
        "version": "2.0",
        "type": "record",
        "name": "schemaSimple",
        "fields": [
            { "name": "F1", "type": "uint32", "default": 1 },
            { "name": "F2", "type": "uint8", "default": 1 },
            { "name": "F3", "type": "int16", "default": 1 },
            { "name": "F4", "type": "uint16", "default": 1 },
            { "name": "F5", "type": "int32", "default": 1 },
            { "name": "F6", "type": "uint32", "default": 1 },
            { "name": "F7", "type": "int64", "default": 1 },
            { "name": "F8", "type": "uint64", "default": 1 },
            { "name": "F9", "type": "int8", "default": 1 },
            { "name": "F10", "type": "double", "nullable": true, "default": 1 }
        ],
        "keys":
            [
                {
                    "node": "schemaSimple",
                    "name": "PK",
                    "fields": [ "F1" ],
                    "index": { "type": "primary" },
                    "constraints": { "unique": true }
                }
            ]
        }]
    )";

    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    // 一般复杂表
    const char *labelName = (char *)"schema_datatype";
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // kv表
    const char *tableNameKV = (char *)"kv";
    ret = GmcKvCreateTable(stmt, tableNameKV, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 简单表
    const char *tableSimple = (char *)"schemaSimple";
    ret = GmcCreateVertexLabel(stmt, schemaSimple, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 大对象
    const char *tableBigObj = (char *)"schemaBigObj";
    ret = GmcCreateVertexLabel(stmt, schemaBigObj, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int thr_num = 8;
    pthread_t thr_arr[thr_num];
    void *thr_ret[thr_num];
    ThreadFunc thr_func[8] = { threadWriteBatch, threadDmlKv, threadWriteBigObj,
        threadWriteSimple, threadSub, threadDml, threadTruncate, threadCheck };
    for (int i = 0; i < thr_num; i++) {
        ret = pthread_create(&thr_arr[i], NULL, thr_func[i % 8], NULL);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (int y = 0; y < thr_num; y++) {
        ret = pthread_join(thr_arr[y], &thr_ret[y]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcKvDropTable(stmt, tableNameKV);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, tableSimple);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, tableBigObj);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 将两张表，一张表不开启表锁一张表开启表锁写满内存，执行truncate，再写满另外一张表内存
TEST_F(tableLock, HardWare_Offloading_001_Other_038_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    const char *schemaBigObj =
    R"([{
        "version": "2.0",
        "type": "record",
        "name": "schemaBigObj",
        "fields": [
            { "name": "F1", "type": "uint32", "default": 1 },
            { "name": "string", "type": "string", "default": "1111" },
            { "name": "string1", "type": "string", "default": "1111" },
            { "name": "string2", "type": "string", "default": "1111" }
        ],
        "keys":
            [
                {
                    "node": "schemaBigObj",
                    "name": "PK",
                    "fields": [ "F1" ],
                    "index": { "type": "primary" },
                    "constraints": { "unique": true }
                }
            ]
        }]
    )";

    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWriteBigObj(RECORDCOUNTSTART, RECORDCOUNTEND * 100, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *key = (char *)"PK";
    ret = vertexDelete(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcResetStmt(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = vertexWriteBigObj(RECORDCOUNTSTART, RECORDCOUNTEND * 100, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    const char *bigObjName = (char *)"schemaBigObj";
    ret = GmcCreateVertexLabel(stmt, schemaBigObj, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, bigObjName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 将两张表，一张表不开启表锁一张表开启表锁写满内存，执行对账，再写满另外一张表内存
TEST_F(tableLock, HardWare_Offloading_001_Other_038_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=300\"");
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createVertexLabel((char *)"schema_datatype.gmjson", stmt, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWriteBigObj(RECORDCOUNTSTART, RECORDCOUNTEND * 100, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBeginCheck(stmt, labelName, FULLTABLE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isAbnormal = false;
    ret = GmcEndCheck(stmt, labelName, FULLTABLE, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(10);
    ret = vertexWriteBigObj(RECORDCOUNTSTART, 100, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 建2个表一张表不开启表锁一张表开启表锁，分别写入10万数据；truncate掉表2数据，读取表1数据
TEST_F(tableLock, HardWare_Offloading_001_Other_038_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *schemaSimple =
        R"([{
        "version": "2.0",
        "type": "record",
        "name": "schemaSimple",
        "fields": [
            { "name": "F1", "type": "uint32", "default": 1 },
            { "name": "F2", "type": "uint8", "default": 1 },
            { "name": "F3", "type": "int16", "default": 1 },
            { "name": "F4", "type": "uint16", "default": 1 },
            { "name": "F5", "type": "int32", "default": 1 },
            { "name": "F6", "type": "uint32", "default": 1 },
            { "name": "F7", "type": "int64", "default": 1 },
            { "name": "F8", "type": "uint64", "default": 1 },
            { "name": "F9", "type": "int8", "default": 1 },
            { "name": "F10", "type": "double", "nullable": true, "default": 1 }
        ],
        "keys":
            [
                {
                    "node": "schemaSimple",
                    "name": "PK",
                    "fields": [ "F1" ],
                    "index": { "type": "primary" },
                    "constraints": { "unique": true }
                }
            ]
        }]
    )";
    
    restartServerLock();
    int ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *tableSimple = (char *)"schemaSimple";
    ret = GmcCreateVertexLabel(stmt, schemaSimple, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, tableSimple, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10000; i++) {
        uint32_t F1 = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1, sizeof(F1));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int16_t F3 = i % 32768;
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &F3, sizeof(F3));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint16_t F4 = i;
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT16, &F4, sizeof(F4));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int32_t F5 = i;
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &F5, sizeof(F5));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t F6 = i;
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &F6, sizeof(F6));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int64_t F7 = i;
        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_INT64, &F7, sizeof(F7));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t F8 = i;
        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UINT64, &F8, sizeof(F8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int8_t F9 = i % 127;
        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT8, &F9, sizeof(F9));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t F2 = i % 255;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT8, &F2, sizeof(F2));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        double f10_value = i;
        ret = GmcSetVertexProperty(stmt, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcTruncateVertexLabel(stmt, tableSimple);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelConfig = (char *)"{\"enableTableLock\":true}";
    ret = createVertexLabel((char *)"schema_datatype1.gmjson", stmt, labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = (char *)"schema_datatype";
    ret = vertexWrite(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *key = (char *)"hashcluster";
    ret = vertexRead(RECORDCOUNTSTART, RECORDCOUNTEND, stmt, labelName, key);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, tableSimple);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
