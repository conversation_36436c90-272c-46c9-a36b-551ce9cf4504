/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

#ifndef VIEW_H
#define VIEW_H

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
const char *g_configJson = R"({"max_record_count":1000000})";
char const *g_vertexLabelView = "V\\$CATA_VERTEX_LABEL_INFO";
const char *g_labelName_001 = "Label_001";
const char *g_labelName_002 = "Label_002";
const char *g_labelName_003 = "Label_003";
const char *g_labelName_004 = "Label_004";
const char *g_labelName_005 = "Label_005";
const char *g_labelName_006 = "Label_006";
const char *g_labelName_007 = "Label_007";
const char *g_labelName_008 = "Label_008";
const char *g_labelName_009 = "Label_009";
const char *g_labelName_010 = "Label_010";
const char *g_labelName_011 = "Label_011";
const char *g_labelName_012 = "Label_012";
const char *g_labelName_013 = "Label_013";

char g_labelName1[16] = "OP_T0";

int32_t start_num1 = 0;
int32_t end_num1 = 100;
int32_t start_num2 = 100;
int32_t end_num2 = 200;
int array_num = 3;
int vector_num = 3;

// lpm4
void check_indexName_nodeName_fieldName_001(char *command)
{
    int ret = executeCommand(command, "Label_001", "Label_PK", "F0_Label_PK", "primary", "TRUE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "Label_001", "Label_hash", "F1_Label_hash", "localhash", "FALSE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "Label_001", "Label_local", "F2_Label_local", "local", "TRUE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "Label_001", "Label_hashcluster", "F3_Label_hashcluster", "hashcluster", "FALSE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "lpm4_test", "Label_lpm4", "dest_ip_addr", "lpm4_tree_bitmap", "TRUE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// lpm6
void check_indexName_nodeName_fieldName_002(char *command)
{
    int ret = executeCommand(command, "Label_002", "Label_PK", "F0_Label_PK", "primary", "TRUE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "Label_002", "Label_hash", "F1_Label_hash", "localhash", "FALSE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "Label_002", "Label_local", "F2_Label_local", "local", "TRUE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "Label_002", "Label_hashcluster", "F3_Label_hashcluster", "hashcluster", "FALSE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "lpm6_test", "Label_lpm6", "dest_ip_addr", "lpm6_tree_bitmap", "TRUE");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// primary 8 fields
void check_primary_eight_fields(char *command)
{
    int ret = executeCommand(command, "Label_003", "Label_PK", "F0_Label_PK", "F1_Label_PK", "F2_Label_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "F3_Label_PK", "F4_Label_PK", "F5_Label_PK", "F6_Label_PK", "F7_Label_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// localhash 8 fields
void check_localhash_eight_fields(char *command)
{
    int ret = executeCommand(command, "Label_004", "Label_hash", "F0_Label_PK", "F1_Label_PK", "F2_Label_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "F3_Label_PK", "F4_Label_PK", "F5_Label_PK", "F6_Label_PK", "F7_Label_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// local 8 fields
void check_local_eight_fields(char *command)
{
    int ret = executeCommand(command, "Label_005", "Label", "F0_Label_PK", "F1_Label_PK", "F2_Label_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "F3_Label_PK", "F4_Label_PK", "F5_Label_PK", "F6_Label_PK", "F7_Label_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// hashcluster 8 fields
void check_hashcluster_eight_fields(char *command)
{
    int ret = executeCommand(command, "Label_006", "Label_local", "F0_Label_PK", "F1_Label_PK", "F2_Label_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "F3_Label_PK", "F4_Label_PK", "F5_Label_PK", "F6_Label_PK", "F7_Label_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// localhash 8 fieldsDis
void check_localhash_eight_fieldsDis(char *command)
{
    int ret =
        executeCommand(command, "Label_007", "Label_hash", "F0_Label_PK", "F4_Label_localhash", "F7_Label_localhash");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "F1_Label_PK", "F12_Label_localhash", "F14_Label_localhash",
        "F15_Label_localhash", "F17_Label_localhash");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// local 8 fieldsDis
void check_local_eight_fieldsDis(char *command)
{
    int ret = executeCommand(command, "Label_008", "Label_local", "F0_Label_PK", "F2_Label_PK", "F3_Label_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret =
        executeCommand(command, "F5_Label_PK", "F7_Label_PK", "F11_Label_local", "F13_Label_local", "F17_Label_local");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// hashcluster 8 fieldsDis
void check_hashcluster_eight_fieldsDis(char *command)
{
    int ret = executeCommand(command, "Label_009", "Label_local", "F0_Label_PK", "F1_Label_PK", "F2_Label_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "F3_Label_PK", "F4_Label_PK", "F5_Label_PK", "F6_Label_PK", "F7_Label_PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// localhash 16 keys
void check_localhash_Sixteenth_keys(char *command)
{
    int ret = executeCommand(command, "Label_010", "Label_hash02", "Label_hash03", "Label_hash04", "Label_hash06");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "Label_hash07", "Label_hash09", "Label_hash12", "Label_hash14", "Label_hash16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// local 16 keys
void check_local_Sixteenth_keys(char *command)
{
    int ret = executeCommand(command, "Label_011", "Label_local02", "Label_local03", "Label_local04", "Label_local06");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "Label_local07", "Label_local09", "Label_local12", "Label_local14", "Label_local16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// hashcluster 16 keys
void check_hashcluster_Sixteenth_keys(char *command)
{
    int ret = executeCommand(
        command, "Label_012", "Label_cluster02", "Label_cluster03", "Label_cluster04", "Label_cluster06");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(
        command, "Label_cluster07", "Label_cluster09", "Label_cluster12", "Label_cluster14", "Label_cluster16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// index 16 keys
void check_index_Sixteenth_keys(char *command)
{
    int ret = executeCommand(command, "Label_013", "Label_local01", "Label_cluster03", "Label_hash04", "Label_local05");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(command, "Label_hash07", "Label_hash09", "Label_cluster12", "Label_hash14", "Label_cluster16");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    int32_t f2_value = 2 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f3_value = 3 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f4_value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int8_t f6_value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = (7 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f9_value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f10_value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f12_value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_P(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f1_value = 1 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f2_value = 2 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f3_value = 3 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f4_value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"P4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"P5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int8_t f6_value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"P6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = (7 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"P7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f9_value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f10_value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f12_value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f1_value = 1 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f2_value = 2 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f3_value = 3 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f4_value = (4 + i) % 32768;
    ret = GmcNodeSetPropertyByName(node, (char *)"A4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = (5 + i) % 65536;
    ret = GmcNodeSetPropertyByName(node, (char *)"A5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int8_t f6_value = (6 + i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"A6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f7_value = (7 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"A7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(node, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f9_value = 9.11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f10_value = 10.68 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 + i;
    ret = GmcNodeSetPropertyByName(node, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f12_value = (i) % 128;
    ret = GmcNodeSetPropertyByName(node, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = (13 + i) % 256;
    ret = GmcNodeSetPropertyByName(node, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(node, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_LocalhashUnique(GmcNodeT *node, int i)
{
    int ret = 0;
    uint64_t f1_value = i + 1;
    ret = GmcNodeSetPropertyByName(node, (char *)"F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_PK(GmcNodeT *node, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcNodeSetPropertyByName(node, (char *)"F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 只有array节点时获取node节点
void TestGetRootAndChild_A(GmcStmtT *stmt, GmcNodeT **root, GmcNodeT **T1, GmcNodeT **T2)
{
    GmcNodeT *Root = NULL, *node1 = NULL, *node2 = NULL;
    *root = NULL;
    *T1 = NULL;
    *T2 = NULL;
    // 获取根节点与子节点
    int ret = GmcGetRootNode(stmt, &Root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(Root, "T1", &node1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(node1, "T2", &node2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    *root = Root;
    *T1 = node1;
    *T2 = node2;
}

void TestGmcInsertVertex(
    GmcStmtT *stmt, bool bool_value, char *f14_value, int start_num, int end_num, int array_num, const char *labelName)
{
    int32_t ret = 0;
    void *label = NULL;
    unsigned int len = 0;
    int affectRows = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL;
    // 获取根节点和子节点
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestGetRootAndChild_A(stmt, &root, &T1, &T2);
    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        TestGmcSetNodePropertyByName_PK(root, i);
        TestGmcSetNodePropertyByName_LocalhashUnique(root, i);
        TestGmcSetNodePropertyByName_R(root, i, bool_value, f14_value);
        TestGmcSetNodePropertyByName_P(T1, i, bool_value, f14_value);

        // 插入array节点
        for (uint32_t j = 0; j < array_num; j++) {
            ret = GmcNodeGetElementByIndex(T2, j, &T2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_A(T2, i, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    }
}

void TestGetUniqueLocalHash(GmcNodeT *node, int i)
{
    int ret = 0;
    bool isNull = 0;
    uint64_t f1_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"F1", &f1_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(1 + i, f1_value);
}

void TestGmcGetNodePropertyByName_R(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull = 0;

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F2", &f2_value, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(2 + i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F3", &f3_value, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(3 + i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F4", &f4_value, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((4 + i) % 32768, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F5", &f5_value, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((5 + i) % 65536, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F6", &f6_value, sizeof(int8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((6 + i) % 128, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F7", &f7_value, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((7 + i) % 256, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    ret = (labs((float)(9.11 + i) - f9_value) < __FLT_EPSILON__) ? 0 : 1;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    ret = (labs((double)(10.68 + i) - f10_value) < __DBL_EPSILON__) ? 0 : 1;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(11 + i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((i) % 128, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(13 + i) % 256;
    AW_MACRO_ASSERT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"F14", &propSize);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[100] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"F14", &string_value, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"F15", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"F16", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGmcGetNodePropertyByName_P(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull = 0;
    int64_t f0_value = 0;
    ret = GmcNodeGetPropertyByName(node, (char *)"P0", &f0_value, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P1", &f1_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((1 + i), f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P2", &f2_value, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(2 + i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P3", &f3_value, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(3 + i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P4", &f4_value, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((4 + i) % 32768, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P5", &f5_value, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((5 + i) % 65536, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P6", &f6_value, sizeof(int8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((6 + i) % 128, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P7", &f7_value, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((7 + i) % 256, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    ret = (labs((float)(9.11 + i) - f9_value) < __FLT_EPSILON__) ? 0 : 1;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    ret = (labs((double)(10.68 + i) - f10_value) < __DBL_EPSILON__) ? 0 : 1;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(11 + i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((i) % 128, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(13 + i) % 256;
    AW_MACRO_ASSERT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"P14", &propSize);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[7] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"P14", &string_value, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    ret = (strcmp(string_value, f14_value));
    if (ret != 0) {
        printf("string_value:%s f14_value:%s i:%d\n", string_value, f14_value, i);
    }
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"P15", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"P16", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGmcGetNodePropertyByName_A(GmcNodeT *node, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int64_t f0_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A0", &f0_value, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(i, f0_value);

    uint64_t f1_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A1", &f1_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(1 + i, f1_value);

    int32_t f2_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A2", &f2_value, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(2 + i, f2_value);

    uint32_t f3_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A3", &f3_value, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(3 + i, f3_value);

    int16_t f4_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A4", &f4_value, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((4 + i) % 32768, f4_value);

    uint16_t f5_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A5", &f5_value, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((5 + i) % 65536, f5_value);

    int8_t f6_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A6", &f6_value, sizeof(int8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((6 + i) % 128, f6_value);

    uint8_t f7_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A7", &f7_value, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((7 + i) % 256, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    ret = (labs((float)(9.11 + i) - f9_value) < __FLT_EPSILON__) ? 0 : 1;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f10_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    ret = (labs((double)(10.68 + i) - f10_value) < __DBL_EPSILON__) ? 0 : 1;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(11 + i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT((i % 128), f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(node, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    unsigned char k = (unsigned char)(13 + i) % 256;
    AW_MACRO_ASSERT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(node, (char *)"A14", &propSize);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[7] = {0};
    ret = GmcNodeGetPropertyByName(node, (char *)"A14", &string_value, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"A15", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memset(string_value, 0, 7);
    ret = GmcNodeGetPropertyByName(node, (char *)"A16", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = memcmp(f14_value, string_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGmcDirectFetchVertex(GmcStmtT *stmt, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, char *labelName, const char *keyName, bool isRead)
{
    int32_t ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL, *T2 = NULL;
    bool isFinish = false;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读取顶点
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        if (isRead == false) {
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(true, isFinish);
        } else if (isRead == true) {
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // 获取根节点和子节点
            TestGetRootAndChild_A(stmt, &root, &T1, &T2);
            TestGetUniqueLocalHash(root, i);
            TestGmcGetNodePropertyByName_R(root, i, bool_value, f14_value);
            TestGmcGetNodePropertyByName_P(T1, i, bool_value, f14_value);
            // 读取array节点
            for (uint32_t j = 0; j < array_num; j++) {
                ret = GmcNodeGetElementByIndex(T2, j, &T2);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                TestGmcGetNodePropertyByName_A(T2, i, bool_value, f14_value);
            }
        }
    }
}

#endif /* VIEW_H */

