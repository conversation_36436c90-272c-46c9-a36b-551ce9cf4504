/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "UndoLogOptimize.h"
#include "../../common/hash_util.h"

#if defined ENV_RTOSV2X
#define COUNTS 50
#else
#define COUNTS 100
#endif
class UndoLogOptimize : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        //恢复配置文件
        RecoverCfg();
    };

    virtual void SetUp()
    {
        // 创建连接
        int ret = testGmcConnect(&g_conn_sync, &g_stmt_sync);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&g_conn_async1, &g_stmt_async1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // 避免其他用例失败影响下一个执行的用例
        GmcUseNamespace(g_stmt_sync, "yang");
        GmcDropVertexLabel(g_stmt_sync, "root_NP");
        GmcDropVertexLabel(g_stmt_sync, "root_P");
        GmcDropNamespace(g_stmt_sync, "yang");
        g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
        g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
        g_trxConfig.readOnly = false;
        g_trxConfig.trxType = GMC_OPTIMISTIC_TRX;
        AsyncUserDataT data = {0};
        // 配置ns级别，RR+乐观事务模式
        GmcNspCfgT yangNspCfg = {};
        yangNspCfg.tablespaceName = NULL;
        yangNspCfg.namespaceName = "yang";
        yangNspCfg.userName = "abc";
        yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
        ret = GmcCreateNamespaceWithCfgAsync(g_stmt_async, &yangNspCfg, create_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        ret = GmcUseNamespaceAsync(g_stmt_async, "yang", use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        CreateTree_Container_List(g_stmt_async, data);
        // 预置数据
        testYangPresetDataContainerList(g_conn_async, g_stmt_async, 100);
        ret = GmcUseNamespace(g_stmt_sync, "yang");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char *labelJson = NULL;
        GmcDropVertexLabel(g_stmt_sync, "bigobject");
        readJanssonFile("./schema_file/big_object.gmjson", &labelJson);
        EXPECT_NE(labelJson, (void *)NULL);
        ret = GmcCreateVertexLabel(g_stmt_sync, labelJson, g_labelconfig);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(labelJson);
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
        AsyncUserDataT data = {0};
        // 删除yang表和vertex以及namespace
        int ret = GmcClearNamespaceAsync(g_stmt_async, "yang", ClearNSCallbak, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        ret = GmcDropNamespaceAsync(g_stmt_async, "yang", drop_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        // 释放连接
        ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn_async, g_stmt_async);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_conn_async1, g_stmt_async1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
};
// 001 简单表预置数据，多个乐观事务，重复更新同一条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    int ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertVertex(g_stmt_sync, i, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespace(stmt1, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn1, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertex(stmt1, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为300
    ret = GmcUseNamespace(stmt2, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn2, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertex(stmt2, 1, 300);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertex(stmt1, 1, 200);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertex(stmt2, 1, 400);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommit(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 简单表预置数据，多个乐观事务，重复更新多条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    int ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertVertex(g_stmt_sync, i, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespace(stmt1, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn1, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertVertex(stmt1, i, 100);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务2 更新1为300
    ret = GmcUseNamespace(stmt2, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn2, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertVertex(stmt2, i, 300);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);
    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertVertex(stmt1, i, 200);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertVertex(stmt2, i, 400);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);

    // 先提交事务1再提交事务2
    ret = GmcTransCommit(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 简单表预置数据，乐观事务，创建savepoint重复更新同一条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    int ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertVertex(g_stmt_sync, i, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespaceAsync(stmt1, "yang", use_namespace_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransStartAsync(conn1, &g_trxConfig, trans_start_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertexAsync(stmt1, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为300
    ret = GmcUseNamespaceAsync(stmt2, "yang", use_namespace_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = GmcTransStartAsync(conn2, &g_trxConfig, trans_start_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertexAsync(stmt2, 1, 300);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);
    ret = GmcTransCreateSavepointAsync(conn1, "sp1", TransSavePointCb, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCreateSavepointAsync(conn2, "sp2", TransSavePointCb, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertexAsync(stmt1, 1, 200);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertexAsync(stmt2, 1, 400);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);

    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore + 2);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);

    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommitAsync(conn1, trans_commit_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCommitAsync(conn2, trans_commit_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data2.status);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 简单表预置数据，乐观事务，创建多个savepoint重复更新同一条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    int ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertVertex(g_stmt_sync, i, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespaceAsync(stmt1, "yang", use_namespace_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransStartAsync(conn1, &g_trxConfig, trans_start_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertexAsync(stmt1, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为300
    ret = GmcUseNamespaceAsync(stmt2, "yang", use_namespace_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = GmcTransStartAsync(conn2, &g_trxConfig, trans_start_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertexAsync(stmt2, 1, 300);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);
    ret = GmcTransCreateSavepointAsync(conn1, "sp1", TransSavePointCb, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCreateSavepointAsync(conn2, "sp2", TransSavePointCb, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertexAsync(stmt1, 1, 200);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertexAsync(stmt2, 1, 400);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCreateSavepointAsync(conn1, "sp3", TransSavePointCb, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCreateSavepointAsync(conn2, "sp4", TransSavePointCb, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertexAsync(stmt1, 1, 500);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertexAsync(stmt2, 1, 600);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore + 4);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommitAsync(conn1, trans_commit_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCommitAsync(conn2, trans_commit_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data2.status);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 yang表预置数据，多个乐观事务，重复更新同一条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt1_1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    GmcStmtT *stmt2_2 = NULL;
    GmcBatchT *batch1 = NULL;
    GmcBatchT *batch2 = NULL;
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    uint32_t pkVal = 1;
    int ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn1, &stmt1_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn2, &stmt2_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 事务1 更新1为100
    ret = GmcUseNamespaceAsync(stmt1, "yang", use_namespace_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransStartAsync(conn1, &g_trxConfig, trans_start_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(conn1, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode1 = NULL;
    ret = GmcGetRootNode(stmt1, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ListNode1 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt1_1, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, stmt1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1_1, &ListNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt1_1, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1_1, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode1, 200, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch1, data1, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为300
    ret = GmcUseNamespaceAsync(stmt2, "yang", use_namespace_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = GmcTransStartAsync(conn2, &g_trxConfig, trans_start_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(conn2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt2, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode2 = NULL;
    GmcNodeT *childNode2 = NULL;
    ret = GmcGetRootNode(stmt2, &rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode2, "C1", GMC_OPERATION_NONE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ListNode2 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt2_2, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch2, stmt2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2_2, &ListNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2_2, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2_2, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode2, 300, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch2, data2, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'T1\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    // 事务1 更新100为200
    ret = testBatchPrepareAndSetDiff(conn1, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1_1, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, stmt1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1_1, &ListNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt1_1, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1_1, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode1, 500, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch1, data1, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testBatchPrepareAndSetDiff(conn2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt2, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2, &rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode2, "C1", GMC_OPERATION_NONE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2_2, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch2, stmt2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2_2, &ListNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2_2, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2_2, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode2, 400, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch2, data2, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'T1\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommitAsync(conn1, trans_commit_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCommitAsync(conn2, trans_commit_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data2.status);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 yang表预置数据，多个乐观事务，重复更新多条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt1_1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    GmcStmtT *stmt2_2 = NULL;
    GmcBatchT *batch1 = NULL;
    GmcBatchT *batch2 = NULL;
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    uint32_t pkVal = 1;
    int ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn1, &stmt1_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn2, &stmt2_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 事务1 更新为100
    ret = GmcUseNamespaceAsync(stmt1, "yang", use_namespace_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransStartAsync(conn1, &g_trxConfig, trans_start_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(conn1, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode1 = NULL;
    ret = GmcGetRootNode(stmt1, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ListNode1 = NULL;
    for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1_1, "T1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch1, stmt1, stmt1_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt1_1, &ListNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt1_1, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(ListNode1, 100, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch1, stmt1_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = testBatchExecuteAndWait(batch1, data1, 11, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新为200
    ret = GmcUseNamespaceAsync(stmt2, "yang", use_namespace_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = GmcTransStartAsync(conn2, &g_trxConfig, trans_start_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(conn2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt2, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode2 = NULL;
    GmcNodeT *childNode2 = NULL;
    ret = GmcGetRootNode(stmt2, &rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode2, "C1", GMC_OPERATION_NONE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ListNode2 = NULL;
    for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt2_2, "T1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch2, stmt2, stmt2_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt2_2, &ListNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt2_2, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt2_2, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(ListNode2, 200, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch2, stmt2_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = testBatchExecuteAndWait(batch2, data2, 11, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    // 事务1 更新为300
    ret = testBatchPrepareAndSetDiff(conn1, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt1_1, "T1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch1, stmt1, stmt1_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt1_1, &ListNode1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt1_1, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt1_1, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(ListNode1, 300, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch1, stmt1_1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = testBatchExecuteAndWait(batch1, data1, 11, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新为400
    ret = testBatchPrepareAndSetDiff(conn2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt2, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2, &rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode2, "C1", GMC_OPERATION_NONE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 10; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt2_2, "T1", GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcYangBindChild(batch2, stmt2, stmt2_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetRootNode(stmt2_2, &ListNode2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt2_2, 1, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt2_2, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testYangSetNodeProperty(ListNode2, 400, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 添加DML操作
        ret = GmcBatchAddDML(batch2, stmt2_2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 批处理提交
    ret = testBatchExecuteAndWait(batch2, data2, 11, 11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommitAsync(conn1, trans_commit_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCommitAsync(conn2, trans_commit_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data2.status);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 yang表预置数据，乐观事务，创建savepoint重复更新同一条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt1_1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    GmcStmtT *stmt2_2 = NULL;
    GmcBatchT *batch1 = NULL;
    GmcBatchT *batch2 = NULL;
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    uint32_t pkVal = 1;
    int ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn1, &stmt1_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn2, &stmt2_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 事务1 更新1为100
    ret = GmcUseNamespaceAsync(stmt1, "yang", use_namespace_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransStartAsync(conn1, &g_trxConfig, trans_start_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(conn1, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode1 = NULL;
    ret = GmcGetRootNode(stmt1, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ListNode1 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt1_1, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, stmt1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1_1, &ListNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt1_1, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1_1, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode1, 100, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch1, data1, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为200
    ret = GmcUseNamespaceAsync(stmt2, "yang", use_namespace_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = GmcTransStartAsync(conn2, &g_trxConfig, trans_start_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(conn2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt2, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode2 = NULL;
    GmcNodeT *childNode2 = NULL;
    ret = GmcGetRootNode(stmt2, &rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode2, "C1", GMC_OPERATION_NONE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ListNode2 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt2_2, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch2, stmt2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2_2, &ListNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2_2, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2_2, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode2, 200, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch2, data2, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'T1\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    ret = GmcTransCreateSavepointAsync(conn1, "sp1", TransSavePointCb, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCreateSavepointAsync(conn2, "sp2", TransSavePointCb, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 事务1 更新100为300
    ret = testBatchPrepareAndSetDiff(conn1, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1_1, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, stmt1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1_1, &ListNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt1_1, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1_1, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode1, 300, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch1, data1, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新200为400
    ret = testBatchPrepareAndSetDiff(conn2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt2, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2, &rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode2, "C1", GMC_OPERATION_NONE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2_2, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch2, stmt2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2_2, &ListNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2_2, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2_2, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode2, 400, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch2, data2, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'T1\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore + 2);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);

    // 先提交事务1再提交事务2
    ret = GmcTransCommitAsync(conn1, trans_commit_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCommitAsync(conn2, trans_commit_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data2.status);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 yang表预置数据，乐观事务，创建多个savepoint重复更新同一条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt1_1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    GmcStmtT *stmt2_2 = NULL;
    GmcBatchT *batch1 = NULL;
    GmcBatchT *batch2 = NULL;
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    uint32_t pkVal = 1;
    int ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn1, &stmt1_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn2, &stmt2_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 事务1 更新1为100
    ret = GmcUseNamespaceAsync(stmt1, "yang", use_namespace_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransStartAsync(conn1, &g_trxConfig, trans_start_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(conn1, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode1 = NULL;
    ret = GmcGetRootNode(stmt1, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ListNode1 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt1_1, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, stmt1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1_1, &ListNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt1_1, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1_1, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode1, 100, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch1, data1, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为200
    ret = GmcUseNamespaceAsync(stmt2, "yang", use_namespace_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = GmcTransStartAsync(conn2, &g_trxConfig, trans_start_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(conn2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt2, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode2 = NULL;
    GmcNodeT *childNode2 = NULL;
    ret = GmcGetRootNode(stmt2, &rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode2, "C1", GMC_OPERATION_NONE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ListNode2 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt2_2, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch2, stmt2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2_2, &ListNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2_2, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2_2, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode2, 200, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch2, data2, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'T1\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    ret = GmcTransCreateSavepointAsync(conn1, "sp1", TransSavePointCb, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCreateSavepointAsync(conn2, "sp2", TransSavePointCb, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 事务1 更新100为300
    ret = testBatchPrepareAndSetDiff(conn1, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1_1, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, stmt1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1_1, &ListNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt1_1, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1_1, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode1, 300, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch1, data1, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新200为400
    ret = testBatchPrepareAndSetDiff(conn2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt2, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2, &rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode2, "C1", GMC_OPERATION_NONE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2_2, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch2, stmt2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2_2, &ListNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2_2, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2_2, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode2, 400, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch2, data2, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCreateSavepointAsync(conn1, "sp3", TransSavePointCb, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCreateSavepointAsync(conn2, "sp4", TransSavePointCb, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 事务1 更新100为300
    ret = testBatchPrepareAndSetDiff(conn1, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1_1, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, stmt1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1_1, &ListNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt1_1, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1_1, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode1, 500, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch1, data1, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新200为400
    ret = testBatchPrepareAndSetDiff(conn2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt2, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2, &rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode2, "C1", GMC_OPERATION_NONE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2_2, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch2, stmt2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2_2, &ListNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2_2, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2_2, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode2, 600, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch2, data2, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'T1\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore + 4);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);

    // 先提交事务1再提交事务2
    ret = GmcTransCommitAsync(conn1, trans_commit_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCommitAsync(conn2, trans_commit_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data2.status);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009 大对象表预置数据，多个乐观事务，重复更新同一条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg3[128] = {}, errorMsg4[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg3, errorMsg4);
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    int ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertBigObjVertex(g_stmt_sync, i, i, 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespace(stmt1, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn1, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt1, 1, 100, 10, "b");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为300
    ret = GmcUseNamespace(stmt2, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn2, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt2, 1, 300, 10, "c");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt1, 1, 200, 10, "d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt2, 1, 400, 10, "e");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommit(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 大对象表预置数据，多个乐观事务，重复更新多条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    int ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < COUNTS; i++) {
        ret = InsertBigObjVertex(g_stmt_sync, i, i, 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespace(stmt1, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn1, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < COUNTS; i++) {
        ret = InsertBigObjVertex(stmt1, i, 100, 10, "b");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务2 更新1为300
    ret = GmcUseNamespace(stmt2, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn2, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < COUNTS; i++) {
        ret = InsertBigObjVertex(stmt2, i, 300, 10, "c");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < COUNTS; i++) {
        ret = InsertBigObjVertex(stmt1, i, 200, 10, "d");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < COUNTS; i++) {
        ret = InsertBigObjVertex(stmt2, i, 400, 10, "e");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommit(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 011 大对象表预置数据，乐观事务，创建savepoint重复更新同一条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    int ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertBigObjVertex(g_stmt_sync, i, i, 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespaceAsync(stmt1, "yang", use_namespace_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransStartAsync(conn1, &g_trxConfig, trans_start_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt1, 1, 100, 10, "b");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为200
    ret = GmcUseNamespaceAsync(stmt2, "yang", use_namespace_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = GmcTransStartAsync(conn2, &g_trxConfig, trans_start_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt2, 1, 200, 10, "c");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);
    ret = GmcTransCreateSavepointAsync(conn1, "sp1", TransSavePointCb, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCreateSavepointAsync(conn2, "sp2", TransSavePointCb, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt1, 1, 300, 10, "d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt2, 1, 400, 10, "e");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);

    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore + 2);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);

    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommitAsync(conn1, trans_commit_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCommitAsync(conn2, trans_commit_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data2.status);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 大对象表预置数据，乐观事务，创建多个savepoint重复更新同一条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    int ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertBigObjVertex(g_stmt_sync, i, i, 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespaceAsync(stmt1, "yang", use_namespace_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransStartAsync(conn1, &g_trxConfig, trans_start_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt1, 1, 100, 10, "b");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为200
    ret = GmcUseNamespaceAsync(stmt2, "yang", use_namespace_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = GmcTransStartAsync(conn2, &g_trxConfig, trans_start_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt2, 1, 200, 10, "c");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);
    ret = GmcTransCreateSavepointAsync(conn1, "sp1", TransSavePointCb, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCreateSavepointAsync(conn2, "sp2", TransSavePointCb, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 事务1 更新100为300
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt1, 1, 300, 10, "d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新200为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt2, 1, 400, 10, "e");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCreateSavepointAsync(conn1, "sp3", TransSavePointCb, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCreateSavepointAsync(conn2, "sp4", TransSavePointCb, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt1, 1, 500, 10, "f");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt2, 1, 600, 10, "g");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);

    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore + 4);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);

    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommitAsync(conn1, trans_commit_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCommitAsync(conn2, trans_commit_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data2.status);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 大对象表预置数据，乐观事务，重复更新同一条数据buffersize不同，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    int ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertBigObjVertex(g_stmt_sync, i, i, 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespace(stmt1, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn1, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt1, 1, 100, 8, "b");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为300
    ret = GmcUseNamespace(stmt2, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn2, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt2, 1, 300, 13, "c");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt1, 1, 200, 9, "d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt2, 1, 400, 12, "e");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommit(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014 大对象表预置数据，乐观事务，重复更新同一条数据buffersize不同，创建savepoint，重复更新同一条数据，查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    int ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertBigObjVertex(g_stmt_sync, i, i, 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespaceAsync(stmt1, "yang", use_namespace_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransStartAsync(conn1, &g_trxConfig, trans_start_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt1, 1, 100, 12, "b");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为200
    ret = GmcUseNamespaceAsync(stmt2, "yang", use_namespace_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = GmcTransStartAsync(conn2, &g_trxConfig, trans_start_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt2, 1, 200, 8, "c");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);
    ret = GmcTransCreateSavepointAsync(conn1, "sp1", TransSavePointCb, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCreateSavepointAsync(conn2, "sp2", TransSavePointCb, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt1, 1, 300, 13, "d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertexAsync(stmt2, 1, 400, 9, "e");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);

    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore + 2);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);

    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommitAsync(conn1, trans_commit_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransCommitAsync(conn2, trans_commit_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data2.status);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 大对象表，开启乐观事务，插入超过一个页的数据，更新所有数据，bufsize变大，重查询undo日志视图/索引entry数量视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    int ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    char *srcLabelJson = NULL;
    char *destLabelJson = NULL;
    char *edgeLabelJson = NULL;
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt_sync, "srcVertex");
    GmcDropVertexLabel(g_stmt_sync, "destVertex");
    readJanssonFile("./schema_file/srcVertex.gmjson", &srcLabelJson);
    EXPECT_NE(srcLabelJson, (void *)NULL);
    readJanssonFile("./schema_file/destVertex.gmjson", &destLabelJson);
    EXPECT_NE(destLabelJson, (void *)NULL);
    readJanssonFile("./schema_file/edgeLabel.gmjson", &edgeLabelJson);
    EXPECT_NE(edgeLabelJson, (void *)NULL);
    ret = GmcCreateVertexLabel(g_stmt_sync, srcLabelJson, g_labelconfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt_sync, destLabelJson, g_labelconfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateEdgeLabel(g_stmt_sync, edgeLabelJson, g_labelconfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(destLabelJson);
    free(srcLabelJson);
    free(edgeLabelJson);
    // 预置数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "srcVertex", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 50; i++) {
        ret = InsertBigObjVertex(g_stmt_sync, i, i, 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'srcVertex\'";
    GetViewFieldResultFilter("V\\$STORAGE_HEAP_STAT", " PAGE_COUNT", filter, cmdOutput, 64);  // 新增META_PAGE_COUNT字段，通过空格区分
    uint32_t pageCount = atoi(cmdOutput);
    if (pageCount < 2) {
        ret = -1;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务1 更新1为100
    ret = GmcUseNamespace(stmt1, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn1, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1, "srcVertex", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 50; i++) {
        ret = InsertBigObjVertex(stmt1, i, 100, 12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务2 更新1为300
    ret = GmcUseNamespace(stmt2, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn2, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2, "srcVertex", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 50; i++) {
        ret = InsertBigObjVertex(stmt2, i, 200, 12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'srcVertex\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'srcVertex\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "srcVertex", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 50; i++) {
        ret = InsertBigObjVertex(stmt1, i, 300, 13);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "srcVertex", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 50; i++) {
        ret = InsertBigObjVertex(stmt2, i, 400, 13);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'srcVertex\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'srcVertex\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 先提交事务1再提交事务2
    ret = GmcTransCommit(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCommit(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = GmcTransRollBack(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 简单表预置数据，多个乐观事务，重复更新同一条数据，查询undo日志视图/索引entry数量视图，回滚事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    int ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertVertex(g_stmt_sync, i, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespace(stmt1, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn1, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertex(stmt1, 1, 100);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为300
    ret = GmcUseNamespace(stmt2, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn2, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertex(stmt2, 1, 300);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertex(stmt1, 1, 200);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertVertex(stmt2, 1, 400);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 回滚事务
    ret = GmcTransRollBack(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransRollBack(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfterRollback = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfterRollback, undoRecCntBefore - 2);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfterRollback, undoRecCntAfter - 2);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 yang表预置数据，多个乐观事务，重复更新同一条数据，查询undo日志视图/索引entry数量视图，回滚事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcStmtT *stmt1_1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    GmcStmtT *stmt2_2 = NULL;
    GmcBatchT *batch1 = NULL;
    GmcBatchT *batch2 = NULL;
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    uint32_t pkVal = 1;
    int ret = testGmcConnect(&conn1, &stmt1, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn1, &stmt1_1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcAllocStmt(conn2, &stmt2_2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 事务1 更新1为100
    ret = GmcUseNamespaceAsync(stmt1, "yang", use_namespace_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransStartAsync(conn1, &g_trxConfig, trans_start_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(conn1, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode1 = NULL;
    GmcNodeT *childNode1 = NULL;
    ret = GmcGetRootNode(stmt1, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ListNode1 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt1_1, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, stmt1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1_1, &ListNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt1_1, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1_1, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode1, 200, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch1, data1, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为300
    ret = GmcUseNamespaceAsync(stmt2, "yang", use_namespace_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = GmcTransStartAsync(conn2, &g_trxConfig, trans_start_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 设置批处理batch参数
    ret = testBatchPrepareAndSetDiff(conn2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt2, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *rootNode2 = NULL;
    GmcNodeT *childNode2 = NULL;
    ret = GmcGetRootNode(stmt2, &rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode2, "C1", GMC_OPERATION_NONE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcNodeT *ListNode2 = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt2_2, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch2, stmt2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2_2, &ListNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2_2, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2_2, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode2, 300, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch2, data2, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'T1\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    // 事务1 更新100为200
    ret = testBatchPrepareAndSetDiff(conn1, &batch1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt1, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1, &rootNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode1, "C1", GMC_OPERATION_NONE, &childNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch1, stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1_1, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch1, stmt1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt1_1, &ListNode1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt1_1, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt1_1, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode1, 500, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch1, stmt1_1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch1, data1, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testBatchPrepareAndSetDiff(conn2, &batch2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(stmt2, "root", GMC_OPERATION_NONE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2, &rootNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 设置node节点P1
    ret = GmcYangEditChildNode(rootNode2, "C1", GMC_OPERATION_NONE, &childNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch2, stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2_2, "T1", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch2, stmt2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(stmt2_2, &ListNode2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2_2, 1, GMC_DATATYPE_UINT32, &pkVal, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2_2, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testYangSetNodeProperty(ListNode2, 400, GMC_YANG_PROPERTY_OPERATION_MERGE, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 添加DML操作
    ret = GmcBatchAddDML(batch2, stmt2_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批处理提交
    ret = testBatchExecuteAndWait(batch2, data2, 2, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'T1\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 回滚事务
    ret = GmcTransRollBackAsync(conn1, trans_commit_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = GmcTransRollBackAsync(conn2, trans_commit_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfterRollback = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfterRollback, undoRecCntBefore - 2);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfterRollback, undoRecCntAfter - 2);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 大对象表预置数据，多个乐观事务，重复更新同一条数据，查询undo日志视图/索引entry数量视图，回滚事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 创建连接
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    GmcConnT *conn2 = NULL;
    GmcStmtT *stmt2 = NULL;
    int ret = testGmcConnect(&conn1, &stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&conn2, &stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    // 预置数据
    ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertBigObjVertex(g_stmt_sync, i, i, 10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    // 事务1 更新1为100
    ret = GmcUseNamespace(stmt1, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn1, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt1, 1, 100, 10, "b");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新1为300
    ret = GmcUseNamespace(stmt2, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransStart(conn2, &g_trxConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt2, 1, 300, 10, "c");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    const char *filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntBefore = atoi(cmdOutput);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumBefore = atoi(cmdOutput);

    // 事务1 更新100为200
    ret = testGmcPrepareStmtByLabelName(stmt1, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt1, 1, 200, 10, "d");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 事务2 更新300为400
    ret = testGmcPrepareStmtByLabelName(stmt2, "bigobject", GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = InsertBigObjVertex(stmt2, 1, 400, 10, "e");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfter, undoRecCntBefore);

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_HASH_INDEX_STAT", "ENTRY_USED", filter, cmdOutput, 64);
    uint32_t indexEntryUsedNumAfter = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(indexEntryUsedNumAfter, indexEntryUsedNumBefore);
    // 回滚事务
    ret = GmcTransRollBack(conn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransRollBack(conn2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    filter = "LABEL_NAME=\'bigobject\'";
    GetViewFieldResultFilter("V\\$STORAGE_UNDO_STAT", "UNDORECORD_NUM", NULL, cmdOutput, 64);
    uint32_t undoRecCntAfterRollback = atoi(cmdOutput);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfterRollback, undoRecCntBefore - 2);
    AW_MACRO_EXPECT_EQ_INT(undoRecCntAfterRollback, undoRecCntAfter - 2);
    // 释放连接
    ret = testGmcDisconnect(conn1, stmt1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn2, stmt2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 vertex表预置数据，多线程开启多个乐观事务，重复操作同一条数据，查询视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    // 预置数据
    int ret = GmcUseNamespace(g_stmt_sync, "yang");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, "bigobject", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        ret = InsertVertex(g_stmt_sync, i, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    pthread_t tid[4];
    ret = pthread_create(&tid[0], NULL, TrxThread1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, TrxThread2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[2], NULL, TrxThread3, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[3], NULL, TrxThread4, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 4; i++) {
       pthread_join(tid[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020 yang表预置数据，多线程开启多个乐观事务，重复操作同一条数据，查询视图，提交事务
TEST_F(UndoLogOptimize, HardWare_Offloading_001_DML_093_UndoLogOptimize_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    pthread_t tid[4];
    int ret = pthread_create(&tid[0], NULL, TrxYangThread1, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[1], NULL, TrxYangThread2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[2], NULL, TrxYangThread3, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&tid[3], NULL, TrxYangThread4, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 4; i++) {
       pthread_join(tid[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

class UndoLogOptimize_01 : public testing::Test {
public:
    static void SetUpTestCase()
    {
    #define MAX_CMD_SIZE 1024
        char g_command[MAX_CMD_SIZE];
    #ifdef ENV_EULER
        (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/modifyCfg.sh \"clientServerFlowControl=0;0;0;0\"");
        system(g_command);
    #endif
        // 配置相关环境变量及重启server
        InitCfg();
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        //恢复配置文件
        RecoverCfg();
    };

    virtual void SetUp()
    {
        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
    }
};
// 021 大对象表只生产不消费写入至队列满后开始消费（问题单用例转化）
TEST_F(UndoLogOptimize_01, HardWare_Offloading_001_DML_093_UndoLogOptimize_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
#ifdef DIRECT_WRITE
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
#endif
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
    char g_subConnName[32] = "subConnName";
    GmcStmtT *g_stmt = NULL;
    GmcConnT *g_conn = NULL;
    GmcStmtT *g_stmt_sub = NULL;
    GmcConnT *g_conn_sub = NULL;
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SnUserDataT *user_data = NULL;
    ret = testSnMallocUserData(&user_data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "bigobject";
    char *labelJson = NULL;
    GmcDropVertexLabel(g_stmt, labelName);
    readJanssonFile("./schema_file/BigObj.gmjson", &labelJson);
    EXPECT_NE(labelJson, (void *)NULL);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, "{\"max_record_num\":1000}");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(labelJson);
    char *g_sub_info = NULL;
    const char *g_subName = "subVertexLabel";
    readJanssonFile("schema_file/subinfo.gmjson", &g_sub_info);
    EXPECT_NE((void *)NULL, g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback_not_cmp, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_sub_info);
    // 不消费订阅消息
    gIsSnCallbackWait = true;
    char str[1024] = {0};
    const char *p = "a";
    for (int i = 0; i < 1023; i++) {
        strcat(str, p);
    }
    int i = 0;
    while (!ret) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &i, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_STRING, str, strlen(str));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_STRING, str, strlen(str));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_STRING, str, strlen(str));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_STRING, str, strlen(str));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F6", GMC_DATATYPE_STRING, str, strlen(str));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F7", GMC_DATATYPE_STRING, str, strlen(str));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F8", GMC_DATATYPE_STRING, str, strlen(str));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F9", GMC_DATATYPE_STRING, str, strlen(str));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        i++;
    }
#ifdef DIRECT_WRITE
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret); 
#endif
    AW_FUN_Log(LOG_INFO, "insert success :%d\n", i);
    // 校验收到的消息
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$DRT_DATA_PLANE_CHANNEL_STAT");
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    // 重新消费订阅消息up
    gIsSnCallbackWait = false;
    // 校验收到的消息
#ifdef DIRECT_WRITE
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 16400);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
#else
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, 16400);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
     // 取消订阅
    ret = GmcUnSubscribe(g_stmt, g_subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "bigobject");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(user_data);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class UndoLogOptimize_02 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void UndoLogOptimize_02::SetUpTestCase()
{
    int ret;

    /*
    maxTotalDynSize=maxSysDynSize+APP_DYN_SIZE，其中APP_DYN_SIZE为应用区动态内存大小且至少要有12MB
    如果APP_DYN_SIZE越小，客户端用于发送消息的内存就越小，会导致消息发送失败，返回2000错误码

    [客户端内存不足]
    [GMERR-15004] Unsuccessful to alloc memory for message buffer  ---代表由于内存不足发送消息失败
    [GMERR-15004] Fixbuf extends unsuccessful when recv msg body, pipe 1, msgSize 2089304, recvSize 56
    [GMERR-15004] Recv msg unsuccessful, pipe 1, conn 0
    [GMERR-15005] The physical memory of the memory context exceeds the threshold. Max total physical size: 4194304,
    Now the size is: 3075568, requireSize : 2089408, curMemCtx is : Conn 0 mem context,
    thresholdParent is : AppDynContext

    [服务端内存不足]本特性测试场景
    [SE-Transaction] TrxSetEscapeMemCtx, trxId: 568, tid: 281473575095680
    [SE-Transaction] TrxRestoreSessionMemCtx, trxId: 568, tid: 281473575095680
    [GMERR-15005] The physical memory of the memory context exceeds the threshold. Max total physical size: 498067456,
    Now the size is: 497867896, requireSize : 982704, curMemCtx is : catalog memory context,
    thresholdParent is : SysDynContext
    */
    system(
        "sh $TEST_HOME/tools/modifyCfg.sh "
        "\"workerHungThreshold=6,200,300\" "
        "\"maxSysDynSize=483\" "
        "\"pageSize=8\" "
        "\"shmCoreDumpMode=1\" "
        "\"planCacheSize=8\" "
        "\"maxSortBufferSize=1\" "
        "\"maxTotalDynSize=500\" "
        );

    system("sh $TEST_HOME/tools/start.sh");
    system("sh create_multi_label.sh 1000");
    // 配置相关环境变量及重启server
    InitCfg();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void UndoLogOptimize_02::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    //恢复配置文件
    RecoverCfg();
    system("rm -rf multi_vertexlabel/");
}

void UndoLogOptimize_02::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_dyn, &g_stmt_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {}, errorMsg2[128] = {}, errorMsg3[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_OUT_OF_MEMORY);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(3, errorMsg1, errorMsg2, errorMsg3);
}

void UndoLogOptimize_02::TearDown()
{
    int ret;
    AW_CHECK_LOG_END();

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    ret = testGmcDisconnect(g_conn_dyn, g_stmt_dyn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn_dyn = NULL;
    g_stmt_dyn = NULL;
}
// 022 vertex表创建订阅关系，写入数据，触发推送，不断创建新的vertex表直到内存满，删除第一个vertex表的订阅关系，删除所有vertex表
TEST_F(UndoLogOptimize_02, HardWare_Offloading_001_DML_093_UndoLogOptimize_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int before = 0;
    int after = 0;
    uint32_t times = RECORD_NUM_003;
    uint32_t initValue = 0;
    char g_sub_info[1024] = "";
    char g_subName[20] = "subVertex";
    char g_subConnName[32] = "subConnName";

    int num = 0;
    GmcStmtT *g_stmt_sub = NULL;
    GmcConnT *g_conn_sub = NULL;
    SnUserDataT *user_data = NULL;
    ret = testSnMallocUserData(&user_data, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 创建表
    testCreateLabelTrx(g_stmt);
    snprintf(g_sub_info, 1024,
        "{  \"name\":\"subVertex\","
        "\"label_name\":\"%s\","
        "\"comment\":\"VertexLabel subscription\","
        "\"type\":\"before_commit\","
        "\"events\":"
        "["
        "{\"type\":\"insert\", \"msgTypes\":[\"new object\", \"old object\"]},"
        "{\"type\":\"replace\", \"msgTypes\":[\"new object\", \"old object\"]},"
        "{\"type\":\"update\", \"msgTypes\":[\"new object\", \"old object\"]},"
        "{\"type\":\"delete\", \"msgTypes\":[\"new object\", \"old object\"]}"
        "],"
        "\"is_path\":false,"
        "\"retry\":true,"
        "\"is_reliable\":true }", "Vertex_01");
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = g_subName;
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_g_sub_info, g_conn_sub, sn_callback_not_cmp, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // DML操作
    testInsertVertexLabel(g_stmt, times, initValue, "Vertex_01");
    ret = testWaitSnRecv(user_data, GMC_SUB_EVENT_INSERT, times);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 占满动态内存
    uint32_t tableNum = 1000;
    TestCreateMultiLabel(g_stmt_dyn, tableNum);
    // 取消订阅
    ret = GmcUnSubscribe(g_stmt, "subVertex");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除表
    TestDropLabel(g_stmt, "Vertex_01");
    TestDropMultiLabel(g_stmt_dyn, tableNum);
    testSnFreeUserData(user_data);
    AW_FUN_Log(LOG_STEP, "test end.");
}
class UndoLogOptimize_03 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void UndoLogOptimize_03::SetUpTestCase()
{
    int ret;
    // 配置相关环境变量及重启server
    InitCfg();
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = createEpollOneThread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void UndoLogOptimize_03::TearDownTestCase()
{
    closeEpollOneThread();
    GmcDetachAllShmSeg();
    testEnvClean();
    //恢复配置文件
    RecoverCfg();
}

void UndoLogOptimize_03::SetUp()
{
    int ret;
    AsyncUserDataT data = {0};
    char *vertexSchema = NULL;
    // 创建连接
    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
    for (int i = 0; i < 4; i++) {
        ret = TestYangGmcConnect(&g_conn_trx[i], &g_stmt_trx[i], GMC_CONN_TYPE_ASYNC, &connOptions);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestYangGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, &connOptions);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    g_trxConfig.transMode = GMC_TRANS_USED_IN_CS;
    g_trxConfig.type = GMC_TX_ISOLATION_REPEATABLE;
    g_trxConfig.readOnly = false;
    g_trxConfig.trxType = GMC_OPTIMISTIC_TRX;
    // 配置ns级别，RR+乐观事务模式
    GmcNspCfgT yangNspCfg = {};
    yangNspCfg.tablespaceName = NULL;
    yangNspCfg.namespaceName = "yang";
    yangNspCfg.userName = "abc";
    yangNspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE};
    ret = GmcCreateNamespaceWithCfgAsync(g_stmt_trx[0], &yangNspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    for (int i = 0; i < 4; i++) {
        ret = GmcUseNamespaceAsync(g_stmt_trx[i], "yang", use_namespace_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }
    readJanssonFile("schema_file/container.gmjson", &vertexSchema);
    AW_MACRO_ASSERT_NOTNULL(vertexSchema);
    ret = GmcCreateVertexLabelAsync(g_stmt_trx[0], vertexSchema, g_msConfigJson, create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(vertexSchema);
    AW_CHECK_LOG_BEGIN();
    char errorMsg1[128] = {}, errorMsg2[128] = {}, errorMsg3[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
}

void UndoLogOptimize_03::TearDown()
{
    int ret;
    AsyncUserDataT data = {0};
    AW_CHECK_LOG_END();
    ret = GmcDropVertexLabelAsync(g_stmt_trx[0], "root", create_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    // 删除namesapce
    ret = GmcDropNamespaceAsync(g_stmt_trx[0], "yang", drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    for (int i = 0; i < 4; i++) {
        ret = testGmcDisconnect(g_conn_trx[i], g_stmt_trx[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
static vector<string> expectDiffBaseNULL = {

};
// 1. 根节点---》跳转行（json中有变长字段），trx0 insert 1条，trx0-1 insert 1条，更新bufSize变大，查跳转行日志是否有出现
// 2. trx1 更新
// 3. trx2 开启diff， 删除
// 4. trx1 更新
// 5. trx2 insert 刚刚删除的记录
// 6. 查diff，（异常情况：一个是insert，一个是删除），预期应该diff是空的 
TEST_F(UndoLogOptimize_03, DISABLED_TODEL_HardWare_Offloading_001_DML_093_UndoLogOptimize_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AsyncUserDataT data = {0};
    AsyncUserDataT data1 = {0};
    AsyncUserDataT data2 = {0};
    AsyncUserDataT data3 = {0};
    GmcBatchT *batch[4] = {0};
    GmcNodeT *rootNode = NULL;
    int ret;
    
    // 启动事务0,写入一条数据
    ret = GmcTransStartAsync(g_conn_trx[0], &g_trxConfig, trans_start_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testBatchPrepareAndSetDiff(g_conn_trx[0], &batch[0], GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_trx[0], "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch[0], g_stmt_trx[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_trx[0], &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testInsertYangFieldProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch[0], g_stmt_trx[0]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch[0], batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    // 启动事务0-1，写入一条数据，占1个页
    ret = GmcTransStartAsync(g_conn_trx[1], &g_trxConfig, trans_start_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    ret = testBatchPrepareAndSetDiff(g_conn_trx[1], &batch[1], GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_trx[1], "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch[1], g_stmt_trx[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_trx[1], &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testInsertYangFieldProperty(rootNode, 100, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch[1], g_stmt_trx[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch[1], batch_execute_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    AW_MACRO_EXPECT_EQ_INT(1, data1.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data1.succNum);
    system("gmsysview -q V\\$STORAGE_HEAP_STAT -f LABEL_NAME=root");
    // 事务0-1更新数据，占2个页（跳转行）
    ret = testGmcPrepareStmtByLabelName(g_stmt_trx[1], "root", GMC_OPERATION_REPLACE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch[1], g_stmt_trx[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_trx[1], &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangFieldProperty(rootNode, 200, GMC_YANG_PROPERTY_OPERATION_REPLACE);
    ret = GmcBatchAddDML(batch[1], g_stmt_trx[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch[1], batch_execute_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    AW_MACRO_EXPECT_EQ_INT(1, data1.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data1.succNum);
    // 提交事务0-1，成功
    ret = GmcTransCommitAsync(g_conn_trx[1], trans_commit_callback, &data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data1.status);
    system("gmsysview -q V\\$STORAGE_HEAP_STAT -f LABEL_NAME=root");
    // 事务1更新数据
    ret = GmcTransStartAsync(g_conn_trx[2], &g_trxConfig, trans_start_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    ret = testBatchPrepareAndSetDiff(g_conn_trx[2], &batch[2], GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_trx[2], "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch[2], g_stmt_trx[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_trx[2], &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangFieldProperty(rootNode, 300, GMC_YANG_PROPERTY_OPERATION_MERGE);
    ret = GmcBatchAddDML(batch[2], g_stmt_trx[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch[2], batch_execute_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    AW_MACRO_EXPECT_EQ_INT(1, data2.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data2.succNum);
    // 事务2删除数据
    ret = GmcTransStartAsync(g_conn_trx[3], &g_trxConfig, trans_start_callback, &data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data3.status);
    ret = testBatchPrepareAndSetDiff(g_conn_trx[3], &batch[3], GMC_BATCH_YANG, GMC_YANG_DIFF_DELAY_READ_ON);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_trx[3], "root", GMC_OPERATION_REMOVE_GRAPH);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch[3], g_stmt_trx[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_trx[3], &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchAddDML(batch[3], g_stmt_trx[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch[3], batch_execute_callback, &data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data3.status);
    AW_MACRO_EXPECT_EQ_INT(1, data3.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data3.succNum);
    // 事务1更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_trx[2], "root", GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch[2], g_stmt_trx[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_trx[2], &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangFieldProperty(rootNode, 400, GMC_YANG_PROPERTY_OPERATION_MERGE);
    ret = GmcBatchAddDML(batch[2], g_stmt_trx[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch[2], batch_execute_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    AW_MACRO_EXPECT_EQ_INT(1, data2.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data2.succNum);
    // 事务2 插入删除的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt_trx[3], "root", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch[3], g_stmt_trx[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_trx[3], &rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testUpdateYangFieldProperty(rootNode, 200, GMC_YANG_PROPERTY_OPERATION_CREATE);
    ret = GmcBatchAddDML(batch[3], g_stmt_trx[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch[3], batch_execute_callback, &data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data3.status);
    AW_MACRO_EXPECT_EQ_INT(1, data3.totalNum);
    AW_MACRO_EXPECT_EQ_INT(1, data3.succNum);
    // 事务2查diff
    data3.stmt = g_stmt_trx[3];
    data3.expectDiff = &expectDiffBaseNULL;
    ret = GmcYangFetchDiffExecuteAsync(g_stmt_trx[3], NULL, FetchDiff_callback, &data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data3.status);
    // 提交事务1成功
    ret = GmcTransCommitAsync(g_conn_trx[2], trans_commit_callback, &data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data2.status);
    // 提交事务2失败回滚
    ret = GmcTransCommitAsync(g_conn_trx[3], trans_commit_callback, &data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data3.status);
    ret = GmcTransRollBackAsync(g_conn_trx[3], trans_commit_callback, &data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data3.status);
    // 提交事务0，失败回滚
    ret = GmcTransCommitAsync(g_conn_trx[0], trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, data.status);
    ret = GmcTransRollBackAsync(g_conn_trx[0], trans_commit_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testWaitAsyncRecvOneThread(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_FUN_Log(LOG_STEP, "test end.");
}
