/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2017-2024. All rights reserved.
 * File Name: syCommon.h
 * Author: yaosiyuan ywx758883
 * Date: 2021-5-31
 * Describle:
 */
#ifndef _SYCOMMON_H
#define _SYCOMMON_H

#include <stdarg.h>
#include <semaphore.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#ifdef __cplusplus
extern "C" {
#endif

#define RECORDCOUNTSTART 0
#define RECORDCOUNTEND 10
#define GMIMPORT (char *)"gmimport"
#define GMEXPORT (char *)"gmexport"
#define GMSYSVIEW (char *)"gmsysview"
#define FULLTABLE 0xff
#define LOG_LEVEL 1

sem_t g_semSub;
int g_time = 0;
uint32_t g_subPushCount[10];
int g_subIndex = 0, g_maxRecordCout;
int g_chanRingLen = 256;
GmcConnT *g_conn = NULL;
GmcConnT *g_conn_sub = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_sub = NULL;
GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;
void *g_vertexLabel = NULL;
AsyncUserDataT g_data = {0};
SnUserDataT *g_userData;
char *g_subConnName = (char *)"subConnName";
typedef void *(*thread_func)(void *);

typedef enum tagRunMode { MODE_EULER = 0, MODE_DAP = 1, MODE_HONGMENG = 2 } GtRunModeE;

#define COMPARE_NE(expect_value, actual_value)                                \
    do {                                                                      \
        if ((expect_value) == (actual_value)) {                               \
            printf("[Error file: %s, line: %d]\n", __FILE__, __LINE__);       \
            printf("Value of: " #actual_value " = %p\n", (actual_value));     \
            printf("Not Expected: " #expect_value " = %p\n", (expect_value)); \
            return -1;                                                        \
        };                                                                    \
    } while (0)
#define TEST_INFO(format, ...)                      \
    do {                                            \
        if (LOG_LEVEL >= 2) {                       \
            fprintf(stdout,                         \
                "["                                 \
                "Test"                              \
                "]["                                \
                "Info"                              \
                "]: File:%s Func: %s Lineno:%d. ",  \
                __FILE__, __func__, __LINE__);      \
            fprintf(stdout, format, ##__VA_ARGS__); \
            fprintf(stdout, "\n");                  \
        }                                           \
    } while (0)

// schema 拼装的字符串开头和结尾
const char *schemaJsonHead =
    R"([{
    "type":"record",
    "name":"partition_test",
    "fields":
        [
            {"name":"F0", "type":"int32"},
    )";

const char *schemaAllTypes =
    R"([{
    "version":"2.0",
    "type":"record",
    "name":"schema_datatype",
    "fields":[
        { "name":"F1", "type":"uint32", "default":1 },
        { "name":"F2", "type":"uint8", "default":1 },
        { "name":"F3", "type":"int16", "default":1 },
        { "name":"F4", "type":"uint16", "default":1 },
        { "name":"F5", "type":"int32", "default":1 },
        { "name":"F6", "type":"uint32", "default":1 },
        { "name":"F7", "type":"int64", "default":1 },
        { "name":"F8", "type":"uint64", "default":1 },
        { "name":"F9", "type":"int8", "default":1 },
        { "name":"F10", "type":"double", "nullable":false, "default":1 },
        { "name":"F11", "type":"time", "nullable":false, "default":1 },
        { "name":"F12", "type":"char", "nullable":false, "default":"1" },
        { "name":"F13", "type":"uchar", "nullable":false, "default":"1" },
        { "name":"F14", "type":"string", "size" :1024, "nullable":false, "default":"1" },
        { "name":"F15", "type":"bytes", "size" :7, "nullable":false, "default":"1" },
        { "name":"F16", "type":"fixed", "size" :7, "nullable":false, "default":"1111111" },
        { "name":"F17", "type":"uint8: 4", "nullable":false, "default":"0x01" },
        { "name":"F18", "type":"uint16: 4", "nullable":false, "default":"0x0f" },
        { "name":"F19", "type":"uint32: 8", "nullable":false, "default":"0xff" },
        { "name":"F20", "type":"uint64: 16", "nullable":false, "default":"0xffff" },
        { "name":"F21", "type":"fixed", "size":532, "nullable":true },
        { "name":"F22", "type":"float", "nullable":false, "default":1 },
        { "name":"F23", "type":"uint64", "nullable":false, "default":1 },
        { "name":"F24", "type":"uint64", "size":7, "nullable":false, "default":"1" },
        { "name":"F25", "type":"uint64", "size":7, "nullable":false, "default":"1" },
        { "name":"F26", "type":"uint64", "size":7, "nullable":false, "default":"1111111" },
        { "name":"F27", "type":"uint64: 4", "nullable":false, "default":"0x01" },
        { "name":"F28", "type":"uint64: 4", "nullable":false, "default":"0x0f" },
        { "name":"F29", "type":"uint64: 8", "nullable":false, "default":"0xff" },
        { "name":"F30", "type":"uint64: 16", "nullable":false, "default":"0xffff" },
        { "name":"F31", "type":"uint64", "nullable":false, "default":1 },
        { "name":"F32", "type":"uint64", "nullable":false, "default":"1" },
        { "name":"F33", "type":"uint64", "nullable":false, "default":"1" },
        { "name":"F34", "type":"uint64", "size":1024, "nullable":false, "default":"1" },
        { "name":"F35", "type":"uint64", "size":7, "nullable":false, "default":"1" },
        { "name":"F36", "type":"uint64", "size":7, "nullable":false, "default":"1111111" },
        { "name":"F37", "type":"uint64: 4", "nullable":false, "default":"0x01" },
        { "name":"F38", "type":"uint64: 4", "nullable":false, "default":"0x0f" },
        { "name":"F39", "type":"uint64: 8", "nullable":false, "default":"0xff" },
        { "name":"F40", "type":"uint64: 16", "nullable":false, "default":"0xffff" },
        { "name":"F41", "type":"uint64", "nullable":false, "default":1 },
        { "name":"F42", "type":"uint64", "nullable":false, "default":"1" },
        { "name":"F43", "type":"uint64", "nullable":false, "default":"1" },
        { "name":"F44", "type":"uint64", "size":1024, "nullable":false, "default":"1" },
        { "name":"F45", "type":"bytes", "size":7, "nullable":false, "default":"1" },
        { "name":"F46", "type":"fixed", "size":7, "nullable":false, "default":"1111111" },
        { "name":"F47", "type":"uint8: 4", "nullable":false, "default":"0x01" },
        { "name":"F48", "type":"uint16: 4", "nullable":false, "default":"0x0f" },
        { "name":"F49", "type":"uint32: 8", "nullable":false, "default":"0xff" },
        { "name":"F50", "type":"uint64: 16", "nullable":false, "default":"0xffff" },
        { "name":"F51", "type":"uint64", "nullable":false, "default":1 },
        { "name":"F52", "type":"uint64", "nullable":false, "default":"1" },
        { "name":"F53", "type":"uint64", "nullable":false, "default":"1" },
        { "name":"F54", "type":"uint64", "size":1024, "nullable":false, "default":"1" },
        { "name":"F55", "type":"uint64", "size":7, "nullable":false, "default":"1" },
        { "name":"F56", "type":"uint64", "size":7, "nullable":false, "default":"1111111" },
        { "name":"F57", "type":"uint64: 4", "nullable":false, "default":"0x01" },
        { "name":"F58", "type":"uint64: 4", "nullable":false, "default":"0x0f" },
        { "name":"F59", "type":"uint64: 8", "nullable":false, "default":"0xff" },
        { "name":"F60", "type":"uint64: 16", "nullable":false, "default":"0xffff" },
        { "name":"F61", "type":"uint64", "default":1 },
        { "name":"F62", "type":"uint64", "default":1 },
        { "name":"F63", "type":"uint64", "default":1 },
        { "name":"F64", "type":"uint64", "default":1 },
        { "name":"F65", "type":"uint64", "default":1 },
        { "name":"vr_id", "type":"uint32", "default":1 },
        { "name":"vrf_index", "type":"uint32", "default":1 },
        { "name":"dest_ip_addr", "type":"uint32", "default":1 },
        { "name":"mask_len", "type":"uint8", "default":1 },
        { "name":"dest_ip_addr6", "type":"fixed", "size": 16, "default":"1111111111111111" }
    ],
    "keys":[
)";


const char *schemaAllTypesTail =
    R"(
        ]
}]
)";

const char *schemaJsonTail =
    R"(
        ],
    "keys":
        [
            {
                "node":"VertexLabel",
                "name":"PK",
                "fields":["F0"],
                "index":{"type":"primary"},
                "constraints":{"unique":true}
            }
        ]
    }])";

const char *subAllType =
    R"({
    "name":"subVertexLabel",
    "label_name":"schema_datatype",
    "comment":"VertexLabel subscription",
    "type":"before_commit",
    "events":
        [
            {"type":"insert", "msgTypes":["new object", "old object"]},
            {"type":"delete", "msgTypes":["new object", "old object"]},
            {"type":"age", "msgTypes":["new object", "old object"]},
            {"type":"update", "msgTypes":["new object", "old object"]},
            {"type":"replace", "msgTypes":["new object", "old object"]}
        ],
    "retry":true
})";

// 配置stlm日志
int GtSetStlmLog(bool isDisableSuppres)
{
    if (g_envType != MODE_DAP) {
        return GMERR_OK;
    }
    int ret;
    if (isDisableSuppres) {
        // 修改日志抑制条件 (使stlm日志不容易被抑制)
        ret = system("/usr/local/bin/stlmbox --logsuppressparamset --suppress_level 1 --suppress_interval 1 "
                     "--suppress_threshhold 4098 --suppress_timecount 60");
        return ret;
    } else {
        // 恢复日志抑制条件
        ret = system("/usr/local/bin/stlmbox --logsuppressparamset --suppress_level 0 --suppress_interval 1 "
                     "--suppress_threshhold 500 --suppress_timecount 3600");
        return ret;
    }
    return GMERR_OK;
}

int compare_file_content(char *expect_file_path, char *actual_file_path, int length = 3)
{
    int ret = 0;
    char *expect_value = NULL;
    ret = readJanssonFile(expect_file_path, &expect_value);
    COMPARE_NE((char *)NULL, expect_value);

    char *actual_value = NULL;
    ret = readJanssonFile(actual_file_path, &actual_value);
    COMPARE_NE((char *)NULL, (char *)actual_value);

    ret = strncmp(expect_value, actual_value, length);
    if (ret != 0) {
        printf("[Error file: %s, line: %d]\n", __FILE__, __LINE__);
        printf("Value of: actual_value : \n%s\n", actual_value);
        printf("Expected: expect_value : \n%s\n", expect_value);
    };

    free(expect_value);
    free(actual_value);
    return ret;
}

// 功能同strcat, 增加支持格式化入参
int GtStrcat(char *dest, size_t dest_max, const char *src, ...)
{
    int ret;
    errno = 0;
    char *tmpSrc = (char *)malloc(dest_max);
    if (tmpSrc == NULL) {
        return -1;
    }

    va_list args;
    va_start(args, src);
    ret = vsnprintf(tmpSrc, dest_max, src, args);
    if (ret <= 0) {
        TEST_INFO("call vsnprintf failed, ret = %d, errno = %d, %s\n", ret, errno, strerror(errno));
        va_end(args);
        free(tmpSrc);
        return ret;
    }
    va_end(args);

    strncat(dest, tmpSrc, dest_max);
    if (errno != GMERR_OK) {
        TEST_INFO("call strncat failed, errno = %d, %s", errno, strerror(errno));
        free(tmpSrc);
        return errno;
    }
    free(tmpSrc);
    return GMERR_OK;
}

// schema拼接后建表
int spliceSchemaCreateTable(GmcStmtT *stmt, char *schemaString, int expected = GMERR_OK)
{
    int ret = 0;
    int32_t jsonLen = 1024 * 200;
    char *schemaJson = (char *)malloc(jsonLen * sizeof(char));
    if (schemaJson == NULL) {
        return -1;
    }
    EXPECT_NE((char *)NULL, schemaJson);
    memset(schemaJson, '\0', jsonLen);
    ret = GtStrcat(schemaJson, jsonLen, schemaAllTypes);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaString, 12, " ", 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GtStrcat(schemaJson, jsonLen, schemaAllTypesTail);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TEST_INFO("\n%s", schemaJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schemaJson, NULL);
    AW_MACRO_EXPECT_EQ_INT(expected, ret);
    free(schemaJson);
    return ret;
}

// 定制化校验日志
int checkLog(char *ecpectLog, int expectNum, int length = 3)
{
    int ret = 0;
    char string[100];
    FILE *fp;
    char *actual = (char *)"temp.log";
    char *expect = (char *)"expect.log";

    system("touch expect.log");
    fp = fopen("expect.log", "r+");
    if (fp == NULL) { // 判断如果文件指针为空
        printf("File cannot open!\n");
        return -1;
    }
    fprintf(fp, "%d", expectNum);
    fclose(fp);
    memset(string, 0, 100);
    if (g_envType == MODE_EULER) {
        sprintf(string, "cat log/run/rgmserver/* |grep \"%s\" |wc -l > temp.log", ecpectLog);
        ret = system(string);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = compare_file_content(expect, actual, length);
        system("cat /dev/null > log/run/rgmserver/rgmserver.1.log");
    } else if (g_envType == MODE_DAP) {
        sprintf(string, "cat /opt/vrpv8/var/log/stlm/info |grep \"%s\" |wc -l > temp.log", ecpectLog);
        ret = system(string);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    system("rm -rf expect.log");
    return ret;
}

void clearlog()
{
    if (g_envType == MODE_EULER) {
        system("cat /dev/null > log/run/rgmserver/rgmserver.1.log");
    } else {
        system("rm -rf /opt/vrpv8/var/log/stlm/info/stlmlog.csv");
    }
}

// 创建订阅关系
int createSubscribe(char *file, GmcStmtT *stmt, char *subName, GmcConnT *conn,
    void (*snCallBack)(GmcStmtT *, const GmcSubMsgInfoT *, void *))
{
    int ret = 0;
    char *schema = NULL;
    readJanssonFile(file, &schema);
    EXPECT_NE((void *)NULL, schema);
    GmcSubConfigT tmp_schema;
    tmp_schema.subsName = subName;
    tmp_schema.configJson = schema;
    ret = GmcSubscribe(stmt, &tmp_schema, conn, snCallBack, g_userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    return ret;
}

// 建表
int createVertexLabel(char *file, GmcStmtT *stmt, char *config, int expected = GMERR_OK)
{
    int ret = 0;
    char *schema = NULL;
    readJanssonFile(file, &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(stmt, schema, config);
    AW_MACRO_EXPECT_EQ_INT(expected, ret);
    free(schema);
    return ret;
}

int createEdgeLabel(char *file, GmcStmtT *stmt, char *config)
{
    int ret = 0;
    char *schema = NULL;
    readJanssonFile(file, &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateEdgeLabel(stmt, schema, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);
    return ret;
}

// 工具导入
int toolModelOperation(char *toolMode, char *cmdType, char *fileName, char *labelName)
{
    int ret = 0;
    char cmd[512];
    if (strcmp(toolMode, (char *)"gmimport") == 0) {
        snprintf(cmd, 512, "%s/gmimport -c %s -f %s -s %s -ns %s", g_toolPath, cmdType,
            fileName, g_connServer, g_testNameSpace);
    } else if (strcmp(toolMode, (char *)"gmexport") == 0) {
        snprintf(cmd, 512, "%s/gmexport -c %s -t %s -s %s -ns %s", g_toolPath, cmdType,
            labelName, g_connServer, g_testNameSpace);
    } else if (strcmp(toolMode, (char *)"gmsysview") == 0) {
        snprintf(cmd, 512, "%s/gmsysview %s -s %s -ns %s", g_toolPath, cmdType,
            g_connServer, g_testNameSpace);
    }
    ret = system(cmd);
    return ret;
}

// 更新配置项
void modifyConfig(char *config)
{
    char cmd[512];
    system("sh $TEST_HOME/tools/stop.sh");
    (void)GmcDetachAllShmSeg();
    system("ipcs");
    snprintf(cmd, 512, "sh modifyCfg.sh \"%s\"", config);
    system(cmd);
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");
}

int checkPartition(GmcStmtT *stmt, char *labelName, uint8_t partitionStart, uint8_t partitionEnd)
{
    int ret = 0;
    bool isAbnormal = false;
    for (uint8_t i = partitionStart; i < partitionEnd; i++) {
        ret = GmcBeginCheck(stmt, labelName, i);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcEndCheck(stmt, labelName, i, isAbnormal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    return ret;
}

// 事务
int syTransStart(GmcConnT *conn, char model)
{
    int ret = 0;
    GmcTxConfigT transcction_config;
    transcction_config.readOnly = false;
    transcction_config.transMode = model;
    transcction_config.type = GMC_TX_ISOLATION_COMMITTED;
    ret = GmcTransStart(conn, &transcction_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int gmsysview_popen(char *result[], const char *format, ...)
{
    int ret = 0;
    char buf[1024] = {0};
    FILE *p_file = NULL;
    char command[1024] = {0};

    va_list args;
    va_start(args, format);
    ret = vsnprintf(command, sizeof(command), format, args);
    va_end(args);
    p_file = popen(command, (char *)"r");
    if (NULL == p_file) {
        ret = 1;
        printf("popen %s error/n", p_file);
        goto END;
    }

    while (fgets(buf, sizeof(buf), p_file) != NULL) {
        strcpy((char *)result, buf);
    }

END:
    if (pclose(p_file) == 1) {
        printf("pclose failed.");
        return 1;
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
#endif
