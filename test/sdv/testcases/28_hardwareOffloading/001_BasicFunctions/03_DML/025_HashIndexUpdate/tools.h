#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <pthread.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"


using namespace std;

GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char *schema_json = NULL;
int32_t ret;
int res = 0;
int conn_id = 0;
bool isNull;
unsigned int valueSize;
int total_num = 0;

unsigned int totalNum = 0;
unsigned int successNum = 0;
unsigned int len;
int affectRows;
int g_subIndex = 0;
// test datatype label: write vertexLabel
char wr_int8 = 1;
unsigned char wr_uint8 = '1';
short wr_int16 = -1111;
unsigned short wr_uint16 = 1111;
int int32_tmp = -111111;
unsigned int uint32_tmp = 111111;
unsigned long long wr_uint64 = 11111111;
long long int64_tmp = -11111111;
int wr_int32 = -111111;
long long wr_long = -11111111;
float float_tmp = 1111.111;
double double_tmp = 1111.111;
bool bool_tmp = true;
char bytes_tmp[6] = "14";
char string_tmp[64] = "10.157.123.25";
char fixed_tmp[10] = "16";
char wr_fixed[36] = "write";
long long time_tmp = 369852147;
char char_tmp = -1;
unsigned char uchar_tmp = 11;
bool isFinish;

void *vertexLabel1 = NULL, *vertexLabel2 = NULL;
void *vertexLabel = NULL;
void *g_vertexLabel = NULL;

typedef enum EnumOpTypeNum {
    OpTypeNum_1 = 1,  //只有1种dml类型，申请内存时只需申请1个g_data_num的大小
    OpTypeNum_2,      //有2种dml类型
    OpTypeNum_3,
} OpTypeNumE;

#define THR_NUM 2
#define MAX_NAME_LENGTH 128

#define LABELNAME_MAX_LENGTH 128
#define SCHEMA_JSON_SIZE 1024

#define NONE "\033[0m"
#define BLACK "\033[0;30m"
#define L_BLACK "\033[1;30m"
#define RED_S "\033[0;31m"
#define L_RED "\033[1;31m"
#define L_GREEN "\033[1;32m"
#define BROWN "\033[0;33m"
#define YELLOW_S "\033[1;33m"
#define L_BLUE "\033[1;34m"
#define PURPLE "\033[0;35m"
#define L_PURPLE "\033[1;35m"
#define CYAN "\033[0;36m"
#define L_CYAN "\033[1;36m"
#define GRAY "\033[0;37m"
#define WHITE "\033[1;37m"

#define TEST_INFO_1(testItem, fieldName, fieldvalue, thread_id, isprint)                              \
    do {                                                                                              \
        if (isprint == 1) {                                                                           \
            printf("[INFO][Thread_%d][%7s][%15s] %d \n", thread_id, testItem, fieldName, fieldvalue); \
        }                                                                                             \
        if () {                                                                                       \
        }                                                                                             \
    } while (0)

#define TEST_INFO(testItem, fieldName, fieldvalue, thread_id, isprint, isUniquleHash)                            \
    do {                                                                                                         \
        if (isprint == 1) {                                                                                      \
            if (isUniquleHash == 1)                                                                              \
                printf("[INFO][Thread_%d][%7s][" L_PURPLE "%17s" NONE "] %d \n", thread_id, testItem, fieldName, \
                    fieldvalue);                                                                                 \
            else if (isUniquleHash == 0)                                                                         \
                printf("[INFO][Thread_%d][%7s][" YELLOW_S "%17s" NONE "] %d \n", thread_id, testItem, fieldName, \
                    fieldvalue);                                                                                 \
            else                                                                                                 \
                printf("[INFO][Thread_%d][%7s][%17s] %d \n", thread_id, testItem, fieldName, fieldvalue);        \
        }                                                                                                        \
    } while (0)

#define TEST_INFO_STR(testItem, fieldName, fieldvalue, thread_id, isprint, isUniquleHash)                        \
    do {                                                                                                         \
        if (isprint == 1) {                                                                                      \
            if (isUniquleHash == 1)                                                                              \
                printf("[INFO][Thread_%d][%7s][" L_PURPLE "%17s" NONE "] %s \n", thread_id, testItem, fieldName, \
                    fieldvalue);                                                                                 \
            else if (isUniquleHash == 0)                                                                         \
                printf("[INFO][Thread_%d][%7s][" YELLOW_S "%17s" NONE "] %s \n", thread_id, testItem, fieldName, \
                    fieldvalue);                                                                                 \
            else                                                                                                 \
                printf("[INFO][Thread_%d][%7s][%17s] %s \n", thread_id, testItem, fieldName, fieldvalue);        \
        }                                                                                                        \
    } while (0)

#define TEST_SCAN_DEL_RES(expect_status, ret, loop)                                              \
    do {                                                                                         \
        if ((ret) != (expect_status)) {                                                          \
            fprintf(stderr,                                                                      \
                "[" CYAN "Test" NONE "][" RED_S "Error" NONE                                     \
                "][oper_num: %d][File: %s:%d Func: %s] expect %lu, real %lu\n",                  \
                loop, __FILE__, __LINE__, __func__, (uint64_t)(expect_status), (uint64_t)(ret)); \
            break;                                                                               \
        }                                                                                        \
    } while (0)
#define TEST_AW_MACRO_ASSERT_EQ_INT(expect_status, ret)                                                                     \
    do {                                                                                                       \
        if ((ret) != (expect_status)) {                                                                        \
            fprintf(stderr,                                                                                    \
                "[" CYAN "Test" NONE "][" RED_S "Error" NONE "][File: %s:%d Func: %s] expect %lu, real %lu\n", \
                __FILE__, __LINE__, __func__, (uint64_t)(expect_status), (uint64_t)(ret));                     \
            continue;                                                                                          \
        }                                                                                                      \
    } while (0)

int32_t queryFieldValueAndCompare(
    GmcStmtT *stmt, const char *fieldName, void *readValueOut, unsigned int *getValueSizeOut)
{
    int ret = 0;

    ret = GmcGetVertexPropertySizeByName(stmt, fieldName, getValueSizeOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, fieldName, readValueOut, *getValueSizeOut, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return ret;
}

/* 写数据 label_name:  schema_datatype */
void test_insert_vertex_datatype(GmcStmtT *stmt, int oper_nums)
{
    int ret = 0;
    for (int loop = 0; loop < oper_nums; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt, "schema_datatype", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &wr_int8, sizeof(wr_int8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT16, &wr_int16, sizeof(wr_int16));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // hash_2   unique = true
        ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_INT32, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // hash_2   unique = true
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_INT64, &int64_tmp, sizeof(int64_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT32, &wr_int32, sizeof(wr_int32));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_INT64, &wr_long, sizeof(wr_long));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &float_tmp, sizeof(float_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &double_tmp, sizeof(double_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_BOOL, &bool_tmp, sizeof(bool_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        char bytes_tmp[6] = "14";
        ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_BYTES, bytes_tmp, strlen(bytes_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_STRING, string_tmp, strlen(string_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, fixed_tmp, 6);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_TIME, &time_tmp, sizeof(time_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_CHAR, &char_tmp, sizeof(char_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_UCHAR, &uchar_tmp, sizeof(uchar_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
/* 主键读 label_name:  schema_datatype */
int32_t PK_read_fieldValue_datatype(
    GmcConnT *conn, const char *keyName, GmcDataTypeE datatype, int oper_nums, int isPrint = 1)
{
    int ret = 0;
    GmcStmtT *stmt1;
    ret = GmcAllocStmt(conn, &stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (unsigned int loop = 0; loop < oper_nums; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt1, "schema_datatype", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt1, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt1, 0, datatype, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt1, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int valueF0;
        ret = queryFieldValueAndCompare(stmt1, "F0", &valueF0, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        AW_MACRO_EXPECT_EQ_INT(loop, valueF0);

        char r_int8;
        ret = queryFieldValueAndCompare(stmt1, "F1", &r_int8, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(wr_int8, r_int8);
        AW_MACRO_EXPECT_EQ_INT(1, valueSize);

        unsigned char rd_uint8;
        ret = queryFieldValueAndCompare(stmt1, "F2", &rd_uint8, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(wr_uint8, rd_uint8);
        AW_MACRO_EXPECT_EQ_INT(1, valueSize);

        short rd_int16;
        ret = queryFieldValueAndCompare(stmt1, "F3", &rd_int16, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(wr_int16, rd_int16);
        AW_MACRO_EXPECT_EQ_INT(2, valueSize);

        unsigned short r_uint16;
        ret = queryFieldValueAndCompare(stmt1, "F4", &r_uint16, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(wr_uint16, r_uint16);
        AW_MACRO_EXPECT_EQ_INT(2, valueSize);

        int r_F5;
        ret = queryFieldValueAndCompare(stmt1, "F5", &r_F5, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(loop, r_F5);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);

        unsigned int r_F6;
        ret = queryFieldValueAndCompare(stmt1, "F6", &r_F6, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(loop, r_F6);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);

        if (isPrint == 1) {
            printf("\n[INFO] ======== Write value ======== \n");
            printf("[ F0  int32 ]  %d \n[ F1   int8 ]  %d \n[ F2  uint8 ]  %d \n[ F3  int16 ]  %d \n[ F4 uint16 ]  %d "
                   "\n[ F5  int16 ]  %d \n[ F6  int16 ]  %d \n",
                valueF0, r_int8, rd_uint8, rd_int16, r_uint16, r_F5, r_F6);
        }
    }
    GmcFreeStmt(stmt1);
    return ret;
}

/******* insert vtxLabel:  ip4forward *******/
int32_t test_insert_vertex_ip4forward(GmcStmtT *stmt, int oper_begin, int oper_end)
{
    int ret = 0;
    for (unsigned int loop = oper_begin; loop < oper_end; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt, "ip4forward", GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // hash index & pk
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // pk
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // pk
        ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // hash index: unique = false
        ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // hash index: unique = true
        ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // hash index: unique = true
        ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // hash index: unique = false
        ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // fixed 36
        // char *wr_fixed = (char *)"write";
        char wr_fixed[34] = "write";
        ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, wr_fixed, 34);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // fixed 16
        ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, wr_fixed, 34);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    return ret;
}

/* 主键读 label_name:  ip4forward */
GmcStmtT *g_stmt_rd[THR_NUM];
void *g_vertexLabel_rd[THR_NUM * 2];
int32_t test_PK_read_ip4forward(GmcConnT *conn, const char *keyName, int read_begin, int read_end,
    GmcDataTypeE datatype, int thread_id, const char *comment, int isPrint = 1)
{
    int ret = 0;
    GmcStmtT *stmt3;
    ret = GmcAllocStmt(conn, &stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (unsigned int loop = read_begin; loop < read_end; loop++) {

        ret = testGmcPrepareStmtByLabelName(stmt3, "ip4forward", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt3, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt3, 0, datatype, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt3, 1, datatype, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt3, 2, datatype, &loop, sizeof(loop));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt3);
        TEST_SCAN_DEL_RES(0, ret, loop);
        if (ret != 0)
            return ret;

        unsigned int rd_vr_id, valueSize;
        ret = queryFieldValueAndCompare(stmt3, "vr_id", &rd_vr_id, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "vr_id", rd_vr_id, thread_id, isPrint, 999);

        unsigned int rd_vrf_index;
        ret = queryFieldValueAndCompare(stmt3, "vrf_index", &rd_vrf_index, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "vrf_index", rd_vrf_index, thread_id, isPrint, 999);

        unsigned int rd_dest_ip_addr;
        ret = queryFieldValueAndCompare(stmt3, "dest_ip_addr", &rd_dest_ip_addr, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "dest_ip_addr", rd_dest_ip_addr, thread_id, isPrint, 999);

        unsigned short rd_uint16;
        ret = queryFieldValueAndCompare(stmt3, "qos_profile_id", &rd_uint16, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, valueSize);
        TEST_INFO(comment, "qos_profile_id", rd_uint16, thread_id, isPrint, 0);

        unsigned int rd_nhp_group_id;
        ret = queryFieldValueAndCompare(stmt3, "nhp_group_id", &rd_nhp_group_id, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "nhp_group_id", rd_nhp_group_id, thread_id, isPrint, 0);

        unsigned char rd_mask_len;
        ret = queryFieldValueAndCompare(stmt3, "mask_len", &rd_mask_len, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, valueSize);
        TEST_INFO(comment, "mask_len", rd_mask_len, thread_id, isPrint, 999);

        unsigned int rd_path_flags;
        ret = queryFieldValueAndCompare(stmt3, "path_flags", &rd_path_flags, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "path_flags", rd_path_flags, thread_id, isPrint, 999);

        unsigned int rd_flags;
        ret = queryFieldValueAndCompare(stmt3, "flags", &rd_flags, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "flags", rd_flags, thread_id, isPrint, 999);

        unsigned int rd_app_version;
        ret = queryFieldValueAndCompare(stmt3, "app_version", &rd_app_version, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "app_version", rd_app_version, thread_id, isPrint, 999);

        unsigned int rd_primary_label, valueSize2;
        ret = queryFieldValueAndCompare(stmt3, "primary_label", &rd_primary_label, &valueSize2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize2);
        TEST_INFO(comment, "primary_label", rd_primary_label, thread_id, isPrint, 1);

        unsigned int rd_attribute_id;
        ret = queryFieldValueAndCompare(stmt3, "attribute_id", &rd_attribute_id, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "attribute_id", rd_attribute_id, thread_id, isPrint, 1);

        unsigned char rd_nhp_group_flag;
        ret = queryFieldValueAndCompare(stmt3, "nhp_group_flag", &rd_nhp_group_flag, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // AW_MACRO_EXPECT_EQ_INT(wr_uint8, rd_nhp_group_flag);
        AW_MACRO_EXPECT_EQ_INT(1, valueSize);
        TEST_INFO(comment, "nhp_group_flag", rd_nhp_group_flag, thread_id, isPrint, 999);

        unsigned long long rd_app_obj_id;
        ret = queryFieldValueAndCompare(stmt3, "app_obj_id", &rd_app_obj_id, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // AW_MACRO_EXPECT_EQ_INT(wr_uint8, rd_app_obj_id);
        AW_MACRO_EXPECT_EQ_INT(8, valueSize);
        TEST_INFO(comment, "app_obj_id", rd_app_obj_id, thread_id, isPrint, 999);

        // char *rd_svc_ctx_high_prio_fixed = (char *)malloc(sizeof(char) * 36);
        char rd_svc_ctx_high_prio_fixed[35] = {0};
        ret = queryFieldValueAndCompare(stmt3, "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(34, valueSize);
        TEST_INFO_STR(comment, "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, thread_id, isPrint, 999);
        // free(rd_svc_ctx_high_prio_fixed);

#if NOT_EXIST		
		char *rd_svc_ctx_high_prio_fixed = (char *)malloc(sizeof(char) * 36);
		ret = GmcGetVertexPropertySizeByName(stmt1, "svc_ctx_high_prio", &valueSize);
		AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
		ret = GmcGetVertexPropertyByName(stmt1, "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, valueSize, &isNull);    
		AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret); 
		printf("[INFO][ERROR] fixed value %s \n", rd_svc_ctx_high_prio_fixed);
#endif
        if (isPrint == 1)
            printf("\n");
    }
    GmcFreeIndexKey(stmt3);
    GmcFreeStmt(stmt3);
    return ret;
}

/***** check value with pk *****/
// void *vertexLabel2 = NULL;
GmcStmtT *stmt2;
int32_t check_value_prepare(GmcConnT *conn, const char *labelName, const char *keyName, int keyValue)
{

    int ret = 0;

    ret = GmcAllocStmt(conn, &stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // ret = GmcOpenVertexLabelByName(stmt2, labelName, &vertexLabel2);

    ret = testGmcPrepareStmtByLabelName(stmt2, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt2, keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2, 0, GMC_DATATYPE_UINT32, &keyValue, sizeof(keyValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2, 1, GMC_DATATYPE_UINT32, &keyValue, sizeof(keyValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt2, 2, GMC_DATATYPE_UINT32, &keyValue, sizeof(keyValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // ret = GmcDirectFetchVertex(stmt2, vertexLabel2, keyName, NULL);
    ret = GmcExecute(stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool eof;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcFetch(stmt2, &eof));
    return ret;
}

int32_t check_value(
    const char *fieldName, GmcDataTypeE datatype, void *expectValue, const char *comment, int isPrint = 1)
{

    int ret = 0;

    switch (datatype) {
        case GMC_DATATYPE_UINT32:
            unsigned int rd_uint32;
            ret = queryFieldValueAndCompare(stmt2, fieldName, &rd_uint32, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(*(uint32_t *)expectValue, rd_uint32);
            TEST_INFO(comment, fieldName, rd_uint32, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_CHAR:
            char rd_int8;
            ret = queryFieldValueAndCompare(stmt2, fieldName, &rd_int8, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(*(char *)expectValue, rd_int8);
            AW_MACRO_EXPECT_EQ_INT(4, valueSize);
            TEST_INFO(comment, fieldName, rd_int8, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT16:
            unsigned short rd_uint16;
            ret = queryFieldValueAndCompare(stmt2, fieldName, &rd_uint16, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(*(unsigned short *)expectValue, rd_uint16);
            AW_MACRO_EXPECT_EQ_INT(2, valueSize);
            TEST_INFO(comment, fieldName, rd_uint16, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT8:
            unsigned char rd_uint8;
            ret = queryFieldValueAndCompare(stmt2, fieldName, &rd_uint8, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(*(unsigned char *)expectValue, rd_uint8);
            AW_MACRO_EXPECT_EQ_INT(1, valueSize);
            TEST_INFO(comment, fieldName, rd_uint8, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT64:
            unsigned long long rd_uint64;
            ret = queryFieldValueAndCompare(stmt2, fieldName, &rd_uint64, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(*(unsigned long long *)expectValue, rd_uint64);
            AW_MACRO_EXPECT_EQ_INT(8, valueSize);
            TEST_INFO(comment, fieldName, rd_uint64, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_FIXED:
            char rd_fixed[37] = {};
            ret = queryFieldValueAndCompare(stmt2, fieldName, rd_fixed, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            EXPECT_STREQ((char *)expectValue, rd_fixed);
            AW_MACRO_EXPECT_EQ_INT(36, valueSize);
            TEST_INFO_STR(comment, fieldName, rd_fixed, 0, isPrint, 0);
            break;
    }
}

int32_t check_value_close()
{
    GmcFreeIndexKey(stmt2);
    GmcFreeStmt(stmt2);
    return ret;
}

/* hash索引 Scan */
int scan_end = 0;
extern unsigned char up_nhp_group_flag;
extern unsigned short up_qos_profile_id;

int32_t test_hashInx_scan_ip4forward(
    GmcStmtT *stmt, const char *keyName, int oper_nums, int thread_id, const char *comment, int isPrint = 1)
{

    int ret = 0;

    scan_end = 0;
    int cnt = 0;
    for (int loop = 0; loop < oper_nums + 2; loop++) {
        cnt++;
        ret = GmcFetch(stmt, &isFinish);
        TEST_SCAN_DEL_RES(0, ret, cnt);
        if (isFinish == true || ret != 0) {
            printf("fetch times: %d, status is %d \n", cnt, ret);
            scan_end = cnt;
            return GMERR_OK;
        }
        unsigned int sc_vr_id;
        ret = queryFieldValueAndCompare(stmt, "vr_id", &sc_vr_id, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "vr_id", sc_vr_id, thread_id, isPrint, 999);
#if 1
        unsigned int sc_vrf_index;
        ret = queryFieldValueAndCompare(stmt, "vrf_index", &sc_vrf_index, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "vrf_index", sc_vrf_index, thread_id, isPrint, 999);

        unsigned int sc_dest_ip_addr;
        ret = queryFieldValueAndCompare(stmt, "dest_ip_addr", &sc_dest_ip_addr, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "dest_ip_addr", sc_dest_ip_addr, thread_id, isPrint, 999);

        unsigned short sc_qos_profile_id;
        unsigned int valueSize2;
        ret = queryFieldValueAndCompare(stmt, "qos_profile_id", &sc_qos_profile_id, &valueSize2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // AW_MACRO_EXPECT_EQ_INT(up_qos_profile_id, sc_qos_profile_id);  // update field
        AW_MACRO_EXPECT_EQ_INT(2, valueSize2);
        TEST_INFO(comment, "qos_profile_id", sc_qos_profile_id, thread_id, isPrint, 0);

        unsigned int sc_nhp_group_id;
        ret = queryFieldValueAndCompare(stmt, "nhp_group_id", &sc_nhp_group_id, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        // AW_MACRO_EXPECT_EQ_INT(uint32_tmp, sc_nhp_group_id);
        TEST_INFO(comment, "nhp_group_id", sc_nhp_group_id, thread_id, isPrint, 0);

        // batch update 3 fields
        unsigned int sc_path_flags;
        ret = queryFieldValueAndCompare(stmt, "path_flags", &sc_path_flags, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "path_flags", sc_path_flags, thread_id, isPrint, 999);

        unsigned int sc_flags;
        ret = queryFieldValueAndCompare(stmt, "flags", &sc_flags, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "flags", sc_flags, thread_id, isPrint, 999);

        unsigned int sc_app_version;
        ret = queryFieldValueAndCompare(stmt, "app_version", &sc_app_version, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "app_version", sc_app_version, thread_id, isPrint, 999);

        unsigned int sc_table_smooth_id;
        unsigned int valueSize1;
        ret = queryFieldValueAndCompare(stmt, "table_smooth_id", &sc_table_smooth_id, &valueSize1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize1);
        TEST_INFO(comment, "table_smooth_id", sc_table_smooth_id, thread_id, isPrint, 999);

        unsigned int sc_primary_label;
        ret = queryFieldValueAndCompare(stmt, "primary_label", &sc_primary_label, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "primary_label", sc_primary_label, thread_id, isPrint, 1);

        unsigned int sc_attribute_id;
        ret = queryFieldValueAndCompare(stmt, "attribute_id", &sc_attribute_id, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        TEST_INFO(comment, "attribute_id", sc_attribute_id, thread_id, isPrint, 1);

        unsigned char sc_nhp_group_flag;
        ret = queryFieldValueAndCompare(stmt, "nhp_group_flag", &sc_nhp_group_flag, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // AW_MACRO_EXPECT_EQ_INT(up_nhp_group_flag, sc_nhp_group_flag);  // update field
        AW_MACRO_EXPECT_EQ_INT(1, valueSize);
        TEST_INFO(comment, "nhp_group_flag", sc_nhp_group_flag, thread_id, isPrint, 999);

        char rd_svc_ctx_high_prio_fixed[37] = {};
        ret = queryFieldValueAndCompare(stmt, "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(34, valueSize);
        TEST_INFO_STR(comment, "svc_ctx_high_prio", rd_svc_ctx_high_prio_fixed, thread_id, isPrint, 999);

        if (isPrint == 1)
            printf("\n");
#endif
    }
    GmcResetStmt(stmt);
    // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // GmcFreeIndexKey(stmt);
    return ret;
}

void set_vtxLabel_pk_field(GmcStmtT *stmt, int pk_value)
{

    int ret = 0;

    ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &pk_value, sizeof(pk_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &pk_value, sizeof(pk_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "dest_ip_addr", GMC_DATATYPE_UINT32, &pk_value, sizeof(pk_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void set_vtxLabel_uniq_hash_field(GmcStmtT *stmt, int hash_value)
{

    int ret = 0;

    ret = GmcSetVertexProperty(stmt, "primary_label", GMC_DATATYPE_UINT32, &hash_value, sizeof(hash_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "attribute_id", GMC_DATATYPE_UINT32, &hash_value, sizeof(hash_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void set_vtxLabel_field(GmcStmtT *stmt, int set_value)
{

    int ret = 0;

    ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "nhp_group_flag", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = false
    ret = GmcSetVertexProperty(stmt, "qos_profile_id", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // hash index: unique = false
    ret = GmcSetVertexProperty(stmt, "nhp_group_id", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "path_flags", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "flags", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "status_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_high_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "errcode_normal_prio", GMC_DATATYPE_UINT8, &wr_uint8, sizeof(wr_uint8));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // fixed 16
    char fixed_tmp[36] = "aaaaaa";
    ret = GmcSetVertexProperty(stmt, "svc_ctx_high_prio", GMC_DATATYPE_FIXED, fixed_tmp, 34);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // fixed 16
    ret = GmcSetVertexProperty(stmt, "svc_ctx_normal_prio", GMC_DATATYPE_FIXED, fixed_tmp, 34);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_source_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "table_smooth_id", GMC_DATATYPE_UINT32, &uint32_tmp, sizeof(uint32_tmp));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_obj_id", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "app_version", GMC_DATATYPE_UINT32, &set_value, sizeof(set_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "trace", GMC_DATATYPE_UINT64, &wr_uint64, sizeof(wr_uint64));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "route_flags", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "reserved", GMC_DATATYPE_UINT16, &wr_uint16, sizeof(wr_uint16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/************* SN 条件订阅 *************/
int32_t check_value_sn(GmcStmtT *sub_stmt, const char *fieldName, GmcDataTypeE datatype, void *expectValue,
    const char *comment, int isPrint = 1)
{
    int ret = 0;

    switch (datatype) {
        case GMC_DATATYPE_UINT32:
            unsigned int rd_uint32;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint32, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(*(uint32_t *)expectValue, rd_uint32);
            AW_MACRO_EXPECT_EQ_INT(4, valueSize);
            TEST_INFO(comment, fieldName, rd_uint32, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_CHAR:
            char rd_int8;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_int8, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // AW_MACRO_EXPECT_EQ_INT(*(char *)expectValue, rd_int8);
            AW_MACRO_EXPECT_EQ_INT(4, valueSize);
            TEST_INFO(comment, fieldName, rd_int8, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT16:
            unsigned short rd_uint16;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint16, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // AW_MACRO_EXPECT_EQ_INT(*(unsigned short *)expectValue, rd_uint16);
            AW_MACRO_EXPECT_EQ_INT(2, valueSize);
            TEST_INFO(comment, fieldName, rd_uint16, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT8:
            unsigned char rd_uint8;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint8, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(*(unsigned char *)expectValue, rd_uint8);
            AW_MACRO_EXPECT_EQ_INT(1, valueSize);
            TEST_INFO(comment, fieldName, rd_uint8, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_UINT64:
            unsigned long long rd_uint64;
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, &rd_uint64, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(*(unsigned long long *)expectValue, rd_uint64);
            AW_MACRO_EXPECT_EQ_INT(8, valueSize);
            TEST_INFO(comment, fieldName, rd_uint64, 0, isPrint, 0);
            break;
        case GMC_DATATYPE_FIXED:
            char rd_fixed[37] = {};
            ret = queryFieldValueAndCompare(sub_stmt, fieldName, rd_fixed, &valueSize);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            EXPECT_STREQ((char *)expectValue, rd_fixed);
            AW_MACRO_EXPECT_EQ_INT(34, valueSize);
            TEST_INFO_STR(comment, fieldName, rd_fixed, 0, isPrint, 0);
            break;

            // case GMC_DATATYPE_UCHAR :
            // case GMC_DATATYPE_INT8 :
            // case GMC_DATATYPE_INT16 :
            // case GMC_DATATYPE_INT32 :
            // case GMC_DATATYPE_BOOL :
            // case GMC_DATATYPE_INT64 :
            // case GMC_DATATYPE_FLOAT :
            // case GMC_DATATYPE_DOUBLE :
            // case GMC_DATATYPE_TIME :
            // case GMC_DATATYPE_STRING :
            // case GMC_DATATYPE_BYTES :
    }
}

GmcConnT *conn_sn_sync;
// void *g_label = NULL;
void sn_push_check(GmcConnT *sn_conn, void *g_vertex, uint32_t sub_type, const char *vtx_labelName,
    const char *fieldName, GmcDataTypeE fieldType, int oper_nums, void *oldValue, void *newValue, const char *comment,
    int isprint = 0)
{
    int sub_num = 0;
    int sub_push_add = 0;
    int sub_push_updt = 0;
    int sub_push_del = 0;
    int ret = 0;

    for (int i = 0; i < oper_nums; i++) {

        GmcStmtT *subStmt = NULL;
        void *new_vtx;
        ret = GmcAllocStmt(sn_conn, &subStmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // check RecvPushMsg: event_type, vrtxLabelNum
        char pushVrtxName[MAX_NAME_LENGTH] = {0};
        uint32_t vrtxNameLen = MAX_NAME_LENGTH;
        uint32_t eventType, vrtxLabelNum, msgType;
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)1, vrtxLabelNum);

        ret = GmcSubGetLabelName(subStmt, 0, pushVrtxName, &vrtxNameLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        EXPECT_STREQ(vtx_labelName, pushVrtxName);
        AW_MACRO_EXPECT_EQ_INT(strlen(vtx_labelName), vrtxNameLen);
        if (ret == 0)
            sub_num++;

        bool eof = false;
        while (!eof) {
            ret = GmcFetch(subStmt, &eof);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true) {
                break;
            }
            switch (eventType) {
                case GMC_SUB_EVENT_INSERT:  // sub insert 只校验new value
                {
                    // check new value
                    // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = check_value_sn(subStmt, fieldName, fieldType, newValue, comment, isprint);
                    sub_push_add++;
                    break;
                }
                case GMC_SUB_EVENT_DELETE:  // sub delete 只校验old value
                {
                    // check old value
                    // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = check_value_sn(subStmt, fieldName, fieldType, oldValue, comment, isprint);
                    sub_push_del++;
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // check new value
                    // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = check_value_sn(subStmt, fieldName, fieldType, newValue, comment, isprint);
                    // check old value
                    // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = check_value_sn(subStmt, fieldName, fieldType, oldValue, comment, isprint);
                    sub_push_updt++;
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            // GmcFreeStmt(subStmt);
        }
    }
    fprintf(stderr, "\n[INFO][" L_PURPLE "SUB PUSH" NONE "][%s] \ninsert: %d, update: %d, delete: %d \n", vtx_labelName,
        sub_push_add, sub_push_updt, sub_push_del);
}

void sn_push_check_1(GmcConnT *sn_conn, const char *vtx_labelName, const char *fieldName, GmcDataTypeE fieldType,
    int oper_nums, void *oldValue, void *newValue, const char *comment, int isprint = 0)
{
    int sub_num = 0;
    int sub_push_add = 0;
    int sub_push_updt = 0;
    int sub_push_del = 0;
    int ret = 0;

    GmcStmtT *subStmt = NULL;
    void *new_vtx;
    ret = GmcAllocStmt(sn_conn, &subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // ret = GmcOpenVertexLabelByName(stmt, vtx_labelName, &g_vertex);
    // AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    for (int i = 0; i < oper_nums; i++) {

        // check RecvPushMsg: event_type, vrtxLabelNum
        char pushVrtxName[MAX_NAME_LENGTH] = {0};
        uint32_t vrtxNameLen = MAX_NAME_LENGTH;
        uint32_t eventType, vrtxLabelNum, msgType;
        // ret = GmcRecvSubPushMsg(subStmt, &eventType, &vrtxLabelNum, &msgType);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // AW_MACRO_EXPECT_EQ_INT((uint32_t)sub_type, eventType);
        AW_MACRO_EXPECT_EQ_INT((uint32_t)1, vrtxLabelNum);

        ret = GmcSubGetLabelName(subStmt, 0, pushVrtxName, &vrtxNameLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        EXPECT_STREQ(vtx_labelName, pushVrtxName);
        AW_MACRO_EXPECT_EQ_INT(strlen(vtx_labelName), vrtxNameLen);
        if (ret == 0)
            sub_num++;

        bool eof = false;
        while (!eof) {
            ret = GmcFetch(subStmt, &eof);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true) {
                break;
            }
            switch (eventType) {
                case GMC_SUB_EVENT_INSERT:  // sub insert 只校验new value
                {
                    // check new value
                    // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = check_value_sn(subStmt, fieldName, fieldType, newValue, comment, isprint);
                    sub_push_add++;
                    break;
                }
                case GMC_SUB_EVENT_DELETE:  // sub delete 只校验old value
                {
                    // check old value
                    // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = check_value_sn(subStmt, fieldName, fieldType, oldValue, comment, isprint);
                    sub_push_del++;
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // check new value
                    // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = check_value_sn(subStmt, fieldName, fieldType, newValue, comment, isprint);
                    // check old value
                    // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = check_value_sn(subStmt, fieldName, fieldType, oldValue, comment, isprint);
                    sub_push_updt++;
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            GmcFreeStmt(subStmt);
        }
    }
    // printf("\n[INFO] push sub num: %d \n", sub_num);
    // printf("\n[INFO][SUB PUSH] insert: %d, update: %d, delete: %d, num: %d \n", sub_del_push);
    fprintf(stderr, "\n[INFO][" L_PURPLE "SUB PUSH" NONE "][%s] \ninsert: %d, update: %d, delete: %d \n", vtx_labelName,
        sub_push_add, sub_push_updt, sub_push_del);
}

#if NOT_EXIST
void sn_push_check_new(GmcConnT *sn_conn, void *g_vertex, uint32_t sub_type, const char *vtx_labelName, const char *fieldName,GmcDataTypeE fieldType, int oper_nums, void* oldValue, void* newValue, const char *comment, int isprint = 0)
{
	int sub_num = 0;
	int sub_push_add = 0;
	int sub_push_updt = 0;
	int sub_push_del = 0;
	int ret = 0;
	
    // SnUserDataT *user_data = (SnUserDataT *)userData;
    switch(eventType)
    {
        case GMC_SUB_EVENT_INSERT:    // sub insert 只校验new value
        {		
            // check new value
            // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = check_value_sn(subStmt, fieldName, fieldType, newValue, comment, isprint);					
            sub_push_add++;
            break;
        }
        case GMC_SUB_EVENT_DELETE:    // sub delete 只校验old value
        {				
            // check old value
            // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = check_value_sn(subStmt, fieldName, fieldType, oldValue, comment, isprint);					
            sub_push_del++;
            break;
        }			
        case GMC_SUB_EVENT_UPDATE:
        {
            // check new value
            // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = check_value_sn(subStmt, fieldName, fieldType, newValue, comment, isprint);
            // check old value				
            // ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
            ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = check_value_sn(subStmt, fieldName, fieldType, oldValue, comment, isprint);
            sub_push_updt++;
            break;
        }
        default:
        {
            printf("default: invalid eventType\r\n");
            break;
        }
    } 
    // GmcFreeStmt(subStmt);
	
	fprintf(stderr, "\n[INFO][" L_PURPLE "SUB PUSH" NONE "][%s] \ninsert: %d, update: %d, delete: %d \n", vtx_labelName, sub_push_add, sub_push_updt, sub_push_del);
}



/***************** sn callback *****************/
// void sn_callback(void *sn_conn, void *g_vertex, uint32_t sub_type, const char *vtx_labelName, const char *fieldName,GmcDataTypeE fieldType, int oper_nums, void* oldValue, void* newValue, const char *comment, int isprint = 0)
void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
	int sub_num = 0;
	int sub_push_add = 0;
	int sub_push_updt = 0;
	int sub_push_del = 0;
    
    int ret = 0;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;

			
	for (int i = 0; i < oper_nums; i++) {
        
		GmcStmtT *subStmt = NULL;
		void *new_vtx;
		ret = GmcAllocStmt(sn_conn, &subStmt);
		AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
		
        // check RecvPushMsg: event_type, vrtxLabelNum
		char pushVrtxName[MAX_NAME_LENGTH] = {0};
		uint32_t vrtxNameLen = MAX_NAME_LENGTH;
		uint32_t eventType, vrtxLabelNum, msgType;
		AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
		AW_MACRO_EXPECT_EQ_INT((uint32_t)1, vrtxLabelNum);
		
        ret = GmcSubGetLabelName(subStmt, 0, pushVrtxName, &vrtxNameLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        EXPECT_STREQ(vtx_labelName, pushVrtxName);
        AW_MACRO_EXPECT_EQ_INT(strlen(vtx_labelName), vrtxNameLen);
		if(ret == 0)
			 sub_num++;

        bool eof = false;    
		while (!eof){
            ret = GmcFetch(subStmt, &eof);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (ret != GMERR_OK || eof == true ) {
                break;
            }    
            switch(eventType)
            {
				case GMC_SUB_EVENT_INSERT:    // sub insert 只校验new value
                {		
					// check new value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
					AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
					ret = check_value_sn(subStmt, fieldName, fieldType, newValue, comment, isprint);					
					sub_push_add++;
					break;
				}
				case GMC_SUB_EVENT_DELETE:    // sub delete 只校验old value
                {				
					// check old value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
					AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
					ret = check_value_sn(subStmt, fieldName, fieldType, oldValue, comment, isprint);					
					sub_push_del++;
					break;
				}			
				case GMC_SUB_EVENT_UPDATE:
                {
					// check new value
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, false);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
					AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
					ret = check_value_sn(subStmt, fieldName, fieldType, newValue, comment, isprint);
					// check old value				
					// ret = GmcSubSetFetchVertexLabel(subStmt, pushVrtxName, true);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
					AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
					ret = check_value_sn(subStmt, fieldName, fieldType, oldValue, comment, isprint);
					sub_push_updt++;
					break;
				}
				default:
                {
                    printf("default: invalid eventType\r\n");
                    break;
                }
			} 
			// GmcFreeStmt(subStmt);
		}
	}	
	fprintf(stderr, "\n[INFO][" L_PURPLE "SUB PUSH" NONE "][%s] \ninsert: %d, update: %d, delete: %d \n", vtx_labelName, sub_push_add, sub_push_updt, sub_push_del);
}

#endif

void sn_callback_simple(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = 0;
    ;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *label = 0, *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    bool eof = false;
    const char *fieldName = "qos_profile_id";
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);
            //默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[0];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_INSERT new_value is %lu\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);

                    ret = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push insert", 1);
                    // ret = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, (void
                    // *)((user_data->new_value))[0], "sn push insert", 1); ret = check_value_sn(subStmt, fieldName,
                    // fieldType, ((int *)user_data->new_value), comment, isprint);

                    //读old
                    // ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读new
                    // ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    // AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[0];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_DELETE old_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    ret = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push delete", 1);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // index = ((int *)user_data->new_value)[g_subIndex];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    ret = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push update", 1);
                    //读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // index = ((int *)user_data->old_value)[g_subIndex];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    ret = check_value_sn(subStmt, fieldName, GMC_DATATYPE_UINT16, &index, "sn push update", 1);
                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    //读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    // index = ((int *)user_data->new_value)[g_subIndex];
                    // printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    // test_checkVertexProperty_sub(subStmt, index);
                    //读old
                    if (((bool *)user_data->isReplace_insert)[g_subIndex]) {
                        // printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE insert\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        char *pValue = (char *)malloc(sizeof(int32_t));
                        bool isNull;
                        ret = GmcGetVertexPropertyByName(subStmt, "F6", pValue, sizeof(int32_t), &isNull);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        AW_MACRO_EXPECT_EQ_INT(1, isNull);
                        free(pValue);
                    } else {
                        // printf("[NEW OBJECT] GMC_SUB_EVENT_REPLACE update\r\n");
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        // index = ((int *)user_data->old_value)[g_subIndex];
                        // test_checkVertexProperty_sub(subStmt, index);
                    }
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        g_subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
