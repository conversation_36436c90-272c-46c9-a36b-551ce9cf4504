/* ****************************************************************************
 Description  :二级索引支持Partial能力 hashcluster测试
001.uchar类型字段=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/locahashl删除记录并hashcluster查询
002.char类型字段!=多个条件建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
003.uint8类型字段!=多个条件建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
004.int8类型字段=负数建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
005.uint16类型字段=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
006.int16类型字段！=多个（含负数）建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
007.uint32类型字段=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
008.int32类型字段!=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
009.uint64类型字段=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
010.int64类型字段!=建partial索引（含负数），插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
011.fixed
类型字段!=建partial索引(含16进制值与字符串值至少2个条件)，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster查询hashcluster删除记录并hashcluster查询
012.time类型字段=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
013.多个字段不连续且乱序满16个条件建partial索引，插入多条记录并hashcluster查询/hashcluster更新成不满足filter记录并hashcluster查询/插入新值/hashcluster删除记录并hashcluster查询
014.唯一索引，已存在满足filter的记录，将不满足filter的记录主键更新/merge/replace为满足filter的记录并hashcluster查询
015.多个字段多个条件将不满足filter的记录replace成满足filter的记录并hashcluster查询和主键查询
016.多个字段多个条件将不满足filter的记录merge成满足filter的记录并hashcluster查询和主键查询
017.多个字段多个条件将不满足filter的记录主键更新成满足filter的记录并hashcluster查询和主键查询
018.多个字段多个条件将满足filter的记录merge成不满足filter的记录并hashcluster查询和主键查询
019.多个字段多个条件将满足filter的记录replace成不满足filter的记录并hashcluster查询和主键查询
020.多个字段多个条件将满足filter的记录主键更新成不满足filter的记录并hashcluster查询和主键查询

 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2022/1/25
**************************************************************************** */

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"
#include "indexPartial.h"
#include "../../common/hash_util.h"

class indexPartialHashCluster : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void indexPartialHashCluster::SetUpTestCase()
{
    system("sh $TEST_HOME/tools/modifyCfg.sh \"compatibleV3=0\"");
    // 配置相关环境变量及重启server
    InitCfg();
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void indexPartialHashCluster::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复配置文件
    RecoverCfg();
}

void indexPartialHashCluster::SetUp()
{
    int ret = 0;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void indexPartialHashCluster::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = GmcDropVertexLabel(g_stmt, g_labelNameHashC1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testGmcDisconnect(g_conn, g_stmt);
    g_conn = NULL;
    g_stmt = NULL;
}

// 001.uchar类型字段=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/locahashl删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_uchar.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    uint8_t *fixedValue = (uint8_t *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    uint32_t FetchTimes = 0;
    bool isNull = false;

    //插入数据
    GmcNodeT *root = NULL, *T1 = NULL;
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialSetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
        if (i % 4 == 1) {
            indexPartialHashCluSetHashcluster2Property(root, 1, 'a');
        } else if (i % 4 == 2) {
            indexPartialHashCluSetHashcluster2Property(root, 1, 'b');
        } else if (i % 4 == 3) {
            indexPartialHashCluSetHashcluster2Property(root, 1, '=');
        } else {
            indexPartialHashCluSetHashcluster2Property(root, 1, '?');
        }
        // 插入vector节点
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    //主键fetch
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexPartialSetPkIndex(g_stmt, g_PkName, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //获取根节点和子节点
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialGetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
        if (i % 4 == 1) {
            indexPartialHashCluGetHashcluster2Property(root, 1, 'a');
        } else if (i % 4 == 2) {
            indexPartialHashCluGetHashcluster2Property(root, 1, 'b');
        } else if (i % 4 == 3) {
            indexPartialHashCluGetHashcluster2Property(root, 1, '=');
        } else {
            indexPartialHashCluGetHashcluster2Property(root, 1, '?');
        }
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(true, isFinish);
    }
    //索引查
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (i % 4 == 1) {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', 1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(true, isFinish);
        } else if (i % 4 == 2) {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'b', 1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(true, isFinish);
        } else if (i % 4 == 3) {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, '=', 1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(true, isFinish);
        } else {
            FetchTimes = 0;
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, '?', 1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            //获取根节点和子节点
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t j = f0_value;
                if (j % 4 != 0) {
                    AW_MACRO_ASSERT_EQ_INT(1, 0);
                }
                indexPartialGetPropertyRoot2(root, j, boolValue, strValue, fixedValue, bytesValue);
                indexPartialHashCluGetHashcluster2Property(root, 1, '?');
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            AW_MACRO_ASSERT_EQ_INT(25, FetchTimes);
        }
    }
    //主键更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 1) {
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            //更新字段
            indexPartialHashCluSetHashcluster2Property(root, 1, '?');
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else if (i % 4 == 0) {
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            //更新字段
            indexPartialHashCluSetHashcluster2Property(root, 1, 'c');
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    //查询
    FetchTimes = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, '?', 1);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //获取根节点和子节点
    while (!isFinish) {
        FetchTimes++;
        int64_t f0_value = 0;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t i = f0_value;
        if (i % 4 != 1) {
            AW_MACRO_ASSERT_EQ_INT(1, 0);
        }
        indexPartialGetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
        indexPartialHashCluGetHashcluster2Property(root, 1, '?');
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_ASSERT_EQ_INT(25, FetchTimes);
    //删除
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, '?', 1);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestAffactRows(g_stmt, 25);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, '?', 1);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.char类型字段!=多个条件建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_char.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    uint8_t *fixedValue = (uint8_t *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;

    //插入数据
    indexPartialHashclusterTInsertVertex(
        g_stmt, g_labelNameHashC1, startNum, endNum, boolValue, strValue, fixedValue, bytesValue);
    //主键fetch
    indexPartialTestFetchVertex(
        g_stmt, g_labelNameHashC1, boolValue, strValue, fixedValue, bytesValue, startNum, endNum, vectorNum, true);
    //索引查
    for (int32_t i = 10; i < endNum; i++) {
        uint32_t a = i;
        indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '1', boolValue, strValue, fixedValue,
            bytesValue, i, a, vectorNum, true);
    }
    for (int32_t i = 0; i < 2; i++) {
        uint32_t a = i;
        indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, 'b', boolValue, strValue, fixedValue,
            bytesValue, i, a, vectorNum, false);
    }
    for (int32_t i = 2; i < 4; i++) {
        uint32_t a = i;
        indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '0', boolValue, strValue, fixedValue,
            bytesValue, i, a, vectorNum, false);
    }
    for (int32_t i = 8; i < 10; i++) {
        uint32_t a = i;
        indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, 'a', boolValue, strValue, fixedValue,
            bytesValue, i, a, vectorNum, false);
    }
    for (int32_t i = 4; i < 8; i++) {
        uint32_t a = i;
        indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '1', boolValue, strValue, fixedValue,
            bytesValue, i, a, vectorNum, true);
    }

    //更新其中2条
    GmcNodeT *root = NULL, *T1 = NULL;
    bool isNull = false;
    int64_t f0_value = 0;
    char updateChar = '2';
    int FetchTimes = 0;
    for (int32_t i = 8; i < 10; i++) {
        uint32_t a = i;
        indexHashClusterTestPkUpdate(g_stmt, g_labelNameHashC1, g_PkName, i, updateChar, fixedValue, i, a, 1);
    }
    //搜索
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int32_t j = 8; j < 10; j++) {
        uint32_t a = j;
        FetchTimes = 0;
        indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, updateChar, j, a, fixedValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //获取根节点和子节点
        while (!isFinish) {
            FetchTimes++;
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
            int32_t i = f0_value;
            indexPartialGetHashclusterProperty(root, updateChar, j, a, fixedValue);
            indexPartialGetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
            // 读取vector节点
            for (uint32_t m = 0; m < vectorNum; m++) {
                ret = GmcNodeGetElementByIndex(T1, m, &T1);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                indexPartialGetChildNodePropertyByName(T1, m);
            }
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        AW_MACRO_ASSERT_EQ_INT(1, FetchTimes);
    }

    //删除部分
    for (int32_t i = 10; i < endNum; i++) {
        uint32_t a = i;
        indexHashClusterDelete(g_stmt, g_labelNameHashC1, g_HashclusterName1, '1', fixedValue, i, a, 1);
    }
    //搜索
    for (int32_t i = 10; i < endNum; i++) {
        uint32_t a = i;
        indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '1', boolValue, strValue, fixedValue,
            bytesValue, i, a, vectorNum, false);
    }
    for (int32_t i = 4; i < 8; i++) {
        uint32_t a = i;
        indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '1', boolValue, strValue, fixedValue,
            bytesValue, i, a, vectorNum, true);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.uint8类型字段!=多个条件建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_uint8.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    uint8_t *fixedValue = (uint8_t *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    uint32_t FetchTimes = 0;
    bool isNull = false;

    //插入数据
    GmcNodeT *root = NULL, *T1 = NULL;
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialSetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
        if (i % 4 == 1) {
            indexPartialHashCluSetHashcluster2Property(root, -1, 'a');
        } else if (i % 4 == 2) {
            indexPartialHashCluSetHashcluster2Property(root, 100, 'a');
        } else if (i % 4 == 3) {
            indexPartialHashCluSetHashcluster2Property(root, 254, 'a');
        } else {
            indexPartialHashCluSetHashcluster2Property(root, i, 'a');
        }
        // 插入vector节点
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    //主键fetch
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexPartialSetPkIndex(g_stmt, g_PkName, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //获取根节点和子节点
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialGetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
        if (i % 4 == 1) {
            indexPartialHashCluGetHashcluster2Property(root, -1, 'a');
        } else if (i % 4 == 2) {
            indexPartialHashCluGetHashcluster2Property(root, 100, 'a');
        } else if (i % 4 == 3) {
            indexPartialHashCluGetHashcluster2Property(root, 254, 'a');
        } else {
            indexPartialHashCluGetHashcluster2Property(root, i, 'a');
        }
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(true, isFinish);
    }
    //索引查
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (i % 4 == 1) {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', -1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(true, isFinish);
        } else if (i % 4 == 2) {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', 100);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(true, isFinish);
        } else if (i % 4 == 3) {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', 254);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(true, isFinish);
        } else {
            FetchTimes = 0;
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', i);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(false, isFinish);
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t j = f0_value;
                if (j % 4 != 0) {
                    AW_MACRO_ASSERT_EQ_INT(1, 0);
                }
                indexPartialGetPropertyRoot2(root, j, boolValue, strValue, fixedValue, bytesValue);
                indexPartialHashCluGetHashcluster2Property(root, i, 'a');
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            AW_MACRO_ASSERT_EQ_INT(1, FetchTimes);
        }
    }
    //主键更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 1) {
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            //更新字段
            indexPartialHashCluSetHashcluster2Property(root, 10, 'a');
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else if (i % 4 == 0) {
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            //更新字段
            indexPartialHashCluSetHashcluster2Property(root, 100, 'a');
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    //查询
    FetchTimes = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', 10);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isFinish);
    //获取根节点和子节点
    while (!isFinish) {
        FetchTimes++;
        int64_t f0_value = 0;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t i = f0_value;
        if (i % 4 != 1) {
            AW_MACRO_ASSERT_EQ_INT(1, 0);
        }
        indexPartialGetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
        indexPartialHashCluGetHashcluster2Property(root, 10, 'a');
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_ASSERT_EQ_INT(25, FetchTimes);
    //删除
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', 10);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestAffactRows(g_stmt, 25);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', 10);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.int8类型字段=负数建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_int8.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    uint8_t *fixedValue = (uint8_t *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    uint32_t FetchTimes = 0;
    bool isNull = false;

    //插入数据
    GmcNodeT *root = NULL, *T1 = NULL;
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialSetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
        if (i % 4 == 1) {
            indexPartialHashCluSetHashcluster2Property(root, -127, 'a');
        } else {
            indexPartialHashCluSetHashcluster2Property(root, i, 'a');
        }
        // 插入vector节点
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    //主键fetch
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexPartialSetPkIndex(g_stmt, g_PkName, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //获取根节点和子节点
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialGetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
        if (i % 4 == 1) {
            indexPartialHashCluGetHashcluster2Property(root, -127, 'a');
        } else {
            indexPartialHashCluGetHashcluster2Property(root, i, 'a');
        }
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(true, isFinish);
    }
    //索引查
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (i % 4 != 1) {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', i);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(true, isFinish);
        } else {
            FetchTimes = 0;
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', -127);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(false, isFinish);
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t j = f0_value;
                if (j % 4 != 1) {
                    AW_MACRO_ASSERT_EQ_INT(1, 0);
                }
                indexPartialGetPropertyRoot2(root, j, boolValue, strValue, fixedValue, bytesValue);
                indexPartialHashCluGetHashcluster2Property(root, -127, 'a');
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            AW_MACRO_ASSERT_EQ_INT(25, FetchTimes);
        }
    }
    //主键更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 1) {
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            //更新字段
            indexPartialHashCluSetHashcluster2Property(root, i, 'a');
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else if (i % 4 == 2) {
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            //更新字段
            indexPartialHashCluSetHashcluster2Property(root, -127, 'a');
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    //查询
    for (int32_t i = startNum; i < endNum; i++) {
        FetchTimes = 0;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (i % 4 == 2) {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', -127);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(false, isFinish);
            //获取根节点和子节点
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t i = f0_value;
                if (i % 4 != 2) {
                    AW_MACRO_ASSERT_EQ_INT(1, 0);
                }
                indexPartialGetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
                indexPartialHashCluGetHashcluster2Property(root, -127, 'a');
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            AW_MACRO_ASSERT_EQ_INT(25, FetchTimes);
        } else {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', i);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(true, isFinish);
        }
    }
    //删除
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', -127);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestAffactRows(g_stmt, 25);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', -127);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    //重新插入原来的记录
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialSetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
        if (i % 4 == 2) {
            indexPartialHashCluSetHashcluster2Property(root, -127, 'a');
            // 插入vector节点
            indexPartialSetPropertyT1(T1, vectorNum);
            ret = GmcExecute(g_stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    FetchTimes = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', -127);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(false, isFinish);
    //获取根节点和子节点
    while (!isFinish) {
        FetchTimes++;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_ASSERT_EQ_INT(25, FetchTimes);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005.uint16类型字段=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_uint16.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    uint8_t *fixedValue = (uint8_t *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '1';
    int16_t int16Value1 = -5;
    int16_t int16Value2 = -100;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);

        if (i % 2 == 1) {
            uint32_t a = 200;
            indexPartialSetHashclusterProperty(root, charValue, i, a, fixedValue);
        } else {
            uint32_t a = i;
            indexPartialSetHashclusterProperty(root, charValue, i, a, fixedValue);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 2 == 1) {
            uint32_t a = 200;
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, true);
        } else {
            uint32_t a = i;
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, false);
        }
    }
    uint32_t updateV2 = 10;
    bool isNull = false;
    int64_t f0_value = 0;
    int FetchTimes = 0;
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 2 == 1) {
            uint32_t a = i;
            indexHashClusterTestPkUpdate(g_stmt, g_labelNameHashC1, g_PkName, i, charValue, fixedValue, i, updateV2, 1);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, updateV2, vectorNum, false);
        } else {
            uint32_t a = 200;
            indexHashClusterTestPkUpdate(g_stmt, g_labelNameHashC1, g_PkName, i, charValue, fixedValue, i, a, 1);
        }
    }
    //删除
    uint32_t a = 200;
    for (int32_t i = 0; i < endNum; i++) {
        if (i % 2 == 0) {
            indexHashClusterDelete(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, fixedValue, i, a, 1);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, false);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.int16类型字段！=多个（含负数）建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_int16.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    uint8_t *fixedValue = (uint8_t *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '1';
    int16_t int16Value1 = -5;
    int16_t int16Value2 = -100;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
        uint32_t a = i;
        if (i % 5 == 1) {
            indexPartialSetHashclusterProperty(root, charValue, int16Value1, a, fixedValue);
        } else if (i % 5 == 2) {
            indexPartialSetHashclusterProperty(root, charValue, int16Value2, a, fixedValue);
        } else if (i % 5 == 3) {
            indexPartialSetHashclusterProperty(root, charValue, 1, a, fixedValue);
        } else if (i % 5 == 4) {
            indexPartialSetHashclusterProperty(root, charValue, 2, a, fixedValue);
        } else {
            indexPartialSetHashclusterProperty(root, charValue, i, a, fixedValue);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t a = i;
        if (i % 5 == 1) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, int16Value1, a, vectorNum, false);
        } else if (i % 5 == 2) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, int16Value2, a, vectorNum, false);
        } else if (i % 5 == 3) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, 1, a, vectorNum, false);
        } else if (i % 5 == 4) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, 2, a, vectorNum, false);
        } else {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, true);
        }
    }
    int32_t updateV1 = 10;
    uint32_t updateV2 = 10;
    bool isNull = false;
    int64_t f0_value = 0;
    char updateChar = '2';
    int FetchTimes = 0;
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 5 == 2) {
            indexHashClusterTestPkUpdate(
                g_stmt, g_labelNameHashC1, g_PkName, i, updateChar, fixedValue, updateV1, updateV2, 1);
        }
    }
    //搜索
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, updateChar, updateV1, updateV2, fixedValue);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //获取根节点和子节点
    while (!isFinish) {
        FetchTimes++;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t i = f0_value;
        indexPartialGetHashclusterProperty(root, updateChar, updateV1, updateV2, fixedValue);
        indexPartialGetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(20, FetchTimes);
    FetchTimes = 0;
    //删除部分
    for (int32_t i = 0; i < endNum; i++) {
        uint32_t a = i;
        if (i % 5 == 0) {
            indexHashClusterDelete(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, fixedValue, i, a, 1);
        }
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, updateChar, updateV1, updateV2, fixedValue);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        FetchTimes++;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t i = f0_value;
        indexPartialGetHashclusterProperty(root, updateChar, updateV1, updateV2, fixedValue);
        indexPartialGetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(20, FetchTimes);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.uint32类型字段=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_uint32.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    uint8_t *fixedValue = (uint8_t *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '1';

    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);

        if (i % 2 == 1) {
            uint32_t a = 200;
            indexPartialSetHashclusterProperty(root, charValue, i, a, fixedValue);
        } else {
            uint32_t a = i;
            indexPartialSetHashclusterProperty(root, charValue, i, a, fixedValue);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 2 == 1) {
            uint32_t a = 200;
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, true);
        } else {
            uint32_t a = i;
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, false);
        }
    }
    uint32_t updateV2 = 10;
    bool isNull = false;
    int64_t f0_value = 0;
    int FetchTimes = 0;
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 2 == 1) {
            uint32_t a = i;
            indexHashClusterTestPkUpdate(g_stmt, g_labelNameHashC1, g_PkName, i, charValue, fixedValue, i, updateV2, 1);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, updateV2, vectorNum, false);
        } else {
            uint32_t a = 200;
            indexHashClusterTestPkUpdate(g_stmt, g_labelNameHashC1, g_PkName, i, charValue, fixedValue, i, a, 1);
        }
    }
    //删除
    uint32_t a = 200;
    for (int32_t i = 0; i < endNum; i++) {
        if (i % 2 == 0) {
            indexHashClusterDelete(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, fixedValue, i, a, 1);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, false);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.int32类型字段!=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_int32.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    uint8_t *fixedValue = (uint8_t *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '1';
    int16_t int16Value1 = -5;
    int16_t int16Value2 = -100;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
        uint32_t a = i;
        if (i % 5 == 1) {
            indexPartialSetHashclusterProperty(root, charValue, int16Value1, a, fixedValue);
        } else if (i % 5 == 2) {
            indexPartialSetHashclusterProperty(root, charValue, int16Value2, a, fixedValue);
        } else if (i % 5 == 3) {
            indexPartialSetHashclusterProperty(root, charValue, 1, a, fixedValue);
        } else if (i % 5 == 4) {
            indexPartialSetHashclusterProperty(root, charValue, 2, a, fixedValue);
        } else {
            indexPartialSetHashclusterProperty(root, charValue, i, a, fixedValue);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t a = i;
        if (i % 5 == 1) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, int16Value1, a, vectorNum, false);
        } else if (i % 5 == 2) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, int16Value2, a, vectorNum, false);
        } else if (i % 5 == 3) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, 1, a, vectorNum, false);
        } else if (i % 5 == 4) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, 2, a, vectorNum, false);
        } else {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, true);
        }
    }
    int32_t updateV1 = 100;
    uint32_t updateV2 = 100;
    bool isNull = false;
    int64_t f0_value = 0;
    int FetchTimes = 0;
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 5 == 2) {
            indexHashClusterTestPkUpdate(
                g_stmt, g_labelNameHashC1, g_PkName, i, charValue, fixedValue, updateV1, updateV2, 1);
        }
    }
    //搜索
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, updateV1, updateV2, fixedValue);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //获取根节点和子节点
    while (!isFinish) {
        FetchTimes++;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t i = f0_value;
        indexPartialGetHashclusterProperty(root, charValue, updateV1, updateV2, fixedValue);
        indexPartialGetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(20, FetchTimes);
    FetchTimes = 0;
    //删除部分
    for (int32_t i = 0; i < endNum; i++) {
        uint32_t a = i;
        if (i % 5 == 0) {
            indexHashClusterDelete(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, fixedValue, i, a, 1);
        }
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, updateV1, updateV2, fixedValue);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        FetchTimes++;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t i = f0_value;
        indexPartialGetHashclusterProperty(root, charValue, updateV1, updateV2, fixedValue);
        indexPartialGetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(20, FetchTimes);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.uint64类型字段=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_uint64.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    uint8_t *fixedValue = (uint8_t *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    uint32_t FetchTimes = 0;
    bool isNull = false;

    //插入数据
    GmcNodeT *root = NULL, *T1 = NULL;
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialSetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
        if (i % 4 == 1) {
            indexPartialHashCluSetHashcluster2Property(root, 1000, 'a');
        } else {
            indexPartialHashCluSetHashcluster2Property(root, i, 'a');
        }
        // 插入vector节点
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    //主键fetch
    for (int i = startNum; i < endNum; i++) {
        int64_t f0_value = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexPartialSetPkIndex(g_stmt, g_PkName, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //获取根节点和子节点
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialGetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
        if (i % 4 == 1) {
            indexPartialHashCluGetHashcluster2Property(root, 1000, 'a');
        } else {
            indexPartialHashCluGetHashcluster2Property(root, i, 'a');
        }
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(true, isFinish);
    }
    //索引查
    for (int i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (i % 4 != 1) {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', i);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(true, isFinish);
        } else {
            FetchTimes = 0;
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', 1000);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(false, isFinish);
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t j = f0_value;
                if (j % 4 != 1) {
                    AW_MACRO_ASSERT_EQ_INT(1, 0);
                }
                indexPartialGetPropertyRoot2(root, j, boolValue, strValue, fixedValue, bytesValue);
                indexPartialHashCluGetHashcluster2Property(root, 1000, 'a');
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            AW_MACRO_ASSERT_EQ_INT(25, FetchTimes);
        }
    }
    //主键更新
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 1) {
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            //更新字段
            indexPartialHashCluSetHashcluster2Property(root, i, 'a');
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else if (i % 4 == 2) {
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            //更新字段
            indexPartialHashCluSetHashcluster2Property(root, 1000, 'a');
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    //查询
    for (int32_t i = startNum; i < endNum; i++) {
        FetchTimes = 0;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (i % 4 == 2) {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', 1000);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(false, isFinish);
            //获取根节点和子节点
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t i = f0_value;
                if (i % 4 != 2) {
                    AW_MACRO_ASSERT_EQ_INT(1, 0);
                }
                indexPartialGetPropertyRoot2(root, i, boolValue, strValue, fixedValue, bytesValue);
                indexPartialHashCluGetHashcluster2Property(root, 1000, 'a');
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            AW_MACRO_ASSERT_EQ_INT(25, FetchTimes);
        } else {
            indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', i);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(true, isFinish);
        }
    }
    //删除
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', 1000);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestAffactRows(g_stmt, 25);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //查询
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex2(g_stmt, g_HashclusterName1, 'a', 1000);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(true, isFinish);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010.int64类型字段!=建partial索引（含负数），插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_int64.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    uint8_t *fixedValue = (uint8_t *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '1';
    int16_t int16Value1 = -5;
    int16_t int16Value2 = -100;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
        uint32_t a = i;
        if (i % 5 == 1) {
            indexPartialSetHashclusterProperty(root, charValue, int16Value1, a, fixedValue);
        } else if (i % 5 == 2) {
            indexPartialSetHashclusterProperty(root, charValue, int16Value2, a, fixedValue);
        } else if (i % 5 == 3) {
            indexPartialSetHashclusterProperty(root, charValue, 1, a, fixedValue);
        } else if (i % 5 == 4) {
            indexPartialSetHashclusterProperty(root, charValue, 2, a, fixedValue);
        } else {
            indexPartialSetHashclusterProperty(root, charValue, i, a, fixedValue);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t a = i;
        if (i % 5 == 1) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, int16Value1, a, vectorNum, false);
        } else if (i % 5 == 2) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, int16Value2, a, vectorNum, false);
        } else if (i % 5 == 3) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, 1, a, vectorNum, false);
        } else if (i % 5 == 4) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, 2, a, vectorNum, false);
        } else {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, true);
        }
    }
    int32_t updateV1 = 100;
    uint32_t updateV2 = 100;
    bool isNull = false;
    int64_t f0_value = 0;
    int FetchTimes = 0;
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 5 == 2) {
            indexHashClusterTestPkUpdate(
                g_stmt, g_labelNameHashC1, g_PkName, i, charValue, fixedValue, updateV1, updateV2, 1);
        }
    }
    //搜索
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, updateV1, updateV2, fixedValue);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //获取根节点和子节点
    while (!isFinish) {
        FetchTimes++;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t i = f0_value;
        indexPartialGetHashclusterProperty(root, charValue, updateV1, updateV2, fixedValue);
        indexPartialGetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(20, FetchTimes);
    FetchTimes = 0;
    //删除部分
    for (int32_t i = 0; i < endNum; i++) {
        uint32_t a = i;
        if (i % 5 == 0) {
            indexHashClusterDelete(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, fixedValue, i, a, 1);
        }
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, updateV1, updateV2, fixedValue);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (!isFinish) {
        FetchTimes++;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t i = f0_value;
        indexPartialGetHashclusterProperty(root, charValue, updateV1, updateV2, fixedValue);
        indexPartialGetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(20, FetchTimes);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.fixed
// 类型字段!=建partial索引(含16进制值与字符串值至少2个条件)，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster查询hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_fixed.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    char *fixedValue = (char *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '1';
    int32_t intValue = -1;
    uint32_t uintValue = 100;
    uint8_t fixed1[INDEX_FIXED_FIELD_LENGTH] = {0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17};
    char *fixed2 = (char *)"gabcdef";
    char *fixed3 = (char *)"0123456";
    int FetchTimes = 0;
    bool isNull = false;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        if (i % 4 == 1) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, intValue, uintValue, fixed1);
        } else if (i % 4 == 2) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed2, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, intValue, uintValue, fixed2);
        } else if (i % 4 == 3) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed3, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, intValue, uintValue, fixed3);
        } else {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, intValue, uintValue, fixedValue);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 1) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue, fixed1,
                bytesValue, intValue, uintValue, vectorNum, false);
        } else if (i % 4 == 2) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue, fixed2,
                bytesValue, intValue, uintValue, vectorNum, false);
        } else if (i % 4 == 3) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue, fixed3,
                bytesValue, intValue, uintValue, vectorNum, false);
        } else {
            FetchTimes = 0;
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, intValue, uintValue, fixedValue);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            //获取根节点和子节点
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t i = f0_value;
                indexPartialGetHashclusterProperty(root, charValue, intValue, uintValue, fixedValue);
                indexPartialGetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            AW_MACRO_ASSERT_EQ_INT(25, FetchTimes);
        }
    }
    FetchTimes = 0;
    int64_t f0_value = 0;
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 2) {
            indexHashClusterTestPkUpdate(
                g_stmt, g_labelNameHashC1, g_PkName, i, charValue, fixedValue, intValue, uintValue, 1);
        }
    }
    //搜索
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, intValue, uintValue, fixedValue);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //获取根节点和子节点
    while (!isFinish) {
        FetchTimes++;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t i = f0_value;
        indexPartialGetHashclusterProperty(root, charValue, intValue, uintValue, fixedValue);
        if (i % 4 == 2) {
            indexPartialGetPropertyRoot1(root, i, boolValue, strValue, fixed2, bytesValue);
        } else {
            indexPartialGetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
        }
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_ASSERT_EQ_INT(50, FetchTimes);
    FetchTimes = 0;
    //删除
    indexHashClusterDelete(
        g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, fixedValue, intValue, uintValue, 50);
    indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue, fixedValue,
        bytesValue, intValue, uintValue, vectorNum, false);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.time类型字段=建partial索引，插入多条记录并hashcluster查询/主键更新部分记录/hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_time.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    uint8_t *fixedValue = (uint8_t *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '1';

    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);

        if (i % 2 == 1) {
            uint32_t a = 200;
            indexPartialSetHashclusterProperty(root, charValue, i, a, fixedValue);
        } else {
            uint32_t a = i;
            indexPartialSetHashclusterProperty(root, charValue, i, a, fixedValue);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 2 == 1) {
            uint32_t a = 200;
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, true);
        } else {
            uint32_t a = i;
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, false);
        }
    }
    uint32_t updateV2 = 100;
    bool isNull = false;
    int64_t f0_value = 0;
    int FetchTimes = 0;
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 2 == 1) {
            uint32_t a = i;
            indexHashClusterTestPkUpdate(g_stmt, g_labelNameHashC1, g_PkName, i, charValue, fixedValue, i, updateV2, 1);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, updateV2, vectorNum, false);
        } else {
            uint32_t a = 200;
            indexHashClusterTestPkUpdate(g_stmt, g_labelNameHashC1, g_PkName, i, charValue, fixedValue, i, a, 1);
        }
    }
    //删除
    uint32_t a = 200;
    for (int32_t i = 0; i < endNum; i++) {
        if (i % 2 == 0) {
            indexHashClusterDelete(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, fixedValue, i, a, 1);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, i, a, vectorNum, false);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.多个字段不连续且乱序满16个条件建partial索引，插入多条记录并hashcluster查询/hashcluster更新成不满足filter记录并hashcluster查询/插入新值/hashcluster删除记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_multi16.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    char *fixedValue = (char *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '=';
    int32_t intValue = -5;
    int32_t intValue2 = -100;
    uint8_t fixed1[INDEX_FIXED_FIELD_LENGTH] = {0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17};
    char *fixed2 = (char *)"testfix";
    char *fixed3 = (char *)"0123456";
    int FetchTimes = 0;
    bool isNull = false;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, intValue, uintValue, fixed1);
        } else if (i % 4 == 2) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, intValue2, uintValue, fixedValue);
        } else if (i % 4 == 3) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed3, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed3);
        } else {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
            indexPartialSetHashclusterProperty(root, '0', i, uintValue, fixedValue);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue, fixed1,
                bytesValue, intValue, uintValue, vectorNum, false);
        } else if (i % 4 == 2) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, intValue2, uintValue, vectorNum, false);
        } else if (i % 4 == 0) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '0', boolValue, strValue, fixedValue,
                bytesValue, i, uintValue, vectorNum, false);
        } else {
            FetchTimes = 0;
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, i, 200, fixed3);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            //获取根节点和子节点
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t j = f0_value;
                if (j % 4 != 3) {
                    AW_MACRO_ASSERT_EQ_INT(1, 0);
                }
                indexPartialGetHashclusterProperty(root, charValue, j, 200, fixed3);
                indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed3, bytesValue);
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            AW_MACRO_ASSERT_EQ_INT(1, FetchTimes);
        }
    }

    // hashcluster更新部分
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 3) {
            if (i % 5 == 0) {
                ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_UPDATE);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, i, 200, fixed3);
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                indexPartialSetHashclusterProperty(root, 'a', i, 200, fixedValue);
                ret = GmcExecute(g_stmt);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = TestAffactRows(g_stmt, 1);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        }
    }
    //搜索
    for (int32_t i = startNum; i < endNum; i++) {
        FetchTimes = 0;
        if (i % 4 == 3) {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (i % 5 == 0) {
                indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, 'a', i, 200, fixedValue);
                ret = GmcExecute(g_stmt);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT(true, isFinish);
            } else {
                indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, i, 200, fixed3);
                ret = GmcExecute(g_stmt);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                //获取根节点和子节点
                while (!isFinish) {
                    FetchTimes++;
                    int64_t f0_value = 0;
                    indexSupportPartialGetNode(g_stmt, &root, &T1);
                    ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                    int32_t j = f0_value;
                    if (j % 4 != 3) {
                        AW_MACRO_ASSERT_EQ_INT(1, 0);
                    }
                    indexPartialGetHashclusterProperty(root, charValue, j, 200, fixed3);
                    indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed3, bytesValue);
                    // 读取vector节点
                    indexPartialGetPropertyT1(T1, vectorNum);
                    ret = GmcFetch(g_stmt, &isFinish);
                    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                }
                AW_MACRO_ASSERT_EQ_INT(1, FetchTimes);
            }
        }
    }
    //删除
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 3) {
            if (i % 5 != 0) {
                indexHashClusterDelete(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, fixed3, i, 200, 1);
                indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                    fixed3, bytesValue, i, 200, vectorNum, false);
            }
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.唯一索引，已存在满足filter的记录，将不满足filter的记录主键更新/merge/replace为满足filter的记录并hashcluster查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char *schema_path = (char *)"./schemaFile/indexHashcluster_unique.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    char *fixedValue = (char *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '=';
    int32_t intValue = -5;
    int32_t intValue2 = -100;
    uint8_t fixed1[INDEX_FIXED_FIELD_LENGTH] = {0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17};
    int FetchTimes = 0;
    bool isNull = false;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed1);
        } else {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, '0', i, uintValue, fixed1);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    //更新：索引冲突
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 2)  //主键更新
        {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_UPDATE);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            //更新字段
            indexPartialSetHashclusterProperty(root, charValue, 1, 200, fixed1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_UNIQUE_VIOLATION, ret);
            if (ret != GMERR_UNIQUE_VIOLATION) {
                AW_FUN_Log(LOG_DEBUG, "i = %d\n", i);
            }
            ret = TestAffactRows(g_stmt, 0);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else if (i % 4 == 3)  // merge
        {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_MERGE);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            //更新字段
            indexPartialSetHashclusterProperty(root, charValue, 5, 200, fixed1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_UNIQUE_VIOLATION, ret);
            if (ret != GMERR_UNIQUE_VIOLATION) {
                AW_FUN_Log(LOG_DEBUG, "i = %d\n", i);
            }
            ret = TestAffactRows(g_stmt, 0);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else if (i % 4 == 0)  // replace
        {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_REPLACE);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, 9, 200, fixed1);
            indexPartialSetPropertyT1(T1, vectorNum);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_UNIQUE_VIOLATION, ret);
            if (ret != GMERR_UNIQUE_VIOLATION) {
                AW_FUN_Log(LOG_DEBUG, "i = %d\n", i);
            }
            ret = TestAffactRows(g_stmt, 0);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    FetchTimes = 0;
    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 != 1) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '0', boolValue, strValue, fixed1,
                bytesValue, i, uintValue, vectorNum, false);
        } else {
                ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, i, 200, fixed1);
                ret = GmcExecute(g_stmt);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                //获取根节点和子节点
                while (!isFinish) {
                    FetchTimes++;
                    int64_t f0_value = 0;
                    indexSupportPartialGetNode(g_stmt, &root, &T1);
                    ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                    int32_t j = f0_value;
                    if (j % 4 != 1) {
                        AW_MACRO_ASSERT_EQ_INT(1, 0);
                    }
                    indexPartialGetHashclusterProperty(root, charValue, j, 200, fixed1);
                    indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed1, bytesValue);
                    // 读取vector节点
                    indexPartialGetPropertyT1(T1, vectorNum);
                    ret = GmcFetch(g_stmt, &isFinish);
                    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                }
        }
    }
    AW_MACRO_ASSERT_EQ_INT(25, FetchTimes);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015.多个字段多个条件将不满足filter的记录replace成满足filter的记录并hashcluster查询和主键查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_replace.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    char *fixedValue = (char *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '=';
    int32_t intValue = -5;
    int32_t intValue2 = -100;
    uint8_t fixed1[INDEX_FIXED_FIELD_LENGTH] = {0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17};
    int FetchTimes = 0;
    bool isNull = false;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed1);
        } else {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, '0', i, uintValue, fixed1);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // replace
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 0)  // replace
        {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_REPLACE);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed1);
            indexPartialSetPropertyT1(T1, vectorNum);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    FetchTimes = 0;
    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 == 2 || i % 4 == 3) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '0', boolValue, strValue, fixed1,
                bytesValue, i, uintValue, vectorNum, false);
        } else {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, i, 200, fixed1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            //获取根节点和子节点
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t j = f0_value;
                if ((j % 4 != 1) && (j % 4 != 0)) {
                    AW_MACRO_ASSERT_EQ_INT(1, 0);
                }
                indexPartialGetHashclusterProperty(root, charValue, j, 200, fixed1);
                indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed1, bytesValue);
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
        }
    }
    AW_MACRO_ASSERT_EQ_INT(50, FetchTimes);
    //主键查询
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexPartialSetPkIndex(g_stmt, g_PkName, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(false, isFinish);
        int64_t f0_value = 0;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t j = f0_value;
        uint32_t uintValue = j;
        if ((j % 4 == 1) || (j % 4 == 0)) {
            indexPartialGetHashclusterProperty(root, charValue, j, 200, fixed1);
        } else {
            indexPartialGetHashclusterProperty(root, '0', j, uintValue, fixed1);
        }
        indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed1, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.多个字段多个条件将不满足filter的记录merge成满足filter的记录并hashcluster查询和主键查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_replace.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    char *fixedValue = (char *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '=';
    int32_t intValue = -5;
    int32_t intValue2 = -100;
    uint8_t fixed1[INDEX_FIXED_FIELD_LENGTH] = {0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17};
    int FetchTimes = 0;
    bool isNull = false;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed1);
        } else {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, '0', i, uintValue, fixed1);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // merge
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 0) {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_MERGE);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    FetchTimes = 0;
    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 == 2 || i % 4 == 3) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '0', boolValue, strValue, fixed1,
                bytesValue, i, uintValue, vectorNum, false);
        } else {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, i, 200, fixed1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            //获取根节点和子节点
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t j = f0_value;
                if ((j % 4 != 1) && (j % 4 != 0)) {
                    AW_MACRO_ASSERT_EQ_INT(1, 0);
                }
                indexPartialGetHashclusterProperty(root, charValue, j, 200, fixed1);
                indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed1, bytesValue);
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
        }
    }
    AW_MACRO_ASSERT_EQ_INT(50, FetchTimes);
    //主键查询
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexPartialSetPkIndex(g_stmt, g_PkName, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(false, isFinish);
        int64_t f0_value = 0;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t j = f0_value;
        uint32_t uintValue = j;
        if ((j % 4 == 1) || (j % 4 == 0)) {
            indexPartialGetHashclusterProperty(root, charValue, j, 200, fixed1);
        } else {
            indexPartialGetHashclusterProperty(root, '0', j, uintValue, fixed1);
        }
        indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed1, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.多个字段多个条件将不满足filter的记录主键更新成满足filter的记录并hashcluster查询和主键查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_replace.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    char *fixedValue = (char *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '=';
    int32_t intValue = -5;
    int32_t intValue2 = -100;
    uint8_t fixed1[INDEX_FIXED_FIELD_LENGTH] = {0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17};
    int FetchTimes = 0;
    bool isNull = false;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed1);
        } else {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, '0', i, uintValue, fixed1);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // update
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 0) {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_UPDATE);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    FetchTimes = 0;
    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 == 2 || i % 4 == 3) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '0', boolValue, strValue, fixed1,
                bytesValue, i, uintValue, vectorNum, false);
        } else {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, i, 200, fixed1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            //获取根节点和子节点
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t j = f0_value;
                if ((j % 4 != 1) && (j % 4 != 0)) {
                    AW_MACRO_ASSERT_EQ_INT(1, 0);
                }
                indexPartialGetHashclusterProperty(root, charValue, j, 200, fixed1);
                indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed1, bytesValue);
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
        }
    }
    AW_MACRO_ASSERT_EQ_INT(50, FetchTimes);
    //主键查询
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexPartialSetPkIndex(g_stmt, g_PkName, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(false, isFinish);
        int64_t f0_value = 0;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t j = f0_value;
        uint32_t uintValue = j;
        if ((j % 4 == 1) || (j % 4 == 0)) {
            indexPartialGetHashclusterProperty(root, charValue, j, 200, fixed1);
        } else {
            indexPartialGetHashclusterProperty(root, '0', j, uintValue, fixed1);
        }
        indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed1, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.多个字段多个条件将满足filter的记录merge成不满足filter的记录并hashcluster查询和主键查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_replace.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    char *fixedValue = (char *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '=';
    int32_t intValue = -5;
    int32_t intValue2 = -100;
    uint8_t fixed1[INDEX_FIXED_FIELD_LENGTH] = {0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17};
    int FetchTimes = 0;
    bool isNull = false;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed1);
        } else {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, '0', i, uintValue, fixed1);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // merge
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_MERGE);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            indexPartialSetHashclusterProperty(root, charValue, i, uintValue, fixed1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue, fixed1,
                bytesValue, i, uintValue, vectorNum, false);
        } else {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '0', boolValue, strValue, fixed1,
                bytesValue, i, uintValue, vectorNum, false);
        }
    }
    //主键查询
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexPartialSetPkIndex(g_stmt, g_PkName, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(false, isFinish);
        int64_t f0_value = 0;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t j = f0_value;
        uint32_t uintValue = j;
        if (j % 4 == 1) {
            indexPartialGetHashclusterProperty(root, charValue, j, uintValue, fixed1);
        } else {
            indexPartialGetHashclusterProperty(root, '0', j, uintValue, fixed1);
        }
        indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed1, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.多个字段多个条件将满足filter的记录replace成不满足filter的记录并hashcluster查询和主键查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_replace.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    char *fixedValue = (char *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '=';
    int32_t intValue = -5;
    int32_t intValue2 = -100;
    uint8_t fixed1[INDEX_FIXED_FIELD_LENGTH] = {0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17};
    int FetchTimes = 0;
    bool isNull = false;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed1);
        } else {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, '0', i, uintValue, fixed1);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // replace
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_REPLACE);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, i, uintValue, fixed1);
            indexPartialSetPropertyT1(T1, vectorNum);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 2);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue, fixed1,
                bytesValue, i, uintValue, vectorNum, false);
        } else {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '0', boolValue, strValue, fixed1,
                bytesValue, i, uintValue, vectorNum, false);
        }
    }
    //主键查询
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexPartialSetPkIndex(g_stmt, g_PkName, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(false, isFinish);
        int64_t f0_value = 0;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t j = f0_value;
        uint32_t uintValue = j;
        if (j % 4 == 1) {
            indexPartialGetHashclusterProperty(root, charValue, j, uintValue, fixed1);
        } else {
            indexPartialGetHashclusterProperty(root, '0', j, uintValue, fixed1);
        }
        indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed1, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.多个字段多个条件将满足filter的记录主键更新成不满足filter的记录并hashcluster查询和主键查询
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_replace.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    char *fixedValue = (char *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '=';
    int32_t intValue = -5;
    int32_t intValue2 = -100;
    uint8_t fixed1[INDEX_FIXED_FIELD_LENGTH] = {0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17};
    int FetchTimes = 0;
    bool isNull = false;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed1);
        } else {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, '0', i, uintValue, fixed1);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // update
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_UPDATE);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetPkIndex(g_stmt, g_PkName, i);
            indexSupportPartialGetNode(g_stmt, &root, &T1);
            indexPartialSetHashclusterProperty(root, charValue, i, uintValue, fixed1);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue, fixed1,
                bytesValue, i, uintValue, vectorNum, false);
        } else {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '0', boolValue, strValue, fixed1,
                bytesValue, i, uintValue, vectorNum, false);
        }
    }
    //主键查询
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexPartialSetPkIndex(g_stmt, g_PkName, i);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(false, isFinish);
        int64_t f0_value = 0;
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
        int32_t j = f0_value;
        uint32_t uintValue = j;
        if (j % 4 == 1) {
            indexPartialGetHashclusterProperty(root, charValue, j, uintValue, fixed1);
        } else {
            indexPartialGetHashclusterProperty(root, '0', j, uintValue, fixed1);
        }
        indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed1, bytesValue);
        // 读取vector节点
        indexPartialGetPropertyT1(T1, vectorNum);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

//索引交互测试
TEST_F(indexPartialHashCluster, HardWare_Offloading_001_DML_084_002_002_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_path = (char *)"./schemaFile/indexHashcluster_mix.gmjson";
    int ret = indexSupportPartialCreateLabel(schema_path, g_labelNameHashC1, g_labelConfig);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int32_t startNum = 0;
    int32_t endNum = 100;
    bool boolValue = false;
    char *strValue = (char *)"indexPartial";
    char *bytesValue = (char *)"1234567";
    char *fixedValue = (char *)"fixedT2";
    int32_t vectorNum = 3;
    bool isFinish = false;
    GmcNodeT *root = NULL, *T1 = NULL;
    char charValue = '=';
    int32_t intValue = -5;
    int32_t intValue2 = -100;
    uint8_t fixed1[INDEX_FIXED_FIELD_LENGTH] = {0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17};
    char *fixed2 = (char *)"testfix";
    char *fixed3 = (char *)"0123456";
    int FetchTimes = 0;
    bool isNull = false;
    //插入数据
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexSupportPartialGetNode(g_stmt, &root, &T1);
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed1, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, intValue, uintValue, fixed1);
        } else if (i % 4 == 2) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, intValue2, uintValue, fixedValue);
        } else if (i % 4 == 3) {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixed3, bytesValue);
            indexPartialSetHashclusterProperty(root, charValue, i, 200, fixed3);
        } else {
            indexPartialSetPropertyRoot1(root, i, boolValue, strValue, fixedValue, bytesValue);
            indexPartialSetHashclusterProperty(root, '0', i, uintValue, fixedValue);
        }
        indexPartialSetPropertyT1(T1, vectorNum);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //索引查
    for (int32_t i = startNum; i < endNum; i++) {
        uint32_t uintValue = i;
        if (i % 4 == 1) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue, fixed1,
                bytesValue, intValue, uintValue, vectorNum, false);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, (char *)"localhash_key", charValue, boolValue, strValue,
                fixed1, bytesValue, intValue, uintValue, vectorNum, false);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, (char *)"local_key", charValue, boolValue, strValue, fixed1,
                bytesValue, intValue, uintValue, vectorNum, false);

        } else if (i % 4 == 2) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                fixedValue, bytesValue, intValue2, uintValue, vectorNum, false);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, (char *)"localhash_key", charValue, boolValue, strValue,
                fixedValue, bytesValue, intValue2, uintValue, vectorNum, false);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, (char *)"local_key", charValue, boolValue, strValue,
                fixedValue, bytesValue, intValue2, uintValue, vectorNum, false);

        } else if (i % 4 == 0) {
            indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, '0', boolValue, strValue, fixedValue,
                bytesValue, i, uintValue, vectorNum, false);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, (char *)"localhash_key", '0', boolValue, strValue,
                fixedValue, bytesValue, i, uintValue, vectorNum, false);
            indexHashClusterScan(g_stmt, g_labelNameHashC1, (char *)"local_key", '0', boolValue, strValue, fixedValue,
                bytesValue, i, uintValue, vectorNum, false);

        } else {
            FetchTimes = 0;
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, i, 200, fixed3);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            //获取根节点和子节点
            while (!isFinish) {
                FetchTimes++;
                int64_t f0_value = 0;
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                int32_t j = f0_value;
                if (j % 4 != 3) {
                    AW_MACRO_ASSERT_EQ_INT(1, 0);
                }
                indexPartialGetHashclusterProperty(root, charValue, j, 200, fixed3);
                indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed3, bytesValue);
                // 读取vector节点
                indexPartialGetPropertyT1(T1, vectorNum);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            AW_MACRO_ASSERT_EQ_INT(1, FetchTimes);
        }
    }

    // hashcluster更新部分
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 3) {
            if (i % 5 == 0) {
                ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_UPDATE);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                indexPartialSetHashclusterIndex(g_stmt, g_HashclusterName1, charValue, i, 200, fixed3);
                indexSupportPartialGetNode(g_stmt, &root, &T1);
                indexPartialSetHashclusterProperty(root, 'a', i, 200, fixedValue);
                ret = GmcExecute(g_stmt);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = TestAffactRows(g_stmt, 1);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        }
    }
    //搜索
    for (int32_t i = startNum; i < endNum; i++) {
        FetchTimes = 0;
        if (i % 4 == 3) {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelNameHashC1, GMC_OPERATION_SCAN);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            if (i % 5 == 0) {
                indexPartialSetHashclusterIndex(g_stmt, (char *)"localhash_key", 'a', i, 200, fixedValue);
                ret = GmcExecute(g_stmt);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                AW_MACRO_ASSERT_EQ_INT(true, isFinish);
            } else {
                indexPartialSetHashclusterIndex(g_stmt, (char *)"local_key", charValue, i, 200, fixed3);
                ret = GmcExecute(g_stmt);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = GmcFetch(g_stmt, &isFinish);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                //获取根节点和子节点
                while (!isFinish) {
                    FetchTimes++;
                    int64_t f0_value = 0;
                    indexSupportPartialGetNode(g_stmt, &root, &T1);
                    ret = GmcNodeGetPropertyByName(root, (char *)"F0", &f0_value, sizeof(int64_t), &isNull);
                    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                    AW_MACRO_ASSERT_EQ_INT((unsigned int)0, isNull);
                    int32_t j = f0_value;
                    if (j % 4 != 3) {
                        AW_MACRO_ASSERT_EQ_INT(1, 0);
                    }
                    indexPartialGetHashclusterProperty(root, charValue, j, 200, fixed3);
                    indexPartialGetPropertyRoot1(root, j, boolValue, strValue, fixed3, bytesValue);
                    // 读取vector节点
                    indexPartialGetPropertyT1(T1, vectorNum);
                    ret = GmcFetch(g_stmt, &isFinish);
                    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                }
                AW_MACRO_ASSERT_EQ_INT(1, FetchTimes);
            }
        }
    }
    //删除
    for (int32_t i = startNum; i < endNum; i++) {
        if (i % 4 == 3) {
            if (i % 5 != 0) {
                indexHashClusterDelete(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, fixed3, i, 200, 1);
                indexHashClusterScan(g_stmt, g_labelNameHashC1, g_HashclusterName1, charValue, boolValue, strValue,
                    fixed3, bytesValue, i, 200, vectorNum, false);
                indexHashClusterScan(g_stmt, g_labelNameHashC1, (char *)"local_key", charValue, boolValue, strValue,
                    fixed3, bytesValue, i, 200, vectorNum, false);
                indexHashClusterScan(g_stmt, g_labelNameHashC1, (char *)"localhash_key", charValue, boolValue, strValue,
                    fixed3, bytesValue, i, 200, vectorNum, false);
            }
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
