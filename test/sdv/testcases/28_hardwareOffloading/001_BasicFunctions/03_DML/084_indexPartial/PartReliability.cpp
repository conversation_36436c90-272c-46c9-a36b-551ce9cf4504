extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/prctl.h>
#include <sys/socket.h>
#include <sys/epoll.h>
#include <assert.h>
#include <errno.h>
#include <stdint.h>
#include <typeinfo>
#include <limits.h>
#include "gtest/gtest.h"
#include "jansson.h"
#include "t_datacom_lite.h"
#include "../../common/hash_util.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

#define FIXED_LEN 7

// MS config
const char *MS_config = "{\"max_record_count\" : 20000}";

// Vertex Name
const char *Vertex_Name = "IndexPartialRel";
const char *Vertex_KeyName = "IndexPartialRel_pk";
const char *Vertex_IndexName = "IndexPartialRel_index";

uint32_t IndexLocal = 1;
uint32_t IndexHashcluster = 2;
uint32_t IndexLocalhash = 3;
pthread_barrier_t barrier1;
int32_t startInsertNum = 1000;

/**************************公共函数***********************************/
void testCreateLabel(GmcStmtT *stmt, const char *schema_path, const char *labelName)
{
    int ret = 0;
    char *VLabel_schema = NULL;
    ASSERT_NE((void *)NULL, schema_path);
    ret = GmcDropVertexLabel(stmt, labelName);
    readJanssonFile(schema_path, &VLabel_schema);
    ASSERT_NE((void *)NULL, VLabel_schema);
    ret = GmcCreateVertexLabel(stmt, VLabel_schema, MS_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(VLabel_schema);
}

void testDropLabel(GmcStmtT *stmt, const char *LabelName)
{
    int ret = 0;

    ret = GmcDropVertexLabel(stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void testGmcGetStmtAttr(GmcConnT *conn, GmcStmtT *stmt, int expectAffectRows, uint32_t CycleNum)
{
    int affectRows = 0;
    int ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    if (ret == GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expectAffectRows, affectRows);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("GmcGetStmtAttr Fail, CycleNum = %d.\n", CycleNum);
    }
}

void TestGmcSetVertexProperty_PK(GmcStmtT *stmt, int32_t i)
{
    int ret = 0;
    uint32_t PK_value = (uint32_t)i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &PK_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetVertexProperty(GmcStmtT *stmt, int32_t i)
{
    int ret = 0;
    int8_t value = i % 10;

    unsigned char f1_value = value;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f1_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f2_value = value;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_CHAR, &f2_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f3_value = value;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f4_value = value;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT8, &f4_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = value;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f6_value = value;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT16, &f6_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f7_value = value;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &f7_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f8_value = value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_INT32, &f8_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f9_value = value;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_UINT64, &f9_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f10_value = value;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_INT64, &f10_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f11_value[FIXED_LEN + 1] = {0};
    snprintf(f11_value, FIXED_LEN + 1, "string%1d", value);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FIXED, f11_value, FIXED_LEN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f12_value = value;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_TIME, &f12_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetVertexPropertyUpdate(GmcStmtT *stmt, int32_t i)
{
    int ret = 0;
    int8_t value = i % 5;

    unsigned char f1_value = value;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &f1_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f2_value = value;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_CHAR, &f2_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint8_t f3_value = value;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int8_t f4_value = value;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT8, &f4_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint16_t f5_value = value;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int16_t f6_value = value;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT16, &f6_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t f7_value = value;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &f7_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t f8_value = value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_INT32, &f8_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f9_value = value;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_UINT64, &f9_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int64_t f10_value = value;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_INT64, &f10_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char f11_value[FIXED_LEN + 1] = {0};
    snprintf(f11_value, FIXED_LEN + 1, "string%1d", value);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FIXED, f11_value, FIXED_LEN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t f12_value = value;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_TIME, &f12_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// Insert
void testInsertVertexLabel(GmcConnT *conn, GmcStmtT *stmt, uint32_t times, int32_t initValue, const char *labelname)
{
    int ret = 0;
    int32_t i = 0;
    int32_t value = 0;

    ret = testGmcPrepareStmtByLabelName(stmt, labelname, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        //写数据
        value = initValue + i;

        TestGmcSetVertexProperty_PK(stmt, value);
        TestGmcSetVertexProperty(stmt, value);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        testGmcGetStmtAttr(conn, stmt, 1, i);
    }
}

class IndexPartialRel_test : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void IndexPartialRel_test::SetUpTestCase()
{
    int ret = 0;
    // 配置相关环境变量及重启server
    InitCfg();
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void IndexPartialRel_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复配置文件
    RecoverCfg();
}

void IndexPartialRel_test::SetUp()
{
    int ret = 0;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void IndexPartialRel_test::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    testGmcDisconnect(g_conn, g_stmt);
    g_conn = NULL;
    g_stmt = NULL;
}

/********************************线程函数*******************************************/
void *Thread_Insert(void *args)
{
    int ret = 0;
    uint32_t initValue = startInsertNum;
    uint32_t times = 200;
    uint32_t SuccNum = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char Index[20] = {0};
    int affectRows = 0;
    uint32_t IndexType = *((uint32_t *)args);  //索引类型
    if (IndexType == IndexLocal) {
        memcpy(Index, "local", sizeof("local"));
    } else if (IndexType == IndexHashcluster) {
        memcpy(Index, "hashcluster", sizeof("hashcluster"));
    } else {
        memcpy(Index, "localhash", sizeof("localhash"));
    }

    printf("==============%s Insert Thread Begin==================\n", Index);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t i = 0;
    int32_t value = 0;
    pthread_barrier_wait(&barrier1);
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert vertex
    for (i = 0; i < times; i++) {
        //写数据，从100开始写，插入50条，符合条件的会创建二级索引
        value = initValue + i;
        TestGmcSetVertexProperty_PK(stmt, value);
        TestGmcSetVertexProperty(stmt, value);

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        if (ret == GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (affectRows == 1) {
                SuccNum++;
            }
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            printf("GmcGetStmtAttr Fail, CycleNum = %d.\n", i);
        }
    }

    printf("==============%s Insert Thread Succ Num(%d)==================\n", Index, SuccNum);
    testGmcDisconnect(conn, stmt);
    printf("==============%s Insert Thread End==================\n", Index);
    pthread_exit((void *)"结束");
}

void *Thread_KeyUpdate(void *args)
{
    int ret = 0;
    uint32_t initValue = startInsertNum;
    uint32_t times = 200;
    uint32_t SuccNum = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char Index[20] = {0};
    int affectRows = 0;
    uint32_t IndexType = *((uint32_t *)args);  //索引类型
    if (IndexType == IndexLocal) {
        memcpy(Index, "local", sizeof("local"));
    } else if (IndexType == IndexHashcluster) {
        memcpy(Index, "hashcluster", sizeof("hashcluster"));
    } else {
        memcpy(Index, "localhash", sizeof("localhash"));
    }
    printf("==============%s Update Thread Begin==================\n", Index);

    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新
    int32_t i = 0;
    int32_t keyvalue = 0;
    pthread_barrier_wait(&barrier1);
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update vertex
    for (i = 0; i < times; i++) {
        keyvalue = initValue + i;
        // 设置Filter
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 将前100条更新为index % 5
        TestGmcSetVertexPropertyUpdate(stmt, keyvalue);

        ret = GmcSetIndexKeyName(stmt, Vertex_KeyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        if (ret == GMERR_OK) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (affectRows == 1) {
                SuccNum++;
            }
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            printf("GmcGetStmtAttr Fail, CycleNum = %d.\n", i);
        }
    }

    printf("==============%s Update Thread Succ Num(%d)==================\n", Index, SuccNum);
    testGmcDisconnect(conn, stmt);
    printf("==============%s Update Thread End==================\n", Index);
    pthread_exit((void *)"结束");
}

void *Thread_IndexDelete(void *args)
{
    int ret = 0;
    uint32_t initValue = startInsertNum;
    uint32_t times = 200;
    uint32_t SuccNum = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char Index[20] = {0};
    int affectRows = 0;
    uint32_t IndexType = *((uint32_t *)args);  //索引类型
    if (IndexType == IndexLocal) {
        memcpy(Index, "local", sizeof("local"));
    } else if (IndexType == IndexHashcluster) {
        memcpy(Index, "hashcluster", sizeof("hashcluster"));
    } else {
        memcpy(Index, "localhash", sizeof("localhash"));
    }
    printf("==============%s Delete Thread Begin==================\n", Index);
    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 索引删除
    int32_t i = 0;
    int8_t f4_value = 0;
    uint32_t f7_value = 0;
    pthread_barrier_wait(&barrier1);
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // delete vertex
    f4_value = 1;
    f7_value = 1;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &f4_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &f7_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, Vertex_IndexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    if (ret == GMERR_OK) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (affectRows >= 1) {
            SuccNum = affectRows;
        }
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("GmcGetStmtAttr Fail, CycleNum = %d.\n", i);
    }

    printf("==============%s Delete Thread Succ Num(%d)==================\n", Index, SuccNum);
    testGmcDisconnect(conn, stmt);
    printf("==============%s Delete Thread End==================\n", Index);
    pthread_exit((void *)"结束");
}

void *Thread_IndexScan(void *args)
{
    int ret = 0;
    uint32_t SuccNum = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char Index[20] = {0};

    uint32_t IndexType = *((uint32_t *)args);  //索引类型
    int8_t l_val0 = 0;
    int8_t r_val0 = 9;
    uint32_t l_val1 = 0;
    uint32_t r_val1 = 9;
    unsigned int arrLen = 2;

    GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps[0].type = GMC_DATATYPE_INT8;
    leftKeyProps[0].value = &l_val0;
    leftKeyProps[0].size = sizeof(l_val0);
    leftKeyProps[1].type = GMC_DATATYPE_UINT32;
    leftKeyProps[1].value = &l_val1;
    leftKeyProps[1].size = sizeof(l_val1);

    GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps[0].type = GMC_DATATYPE_INT8;
    rightKeyProps[0].value = &r_val0;
    rightKeyProps[0].size = sizeof(r_val0);
    rightKeyProps[1].type = GMC_DATATYPE_UINT32;
    rightKeyProps[1].value = &r_val1;
    rightKeyProps[1].size = sizeof(r_val1);

    GmcRangeItemT items_sc[arrLen];
    items_sc[0].lValue = &leftKeyProps[0];
    items_sc[0].rValue = &rightKeyProps[0];
    items_sc[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[0].order = GMC_ORDER_ASC;
    items_sc[1].lValue = &leftKeyProps[1];
    items_sc[1].rValue = &rightKeyProps[1];
    items_sc[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items_sc[1].order = GMC_ORDER_ASC;

    printf("==============%s Scan Thread Begin==================\n", Index);

    ret = testGmcConnect(&conn, &stmt, GMC_CONN_TYPE_SYNC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_barrier_wait(&barrier1);
    ret = testGmcPrepareStmtByLabelName(stmt, Vertex_Name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, Vertex_IndexName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (IndexType == IndexLocal) {
        memcpy(Index, "local", sizeof("local"));
        ret = GmcSetKeyRange(stmt, items_sc, arrLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (IndexType == IndexHashcluster) {
        memcpy(Index, "hashcluster", sizeof("hashcluster"));
        int8_t int8Value = 5;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &int8Value, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t uint32Value = 5;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        memcpy(Index, "localhash", sizeof("localhash"));
        int8_t int8Value = 9;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &int8Value, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint32_t uint32Value = 9;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &uint32Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish == true) {
            break;
        }
        cnt++;
    }

    SuccNum = cnt;

    GmcFreeIndexKey(stmt);
    free(leftKeyProps);
    free(rightKeyProps);
    printf("==============%s Scan Thread Succ Num(%d)==================\n", Index, SuccNum);
    testGmcDisconnect(conn, stmt);
    printf("==============%s Scan Thread End==================\n", Index);
    pthread_exit((void *)"结束");
}

/*****************************************************************************
 Description  : 001. 1个线程插入,1个线程删除，1个线程更新，1个线程local partial 扫描4线程并发
 Author       : hanyang
*****************************************************************************/
TEST_F(IndexPartialRel_test, HardWare_Offloading_001_DML_084_004_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t initValue = 0;
    pthread_barrier_init(&barrier1, NULL, 4);
    // 创建表
    const char *schema_path = "./schema_file/Reliability_Local.gmjson";
    testCreateLabel(g_stmt, schema_path, Vertex_Name);

    // 插入多条记录：含满足filter条件和不满足filter条件的记录成功
    // insert 100条，索引值= key % 10，过滤条件index != 2,!= 6
    testInsertVertexLabel(g_conn, g_stmt, startInsertNum, initValue, Vertex_Name);

    // 四线程并发
    pthread_t Thread[4] = {0};

    pthread_create(&Thread[0], NULL, Thread_Insert, (void *)&IndexLocal);
    pthread_create(&Thread[1], NULL, Thread_KeyUpdate, (void *)&IndexLocal);
    pthread_create(&Thread[2], NULL, Thread_IndexDelete, (void *)&IndexLocal);
    pthread_create(&Thread[3], NULL, Thread_IndexScan, (void *)&IndexLocal);

    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);
    pthread_join(Thread[3], NULL);

    pthread_barrier_destroy(&barrier1);
    // 删除表
    testDropLabel(g_stmt, Vertex_Name);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 002.1个线程插入,1个线程删除，1个线程更新,1个线程hashcluster partial扫描4线程并发
 Author       : hanyang
*****************************************************************************/
TEST_F(IndexPartialRel_test, HardWare_Offloading_001_DML_084_004_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t initValue = 0;
    pthread_barrier_init(&barrier1, NULL, 4);
    // 创建表
    const char *schema_path = "./schema_file/Reliability_Localhash.gmjson";
    testCreateLabel(g_stmt, schema_path, Vertex_Name);

    // 插入多条记录：含满足filter条件和不满足filter条件的记录成功
    // insert 100条，索引值= key % 10，过滤条件index != 2,!= 6
    testInsertVertexLabel(g_conn, g_stmt, startInsertNum, initValue, Vertex_Name);

    // 四线程并发
    pthread_t Thread[4] = {0};

    pthread_create(&Thread[0], NULL, Thread_Insert, (void *)&IndexLocalhash);
    pthread_create(&Thread[1], NULL, Thread_KeyUpdate, (void *)&IndexLocalhash);
    pthread_create(&Thread[2], NULL, Thread_IndexDelete, (void *)&IndexLocalhash);
    pthread_create(&Thread[3], NULL, Thread_IndexScan, (void *)&IndexLocalhash);

    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);
    pthread_join(Thread[3], NULL);

    pthread_barrier_destroy(&barrier1);
    // 删除表
    testDropLabel(g_stmt, Vertex_Name);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 003. 1个线程插入,1个线程删除，1个线程更新，1个线程localhash partial扫描4线程并发
 Author       : hanyang
*****************************************************************************/
TEST_F(IndexPartialRel_test, HardWare_Offloading_001_DML_084_004_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int32_t initValue = 0;
    pthread_barrier_init(&barrier1, NULL, 4);
    // 创建表
    const char *schema_path = "./schema_file/Reliability_Hashcluster.gmjson";
    testCreateLabel(g_stmt, schema_path, Vertex_Name);

    // 插入多条记录：含满足filter条件和不满足filter条件的记录成功
    // insert 100条，索引值= key % 10，过滤条件index != 2,!= 6
    testInsertVertexLabel(g_conn, g_stmt, startInsertNum, initValue, Vertex_Name);

    // 四线程并发
    pthread_t Thread[4] = {0};

    pthread_create(&Thread[0], NULL, Thread_Insert, (void *)&IndexHashcluster);
    pthread_create(&Thread[1], NULL, Thread_KeyUpdate, (void *)&IndexHashcluster);
    pthread_create(&Thread[2], NULL, Thread_IndexDelete, (void *)&IndexHashcluster);
    pthread_create(&Thread[3], NULL, Thread_IndexScan, (void *)&IndexHashcluster);

    pthread_join(Thread[0], NULL);
    pthread_join(Thread[1], NULL);
    pthread_join(Thread[2], NULL);
    pthread_join(Thread[3], NULL);
    pthread_barrier_destroy(&barrier1);
    // 删除表
    testDropLabel(g_stmt, Vertex_Name);
    AW_FUN_Log(LOG_STEP, "test end.");
}
