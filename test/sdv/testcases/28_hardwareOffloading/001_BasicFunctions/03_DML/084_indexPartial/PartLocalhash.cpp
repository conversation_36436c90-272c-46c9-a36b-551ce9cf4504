#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "../../common/hash_util.h"

#define MAX_NAME_LENGTH 128
//全局变量
GmcConnT *conn;  //键连句柄
GmcStmtT *stmt;  // stmt操作句柄

void *label = NULL;

int AffectRows;
unsigned int Len;

class DML_084_002_003 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
        system("gmadmin -cfgName compatibleV3 -cfgVal 0");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        system("gmadmin -cfgName compatibleV3 -cfgVal 1");
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        //恢复配置文件
        RecoverCfg();
    }

public:
    SnUserDataT *user_data;
    int *newValue;
    int *oldValue;
    virtual void SetUp();
    virtual void TearDown();
};
void DML_084_002_003::SetUp()
{
    printf("******************************start \n");
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void DML_084_002_003::TearDown()
{
    AW_CHECK_LOG_END();
    //客户端断链
    testGmcDisconnect(conn, stmt);

    printf("**********************************end\n");
}

// insert 数据  入参stmt 表明 startmun endnum
void test_insert_vertex(GmcStmtT *stmt, const char *labelName, int start_num, int end_num, int filterType)
{
    if (filterType ==
        1)  // 如果filterType 为1 代表
            // uchar为partial索引字段,其中5条5条记录hansh索引满足schema中的相等条件为A，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (i < 5) {
                unsigned char F8_value = 'A';
                ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                unsigned char F8_value = 'B';
                ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            char F9_value = 'a';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType == 2)  // 如果filterType 为2 代表
                          // char为partial索引字段,其中5条记录hansh索引满足schema中的不等条件不为C，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (i < 5) {
                char F9_value = 'A';
                ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                char F9_value = 'C';
                ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType ==
        3)  // 如果filterType 为3 代表 uint8为partial索引字段,其中5条记录hansh索引满足schema中的不等条件不是 0 1 2 3
            // 4，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (i > 4) {
                uint8_t F12_value = 5;
                ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                uint8_t F12_value = i;
                ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType == 4)  // 如果filterType 为4 代表
                          // int8为partial索引字段,其中5条记录hansh索引满足schema中的相等条件为-1，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (i < 5) {
                int8_t F1_value = -1;
                ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                int8_t F1_value = i;
                ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType ==
        5)  // 如果filterType 为5 代表
            // uint16为partial索引字段,其中5条记录hansh索引满足schema中的相等条件为-1，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (i < 5) {
                uint16_t F3_value = 1;
                ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                uint16_t F3_value = i;
                ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType ==
        6)  // 如果filterType 为6 代表 int16为partial索引字段,其中5条记录hansh索引满足schema中的不等条件不是-1 1 2 3
            // 4，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (i == 0) {
                int16_t F2_value = -1;
                ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            if (i > 4) {
                int16_t F2_value = 5;
                ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                int16_t F2_value = i;
                ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType ==
        7)  // 如果filterType 为7 代表
            // uint32为partial索引字段,其中5条记录hansh索引满足schema中的相等条件相同为1，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (i < 5) {
                uint32_t F5_value = 1;
                ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                uint32_t F5_value = i;
                ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType ==
        8)  // 如果filterType 为8 代表 int32为partial索引字段,其中5条记录hansh索引满足schema中的不等条件相同不是-1 1 2 3
            // 4，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (i == 0) {
                int32_t F4_value = -1;
                ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            if (i > 4) {
                int32_t F4_value = 5;
                ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                int32_t F4_value = i;
                ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType ==
        9)  // 如果filterType 为9 代表
            // uint64为partial索引字段,其中5条记录hansh索引满足schema中的相等条件相同为1，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (i < 5) {
                uint64_t F7_value = 1;
                ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                uint64_t F7_value = i;
                ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType ==
        10)  // 如果filterType 为10 代表 int64为partial索引字段,其中5条记录hansh索引满足schema中的不等条件相同不是-1 1 2
             // 3 4，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (i == 0) {
                int64_t F6_value = -1;
                ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            if (i > 4) {
                int64_t F6_value = 5;
                ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                int64_t F6_value = i;
                ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType == 11)  // 如果filterType 为11 代表
                           // fixed为partial索引字段,其中5条记录hansh索引满足schema中的不等条件相同不是16进制0x112233445566778899
                           // 字符串值fffffffff，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (i == 0) {
                uint8_t F13_value[9] = {0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99};
                ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_FIXED, F13_value, 9);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            if (i > 4) {
                char F13_value[] = "123456789";
                ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_FIXED, F13_value, 9);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                char F13_value[] = "fffffffff";
                ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_FIXED, F13_value, 9);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType ==
        12)  // 如果filterType 为12 代表
             // time为partial索引字段,其中5条记录hansh索引满足schema中的相等条件相同是1，其余hash索引不能建立
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (i > 4) {
                uint64_t F11_value = 1;
                ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                uint64_t F11_value = 1000;
                ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType == 13)  // 如果filterType 为13 代表
                           // 多个字段不连续且乱序满16个条件建partial索引,条件为F2不等于0-4，F4不等于0-4，F6不等于0-4，F12要是5.才能建立
                           // (这里将F2,F4,F6,F12设置为5 建立hash索引)
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (i > 4) {
                int16_t F2_value = 5;
                ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                int32_t F4_value = 5;
                ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                int64_t F6_value = 5;
                ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                uint8_t F12_value = 5;
                ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                int16_t F2_value = i;
                ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                int32_t F4_value = i;
                ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                int64_t F6_value = i;
                ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                uint8_t F12_value = i;
                ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType == 14)  // 如果filterType 为14 代表 uint8建partial唯一索引,条件为1建立唯一hash索引，其他不建立索引
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F10_value[] = "111111111111";
            ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F11_value = 1000;
            ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int32_t F4_value = i;
            ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    if (filterType ==
        15)  // 如果filterType 为15 代表
             // 多个字段不连续int32,fixed,time建partial唯一索引,条件为int32,time为1，fixed不等于0x112233445566778899AABBCC建立唯一hash索引。
    {
        for (int i = start_num; i < end_num; i++) {
            int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            // hash index & pk
            uint8_t F0_value = i;
            ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT8, &F0_value, sizeof(F0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int8_t F1_value = i;
            ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT8, &F1_value, sizeof(F1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint16_t F3_value = i;
            ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT16, &F3_value, sizeof(F3_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint32_t F5_value = i;
            ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5_value, sizeof(F5_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint64_t F7_value = i;
            ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7_value, sizeof(F7_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            unsigned char F8_value = 'b';
            ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            char F9_value = 'A';
            ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int16_t F2_value = i;
            ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2_value, sizeof(F2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            int64_t F6_value = i;
            ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6_value, sizeof(F6_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            uint8_t F12_value = i;
            ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_value, sizeof(F12_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            if (i == 0) {
                int32_t F4_value = 0;
                ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                uint8_t F10_value[12] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
                ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                uint64_t F11_value = 0;
                ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                int32_t F4_value = i;
                ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_value, sizeof(F4_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                uint8_t F10_value[12] = {0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99, 0xAA, 0xBB, 0xCC};
                ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, F10_value, 12);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                uint64_t F11_value = 1000;
                ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_value, sizeof(F11_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }

            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
}
/* ****************************************************************************
 Description  : uchar类型字段='A'建partial索引，插入多条记录
                并local查询/主键更新部分记录/localhash删除记录并localhash查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_uchar.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // uchar类型=A值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_uchar";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(stmt, labelName, start_num, end_num, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    unsigned char F8Key_value = 'A';
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UCHAR, &F8Key_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);
    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 0;
    unsigned char F8_value = 'C';
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "F8", GMC_DATATYPE_UCHAR, &F8_value, sizeof(F8_value));  //将满足flter记录的索引字段设置为不满足
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UCHAR, &F8Key_value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    LocalhashFetchNum = 0;  //重新将标志位set为0
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
char类型字段!='C'多个条件建partial索引，插入多条记录并localhash查询/主键更新部分记录/localhash删除记录并localhash查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_char.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // uchar类型=A值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_char";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(stmt, labelName, start_num, end_num, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    char F9Key_value = 'A';  // insert 10条数据，其中5条为A，5条为C. 这里只有5条为A的记录通过hash索引可以扫描到预期为5条
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_CHAR, &F9Key_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);
    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 0;
    char F9_value = 'C';
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "F9", GMC_DATATYPE_CHAR, &F9_value, sizeof(F9_value));  //将满足flter记录的索引字段设置为不满足
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_CHAR, &F9Key_value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    LocalhashFetchNum = 0;  //重新将标志位set为0
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : uint8类型字段!= "某值" 多个条件
建partial索引，插入多条记录并localhash查询/主键更新部分记录/localhash删除记录并localhash查询 Input        : None Output
: None Return Value : Notes        : History      : Author       : wwx1038088 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_uint8.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // uchar类型=A值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_uint8";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(stmt, labelName, start_num, end_num, 3);  // uint8类型字段!=  1 2 3 4 5
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    uint8_t F12Key_value = 5;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &F12Key_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);
    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 5;
    F12Key_value = 0;  //将原先可以通过hash查到的数据 设置为0，这样预期通过hash查询不到这条记录
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "F12", GMC_DATATYPE_UINT8, &F12Key_value, sizeof(F12Key_value));  //将满足flter记录的索引字段设置为不满足
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F12Key_value = 5;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &F12Key_value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : int8类型字段=负数
建partial索引，插入多条记录并localhash查询/主键更新部分记录/localhash删除记录并localhash查询 Input        : None Output
: None Return Value : Notes        : History      : Author       : wwx1038088 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_int8.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // uchar类型=A值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_int8";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(
        stmt, labelName, start_num, end_num, 4);  // 插入hash索引类型为int8且值为负数的 数据5条，其余正数的数据有5条
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    int8_t F1Key_value = -1;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &F1Key_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到5条数据
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);
    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 4;
    F1Key_value = 0;  //将这条数据从-1 设置为0 使得hash查询不到
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "F1", GMC_DATATYPE_INT8, &F1Key_value, sizeof(F1Key_value));  //将满足flter记录的索引字段设置为不满足
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F1Key_value = -1;       //将hash索引值重新set为-1
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &F1Key_value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到4条数据
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
uint16类型字段=建partial索引，插入多条记录并localhash查询/主键更新部分记录/localhash删除记录并localhash查询 Input : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_uint16.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型uint16类型=-1值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_uint16";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(
        stmt, labelName, start_num, end_num, 5);  // 插入hash索引类型为uint16且值为1的 数据5条，其余数据不建hash索引
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    uint16_t F3Key_value = 1;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &F3Key_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到5条数据
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);
    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 4;
    F3Key_value = 4;  //将这条数据从1 设置为4 使得hash查询不到
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "F3", GMC_DATATYPE_UINT16, &F3Key_value, sizeof(F3Key_value));  //将满足flter记录的索引字段设置为不满足
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F3Key_value = 1;        //将hash索引值重新set为1
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT16, &F3Key_value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到4条数据
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
int16类型字段！=多个（含负数）建partial索引，插入多条记录并localhash查询/主键更新部分记录/localhash删除记录并localhash查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_int16.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int16类型!= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_int16";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(stmt, labelName, start_num, end_num,
        6);  // insert 10条数据，Localhash索引值不为1-4和-1建立hash索引 一共有5条数据建立hash索引
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    int16_t F2Key_value = 5;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &F2Key_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到5条数据
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);
    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 0;
    F2Key_value = 5;  //将这条数据从-1 设置为5 使得hash索引可以查到数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2Key_value, sizeof(F2Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前多一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F2Key_value = 5;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &F2Key_value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到6条数据
    AW_MACRO_EXPECT_EQ_INT(6, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
uint32类型字段=建partial索引，插入多条记录并localhash查询/主键更新部分记录/localhash删除记录并localhash查询 Input : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_uint32.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型uint32类型= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_uint32";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(stmt, labelName, start_num, end_num,
        7);  // insert 10条数据，对于0-4条数据来说，hash索引就是1,其余的数据不能建立索引
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    uint32_t F5Key_value = 1;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F5Key_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到5条数据
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);
    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 4;
    F5Key_value = 4;  //将这条数据从1 设置为4 使得hash索引可以查不到数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT32, &F5Key_value, sizeof(F5Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F5Key_value = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F5Key_value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到4条数据
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
int32类型字段!=建partial索引，插入多条记录并localhash查询/主键更新部分记录/localhash删除记录并localhash查询 Input : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_int32.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int32类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_int32";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(stmt, labelName, start_num, end_num,
        8);  // insert 10条数据，其中0-4的数据没有建立hash索引,其余建立hash索引 索引值为5
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    int32_t F4Key_value = 5;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4Key_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到5条数据
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);

    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 5;
    F4Key_value = 4;  //将这条数据从5 设置为4 使得hash索引不能建立
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4Key_value, sizeof(F4Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F4Key_value = 5;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4Key_value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到4条数据
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
uint64类型字段=建partial索引，插入多条记录并localhash查询/主键更新部分记录/localhash删除记录并localhash查询 Input : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_uint64.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int32类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_uint64";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(
        stmt, labelName, start_num, end_num, 9);  // insert 10条数据，其中0-4的数据建立hash索引,其余没有建立hash索引
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    uint64_t F7Key_value = 1;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &F7Key_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到5条数据
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);

    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 4;
    F7Key_value = 4;  //将这条数据从1 设置为4 使得hash索引不能建立
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT64, &F7Key_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F7Key_value = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &F7Key_value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到4条数据
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
int64类型字段!=建partial索引（含负数），插入多条记录并localhash查询/主键更新部分记录/localhash删除记录并localhash查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_int64.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int64类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_int64";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(stmt, labelName, start_num, end_num,
        10);  // insert 10条数据，其中0-4的数据不会建立hash索引,其余建立hash索引值为5
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    int64_t F6Key_value = 5;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F6Key_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到5条数据
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);

    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 5;
    F6Key_value = 4;  //将这条数据从5 设置为4 使得hash索引不能建立
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6Key_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F6Key_value = 5;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F6Key_value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到4条数据
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : fixed
类型字段!=建partial索引(含16进制值与字符串值至少2个条件)，插入多条记录并localhash查询/主键更新部分记录/localhash查询localhash删除记录并localhash查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_fixed.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int64类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_fixed";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(stmt, labelName, start_num, end_num,
        11);  // insert 10条数据，其中0-4的数据不会建立hash索引,其余建立hash索引值为123456789
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    char F13_value[] = "123456789";
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, &F13_value, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到5条数据
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);

    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 5;
    uint8_t F13_UpdateValue[9] = {0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99};
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_FIXED, &F13_UpdateValue, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, &F13_value, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到4条数据
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
time类型字段=建partial索引，插入多条记录并localhash查询/主键更新部分记录/localhash删除记录并localhash查询 Input        :
None Output       : None Return Value : Notes        : History      : Author       : wwx1038088 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_time.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int64类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_time";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(stmt, labelName, start_num, end_num,
        12);  // insert 10条数据，其中0-4的数据不会建立hash索引,其余建立hash索引值为123456789
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    uint64_t F11_keyvalue = 1;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到5条数据
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);

    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 5;
    F11_keyvalue = 5;  //将值由1改为5 使得索引不能建立
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F11_keyvalue = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到4条数据
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
多个字段不连续且乱序满16个条件建partial索引，插入多条记录并localhash查询/localhash更新成不满足filter记录并localhash查询/插入新值/localhash删除记录并localhash查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_multiplefields_16.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int64类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_multiplefields_16";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(stmt, labelName, start_num, end_num,
        13);  // insert 10条数据，其中0-4的数据不会建立hash索引,其余建立hash索引值为5
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    int16_t F2Key_value = 5;
    int32_t F4Key_value = 5;
    int64_t F6Key_value = 5;
    uint8_t F12Key_value = 5;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &F2Key_value, sizeof(F2Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &F4Key_value, sizeof(F4Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &F6Key_value, sizeof(F6Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &F12Key_value, sizeof(F12Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到5条数据
    AW_MACRO_EXPECT_EQ_INT(5, LocalhashFetchNum);

    //主键更新满足filter的部分记录成不满足filter的记录成功
    uint8_t primary_pk_value = 5;
    F2Key_value = 1;
    F4Key_value = 1;
    F6Key_value = 1;
    F12Key_value = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT16, &F2Key_value, sizeof(F2Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4Key_value, sizeof(F4Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT64, &F6Key_value, sizeof(F6Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12Key_value, sizeof(F12Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前少一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F2Key_value = 5;
    F4Key_value = 5;
    F6Key_value = 5;
    F12Key_value = 5;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT16, &F2Key_value, sizeof(F2Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &F4Key_value, sizeof(F4Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &F6Key_value, sizeof(F6Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &F12Key_value, sizeof(F12Key_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到4条数据
    AW_MACRO_EXPECT_EQ_INT(4, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
唯一索引，已存在满足filter的记录，将不满足filter的记录主键更新/merge/replace为满足filter的记录并localhash查询 Input :
None Output       : None Return Value : Notes        : History      : Author       : wwx1038088 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_Uniqueindex.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int64类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_Uniqueindex";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(
        stmt, labelName, start_num, end_num, 14);  // insert 10条数据，其中只有1会建立唯一的hash索引，其他不会建立索引
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    uint8_t F12_Keyvalue = 1;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &F12_Keyvalue, sizeof(F12_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到1条数据
    AW_MACRO_EXPECT_EQ_INT(1, LocalhashFetchNum);
    //将不满足filter的记录主键更新成满足filter的记录 预期更新失败报索引冲突 GMERR_UNIQUE_VIOLATION
    uint8_t primary_pk_value = 2;
    F12_Keyvalue = 1;  //将值由2改为1
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_Keyvalue, sizeof(F12_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);                  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNIQUE_VIOLATION, ret);  //预期报索引冲突

    // localhash索引查询记录
    LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &F12_Keyvalue, sizeof(F12_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到1条数据
    AW_MACRO_EXPECT_EQ_INT(1, LocalhashFetchNum);
    //将不满足filter的记录merge成满足filter的记录 预期更新失败报索引冲突 GMERR_UNIQUE_VIOLATION
    primary_pk_value = 5;
    F12_Keyvalue = 1;  //将值由5改为1
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_Keyvalue, sizeof(F12_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);                  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNIQUE_VIOLATION, ret);  //预期报索引冲突

    // localhash索引查询记录
    LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &F12_Keyvalue, sizeof(F12_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到1条数据
    AW_MACRO_EXPECT_EQ_INT(1, LocalhashFetchNum);
    //将不满足filter的记录replace成满足filter的记录 预期更新失败报索引冲突 GMERR_UNIQUE_VIOLATION
    LocalhashFetchNum = 0;
    primary_pk_value = 6;
    uint8_t primary_pk_value_new = 11;
    F12_Keyvalue = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "F0", GMC_DATATYPE_UINT8, &primary_pk_value_new, sizeof(primary_pk_value_new));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_UINT8, &F12_Keyvalue, sizeof(F12_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);                  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_UNIQUE_VIOLATION, ret);  //预期报索引冲突

    // localhash索引查询记录
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &F12_Keyvalue, sizeof(F12_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到1条数据
    AW_MACRO_EXPECT_EQ_INT(1, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 多个字段多个条件将不满足filter的记录replace成满足filter的记录并localhash查询和主键查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_multiplefields_replace.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int64类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_multiplefields_replace";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(
        stmt, labelName, start_num, end_num, 15);  // insert 10条数据，其中0建立hash索引,其余不会建立hash索引
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描  预期扫到一条记录
    int32_t F4_Keyvalue = 0;
    uint8_t F10_Keyvalue[12] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint64_t F11_keyvalue = 0;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到1条数据
    AW_MACRO_EXPECT_EQ_INT(1, LocalhashFetchNum);
    //进行replace满足filter的部分记录成将不满足filter的记录变成满足filter的部分记录
    uint8_t primary_pk_value = 10;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "F0", GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前多一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到2条数据
    AW_MACRO_EXPECT_EQ_INT(2, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 多个字段多个条件将不满足filter的记录merge成满足filter的记录并localhash查询和主键查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_multiplefields_merge.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int64类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_multiplefields_merge";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(
        stmt, labelName, start_num, end_num, 15);  // insert 10条数据，其中0建立hash索引,其余不会建立hash索引
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描  预期扫到一条记录
    int32_t F4_Keyvalue = 0;
    uint8_t F10_Keyvalue[12] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint64_t F11_keyvalue = 0;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(GMC_DATATYPE_INT32));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到1条数据
    AW_MACRO_EXPECT_EQ_INT(1, LocalhashFetchNum);
    //进行merge满足filter的部分记录成将不满足filter的记录变成满足filter的部分记录
    uint8_t primary_pk_value = 2;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(GMC_DATATYPE_INT32));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前多一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(GMC_DATATYPE_INT32));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到2条数据
    AW_MACRO_EXPECT_EQ_INT(2, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 多个字段多个条件将不满足filter的记录主键更新成满足filter的记录并localhash查询和主键查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_multiplefields_update.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int64类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_multiplefields_update";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(
        stmt, labelName, start_num, end_num, 15);  // insert 10条数据，其中0建立hash索引,其余不会建立hash索引
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描  预期扫到一条记录
    int32_t F4_Keyvalue = 0;
    uint8_t F10_Keyvalue[12] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint64_t F11_keyvalue = 0;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(GMC_DATATYPE_INT32));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到1条数据
    AW_MACRO_EXPECT_EQ_INT(1, LocalhashFetchNum);
    //进行merge满足filter的部分记录成将不满足filter的记录变成满足filter的部分记录
    uint8_t primary_pk_value = 2;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(F4_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前多一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(F4_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到2条数据
    AW_MACRO_EXPECT_EQ_INT(2, LocalhashFetchNum);
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 多个字段多个条件将满足filter的记录merge成不满足filter的记录并localhash查询和主键查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_multiplefields_merge.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int64类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_multiplefields_merge";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(
        stmt, labelName, start_num, end_num, 15);  // insert 10条数据，其中0建立hash索引,其余不会建立hash索引
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描  预期扫到一条记录
    int32_t F4_Keyvalue = 0;
    uint8_t F10_Keyvalue[12] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint64_t F11_keyvalue = 0;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(F4_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到1条数据
    AW_MACRO_EXPECT_EQ_INT(1, LocalhashFetchNum);
    //进行merge满足filter的部分记录成将满足filter的记录变成不满足filter的部分记录
    uint8_t primary_pk_value = 0;
    F4_Keyvalue = 5;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(F4_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录为0
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F4_Keyvalue = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(F4_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到0条数据
    AW_MACRO_EXPECT_EQ_INT(0, LocalhashFetchNum);
    //通过主键查询这条数据，查到数据变成了修改后的数据
    int FetchNum = 0;
    primary_pk_value = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end FetchNum is %d \n", FetchNum);
        } else {
            // 查看修改的数据是否符合预期
            unsigned int sizeF4;
            ret = GmcGetVertexPropertySizeByName(stmt, "F4", &sizeF4);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            int32_t getF4_value;
            F4_Keyvalue = 5;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F4", &getF4_value, sizeF4, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(F4_Keyvalue, getF4_value);
            FetchNum++;
        }
    }
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 多个字段多个条件将满足filter的记录replace成不满足filter的记录并localhash查询和主键查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_multiplefields_replace.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int64类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_multiplefields_replace";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(
        stmt, labelName, start_num, end_num, 15);  // insert 10条数据，其中0建立hash索引,其余不会建立hash索引
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描  预期扫到一条记录
    int32_t F4_Keyvalue = 0;
    uint8_t F10_Keyvalue[12] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint64_t F11_keyvalue = 0;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(GMC_DATATYPE_INT32));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到1条数据
    AW_MACRO_EXPECT_EQ_INT(1, LocalhashFetchNum);
    //进行replace满足filter的部分记录成将满足filter的记录变成不满足filter的部分记录
    uint8_t primary_pk_value = 0;
    F4_Keyvalue = 5;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(
        stmt, "F0", GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(F4_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前多一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F4_Keyvalue = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(F4_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到0条数据
    AW_MACRO_EXPECT_EQ_INT(0, LocalhashFetchNum);
    //通过主键查询这条数据，查到数据变成了修改后的数据
    int FetchNum = 0;
    primary_pk_value = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end FetchNum is %d \n", FetchNum);
        } else {
            // 查看修改的数据是否符合预期
            unsigned int sizeF4;
            ret = GmcGetVertexPropertySizeByName(stmt, "F4", &sizeF4);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            int32_t getF4_value;
            F4_Keyvalue = 5;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F4", &getF4_value, sizeF4, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(F4_Keyvalue, getF4_value);
            FetchNum++;
        }
    }
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 多个字段多个条件将满足filter的记录replace成不满足filter的记录并localhash查询和主键查询
 Input        : None
 Output       : None
 Return Value :
 Notes        :
 History      :
 Author       : wwx1038088
 Modification :
**************************************************************************** */
TEST_F(DML_084_002_003, HardWare_Offloading_001_DML_084_002_003_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *schema_json = NULL;                                   // schema_json
    char g_configJson[128] = "{\"max_record_count\" : 10000}";  // config_json
    //读json文件  和config文件
    readJanssonFile("./schema_file/Localhash_multiplefields_update.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    // 索引条件类型int64类型！= 某值进行创表成功
    int32_t ret = 0;
    char labelName[128] = "Localhash_multiplefields_update";  //表名
    ret = GmcDropVertexLabel(stmt, labelName);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(schema_json);

    // insert 10条数据 ：含满足filter条件的记录和不满足filter条件的记录
    int start_num = 0, end_num = 10;
    test_insert_vertex(
        stmt, labelName, start_num, end_num, 15);  // insert 10条数据，其中0建立hash索引,其余不会建立hash索引
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash扫描  预期扫到一条记录
    int32_t F4_Keyvalue = 0;
    uint8_t F10_Keyvalue[12] = {0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff};
    uint64_t F11_Keyvalue = 0;
    int LocalhashFetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(F4_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_Keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //预期查询到1条数据
    AW_MACRO_EXPECT_EQ_INT(1, LocalhashFetchNum);
    //进行replace满足filter的部分记录成将满足filter的记录变成不满足filter的部分记录
    uint8_t primary_pk_value = 0;
    F4_Keyvalue = 5;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));  //设置主键的值
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(F4_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);  //插入数据
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描 此时扫描到的记录要比之前多一条
    LocalhashFetchNum = 0;  //重新将标志位set为0
    F4_Keyvalue = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "localhash_key");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &F4_Keyvalue, sizeof(F4_Keyvalue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_FIXED, &F10_Keyvalue, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_TIME, &F11_Keyvalue, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end LocalhashFetchNum is %d \n", LocalhashFetchNum);
        } else {
            LocalhashFetchNum++;  // 若果有记录 则标志位+1
        }
    }
    //记录扫描结束 打印扫描到的数据
    printf("Fetch Over num is %d \n ", LocalhashFetchNum);  //这里预期查询到0条数据
    AW_MACRO_EXPECT_EQ_INT(0, LocalhashFetchNum);
    //通过主键查询这条数据，查到数据变成了修改后的数据
    primary_pk_value = 0;
    int FetchNum = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "primary_pk");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT8, &primary_pk_value, sizeof(primary_pk_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            //如果没查到或者结束返回
            printf("Fetch end FetchNum is %d \n", FetchNum);
        } else {
            // 查看修改的数据是否符合预期
            unsigned int sizeF4;
            ret = GmcGetVertexPropertySizeByName(stmt, "F4", &sizeF4);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            int32_t getF4_value;
            F4_Keyvalue = 5;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "F4", &getF4_value, sizeF4, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(F4_Keyvalue, getF4_value);
            FetchNum++;
        }
    }
    // 删除表
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
