[{"type": "record", "name": "partial_Hashcluster_T", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": false}, {"name": "F6", "type": "int8", "nullable": false}, {"name": "F7", "type": "uint8", "nullable": false}, {"name": "F8", "type": "boolean", "nullable": false}, {"name": "F9", "type": "float", "nullable": false}, {"name": "F10", "type": "double", "nullable": false}, {"name": "F11", "type": "time", "nullable": false}, {"name": "F12", "type": "char", "nullable": false}, {"name": "F13", "type": "uchar", "nullable": false}, {"name": "F16", "type": "fixed", "size": 7, "nullable": false}, {"name": "F17", "type": "uint32", "nullable": false}, {"name": "F18", "type": "uint32", "nullable": false}, {"name": "F19", "type": "uint8", "nullable": false}, {"name": "F20", "type": "string", "size": 20, "nullable": false}, {"name": "F21", "type": "bytes", "size": 7, "nullable": false}, {"name": "F22", "type": "fixed", "size": 7, "nullable": false}, {"name": "F23", "type": "time", "nullable": false}, {"name": "F24", "type": "int32", "nullable": false}, {"name": "F25", "type": "uint32", "nullable": false}, {"name": "F26", "type": "int16", "nullable": false}, {"name": "F27", "type": "uint16", "nullable": false}, {"name": "F28", "type": "int64", "nullable": false}, {"name": "F29", "type": "uint64", "nullable": false}, {"name": "F30", "type": "char", "nullable": false}, {"name": "F31", "type": "int32", "nullable": false}, {"name": "F32", "type": "uint32", "nullable": false}, {"name": "T1", "type": "record", "array": true, "size": 24, "fields": [{"name": "A0", "type": "int64", "nullable": false}, {"name": "A1", "type": "uint64", "nullable": false}, {"name": "A2", "type": "int32", "nullable": false}]}], "keys": [{"node": "partial_Hashcluster_T", "name": "PK", "fields": ["F0"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "partial_Hashcluster_T", "name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "fields": ["F2", "F4", "F5", "F6"], "constraints": {"unique": false}}, {"node": "partial_Hashcluster_T", "name": "lpm4_key", "index": {"type": "lpm4_tree_bitmap"}, "fields": ["F3", "F17", "F18", "F19"], "constraints": {"unique": true}}, {"node": "partial_Hashcluster_T", "name": "local_key", "fields": ["F1", "F7", "F11", "F12", "F13", "F16", "F24", "F25"], "index": {"type": "local"}, "constraints": {"unique": false}}, {"node": "partial_Hashcluster_T", "name": "hashcluster_key1", "fields": ["F22", "F26", "F27", "F28", "F23", "F30", "F31", "F32"], "index": {"type": "hashcluster"}, "constraints": {"unique": false}, "filter": {"operator_type": "and", "conditions": [{"property": "F30", "compare_type": "unequal", "value": "a"}, {"property": "F30", "compare_type": "unequal", "value": "b"}, {"property": "F30", "compare_type": "unequal", "value": "0"}]}}, {"node": "T1", "name": "member_key", "index": {"type": "none"}, "fields": ["A0"], "constraints": {"unique": false}}]}]