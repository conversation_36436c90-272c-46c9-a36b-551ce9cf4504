[{"type": "record", "name": "TEST_T5", "fields": [{"name": "F0", "type": "int64", "nullable": false}, {"name": "F1", "type": "uint64", "nullable": false}, {"name": "F2", "type": "int32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "int16", "nullable": false}, {"name": "F5", "type": "uint16", "nullable": false}, {"name": "F6", "type": "int8", "nullable": false}, {"name": "F7", "type": "uint8", "nullable": false}, {"name": "F8", "type": "boolean", "nullable": true}, {"name": "F9", "type": "float", "nullable": true}, {"name": "F10", "type": "double", "nullable": true}, {"name": "F11", "type": "time", "nullable": true}, {"name": "F12", "type": "char", "nullable": true}, {"name": "F13", "type": "uchar", "nullable": true}, {"name": "F14", "type": "fixed", "size": 16, "nullable": true}, {"name": "F16", "type": "string", "size": 100, "nullable": true}, {"name": "F17", "type": "bytes", "size": 100, "nullable": true}, {"name": "T1", "type": "record", "fields": [{"name": "P0", "type": "int64", "nullable": true}, {"name": "P1", "type": "uint64", "nullable": true}, {"name": "P2", "type": "int32", "nullable": true}, {"name": "P3", "type": "uint32", "nullable": true}, {"name": "P4", "type": "int16", "nullable": true}, {"name": "P5", "type": "uint16", "nullable": true}, {"name": "P6", "type": "int8", "nullable": true}, {"name": "P7", "type": "uint8", "nullable": true}, {"name": "P8", "type": "boolean", "nullable": true}, {"name": "P9", "type": "float", "nullable": true}, {"name": "P10", "type": "double", "nullable": true}, {"name": "P11", "type": "time", "nullable": true}, {"name": "P12", "type": "char", "nullable": true}, {"name": "P13", "type": "uchar", "nullable": true}, {"name": "P14", "type": "fixed", "size": 16, "nullable": true}, {"name": "P15", "type": "string", "size": 100, "nullable": true}, {"name": "P16", "type": "bytes", "size": 100, "nullable": true}, {"name": "T1_1", "type": "record", "fields": [{"name": "P0", "type": "int64", "nullable": true}]}]}, {"name": "T2", "type": "record", "fixed_array": true, "size": 10, "fields": [{"name": "A0", "type": "int64", "nullable": true}, {"name": "A1", "type": "uint64", "nullable": true}, {"name": "A2", "type": "int32", "nullable": true}, {"name": "A3", "type": "uint32", "nullable": true}, {"name": "A4", "type": "int16", "nullable": true}, {"name": "A5", "type": "uint16", "nullable": true}, {"name": "A6", "type": "int8", "nullable": true}, {"name": "A7", "type": "uint8", "nullable": true}, {"name": "A8", "type": "boolean", "nullable": true}, {"name": "A9", "type": "float", "nullable": true}, {"name": "A10", "type": "double", "nullable": true}, {"name": "A11", "type": "time", "nullable": true}, {"name": "A12", "type": "char", "nullable": true}, {"name": "A13", "type": "uchar", "nullable": true}, {"name": "A14", "type": "fixed", "size": 16, "nullable": true}, {"name": "A15", "type": "string", "size": 100, "nullable": true}, {"name": "A16", "type": "bytes", "size": 100, "nullable": true}, {"name": "T2_1", "type": "record", "fields": [{"name": "P0", "type": "int64", "nullable": true}]}]}, {"name": "T3", "type": "record", "vector": true, "size": 1024, "fields": [{"name": "V0", "type": "int64", "nullable": true}, {"name": "V1", "type": "uint64", "nullable": true}, {"name": "V2", "type": "int32", "nullable": true}, {"name": "V3", "type": "uint32", "nullable": true}, {"name": "V4", "type": "int16", "nullable": true}, {"name": "V5", "type": "uint16", "nullable": true}, {"name": "V6", "type": "int8", "nullable": true}, {"name": "V7", "type": "uint8", "nullable": true}, {"name": "V8", "type": "boolean", "nullable": true}, {"name": "V9", "type": "float", "nullable": true}, {"name": "V10", "type": "double", "nullable": true}, {"name": "V11", "type": "time", "nullable": true}, {"name": "V12", "type": "char", "nullable": true}, {"name": "V13", "type": "uchar", "nullable": true}, {"name": "V14", "type": "fixed", "size": 16, "nullable": true}, {"name": "V15", "type": "string", "size": 100, "nullable": true}, {"name": "V16", "type": "bytes", "size": 100, "nullable": true}, {"name": "T3_1", "type": "record", "vector": true, "size": 1024, "fields": [{"name": "P0", "type": "int64", "nullable": true}]}]}], "keys": [{"node": "TEST_T5", "name": "TEST_PK", "fields": ["F0", "F1", "F2", "F3", "F4", "F5", "F6", "F7"], "index": {"type": "primary"}, "constraints": {"unique": true}}, {"node": "T2", "name": "member_key1", "index": {"type": "none"}, "fields": ["A0"], "constraints": {"unique": false}}, {"node": "T2", "name": "member_key2", "index": {"type": "none"}, "fields": ["A1"], "constraints": {"unique": false}}, {"node": "T3", "name": "member_key3", "index": {"type": "none"}, "fields": ["V0"], "constraints": {"unique": false}}, {"node": "TEST_T5", "name": "uniq_localhash", "fields": ["F1", "F2", "F3", "F4", "F5", "F6", "F7", "F11"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": true}}, {"node": "TEST_T5", "name": "nonuniq_localhash", "fields": ["F1", "F2", "F3"], "index": {"type": "<PERSON><PERSON><PERSON>"}, "constraints": {"unique": false}}, {"node": "TEST_T5", "name": "uniq_hashcluster", "fields": ["F1", "F2", "F3", "F4", "F5", "F6", "F7", "F11"], "index": {"type": "hashcluster"}, "constraints": {"unique": true}}, {"node": "TEST_T5", "name": "nonuniq_hashcluster", "fields": ["F1", "F2", "F3"], "index": {"type": "hashcluster"}, "constraints": {"unique": false}}, {"node": "TEST_T5", "name": "local", "fields": ["F1", "F2", "F3", "F4", "F5", "F6", "F7", "F11"], "index": {"type": "local"}, "constraints": {"unique": true}}]}]