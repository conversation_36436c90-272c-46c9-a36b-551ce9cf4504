//
// Created by w00495442 on 2021/11/2.
//
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "table_struct.h"

#define MAX_CMD_SIZE 1024
GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL, *g_conn_sub = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL, *g_stmt_sub = NULL;
char *g_schema = NULL;
int affectRows;
unsigned int len;
char g_table_name[] = "TEST_T1", g_table_name2[] = "TEST_T2", g_table_name3[] = "TEST_T3", g_table_name4[] = "TEST_T4",
     g_table_name5[] = "TEST_T5", g_table_name6[] = "TEST_T6", g_table_name7[] = "TEST_T7", g_table_name8[] = "TEST_T8",
     g_table_name9[] = "TEST_T9", g_table_name10[] = "TEST_T10";
char g_pk_name[] = "TEST_PK";
char g_command[MAX_CMD_SIZE] = {0};
int g_data_num = RECORD_NUM_003;
pthread_mutex_t g_threadLock;
#define MAX_NAME_LENGTH 128
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";
GmcCheckInfoT *checkInfo, *checkInfo2;
GmcCheckStatusE checkStatus, checkStatus2;
bool isAbnormal = false;
#define FULLTABLE 0

static const char *gResPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type" : 0
    })";
static const char *resPoolTestName = "resource_pool_test";

#define TEST_INFO(info, recordId, mod, threadId)                                                   \
    do {                                                                                           \
        if (0) {                                                                                   \
            if (recordId % mod == 0) {                                                             \
                fprintf(stdout, "[%s] record Id : %d, threadId : %d\n", info, recordId, threadId); \
            }                                                                                      \
        }                                                                                          \
    } while (0)

using namespace std;

void test_set_simple_table(GmcStmtT *stmt, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;

    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value, sizeof(F15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void test_set_simple_table_root(GmcStmtT *stmt, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value, sizeof(F15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void test_read_simple_table(GmcStmtT *stmt, int index, char *label_name)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &F0Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &F2Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT8, &F6Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_TIME, &F11Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_FIXED, F14Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_struct_read_simple_table(TEST_T1_struct_t *d, int index, char *label_name)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
#if NOT_EXIST
    printf("F0Value : %lld\n", d->F0);
    printf("F1Value : %lld\n", d->F1);
    printf("F2Value : %ld\n", d->F2);
    printf("F3Value : %ld\n", d->F3);
    printf("F4Value : %hd\n", d->F4);
    printf("F5Value : %hd\n", d->F5);
    printf("F6Value : %u\n", d->F6);
    printf("F7Value : %u\n", d->F7);
    printf("F8Value : %d\n", d->F8);
    printf("F9Value : %f\n", d->F9);
    printf("F10Value : %lf\n", d->F10);
    printf("F11Value : %lld\n", d->F11);
    printf("F12Value : %c\n", d->F12);
    printf("F13Value : %c\n", d->F13);
    printf("F14Value : %s\n", d->F14);
    printf("F15Value : %u\n", d->F15);
#endif
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_struct_read_simple_table_root2(TEST_T1_struct_t *d, int index, char *label_name)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
#if NOT_EXIST
    printf("F0Value : %lld\n", d->F0);
    printf("F1Value : %lld\n", d->F1);
    printf("F2Value : %ld\n", d->F2);
    printf("F3Value : %ld\n", d->F3);
    printf("F4Value : %hd\n", d->F4);
    printf("F5Value : %hd\n", d->F5);
    printf("F6Value : %u\n", d->F6);
    printf("F7Value : %u\n", d->F7);
    printf("F8Value : %d\n", d->F8);
    printf("F9Value : %f\n", d->F9);
    printf("F10Value : %lf\n", d->F10);
    printf("F11Value : %lld\n", d->F11);
    printf("F12Value : %c\n", d->F12);
    printf("F13Value : %c\n", d->F13);
    printf("F14Value : %s\n", d->F14);
    printf("F15Value : %u\n", d->F15);
#endif
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void db_test_write_simple_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("INSERT", i, 1000, 0);
        test_set_simple_table(stmt, i, false);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}
//普通同步操作
void db_test_struct_write_simple_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 10000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_merge_simple_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_simple_table(stmt, i, false);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_replace_simple_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_simple_table(stmt, i, false);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
        }
    }
}

void db_test_struct_merge_simple_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_struct_replace_simple_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 10000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
        }
    }
}
void db_test_struct_replace_simple_table_root2(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_T1_value_root2(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_struct_delete_simple_table(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT DELETE", i, 10000, 0);
        if (strcmp(label_name, g_table_name) == 0 || strcmp(label_name, g_table_name8) == 0 ||
            strcmp(label_name, g_table_name9) == 0) {
            test_set_TEST_T1_primary_key(&obj, i);
        } else if (strcmp(label_name, g_table_name4) == 0) {
            test_set_TEST_T4_primary_key(&obj, i);
        } else {
            test_set_TEST_T1_primary_key(&obj, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
        }
    }
}

void db_test_delete_simple_table(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("DELETE", i, 1000, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
        }
    }
}

void db_test_read_simple_table(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 10000, 0);
        if (strcmp(label_name, g_table_name) == 0 || strcmp(label_name, g_table_name9) == 0 ||
            strcmp(label_name, g_table_name3) == 0) {
            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else if (strcmp(label_name, g_table_name4) == 0) {
            int64_t f0_value = i;
            uint64_t f1_value = (uint64_t)i + 0xFFFFFFFF;
            int32_t f2_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &f1_value, sizeof(f1_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &f2_value, sizeof(f2_value));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_simple_table(stmt, i, label_name);
            if (strcmp(label_name, g_table_name3) == 0) {
                bool isNull;
                uint32_t resSize;
                uint64_t resVal;
                ret = GmcGetVertexPropertySizeByName(stmt, "F16", &resSize);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = GmcGetVertexPropertyByName(stmt, "F16", &resVal, resSize, &isNull);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

                uint16_t tmpPoolId, tmpCount;
                uint32_t tmpStartIndex;
                ret = GmcGetPoolIdResource(resVal, &tmpPoolId);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = GmcGetCountResource(resVal, &tmpCount);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                ret = GmcGetStartIdxResource(resVal, &tmpStartIndex);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

                // poolId修改为随机分配 AW_MACRO_ASSERT_EQ_INT(0, tmpPoolId)
                AW_MACRO_ASSERT_EQ_INT(1, tmpCount);
                AW_MACRO_ASSERT_EQ_INT(i, tmpStartIndex);
            }
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
}
void db_test_read_simple_table_concurrency(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 1000, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_simple_table(stmt, i, label_name);
            cnt++;
        }
    }
}

void db_test_read_simple_table_localhash(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 1000, 0);
        uint64_t f1_value = (uint64_t)i + 0xFFFFFFFF;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_simple_table(stmt, i, label_name);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
}

void db_test_read_simple_table_hashcluster(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 1000, 0);
        int32_t f2_value = (int32_t)i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f2_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_simple_table(stmt, i, label_name);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
}

void db_test_update_simple_table_localhash(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i;
    for (i = 0; i < data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TEST_INFO("LOCALHASH UPDATE", i, 500, 0);
        uint64_t f1_value = (uint64_t)i + 0xFFFFFFFF;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int32_t f2_value = i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_update_simple_table_localhash_full(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i;
    for (i = 0; i < data_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TEST_INFO("LOCALHASH UPDATE", i, 500, 0);
        uint64_t f1_value = (uint64_t)i + 0xFFFFFFFF;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        test_set_simple_table_root(stmt, i, false);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_delete_simple_table_localkey(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TEST_INFO("LOCALKEY DELETE", i, 500, 0);

    unsigned int l_val_del = 0;
    unsigned int r_val_del = data_num;
    unsigned int arrLen = 1;

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[0].value = &l_val_del;
    leftKeyProps_del[0].size = sizeof(l_val_del);

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[0].value = &r_val_del;
    rightKeyProps_del[0].size = sizeof(r_val_del);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps_del[0];
    items[0].rValue = &rightKeyProps_del[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;

    // 设置删除扫描
    ret = GmcSetKeyRange(stmt, items, arrLen);

    ret = GmcSetIndexKeyName(stmt, keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
}
void db_test_struct_read_simple_table(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 10000, 0);
        if (strcmp(label_name, g_table_name) == 0 || strcmp(label_name, g_table_name9) == 0 ||
            strcmp(label_name, g_table_name10) == 0 || strcmp(label_name, g_table_name3) == 0) {
            test_set_TEST_T1_primary_key(&obj, i);
        } else if (strcmp(label_name, g_table_name4) == 0) {
            test_set_TEST_T4_primary_key(&obj, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            test_struct_read_simple_table(&obj, i, label_name);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_read_simple_table_concurrency(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            test_struct_read_simple_table(&obj, i, label_name);
            cnt++;
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_scan_simple_table(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TEST_INFO("SCAN", i, 1000, 0);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        // 聚簇容器变更，不关注顺序
        cnt++;
    }
    if (read_num) {
        AW_MACRO_EXPECT_EQ_INT(data_num, cnt);
    } else {
        AW_MACRO_EXPECT_EQ_INT(0, cnt);
    }
}

void db_test_scan_simple_table_concurrency(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TEST_INFO("SCAN", i, 1000, 0);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
    }
}

void db_test_struct_scan_simple_table(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 聚簇容器变更，不关注顺序
        cnt++;
    }
    if (read_num) {
        AW_MACRO_EXPECT_EQ_INT(data_num, cnt);
    } else {
        AW_MACRO_EXPECT_EQ_INT(0, cnt);
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_scan_simple_table_concurrency(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        cnt++;
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_read_simple_table_root2(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        if (strcmp(label_name, g_table_name) == 0 || strcmp(label_name, g_table_name9) == 0) {
            test_set_TEST_T1_primary_key(&obj, i);
        } else if (strcmp(label_name, g_table_name4) == 0) {
            test_set_TEST_T4_primary_key(&obj, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            test_struct_read_simple_table_root2(&obj, i, label_name);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

//批量同步操作
void db_test_struct_write_simple_table_batch(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (j = 0; j < data_num / num_per_batch; j++) {
        TEST_INFO("STRUCT BATCH INSERT", j, 100, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_value(&obj, num_per_batch * j + i, false);
            ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, successNum);
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(data_num, count);
}

void db_test_struct_merge_simple_table_batch(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT BATCH INSERT", i, 1, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(totalNum, data_num);
    AW_MACRO_EXPECT_EQ_INT(successNum, data_num);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(data_num, count);
}

void db_test_struct_replace_simple_table_batch(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (j = 0; j < data_num / num_per_batch; j++) {
        TEST_INFO("STRUCT BATCH REPLACE", j, 100, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_value(&obj, num_per_batch * j + i, false);
            ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(num_per_batch, totalNum);
        AW_MACRO_EXPECT_EQ_INT(num_per_batch, successNum);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(data_num, count);
}

void db_test_struct_delete_simple_table_batch(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (j = 0; j < data_num / num_per_batch; j++) {
        TEST_INFO("STRUCT BATCH DELETE", j, 100, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_primary_key(&obj, num_per_batch * j + i);
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(num_per_batch, totalNum);
        AW_MACRO_EXPECT_EQ_INT(num_per_batch, successNum);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, count);
}

void db_test_struct_write_big_obj_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T6_struct_t *obj = (TEST_T6_struct_t *)malloc(sizeof(TEST_T6_struct_t));
    memset(obj, 0, sizeof(TEST_T6_struct_t));
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 500, 0);
        test_set_TEST_T6_value(obj, i);
        ret = testStructSetVertexWithBuf(stmt, obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
    free(obj);
}

void db_test_struct_merge_big_obj_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T6_struct_t obj = (TEST_T6_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT MERGE", i, 500, 0);
        test_set_TEST_T6_value(&obj, i);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_struct_replace_big_obj_table(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T6_struct_t *obj = (TEST_T6_struct_t *)malloc(sizeof(TEST_T6_struct_t));
    memset(obj, 0, sizeof(TEST_T6_struct_t));
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT REPLACE", i, 500, 0);
        test_set_TEST_T6_value(obj, i);
        ret = testStructSetVertexWithBuf(stmt, obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
    free(obj);
}

void test_read_big_obj_table(GmcStmtT *stmt, int index, char *label_name)
{
    int ret, i;
    char field_value[STRING_MAX_SIZE] = {0};
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    for (i = 0; i < STRING_MAX_SIZE - 1; i++) {
        field_value[i] = 'A';
    }

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &F0Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &F2Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_FIXED, field_value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_read_big_obj_table(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 500, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_big_obj_table(stmt, i, label_name);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
}

void test_struct_read_big_obj_table(TEST_T6_struct_t *d, int index, char *label_name)
{
    int ret, i;
    char field_value[STRING_MAX_SIZE] = {0};
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    for (i = 0; i < STRING_MAX_SIZE - 1; i++) {
        field_value[i] = 'A';
    };

    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F4, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F5, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F6, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F7, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F8, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F9, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F10, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F11, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F12, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F13, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F14, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F15, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F16, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F17, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F18, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F19, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F20, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_struct_read_big_obj_table(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T6_struct_t *obj = (TEST_T6_struct_t *)malloc(sizeof(TEST_T6_struct_t));
    memset(obj, 0, sizeof(TEST_T6_struct_t));

    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    testStructSetDeseri(stmt, obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 500, 0);
        test_set_TEST_T6_primary_key(obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, obj, 0, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            test_struct_read_big_obj_table(obj, i, label_name);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    free(obj);
}

void db_test_struct_delete_big_obj_table(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T6_struct_t *obj = (TEST_T6_struct_t *)malloc(sizeof(TEST_T6_struct_t));
    memset(obj, 0, sizeof(TEST_T6_struct_t));
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT DELETE", i, 500, 0);
        test_set_TEST_T6_primary_key(obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, obj, 0, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
    free(obj);
}

// 异步批量结构化写
void db_test_struct_write_simple_table_batch_async(
    GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (j = 0; j < data_num / num_per_batch; j++) {
        TEST_INFO("STRUCT BATCH INSERT ASYNC", j, 100, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_value(&obj, num_per_batch * j + i, false);
            ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(num_per_batch, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(num_per_batch, data.succNum);
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

// 异步批量结构化写
void db_test_struct_merge_simple_table_batch_async(
    GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT BATCH MERGE ASYNC", i, 500, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(data_num, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(data_num, data.succNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

// 异步批量结构化写
void db_test_struct_replace_simple_table_batch_async(
    GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (j = 0; j < data_num / num_per_batch; j++) {
        TEST_INFO("STRUCT BATCH REPLACE ASYNC", j, 100, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_value(&obj, num_per_batch * j + i, false);
            ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(num_per_batch, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(num_per_batch, data.succNum);
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

// 异步单个结构化写
void db_test_struct_write_simple_table_async(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT ASYNC", i, 10000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, data.affectRows);
    }
}

// 异步单个结构化写
void db_test_struct_merge_simple_table_async(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT MERGE ASYNC", i, 10000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = merge_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, data.affectRows);
    }
}

// 异步单个结构化写
void db_test_struct_replace_simple_table_async(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT REPLACE ASYNC", i, 10000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = replace_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, data.affectRows);
    }
}

// 异步单个结构化删
void db_test_struct_delete_simple_table_async(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT DELETE ASYNC", i, 10000, 0);
        test_set_TEST_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, data.affectRows);
    }
}

// 异步批量结构化删
void db_test_struct_delete_simple_table_batch_async(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (j = 0; j < data_num / num_per_batch; j++) {
        TEST_INFO("STRUCT DELETE BATCH ASYNC", j, 10000, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_primary_key(&obj, num_per_batch * j + i);
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

void test_read_TEST_T8(GmcStmtT *stmt, int index, char *label_name)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index + 100;  // 自增列起始值为100
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &F0Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &F2Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT8, &F6Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_TIME, &F11Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_FIXED, F14Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_read_TEST_T8(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("SCAN", i, 1000, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_TEST_T8(stmt, i, label_name);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
}

void test_struct_read_TEST_T8(TEST_T1_struct_t *d, int index, char *label_name)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index + 100;  // 自增列起始值为100
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;

    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_struct_read_TEST_T8(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            test_struct_read_TEST_T8(&obj, i, label_name);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_write_TEST_T8(GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_T8_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void test_set_TEST_T1_value_partition(TEST_T1_struct_t *d, int v, bool bool_value)
{
    d->F0 = (int64_t)v;                                          // int64   8
    d->F1 = (uint64_t)v + 0xFFFFFFFF;                            // uint64  8
    d->F2 = v;                                                   // int32   4
    d->F3 = v;                                                   // uint32  4
    d->F4 = v & 0x7FFF;                                          // int16   2
    d->F5 = v & 0xFFFF;                                          // uint16  2
    d->F6 = v & 0x7F;                                            // int8    1
    d->F7 = v & 0xFF;                                            // uint8   1
    d->F8 = bool_value;                                          // boolean 4
    d->F9 = v;                                                   // float   4
    d->F10 = v;                                                  // double  8
    d->F11 = v + 0xFFFFFFFF;                                     // time    8
    d->F12 = 'a' + (v & 0x1A);                                   // char     1
    d->F13 = 'A' + (v & 0x1A);                                   // uchar   1
    snprintf((char *)d->F14, sizeof(d->F14), "aaaaaaa%08d", v);  // fixed  16
    d->F15 = 100;                                                // partition  1
}

void db_test_struct_replace_simple_table_partition(
    GmcStmtT *stmt, int data_num, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_T1_value_partition(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_struct_read_res_table(GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T3_struct_t obj = (TEST_T3_struct_t){0};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_T3_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint64_t resVal = obj.F16;

            uint16_t tmpPoolId, tmpCount;
            uint32_t tmpStartIndex;
            ret = GmcGetPoolIdResource(resVal, &tmpPoolId);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetCountResource(resVal, &tmpCount);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetStartIdxResource(resVal, &tmpStartIndex);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

            // poolId修改为随机分配 AW_MACRO_ASSERT_EQ_INT(0, tmpPoolId)
            AW_MACRO_ASSERT_EQ_INT(1, tmpCount);
            AW_MACRO_ASSERT_EQ_INT(i, tmpStartIndex);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_get_vertex_count_simple_table(
    GmcStmtT *stmt, int data_num, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    for (i = 0; i < data_num; i++) {
        TEST_INFO("STRUCT GmcGetVertexCount", i, 1000, 0);
        test_set_TEST_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t count = 0;
        ret = GmcGetVertexCount(stmt, label_name, keyName, &count);
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, count);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, count);
        }
    }
}
