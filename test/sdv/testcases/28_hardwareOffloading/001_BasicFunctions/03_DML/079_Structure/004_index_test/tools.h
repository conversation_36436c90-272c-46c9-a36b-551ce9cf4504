//
// Created by w00495442 on 2021/11/30.
//
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "table_struct.h"

#define MAX_CMD_SIZE 1024
GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL, *g_conn_sub = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL, *g_stmt_sub = NULL;
char *g_schema = NULL;
int affectRows;
char g_table_name[] = "TEST_T1", g_table_name2[] = "TEST_T2", g_table_name3[] = "TEST_T3", g_table_name4[] = "TEST_T4",
     g_table_name5[] = "TEST_T5", g_table_name6[] = "TEST_T6";
char g_pk_name[] = "TEST_PK";
char g_uniq_localhash_name[] = "uniq_localhash";
char g_nonuniq_localhash_name[] = "nonuniq_localhash";
char g_uniq_hashcluster_name[] = "uniq_hashcluster";
char g_nonuniq_hashcluster_name[] = "nonuniq_hashcluster";
char g_local_name[] = "local";
char g_lpm4_name[] = "lpm4";
char g_lpm6_name[] = "lpm6";
char g_command[MAX_CMD_SIZE] = {0};
int g_data_num = RECORD_NUM_003;
pthread_mutex_t g_threadLock;
#define MAX_NAME_LENGTH 128
const char *g_subConnName = "subConnName";
const char *g_subName = "subVertexLabel";

#define TEST_INFO(info, recordId, mod, threadId)                                                   \
    do {                                                                                           \
        if (1) {                                                                                   \
            if (recordId % mod == 0) {                                                             \
                fprintf(stdout, "[%s] record Id : %d, threadId : %d\n", info, recordId, threadId); \
            }                                                                                      \
        }                                                                                          \
    } while (0)

using namespace std;

/************************************** simple table **************************************/

void test_set_vertex_pk(GmcStmtT *stmt, int index)
{
    int ret;
    int64_t F0Value = index;

    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_set_vertex_uniq_key(GmcStmtT *stmt, int index)
{
    int ret;
    uint32_t F3Value = index;

    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_set_vertex_nonuniq_key(GmcStmtT *stmt, int index)
{
    int ret;
    int32_t F2Value = index;

    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_set_vertex_property(
    GmcStmtT *stmt, int index, bool bool_value, bool isUpdate, bool withLPM4 = false, bool withLPM6 = false)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;

    //    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!isUpdate) {
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value, sizeof(F15Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (withLPM4) {
        uint32_t vr_id = 0;
        uint32_t vrf_index = 0;
        uint32_t dest_ip_addr_lpm4 = index;
        uint8_t mask_len = 32;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            stmt, "dest_ip_addr_lpm4", GMC_DATATYPE_UINT32, &dest_ip_addr_lpm4, sizeof(dest_ip_addr_lpm4));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (withLPM6) {
        uint32_t vr_id = 0;
        uint32_t vrf_index = 0;
        char dest_ip_addr_lpm6[16] = {0};
        snprintf((char *)dest_ip_addr_lpm6, sizeof(dest_ip_addr_lpm6), "aaaaaaa%08d", index);
        uint8_t mask_len = 128;
        ret = GmcSetVertexProperty(stmt, "vr_id", GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "vrf_index", GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            stmt, "dest_ip_addr_lpm6", GMC_DATATYPE_FIXED, dest_ip_addr_lpm6, sizeof(dest_ip_addr_lpm6));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "mask_len", GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

void db_test_struct_merge_simple_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, uint32_t keyId, int expected_affectRows, bool isUpdate, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT MERGE", i, 1000, threadId);
        test_set_TEST_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isUniq) {  // 非唯一索引字段是否构造冲突链
            test_set_vertex_nonuniq_key(stmt, i + delta);
        } else {
            test_set_vertex_nonuniq_key(stmt, 0);
        }
        test_set_vertex_uniq_key(stmt, i + delta);
        test_set_vertex_property(stmt, i + delta, bool_value, isUpdate);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_struct_merge_simple_table_batch(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, int expected_affectRows, bool isUpdate, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT MERGE", i, 1000, threadId);
        test_set_TEST_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isUniq) {  // 非唯一索引字段是否构造冲突链
            test_set_vertex_nonuniq_key(stmt, i + delta);
        } else {
            test_set_vertex_nonuniq_key(stmt, 0);
        }
        test_set_vertex_uniq_key(stmt, i + delta);
        test_set_vertex_property(stmt, i + delta, bool_value, isUpdate);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(end - start, totalNum);
    AW_MACRO_EXPECT_EQ_INT(end - start, successNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

void test_read_simple_table(GmcStmtT *stmt, int index, int delta, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)(index + delta);
    uint64_t F1Value = (uint64_t)(index + delta) + 0xFFFFFFFF;
    int32_t F2Value = index + delta;
    uint32_t F3Value = index + delta;
    int16_t F4Value = (index + delta) & 0x7FFF;
    uint16_t F5Value = (index + delta) & 0xFFFF;
    int8_t F6Value = (index + delta) & 0x7F;
    uint8_t F7Value = (index + delta) & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index + delta;
    double F10Value = index + delta;
    uint64_t F11Value = (index + delta) + 0xFFFFFFFF;
    char F12Value = 'a' + ((index + delta) & 0x1A);
    unsigned char F13Value = 'A' + ((index + delta) & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index + delta);
    uint8_t F15Value = (index + delta) & 0xF;

    //    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &F0Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &F2Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT8, &F6Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_TIME, &F11Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_FIXED, F14Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_read_simple_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, char *keyName, bool read_num, int threadId = 0)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("SCAN", i, 1000, threadId);
            if (strcmp(keyName, g_pk_name) == 0) {
                int64_t f0_value = i;
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                uint32_t f3_value = i;
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &f3_value, sizeof(f3_value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            ret = GmcSetIndexKeyName(stmt, keyName);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish = false;
            int cnt = 0;
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }
                test_read_simple_table(stmt, i, delta, bool_value);
                cnt++;
            }
            if (read_num) {
                AW_MACRO_EXPECT_EQ_INT(1, cnt);
            } else {
                AW_MACRO_EXPECT_EQ_INT(0, cnt);
            }
        }
    } else {
        TEST_INFO("NON UNIQUE SCAN", 0, 1, threadId);
        int32_t f2_value = 0;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f2_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(end - start, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
}

void test_struct_read_simple_table(TEST_T1_struct_t *d, int index, int delta, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)(index + delta);
    uint64_t F1Value = (uint64_t)(index + delta) + 0xFFFFFFFF;
    int32_t F2Value = index + delta;
    uint32_t F3Value = index + delta;
    int16_t F4Value = (index + delta) & 0x7FFF;
    uint16_t F5Value = (index + delta) & 0xFFFF;
    int8_t F6Value = (index + delta) & 0x7F;
    uint8_t F7Value = (index + delta) & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index + delta;
    double F10Value = index + delta;
    uint64_t F11Value = (index + delta) + 0xFFFFFFFF;
    char F12Value = 'a' + ((index + delta) & 0x1A);
    unsigned char F13Value = 'A' + ((index + delta) & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index + delta);
    uint8_t F15Value = (index + delta) & 0xF;

    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_struct_read_simple_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, uint32_t keyId, bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT SCAN", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T1_primary_key(&obj, i);
            } else {
                test_set_TEST_T1_uniq_key(&obj, i);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish = false;
            int cnt = 0;
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }
                ret = testStructGetVertexDeseri(stmt, &deseri);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                test_struct_read_simple_table(&obj, i, delta, bool_value);
                cnt++;
            }
            if (read_num) {
                AW_MACRO_EXPECT_EQ_INT(1, cnt);
            } else {
                AW_MACRO_EXPECT_EQ_INT(0, cnt);
            }
        }
    } else {
        TEST_INFO("NON UNIQUE STRUCT SCAN", 0, 1, threadId);
        test_set_TEST_T1_nonuniq_key(&obj, 0);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(end - start, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void test_struct_get_vertex_buf_simple_table(GmcStructBufferT *inputBufInfo, int index, int delta, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)(index + delta);
    uint64_t F1Value = (uint64_t)(index + delta) + 0xFFFFFFFF;
    int32_t F2Value = index + delta;
    uint32_t F3Value = index + delta;
    int16_t F4Value = (index + delta) & 0x7FFF;
    uint16_t F5Value = (index + delta) & 0xFFFF;
    int8_t F6Value = (index + delta) & 0x7F;
    uint8_t F7Value = (index + delta) & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index + delta;
    double F10Value = index + delta;
    uint64_t F11Value = (index + delta) + 0xFFFFFFFF;
    char F12Value = 'a' + ((index + delta) & 0x1A);
    unsigned char F13Value = 'A' + ((index + delta) & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index + delta);
    uint8_t F15Value = (index + delta) & 0xF;

    TEST_T1_struct_t *obj = (TEST_T1_struct_t *)inputBufInfo->buf;

    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &obj->F0, sizeof(F0Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &obj->F1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &obj->F2, sizeof(F2Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &obj->F3, sizeof(F3Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &obj->F4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &obj->F5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &obj->F6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &obj->F7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &obj->F8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &obj->F9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &obj->F10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &obj->F11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &obj->F12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &obj->F13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, obj->F14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &obj->F15, sizeof(F15Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_struct_get_vertex_buf_simple_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};
    GmcSeriT keySeri = (GmcSeriT){0};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (keyId == 0) {  // 主键
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT GetVertexBuf SCAN", i, 1000, threadId);
            test_set_TEST_T1_primary_key(&obj, i);
            ret = testStructGetVertexBuf(stmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
            if (read_num) {
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                test_struct_get_vertex_buf_simple_table(&inputBufInfo, i, delta, bool_value);
            } else {
                AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
            }
        }
    } else {           // 非主键取fetch后的第一条数据
        if (isUniq) {  // 非主键 唯一索引
            for (i = start; i < end; i++) {
                TEST_INFO("STRUCT GetVertexBuf SCAN", i, 1000, threadId);
                test_set_TEST_T1_uniq_key(&obj, i);
                ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcExecute(stmt);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                bool isFinish = false;
                int cnt = 0;
                while (!isFinish) {
                    ret = GmcFetch(stmt, &isFinish);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    if (isFinish) {
                        break;
                    }
                    ret = testStructGetVertexBuf(stmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_get_vertex_buf_simple_table(&inputBufInfo, i, delta, bool_value);
                    cnt++;
                }
                if (read_num) {
                    AW_MACRO_EXPECT_EQ_INT(1, cnt);
                } else {
                    AW_MACRO_EXPECT_EQ_INT(0, cnt);
                }
            }
        } else {  // 非主键 非唯一索引
            TEST_INFO("NON UNIQUE STRUCT GetVertexBuf SCAN", 0, 1000, threadId);
            test_set_TEST_T1_nonuniq_key(&obj, 0);
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish = false;
            int cnt = 0;
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }
                ret = testStructGetVertexBuf(stmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                cnt++;
            }
            if (read_num) {
                AW_MACRO_EXPECT_EQ_INT(end - start, cnt);
            } else {
                AW_MACRO_EXPECT_EQ_INT(0, cnt);
            }
        }
    }
}

void db_test_struct_get_vertex_count_simple_table(GmcStmtT *stmt, int start, int end, bool isUniq, char *label_name,
    char *keyName, uint32_t keyId, bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT GmcGetVertexCount", i, 1000, 0);
            if (keyId == 0) {
                test_set_TEST_T1_primary_key(&obj, i);
            } else {
                test_set_TEST_T1_uniq_key(&obj, i);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            uint64_t count = 0;
            ret = GmcGetVertexCount(stmt, label_name, keyName, &count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (read_num) {
                AW_MACRO_EXPECT_EQ_INT(1, count);
            } else {
                AW_MACRO_EXPECT_EQ_INT(0, count);
            }
        }
    } else {
        TEST_INFO("NONUNIQ STRUCT GmcGetVertexCount", i, 1000, 0);
        test_set_TEST_T1_nonuniq_key(&obj, 0);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t count = 0;
        ret = GmcGetVertexCount(stmt, label_name, keyName, &count);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(end - start, count);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, count);
        }
    }
}

// 主键, localhash, hashcluster
void db_test_read_all_simple_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, char *keyName, uint32_t keyId, bool read_num, int threadId = 0)
{
    // 非结构化读
    db_test_read_simple_table(stmt, start, end, delta, isUniq, bool_value, label_name, keyName, read_num, threadId);

    // 结构化读(GmcGetVertexDeseri)
    db_test_struct_read_simple_table(
        stmt, start, end, delta, isUniq, bool_value, label_name, keyId, read_num, threadId);

    // 结构化读(GmcGetVertexBuf)
    db_test_struct_get_vertex_buf_simple_table(
        stmt, start, end, delta, isUniq, bool_value, label_name, keyId, read_num, threadId);

    db_test_struct_get_vertex_count_simple_table(
        stmt, start, end, isUniq, label_name, keyName, keyId, read_num, threadId);
}

// 非结构化local范围扫描
void db_test_read_local_range_simple_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, char *keyName, bool lKeyIsNull, bool rKeyIsNull, GmcOrderDirectionE order, bool read_num,
    int threadId = 0)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        TEST_INFO("SCAN", 0, 1, threadId);

        unsigned int l_val;
        unsigned int r_val;
        unsigned int arrLen = 1;

        if (lKeyIsNull) {  // 左值为空, 左边全扫, leftKey为NULL
            l_val = start;
        } else {  // 左值不为空, 左边范围扫
            l_val = start + 1;
        }

        if (rKeyIsNull) {  // 右值为空, 右边全扫, rightKey为NULL
            r_val = end - 1;
        } else {  // 右值不为空, 右边范围扫
            r_val = end - 2;
        }

        GmcPropValueT *leftKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        leftKeyProps[0].type = GMC_DATATYPE_UINT32;
        leftKeyProps[0].value = &l_val;
        leftKeyProps[0].size = sizeof(l_val);

        GmcPropValueT *rightKeyProps = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
        rightKeyProps[0].type = GMC_DATATYPE_UINT32;
        rightKeyProps[0].value = &r_val;
        rightKeyProps[0].size = sizeof(r_val);

        GmcRangeItemT items[arrLen];
        items[0].lValue = &leftKeyProps[0];
        items[0].rValue = &rightKeyProps[0];
        items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].order = order;

        if (order == GMC_ORDER_ASC) {
            i = l_val;
        } else {
            i = r_val;
        }

        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetKeyRange(stmt, items, arrLen);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            if (order == GMC_ORDER_ASC) {
                test_read_simple_table(stmt, i++, delta, bool_value);
            } else {
                test_read_simple_table(stmt, i--, delta, bool_value);
            }
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(r_val - l_val + 1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
        free(leftKeyProps);
        free(rightKeyProps);
    } else {
        printf("NON UNIQ LOCAL KEY\n");
    }
}

// 结构化local范围扫描
void db_test_struct_read_local_range_simple_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, bool lKeyIsNull, bool rKeyIsNull, GmcOrderDirectionE order,
    bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T1_struct_t lKey = (TEST_T1_struct_t){0};
    TEST_T1_struct_t rKey = (TEST_T1_struct_t){0};
    TEST_T1_struct_t *leftKey = NULL;
    TEST_T1_struct_t *rightKey = NULL;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        TEST_INFO("STRUCT SCAN", 0, 1, threadId);

        unsigned int l_val;
        unsigned int r_val;
        unsigned int arrLen = 1;

        if (lKeyIsNull) {  // 左值为空, 左边全扫, leftKey为NULL
            l_val = start;
        } else {  // 左值不为空, 左边范围扫
            l_val = start + 1;
            // 设置左key
            leftKey = &lKey;
            test_set_TEST_T1_uniq_key(leftKey, l_val);
        }

        if (rKeyIsNull) {  // 右值为空, 右边全扫, rightKey为NULL
            r_val = end - 1;
        } else {  // 右值不为空, 右边范围扫
            r_val = end - 2;
            // 设置右key
            rightKey = &rKey;
            test_set_TEST_T1_uniq_key(rightKey, r_val);
        }

        GmcRangeItemFlagT items[arrLen];
        items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].order = order;

        if (order == GMC_ORDER_ASC) {
            i = l_val;
        } else {
            i = r_val;
        }

        ret = testStructSetKeyRangeStructure(stmt, leftKey, rightKey, items, arrLen, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (order == GMC_ORDER_ASC) {
                test_struct_read_simple_table(&obj, i++, delta, bool_value);
            } else {
                test_struct_read_simple_table(&obj, i--, delta, bool_value);
            }
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(r_val - l_val + 1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    } else {
        printf("NON UNIQ LOCAL KEY\n");
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

// 结构化local范围扫描(GetVertexBuf)
void db_test_struct_get_vertex_buf_local_range_simple_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, bool lKeyIsNull, bool rKeyIsNull, GmcOrderDirectionE order,
    bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T1_struct_t lKey = (TEST_T1_struct_t){0};
    TEST_T1_struct_t rKey = (TEST_T1_struct_t){0};
    TEST_T1_struct_t *leftKey = NULL;
    TEST_T1_struct_t *rightKey = NULL;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};
    GmcSeriT keySeri = (GmcSeriT){0};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        TEST_INFO("STRUCT GetVertexBuf SCAN", 0, 1, threadId);

        unsigned int l_val;
        unsigned int r_val;
        unsigned int arrLen = 1;

        if (lKeyIsNull) {  // 左值为空, 左边全扫, leftKey为NULL
            l_val = start;
        } else {  // 左值不为空, 左边范围扫
            l_val = start + 1;
            // 设置左key
            leftKey = &lKey;
            test_set_TEST_T1_uniq_key(leftKey, l_val);
        }

        if (rKeyIsNull) {  // 右值为空, 右边全扫, rightKey为NULL
            r_val = end - 1;
        } else {  // 右值不为空, 右边范围扫
            r_val = end - 2;
            // 设置右key
            rightKey = &rKey;
            test_set_TEST_T1_uniq_key(rightKey, r_val);
        }

        GmcRangeItemFlagT items[arrLen];
        items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].order = order;

        if (order == GMC_ORDER_ASC) {
            i = l_val;
        } else {
            i = r_val;
        }

        ret = testStructSetKeyRangeStructure(stmt, leftKey, rightKey, items, arrLen, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexBuf(stmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (order == GMC_ORDER_ASC) {
                test_struct_get_vertex_buf_simple_table(&inputBufInfo, i++, delta, bool_value);
            } else {
                test_struct_get_vertex_buf_simple_table(&inputBufInfo, i--, delta, bool_value);
            }
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(r_val - l_val + 1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    } else {
        printf("NON UNIQ LOCAL KEY\n");
    }
}

// local范围
void db_test_read_local_range_all_simple_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, char *keyName, uint32_t keyId, bool lKeyIsNull, bool rKeyIsNull,
    GmcOrderDirectionE order, bool read_num, int threadId = 0)
{
    // 非结构化读
    db_test_read_local_range_simple_table(stmt, start, end, delta, isUniq, bool_value, label_name, keyName, lKeyIsNull,
        rKeyIsNull, order, read_num, threadId);

    // 结构化读(GmcGetVertexDeseri)
    db_test_struct_read_local_range_simple_table(stmt, start, end, delta, isUniq, bool_value, label_name, keyId,
        lKeyIsNull, rKeyIsNull, order, read_num, threadId);

    // 结构化读(GmcGetVertexBuf)
    db_test_struct_get_vertex_buf_local_range_simple_table(stmt, start, end, delta, isUniq, bool_value, label_name,
        keyId, lKeyIsNull, rKeyIsNull, order, read_num, threadId);

    db_test_struct_get_vertex_count_simple_table(
        stmt, start + 1, end - 1, isUniq, label_name, keyName, keyId, read_num, threadId);
}

void db_test_struct_update_simple_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, uint32_t keyId, int expected_affectRows, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT UPDATE", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T1_primary_key(&obj, i);
            } else {
                test_set_TEST_T1_uniq_key(&obj, i);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (keyId == 0) {  // 使用主键时才更新唯一二级索引字段
                test_set_vertex_uniq_key(stmt, i + delta);
                test_set_vertex_nonuniq_key(stmt, i + delta);
            }
            test_set_vertex_property(stmt, i + delta, bool_value, true);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
        }
    } else {
        TEST_INFO("NON UNIQUE STRUCT UPDATE", 0, 1, threadId);
        test_set_TEST_T1_nonuniq_key(&obj, 0);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        test_set_vertex_property(stmt, end + delta, bool_value, true);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

// 主键 localhash hashcluster
void db_test_struct_delete_simple_table(GmcStmtT *stmt, int start, int end, bool isUniq, char *label_name,
    uint32_t keyId, int expected_affectRows, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT DELETE", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T1_primary_key(&obj, i);
            } else {
                test_set_TEST_T1_uniq_key(&obj, i);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
        }
    } else {
        TEST_INFO("NON UNIQUE STRUCT DELETE", 0, 1, threadId);
        test_set_TEST_T1_nonuniq_key(&obj, 0);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

// local
void db_test_struct_delete_local_range_simple_table(GmcStmtT *stmt, int start, int end, bool isUniq, char *label_name,
    uint32_t keyId, bool lKeyIsNull, bool rKeyIsNull, int expected_affectRows, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T1_struct_t lKey = (TEST_T1_struct_t){0};
    TEST_T1_struct_t rKey = (TEST_T1_struct_t){0};
    TEST_T1_struct_t *leftKey = NULL;
    TEST_T1_struct_t *rightKey = NULL;
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        TEST_INFO("STRUCT DELETE", 0, 1000, threadId);

        unsigned int l_val;
        unsigned int r_val;
        unsigned int arrLen = 1;

        if (lKeyIsNull) {  // 左值为空, 左边全扫, leftKey为NULL
            l_val = start;
        } else {  // 左值不为空, 左边范围扫
            l_val = start + 1;
            // 设置左key
            leftKey = &lKey;
            test_set_TEST_T1_uniq_key(leftKey, l_val);
        }

        if (rKeyIsNull) {  // 右值为空, 右边全扫, rightKey为NULL
            r_val = end - 1;
        } else {  // 右值不为空, 右边范围扫
            r_val = end - 2;
            // 设置右key
            rightKey = &rKey;
            test_set_TEST_T1_uniq_key(rightKey, r_val);
        }

        GmcRangeItemFlagT items[arrLen];
        items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
        items[0].order = GMC_ORDER_ASC;

        ret = testStructSetKeyRangeStructure(stmt, leftKey, rightKey, items, arrLen, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(r_val - l_val + 1, affectRows);
    } else {
        printf("NON UNIQ LOCAL KEY\n");
    }
}

/************************************** lpm4 table **************************************/

void db_test_struct_merge_lpm4_table(GmcStmtT *stmt, int start, int end, int delta, bool bool_value, char *label_name,
    uint32_t keyId, int expected_affectRows, bool isUpdate, bool withLPM4, bool withLPM6, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T2_struct_t obj = (TEST_T2_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT MERGE", i, 1000, threadId);
        test_set_TEST_T2_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        test_set_vertex_nonuniq_key(stmt, i + delta);
        test_set_vertex_uniq_key(stmt, i + delta);
        test_set_vertex_property(stmt, i + delta, bool_value, isUpdate, withLPM4, withLPM6);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_read_lpm4_table(GmcStmtT *stmt, int start, int end, int delta, bool bool_value, char *label_name,
    char *keyName, bool read_num, int threadId = 0)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start; i < end; i++) {
        TEST_INFO("SCAN", i, 1000, threadId);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t vr_id = 0;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t vrf_index = 0;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t dest_ip_addr_lpm4 = i;
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr_lpm4, sizeof(dest_ip_addr_lpm4));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t mask_len = 32;
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_simple_table(stmt, i, delta, bool_value);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
}

void test_struct_read_lpm4_table(TEST_T2_struct_t *d, int index, int delta, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)(index + delta);
    uint64_t F1Value = (uint64_t)(index + delta) + 0xFFFFFFFF;
    int32_t F2Value = index + delta;
    uint32_t F3Value = index + delta;
    int16_t F4Value = (index + delta) & 0x7FFF;
    uint16_t F5Value = (index + delta) & 0xFFFF;
    int8_t F6Value = (index + delta) & 0x7F;
    uint8_t F7Value = (index + delta) & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index + delta;
    double F10Value = index + delta;
    uint64_t F11Value = (index + delta) + 0xFFFFFFFF;
    char F12Value = 'a' + ((index + delta) & 0x1A);
    unsigned char F13Value = 'A' + ((index + delta) & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index + delta);
    uint8_t F15Value = (index + delta) & 0xF;

    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_struct_read_lpm4_table(GmcStmtT *stmt, int start, int end, int delta, bool bool_value, char *label_name,
    uint32_t keyId, bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T2_struct_t obj = (TEST_T2_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, threadId);
        test_set_TEST_T2_lpm4_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            test_struct_read_lpm4_table(&obj, i, delta, bool_value);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void test_struct_get_vertex_buf_lpm4_table(GmcStructBufferT *inputBufInfo, int index, int delta, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)(index + delta);
    uint64_t F1Value = (uint64_t)(index + delta) + 0xFFFFFFFF;
    int32_t F2Value = index + delta;
    uint32_t F3Value = index + delta;
    int16_t F4Value = (index + delta) & 0x7FFF;
    uint16_t F5Value = (index + delta) & 0xFFFF;
    int8_t F6Value = (index + delta) & 0x7F;
    uint8_t F7Value = (index + delta) & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index + delta;
    double F10Value = index + delta;
    uint64_t F11Value = (index + delta) + 0xFFFFFFFF;
    char F12Value = 'a' + ((index + delta) & 0x1A);
    unsigned char F13Value = 'A' + ((index + delta) & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index + delta);
    uint8_t F15Value = (index + delta) & 0xF;

    TEST_T2_struct_t *obj = (TEST_T2_struct_t *)inputBufInfo->buf;

    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &obj->F0, sizeof(F0Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &obj->F1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &obj->F2, sizeof(F2Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &obj->F3, sizeof(F3Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &obj->F4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &obj->F5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &obj->F6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &obj->F7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &obj->F8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &obj->F9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &obj->F10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &obj->F11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &obj->F12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &obj->F13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, obj->F14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &obj->F15, sizeof(F15Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_struct_get_vertex_buf_lpm4_table(GmcStmtT *stmt, int start, int end, int delta, bool bool_value,
    char *label_name, uint32_t keyId, bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T2_struct_t obj = (TEST_T2_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};
    GmcSeriT keySeri = (GmcSeriT){0};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT GetVertexBuf SCAN", i, 1000, threadId);
        test_set_TEST_T2_lpm4_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexBuf(stmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            test_struct_get_vertex_buf_lpm4_table(&inputBufInfo, i, delta, bool_value);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
}

void db_test_struct_get_vertex_count_lpm4_table(GmcStmtT *stmt, int start, int end, char *label_name, char *keyName,
    uint32_t keyId, bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T2_struct_t obj = (TEST_T2_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT GmcGetVertexCount", i, 1000, threadId);
        test_set_TEST_T2_lpm4_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t count = 0;
        ret = GmcGetVertexCount(stmt, label_name, keyName, &count);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(end - start, count);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, count);
        }
    }
}

void db_test_read_all_lpm4_table(GmcStmtT *stmt, int start, int end, int delta, bool bool_value, char *label_name,
    char *keyName, uint32_t keyId, bool read_num, int threadId = 0)
{
    // 非结构化读
    db_test_read_lpm4_table(stmt, start, end, delta, bool_value, label_name, keyName, read_num, threadId);

    // 结构化读(GmcGetVertexDeseri)
    db_test_struct_read_lpm4_table(stmt, start, end, delta, bool_value, label_name, keyId, read_num, threadId);

    // 结构化读(GmcGetVertexBuf)
    db_test_struct_get_vertex_buf_lpm4_table(
        stmt, start, end, delta, bool_value, label_name, keyId, read_num, threadId);

    db_test_struct_get_vertex_count_lpm4_table(stmt, start, end, label_name, keyName, keyId, read_num, threadId);
}

/************************************** lpm6 table **************************************/

void db_test_struct_merge_lpm6_table(GmcStmtT *stmt, int start, int end, int delta, bool bool_value, char *label_name,
    uint32_t keyId, int expected_affectRows, bool isUpdate, bool withLPM4, bool withLPM6, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T3_struct_t obj = (TEST_T3_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT MERGE", i, 1000, threadId);
        test_set_TEST_T3_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        test_set_vertex_nonuniq_key(stmt, i + delta);
        test_set_vertex_uniq_key(stmt, i + delta);
        test_set_vertex_property(stmt, i + delta, bool_value, isUpdate, withLPM4, withLPM6);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_read_lpm6_table(GmcStmtT *stmt, int start, int end, int delta, bool bool_value, char *label_name,
    char *keyName, bool read_num, int threadId = 0)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start; i < end; i++) {
        TEST_INFO("SCAN", i, 1000, threadId);
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t vr_id = 0;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(vr_id));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t vrf_index = 0;
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(vrf_index));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        char dest_ip_addr_lpm6[16] = {0};
        snprintf((char *)dest_ip_addr_lpm6, sizeof(dest_ip_addr_lpm6), "aaaaaaa%08d", i);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_FIXED, &dest_ip_addr_lpm6, sizeof(dest_ip_addr_lpm6));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t mask_len = 128;
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(mask_len));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_simple_table(stmt, i, delta, bool_value);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
}

void test_struct_read_lpm6_table(TEST_T3_struct_t *d, int index, int delta, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)(index + delta);
    uint64_t F1Value = (uint64_t)(index + delta) + 0xFFFFFFFF;
    int32_t F2Value = index + delta;
    uint32_t F3Value = index + delta;
    int16_t F4Value = (index + delta) & 0x7FFF;
    uint16_t F5Value = (index + delta) & 0xFFFF;
    int8_t F6Value = (index + delta) & 0x7F;
    uint8_t F7Value = (index + delta) & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index + delta;
    double F10Value = index + delta;
    uint64_t F11Value = (index + delta) + 0xFFFFFFFF;
    char F12Value = 'a' + ((index + delta) & 0x1A);
    unsigned char F13Value = 'A' + ((index + delta) & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index + delta);
    uint8_t F15Value = (index + delta) & 0xF;

    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_struct_read_lpm6_table(GmcStmtT *stmt, int start, int end, int delta, bool bool_value, char *label_name,
    uint32_t keyId, bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T3_struct_t obj = (TEST_T3_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, threadId);
        test_set_TEST_T3_lpm6_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            test_struct_read_lpm6_table(&obj, i, delta, bool_value);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void test_struct_get_vertex_buf_lpm6_table(GmcStructBufferT *inputBufInfo, int index, int delta, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)(index + delta);
    uint64_t F1Value = (uint64_t)(index + delta) + 0xFFFFFFFF;
    int32_t F2Value = index + delta;
    uint32_t F3Value = index + delta;
    int16_t F4Value = (index + delta) & 0x7FFF;
    uint16_t F5Value = (index + delta) & 0xFFFF;
    int8_t F6Value = (index + delta) & 0x7F;
    uint8_t F7Value = (index + delta) & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index + delta;
    double F10Value = index + delta;
    uint64_t F11Value = (index + delta) + 0xFFFFFFFF;
    char F12Value = 'a' + ((index + delta) & 0x1A);
    unsigned char F13Value = 'A' + ((index + delta) & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index + delta);
    uint8_t F15Value = (index + delta) & 0xF;

    TEST_T3_struct_t *obj = (TEST_T3_struct_t *)inputBufInfo->buf;

    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &obj->F0, sizeof(F0Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &obj->F1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &obj->F2, sizeof(F2Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &obj->F3, sizeof(F3Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &obj->F4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &obj->F5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &obj->F6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &obj->F7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &obj->F8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &obj->F9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &obj->F10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &obj->F11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &obj->F12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &obj->F13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, obj->F14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &obj->F15, sizeof(F15Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_struct_get_vertex_buf_lpm6_table(GmcStmtT *stmt, int start, int end, int delta, bool bool_value,
    char *label_name, uint32_t keyId, bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T3_struct_t obj = (TEST_T3_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};
    GmcSeriT keySeri = (GmcSeriT){0};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT GetVertexBuf SCAN", i, 1000, threadId);
        test_set_TEST_T3_lpm6_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexBuf(stmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            test_struct_get_vertex_buf_lpm6_table(&inputBufInfo, i, delta, bool_value);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
}

void db_test_struct_get_vertex_count_lpm6_table(GmcStmtT *stmt, int start, int end, char *label_name, char *keyName,
    uint32_t keyId, bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T3_struct_t obj = (TEST_T3_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT GmcGetVertexCount", i, 1000, threadId);
        test_set_TEST_T3_lpm6_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint64_t count = 0;
        ret = GmcGetVertexCount(stmt, label_name, keyName, &count);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(end - start, count);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, count);
        }
    }
}

void db_test_read_all_lpm6_table(GmcStmtT *stmt, int start, int end, int delta, bool bool_value, char *label_name,
    char *keyName, uint32_t keyId, bool read_num, int threadId = 0)
{
    // 非结构化读
    db_test_read_lpm6_table(stmt, start, end, delta, bool_value, label_name, keyName, read_num, threadId);

    // 结构化读(GmcGetVertexDeseri)
    db_test_struct_read_lpm6_table(stmt, start, end, delta, bool_value, label_name, keyId, read_num, threadId);

    // 结构化读(GmcGetVertexBuf)
    db_test_struct_get_vertex_buf_lpm6_table(
        stmt, start, end, delta, bool_value, label_name, keyId, read_num, threadId);

    db_test_struct_get_vertex_count_lpm6_table(stmt, start, end, label_name, keyName, keyId, read_num, threadId);
}

/************************************** special complex table **************************************/

void test_set_node_property_root(GmcNodeT *node, int index, bool bool_value, bool isUpdate, bool isPkOper)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = (uint64_t)index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
    char F16Value[STRING_LEN] = {0};
    char F17Value[BYTES_LEN] = {0};
    snprintf((char *)F16Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F17Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    //    ret = GmcNodeSetPropertyByName(node, "F0", GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = GmcNodeSetPropertyByName(node, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = GmcNodeSetPropertyByName(node, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = GmcNodeSetPropertyByName(node, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = GmcNodeSetPropertyByName(node, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = GmcNodeSetPropertyByName(node, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = GmcNodeSetPropertyByName(node, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = GmcNodeSetPropertyByName(node, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isPkOper) {  // 主键操作时才设置F11(F11为二级索引)
        ret = GmcNodeSetPropertyByName(node, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcNodeSetPropertyByName(node, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!isUpdate) {  // partition不支持更新
        ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_PARTITION, &F15Value, sizeof(F15Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_STRING, F16Value, strlen(F16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_BYTES, F17Value, strlen(F17Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_set_node_property_t1(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    char F15Value[STRING_LEN] = {0};
    char F16Value[BYTES_LEN] = {0};
    snprintf((char *)F15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    ret = GmcNodeSetPropertyByName(node, "P0", GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P15", GMC_DATATYPE_STRING, F15Value, strlen(F15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "P16", GMC_DATATYPE_BYTES, F16Value, strlen(F16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_set_node_property_t2(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    char F15Value[STRING_LEN] = {0};
    char F16Value[BYTES_LEN] = {0};
    snprintf((char *)F15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    ret = GmcNodeSetPropertyByName(node, "A0", GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A15", GMC_DATATYPE_STRING, F15Value, strlen(F15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A16", GMC_DATATYPE_BYTES, F16Value, strlen(F16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_set_node_property_t3(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    char F15Value[STRING_LEN] = {0};
    char F16Value[BYTES_LEN] = {0};
    snprintf((char *)F15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    ret = GmcNodeSetPropertyByName(node, "V0", GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V15", GMC_DATATYPE_STRING, F15Value, strlen(F15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "V16", GMC_DATATYPE_BYTES, F16Value, strlen(F16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_struct_merge_special_complex_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, int expected_affectRows,
    bool isUpdate, int threadId = 0)
{
    int ret, i, j, affectRows;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    record_T1 t1 = (record_T1){0};
    fixed_array_T2 t2[T2_count];
    vector_T3 t3[T3_count];
    test_malloc_TEST_T4(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT MERGE", i, 1000, threadId);
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        test_set_TEST_T4_primary_key(&obj, i, isUniq);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *root, *tree1, *tree2, *tree3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &tree1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &tree2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &tree3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        test_set_node_property_root(root, i + delta, bool_value, isUpdate, true);
        test_set_node_property_t1(tree1, i + delta, bool_value);
        for (j = 0; j < T2_count; j++) {
            test_set_node_property_t2(tree2, j + delta, bool_value);
            GmcNodeGetNextElement(tree2, &tree2);
        }
        for (j = 0; j < T3_count; j++) {
            if (isUpdate) {
                ret = GmcNodeGetElementByIndex(tree3, j, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            } else {
                ret = GmcNodeAppendElement(tree3, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            test_set_node_property_t3(tree3, j + delta, bool_value);
        }

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }

    test_free_TEST_T4(&obj, T2_count, T3_count);
}

void db_test_struct_merge_special_complex_table_async(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, int expected_affectRows,
    bool isUpdate, int threadId = 0)
{
    int ret, i, j, affectRows;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    record_T1 t1 = (record_T1){0};
    fixed_array_T2 t2[T2_count];
    vector_T3 t3[T3_count];
    test_malloc_TEST_T4(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT MERGE ASYNC", i, 1000, threadId);
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        test_set_TEST_T4_primary_key(&obj, i, isUniq);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *root, *tree1, *tree2, *tree3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &tree1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &tree2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &tree3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        test_set_node_property_root(root, i + delta, bool_value, isUpdate, true);
        test_set_node_property_t1(tree1, i + delta, bool_value);
        for (j = 0; j < T2_count; j++) {
            test_set_node_property_t2(tree2, j + delta, bool_value);
            GmcNodeGetNextElement(tree2, &tree2);
        }
        for (j = 0; j < T3_count; j++) {
            if (isUpdate) {
                ret = GmcNodeGetElementByIndex(tree3, j, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            } else {
                ret = GmcNodeAppendElement(tree3, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            test_set_node_property_t3(tree3, j + delta, bool_value);
        }

        AsyncUserDataT data = {0};
        GmcAsyncRequestDoneContextT RequestCtx;
        RequestCtx.insertCb = merge_vertex_callback;
        RequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &RequestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, data.affectRows);
    }

    test_free_TEST_T4(&obj, T2_count, T3_count);
}

void db_test_struct_merge_special_complex_table_batch(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, int expected_affectRows,
    bool isUpdate, int threadId = 0)
{
    int ret, i, j, affectRows;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    record_T1 t1 = (record_T1){0};
    fixed_array_T2 t2[T2_count];
    vector_T3 t3[T3_count];
    test_malloc_TEST_T4(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT MERGE BATCH", i, 1000, threadId);
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        test_set_TEST_T4_primary_key(&obj, i, isUniq);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *root, *tree1, *tree2, *tree3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &tree1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &tree2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &tree3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        test_set_node_property_root(root, i + delta, bool_value, isUpdate, true);
        test_set_node_property_t1(tree1, i + delta, bool_value);
        for (j = 0; j < T2_count; j++) {
            test_set_node_property_t2(tree2, j + delta, bool_value);
            GmcNodeGetNextElement(tree2, &tree2);
        }
        for (j = 0; j < T3_count; j++) {
            if (isUpdate) {
                ret = GmcNodeGetElementByIndex(tree3, j, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            } else {
                ret = GmcNodeAppendElement(tree3, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            test_set_node_property_t3(tree3, j + delta, bool_value);
        }

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(end - start, totalNum);
    AW_MACRO_EXPECT_EQ_INT(end - start, successNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    test_free_TEST_T4(&obj, T2_count, T3_count);
}

void db_test_struct_merge_special_complex_table_batch_async(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, int expected_affectRows,
    bool isUpdate, int threadId = 0)
{
    int ret, i, j, affectRows;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    record_T1 t1 = (record_T1){0};
    fixed_array_T2 t2[T2_count];
    vector_T3 t3[T3_count];
    test_malloc_TEST_T4(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT MERGE BATCH ASYNC", i, 1000, threadId);
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        test_set_TEST_T4_primary_key(&obj, i, isUniq);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *root, *tree1, *tree2, *tree3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &tree1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &tree2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &tree3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        test_set_node_property_root(root, i + delta, bool_value, isUpdate, true);
        test_set_node_property_t1(tree1, i + delta, bool_value);
        for (j = 0; j < T2_count; j++) {
            test_set_node_property_t2(tree2, j + delta, bool_value);
            GmcNodeGetNextElement(tree2, &tree2);
        }
        for (j = 0; j < T3_count; j++) {
            if (isUpdate) {
                ret = GmcNodeGetElementByIndex(tree3, j, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            } else {
                ret = GmcNodeAppendElement(tree3, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            test_set_node_property_t3(tree3, j + delta, bool_value);
        }

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AsyncUserDataT data = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(end - start, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(end - start, data.succNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    test_free_TEST_T4(&obj, T2_count, T3_count);
}

void test_get_node_property_root(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
    char F16Value[STRING_LEN] = {0};
    char F17Value[BYTES_LEN] = {0};
    snprintf((char *)F16Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F17Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // root
    //    ret = queryNodePropertyAndCompare(node, "F0", GMC_DATATYPE_INT64, &F0Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryNodePropertyAndCompare(node, "F1", GMC_DATATYPE_UINT64, &F1Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryNodePropertyAndCompare(node, "F2", GMC_DATATYPE_INT32, &F2Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryNodePropertyAndCompare(node, "F3", GMC_DATATYPE_UINT32, &F3Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryNodePropertyAndCompare(node, "F4", GMC_DATATYPE_INT16, &F4Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryNodePropertyAndCompare(node, "F5", GMC_DATATYPE_UINT16, &F5Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryNodePropertyAndCompare(node, "F6", GMC_DATATYPE_INT8, &F6Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryNodePropertyAndCompare(node, "F7", GMC_DATATYPE_UINT8, &F7Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F8", GMC_DATATYPE_BOOL, &F8Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F9", GMC_DATATYPE_FLOAT, &F9Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F10", GMC_DATATYPE_DOUBLE, &F10Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryNodePropertyAndCompare(node, "F11", GMC_DATATYPE_TIME, &F11Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F12", GMC_DATATYPE_CHAR, &F12Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F13", GMC_DATATYPE_UCHAR, &F13Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F14", GMC_DATATYPE_FIXED, F14Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = queryNodePropertyAndCompare(node, "F15", GMC_DATATYPE_PARTITION, &F15Value);
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F16", GMC_DATATYPE_STRING, F16Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "F17", GMC_DATATYPE_BYTES, F17Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_get_node_property_t1(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    char F15Value[STRING_LEN] = {0};
    char F16Value[BYTES_LEN] = {0};
    snprintf((char *)F15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // root
    ret = queryNodePropertyAndCompare(node, "P0", GMC_DATATYPE_INT64, &F0Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P1", GMC_DATATYPE_UINT64, &F1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P2", GMC_DATATYPE_INT32, &F2Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P3", GMC_DATATYPE_UINT32, &F3Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P4", GMC_DATATYPE_INT16, &F4Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P5", GMC_DATATYPE_UINT16, &F5Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P6", GMC_DATATYPE_INT8, &F6Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P7", GMC_DATATYPE_UINT8, &F7Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P8", GMC_DATATYPE_BOOL, &F8Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P9", GMC_DATATYPE_FLOAT, &F9Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P10", GMC_DATATYPE_DOUBLE, &F10Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P11", GMC_DATATYPE_TIME, &F11Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P12", GMC_DATATYPE_CHAR, &F12Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P13", GMC_DATATYPE_UCHAR, &F13Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P14", GMC_DATATYPE_FIXED, F14Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P15", GMC_DATATYPE_STRING, F15Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "P16", GMC_DATATYPE_BYTES, F16Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_get_node_property_t2(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    char F15Value[STRING_LEN] = {0};
    char F16Value[BYTES_LEN] = {0};
    snprintf((char *)F15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // root
    ret = queryNodePropertyAndCompare(node, "A0", GMC_DATATYPE_INT64, &F0Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A1", GMC_DATATYPE_UINT64, &F1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A2", GMC_DATATYPE_INT32, &F2Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A3", GMC_DATATYPE_UINT32, &F3Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A4", GMC_DATATYPE_INT16, &F4Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A5", GMC_DATATYPE_UINT16, &F5Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A6", GMC_DATATYPE_INT8, &F6Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A7", GMC_DATATYPE_UINT8, &F7Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A8", GMC_DATATYPE_BOOL, &F8Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A9", GMC_DATATYPE_FLOAT, &F9Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A10", GMC_DATATYPE_DOUBLE, &F10Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A11", GMC_DATATYPE_TIME, &F11Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A12", GMC_DATATYPE_CHAR, &F12Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A13", GMC_DATATYPE_UCHAR, &F13Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A14", GMC_DATATYPE_FIXED, F14Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A15", GMC_DATATYPE_STRING, F15Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "A16", GMC_DATATYPE_BYTES, F16Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_get_node_property_t3(GmcNodeT *node, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    char F15Value[STRING_LEN] = {0};
    char F16Value[BYTES_LEN] = {0};
    snprintf((char *)F15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // root
    ret = queryNodePropertyAndCompare(node, "V0", GMC_DATATYPE_INT64, &F0Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V1", GMC_DATATYPE_UINT64, &F1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V2", GMC_DATATYPE_INT32, &F2Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V3", GMC_DATATYPE_UINT32, &F3Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V4", GMC_DATATYPE_INT16, &F4Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V5", GMC_DATATYPE_UINT16, &F5Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V6", GMC_DATATYPE_INT8, &F6Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V7", GMC_DATATYPE_UINT8, &F7Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V8", GMC_DATATYPE_BOOL, &F8Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V9", GMC_DATATYPE_FLOAT, &F9Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V10", GMC_DATATYPE_DOUBLE, &F10Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V11", GMC_DATATYPE_TIME, &F11Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V12", GMC_DATATYPE_CHAR, &F12Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V13", GMC_DATATYPE_UCHAR, &F13Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V14", GMC_DATATYPE_FIXED, F14Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V15", GMC_DATATYPE_STRING, F15Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryNodePropertyAndCompare(node, "V16", GMC_DATATYPE_BYTES, F16Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_read_special_complex_table(
    GmcStmtT *stmt, int index, int delta, bool bool_value, uint16_t T2_count, uint16_t T3_count)
{
    int ret, i;

    GmcNodeT *root, *tree1, *tree2, *tree3;
    ret = GmcGetRootNode(stmt, &root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T1", &tree1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T2", &tree2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeGetChild(root, "T3", &tree3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    test_get_node_property_root(root, index + delta, bool_value);
    test_get_node_property_t1(tree1, index + delta, bool_value);

    // fixed_array
    for (i = 0; i < T2_count; ++i) {
        ret = GmcNodeGetElementByIndex(tree2, i, &tree2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        test_get_node_property_t2(tree2, i + delta, bool_value);
    }

    // vector
    for (i = 0; i < T3_count; ++i) {
        ret = GmcNodeGetElementByIndex(tree3, i, &tree3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        test_get_node_property_t3(tree3, i + delta, bool_value);
    }
}

void db_test_read_special_complex_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, char *keyName, uint16_t T2_count, uint16_t T3_count, bool read_num, int threadId = 0)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("SCAN", i, 1000, threadId);

            if (strcmp(keyName, g_pk_name) == 0) {
                int64_t F0Value = (int64_t)i;
                uint64_t F1Value = (uint64_t)i + 0xFFFFFFFF;
                int32_t F2Value = i;
                uint32_t F3Value = i;
                int16_t F4Value = i & 0x7FFF;
                uint16_t F5Value = i & 0xFFFF;
                int8_t F6Value = i & 0x7F;
                uint8_t F7Value = i & 0xFF;
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 7, GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                uint64_t F1Value = (uint64_t)i + 0xFFFFFFFF;
                int32_t F2Value = i;
                uint32_t F3Value = i;
                int16_t F4Value = i & 0x7FFF;
                uint16_t F5Value = i & 0xFFFF;
                int8_t F6Value = i & 0x7F;
                uint8_t F7Value = i & 0xFF;
                uint64_t F11Value = (uint64_t)i + 0xFFFFFFFF;
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = GmcSetIndexKeyValue(stmt, 7, GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            ret = GmcSetIndexKeyName(stmt, keyName);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish = false;
            int cnt = 0;
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }
                test_read_special_complex_table(stmt, i, delta, bool_value, T2_count, T3_count);
                cnt++;
            }
            if (read_num) {
                AW_MACRO_EXPECT_EQ_INT(1, cnt);
            } else {
                AW_MACRO_EXPECT_EQ_INT(0, cnt);
            }
        }
    } else {
        TEST_INFO("NON UNIQUE SCAN", 0, 1, threadId);
        uint64_t F1Value = (uint64_t)0 + 0xFFFFFFFF;
        int32_t F2Value = 0;
        uint32_t F3Value = 0;
        ret = GmcSetIndexKeyName(stmt, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(end - start, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
}

void CompareVertexPropertyValue_R(TEST_T4_struct_t *d, int index, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
    char F16Value[STRING_LEN] = {0};
    char F17Value[BYTES_LEN] = {0};
    snprintf((char *)F16Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F17Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // root
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &F16Value, d->F16, sizeof(F16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &F17Value, d->F17, sizeof(F17Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void CompareVertexPropertyValue_P(TEST_T4_struct_t *d, int index, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    char F15Value[STRING_LEN] = {0};
    char F16Value[BYTES_LEN] = {0};
    snprintf((char *)F15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // record 子节点
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->T1->P0, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->T1->P1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->T1->P2, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->T1->P3, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->T1->P4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->T1->P5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->T1->P6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->T1->P7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->T1->P8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->T1->P9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->T1->P10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->T1->P11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->T1->P12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->T1->P13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->T1->P14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &F15Value, d->T1->P15, sizeof(F15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &F16Value, d->T1->P16, sizeof(F16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void CompareVertexPropertyValue_A(TEST_T4_struct_t *d, int array_index, int index, bool bool_value)
{
    int ret;
    int64_t A0Value = (int64_t)index;
    uint64_t A1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t A2Value = index;
    uint32_t A3Value = index;
    int16_t A4Value = index & 0x7FFF;
    uint16_t A5Value = index & 0xFFFF;
    int8_t A6Value = index & 0x7F;
    uint8_t A7Value = index & 0xFF;
    bool A8Value = bool_value;
    float A9Value = index;
    double A10Value = index;
    uint64_t A11Value = index + 0xFFFFFFFF;
    char A12Value = 'a' + (index & 0x1A);
    unsigned char A13Value = 'A' + (index & 0x1A);
    char A14Value[16] = {0};
    snprintf((char *)A14Value, sizeof(A14Value), "aaaaaaa%08d", index);
    char A15Value[STRING_LEN] = {0};
    char A16Value[BYTES_LEN] = {0};
    snprintf((char *)A15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)A16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // array
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &A0Value, &d->T2[array_index].A0, sizeof(A0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &A1Value, &d->T2[array_index].A1, sizeof(A1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &A2Value, &d->T2[array_index].A2, sizeof(A2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &A3Value, &d->T2[array_index].A3, sizeof(A3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &A4Value, &d->T2[array_index].A4, sizeof(A4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &A5Value, &d->T2[array_index].A5, sizeof(A5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &A6Value, &d->T2[array_index].A6, sizeof(A6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &A7Value, &d->T2[array_index].A7, sizeof(A7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &A8Value, &d->T2[array_index].A8, sizeof(A8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &A9Value, &d->T2[array_index].A9, sizeof(A9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &A10Value, &d->T2[array_index].A10, sizeof(A10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &A11Value, &d->T2[array_index].A11, sizeof(A11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &A12Value, &d->T2[array_index].A12, sizeof(A12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &A13Value, &d->T2[array_index].A13, sizeof(A13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, A14Value, d->T2[array_index].A14, sizeof(A14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &A15Value, d->T2[array_index].A15, sizeof(A15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &A16Value, d->T2[array_index].A16, sizeof(A16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void CompareVertexPropertyValue_V(TEST_T4_struct_t *d, int vector_index, int index, bool bool_value)
{
    int ret;
    int64_t V0Value = (int64_t)index;
    uint64_t V1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t V2Value = index;
    uint32_t V3Value = index;
    int16_t V4Value = index & 0x7FFF;
    uint16_t V5Value = index & 0xFFFF;
    int8_t V6Value = index & 0x7F;
    uint8_t V7Value = index & 0xFF;
    bool V8Value = bool_value;
    float V9Value = index;
    double V10Value = index;
    uint64_t V11Value = index + 0xFFFFFFFF;
    char V12Value = 'a' + (index & 0x1A);
    unsigned char V13Value = 'A' + (index & 0x1A);
    char V14Value[16] = {0};
    snprintf((char *)V14Value, sizeof(V14Value), "aaaaaaa%08d", index);
    char V15Value[STRING_LEN] = {0};
    char V16Value[BYTES_LEN] = {0};
    snprintf((char *)V15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)V16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // vector
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &V0Value, &d->T3[vector_index].V0, sizeof(V0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &V1Value, &d->T3[vector_index].V1, sizeof(V1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &V2Value, &d->T3[vector_index].V2, sizeof(V2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &V3Value, &d->T3[vector_index].V3, sizeof(V3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &V4Value, &d->T3[vector_index].V4, sizeof(V4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &V5Value, &d->T3[vector_index].V5, sizeof(V5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &V6Value, &d->T3[vector_index].V6, sizeof(V6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &V7Value, &d->T3[vector_index].V7, sizeof(V7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &V8Value, &d->T3[vector_index].V8, sizeof(V8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &V9Value, &d->T3[vector_index].V9, sizeof(V9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &V10Value, &d->T3[vector_index].V10, sizeof(V10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &V11Value, &d->T3[vector_index].V11, sizeof(V11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &V12Value, &d->T3[vector_index].V12, sizeof(V12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &V13Value, &d->T3[vector_index].V13, sizeof(V13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, V14Value, d->T3[vector_index].V14, sizeof(V14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &V15Value, d->T3[vector_index].V15, sizeof(V15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &V16Value, d->T3[vector_index].V16, sizeof(V16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_struct_read_TEST_T4(
    TEST_T4_struct_t *d, int index, int delta, bool bool_value, uint16_t T2_count, uint16_t T3_count)
{
    int ret = 0, i;
    CompareVertexPropertyValue_R(d, index + delta, bool_value);
    CompareVertexPropertyValue_P(d, index + delta, bool_value);

    // array
    for (i = 0; i < T2_count; ++i) {
        CompareVertexPropertyValue_A(d, i, i + delta, bool_value);
    }

    // vector
    for (i = 0; i < T3_count; ++i) {
        CompareVertexPropertyValue_V(d, i, i + delta, bool_value);
    }
}

void db_test_struct_read_special_complex_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, bool read_num,
    int threadId = 0)
{
    int ret, i;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT SCAN", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T4_primary_key(&obj, i, isUniq);
            } else {
                test_set_TEST_T4_uniq_key(&obj, i);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish = false;
            int cnt = 0;
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }
                ret = testStructGetVertexDeseri(stmt, &deseri);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                test_struct_read_TEST_T4(&obj, i, delta, bool_value, T2_count, T3_count);
                cnt++;
            }
            if (read_num) {
                AW_MACRO_EXPECT_EQ_INT(1, cnt);
            } else {
                AW_MACRO_EXPECT_EQ_INT(0, cnt);
            }
        }
    } else {
        TEST_INFO("NON UNIQUE STRUCT SCAN", 0, 1, threadId);
        test_set_TEST_T4_nonuniq_key(&obj, 0);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            cnt++;
        }
        if (read_num) {
            AW_MACRO_EXPECT_EQ_INT(end - start, cnt);
        } else {
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

// 主键, localhash, hashcluster
void db_test_read_all_special_complex_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, char *keyName, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, bool read_num,
    int threadId = 0)
{
    // 非结构化读
    db_test_read_special_complex_table(
        stmt, start, end, delta, isUniq, bool_value, label_name, keyName, T2_count, T3_count, read_num, threadId);

    // 结构化读(GmcGetVertexDeseri)
    db_test_struct_read_special_complex_table(
        stmt, start, end, delta, isUniq, bool_value, label_name, keyId, T2_count, T3_count, read_num, threadId);
}

void db_test_struct_update_special_complex_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, int expected_affectRows,
    int threadId = 0)
{
    int ret, i, j, affectRows;
    bool isPkOper;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT UPDATE", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T4_primary_key(&obj, i, isUniq);
                isPkOper = true;
            } else {
                test_set_TEST_T4_uniq_key(&obj, i);
                isPkOper = false;
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeT *root, *tree1, *tree2, *tree3;
            ret = GmcGetRootNode(stmt, &root);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &tree1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T2", &tree2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &tree3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            test_set_node_property_root(root, i + delta, bool_value, true, isPkOper);
            test_set_node_property_t1(tree1, i + delta, bool_value);
            for (j = 0; j < T2_count; j++) {  // fixed_array
                ret = GmcNodeGetElementByIndex(tree2, j, &tree2);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                test_set_node_property_t2(tree2, j + delta, bool_value);
            }
            for (j = 0; j < T3_count; j++) {  // vector
                ret = GmcNodeGetElementByIndex(tree3, j, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                test_set_node_property_t3(tree3, j + delta, bool_value);
            }
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
        }
    } else {
        TEST_INFO("NON UNIQUE STRUCT UPDATE", 0, 1, threadId);
        test_set_TEST_T4_nonuniq_key(&obj, 0);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_struct_update_special_complex_table_async(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, int expected_affectRows,
    int threadId = 0)
{
    int ret, i, j, affectRows;
    bool isPkOper;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT UPDATE ASYNC", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T4_primary_key(&obj, i, isUniq);
                isPkOper = true;
            } else {
                test_set_TEST_T4_uniq_key(&obj, i);
                isPkOper = false;
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeT *root, *tree1, *tree2, *tree3;
            ret = GmcGetRootNode(stmt, &root);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &tree1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T2", &tree2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &tree3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            test_set_node_property_root(root, i + delta, bool_value, true, isPkOper);
            test_set_node_property_t1(tree1, i + delta, bool_value);
            for (j = 0; j < T2_count; j++) {  // fixed_array
                ret = GmcNodeGetElementByIndex(tree2, j, &tree2);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                test_set_node_property_t2(tree2, j + delta, bool_value);
            }
            for (j = 0; j < T3_count; j++) {  // vector
                ret = GmcNodeGetElementByIndex(tree3, j, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                test_set_node_property_t3(tree3, j + delta, bool_value);
            }

            AsyncUserDataT data = {0};
            GmcAsyncRequestDoneContextT RequestCtx;
            RequestCtx.insertCb = update_vertex_callback;
            RequestCtx.userData = &data;
            ret = GmcExecuteAsync(stmt, &RequestCtx);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, data.affectRows);
        }
    } else {
        TEST_INFO("NON UNIQUE STRUCT UPDATE ASYNC", 0, 1, threadId);
    }
}

void db_test_struct_update_special_complex_table_batch(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, int expected_affectRows,
    int threadId = 0)
{
    int ret, i, j, affectRows;
    bool isPkOper;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (isUniq) {
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch;
        GmcBatchRetT batchRet;
        ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchBindStmt(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT UPDATE BATCH", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T4_primary_key(&obj, i, isUniq);
                isPkOper = true;
            } else {
                test_set_TEST_T4_uniq_key(&obj, i);
                isPkOper = false;
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeT *root, *tree1, *tree2, *tree3;
            ret = GmcGetRootNode(stmt, &root);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &tree1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T2", &tree2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &tree3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            test_set_node_property_root(root, i + delta, bool_value, true, isPkOper);
            test_set_node_property_t1(tree1, i + delta, bool_value);
            for (j = 0; j < T2_count; j++) {  // fixed_array
                ret = GmcNodeGetElementByIndex(tree2, j, &tree2);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                test_set_node_property_t2(tree2, j + delta, bool_value);
            }
            for (j = 0; j < T3_count; j++) {  // vector
                ret = GmcNodeGetElementByIndex(tree3, j, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                test_set_node_property_t3(tree3, j + delta, bool_value);
            }
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(end - start, totalNum);
        AW_MACRO_EXPECT_EQ_INT(end - start, successNum);
        GmcBatchUnbindStmt(batch, stmt);
        GmcBatchDestroy(batch);
    } else {
        TEST_INFO("NON UNIQUE STRUCT UPDATE BATCH", 0, 1, threadId);
    }
}

void db_test_struct_update_special_complex_table_batch_async(GmcStmtT *stmt, int start, int end, int delta, bool isUniq,
    bool bool_value, char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, int expected_affectRows,
    int threadId = 0)
{
    int ret, i, j, affectRows;
    bool isPkOper;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT UPDATE BATCH ASYNC", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T4_primary_key(&obj, i, isUniq);
                isPkOper = true;
            } else {
                test_set_TEST_T4_uniq_key(&obj, i);
                isPkOper = false;
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeT *root, *tree1, *tree2, *tree3;
            ret = GmcGetRootNode(stmt, &root);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &tree1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T2", &tree2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &tree3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

            test_set_node_property_root(root, i + delta, bool_value, true, isPkOper);
            test_set_node_property_t1(tree1, i + delta, bool_value);
            for (j = 0; j < T2_count; j++) {  // fixed_array
                ret = GmcNodeGetElementByIndex(tree2, j, &tree2);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                test_set_node_property_t2(tree2, j + delta, bool_value);
            }
            for (j = 0; j < T3_count; j++) {  // vector
                ret = GmcNodeGetElementByIndex(tree3, j, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                test_set_node_property_t3(tree3, j + delta, bool_value);
            }
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        AsyncUserDataT data = {0};
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(end - start, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(end - start, data.succNum);
    } else {
        TEST_INFO("NON UNIQUE STRUCT UPDATE BATCH ASYNC", 0, 1, threadId);
        test_set_TEST_T4_nonuniq_key(&obj, 0);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AsyncUserDataT data = {0};
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

// 主键 localhash hashcluster
void db_test_struct_delete_special_complex_table(GmcStmtT *stmt, int start, int end, bool isUniq, char *label_name,
    uint32_t keyId, int expected_affectRows, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT DELETE", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T4_primary_key(&obj, i, isUniq);
            } else {
                test_set_TEST_T4_uniq_key(&obj, i);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
        }
    } else {
        TEST_INFO("NON UNIQUE STRUCT DELETE", 0, 1, threadId);
        test_set_TEST_T4_nonuniq_key(&obj, 0);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }
}

void db_test_struct_delete_special_complex_table_async(GmcStmtT *stmt, int start, int end, bool isUniq,
    char *label_name, uint32_t keyId, int expected_affectRows, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT DELETE ASYNC", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T4_primary_key(&obj, i, isUniq);
            } else {
                test_set_TEST_T4_uniq_key(&obj, i);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AsyncUserDataT data = {0};
            GmcAsyncRequestDoneContextT RequestCtx;
            RequestCtx.insertCb = delete_vertex_callback;
            RequestCtx.userData = &data;
            ret = GmcExecuteAsync(stmt, &RequestCtx);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, data.affectRows);
        }
    } else {
        TEST_INFO("NON UNIQUE STRUCT DELETE ASYNC", 0, 1, threadId);
    }
}

void db_test_struct_delete_special_complex_table_batch(GmcStmtT *stmt, int start, int end, bool isUniq,
    char *label_name, uint32_t keyId, int expected_affectRows, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        GmcBatchOptionT batchOption;
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcBatchT *batch;
        GmcBatchRetT batchRet;
        ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchBindStmt(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT DELETE BATCH", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T4_primary_key(&obj, i, isUniq);
            } else {
                test_set_TEST_T4_uniq_key(&obj, i);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(end - start, totalNum);
        AW_MACRO_EXPECT_EQ_INT(end - start, successNum);
        GmcBatchUnbindStmt(batch, stmt);
        GmcBatchDestroy(batch);
    } else {
        TEST_INFO("NON UNIQUE STRUCT DELETE BATCH", 0, 1, threadId);
    }
}

void db_test_struct_delete_special_complex_table_batch_async(GmcStmtT *stmt, int start, int end, bool isUniq,
    char *label_name, uint32_t keyId, int expected_affectRows, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT DELETE BATCH ASYNC", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T4_primary_key(&obj, i, isUniq);
            } else {
                test_set_TEST_T4_uniq_key(&obj, i);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        AsyncUserDataT data = {0};
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(end - start, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(end - start, data.succNum);
    } else {
        TEST_INFO("NON UNIQUE STRUCT DELETE BATCH ASYNC", 0, 1, threadId);
        test_set_TEST_T4_nonuniq_key(&obj, 0);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AsyncUserDataT data = {0};
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
        AW_MACRO_EXPECT_EQ_INT(1, data.totalNum);
        AW_MACRO_EXPECT_EQ_INT(1, data.succNum);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

void sn_callback_simple_table(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, delta = 0;
    char labelName[MAX_NAME_LENGTH] = {0}, keyName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH, size;
    unsigned int keyId = 1;  // 此处keyId用在GmcGetVertexBuf中, 只要不为0即可(为0会去主键fetch)
    const void *keyValue = 0;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    const uint16_t T2_count = 10, T3_count = 10;
    bool eof = false, bool_value = true;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            printf("[info]GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
            user_data->scanEofNum++;
            break;
        } else if (eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
            TestLabelInfoT labelInfo = {labelName, 0, g_testNameSpace};
            GmcStructBufferT inputBufInfo = (GmcStructBufferT){0};
            GmcSeriT keySeri = (GmcSeriT){0};

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old object
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_DELETE Old Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexBuf(subStmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_get_vertex_buf_simple_table(&inputBufInfo, index, delta, bool_value);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读old object
                    delta = 0;
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_UPDATE Old Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexBuf(subStmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_get_vertex_buf_simple_table(&inputBufInfo, index, delta, bool_value);

                    //读new object
                    delta = g_data_num;
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_UPDATE New Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexBuf(subStmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_get_vertex_buf_simple_table(&inputBufInfo, index, delta, bool_value);

                    // 读key
                    delta = g_data_num;
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_UPDATE key", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexBuf(subStmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_get_vertex_buf_simple_table(&inputBufInfo, index, delta, bool_value);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_INSERT: {
                    //读new object
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_MERGE_INSERT New Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexBuf(subStmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_get_vertex_buf_simple_table(&inputBufInfo, index, delta, bool_value);

                    // 读key
                    TEST_INFO("GMC_SUB_EVENT_MERGE_INSERT key", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexBuf(subStmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_get_vertex_buf_simple_table(&inputBufInfo, index, delta, bool_value);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_UPDATE: {
                    //读new object
                    delta = 0;
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_MERGE_UPDATE New Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexBuf(subStmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_get_vertex_buf_simple_table(&inputBufInfo, index, delta, bool_value);

                    //读old object
                    delta = g_data_num;
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_MERGE_UPDATE Old Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexBuf(subStmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_get_vertex_buf_simple_table(&inputBufInfo, index, delta, bool_value);

                    // 读key
                    delta = 0;
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_MERGE_UPDATE key", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexBuf(subStmt, &obj, keyId, &keySeri, &inputBufInfo, &labelInfo);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_get_vertex_buf_simple_table(&inputBufInfo, index, delta, bool_value);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    abort();
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                user_data->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void sn_callback_special_complex_table(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret, index, i, delta = 0;
    char labelName[MAX_NAME_LENGTH] = {0}, keyName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH, size;
    const void *keyValue = 0;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    const uint16_t T2_count = 10, T3_count = 10;
    bool eof = false, bool_value = true;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            printf("[info]GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
            user_data->scanEofNum++;
            break;
        } else if (eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            TEST_T4_struct_t obj = (TEST_T4_struct_t){0};
            TestLabelInfoT labelInfo = {labelName, 0, g_testNameSpace};
            structTestCtx deseriCtx = (structTestCtx){0};
            GmcDeseriT deseri = (GmcDeseriT){0};
            testStructSetDeseri(subStmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    //读old object
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_DELETE Old Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexDeseri(subStmt, &deseri);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_read_TEST_T4(&obj, index, delta, bool_value, T2_count, T3_count);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    //读new object
                    delta = g_data_num;
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_UPDATE New Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexDeseri(subStmt, &deseri);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_read_TEST_T4(&obj, index, delta, bool_value, T2_count, T3_count);

                    //读old object
                    delta = 0;
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_UPDATE Old Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexDeseri(subStmt, &deseri);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_read_TEST_T4(&obj, index, delta, bool_value, T2_count, T3_count);

                    //读 key
                    delta = g_data_num;
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_UPDATE Key", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = testStructGetVertexDeseri(subStmt, &deseri);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_read_TEST_T4(&obj, index, delta, bool_value, T2_count, T3_count);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_INSERT: {
                    //读new object
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_MERGE_INSERT New Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexDeseri(subStmt, &deseri);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_read_TEST_T4(&obj, index, delta, bool_value, T2_count, T3_count);

                    //读new key
                    TEST_INFO("GMC_SUB_EVENT_MERGE_INSERT New Key", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = testStructGetVertexDeseri(subStmt, &deseri);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_read_TEST_T4(&obj, index, delta, bool_value, T2_count, T3_count);
                    break;
                }
                case GMC_SUB_EVENT_MERGE_UPDATE: {
                    //读new object
                    delta = 0;
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_MERGE_UPDATE New Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexDeseri(subStmt, &deseri);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_read_TEST_T4(&obj, index, delta, bool_value, T2_count, T3_count);

                    //读old object
                    delta = g_data_num;
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_MERGE_UPDATE Old Value", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    ret = testStructGetVertexDeseri(subStmt, &deseri);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_read_TEST_T4(&obj, index, delta, bool_value, T2_count, T3_count);

                    //读 key
                    delta = 0;
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_MERGE_UPDATE Key", index, 1000, i);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_DATA_BY_KEY);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = testStructGetVertexDeseri(subStmt, &deseri);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    test_struct_read_TEST_T4(&obj, index, delta, bool_value, T2_count, T3_count);
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    abort();
                    break;
                }
            }
            deSeriFreeDynMem(&deseriCtx, true);
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_INSERT: {
                user_data->mergeInsertNum++;
                break;
            }
            case GMC_SUB_EVENT_MERGE_UPDATE: {
                user_data->mergeUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

void test_set_big_obj_node_property_root(GmcNodeT *node, int index, bool bool_value, bool isUpdate, bool isPkOper)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = (uint64_t)index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
    char F16Value[STRING_LEN] = {0};
    char F17Value[BYTES_LEN] = {0};
    snprintf((char *)F16Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F17Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    //    ret = GmcNodeSetPropertyByName(node, "F0", GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isPkOper) {  // 主键操作时才设置F1(F1为二级索引)
        ret = GmcNodeSetPropertyByName(node, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcNodeSetPropertyByName(node, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "F14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!isUpdate) {  // partition不支持更新
        ret = GmcNodeSetPropertyByName(node, "F15", GMC_DATATYPE_PARTITION, &F15Value, sizeof(F15Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcNodeSetPropertyByName(node, (char *)"F16", GMC_DATATYPE_STRING, F16Value, strlen(F16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, (char *)"F17", GMC_DATATYPE_BYTES, F17Value, strlen(F17Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_set_big_obj_node_property_t2(GmcNodeT *node, int index, bool bool_value)
{
    int ret, i;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    char F32Value[STRING_LEN] = {0};
    char F33Value[BYTES_LEN] = {0};
    snprintf((char *)F32Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F33Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    char field_value[STRING_MAX_SIZE] = {0};
    for (i = 0; i < STRING_MAX_SIZE; i++) {
        field_value[i] = 'A';
    }

    ret = GmcNodeSetPropertyByName(node, "A0", GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A15", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A16", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A17", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A18", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A19", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A20", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A21", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A22", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A23", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A24", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A25", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A26", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A27", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A28", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A29", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A30", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A31", GMC_DATATYPE_FIXED, field_value, sizeof(field_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A32", GMC_DATATYPE_STRING, F32Value, strlen(F32Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcNodeSetPropertyByName(node, "A33", GMC_DATATYPE_BYTES, F33Value, strlen(F33Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void db_test_struct_merge_big_obj_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, int expected_affectRows, bool isUpdate,
    int threadId = 0)
{
    int ret, i, j, affectRows;
    TEST_T5_struct_t *obj = (TEST_T5_struct_t *)malloc(sizeof(TEST_T5_struct_t));
    memset(obj, 0, sizeof(TEST_T5_struct_t));
    record_T1 *t1 = (record_T1 *)malloc(sizeof(record_T1));
    memset(t1, 0, sizeof(record_T1));
    fixed_array_T2_big_obj *t2 = (fixed_array_T2_big_obj *)malloc(sizeof(fixed_array_T2_big_obj) * T2_count);
    memset(t2, 0, sizeof(fixed_array_T2_big_obj) * T2_count);
    vector_T3 *t3 = (vector_T3 *)malloc(sizeof(vector_T3) * T3_count);
    memset(t3, 0, sizeof(vector_T3) * T3_count);

    test_malloc_TEST_T5(obj, t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    for (i = start; i < end; i++) {
        TEST_INFO("STRUCT MERGE", i, 1000, threadId);
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        test_set_TEST_T5_primary_key(obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, obj, keyId, NULL, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        GmcNodeT *root, *tree1, *tree2, *tree3;
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T1", &tree1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T2", &tree2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetChild(root, "T3", &tree3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        test_set_big_obj_node_property_root(root, i + delta, bool_value, isUpdate, true);
        test_set_node_property_t1(tree1, i + delta, bool_value);
        for (j = 0; j < T2_count; j++) {
            test_set_big_obj_node_property_t2(tree2, j + delta, bool_value);
            GmcNodeGetNextElement(tree2, &tree2);
        }
        for (j = 0; j < T3_count; j++) {
            if (isUpdate) {
                ret = GmcNodeGetElementByIndex(tree3, j, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            } else {
                ret = GmcNodeAppendElement(tree3, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            }
            test_set_node_property_t3(tree3, j + delta, bool_value);
        }

        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
    }

    test_free_TEST_T5(obj, T2_count, T3_count);
    free(t1);
    free(t2);
    free(t3);
    free(obj);
}

void db_test_read_big_obj_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    const char *label_name, const char *keyName, uint16_t T2_count, uint16_t T3_count, bool read_num, int threadId = 0)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("SCAN", i, 1000, threadId);
            if (strcmp(keyName, g_pk_name) == 0) {
                int64_t F0Value = (int64_t)i;
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            } else {
                uint64_t F1Value = (uint64_t)i + 0xFFFFFFFF;
                ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
            ret = GmcSetIndexKeyName(stmt, keyName);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish = false;
            int cnt = 0;
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }
                // test_read_special_complex_table(stmt, i, delta, bool_value, T2_count, T3_count);
                cnt++;
            }
            if (read_num) {
                AW_MACRO_EXPECT_EQ_INT(1, cnt);
            } else {
                AW_MACRO_EXPECT_EQ_INT(0, cnt);
            }
        }
    } else {
        TEST_INFO("NON UNIQUE SCAN", 0, 1, threadId);
    }
}

void CompareVertexPropertyValue_R_big_obj(TEST_T5_struct_t *d, int index, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = (uint64_t)index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    uint8_t F15Value = index & 0xF;
    char F16Value[STRING_LEN] = {0};
    char F17Value[BYTES_LEN] = {0};
    snprintf((char *)F16Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F17Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // root
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    //    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &F16Value, d->F16, sizeof(F16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &F17Value, d->F17, sizeof(F17Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void CompareVertexPropertyValue_P_big_obj(TEST_T5_struct_t *d, int index, bool bool_value)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    char F15Value[STRING_LEN] = {0};
    char F16Value[BYTES_LEN] = {0};
    snprintf((char *)F15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)F16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // record 子节点
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->T1->P0, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->T1->P1, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->T1->P2, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->T1->P3, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->T1->P4, sizeof(F4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->T1->P5, sizeof(F5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->T1->P6, sizeof(F6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->T1->P7, sizeof(F7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->T1->P8, sizeof(F8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->T1->P9, sizeof(F9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->T1->P10, sizeof(F10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->T1->P11, sizeof(F11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->T1->P12, sizeof(F12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->T1->P13, sizeof(F13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->T1->P14, sizeof(F14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &F15Value, d->T1->P15, sizeof(F15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &F16Value, d->T1->P16, sizeof(F16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void CompareVertexPropertyValue_A_big_obj(TEST_T5_struct_t *d, int array_index, int index, bool bool_value)
{
    int ret;
    int64_t A0Value = (int64_t)index;
    uint64_t A1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t A2Value = index;
    uint32_t A3Value = index;
    int16_t A4Value = index & 0x7FFF;
    uint16_t A5Value = index & 0xFFFF;
    int8_t A6Value = index & 0x7F;
    uint8_t A7Value = index & 0xFF;
    bool A8Value = bool_value;
    float A9Value = index;
    double A10Value = index;
    uint64_t A11Value = index + 0xFFFFFFFF;
    char A12Value = 'a' + (index & 0x1A);
    unsigned char A13Value = 'A' + (index & 0x1A);
    char A14Value[16] = {0};
    snprintf((char *)A14Value, sizeof(A14Value), "aaaaaaa%08d", index);
    char A15Value[STRING_LEN] = {0};
    char A16Value[BYTES_LEN] = {0};
    snprintf((char *)A15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)A16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // array
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &A0Value, &d->T2[array_index].A0, sizeof(A0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &A1Value, &d->T2[array_index].A1, sizeof(A1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &A2Value, &d->T2[array_index].A2, sizeof(A2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &A3Value, &d->T2[array_index].A3, sizeof(A3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &A4Value, &d->T2[array_index].A4, sizeof(A4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &A5Value, &d->T2[array_index].A5, sizeof(A5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &A6Value, &d->T2[array_index].A6, sizeof(A6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &A7Value, &d->T2[array_index].A7, sizeof(A7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &A8Value, &d->T2[array_index].A8, sizeof(A8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &A9Value, &d->T2[array_index].A9, sizeof(A9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &A10Value, &d->T2[array_index].A10, sizeof(A10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &A11Value, &d->T2[array_index].A11, sizeof(A11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &A12Value, &d->T2[array_index].A12, sizeof(A12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &A13Value, &d->T2[array_index].A13, sizeof(A13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, A14Value, d->T2[array_index].A14, sizeof(A14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &A15Value, d->T2[array_index].A32, sizeof(A15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &A16Value, d->T2[array_index].A33, sizeof(A16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void CompareVertexPropertyValue_V_big_obj(TEST_T5_struct_t *d, int vector_index, int index, bool bool_value)
{
    int ret;
    int64_t V0Value = (int64_t)index;
    uint64_t V1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t V2Value = index;
    uint32_t V3Value = index;
    int16_t V4Value = index & 0x7FFF;
    uint16_t V5Value = index & 0xFFFF;
    int8_t V6Value = index & 0x7F;
    uint8_t V7Value = index & 0xFF;
    bool V8Value = bool_value;
    float V9Value = index;
    double V10Value = index;
    uint64_t V11Value = index + 0xFFFFFFFF;
    char V12Value = 'a' + (index & 0x1A);
    unsigned char V13Value = 'A' + (index & 0x1A);
    char V14Value[16] = {0};
    snprintf((char *)V14Value, sizeof(V14Value), "aaaaaaa%08d", index);
    char V15Value[STRING_LEN] = {0};
    char V16Value[BYTES_LEN] = {0};
    snprintf((char *)V15Value, STRING_LEN, "b%08d", index);
    snprintf((char *)V16Value, BYTES_LEN, "ABCDEFGHIJKL%08d", index);

    // vector
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &V0Value, &d->T3[vector_index].V0, sizeof(V0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &V1Value, &d->T3[vector_index].V1, sizeof(V1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &V2Value, &d->T3[vector_index].V2, sizeof(V2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &V3Value, &d->T3[vector_index].V3, sizeof(V3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &V4Value, &d->T3[vector_index].V4, sizeof(V4Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &V5Value, &d->T3[vector_index].V5, sizeof(V5Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &V6Value, &d->T3[vector_index].V6, sizeof(V6Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &V7Value, &d->T3[vector_index].V7, sizeof(V7Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &V8Value, &d->T3[vector_index].V8, sizeof(V8Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &V9Value, &d->T3[vector_index].V9, sizeof(V9Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &V10Value, &d->T3[vector_index].V10, sizeof(V10Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &V11Value, &d->T3[vector_index].V11, sizeof(V11Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &V12Value, &d->T3[vector_index].V12, sizeof(V12Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &V13Value, &d->T3[vector_index].V13, sizeof(V13Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, V14Value, d->T3[vector_index].V14, sizeof(V14Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_STRING, &V15Value, d->T3[vector_index].V15, sizeof(V15Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BYTES, &V16Value, d->T3[vector_index].V16, sizeof(V16Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_struct_read_TEST_T5(
    TEST_T5_struct_t *d, int index, int delta, bool bool_value, uint16_t T2_count, uint16_t T3_count)
{
    int ret = 0, i;
    CompareVertexPropertyValue_R_big_obj(d, index + delta, bool_value);
    CompareVertexPropertyValue_P_big_obj(d, index + delta, bool_value);

    // array
    for (i = 0; i < T2_count; ++i) {
        CompareVertexPropertyValue_A_big_obj(d, i, i + delta, bool_value);
    }

    // vector
    for (i = 0; i < T3_count; ++i) {
        CompareVertexPropertyValue_V_big_obj(d, i, i + delta, bool_value);
    }
}

void db_test_struct_read_big_obj_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, bool read_num, int threadId = 0)
{
    int ret, i;
    TEST_T5_struct_t obj = (TEST_T5_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT SCAN", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T5_primary_key(&obj, i);
            } else {
                test_set_TEST_T5_uniq_key(&obj, i);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            bool isFinish = false;
            int cnt = 0;
            while (!isFinish) {
                ret = GmcFetch(stmt, &isFinish);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }
                ret = testStructGetVertexDeseri(stmt, &deseri);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                test_struct_read_TEST_T5(&obj, i, delta, bool_value, T2_count, T3_count);
                cnt++;
            }
            if (read_num) {
                AW_MACRO_EXPECT_EQ_INT(1, cnt);
            } else {
                AW_MACRO_EXPECT_EQ_INT(0, cnt);
            }
        }
    } else {
        TEST_INFO("NON UNIQUE STRUCT SCAN", 0, 1, threadId);
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

// 主键, localhash, hashcluster
void db_test_read_all_big_obj_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, char *keyName, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, bool read_num,
    int threadId = 0)
{
    // 非结构化读
    db_test_read_big_obj_table(
        stmt, start, end, delta, isUniq, bool_value, label_name, keyName, T2_count, T3_count, read_num, threadId);

    // 结构化读(GmcGetVertexDeseri)
    db_test_struct_read_big_obj_table(
        stmt, start, end, delta, isUniq, bool_value, label_name, keyId, T2_count, T3_count, read_num, threadId);
}

void db_test_struct_update_big_obj_table(GmcStmtT *stmt, int start, int end, int delta, bool isUniq, bool bool_value,
    char *label_name, uint32_t keyId, uint16_t T2_count, uint16_t T3_count, int expected_affectRows, int threadId = 0)
{
    int ret, i, j, affectRows;
    bool isPkOper;
    TEST_T5_struct_t obj = (TEST_T5_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT UPDATE", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T5_primary_key(&obj, i);
                isPkOper = true;
            } else {
                test_set_TEST_T5_uniq_key(&obj, i);
                isPkOper = false;
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            GmcNodeT *root, *tree1, *tree2, *tree3;
            ret = GmcGetRootNode(stmt, &root);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T1", &tree1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T2", &tree2);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetChild(root, "T3", &tree3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#if NOT_EXIST
            test_set_node_property_root(root, i + delta, bool_value, true, isPkOper);
            test_set_node_property_t1(tree1, i + delta, bool_value);
            for (j = 0; j < T2_count; j++) {  // fixed_array
                ret = GmcNodeGetElementByIndex(tree2, j, &tree2);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                test_set_node_property_t2(tree2, j + delta, bool_value);
            }
            for (j = 0; j < T3_count; j++) {  // vector
                ret = GmcNodeGetElementByIndex(tree3, j, &tree3);
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
                test_set_node_property_t3(tree3, j + delta, bool_value);
            }
#endif
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
        }
    } else {
        TEST_INFO("NON UNIQUE STRUCT UPDATE", 0, 1, threadId);
    }
}

// 主键 localhash hashcluster
void db_test_struct_delete_big_obj_table(GmcStmtT *stmt, int start, int end, bool isUniq, char *label_name,
    uint32_t keyId, int expected_affectRows, int threadId = 0)
{
    int ret, i, affectRows;
    TEST_T5_struct_t obj = (TEST_T5_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (isUniq) {
        for (i = start; i < end; i++) {
            TEST_INFO("STRUCT DELETE", i, 1000, threadId);
            if (keyId == 0) {
                test_set_TEST_T5_primary_key(&obj, i);
            } else {
                test_set_TEST_T5_uniq_key(&obj, i);
            }
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, keyId, NULL, &labelInfo);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(expected_affectRows, affectRows);
        }
    } else {
        TEST_INFO("NON UNIQUE STRUCT DELETE", 0, 1, threadId);
    }
}
