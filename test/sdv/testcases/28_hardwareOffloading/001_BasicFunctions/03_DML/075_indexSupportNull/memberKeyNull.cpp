/* ****************************************************************************
 Description  :索引支持NULL值member key测试
001.memberKey 8个不同属性非唯一全都为空（设置与不设置）insert和index查询
002. memberKey 非唯一8个不同属性全都为空index更新成非空值
003. memberKey 非唯一8个不同属性全都为空memberKey更新成非空值
004. memberKey 非唯一8个不同属性为非空index更新成空值
005. memberKey 非唯一8个不同属性为非空值memberKey更新成空值
006. memberKey 非唯一8个不同属性为0值append并查询
007. memberKey 非唯一8个不同属性部分空值元素remove再append回去
008. memberKey 唯一8个不同属性为空值append重复插入
009. memberKey 唯一8个不同属性非空值更新成已存在的带空值的元素
010. memberKey 唯一8个不同属性部分空值元素remove再append回去
011. memberKey 唯一8个不同属性为0值插入与空值插入
 Author       : wuxiaochun wx753022
 Modification :
 Date         : 2021/11/5
**************************************************************************** */

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include <sys/time.h>
#include <time.h>

#include "gtest/gtest.h"
#include "indexKeySupportNull.h"

class indexKeySupportNullMemberKey : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void indexKeySupportNullMemberKey::SetUpTestCase()
{
    // 配置相关环境变量及重启server
    InitCfg();
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void indexKeySupportNullMemberKey::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复配置文件
    RecoverCfg();
}

void indexKeySupportNullMemberKey::SetUp()
{
    int ret = 0;
    int64_t valueInt64 = 1;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    char *fixedValue = (char *)"fixeds";
    ret = indexKeySupportNullCreateLabel();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    indexKeyNullLabel1InsertVertex(g_stmt, 0, 10, true, strValue, fixedValue, bytesValue);
    indexKeyNullLabel2InsertVertex(g_stmt, 0, 10, true, strValue, fixedValue, bytesValue);
    AW_CHECK_LOG_BEGIN();
}

void indexKeySupportNullMemberKey::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = GmcDropVertexLabel(g_stmt, g_indexKeyNullLabel1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_indexKeyNullLabel2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

// 001.memberKey 8个不同属性非唯一全都为空（设置与不设置）insert和index查询
TEST_F(indexKeySupportNullMemberKey, HardWare_Offloading_001_DML_075_002_004_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    //不设置memberkey值插入
    for (int32_t i = indexValue; i < indexEnd; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
        indexLpm4Set(root, i);

        // 插入array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey2(T1, j);
            indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    //设置memberkey空值插入
    for (int32_t i = indexEnd; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
        indexLpm4Set(root, i);
        indexKeyNullLabel1SetLocalhashPropertyNull(root);
        // 插入array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey2(T1, j);
            indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
            indexKeyNullSetPropertyMemberKey(T1, j, strValue, fixedValue, true);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // index查
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullGetHashclusterProperty(root, i, fixedValue);
        indexKeyNullTest1GetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullGetLocalKeyProperty(root, i, fixedValue);
        indexKeyNullLabel1GetLocalhashProperty(root, i, i, true, strValue, true);
        indexLpm4Get(root, i);
        // 读取array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullGetNodePropertyMemberKey(T1, j, strValue, fixedValue, true);
            indexKeyNullGetNodePropertyMemberKey2(T1, j);
            indexKeyNullGetNodePropertyExtra(T1, j, true, bytesValue);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. memberKey 非唯一8个不同属性全都为空index更新成非空值
TEST_F(indexKeySupportNullMemberKey, HardWare_Offloading_001_DML_075_002_004_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    GmcNodeT *keyNode = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    //不设置memberkey值插入
    for (int32_t i = indexValue; i < indexEnd; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
        indexLpm4Set(root, i);

        // 插入array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey2(T1, j);
            indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    //设置memberkey空值插入
    for (int32_t i = indexEnd; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
        indexLpm4Set(root, i);
        indexKeyNullLabel1SetLocalhashPropertyNull(root);
        // 插入array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey2(T1, j);
            indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
            indexKeyNullSetPropertyMemberKey(T1, j, strValue, fixedValue, true);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // index更新成非空值0值
    uint8_t fixedValue1[INDEX_FIXED_FIELD_LENGTH] = {0};
    memset(fixedValue1, 0, INDEX_FIXED_FIELD_LENGTH);
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        ret = GmcNodeGetElementByIndex(T1, 0, &T1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullSetPropertyMemberKey(T1, 0, (char *)"", fixedValue1);
        ret = GmcNodeGetElementByIndex(T1, 2, &T1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullSetPropertyMemberKey(T1, 0, (char *)"", fixedValue1);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // check
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullGetHashclusterProperty(root, i, fixedValue);
        indexKeyNullTest1GetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullGetLocalKeyProperty(root, i, fixedValue);
        indexKeyNullLabel1GetLocalhashProperty(root, i, i, true, strValue, true);
        indexLpm4Get(root, i);
        // 读取array节点
        ret = GmcNodeGetElementByIndex(T1, 0, &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexKeyNullGetNodePropertyMemberKey(T1, 0, (char *)"", fixedValue1);
        indexKeyNullGetNodePropertyMemberKey2(T1, 0);
        indexKeyNullGetNodePropertyExtra(T1, 0, true, bytesValue);
        ret = GmcNodeGetElementByIndex(T1, 2, &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexKeyNullGetNodePropertyMemberKey(T1, 0, (char *)"", fixedValue1);
        indexKeyNullGetNodePropertyMemberKey2(T1, 2);
        indexKeyNullGetNodePropertyExtra(T1, 2, true, bytesValue);
        ret = GmcNodeGetElementByIndex(T1, 1, &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        indexKeyNullGetNodePropertyMemberKey(T1, 0, (char *)"1", fixedValue1, true);
        indexKeyNullGetNodePropertyMemberKey2(T1, 1);
        indexKeyNullGetNodePropertyExtra(T1, 1, true, bytesValue);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003. memberKey 非唯一8个不同属性全都为空memberKey更新成非空值
TEST_F(indexKeySupportNullMemberKey, HardWare_Offloading_001_DML_075_002_004_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    GmcNodeT *keyNode = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    //不设置memberkey值插入
    for (int32_t i = indexValue; i < indexEnd; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
        indexLpm4Set(root, i);

        // 插入array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey2(T1, j);
            indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    //设置memberkey空值插入
    for (int32_t i = indexEnd; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
        indexLpm4Set(root, i);
        indexKeyNullLabel1SetLocalhashPropertyNull(root);
        // 插入array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey2(T1, j);
            indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
            indexKeyNullSetPropertyMemberKey(T1, j, strValue, fixedValue, true);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // memberkey更新成非空值
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        for (int32_t j = 0; j < vector_num; j++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_UPDATE);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
            indexKeySupportNullGetNode(g_stmt, &root, &T1);
            GmcIndexKeyT *T1key = NULL;
            ret = GmcNodeAllocKey(T1, g_memberKeyName, &T1key);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetMemberKeyValue(T1key, 0, strValue, fixedValue, true);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeGetElementByKey(T1, T1key, &keyNode);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey(keyNode, j, strValue, fixedValue);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeFreeKey(T1key);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            GmcResetStmt(g_stmt);
        }
    }

    // check
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullGetHashclusterProperty(root, i, fixedValue);
        indexKeyNullTest1GetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullGetLocalKeyProperty(root, i, fixedValue);
        indexKeyNullLabel1GetLocalhashProperty(root, i, i, true, strValue, true);
        indexLpm4Get(root, i);
        // 读取array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            indexKeyNullGetNodePropertyMemberKey(T1, j, strValue, fixedValue);
            indexKeyNullGetNodePropertyMemberKey2(T1, j);
            indexKeyNullGetNodePropertyExtra(T1, j, true, bytesValue);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004. memberKey 非唯一8个不同属性为非空index更新成空值
TEST_F(indexKeySupportNullMemberKey, HardWare_Offloading_001_DML_075_002_004_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    GmcNodeT *keyNode = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;

    // index更新成空值
    uint8_t fixedValue1[INDEX_FIXED_FIELD_LENGTH] = {0};
    memset(fixedValue1, 0, INDEX_FIXED_FIELD_LENGTH);
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey(T1, j, strValue, fixedValue, true);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // check
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullGetHashclusterProperty(root, i, fixedValue);
        indexKeyNullTest1GetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullGetLocalKeyProperty(root, i, fixedValue);
        indexKeyNullLabel1GetLocalhashProperty(root, i, i, true, strValue, false);
        indexLpm4Get(root, i);
        // 读取array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            indexKeyNullGetNodePropertyByName(T1, j, true, strValue, fixedValue, bytesValue);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005. memberKey 非唯一8个不同属性为非空值memberKey更新成空值
TEST_F(indexKeySupportNullMemberKey, HardWare_Offloading_001_DML_075_002_004_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    GmcNodeT *keyNode = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;

    // memberKey更新成空值
    uint8_t fixedValue1[INDEX_FIXED_FIELD_LENGTH] = {0};
    memset(fixedValue1, 0, INDEX_FIXED_FIELD_LENGTH);
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        GmcIndexKeyT *T1key = NULL;
        ret = GmcNodeAllocKey(T1, g_memberKeyName, &T1key);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullSetMemberKeyValue(T1key, 0, strValue, fixedValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetElementByKey(T1, T1key, &keyNode);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullSetPropertyMemberKey(keyNode, 0, strValue, fixedValue, true);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeFreeKey(T1key);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // check
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullGetHashclusterProperty(root, i, fixedValue);
        indexKeyNullTest1GetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullGetLocalKeyProperty(root, i, fixedValue);
        indexKeyNullLabel1GetLocalhashProperty(root, i, i, true, strValue, false);
        indexLpm4Get(root, i);
        // 读取array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            indexKeyNullGetNodePropertyByName(T1, j, true, strValue, fixedValue, bytesValue);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006. memberKey 非唯一8个不同属性为0值append并查询
TEST_F(indexKeySupportNullMemberKey, HardWare_Offloading_001_DML_075_002_004_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    GmcNodeT *keyNode = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    //设置memberkey空值插入
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
        indexLpm4Set(root, i);
        indexKeyNullLabel1SetLocalhashPropertyNull(root);
        // 插入array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey2(T1, j);
            indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
            indexKeyNullSetPropertyMemberKey(T1, j, strValue, fixedValue, true);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // memberkey append 0值
    char strtest[21] = {0};
    memset(strtest, 0, 21);
    uint8_t fixedT[INDEX_FIXED_FIELD_LENGTH] = {0};
    memset(fixedT, 0, INDEX_FIXED_FIELD_LENGTH);
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        for (int32_t m = 3; m < 6; m++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey(T1, 0, strtest, fixedT);
            indexKeyNullSetPropertyMemberKey2(T1, m);
            indexKeyNullSetPropertyNodeT1Extra(T1, 0, true, fixedT);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcResetStmt(g_stmt);
    }

    // check
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullGetHashclusterProperty(root, i, fixedValue);
        indexKeyNullTest1GetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullGetLocalKeyProperty(root, i, fixedValue);
        indexKeyNullLabel1GetLocalhashProperty(root, i, i, true, strValue, true);
        indexLpm4Get(root, i);
        // 读取array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            indexKeyNullGetNodePropertyMemberKey(T1, j, strValue, fixedValue, true);
            indexKeyNullGetNodePropertyMemberKey2(T1, j);
            indexKeyNullGetNodePropertyExtra(T1, j, true, bytesValue);
        }
        for (uint32_t j = 3; j < 6; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            indexKeyNullGetNodePropertyMemberKey(T1, 0, strtest, fixedT);
            indexKeyNullGetNodePropertyMemberKey2(T1, j);
            indexKeyNullGetNodePropertyExtra(T1, 0, true, fixedT);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007. memberKey 非唯一8个不同属性部分空值元素remove再append回去
TEST_F(indexKeySupportNullMemberKey, HardWare_Offloading_001_DML_075_002_004_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    GmcNodeT *keyNode = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    //设置memberkey空值插入
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
        indexLpm4Set(root, i);
        indexKeyNullLabel1SetLocalhashPropertyNull(root);
        // 插入array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey2(T1, j);
            indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
            indexKeyNullSetPropertyMemberKey(T1, j, strValue, fixedValue, true);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // memberKey remove
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
         
        for (int32_t j = 0; j < vector_num; j++) {
            ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_UPDATE);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
            indexKeySupportNullGetNode(g_stmt, &root, &T1);
            GmcIndexKeyT *T1key = NULL;
            ret = GmcNodeAllocKey(T1, g_memberKeyName, &T1key);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetMemberKeyValue(T1key, 0, strValue, fixedValue, true);
            ret = GmcNodeRemoveElementByKey(T1, T1key);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = TestAffactRows(g_stmt, 1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcNodeFreeKey(T1key);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }

    // memberkey append NULL值
    char strtest[21] = {0};
    memset(strtest, 0, 21);
    uint8_t fixedT[7] = {0};
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
         
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        for (int32_t m = 0; m < 3; m++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey(T1, 0, strtest, fixedT, true);
            indexKeyNullSetPropertyMemberKey2(T1, m);
            indexKeyNullSetPropertyNodeT1Extra(T1, 0, true, fixedT);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcResetStmt(g_stmt);
    }

    // check
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel1, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName, i, strValue, bytesValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullGetHashclusterProperty(root, i, fixedValue);
        indexKeyNullTest1GetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullGetLocalKeyProperty(root, i, fixedValue);
        indexKeyNullLabel1GetLocalhashProperty(root, i, i, true, strValue, true);
        indexLpm4Get(root, i);
        // 读取array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            indexKeyNullGetNodePropertyMemberKey(T1, j, strValue, fixedValue, true);
            indexKeyNullGetNodePropertyMemberKey2(T1, j);
            indexKeyNullGetNodePropertyExtra(T1, 0, true, fixedT);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008. memberKey 唯一8个不同属性为空值append重复插入
TEST_F(indexKeySupportNullMemberKey, HardWare_Offloading_001_DML_075_002_004_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    GmcNodeT *keyNode = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    uint8_t f18_value[LPM6_FIXED_FIELD_LENGTH] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_MEMBER_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    //设置memberkey空值插入
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel2, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
        f18_value[15] = i;
        indexLpm6Set(root, i, f18_value);
        indexKeyNullLabel1SetLocalhashPropertyNull(root);
        // 插入array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey2(T1, j);
            indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
            indexKeyNullSetPropertyMemberKey(T1, j, strValue, fixedValue, true);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_MEMBER_KEY_VIOLATION, ret);
        ret = TestAffactRows(g_stmt, 0);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009. memberKey 唯一8个不同属性非空值更新成空值的元素
TEST_F(indexKeySupportNullMemberKey, HardWare_Offloading_001_DML_075_002_004_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    GmcNodeT *keyNode = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    uint8_t f18_value[LPM6_FIXED_FIELD_LENGTH] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};

    // memberKey更新成空值
    uint8_t fixedValue1[INDEX_FIXED_FIELD_LENGTH] = {0};
    memset(fixedValue1, 0, INDEX_FIXED_FIELD_LENGTH);
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel2, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName2, i, strValue, bytesValue);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        GmcIndexKeyT *T1key = NULL;
        ret = GmcNodeAllocKey(T1, g_memberKeyNameUnique, &T1key);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullSetMemberKeyValue(T1key, 0, strValue, fixedValue, false);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeGetElementByKey(T1, T1key, &keyNode);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullSetPropertyMemberKey(keyNode, 0, strValue, fixedValue, true);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeFreeKey(T1key);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // check
    for (int32_t i = startNum; i < endNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel2, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName2, i, strValue, bytesValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullGetHashclusterProperty(root, i, fixedValue);
        indexKeyNullTest1GetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullGetLocalKeyProperty(root, i, fixedValue);
        indexKeyNullLabel1GetLocalhashProperty(root, i, i, true, strValue, false);
        f18_value[15] = i;
        indexLpm6Get(root, i, f18_value);
        // 读取array节点
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            indexKeyNullGetNodePropertyByName(T1, j, true, strValue, fixedValue, bytesValue);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010. memberKey 唯一8个不同属性部分空值元素remove再append回去
TEST_F(indexKeySupportNullMemberKey, HardWare_Offloading_001_DML_075_002_004_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    GmcNodeT *keyNode = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 105;
    int32_t indexEnd2 = 110;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    uint8_t f18_value[LPM6_FIXED_FIELD_LENGTH] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};

    //设置memberkey空值插入
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel2, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
        f18_value[15] = i;
        indexLpm6Set(root, i, f18_value);
        indexKeyNullLabel2SetLocalhashProperty(root, i, false, bytesValue, fixedValue);
        // 插入array节点
        for (uint32_t j = 0; j < 1; j++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey2(T1, j);
            indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
            indexKeyNullSetPropertyMemberKey(T1, j, strValue, fixedValue, true);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    // memberKey remove
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
         
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel2, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName2, i, strValue, bytesValue);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        GmcIndexKeyT *T1key = NULL;
        ret = GmcNodeAllocKey(T1, g_memberKeyNameUnique, &T1key);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullSetMemberKeyValue(T1key, 0, strValue, fixedValue, true);
        ret = GmcNodeRemoveElementByKey(T1, T1key);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcNodeFreeKey(T1key);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // memberkey append NULL值
    char strtest[21] = {0};
    memset(strtest, 0, 21);
    uint8_t fixedT[7] = {0};
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel2, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName2, i, strValue, bytesValue);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        for (int32_t m = 0; m < 1; m++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey(T1, 0, strtest, fixedT, true);
            indexKeyNullSetPropertyMemberKey2(T1, m);
            indexKeyNullSetPropertyNodeT1Extra(T1, 0, true, fixedT);
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcResetStmt(g_stmt);
    }

    // check
    for (int32_t i = indexValue; i < indexEnd2; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel2, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName2, i, strValue, bytesValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullGetHashclusterProperty(root, i, fixedValue);
        indexKeyNullTest1GetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullGetLocalKeyProperty(root, i, fixedValue);
        indexKeyNullLabel2GetLocalhashProperty(root, i, false, bytesValue, fixedValue, false);
        f18_value[15] = i;
        indexLpm6Get(root, i, f18_value);
        // 读取array节点
        for (uint32_t j = 0; j < 1; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            indexKeyNullGetNodePropertyMemberKey(T1, j, strValue, fixedValue, true);
            indexKeyNullGetNodePropertyMemberKey2(T1, j);
            indexKeyNullGetNodePropertyExtra(T1, 0, true, fixedT);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011. memberKey 唯一8个不同属性为0值插入与空值插入
TEST_F(indexKeySupportNullMemberKey, HardWare_Offloading_001_DML_075_002_004_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    GmcNodeT *root = NULL, *T1 = NULL;
    GmcNodeT *keyNode = NULL;
    int32_t indexValue = 100;
    int32_t indexEnd = 102;
    bool isFinish = false;
    char *strValue = (char *)"indexKeyNullTest";
    char *bytesValue = (char *)"1111111";
    uint8_t bytesPk[INDEX_FIXED_FIELD_LENGTH] = {0};
    char *fixedValue = (char *)"fixeds";
    int32_t vector_num = 3;
    bool isNull = false;
    uint8_t f18_value[LPM6_FIXED_FIELD_LENGTH] = {
        0xcd, 0xcd, 0x91, 0x0a, 0x22, 0x22, 0x54, 0x98, 0x84, 0x75, 0x11, 0x11, 0x39, 0x00, 0x20, 0x21};
    uint8_t fixedT[8] = {0};
    memset(fixedT, 0, INDEX_FIXED_FIELD_LENGTH);
    char strtest[21] = "";

    //设置memberkey空值和0值插入
    for (int32_t i = indexValue; i < indexEnd; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel2, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullSetHashclusterProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullSetLocalKeyProperty(root, i, (uint8_t *)fixedValue);
        indexKeyNullTest1SetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullSetPk(root, &valueInt64, strValue, bytesValue);
        f18_value[15] = i;
        indexLpm6Set(root, i, f18_value);
        indexKeyNullLabel2SetLocalhashProperty(root, i, false, bytesValue, fixedValue);
        // 插入array节点
        for (uint32_t j = 0; j < 1; j++) {
            ret = GmcNodeAppendElement(T1, &T1);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            indexKeyNullSetPropertyMemberKey2(T1, j);
            indexKeyNullSetPropertyNodeT1Extra(T1, j, true, bytesValue);
            if (i == indexValue) {
                indexKeyNullSetPropertyMemberKey(T1, j, strValue, fixedValue, true);
            } else {
                indexKeyNullSetPropertyMemberKey(T1, 0, strtest, fixedT, false);
            }
        }
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = TestAffactRows(g_stmt, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // check
    for (int32_t i = indexValue; i < indexEnd; i++) {
        int64_t valueInt64 = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_indexKeyNullLabel2, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeyNullLabel1SetPkIndex(g_stmt, g_PkName2, i, strValue, bytesValue);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        indexKeySupportNullGetNode(g_stmt, &root, &T1);
        indexKeyNullGetHashclusterProperty(root, i, fixedValue);
        indexKeyNullTest1GetPropertyExtra(root, i, strValue, bytesValue);
        indexKeyNullGetLocalKeyProperty(root, i, fixedValue);
        indexKeyNullLabel1GetLocalhashProperty(root, i, i, false, strValue, false);
        f18_value[15] = i;
        indexLpm6Get(root, i, f18_value);
        // 读取array节点
        for (uint32_t j = 0; j < 1; j++) {
            ret = GmcNodeGetElementByIndex(T1, j, &T1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (i == indexValue) {
                indexKeyNullGetNodePropertyMemberKey(T1, j, strValue, fixedValue, true);
            } else {
                indexKeyNullGetNodePropertyMemberKey(T1, 0, strtest, fixedT, false);
            }
            indexKeyNullGetNodePropertyMemberKey2(T1, j);
            indexKeyNullGetNodePropertyExtra(T1, 0, true, bytesValue);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
