[{"version": "2.0", "type": "record", "name": "Tree_Filter_03", "special_complex": true, "fields": [{"name": "F0", "type": "uint32", "nullable": false}, {"name": "F1", "type": "uint32", "nullable": false}, {"name": "F2", "type": "uint32", "nullable": false}, {"name": "F3", "type": "uint32", "nullable": false}, {"name": "F4", "type": "string", "size": 10, "nullable": true}, {"name": "T1", "type": "record", "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "string", "size": 10, "nullable": true}]}, {"name": "A1", "type": "record", "fixed_array": true, "size": 10, "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "string", "size": 10, "nullable": true}]}, {"name": "V1", "type": "record", "vector": true, "size": 10, "fields": [{"name": "F0", "type": "uint32", "nullable": true}, {"name": "F1", "type": "uint32", "nullable": true}, {"name": "F2", "type": "uint32", "nullable": true}, {"name": "F3", "type": "uint32", "nullable": true}, {"name": "F4", "type": "string", "size": 10, "nullable": true}]}], "keys": [{"name": "Tree_pk", "index": {"type": "primary"}, "node": "Tree_Filter_03", "fields": ["F0"], "constraints": {"unique": true}}, {"name": "Tree_localhash", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "Tree_Filter_03", "fields": ["F1"], "constraints": {"unique": true}}, {"name": "Tree_hashcluster", "index": {"type": "hashcluster"}, "node": "Tree_Filter_03", "fields": ["F2"], "constraints": {"unique": false}}, {"name": "Tree_local", "index": {"type": "local"}, "node": "Tree_Filter_03", "fields": ["F3"], "constraints": {"unique": false}}]}]