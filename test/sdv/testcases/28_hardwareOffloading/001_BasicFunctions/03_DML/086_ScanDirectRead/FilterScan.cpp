/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: 086_ScanDirectRead
 * Author: hanyang
 * Create: 2022-3-16
 */
#include "FilterScan_test.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
uint32_t dest_ip_addr = trans_ip("************");

class FilterScan_test : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void FilterScan_test::SetUpTestCase()
{
    // 配置相关环境变量及重启server
    InitCfg();
    
    int ret;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void FilterScan_test::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复配置文件
    RecoverCfg();
}

void FilterScan_test::SetUp()
{
    int ret;
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 创建Vertex和Tree表
    testCreateLabel(g_stmt);

    AW_CHECK_LOG_BEGIN();
}

void FilterScan_test::TearDown()
{
    int ret;
    AW_CHECK_LOG_END();

    // 删除Vertex和Tree表
    testDropLabel(g_stmt);

    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
}

/*****************************************************************************
 Description  : 001.GmcSetFilterStructure接口stmt参数为NULL
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcFilterStructT FilterStruct;

    ret = GmcSetFilterStructure(NULL, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 002.GmcSetFilterStructure接口filter参数为NULL
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcFilterStructT FilterStruct;

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetFilterStructure(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 003.filter参数中fieldId超出实际存在的最大ID值
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcFilterStructT FilterStruct;
    uint32_t value = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 根节点
    FilterStruct.fieldId = 20;
    FilterStruct.nodeName = NULL;
    FilterStruct.compOp = GMC_OP_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint32_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // T1节点
    FilterStruct.fieldId = 20;
    FilterStruct.nodeName = "T1";
    FilterStruct.compOp = GMC_OP_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint32_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_PROPERTY);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 004.filter参数中fieldId和属性value，valuelen不匹配
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcFilterStructT FilterStruct;
    uint8_t value = 0;
    char *TLabel_schema4 = NULL;

    readJanssonFile("schema_file/Tree_Filter_04.gmjson", &TLabel_schema4);
    ASSERT_NE((void *)NULL, TLabel_schema4);
    ret = GmcCreateVertexLabel(g_stmt, TLabel_schema4, MS_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(TLabel_schema4);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "Tree_Filter_04", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 根节点，fieldId和value不匹配
    FilterStruct.fieldId = 3;
    FilterStruct.nodeName = NULL;
    FilterStruct.compOp = GMC_OP_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint8_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // T1节点，fieldId和value不匹配
    FilterStruct.fieldId = 3;
    FilterStruct.nodeName = "T1";
    FilterStruct.compOp = GMC_OP_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint8_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_NAME);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, "Tree_Filter_04");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 005.filter参数中nodeName为不存在的node
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcFilterStructT FilterStruct;
    uint32_t value = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // nodeName为不存在的node
    FilterStruct.fieldId = 2;
    FilterStruct.nodeName = "T2";
    FilterStruct.compOp = GMC_OP_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint32_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_NAME, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_NAME);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 006.filter参数中nodeName为fixed array下的属性
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcFilterStructT FilterStruct;
    uint32_t value = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FilterStruct.fieldId = 1;
    FilterStruct.nodeName = "A1";
    FilterStruct.compOp = GMC_OP_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint32_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 007.filter参数中nodeName为vector下的属性
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcFilterStructT FilterStruct;
    uint32_t value = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FilterStruct.fieldId = 1;
    FilterStruct.nodeName = "V1";
    FilterStruct.compOp = GMC_OP_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint32_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 008.filter参数中compOp超出枚举GmcFilterCompSymbolE的范围
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcFilterStructT FilterStruct;
    uint32_t value = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FilterStruct.fieldId = 1;
    FilterStruct.nodeName = NULL;
    FilterStruct.compOp = GMC_OP_INVALID;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint32_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_VALUE);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 009.filter参数中value为NULL
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcFilterStructT FilterStruct;
    uint32_t value = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FilterStruct.fieldId = 1;
    FilterStruct.nodeName = NULL;
    FilterStruct.compOp = GMC_OP_EQUAL;
    FilterStruct.value = NULL;
    FilterStruct.valueLen = sizeof(uint32_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_NULL_VALUE_NOT_ALLOWED);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 010.filter参数中valueLen为0
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    GmcFilterStructT FilterStruct;
    uint32_t value = 0;

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    FilterStruct.fieldId = 1;
    FilterStruct.nodeName = NULL;
    FilterStruct.compOp = GMC_OP_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = 0;

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_VALUE);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 011.一般复杂表插入数据，结构化写入1个filter（定长），全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F0 >= 60, 预期扫描到40个
    uint32_t expectNum = 40;
    uint32_t value = 60;
    uint32_t fieldId = 0;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 012.一般复杂表插入数据，结构化写入1个filter（变长），全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F4 = string087, 预期扫描到1个
    uint32_t expectNum = 1;
    uint32_t value = 87;
    uint32_t fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 013.一般复杂表插入数据，结构化写入4个filter（定长和变长混合），全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 014.一般复杂表插入数据（10W），结构化写入8个filter（定长和变长混合），全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = RECORD_NUM_001;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1>20, F1<RECORD_NUM_001, F2=50, F2>20,
    // F3>=40, F3<=60, F4 >= string040, F4 <= string060,
    // 预期扫描到(RECORD_NUM_001/100)个
    uint32_t expectNum = RECORD_NUM_001 / 100;
    uint32_t value = 20;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = RECORD_NUM_001;
    fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 50;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_EQUAL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 40;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 015.一般复杂表插入数据，结构化写入1个filter（定长），local索引范围扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60, 预期扫描到30个
    uint32_t expectNum = 30;
    uint32_t value = 60;
    uint32_t fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 范围10-89
    uint32_t l_val = 10;
    uint32_t r_val = 89;
    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetKeyRange(g_stmt, l_val, r_val);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 016.一般复杂表插入数据，结构化写入1个filter（变长），local索引范围扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F4 >= string087, 预期扫描到3个
    uint32_t expectNum = 3;
    uint32_t value = 87;
    uint32_t fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 范围10-89
    uint32_t l_val = 10;
    uint32_t r_val = 89;
    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetKeyRange(g_stmt, l_val, r_val);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 017.一般复杂表插入数据，结构化写入4个filter（定长和变长混合），local索引范围扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 范围10-89
    uint32_t l_val = 10;
    uint32_t r_val = 89;
    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetKeyRange(g_stmt, l_val, r_val);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 018.一般复杂表插入数据（10W），结构化写入8个filter（定长和变长混合），local索引范围扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = RECORD_NUM_001;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1>20, F1<RECORD_NUM_001, F2=50, F2>20,
    // F3>=40, F3<=60, F4 >= string040, F4 <= string060,
    // 预期扫描到(RECORD_NUM_001/100)个
    uint32_t expectNum = RECORD_NUM_001 / 100;
    uint32_t value = 20;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = RECORD_NUM_001;
    fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 50;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_EQUAL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 40;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    // 范围10-89
    uint32_t l_val = 10;
    uint32_t r_val = 89;
    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetKeyRange(g_stmt, l_val, r_val);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 019.一般复杂表插入数据，结构化写入1个filter（定长），hash cluster扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60, 预期扫描到1个
    uint32_t expectNum = 1;
    uint32_t value = 60;
    uint32_t fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 索引值
    uint32_t indexValue = 80;
    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &indexValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 020.一般复杂表插入数据，结构化写入1个filter（变长），hash cluster扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F4 = string087, 预期扫描到0个
    uint32_t expectNum = 0;
    uint32_t value = 87;
    uint32_t fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_EQUAL);

    // 索引值
    uint32_t indexValue = 80;
    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &indexValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 021.一般复杂表插入数据，结构化写入4个filter（定长和变长混合），hash cluster扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到1个
    uint32_t expectNum = 1;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 索引值
    uint32_t indexValue = 50;
    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &indexValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 022.一般复杂表插入数据（10W），结构化写入8个filter（定长和变长混合），hash cluster扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = RECORD_NUM_001;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1>20, F1<RECORD_NUM_001, F2=50, F2>20,
    // F3>=40, F3<=60, F4 >= string040, F4 <= string060,
    // 预期扫描到(RECORD_NUM_001/100)个
    uint32_t expectNum = RECORD_NUM_001 / 100;
    uint32_t value = 20;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = RECORD_NUM_001;
    fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 50;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_EQUAL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 40;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    // 索引值
    uint32_t indexValue = 50;
    ret = GmcSetIndexKeyName(g_stmt, Vertex_HashClusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &indexValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 023.一般复杂表插入数据，结构化写入4个filter（定长和变长混合），localhash（唯一）扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到1个
    uint32_t expectNum = 1;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 索引值
    uint32_t indexValue = 50;
    ret = GmcSetIndexKeyName(g_stmt, Vertex_LocalName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &indexValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 024.一般复杂表插入数据，结构化写入4个filter（定长和变长混合），Lpm扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_02);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_02, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到1个
    uint32_t expectNum = 1;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 索引值
    ret = GmcSetIndexKeyName(g_stmt, Vertex_lPM4Name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t vr_id = 5;
    uint32_t vrf_index = 55;
    uint8_t mask_len = 24;

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 025.Tree表插入数据，结构化写入1个filter（定长），过滤条件在record节点上，全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60, 预期扫描到40个
    uint32_t expectNum = 40;
    uint32_t value = 60;
    uint32_t fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 026.Tree表插入数据，结构化写入1个filter（变长），过滤条件在record节点上，全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F4 = string087, 预期扫描到1个
    uint32_t expectNum = 1;
    uint32_t value = 87;
    uint32_t fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 027.Tree表插入数据，结构化写入4个filter（定长和变长混合），
                过滤条件在record节点上，全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 028.Tree表插入数据（10W），结构化写入8个filter（定长和变长混合），
                过滤条件在record节点上，全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = RECORD_NUM_001;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1>20, F1<RECORD_NUM_001, F2=50, F2>20,
    // F3>=40, F3<=60, F4 >= string040, F4 <= string060,
    // 预期扫描到(RECORD_NUM_001/100)个
    uint32_t expectNum = RECORD_NUM_001 / 100;
    uint32_t value = 20;
    uint32_t fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = RECORD_NUM_001;
    fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 50;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_EQUAL);

    value = 20;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 40;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 029.Tree表插入数据，结构化写入1个filter（定长），过滤条件在record节点上，
                local索引范围扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60, 预期扫描到30个
    uint32_t expectNum = 30;
    uint32_t value = 60;
    uint32_t fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 范围10-89
    uint32_t l_val = 10;
    uint32_t r_val = 89;
    ret = GmcSetIndexKeyName(g_stmt, Tree_LocalName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetKeyRange(g_stmt, l_val, r_val);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 030.Tree表插入数据，结构化写入1个filter（变长），过滤条件在record节点上，
                local索引范围扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F4 = string087, 预期扫描到1个
    uint32_t expectNum = 1;
    uint32_t value = 87;
    uint32_t fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_EQUAL);

    // 范围10-89
    uint32_t l_val = 10;
    uint32_t r_val = 89;
    ret = GmcSetIndexKeyName(g_stmt, Tree_LocalName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetKeyRange(g_stmt, l_val, r_val);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 031.Tree表插入数据，结构化写入4个filter（定长和变长混合），
                过滤条件在record节点上，local索引范围扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 范围10-89
    uint32_t l_val = 10;
    uint32_t r_val = 89;
    ret = GmcSetIndexKeyName(g_stmt, Tree_LocalName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetKeyRange(g_stmt, l_val, r_val);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 032.Tree表插入数据（10W），结构化写入8个filter（定长和变长混合），
                过滤条件在record节点上，local索引范围扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = RECORD_NUM_001;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1>20, F1<RECORD_NUM_001, F2=50, F2>20,
    // F3>=40, F3<=60, F4 >= string040, F4 <= string060,
    // 预期扫描到(RECORD_NUM_001/100)个
    uint32_t expectNum = RECORD_NUM_001 / 100;
    uint32_t value = 20;
    uint32_t fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = RECORD_NUM_001;
    fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 50;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_EQUAL);

    value = 20;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 40;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    // 范围10-89
    uint32_t l_val = 10;
    uint32_t r_val = 89;
    ret = GmcSetIndexKeyName(g_stmt, Tree_LocalName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSetKeyRange(g_stmt, l_val, r_val);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 033.Tree表插入数据，结构化写入1个filter（定长），过滤条件在record节点上，
                hash cluster扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60, 预期扫描到1个
    uint32_t expectNum = 1;
    uint32_t value = 60;
    uint32_t fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 索引值
    uint32_t indexValue = 80;
    ret = GmcSetIndexKeyName(g_stmt, Tree_HashClusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &indexValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 034.Tree表插入数据，结构化写入1个filter（变长），过滤条件在record节点上，
                hash cluster扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F4 = string087, 预期扫描到0个
    uint32_t expectNum = 0;
    uint32_t value = 87;
    uint32_t fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_EQUAL);

    // 索引值
    uint32_t indexValue = 80;
    ret = GmcSetIndexKeyName(g_stmt, Tree_HashClusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &indexValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 035.Tree表插入数据，结构化写入4个filter（定长和变长混合），
                过滤条件在record节点上，hash cluster扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到1个
    uint32_t expectNum = 1;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 索引值
    uint32_t indexValue = 50;
    ret = GmcSetIndexKeyName(g_stmt, Tree_HashClusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &indexValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 036.Tree表插入数据（10W），结构化写入8个filter（定长和变长混合），
                过滤条件在record节点上，hash cluster扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = RECORD_NUM_001;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1>20, F1<RECORD_NUM_001, F2=50, F2>20,
    // F3>=40, F3<=60, F4 >= string040, F4 <= string060,
    // 预期扫描到(RECORD_NUM_001/100)个
    uint32_t expectNum = RECORD_NUM_001 / 100;
    uint32_t value = 20;
    uint32_t fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = RECORD_NUM_001;
    fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 50;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_EQUAL);

    value = 20;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 40;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    // 索引值
    uint32_t indexValue = 50;
    ret = GmcSetIndexKeyName(g_stmt, Tree_HashClusterName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &indexValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 037.Tree表插入数据，结构化写入4个filter（定长和变长混合），
                过滤条件在record节点上，localhash（唯一）扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到1个
    uint32_t expectNum = 1;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 索引值
    uint32_t indexValue = 50;
    ret = GmcSetIndexKeyName(g_stmt, Tree_LocalHashName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &indexValue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 038.Tree表插入数据，结构化写入4个filter（定长和变长混合），
                过滤条件在record节点上，Lpm扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_02);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_02, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到1个
    uint32_t expectNum = 1;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 索引值
    ret = GmcSetIndexKeyName(g_stmt, Tree_lPM4Name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t vr_id = 5;
    uint32_t vrf_index = 55;
    
    uint8_t mask_len = 24;

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &vr_id, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_UINT32, &vrf_index, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_UINT32, &dest_ip_addr, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_UINT8, &mask_len, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 039.一般复杂表结构化插入数据，结构化写入4个filter（定长和变长混合），全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 结构化写
    GtVertexFilter03CfgT vertexCfg = {0, 100, 0, 1, 0};
    ret = GtVertexFilter03StructReplace(g_stmt, vertexCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_03, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 040.一般复杂表结构化插入数据，结构化写入4个filter（定长和变长混合），
                结构化local范围扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 结构化写
    GtVertexFilter03CfgT vertexCfg = {0, 100, 0, 1, 0};
    ret = GtVertexFilter03StructReplace(g_stmt, vertexCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GtVertexFilter03VertexT obj = (GtVertexFilter03VertexT){0};
    GtVertexFilter03VertexT lKey = (GtVertexFilter03VertexT){0};
    GtVertexFilter03VertexT rKey = (GtVertexFilter03VertexT){0};
    TestLabelInfoT labelInfo = {(char *)Vertex_Name_03, 0, g_testNameSpace};
    GtVertexFilter03VertexT *leftKey = NULL;
    GtVertexFilter03VertexT *rightKey = NULL;
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(g_stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_03, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 范围10-89
    uint32_t l_val = 10;
    uint32_t r_val = 89;
    unsigned int arrLen = 1;
    uint32_t keyId = 3;

    // 设置左key
    leftKey = &lKey;
    leftKey->F3 = l_val;

    // 设置右key
    rightKey = &rKey;
    rightKey->F3 = r_val;

    GmcRangeItemFlagT items[arrLen];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;

    TestVertexLabelT *vertexLabel;
    ret = TestGetVertexLabelFromCtx(&labelInfo, &vertexLabel);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcRangeKeySeriT rangeKeyInfo;
    rangeKeyInfo.keyId = keyId;
    structTestCtx localseriCtx = (structTestCtx){0};
    localseriCtx.vertexLabel = vertexLabel;
    GmcSeriT leftKeySeri = (GmcSeriT){0};
    GmcSeriT rightKeySeri = (GmcSeriT){0};
    structSetKeySeri(g_stmt, leftKey, &leftKeySeri, &localseriCtx, keyId, NULL);
    rangeKeyInfo.leftKeySeri = &leftKeySeri;
    structSetKeySeri(g_stmt, rightKey, &rightKeySeri, &localseriCtx, keyId, NULL);
    rangeKeyInfo.rightKeySeri = &rightKeySeri;

    ret = GmcSetKeyRangeStructure(g_stmt, items, arrLen, &rangeKeyInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);

    deSeriFreeDynMem(&deseriCtx, true);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 041.Tree表结构化插入数据，结构化写入4个filter（定长和变长混合），
                过滤条件在record节点上，全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    // 结构化写
    GtTreeFilter03CfgT vertexCfg = {0, 100, 0, 1, 0, 10, 10};
    ret = GtTreeFilter03StructReplace(g_stmt, vertexCfg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_03, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 042.一般复杂表插入数据，结构化写入4个filter（定长和变长混合），全表扫描过滤，
                同一stmt连续执行2次GmcExecute
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 第一次GmcExecute
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 第二次GmcExecute
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 043.一般复杂表插入数据，结构化写入4个filter（定长和变长混合），全表扫描过滤，
                Excute后不fetch，增加filter，再次excute
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 第一次GmcExecute
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 增加filter，F1=50，预期扫描到1个，第二次GmcExecute
    expectNum = 1;
    value = 50;
    fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_EQUAL);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 044.一般复杂表插入数据，结构化写入4个filter（定长和变长混合），全表扫描过滤，
                GmcExecute后不GmcFetch获取数据，再次执行prepare（SCAN）和GmcExecute
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // GmcExecute后不GmcFetch获取数据
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次prepare
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不同的条件：F1<70, F2>20, F3<=40, F4 >= string030, 预期扫描到11个
    expectNum = 11;
    value = 70;
    fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 40;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 30;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 045.一般复杂表插入数据，结构化写入4个filter（定长和变长混合），全表扫描过滤，
                不GmcExecute，直接GmcFetch
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 不GmcExecute
    bool isFinish = false;
    int cnt = 0;

    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 046.一般复杂表插入数据，结构化写入4个filter（定长和变长混合），全表扫描过滤，
                GmcExecute后连接中断，重新建立连接后，进行DML操作和全表扫描过滤操作
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // GmcExecute后连接中断
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 中断连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 再次prepare
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不同的条件：F1<70, F2>20, F3<=40, F4 >= string030, 预期扫描到11个
    expectNum = 11;
    value = 70;
    fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 40;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 30;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 047.一般复杂表插入数据，结构化写入4个filter（定长和变长混合），全表扫描过滤，
                GmcFetch后连接中断，重新建立连接后，进行DML操作和全表扫描过滤操作
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // GmcFetch后连接中断
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish1 = false;
    ret = GmcFetch(g_stmt, &isFinish1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 中断连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;

    // 重建连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 再次prepare
    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 不同的条件：F1<70, F2>20, F3<=40, F4 >= string030, 预期扫描到11个
    expectNum = 11;
    value = 70;
    fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 40;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 30;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 048.一般复杂表插入数据，结构化写入9个filter，全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1>20, F1<80, F2<=60, F2>20,
    // F3>=40, F3<=60, F4 >= string040, F4 <= string060,
    // 第九个条件：F2=50
    // 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 20;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 80;
    fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 60;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 40;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    value = 60;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    // 第九个条件
    value = 50;
    fieldId = 2;
    GmcFilterStructT FilterStruct;
    FilterStruct.fieldId = fieldId;
    FilterStruct.nodeName = NULL;
    FilterStruct.compOp = GMC_OP_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint32_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_PROGRAM_LIMIT_EXCEEDED);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 049.Tree表插入数据，结构化写入1个filter，过滤条件在第32层record节点上，
                nodeName部分路径，全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel32(g_stmt, times, initValue, Tree_Name_32);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_32, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // T31.F2 >= 60, 预期扫描到40个
    uint32_t expectNum = 40;
    uint32_t value = 60;
    uint32_t fieldId = 2;
    GmcFilterStructT FilterStruct;
    FilterStruct.fieldId = fieldId;
    FilterStruct.nodeName = "T31";
    FilterStruct.compOp = GMC_OP_LARGE_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint32_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 050. 先设置结构化filter，再设置condition语句，全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60, 预期扫描到40个
    uint32_t expectNum = 40;
    uint32_t value = 60;
    uint32_t fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 设置cond：F1 < 50, 预期扫描到50个
    expectNum = 50;
    char *cond1 = (char *)"Vertex_Filter_01.F1<50";
    ret = GmcSetFilter(g_stmt, cond1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 051.先设置condition语句，再设置结构化filter，全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置cond：F1 < 50, 预期扫描到50个
    uint32_t expectNum = 50;
    char *cond1 = (char *)"Vertex_Filter_01.F1<50";
    ret = GmcSetFilter(g_stmt, cond1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60, 预期扫描到40个
    expectNum = 40;
    uint32_t value = 60;
    uint32_t fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 052.先设置结构化filter，再设置condition语句，再设置结构化filter，全表扫描过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60, 预期扫描到40个
    uint32_t expectNum = 40;
    uint32_t value = 60;
    uint32_t fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 设置cond：F1 < 50, 预期扫描到50个
    expectNum = 50;
    char *cond1 = (char *)"Vertex_Filter_01.F1<50";
    ret = GmcSetFilter(g_stmt, cond1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次设置F2 >= 70, 预期扫描到30个
    expectNum = 30;
    value = 70;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 053.结构化filter和limit并存
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60, 预期扫描到40个
    uint32_t expectNum = 40;
    uint32_t value = 60;
    uint32_t fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 设置GmcSetScanLimit
    ret = GmcSetScanLimit(g_stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 054.结构化filter和GmcSetOutputFormat并存
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60, 预期扫描到40个
    uint32_t expectNum = 40;
    uint32_t value = 60;
    uint32_t fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 设置GmcSetOutputFormat
    ret = GmcSetOutputFormat(g_stmt, "F1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_INVALID_PROPERTY);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 055.一般复杂表插入数据，结构化写入4个filter，主键查询过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStru(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 设置主键key=20，预期查询到1个
    uint32_t keyvalue = 20;
    expectNum = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, Vertex_KeyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;

        // 校验值
        TestGmcGetVertexPropertyByName(g_stmt, keyvalue);
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 056.Tree表插入数据，结构化写入4个filter，过滤条件在record节点上，主键查询过滤
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertTreeLabel(g_stmt, times, initValue, Tree_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Tree_Name_01, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F1<80, F2>20, F3<=60, F4 >= string040, 预期扫描到21个
    uint32_t expectNum = 21;
    uint32_t value = 80;
    uint32_t fieldId = 1;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL);

    value = 20;
    fieldId = 2;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE);

    value = 60;
    fieldId = 3;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_SMALL_EQUAL);

    value = 40;
    fieldId = 4;
    testSetFilterStruTree(g_stmt, fieldId, &value, GMC_OP_LARGE_EQUAL);

    // 设置主键key=20，预期查询到1个
    uint32_t keyvalue = 20;
    expectNum = 1;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &keyvalue, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, Tree_KeyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;

    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        if (GMERR_OK != ret) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = testGmcGetLastError();
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            break;
        }
        if (isFinish) {
            break;
        }
        cnt++;

        // 校验值
        TestGmcGetVertexPropertyByName(g_stmt, keyvalue);
    }

    AW_MACRO_EXPECT_EQ_INT(expectNum, cnt);
    AW_FUN_Log(LOG_STEP, "test end.");
}


/*****************************************************************************
 Description  : 057.设置filter，使用过滤条件进行更新操作
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60
    uint32_t value = 60;
    uint32_t fieldId = 2;
    GmcFilterStructT FilterStruct;
    FilterStruct.fieldId = fieldId;
    FilterStruct.nodeName = NULL;
    FilterStruct.compOp = GMC_OP_LARGE_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint32_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 Description  : 058.设置filter，使用过滤条件进行删除操作
 Author       : hanyang
*****************************************************************************/
TEST_F(FilterScan_test, HardWare_Offloading_001_DML_086_Filter_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    uint32_t times = 100;
    uint32_t initValue = 0;

    // 插入数据
    testInsertVertexLabel(g_stmt, times, initValue, Vertex_Name_01);

    ret = testGmcPrepareStmtByLabelName(g_stmt, Vertex_Name_01, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // F2 >= 60
    uint32_t value = 60;
    uint32_t fieldId = 2;
    GmcFilterStructT FilterStruct;
    FilterStruct.fieldId = fieldId;
    FilterStruct.nodeName = NULL;
    FilterStruct.compOp = GMC_OP_LARGE_EQUAL;
    FilterStruct.value = &value;
    FilterStruct.valueLen = sizeof(uint32_t);

    ret = GmcSetFilterStructure(g_stmt, &FilterStruct);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    ret = testGmcGetLastError();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

