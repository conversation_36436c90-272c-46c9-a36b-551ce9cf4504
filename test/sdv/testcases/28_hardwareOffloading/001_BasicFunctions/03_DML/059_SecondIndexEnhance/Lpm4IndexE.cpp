/* ****************************************************************************
 Description  : lpm4索引增强
 Node      :
    001 设置vrid最大值为上限4096，vrid为4096时插入数据，创建lpm4索引表；vrid为4097时插入数据。
    002 设置vrfid最大值为上限16384，vrfid为16384时插入数据，创建lpm4索引表；vrid为16385时插入数据。
    003 使用默认的vrid和vrfid最大值，创建lpm4索引表，插入1024*16对（vrid和vrfid）不同的组合。
    004
使用默认的vrid和vrfid最大值，创建lpm4索引表，insert两条记录，dest_ip_addr和mask_len相同，vrid和vrfid的组合分别为1/1和1/3。
    005
使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，通过索引更新将（vrid和vrfid）更新为已有的组合对，不冲突的记录。
    006
使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，通过索引更新将（vrid和vrfid）更新为已有的组合对，冲突的记录。
    007
使用默认的vrid和vrfid最大值，创建lpm4索引表，lpm4索引不定义在主键上，存在多对（vrid和vrfid）不同的组合，通过主键更新将（vrid和vrfid）更新为已有的组合对，冲突的记录。
    008 使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，正常delete一条的记录。
    009 使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，正常replace一条不冲突记录。
    010 使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，正常merge一条不冲突记录。
    011
使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，每对组合下有多个匹配，正常fetch不同（vrid和vrfid）组合对的记录。
    012
使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，通过vrid和vrfid的值，使用GmcGetVertexCount接口获取count值。
    013 创建lpm4表写数据，存在多对（vrid和vrfid）不同的组合，导出表导出数据，删除表，导入表导入数据。
    014 在不同的namespace下创建同名lpm4表，insert数据
 Author       : 黄楚灿 hwx1007418
 Modification :
 Date         : 2021/07/
 node : 一组（vrid和vrfid）lpm索引对需要申请一个page大小（32K），多组lpm索引对需要考虑内存开销
        vrid和vrfid配置项取值范围[0,max)
**************************************************************************** */
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "SecondIndex.h"
#include "../../common/hash_util.h"

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;

const char *g_labelName = "T25";
const char *g_PKName = "T25_PK";
const char *g_labelName_2 = "T26";
const char *g_PKName_2 = "T26_PK";
char ip[64] = "***********";

class Lpm4IndexEnhance_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
        int ret;
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        //恢复配置文件
        RecoverCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void Lpm4IndexEnhance_test::SetUp()
{
    int ret = 0;
    char *schema = NULL;
    char *schema2 = NULL;
    //建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schema_file/Lpm4InLpmindex_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/Lpm4InPrimary_schema.gmjson", &schema2);
    EXPECT_NE((void *)NULL, schema2);

    GmcDropVertexLabel(g_stmt, g_labelName);
    GmcDropVertexLabel(g_stmt, g_labelName_2);

    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(schema);
    free(schema2);
    AW_CHECK_LOG_BEGIN();
}

void Lpm4IndexEnhance_test::TearDown()
{
    AW_CHECK_LOG_END();
    //断开连接
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

class TestSuit2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
    }
    static void TearDownTestCase()
    {
        //恢复配置文件
        RecoverCfg();
    }

public:
    virtual void SetUp()
    {}
    virtual void TearDown()
    {}
};
#define LPM4_VRID_MAX 16
#define LPM4_VRFID_MAX 1024
// 003 使用默认的vrid和vrfid最大值，创建lpm4索引表，插入1024*16对（vrid和vrfid）不同的组合。
TEST_F(Lpm4IndexEnhance_test, HardWare_Offloading_001_DML_059_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len = 24;
    uint64_t locahash_value, hashcluster_value, value;

    int VridMax = LPM4_VRID_MAX;
    int VrfidMax = LPM4_VRFID_MAX;
    if (g_envType != 0) {
        VrfidMax = 32;
    }

    // lpm4索引不在主键上
    f7_value = 1, value = 5;
    mask_len = 24;
    ip_addr = Get_Addr(0, mask_len);
    locahash_value = 3, hashcluster_value = 4;
    // insert vrid=[0,16] vrfid=[0,1024]
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (vrid = 0; vrid < VridMax; vrid++) {
        for (vrfid = 0; vrfid < VrfidMax; vrfid++) {
            WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value,
                &hashcluster_value, NULL, false);
            ret = GmcExecute(g_stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            f7_value++;
            value++;
        }
        printf("insert 1024 record success. vrid : %d \n", vrid);
    }

    // fetch by lpm4 index
    for (vrid = 0; vrid < VridMax; vrid++) {
        for (vrfid = 0; vrfid < VrfidMax; vrfid++) {
            int cnt =
                ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        }
        printf("fetch 1024 record success. vrid : %d \n", vrid);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 004
// 使用默认的vrid和vrfid最大值，创建lpm4索引表，insert两条记录，dest_ip_addr和mask_len相同，vrid和vrfid的组合分别为1/1和1/3。
TEST_F(Lpm4IndexEnhance_test, HardWare_Offloading_001_DML_059_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *label = NULL;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len;
    uint64_t locahash_value, hashcluster_value, value;

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 3, hashcluster_value = 4;
    // insert (vrid,vrfid)=(1,1)
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        f7_value++;
        value++;
    }

    vrid = 1, vrfid = 3;
    // insert (vrid,vrfid)=(1,3)
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        f7_value++;
        value++;
    }

    // fetch by lpm4 index
    mask_len = 24;
    for (int i = 0; i < 60; i++) {
        if (i < 30) {
            vrid = 1, vrfid = 1;
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
        } else {
            vrid = 1, vrfid = 3;
            sprintf(ip, "192.168.%d.0", i - 30);
            ip_addr = Get_Addr((uint32_t)(i - 30), mask_len);
        }
        int cnt = ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
        AW_MACRO_EXPECT_EQ_INT(1, cnt);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005
// 使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，通过索引更新将（vrid和vrfid）更新为已有的组合对，不冲突的记录。
TEST_F(Lpm4IndexEnhance_test, HardWare_Offloading_001_DML_059_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *label = NULL;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len;
    uint64_t locahash_value, hashcluster_value, value;

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 0, hashcluster_value = 100;
    
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        f7_value++;
        value++;
        locahash_value++;
    }

    vrid = 1, vrfid = 3;
    locahash_value = 100, hashcluster_value = 0;
    
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 30; i < 60; i++) {
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        locahash_value++;
        f7_value++;
        value++;
        hashcluster_value++;
    }

    // fetch by lpm4 index
    mask_len = 24;
    for (int i = 0; i < 60; i++) {
        if (i < 30) {
            vrid = 1, vrfid = 1;
        } else {
            vrid = 1, vrfid = 3;
        }
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        int cnt = ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
        AW_MACRO_EXPECT_EQ_INT(1, cnt);
    }

    // index update 将(vrid,vrfid)=(1,1)的记录更新为(1,3) ip=[***********, ************]
    locahash_value = 0;
    vrid = 1, vrfid = 3;
    mask_len = 24;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT8, &locahash_value, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "localhash_key1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        locahash_value++;
    }

    // fetch by lpm4 index
    /*  (vrid,vrfid)=(1,1) ip=[*********** , ************]*/
    mask_len = 24;
    vrid = 1, vrfid = 3;
    for (int i = 30; i < 60; i++) {
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        int cnt = ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
        AW_MACRO_EXPECT_EQ_INT(1, cnt);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006
// 使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，通过索引更新将（vrid和vrfid）更新为已有的组合对，冲突的记录。
TEST_F(Lpm4IndexEnhance_test, HardWare_Offloading_001_DML_059_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *label = NULL;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len;
    uint64_t locahash_value, hashcluster_value, value;

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 0, hashcluster_value = 100;
    
    printf("insert 1 \n");
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        f7_value++;
        value++;
        locahash_value++;
    }

    printf("insert 2 \n");
    vrid = 1, vrfid = 3;
    locahash_value = 100, hashcluster_value = 0;
    // insert (vrid,vrfid)=(1,3) ip=[***********, ************]
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.1", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len) + 1;
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        locahash_value++;
        f7_value++;
        value++;
        hashcluster_value++;
    }

    // fetch by lpm4 index
    /*  (vrid,vrfid)=(1,1) ip=[*********** , ************]
        (vrid,vrfid)=(1,3) ip=[***********,  ************]*/
    printf("fetch 1 \n");
    mask_len = 24;
    for (int i = 0; i < 60; i++) {
        if (i < 30) {
            vrid = 1, vrfid = 1;
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
        } else {
            vrid = 1, vrfid = 3;
            sprintf(ip, "192.168.%d.1", (i - 30));
            ip_addr = Get_Addr((uint32_t)(i - 30), mask_len) + 1;
        }
        int cnt = ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
        AW_MACRO_EXPECT_EQ_INT(1, cnt);
    }

    // index update 将(vrid,vrfid)=(1,3)的记录更新为(1,1) ip=[***********, ************], 数据冲突，更新失败
    printf("update 1 \n");
    locahash_value = 0;
    vrid = 1, vrfid = 3;
    mask_len = 24;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.1", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT8, &locahash_value, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "localhash_key1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        locahash_value++;
    }

    // fetch by lpm4 index
    /*  (vrid,vrfid)=(1,1) ip=[*********** , ************]*/
    printf("fetch 2 \n");
    mask_len = 24;
    vrid = 1, vrfid = 1;
    for (int i = 0; i < 60; i++) {
        if (i < 30) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            int cnt =
                ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            sprintf(ip, "192.168.%d.1", (i - 30));
            ip_addr = Get_Addr((uint32_t)(i - 30), mask_len) + 1;
            int cnt =
                ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", false, false);
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 007
// 使用默认的vrid和vrfid最大值，创建lpm4索引表，lpm4索引不定义在主键上，存在多对（vrid和vrfid）不同的组合，通过主键更新将（vrid和vrfid）更新为已有的组合对，冲突的记录。
TEST_F(Lpm4IndexEnhance_test, HardWare_Offloading_001_DML_059_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *label = NULL;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len;
    uint64_t locahash_value, hashcluster_value, value;

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 0, hashcluster_value = 100;
    
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        f7_value++;
        value++;
        locahash_value++;
    }

    vrid = 1, vrfid = 3;
    locahash_value = 100, hashcluster_value = 0;
    // insert (vrid,vrfid)=(1,3) ip=[***********, ************]
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.1", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len) + 1;
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        locahash_value++;
        f7_value++;
        value++;
        hashcluster_value++;
    }

    // fetch by lpm4 index
    /*  (vrid,vrfid)=(1,1) ip=[*********** , ************]
        (vrid,vrfid)=(1,3) ip=[***********,  ************]*/
    mask_len = 24;
    for (int i = 0; i < 60; i++) {
        if (i < 30) {
            vrid = 1, vrfid = 1;
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
        } else {
            vrid = 1, vrfid = 3;
            sprintf(ip, "192.168.%d.1", (i - 30));
            ip_addr = Get_Addr((uint32_t)(i - 30), mask_len) + 1;
        }
        int cnt = ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
        AW_MACRO_EXPECT_EQ_INT(1, cnt);
    }

    // primary update 将(vrid,vrfid)=(1,1)的记录更新为(1,3) ip=[***********, ************], 数据冲突，更新失败
    f7_value = 1;
    vrid = 1, vrfid = 3;
    mask_len = 24;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.1", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        set_IndexKeyValue_F7(g_stmt, f7_value);
        ret = GmcSetIndexKeyName(g_stmt, "T25_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        f7_value++;
    }

    // fetch by lpm4 index
    /*  (vrid,vrfid)=(1,3) ip=[*********** , ************]
        (vrid,vrfid)=(1,3) ip=[*********** , ************]*/
    mask_len = 24;
    vrid = 1, vrfid = 3;
    for (int i = 0; i < 60; i++) {
        vrid = 1, vrfid = 1;
        if (i < 30) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            int cnt =
                ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        } else {
            sprintf(ip, "192.168.%d.1", (i - 30));
            ip_addr = Get_Addr((uint32_t)i, mask_len) + 1;
            int cnt =
                ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", false, false);
            AW_MACRO_EXPECT_EQ_INT(0, cnt);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008 使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，正常delete一条的记录。
TEST_F(Lpm4IndexEnhance_test, HardWare_Offloading_001_DML_059_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *label = NULL;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len;
    uint64_t locahash_value, hashcluster_value, value;

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 0, hashcluster_value = 100;
    
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        f7_value++;
        value++;
        locahash_value++;
    }

    vrid = 1, vrfid = 3;
    locahash_value = 100, hashcluster_value = 0;
    // insert (vrid,vrfid)=(1,3) ip=[***********, ************]
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.1", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len) + 1;
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        f7_value++;
        value++;
        hashcluster_value++;
    }

    // fetch by lpm4 index
    /*  (vrid,vrfid)=(1,1) ip=[*********** , ************]
        (vrid,vrfid)=(1,3) ip=[***********,  ************]*/
    mask_len = 24;
    for (int i = 0; i < 60; i++) {
        if (i < 30) {
            vrid = 1, vrfid = 1;
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
        } else {
            vrid = 1, vrfid = 3;
            sprintf(ip, "192.168.%d.1", (i - 30));
            ip_addr = Get_Addr((uint32_t)(i - 30), mask_len) + 1;
        }
        int cnt = ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
        AW_MACRO_EXPECT_EQ_INT(1, cnt);
    }

    // primary delete
    /*  (vrid,vrfid)=(1,1) ip=[*********** , ************]*/
    f7_value = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        set_IndexKeyValue_F7(g_stmt, f7_value);
        ret = GmcSetIndexKeyName(g_stmt, "T25_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        f7_value++;
    }

    // hashcluster index delete
    hashcluster_value = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t f3_value = locahash_value;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT8, &f3_value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "hashcluster_key1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // localhash index delete
    locahash_value = 100;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t f2_value = locahash_value;
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT8, &f2_value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "localhash_key1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // fetch by lpm4 index
    mask_len = 24;
    vrid = 1, vrfid = 3;
    for (int i = 0; i < 60; i++) {
        vrid = 1, vrfid = 1;
        if (i < 30) {
            vrid = 1, vrfid = 1;
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
        } else {
            vrid = 1, vrfid = 3;
            sprintf(ip, "192.168.%d.1", (i - 30));
            ip_addr = Get_Addr((uint32_t)(i - 30), mask_len) + 1;
        }
        int cnt = ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", false, false);
        AW_MACRO_EXPECT_EQ_INT(0, cnt);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009 使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，正常replace一条不冲突记录。
TEST_F(Lpm4IndexEnhance_test, HardWare_Offloading_001_DML_059_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *label = NULL;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len;
    uint64_t locahash_value, hashcluster_value, value;

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 0, hashcluster_value = 100;
    
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        f7_value++;
        value++;
        locahash_value++;
    }

    vrid = 1, vrfid = 3;
    locahash_value = 100, hashcluster_value = 0;
    // insert (vrid,vrfid)=(1,3) ip=[***********, ************]
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 29; i++) {
        sprintf(ip, "192.168.%d.1", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len) + 1;
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        locahash_value++;
        f7_value++;
        value++;
        hashcluster_value++;
    }

    // replace
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sprintf(ip, "192.168.%d.1", 29);
    ip_addr = Get_Addr(29, mask_len) + 1;
    WriteRecord_All(
        g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value, NULL, false);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // fetch by lpm4 index
    /*  (vrid,vrfid)=(1,1) ip=[*********** , ************]
        (vrid,vrfid)=(1,3) ip=[***********,  ************]*/
    mask_len = 24;
    for (int i = 0; i < 60; i++) {
        if (i < 30) {
            vrid = 1, vrfid = 1;
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
        } else {
            vrid = 1, vrfid = 3;
            sprintf(ip, "192.168.%d.1", (i - 30));
            ip_addr = Get_Addr((uint32_t)(i - 30), mask_len) + 1;
        }
        int cnt = ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
        AW_MACRO_EXPECT_EQ_INT(1, cnt);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010 使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，正常merge一条不冲突记录。
TEST_F(Lpm4IndexEnhance_test, HardWare_Offloading_001_DML_059_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *label = NULL;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len;
    uint64_t locahash_value, hashcluster_value, value;

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 0, hashcluster_value = 100;
    
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 30; i++) {
        sprintf(ip, "192.168.%d.0", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len);
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        f7_value++;
        value++;
        locahash_value++;
    }

    vrid = 1, vrfid = 3;
    locahash_value = 100, hashcluster_value = 0;
    // insert (vrid,vrfid)=(1,3) ip=[***********, ************]
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 29; i++) {
        sprintf(ip, "192.168.%d.1", i);
        ip_addr = Get_Addr((uint32_t)i, mask_len) + 1;
        WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value,
            NULL, false);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        locahash_value++;
        f7_value++;
        value++;
        hashcluster_value++;
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    set_IndexKeyValue_F7(g_stmt, f7_value);
    ret = GmcSetIndexKeyName(g_stmt, g_PKName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sprintf(ip, "192.168.%d.1", 29);
    ip_addr = Get_Addr(29, mask_len) + 1;
    WriteRecord_All(
        g_stmt, vrid, vrfid, &ip_addr, mask_len, NULL, &value, &locahash_value, &hashcluster_value, NULL, false);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // fetch by lpm4 index
    /*  (vrid,vrfid)=(1,1) ip=[*********** , ************]
        (vrid,vrfid)=(1,3) ip=[***********,  ************]*/
    mask_len = 24;
    for (int i = 0; i < 60; i++) {
        if (i < 30) {
            vrid = 1, vrfid = 1;
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
        } else {
            vrid = 1, vrfid = 3;
            sprintf(ip, "192.168.%d.1", (i - 30));
            ip_addr = Get_Addr((uint32_t)(i - 30), mask_len) + 1;
        }
        int cnt = ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
        AW_MACRO_EXPECT_EQ_INT(1, cnt);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
#define VRFID_MATCH_COUNT 30
// 011
// 使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，每对组合下有多个匹配，正常fetch不同（vrid和vrfid）组合对的记录。
TEST_F(Lpm4IndexEnhance_test, HardWare_Offloading_001_DML_059_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *label = NULL;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len;
    uint64_t locahash_value, hashcluster_value, value;

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 0, hashcluster_value = 100;
    // insert (vrid,vrfid)=[(1,0), (1,29)] ip=[***********, ************]
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        for (int i = 0; i < 30; i++) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value,
                &hashcluster_value, NULL, false);
            ret = GmcExecute(g_stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            f7_value++;
            value++;
        }
    }

    // fetch by lpm4 index
    /*  (vrid,vrfid)=[(1,0), (1,29)] ip=[***********, ************]*/
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        for (int i = 0; i < 30; i++) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            int cnt =
                ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 012
// 使用默认的vrid和vrfid最大值，创建lpm4索引表，存在多对（vrid和vrfid）不同的组合，通过vrid和vrfid的值，使用GmcGetVertexCount接口获取count值。
TEST_F(Lpm4IndexEnhance_test, HardWare_Offloading_001_DML_059_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *label = NULL;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len;
    uint64_t locahash_value, hashcluster_value, value;

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 0, hashcluster_value = 100;
    // insert (vrid,vrfid)=[(1,0), (1,29)] ip=[***********, ************]
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        for (int i = 0; i < 30; i++) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value,
                &hashcluster_value, NULL, false);
            ret = GmcExecute(g_stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            f7_value++;
            value++;
        }
    }

    // fetch by lpm4 index
    /*  (vrid,vrfid)=[(1,0), (1,29)] ip=[***********, ************]*/
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        for (int i = 0; i < 30; i++) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            int cnt =
                ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        }
    }

    // get vertex count
    mask_len = 24;
    vrid = 1;
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        set_IndexKeyValue_lpm4(g_stmt, vrid, vrfid, ip_addr, mask_len);
        uint64_t count = 0;
        ret = GmcGetVertexCount(g_stmt, g_labelName, "ip4forward_lpm", &count);  //
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(30, count);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
// 013 创建lpm4表写数据，存在多对（vrid和vrfid）不同的组合，导出表导出数据，删除表，导入表导入数据。
TEST_F(Lpm4IndexEnhance_test, DISABLED_TODEL_HardWare_Offloading_001_DML_059_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *label = NULL;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len;
    uint64_t locahash_value, hashcluster_value, value;

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 0, hashcluster_value = 100;
    // insert (vrid,vrfid)=[(1,0), (1,29)] ip=[***********, ************]
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        for (int i = 0; i < 30; i++) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value,
                &hashcluster_value, NULL, false);
            ret = GmcExecute(g_stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            f7_value++;
            value++;
        }
    }

    // fetch by lpm4 index
    /*  (vrid,vrfid)=[(1,0), (1,29)] ip=[***********, ************]*/
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        for (int i = 0; i < 30; i++) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            int cnt =
                ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        }
    }

    char const *g_filePath = "./vertexdata/";
    char const *g_filePath1 = "./vertexjson/";
    char const *g_vertexPath = "./vertexjson/T25.gmjson";
    char const *g_dataPath = "./vertexdata/T25.gmdata";
    // export 导出schema数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vschema -t %s -f %s -s %s -ns %s",
        g_toolPath, g_labelName,
        g_filePath1, g_connServer, g_testNameSpace);
    
    ret = executeCommand(g_command, "Command type: export_vschema, export file successfully.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // export 导出data数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmexport -c vdata -t %s -f %s -s %s -ns %s",
        g_toolPath, g_labelName,
        g_filePath, g_connServer, g_testNameSpace);
    
    ret = executeCommand(g_command, "Command type: export_vdata, export file successfully.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除vertexlabel
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //导入schema
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s -ns %s",
        g_toolPath, g_vertexPath,
        g_connServer, g_testNameSpace);
    
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/03_DML/059_SecondIndexEnhance/vertexjson/T25.gmjson\" successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //导入data
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s -ns %s", g_toolPath, g_dataPath,
        g_connServer, g_testNameSpace);
    
    // system(g_command);
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 900, successNum: 900", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/03_DML/059_SecondIndexEnhance/vertexdata/T25.gmdata\" successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // fetch by lpm4 index
    /*  (vrid,vrfid)=[(1,0), (1,29)] ip=[***********, ************]*/
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        for (int i = 0; i < 30; i++) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            int cnt =
                ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 014 在不同的namespace下创建同名lpm4表，insert数据
TEST_F(Lpm4IndexEnhance_test, HardWare_Offloading_001_DML_059_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *label = NULL;
    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len;
    uint64_t locahash_value, hashcluster_value, value;

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 0, hashcluster_value = 100;
    // insert (vrid,vrfid)=[(1,0), (1,29)] ip=[***********, ************]
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        for (int i = 0; i < 30; i++) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value,
                &hashcluster_value, NULL, false);
            ret = GmcExecute(g_stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            f7_value++;
            value++;
        }
    }

    // fetch by lpm4 index
    /*  (vrid,vrfid)=[(1,0), (1,29)] ip=[***********, ************]*/
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        for (int i = 0; i < 30; i++) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            int cnt =
                ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        }
    }

    char const *nameSpace = "abc";
    char const *nameSpace_userName = "myname";
    // create and use namespace
    ret = GmcCreateNamespace(g_stmt, nameSpace, nameSpace_userName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // difference namespace create same name vertex and insert record
    char *schema = NULL;
    char *schema2 = NULL;

    readJanssonFile("schema_file/Lpm4InLpmindex_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/Lpm4InPrimary_schema.gmjson", &schema2);
    EXPECT_NE((void *)NULL, schema2);

    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(schema);
    free(schema2);

    f7_value = 1, value = 5;
    mask_len = 24;
    vrid = 1, vrfid = 1;
    locahash_value = 0, hashcluster_value = 100;
    // insert (vrid,vrfid)=[(1,0), (1,29)] ip=[***********, ************]
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        for (int i = 0; i < 30; i++) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            WriteRecord_All(g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value,
                &hashcluster_value, NULL, false);
            ret = GmcExecute(g_stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            f7_value++;
            value++;
        }
    }

    // fetch by lpm4 index
    /*  (vrid,vrfid)=[(1,0), (1,29)] ip=[***********, ************]*/
    for (vrfid = 0; vrfid < VRFID_MATCH_COUNT; vrfid++) {
        for (int i = 0; i < 30; i++) {
            sprintf(ip, "192.168.%d.0", i);
            ip_addr = Get_Addr((uint32_t)i, mask_len);
            int cnt =
                ReadAndCheckLpm(g_stmt, vrid, vrfid, &ip_addr, mask_len, g_labelName, "ip4forward_lpm", true, false);
            AW_MACRO_EXPECT_EQ_INT(1, cnt);
        }
    }

    // drop namespace and vertex
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropNamespace(g_stmt, nameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // auto turn in publice namespace
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_labelName_2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 001 设置vrid最大值为上限4096，vrid为4096时插入数据，创建lpm4索引表；vrid为4097时插入数据。
TEST_F(TestSuit2, HardWare_Offloading_001_DML_059_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"lpm4VrIdMax=4096\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *schema = NULL;
    char *schema2 = NULL;
    //建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schema_file/Lpm4InLpmindex_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/Lpm4InPrimary_schema.gmjson", &schema2);
    EXPECT_NE((void *)NULL, schema2);

    GmcDropVertexLabel(g_stmt, g_labelName);
    GmcDropVertexLabel(g_stmt, g_labelName_2);

    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(schema);
    free(schema2);

    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len = 24;
    uint64_t locahash_value, hashcluster_value, value;

    // lpm4索引不在主键上
    f7_value = 1, value = 5;
    ip_addr = Get_Addr(0, 32), mask_len = 24;
    locahash_value = 3, hashcluster_value = 4;
    // insert
    vrid = 4095, vrfid = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    WriteRecord_All(
        g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value, NULL, false);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    f7_value = 2;
    vrid = 4096, vrfid = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    WriteRecord_All(
        g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value, NULL, false);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = testGmcGetLastError("Art delete unsucc. removePara isGc: 0, isErase: 1", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDropVertexLabel(g_stmt, g_labelName);
    GmcDropVertexLabel(g_stmt, g_labelName_2);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 002 设置vrfid最大值为上限16384，vrfid为16384时插入数据，创建lpm4索引表；vrfid为16385时插入数据。
// vrfid和vrid 申请内存大小 vridmax*vrfidmax*32K
TEST_F(TestSuit2, HardWare_Offloading_001_DML_059_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_SYNTAX_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = 0;

    system("sh $TEST_HOME/tools/stop.sh");  //修改配置，先停服务
    system("ipcs");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"lpm4VrfIdMax=16384\"");  //修改时间阈值
    system("sh $TEST_HOME/tools/start.sh -f ");
    system("ipcs");

    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *schema = NULL;
    char *schema2 = NULL;
    //建立连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schema_file/Lpm4InLpmindex_schema.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    readJanssonFile("schema_file/Lpm4InPrimary_schema.gmjson", &schema2);
    EXPECT_NE((void *)NULL, schema2);

    GmcDropVertexLabel(g_stmt, g_labelName);
    GmcDropVertexLabel(g_stmt, g_labelName_2);

    ret = GmcCreateVertexLabel(g_stmt, schema, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, schema2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(schema);
    free(schema2);

    
    uint64_t f7_value;
    uint32_t vrid, vrfid, ip_addr;
    uint8_t mask_len = 24;
    uint64_t locahash_value, hashcluster_value, value;

    // lpm4索引不在主键上
    f7_value = 1, value = 5;
    ip_addr = Get_Addr(0, 32), mask_len = 24;
    locahash_value = 3, hashcluster_value = 4;
    // insert
    vrid = 0, vrfid = 16383;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    WriteRecord_All(
        g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value, NULL, false);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert
    f7_value = 2;
    vrid = 0, vrfid = 16384;
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    WriteRecord_All(
        g_stmt, vrid, vrfid, &ip_addr, mask_len, &f7_value, &value, &locahash_value, &hashcluster_value, NULL, false);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SYNTAX_ERROR, ret);
    ret = testGmcGetLastError("Art delete unsucc. removePara isGc: 0, isErase: 1", false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDropVertexLabel(g_stmt, g_labelName);
    GmcDropVertexLabel(g_stmt, g_labelName_2);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    system("sh $TEST_HOME/tools/stop.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"recover\"");
    AW_FUN_Log(LOG_STEP, "test end.");
}
