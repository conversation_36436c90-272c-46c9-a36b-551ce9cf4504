extern "C" {
}
#include <stdarg.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "scan_fetch_rows_test.h"
#include "t_datacom_lite.h"
#include "../../common/hash_util.h"

#define LABELNAME_MAX_LENGTH 128

GmcConnT *g_conn = NULL;  // conn
GmcStmtT *g_stmt = NULL;  // stmt

GmcConnT *conn = NULL;
GmcStmtT *stmt = NULL;

GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

char *schema_json = NULL;
char g_labelName[LABELNAME_MAX_LENGTH] = "ip4forward00000";
char g_labelNameTree[LABELNAME_MAX_LENGTH] = "Tree_Vector";

char g_configJson[128] = "{\"max_record_num\" : 999999}";
using namespace std;

struct timeval timeBegin, timeEnd;
uint64_t sec;
uint64_t usec;
uint64_t allTime;  // 单位是微妙
float ops;
uint64_t costTime;

class scan_fetch_rows_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();

        int res = testEnvInit();
        EXPECT_EQ(GMERR_OK, res);
    }

    static void TearDownTestCase(){
        //恢复配置文件
        RecoverCfg();
    };

public:
    virtual void SetUp();
    virtual void TearDown();
};

void scan_fetch_rows_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    // 创建同步连接
    conn = NULL;
    stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    ASSERT_EQ(GMERR_OK, ret);

    // 创建异步连接
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    // 创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);

    (void)GmcDropVertexLabel(stmt, g_labelName);
    (void)GmcDropVertexLabel(stmt, g_labelNameTree);

    AW_CHECK_LOG_BEGIN();
}

void scan_fetch_rows_test::TearDown()
{
    printf("\n======================TEST:END========================\n");
    AW_CHECK_LOG_END();

    // 断开同步连接
    int ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 断开异步连接
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    int res = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, res);
}


#define IP4FORWARD_SCAN()                                                                                        \
    do {                                                                                                         \
        ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);                                  \
        ASSERT_EQ(GMERR_OK, ret);                                                                                \
        ret = GmcExecute(stmt);                                                                                  \
        EXPECT_EQ(GMERR_OK, ret);                                                                                \
        bool isFinish = false;                                                                                   \
        unsigned int cnt = 0;                                                                                    \
        while (!isFinish) {                                                                                      \
            ret = GmcFetch(stmt, &isFinish);                                                                     \
            EXPECT_EQ(GMERR_OK, ret);                                                                            \
            if (isFinish == true) {                                                                              \
                break;                                                                                           \
            }                                                                                                    \
            uint32_t rd_primary_label = 0;                                                                       \
            uint32_t rd_vrf_index = 0;                                                                           \
            uint32_t rd_dest_ip_addr = 0;                                                                        \
            uint8_t rd_mask_len = 0;                                                                             \
            uint32_t rd_attribute_id = 0;                                                                        \
            uint32_t rd_app_version = 0;                                                                         \
            uint32_t rd_nhp_group_id = 0;                                                                        \
            bool isNull;                                                                                         \
            ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_primary_label, sizeof(uint32_t), &isNull);       \
            ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, sizeof(uint32_t), &isNull);       \
            ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull); \
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);          \
            ret = GmcGetVertexPropertyByName(stmt, "app_version", &rd_app_version, sizeof(uint32_t), &isNull);   \
            ret = GmcGetVertexPropertyByName(stmt, "attribute_id", &rd_attribute_id, sizeof(uint32_t), &isNull); \
            ret = GmcGetVertexPropertyByName(stmt, "nhp_group_id", &rd_nhp_group_id, sizeof(uint32_t), &isNull); \
            cnt++;                                                                                               \
        }                                                                                                        \
        GmcResetVertex(stmt, false);                                                                             \
    } while (0);

#define IP4FORWARD_SCAN_GETCH()                                                                                  \
    do {                                                                                                         \
        bool isFinish = false;                                                                                   \
        cnt = 0;                                                                                                 \
        while (!isFinish) {                                                                                      \
            ret = GmcFetch(stmt, &isFinish);                                                                     \
            EXPECT_EQ(GMERR_OK, ret);                                                                            \
            if (isFinish == true) {                                                                              \
                break;                                                                                           \
            }                                                                                                    \
            uint32_t rd_primary_label = 0;                                                                       \
            uint32_t rd_vrf_index = 0;                                                                           \
            uint32_t rd_dest_ip_addr = 0;                                                                        \
            uint8_t rd_mask_len = 0;                                                                             \
            uint32_t rd_attribute_id = 0;                                                                        \
            uint32_t rd_app_version = 0;                                                                         \
            uint32_t rd_nhp_group_id = 0;                                                                        \
            bool isNull;                                                                                         \
            ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_primary_label, sizeof(uint32_t), &isNull);       \
            ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, sizeof(uint32_t), &isNull);       \
            ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull); \
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);          \
            ret = GmcGetVertexPropertyByName(stmt, "app_version", &rd_app_version, sizeof(uint32_t), &isNull);   \
            ret = GmcGetVertexPropertyByName(stmt, "attribute_id", &rd_attribute_id, sizeof(uint32_t), &isNull); \
            ret = GmcGetVertexPropertyByName(stmt, "nhp_group_id", &rd_nhp_group_id, sizeof(uint32_t), &isNull); \
            cnt++;                                                                                               \
        }                                                                                                        \
                                                                                 \
        GmcResetVertex(stmt, false);                                                                             \
    } while (0);

// 001. 创建vertex表ip4forward，同步insert 3000条数据，fetch_rows设置为2048，全表扫描；fetch_rows设置为1，全表扫描；
TEST_F(scan_fetch_rows_test, HardWare_Offloading_001_scan_fetch_rows_test_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    uint64_t i = 0;
    uint64_t j = 0;

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    unsigned int cnt = 0;

    //设置扫描缓存条数  设置范围为[1, 2048]
    int prerows = 2048;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 3000);

    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (i = 0; i < 200; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 2800);

    // 清空数据
    
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 2048;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 0);

    
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002. 创建vertex表ip4forward，同步insert
// 3000条数据，fetch_rows设置为2048，唯一localhash索引扫描；fetch_rows设置为1，唯一localhash索引扫描；
TEST_F(scan_fetch_rows_test, HardWare_Offloading_001_scan_fetch_rows_test_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    uint64_t i = 0;
    uint64_t j = 0;

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 2048;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    // 唯一localhash索引
    uint32_t wr_uint32 = 1000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4_localhash_app_version");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 1);

    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (i = 0; i < 200; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 10;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    // 唯一localhash索引
    wr_uint32 = 100;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4_localhash_app_version");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 0);

    // 清空数据
    
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 1000;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 唯一localhash索引
    wr_uint32 = 2000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4_localhash_app_version");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 0);

    
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003. 创建vertex表ip4forward，同步insert
// 3000条数据，fetch_rows设置为2048，非唯一localhash索引扫描；fetch_rows设置为1，非唯一localhash索引扫描；
TEST_F(scan_fetch_rows_test, HardWare_Offloading_001_scan_fetch_rows_test_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    uint64_t i = 0;
    uint64_t j = 0;

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_set_obj(stmt, i, 30);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 1024;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一localhash索引
    uint32_t wr_uint32 = 10;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t wr_vrid = 0;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_vrid, sizeof(wr_vrid));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 100);

    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (i = 0; i < 300; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 10;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    // 唯一localhash索引
    wr_uint32 = 20;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    wr_vrid = 0;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_vrid, sizeof(wr_vrid));
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 90);

    // 清空数据
    
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 1000;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 唯一localhash索引
    wr_uint32 = 2000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    wr_vrid = 0;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_vrid, sizeof(wr_vrid));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 0);

    
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004. 创建vertex表ip4forward，同步insert
// 3000条数据，fetch_rows设置为2048，唯一hashcluster索引扫描；fetch_rows设置为1，唯一hashcluster索引扫描；
TEST_F(scan_fetch_rows_test, HardWare_Offloading_001_scan_fetch_rows_test_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    uint64_t i = 0;
    uint64_t j = 0;
    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 2048;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    // 唯一hashcluster索引
    uint32_t wr_uint32 = 1000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4_hashcluster_app_version");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 1);

    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (i = 0; i < 200; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 10;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    // 唯一hashcluster索引
    wr_uint32 = 100;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4_hashcluster_app_version");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 0);

    // 清空数据
    
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 1000;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 唯一hashcluster索引
    wr_uint32 = 2000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4_hashcluster_app_version");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 0);

    
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005. 创建vertex表ip4forward，同步insert
// 3000条数据，fetch_rows设置为2048，非唯一hashcluster索引扫描；fetch_rows设置为1，非唯一hashcluster索引扫描；
TEST_F(scan_fetch_rows_test, HardWare_Offloading_001_scan_fetch_rows_test_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    uint64_t i = 0;
    uint64_t j = 0;

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_set_obj(stmt, i, 10);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 888;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hashcluster索引
    uint32_t wr_uint32 = 9;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t wr_vrid = 0;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_vrid, sizeof(wr_vrid));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t wr_vfindex = 0;
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &wr_vfindex, sizeof(wr_vfindex));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4_vrfid_hashcluster");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 300);

    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (i = 0; i < 300; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 105;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    // 非唯一hashcluster索引
    wr_uint32 = 8;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    wr_vrid = 0;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_vrid, sizeof(wr_vrid));
    EXPECT_EQ(GMERR_OK, ret);
    wr_vfindex = 0;
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &wr_vfindex, sizeof(wr_vfindex));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4_vrfid_hashcluster");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 270);

    // 清空数据
    
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 1000;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    // 非唯一hashcluster索引
    wr_uint32 = 2000;
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    wr_vrid = 0;
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &wr_vrid, sizeof(wr_vrid));
    EXPECT_EQ(GMERR_OK, ret);
    wr_vfindex = 0;
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_UINT32, &wr_vfindex, sizeof(wr_vfindex));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "ip4_vrfid_hashcluster");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 0);

    
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006. 创建vertex表ip4forward，同步insert
// 3000条数据，fetch_rows设置为2048，local索引扫描；fetch_rows设置为1，local索引扫描；
TEST_F(scan_fetch_rows_test, HardWare_Offloading_001_scan_fetch_rows_test_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    uint64_t i = 0;
    uint64_t j = 0;

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 300;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    // local 区间[0, 500]
    uint32_t l_val_del = 0;
    uint32_t r_val_del = 800;
    uint32_t l_val_del_1 = 50;
    uint32_t r_val_del_1 = 1000;
    unsigned int arrLen = 2;

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[0].value = &l_val_del;
    leftKeyProps_del[0].size = sizeof(l_val_del);
    leftKeyProps_del[1].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[1].value = &l_val_del_1;
    leftKeyProps_del[1].size = sizeof(l_val_del_1);

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[0].value = &r_val_del;
    rightKeyProps_del[0].size = sizeof(r_val_del);
    rightKeyProps_del[1].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[1].value = &r_val_del_1;
    rightKeyProps_del[1].size = sizeof(r_val_del_1);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps_del[0];
    items[0].rValue = &rightKeyProps_del[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    items[1].lValue = &leftKeyProps_del[1];
    items[1].rValue = &rightKeyProps_del[1];
    items[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].order = GMC_ORDER_ASC;

    // 设置区间
    ret = GmcSetKeyRange(stmt, items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "ip4_local");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 801);

    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (i = 0; i < 300; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 1000;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置区间
    ret = GmcSetKeyRange(stmt, items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "ip4_local");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 501);

    // 清空数据
    
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 200;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置区间
    ret = GmcSetKeyRange(stmt, items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "ip4_local");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 0);

    
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007. 创建vertex表ip4forward，同步insert
// 3000条数据，fetch_rows设置为2048，local索引扫描；fetch_rows设置为1，local索引扫描；
TEST_F(scan_fetch_rows_test, HardWare_Offloading_001_scan_fetch_rows_test_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    uint64_t i = 0;
    uint64_t j = 0;

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_set_obj(stmt, i, 100);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 300;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    // local 区间[0, 500]
    uint32_t l_val_del = 0;
    uint32_t r_val_del = 50;
    uint32_t l_val_del_1 = 50;
    uint32_t r_val_del_1 = 1000;
    unsigned int arrLen = 2;

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[0].value = &l_val_del;
    leftKeyProps_del[0].size = sizeof(l_val_del);
    leftKeyProps_del[1].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[1].value = &l_val_del_1;
    leftKeyProps_del[1].size = sizeof(l_val_del_1);

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[0].value = &r_val_del;
    rightKeyProps_del[0].size = sizeof(r_val_del);
    rightKeyProps_del[1].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[1].value = &r_val_del_1;
    rightKeyProps_del[1].size = sizeof(r_val_del_1);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps_del[0];
    items[0].rValue = &rightKeyProps_del[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    items[1].lValue = &leftKeyProps_del[1];
    items[1].rValue = &rightKeyProps_del[1];
    items[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].order = GMC_ORDER_ASC;

    // 设置区间
    ret = GmcSetKeyRange(stmt, items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "ip4_local");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 1530);  // 51 * 30

    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (i = 0; i < 300; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 1000;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置区间
    ret = GmcSetKeyRange(stmt, items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "ip4_local");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 27 * 51);

    // 清空数据
    
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 200;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    // 设置区间
    ret = GmcSetKeyRange(stmt, items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(stmt, "ip4_local");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 0);

    
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008. 创建vertex表ip4forward，同步insert 3000条数据，fetch_rows设置为2048，主键读；fetch_rows设置为1，主键读
TEST_F(scan_fetch_rows_test, HardWare_Offloading_001_scan_fetch_rows_test_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    uint64_t i = 0;
    uint64_t j = 0;

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_set_obj(stmt, i, 100);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 300;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            uint32_t rd_primary_label = 0;
            uint32_t rd_vrf_index = 0;
            uint32_t rd_dest_ip_addr = 0;
            uint8_t rd_mask_len = 0;
            uint32_t rd_attribute_id = 0;
            uint32_t rd_app_version = 0;
            uint32_t rd_nhp_group_id = 0;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_primary_label, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "app_version", &rd_app_version, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "attribute_id", &rd_attribute_id, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "nhp_group_id", &rd_nhp_group_id, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }

        EXPECT_EQ(cnt, 1);
    }

    GmcResetVertex(stmt, false);

    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (i = 0; i < 800; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    prerows = 1000;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            uint32_t rd_primary_label = 0;
            uint32_t rd_vrf_index = 0;
            uint32_t rd_dest_ip_addr = 0;
            uint8_t rd_mask_len = 0;
            uint32_t rd_attribute_id = 0;
            uint32_t rd_app_version = 0;
            uint32_t rd_nhp_group_id = 0;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_primary_label, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "app_version", &rd_app_version, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "attribute_id", &rd_attribute_id, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "nhp_group_id", &rd_nhp_group_id, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }

        if (i < 800) {
            EXPECT_EQ(cnt, 0);
        } else {
            EXPECT_EQ(cnt, 1);
        }
    }

    // 清空数据
    
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 200;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }

            cnt++;
        }
        EXPECT_EQ(cnt, 0);
    }

    
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009. 创建vertex表ip4forward，同步insert 3000条数据，fetch_rows设置为2048，cond扫描；fetch_rows设置为1，cond扫描；
TEST_F(scan_fetch_rows_test, HardWare_Offloading_001_scan_fetch_rows_test_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    uint64_t i = 0;
    uint64_t j = 0;

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_set_obj(stmt, i, 30);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 150;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    const char *cond1 = (const char *)"ip4forward00000.nhp_group_id<20 and ip4forward00000.nhp_group_id>=0";
    ret = GmcSetFilter(stmt, cond1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 2000);

    prerows = 2048;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    const char *cond2 = (const char *)"ip4forward00000.app_source_id<3000 and ip4forward00000.app_source_id>=0";
    ret = GmcSetFilter(stmt, cond2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 3000);

    prerows = 1;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    const char *cond3 = (const char *)"ip4forward00000.nhp_group_id<1 and ip4forward00000.nhp_group_id>=0";
    ret = GmcSetFilter(stmt, cond3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 100);

    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (i = 0; i < 300; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    prerows = 15;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    const char *cond4 = (const char *)"ip4forward00000.nhp_group_id<1 and ip4forward00000.nhp_group_id>=0";
    ret = GmcSetFilter(stmt, cond4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 90);

    // 清空数据
    
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    prerows = 105;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    const char *cond5 = (const char *)"ip4forward00000.nhp_group_id<1 and ip4forward00000.nhp_group_id>=0";
    ret = GmcSetFilter(stmt, cond5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 0);

    
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010. 创建vertex表ip4forward，同步insert 3000条数据，fetch_rows设置为2048，lpm扫描；fetch_rows设置为1，lpm扫描
TEST_F(scan_fetch_rows_test, HardWare_Offloading_001_scan_fetch_rows_test_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    uint64_t i = 0;
    uint64_t j = 0;

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_set_obj(stmt, i, 100);
        EXPECT_EQ(ret, GMERR_OK);
        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 300;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_lpm");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            uint32_t rd_primary_label = 0;
            uint32_t rd_vrf_index = 0;
            uint32_t rd_dest_ip_addr = 0;
            uint8_t rd_mask_len = 0;
            uint32_t rd_attribute_id = 0;
            uint32_t rd_app_version = 0;
            uint32_t rd_nhp_group_id = 0;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_primary_label, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "app_version", &rd_app_version, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "attribute_id", &rd_attribute_id, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "nhp_group_id", &rd_nhp_group_id, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }
        if (cnt != 1) {
            break;
        }
        EXPECT_EQ(cnt, 1);
    }

    GmcResetVertex(stmt, false);

    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);

    for (i = 0; i < 800; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // insert data maskLen=0
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = ip4forward00000_set_obj(stmt, 0, 15365, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    prerows = 1000;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_lpm");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            uint32_t rd_primary_label = 0;
            uint32_t rd_vrf_index = 0;
            uint32_t rd_dest_ip_addr = 0;
            uint8_t rd_mask_len = 0;
            uint32_t rd_attribute_id = 0;
            uint32_t rd_app_version = 0;
            uint32_t rd_nhp_group_id = 0;
            bool isNull;
            ret = GmcGetVertexPropertyByName(stmt, "vr_id", &rd_primary_label, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "vrf_index", &rd_vrf_index, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "app_version", &rd_app_version, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "attribute_id", &rd_attribute_id, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcGetVertexPropertyByName(stmt, "nhp_group_id", &rd_nhp_group_id, sizeof(uint32_t), &isNull);
            EXPECT_EQ(GMERR_OK, ret);
            cnt++;
        }

        EXPECT_EQ(cnt, 1);
    }

    // 清空数据
    
    ret = GmcTruncateVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 200;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_lpm");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }

            cnt++;
        }
        EXPECT_EQ(cnt, 0);
    }

    // insert data maskLen=0
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_REPLACE);
    ASSERT_EQ(GMERR_OK, ret);
    ret = ip4forward00000_set_obj(stmt, 0, 15365, 0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 500;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_pri_key_set(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(stmt, "ip4_lpm");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }

            cnt++;
        }
        EXPECT_EQ(cnt, 1);
    }

    
    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}


const char *g_nameSpace_userName = (const char *)"abc";
// 011 Name_space 1,创建vertex表ip4forward，Name_space 2 ,创建vertex表ip4forward；
// 分别对两个表做，insert 1000条数据，fetch_rows设置为2048，全表扫描；fetch_rows设置为1，全表扫描
TEST_F(scan_fetch_rows_test, DISABLED_TODEL_HardWare_Offloading_001_scan_fetch_rows_test_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    const char *name1 = (const char *)"user011_a";
    const char *name2 = (const char *)"user011_b";

    GmcDropNamespace(stmt, name1);
    GmcDropNamespace(stmt, name2);
    int ret = GmcCreateNamespace(stmt, name1, g_nameSpace_userName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateNamespace(stmt, name2, g_nameSpace_userName);
    EXPECT_EQ(GMERR_OK, ret);

    // namespace_1
    ret = GmcUseNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);

    // namespace_2
    ret = GmcUseNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);

    uint64_t count;

    // namespace_1
    ret = GmcUseNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    uint64_t i = 0;
    for (i = 0; i < 2000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 2048;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 2000);

    // namespace_2
    ret = GmcUseNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);

    // open vertexLabel
    vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_INSERT);
    ASSERT_EQ(GMERR_OK, ret);

    
    i = 0;
    for (i = 0; i < 3000; i++) {
        ret = ip4forward00000_set_obj(stmt, i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(stmt);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_DELETE);
    ASSERT_EQ(GMERR_OK, ret);
    for (i = 0; i < 1000; i++) {
        uint32_t uint_32 = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &uint_32, sizeof(uint_32));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue app_source_id.");
        uint32_t vrid = 0;

        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &vrid, sizeof(vrid));
        CHECK_OK_RET(ret, "GmcSetIndexKeyValue vr_id.");

        ret = GmcSetIndexKeyName(stmt, "ip4_localhash");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcGetVertexCount(stmt, g_labelName, NULL, &count);
    ASSERT_EQ(GMERR_OK, ret);
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 1024;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    IP4FORWARD_SCAN_GETCH();
    EXPECT_EQ(cnt, 2000);

    // name1
    ret = GmcUseNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    // name2
    ret = GmcUseNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_labelName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDropNamespace(stmt, name1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropNamespace(stmt, name2);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

const char *test_delta_config_json = R"(
    {
        "max_record_num":10000,
        "delta_store_name":"dsdml1",
        "writers":"abc"     
    })";
const char *deltaStoreJsonNormalSingle = R"(
    {  
    "delta_stores":                        
        [{                                       
            "name": "dsdml1",    
            "init_mem_size": 10485760,         
            "max_mem_size": 20971520,          
            "extend_mem_size": 6291456,        
            "page_size": 16384                 
        }]                                       
})";

//多线程并发

// 13 多线程并发：2个线程进行DDL(建表/删表/truntcate表)/DML操作，2个线程不断的扫描查询；
#define THR_NUM 2
GmcConnT *g_conn_tht[THR_NUM * 2];
GmcStmtT *g_stmt_tht[THR_NUM * 2];
void *g_vertexLabel_tht[THR_NUM * 2];

void *thread_dml_operation(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);
    int ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    // open vertexLabel
    void *vertexLabel = NULL;
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t i = 0;
    for (i = 0; i < 1000; i++) {
        ret = ip4forward00000_set_obj(g_stmt_tht[conn_id], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[conn_id]);
    }

    sleep(2);

    uint64_t count;
    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    

    for (i = 0; i < 1000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[conn_id], i);
        EXPECT_EQ(ret, GMERR_OK);

        uint32_t value_u32 = i + 1;

        ret = GmcSetVertexProperty(
            g_stmt_tht[conn_id], "primary_label", GMC_DATATYPE_UINT32, &value_u32, sizeof(value_u32));
        CHECK_OK_RET(ret, "GmcSetVertexProperty primary_label.");

        ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_tht[conn_id]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    

    for (i = 1000; i < 2000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_set_obj(g_stmt_tht[conn_id], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[conn_id]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    

    for (i = 2000; i < 3000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_set_obj(g_stmt_tht[conn_id], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcExecute(g_stmt_tht[conn_id]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    sleep(1);
    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    

    for (i = 1000; i < 3000; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);

        ret = ip4forward00000_pri_key_set(g_stmt_tht[conn_id], i);
        EXPECT_EQ(ret, GMERR_OK);

        ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "ip4_key");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt_tht[conn_id]);
        EXPECT_EQ(ret, GMERR_OK);
    }

    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    

    ret = GmcTruncateVertexLabel(g_stmt_tht[conn_id], g_labelName);

    ret = GmcGetVertexCount(g_stmt_tht[conn_id], g_labelName, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    
    return ((void *)0);
}

void *thread_dml_query(void *args)
{
    int conn_id = *((int *)args);
    int res = testGmcConnect(&g_conn_tht[conn_id], &g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, res);
    int ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);


    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 2048 / conn_id;
    
    ret = testGmcPrepareStmtByLabelName(g_stmt_tht[conn_id], g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(g_stmt_tht[conn_id], GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);

    bool isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        uint32_t rd_primary_label = 0;
        uint32_t rd_vrf_index = 0;
        uint32_t rd_dest_ip_addr = 0;
        uint8_t rd_mask_len = 0;
        uint32_t rd_attribute_id = 0;
        uint32_t rd_app_version = 0;
        uint32_t rd_nhp_group_id = 0;
        bool isNull;
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "vr_id", &rd_primary_label, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "vrf_index", &rd_vrf_index, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "app_version", &rd_app_version, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "attribute_id", &rd_attribute_id, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "nhp_group_id", &rd_nhp_group_id, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }


    // 非唯一localhash索引
    uint32_t wr_uint32 = 10;
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t wr_vrid = 0;
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &wr_vrid, sizeof(wr_vrid));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "ip4_localhash");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        uint32_t rd_primary_label = 0;
        uint32_t rd_vrf_index = 0;
        uint32_t rd_dest_ip_addr = 0;
        uint8_t rd_mask_len = 0;
        uint32_t rd_attribute_id = 0;
        uint32_t rd_app_version = 0;
        uint32_t rd_nhp_group_id = 0;
        bool isNull;
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "vr_id", &rd_primary_label, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "vrf_index", &rd_vrf_index, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "app_version", &rd_app_version, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "attribute_id", &rd_attribute_id, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "nhp_group_id", &rd_nhp_group_id, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }

    // 非唯一hashcluster索引
    wr_uint32 = 8;
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 0, GMC_DATATYPE_UINT32, &wr_uint32, sizeof(wr_uint32));
    EXPECT_EQ(GMERR_OK, ret);
    wr_vrid = 0;
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 1, GMC_DATATYPE_UINT32, &wr_vrid, sizeof(wr_vrid));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t wr_vfindex = 0;
    ret = GmcSetIndexKeyValue(g_stmt_tht[conn_id], 2, GMC_DATATYPE_UINT32, &wr_vfindex, sizeof(wr_vfindex));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "ip4_vrfid_hashcluster");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        uint32_t rd_primary_label = 0;
        uint32_t rd_vrf_index = 0;
        uint32_t rd_dest_ip_addr = 0;
        uint8_t rd_mask_len = 0;
        uint32_t rd_attribute_id = 0;
        uint32_t rd_app_version = 0;
        uint32_t rd_nhp_group_id = 0;
        bool isNull;
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "vr_id", &rd_primary_label, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "vrf_index", &rd_vrf_index, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "app_version", &rd_app_version, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "attribute_id", &rd_attribute_id, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "nhp_group_id", &rd_nhp_group_id, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    

    //设置扫描缓存条数  设置范围为[1, 2048]
    prerows = 300;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);

    // local 区间[0, 500]
    uint32_t l_val_del = 0;
    uint32_t r_val_del = 800;
    uint32_t l_val_del_1 = 50;
    uint32_t r_val_del_1 = 1000;
    unsigned int arrLen = 2;

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    leftKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[0].value = &l_val_del;
    leftKeyProps_del[0].size = sizeof(l_val_del);
    leftKeyProps_del[1].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[1].value = &l_val_del_1;
    leftKeyProps_del[1].size = sizeof(l_val_del_1);

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    rightKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[0].value = &r_val_del;
    rightKeyProps_del[0].size = sizeof(r_val_del);
    rightKeyProps_del[1].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[1].value = &r_val_del_1;
    rightKeyProps_del[1].size = sizeof(r_val_del_1);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps_del[0];
    items[0].rValue = &rightKeyProps_del[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;
    items[1].lValue = &leftKeyProps_del[1];
    items[1].rValue = &rightKeyProps_del[1];
    items[1].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[1].order = GMC_ORDER_ASC;

    // 设置区间
    ret = GmcSetKeyRange(g_stmt_tht[conn_id], items, arrLen);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyName(g_stmt_tht[conn_id], "ip4_local");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_tht[conn_id]);
    EXPECT_EQ(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt_tht[conn_id], &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        uint32_t rd_primary_label = 0;
        uint32_t rd_vrf_index = 0;
        uint32_t rd_dest_ip_addr = 0;
        uint8_t rd_mask_len = 0;
        uint32_t rd_attribute_id = 0;
        uint32_t rd_app_version = 0;
        uint32_t rd_nhp_group_id = 0;
        bool isNull;
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "vr_id", &rd_primary_label, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "vrf_index", &rd_vrf_index, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "dest_ip_addr", &rd_dest_ip_addr, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "mask_len", &rd_mask_len, sizeof(uint8_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret =
            GmcGetVertexPropertyByName(g_stmt_tht[conn_id], "app_version", &rd_app_version, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "attribute_id", &rd_attribute_id, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetVertexPropertyByName(
            g_stmt_tht[conn_id], "nhp_group_id", &rd_nhp_group_id, sizeof(uint32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }

    return ((void *)0);
}

// 13 多线程并发：2个线程进行DDL(建表/删表/truntcate表)/DML操作，2个线程不断的扫描查询；
TEST_F(scan_fetch_rows_test, DISABLED_TODEL_HardWare_Offloading_001_scan_fetch_rows_test_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    //创建 vertexLabel_1
    char labelName[128] = "ip4forward00000";

    char *schema_json = NULL;
    readJanssonFile("./schema_file/ip4forward00000.gmjson", &schema_json);
    ASSERT_NE((void *)NULL, schema_json);
    int ret = GmcCreateVertexLabel(stmt, schema_json, g_configJson);
    ASSERT_EQ(GMERR_OK, ret);
    free(schema_json);
    
    uint64_t count;

    // 多线程并发dml操作
    pthread_t thr_arr[32];
    void *thr_ret[32];
    int index[32] = {0};
    memset(g_conn_tht, 0, sizeof(void *) * THR_NUM * 2);

    for (int i = 0; i < 2; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_dml_operation, (void *)&index[i]);
        sleep(1);
    }
    for (int i = 2; i < 4; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_dml_query, (void *)&index[i]);
        sleep(1);
    }

    for (int i = 0; i < 4; i++) {
        pthread_join(thr_arr[i], &thr_ret[i]);
    }

    sleep(1);

    //设置扫描缓存条数  设置范围为[1, 2048]
    unsigned int cnt = 0;
    int prerows = 2048;
    
    ret = testGmcPrepareStmtByLabelName(stmt, g_labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcSetStmtAttr(stmt, GMC_STMT_ATTR_PRE_FETCH_ROWS, &prerows, sizeof(prerows));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);

    IP4FORWARD_SCAN_GETCH();

    ret = GmcDropVertexLabel(stmt, g_labelName);
    ASSERT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
