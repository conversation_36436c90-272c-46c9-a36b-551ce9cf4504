/*****************************************************************************
 Description  : 异步批量Merge功能测试
 Notes        : DDL_015_BatchMergeVertexAsync_001	001.同步批量merge 100个顶点，读数据
                DDL_015_BatchMergeVertexAsync_002	002.同步批量insert
100个顶点，merge该顶点，update该顶点，replace该顶点，delete该顶点 DDL_015_BatchMergeVertexAsync_003	003.同步批量merge
多个vertexLabel的顶点 DDL_015_BatchMergeVertexAsync_004	004.同步批量merge 1025个顶点，执行失败
                DDL_015_BatchMergeVertexAsync_005	005.同一vertexLabel并发同步批量merge100个顶点，读数据
                DDL_015_BatchMergeVertexAsync_006	006.不同vertexLabel并发同步批量merge ,读数据
 History      :
 Author       : 林健 lwx734521
 Modification :
 Date         : 2020/11/17
*****************************************************************************/
extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#include "../../common/hash_util.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

int affectRows;
unsigned int len;
char *normal_vertexlabel_schema = NULL;
char *simple_vertexlabel_schema = NULL;
const char *normal_config_json = R"(
    {
        "max_record_count":10000
    }
)";
const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_simple_vertexlabel_name = "T39_simple";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";
const char *g_simple_pk_name = g_normal_pk_name;
const char *g_simple_sk_name = g_normal_sk_name;

void set_VertexProperty_PK(GmcStmtT *stmt, int i);
void set_VertexProperty_SK(GmcStmtT *stmt, int i);
void set_VertexProperty(GmcStmtT *stmt, int i);
void query_VertexProperty(GmcStmtT *stmt, int i);

class BatchMergeAsync : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
        
        int ret = 0;
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        testEnvClean();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //恢复配置文件
        RecoverCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void BatchMergeAsync::SetUp()
{
    printf("[INFO] BatchMergeAsync Start.\n");
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
    ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    readJanssonFile("schemaFile/SimpleVertexLabel.gmjson", &simple_vertexlabel_schema);
    ASSERT_NE((void *)NULL, simple_vertexlabel_schema);
    AW_CHECK_LOG_BEGIN();
}

void BatchMergeAsync::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(normal_vertexlabel_schema);
    free(simple_vertexlabel_schema);
    printf("[INFO] BatchMergeAsync End.\n");
}

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void set_VertexProperty_SK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char F14Value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    // Get F0
    char F0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F1
    unsigned char F1Value = i;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F2
    int8_t F2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F3
    uint8_t F3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F4
    int16_t F4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F5
    uint16_t F5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F6
    int32_t F6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F8
    bool F8Value = false;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F10
    uint64_t F10Value = i;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F11
    float F11Value = i;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F12
    double F12Value = i;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F13
    uint64_t F13Value = i;
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F14
    char F14Value[] = "testver";
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, F14Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F15
    char F15Value[12] = "12";
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, F15Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // Get F16
    char F16Value[12] = "13";
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, F16Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001.同步批量merge 100个顶点，读数据
TEST_F(BatchMergeAsync, HardWare_Offloading_001_DDL_015_BatchMergeVertexAsync_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t start_num = 0;
    uint32_t end_num = 100;
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // batch merge Vertex
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(end_num, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num, data.succNum);
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // query merge Vertex
    for (uint32_t i = start_num; i < end_num; i++) {
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        query_VertexProperty(g_stmt, i);
        uint64_t SKValue = i;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.同步批量insert 100个顶点，merge该顶点，update该顶点，replace该顶点，delete该顶点
TEST_F(BatchMergeAsync, HardWare_Offloading_001_DDL_015_BatchMergeVertexAsync_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t start_num = 0;
    uint32_t end_num = 100;

    // batch insert Vertex
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt_async, i);
        set_VertexProperty_SK(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(end_num, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num, data.succNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // batch merge Vertex
    ret = GmcBatchReset(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T39_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i + 100);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(end_num, totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num, successNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // query Vertex
    for (uint32_t i = start_num; i < end_num; i++) {
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        query_VertexProperty(g_stmt, i + 100);
        uint64_t SKValue = i;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // batch update Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchReset(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt, i + 100);
        set_VertexProperty(g_stmt, i + 200);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(end_num, totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num, successNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // query Vertex
    for (uint32_t i = start_num; i < end_num; i++) {
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        query_VertexProperty(g_stmt, i + 200);
        uint64_t SKValue = i + 100;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // batch replace Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchReset(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i + 200);
        set_VertexProperty(g_stmt, i + 100);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(end_num, totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num, successNum);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // query Vertex
    for (uint32_t i = start_num; i < end_num; i++) {
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        query_VertexProperty(g_stmt, i + 100);
        uint64_t SKValue = i + 200;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // batch delete Vertex
    ret = GmcBatchReset(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    totalNum = 0;
    successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(end_num, totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num, successNum);
    // remove Vertex
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.同步批量merge 多个vertexLabel的顶点
TEST_F(BatchMergeAsync, HardWare_Offloading_001_DDL_015_BatchMergeVertexAsync_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *vertexLabel1 = NULL;
    void *vertexLabel2 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, simple_vertexlabel_schema, normal_config_json);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t start_num = 0;
    uint32_t end_num = 100;

    // batch merge Vertex
    AsyncUserDataT data = {0};
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_simple_vertexlabel_name, GMC_OPERATION_MERGE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(end_num * 2, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num * 2, data.succNum);

    // query merge Vertex
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Query VertexLabel1
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        query_VertexProperty(g_stmt, i);
        uint64_t SKValue = i;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_simple_vertexlabel_name, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Query VertexLabel2
        ret = GmcSetIndexKeyName(g_stmt, g_simple_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        bool IsFinish = true;
        ret = GmcFetch(g_stmt, &IsFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        query_VertexProperty(g_stmt, i);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // free
    GmcFreeIndexKey(g_stmt);
    GmcBatchDestroy(batch);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simple_vertexlabel_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.同步批量merge 1025个顶点，执行失败
TEST_F(BatchMergeAsync, HardWare_Offloading_001_DDL_015_BatchMergeVertexAsync_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_BATCH_BUFFER_FULL);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    uint32_t start_num = 0;
    uint32_t end_num = 1025;

    // batch merge Vertex 1025
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        if (i == 1024) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_BATCH_BUFFER_FULL, ret);
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    // batch merge Vertex 1024
    end_num = 1024;
    ret = GmcBatchReset(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(end_num, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num, data.succNum);

    // free
    GmcBatchDestroy(batch);
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *batchMergeVertexNormal(void *args)
{
    void *vertexLabel;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn_t, &stmt_t, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt_t, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 100;
    uint32_t val = 1000;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_t, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_t, "T39_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(stmt_t, val);
        ret = GmcBatchAddDML(batch, stmt_t);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(end_num, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num, data.succNum);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn_t, stmt_t);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *batchMergeVertexSimple(void *args)
{
    void *vertexLabel;
    GmcConnT *conn_t = NULL;
    GmcStmtT *stmt_t = NULL;
    int ret = 0;
    ret = testGmcConnect(&conn_t, &stmt_t, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt_t, g_simple_vertexlabel_name, GMC_OPERATION_MERGE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int start_num = 0;
    int end_num = 100;
    uint32_t val = 1000;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn_t, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt_t, "T39_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty(stmt_t, val);
        ret = GmcBatchAddDML(batch, stmt_t);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(end_num, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num, data.succNum);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn_t, stmt_t);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 005.同一vertexLabel并发同步批量merge100个顶点，读数据
TEST_F(BatchMergeAsync, HardWare_Offloading_001_DDL_015_BatchMergeVertexAsync_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *vertexLabel1 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t start_num = 0;
    uint32_t end_num = 100;

    // batch insert Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(end_num, totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num, successNum);

    // multi threads merge
    int tdNum = 8;
    int err = 0;
    pthread_t sameNameth[tdNum];
    for (int i = 0; i < tdNum; i++) {
        err = pthread_create(&sameNameth[i], NULL, batchMergeVertexNormal, NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, err);
    }
    for (int i = 0; i < tdNum; i++) {
        pthread_join(sameNameth[i], NULL);
    }

    // query merge Vertex
    ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int val = 1000;
    for (uint32_t i = start_num; i < end_num; i++) {
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Query Vertex
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        query_VertexProperty(g_stmt, val);
        uint64_t SKValue = i;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.不同vertexLabel并发同步批量merge ,读数据
TEST_F(BatchMergeAsync, HardWare_Offloading_001_DDL_015_BatchMergeVertexAsync_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    void *vertexLabel1 = NULL;
    void *vertexLabel2 = NULL;
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt, simple_vertexlabel_schema, normal_config_json);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t start_num = 0;
    uint32_t end_num = 100;

    // batch insert Vertex
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_simple_vertexlabel_name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt, i);
        set_VertexProperty_SK(g_stmt, i);
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(end_num * 2, totalNum);
    AW_MACRO_EXPECT_EQ_INT(end_num * 2, successNum);

    // multi threads merge
    int err = 0;
    pthread_t sameNameth[2];
    err = pthread_create(&sameNameth[0], NULL, batchMergeVertexNormal, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, err);
    err = pthread_create(&sameNameth[1], NULL, batchMergeVertexSimple, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, err);
    pthread_join(sameNameth[0], NULL);
    pthread_join(sameNameth[1], NULL);

    // query
    int val = 1000;
    for (uint32_t i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_normal_vertexlabel_name, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t priK = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Query VertexLabel1
        ret = GmcSetIndexKeyName(g_stmt, g_normal_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        query_VertexProperty(g_stmt, val);
        uint64_t SKValue = i;
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, g_simple_vertexlabel_name, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &priK, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // Query VertexLabel2
        ret = GmcSetIndexKeyName(g_stmt, g_simple_pk_name);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        query_VertexProperty(g_stmt, val);
        ret = queryPropertyAndCompare(g_stmt, "F9", GMC_DATATYPE_INT64, &SKValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcBatchDestroy(batch);
    // free
    GmcFreeIndexKey(g_stmt);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_simple_vertexlabel_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
