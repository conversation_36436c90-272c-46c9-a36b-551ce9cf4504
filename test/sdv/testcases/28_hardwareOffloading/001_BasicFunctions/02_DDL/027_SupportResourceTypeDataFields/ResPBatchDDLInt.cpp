/*****************************************************************************
 Description  : 支持资源数据类型特性
 Notes        : 1: 一个表中含有多个资源类型的字段
                2：同步更新资源类型数据
                3：同步查询资源类型数据
                4：异步写入资源类型数据
                5：异步批量写入资源类型数据
 History      :
 Author       : liuli lwx1035319
 Modification :
 Date         :
*****************************************************************************/
extern "C" {
}
#include "gtest/gtest.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>

#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "../../common/hash_util.h"

int ret = 0;
GmcConnT *g_conn;
GmcStmtT *g_stmt;
void *g_label = NULL;

GmcConnT *g_conn_async = NULL;
GmcStmtT *g_stmt_async = NULL;

AsyncUserDataT data = {0};

static const char *resPoolTestName = "resource_pool_test";
static const char *g_label_name = "TestResource";
static const char *g_labelConfig = R"({"max_record_num":1000})";
static const char *gResPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 100,
        "order" : 0,
        "alloc_type" : 0
    })";

static const char *resPoolDumpName = "resource_pool_dump";
const char *expect = NULL;
class ResPBatchDDLIntTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();  //迭代一带有资源列的表不能被删除，只能重启gmserver。
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        //恢复配置文件
        RecoverCfg();
    }
};

void ResPBatchDDLIntTest::SetUp()
{

    //封装的创建异步连接
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDestroyResPool(g_stmt, resPoolTestName);
    AW_CHECK_LOG_BEGIN();
}

void ResPBatchDDLIntTest::TearDown()
{
    AW_CHECK_LOG_END();
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    g_conn_async = NULL;
    g_stmt_async = NULL;
    GmcFreeStmt(g_stmt);  //析构与构造顺序相反
    ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 一个表中含有多个资源类型的字段
TEST_F(ResPBatchDDLIntTest, HardWare_Offloading_001_DDL_027_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    ret = GmcCreateResPool(g_stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    void *globleVertexLabel = NULL;

    readJanssonFile("schema_file/BatchDDLInterfaceTest.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(g_stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t respoolId = 10001;
    uint64_t count = 1;
    uint64_t startIndex = 2;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 10001;
    count = 1;
    startIndex = 3;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isBatch = false;
    // uint64_t *resourceId;
    // uint32_t bufLen;

    uint32_t bufLen;
    ret = GmcGetResIdNum(g_stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(g_stmt, resourceId, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (uint32_t i = 0; i < bufLen; i++) {
        uint64_t poolId = 0;
        uint64_t count = 0;
        uint64_t startIndex = 0;
        ret = GmcGetPoolIdResource(resourceId[i], (uint16_t *)&poolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resourceId[i], (uint16_t *)&count);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resourceId[i], (uint32_t *)&startIndex);

        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10001, poolId);
        AW_MACRO_EXPECT_EQ_INT(1, count);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetFilter(g_stmt, "TestResource.F0 = 12");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *resPoolJson = NULL;
    ret = GmcGetResPool(g_stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(g_stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(label_schema);
    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 同步更新资源类型数据
TEST_F(ResPBatchDDLIntTest, HardWare_Offloading_001_DDL_027_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    ret = GmcCreateResPool(g_stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    void *globleVertexLabel = NULL;

    readJanssonFile("schema_file/BatchDDLInterfaceTest.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(g_stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t respoolId = 10001;
    uint64_t count = 1;
    uint64_t startIndex = 2;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 10001;
    count = 1;
    startIndex = 3;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isBatch = false;
    uint32_t bufLen;
    int ret = GmcGetResIdNum(g_stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(g_stmt, resourceId, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (uint32_t i = 0; i < bufLen; i++) {
        uint64_t poolId = 0;
        uint64_t count = 0;
        uint64_t startIndex = 0;
        ret = GmcGetPoolIdResource(resourceId[i], (uint16_t *)&poolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resourceId[i], (uint16_t *)&count);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resourceId[i], (uint32_t *)&startIndex);

        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10001, poolId);
        AW_MACRO_EXPECT_EQ_INT(1, count);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t index = 12;
    int32_t value = 999;

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &index, sizeof(index));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //设置F1属性值
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &value, sizeof(value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //更新顶点
    ret = GmcSetIndexKeyName(g_stmt, "T35_K0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetFilter(g_stmt, "TestResource.F0 = 12");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *resPoolJson = NULL;
    ret = GmcGetResPool(g_stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(g_stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(label_schema);
    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 同步查询资源类型数据
TEST_F(ResPBatchDDLIntTest, HardWare_Offloading_001_DDL_027_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    ret = GmcCreateResPool(g_stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    void *globleVertexLabel = NULL;

    readJanssonFile("schema_file/BatchDDLInterfaceTest.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(g_stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t respoolId = 10001;
    uint64_t count = 1;
    uint64_t startIndex = 2;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 10001;
    count = 1;
    startIndex = 3;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    unsigned int sizeF1;
    uint32_t valueF1;
    bool isNull;
    bool isBatch = false;
    uint32_t bufLen;
    int ret = GmcGetResIdNum(g_stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(g_stmt, resourceId, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    for (uint32_t i = 0; i < bufLen; i++) {
        uint64_t poolId = 0;
        uint64_t count = 0;
        uint64_t startIndex = 0;
        ret = GmcGetPoolIdResource(resourceId[i], (uint16_t *)&poolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resourceId[i], (uint16_t *)&count);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resourceId[i], (uint32_t *)&startIndex);

        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10001, poolId);
        AW_MACRO_EXPECT_EQ_INT(1, count);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //设置过滤条件
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //查询顶点
    ret = GmcSetIndexKeyName(g_stmt, "T35_K0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //再次Get F1
    GmcGetVertexPropertySizeByName(g_stmt, "F1", &sizeF1);
    ret = GmcGetVertexPropertyByName(g_stmt, "F1", &valueF1, sizeF1, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    LOG("valueF1 = %d, value1 = %d, isNull = %d", valueF1, F1Value, isNull);
    if (isNull == 0) {
        ret = 1;
        if (valueF1 == F1Value) {
            ret = 0;
        }
        AW_MACRO_EXPECT_EQ_INT(0, ret);  //预期获取插入值
    } else {
        AW_MACRO_EXPECT_EQ_INT(0, 1);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetFilter(g_stmt, "TestResource.F0 = 12");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *resPoolJson = NULL;
    ret = GmcGetResPool(g_stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(g_stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(label_schema);
    ret = GmcDropVertexLabel(g_stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void ResourceAsyncInsertCb(
    void *userData, uint32_t affectedRows, GmcResourceInfoT *resInfo, int32_t status, const char *errMsg)
{
    for (uint32_t i = 0; i < resInfo->resIdNum; i++) {
        uint16_t poolId = 0;
        uint16_t count = 0;
        uint32_t startIndex = 0;
        ret = GmcGetPoolIdResource(resInfo->resIdBuf[i], &poolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resInfo->resIdBuf[i], &count);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resInfo->resIdBuf[i], &startIndex);

        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10001, poolId);
        AW_MACRO_EXPECT_EQ_INT(1U, count);
    }

    //原有的回调
    AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
    user_data->status = status;
    user_data->affectRows = affectedRows;
    user_data->recvNum++;
}

// 异步写入资源类型数据
TEST_F(ResPBatchDDLIntTest, HardWare_Offloading_001_DDL_027_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    ret = GmcCreateResPool(g_stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schema_file/BatchDDLInterfaceTest.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);

    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, label_schema, g_labelConfig, create_vertex_label_callback, &data);
    if (ret == GMERR_DUPLICATE_TABLE) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data.status = (data.status == GMERR_DUPLICATE_TABLE ? GMERR_OK : data.status);
    if (ret == GMERR_DUPLICATE_TABLE) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(label_schema);
    LOG("CreateVertexLabelAsync succ");

    ret = GmcBindResPoolToLabel(g_stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_INSERT_WITH_RESOURCE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    LOG("OpenVertexLabelAsync succ");

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t respoolId = 10001;
    uint64_t count = 1;
    uint64_t startIndex = 2;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 10001;
    count = 1;
    startIndex = 3;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcAsyncRequestDoneContextT insertResourceRequestCtx;
    insertResourceRequestCtx.insertWithResourceCb = ResourceAsyncInsertCb;
    insertResourceRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &insertResourceRequestCtx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    data = {0};
    ret = GmcSetIndexKeyName(g_stmt_async, (char *)"T35_K0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.affectRows);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *resPoolJson = NULL;
    ret = GmcGetResPool(g_stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(g_stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_label_name, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    LOG("drop asyn vertex label");
    AW_FUN_Log(LOG_STEP, "test end.");
}

void ResourceAsyncBatchCb(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, status);
    uint32_t resIdNum;
    GmcBatchGetResIdNum(batchRet, NULL, &resIdNum);
    AW_MACRO_EXPECT_EQ_INT(2u, resIdNum);

    uint64_t resIdBuf[resIdNum];
    GmcBatchGetResIdInfo(batchRet, NULL, resIdBuf, &resIdNum);
    for (uint32_t i = 0; i < resIdNum; i++) {
        uint16_t poolId = 0;
        uint16_t count = 0;
        uint32_t startIndex = 0;
        ret = GmcGetPoolIdResource(resIdBuf[i], &poolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdBuf[i], &count);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdBuf[i], &startIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10001, poolId);
        AW_MACRO_EXPECT_EQ_INT(1U, count);
    }

    //原有的回调
    AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    GmcBatchDeparseRet(batchRet, &totalNum, &successNum);
    AW_MACRO_EXPECT_EQ_INT(user_data->expTotalNum, totalNum);
    AW_MACRO_EXPECT_EQ_INT(user_data->expSuccNum, successNum);
    user_data->recvNum++;
}

// 异步批量写入资源类型数据
TEST_F(ResPBatchDDLIntTest, HardWare_Offloading_001_DDL_027_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    ret = GmcCreateResPool(g_stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schema_file/BatchDDLInterfaceTest.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);

    AsyncUserDataT data = {0};
    ret = GmcCreateVertexLabelAsync(g_stmt_async, label_schema, g_labelConfig, create_vertex_label_callback, &data);
    if (ret == GMERR_DUPLICATE_TABLE) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = data.status;
    if (ret == GMERR_DUPLICATE_TABLE) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    data.status = (data.status == GMERR_DUPLICATE_TABLE ? GMERR_OK : data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    free(label_schema);
    LOG("CreateVertexLabelAsync succ");

    ret = GmcBindResPoolToLabel(g_stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, NULL, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    LOG("OpenVertexLabelAsync succ");

    int F0Value = 12;
    ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F1Value = 13;
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F2Value = 132;
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t respoolId = 10001;
    uint64_t count = 1;
    uint64_t startIndex = 2;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 10001;
    count = 1;
    startIndex = 3;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F4", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchAddDML(batch, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t received = 0;
    AsyncUserDataT userData = {0};
    userData.expTotalNum = count;
    userData.expSuccNum = count;
    ret = GmcBatchExecuteAsync(batch, ResourceAsyncBatchCb, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    data = {0};
    ret = GmcSetIndexKeyName(g_stmt_async, (char *)"T35_K0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcAsyncRequestDoneContextT deleteRequestCtx;
    deleteRequestCtx.deleteCb = delete_vertex_callback;
    deleteRequestCtx.userData = &data;
    ret = GmcExecuteAsync(g_stmt_async, &deleteRequestCtx);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.affectRows);

    ret = GmcUnbindResPoolFromLabel(g_stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *resPoolJson = NULL;
    ret = GmcGetResPool(g_stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    (void)GmcBatchDestroy(batch);
    ret = GmcDestroyResPool(g_stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabelAsync(g_stmt_async, g_label_name, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    LOG("drop asyn vertex label");
    AW_FUN_Log(LOG_STEP, "test end.");
}
