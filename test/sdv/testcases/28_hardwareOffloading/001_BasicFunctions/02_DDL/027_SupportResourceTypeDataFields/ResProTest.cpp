/*****************************************************************************
 Description  : 支持资源数据类型特性
 Notes        : 010:创建一个多个资源列的表
                011:创建一个资源属性表并open顶点
                012:删除一个多个资源列的表顶点
                013:对资源属性表，批量合并顶点
                014:对资源属性表，同步批量insert顶点
                015:对资源属性表，同步批量update顶点
                016:对资源属性表，同步批量replace顶点
                017:对资源属性表，同步批量delete顶点
                018:对资源属性表，同步查询顶点
                019:对资源属性表，预置部分数据，全表扫描
                021:同步单个从资源表读取资源类型数据
                022:同步批量导入资源表和资源池并将资源池绑定到资源表
                023:同步单个写入资源类型数据
                024:同步批量写入资源类型数据
 History      :
 Author       : liuli lwx1035319
 Modification :
 Date         : 2021/04/06
*****************************************************************************/
extern "C" {
}

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "./resource_sub_tools.h"
#include "../../common/hash_util.h"

#define MEMORY_LEAKAGE_IS_SOLVEED 1
#define TEST_INFO(log, args...)                                               \
    do {                                                                      \
        fprintf(stdout, "Info: %s:%d " log "\n", __FILE__, __LINE__, ##args); \
    } while (0)

#define CHECK_AND_BREAK(ret, log, args...)                                                             \
    if ((ret) != GMERR_OK) {                                                                           \
        fprintf(stdout, "Error: %s:%d " log " failed, ret = %d\n", __FILE__, __LINE__, ##args, (ret)); \
        break;                                                                                         \
    }

char *g_label_schema = NULL;
void *globleVertexLabel = NULL;
char *resPoolJson = NULL;
char g_lable_PK[] = "T35_PK";
int ret = 0;
bool isFinish;
unsigned int valueSize;
bool isNull;

class ResProTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        readJanssonFile("./schema_file/all_type_schema.gmjson", &g_label_schema);
        ASSERT_NE((void *)NULL, g_label_schema);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        free(g_label_schema);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        //恢复配置文件
        RecoverCfg();
    };
};

const char *cfgJson = R"({"max_record_num":1000})";

static const char *g_label_name = "TestResource";
static const char *resPoolTestName = "resource_pool_test";

static const char *gResPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 400,
        "order" : 0,
        "alloc_type" : 0
    })";

static const char *resPoolDumpName = "resource_pool_dump";

void ResProTest::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    GmcDestroyResPool(stmt, resPoolTestName);
    AW_CHECK_LOG_BEGIN();
    AddWhiteList(GMERR_RESOURCE_POOL_ERROR);
}

void ResProTest::TearDown()
{
    AW_CHECK_LOG_END();
    printf("\n======================TEST:END========================\n");
}

void TestSetVertexPropertyCurrent(GmcStmtT *stmt, int pk = 0)
{
    int ret;
    uint64_t respoolId = 0xFFFF;
    uint64_t count = 2;
    uint64_t startIndex = 0xFFFFFFFF;

    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value2 = 1 + pk;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value3 = 10 + pk;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value4 = 100 + pk;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value5 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value6 = pk;  // 联合索引时F6是PK
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value7 = pk;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr14[10] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, (strlen(teststr16)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value17 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void TestVertexPropertyReplace(GmcStmtT *stmt, int pk = 0)
{
    int ret;
    uint64_t respoolId = 0xFFFF;
    uint64_t count = 2;
    uint64_t startIndex = 0xFFFFFFFF;

    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value2 = 1 + pk;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value3 = 10 + pk;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value4 = 100 + pk;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value5 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value6 = pk;  // 联合索引时F6是PK
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value7 = pk;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, (strlen(teststr16)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value17 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F18", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F19", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F20", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F21", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int32_t queryFieldValueAndCompare(
    GmcStmtT *stmt, const char *fieldName, void *readValueOut, unsigned int *getValueSizeOut)
{
    ret = GmcGetVertexPropertySizeByName(stmt, fieldName, getValueSizeOut);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, fieldName, readValueOut, *getValueSizeOut, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int32_t TestScanAllVertexProperty(GmcStmtT *stmt, void *vertexLabel, int oper_nums)
{
    int ret = 0;
    int cnt = 0;
    int count = 0;
    char teststr0[] = {'\0'};
    unsigned char teststr1[] = {'\0'};
    int8_t value2;
    uint8_t value3;
    int16_t value4;
    uint16_t value5;
    int32_t value6;  // 联合索引时F6是pk
    uint32_t value7;
    bool value8;
    int64_t value9;
    uint64_t value10;
    float value11;
    double value12;
    uint64_t value13;
    char teststr14[64] = {'\0'};
    char teststr15[10] = {'\0'};
    char teststr16[6] = {'\0'};
    uint32_t value17;

    uint64_t count0;
    uint64_t count1;
    uint64_t count2;
    uint64_t count3;

    ret = GmcSetFilter(stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetOutputFormat(stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < oper_nums + 1; i++) {
        cnt++;
        ret = GmcFetch(stmt, &isFinish);
        if (isFinish == true || ret != 0) {
            return GMERR_OK;
        }

        ret = queryFieldValueAndCompare(stmt, "F0", &teststr0, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F1", &teststr1, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F2", &value2, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F3", &value3, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F4", &value4, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F5", &value5, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F6", &value6, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryFieldValueAndCompare(stmt, "F7", &value7, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F8", &value8, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F9", &value9, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(8, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F10", &value10, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(8, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F11", &value11, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F12", &value12, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(8, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F13", &value13, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(8, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F14", teststr14, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(7, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F15", teststr15, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(10, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F16", teststr16, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(5, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F17", &value17, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(4, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F18", &count0, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(8, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F19", &count1, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(8, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F20", &count2, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(8, valueSize);
        ret = queryFieldValueAndCompare(stmt, "F21", &count3, &valueSize);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(8, valueSize);
    }
}

//  008:创建一个多资源列的表
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

//	009:创建一个资源属性表并open顶点
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#if NOT_EXIST
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#if 1
    /*影响到DDL_027_021报错munmap_chunk(): invalid pointer
     *胡文奇(00470566) 2021-04-23 17:24
     *刚才我这里这个问题是上面的用例踩内存了
     *胡文奇(00470566) 2021-04-23 17:25
     *下面内存被踩坏了之后内核又复用了内存块
     *然后内存块长度就不对了
     */
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);
#endif

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010:删除一个多个资源列的表顶点
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);


    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestSetVertexPropertyCurrent(stmt);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // uint64_t* resourceId;
    // uint32_t bufLen;
    uint32_t bufLen;
    int ret = GmcGetResIdNum(stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(stmt, resourceId, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetFilter(stmt, "TestResource.F6 = 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011:对资源属性表，批量合并顶点
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int i;
    int start_num = 0;
    int end_num = 50;

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //批量写数据
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        TestSetVertexPropertyCurrent(stmt, i);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), totalNum);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), successNum);
    GmcBatchDestroy(batch);

    //批量更新数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        test_setVertexProperty_updt(stmt, i + end_num);
        ret = GmcSetIndexKeyName(stmt, g_lable_PK);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), totalNum);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), successNum);
    GmcBatchDestroy(batch);

    int tmp;
    //批量删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 5U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_PK);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), totalNum);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), successNum);
    GmcBatchDestroy(batch);

    //删除数据（即释放资源索引）
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = end_num; i < 2 * end_num; i++) {
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F6 = %u", g_label_name, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(stmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
        memset(condStr, 0, sizeof(condStr));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012:对资源属性表，同步批量insert顶点
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int i;
    int start_num = 0;
    int end_num = 10;

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //批量写数据
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        TestSetVertexPropertyCurrent(stmt, i);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), totalNum);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), successNum);

    uint32_t resIdNum;
    ret = GmcBatchGetResIdNum(&batchRet, NULL, &resIdNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t resIdBuf[resIdNum];
    ret = GmcBatchGetResIdInfo(&batchRet, NULL, resIdBuf, &resIdNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000);
    GmcBatchDestroy(batch);
    // 删除数据（即释放资源索引）
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F6 = %u", g_label_name, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(stmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
        memset(condStr, 0, sizeof(condStr));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 :对资源属性表，同步批量update顶点
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int i;
    int start_num = 0;
    int end_num = 10;

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //批量写数据
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        TestSetVertexPropertyCurrent(stmt, i);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), totalNum);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), successNum);

    uint32_t resIdNum;
    ret = GmcBatchGetResIdNum(&batchRet, NULL, &resIdNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t resIdBuf[resIdNum];
    ret = GmcBatchGetResIdInfo(&batchRet, NULL, resIdBuf, &resIdNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000);
    GmcBatchDestroy(batch);

    // 批量更新数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, NULL, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        test_setVertexProperty_updt(stmt, i + end_num);
        ret = GmcSetIndexKeyName(stmt, g_lable_PK);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), totalNum);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), successNum);
    GmcBatchDestroy(batch);

    // 删除数据（即释放资源索引）
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 2021.11.23: 主键不可更新
    for (i = start_num; i < end_num; i++) {
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F6 = %u", g_label_name, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(stmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
        memset(condStr, 0, sizeof(condStr));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014:对资源属性表，同步批量replace顶点
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

    int i;
    int start_num = 0;
    int end_num = 10;

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //批量写数据
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, NULL, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        TestSetVertexPropertyCurrent(stmt, i);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), totalNum);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), successNum);
    GmcBatchDestroy(batch);

    // replace:资源表进行replace操作时，如果是走update流程，只有在不显示更新资源字段或者资源字段的ResId和DB中当前这条记录的
    // resID一致才能成功，其他场景会报错.
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_REPLACE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBatchPrepare(conn, NULL, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestVertexPropertyReplace(stmt, i + end_num);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(end_num, totalNum);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除数据（即释放资源索引）
    ret = GmcDeleteAllFast(stmt, g_label_name);  //对表数据全部删除
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000);

    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    if (ret == GMERR_RESOURCE_POOL_ERROR) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        while (1) {
            ret = GmcDeleteAllFast(stmt, g_label_name);  //对表数据全部删除
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            usleep(100);
            ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
            if (ret == GMERR_OK) {
                break;
            }
        }
    } else
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);
    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015:对资源属性表，同步批量delete顶点
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int i;
    int start_num = 0;
    int end_num = 50;

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //批量写数据
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        TestSetVertexPropertyCurrent(stmt, i);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), totalNum);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), successNum);
    GmcBatchDestroy(batch);

    int tmp;
    //批量删除数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 5U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, g_lable_PK);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), totalNum);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), successNum);
    GmcBatchDestroy(batch);

    // 删除数据（即释放资源索引）
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F6 = %u", g_label_name, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(stmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
        memset(condStr, 0, sizeof(condStr));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016:对资源属性表，同步查询顶点
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int i;
    int start_num = 0;
    int end_num = 10;

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //写数据
    for (i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestSetVertexPropertyCurrent(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, g_affectRows);
        //查询顶点
        TestCheckVertexTableProperty(stmt, g_label_name, g_lable_PK, i);
    }

    // 删除数据（即释放资源索引）
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F6 = %u", g_label_name, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(stmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
        memset(condStr, 0, sizeof(condStr));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017:对资源属性表，预置部分数据，全表扫描
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int i;
    int start_num = 0;
    int end_num = 10;

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //写数据
    for (i = start_num; i < end_num; i++) {
        TestSetVertexPropertyCurrent(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, g_affectRows);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestScanAllVertexProperty(stmt, globleVertexLabel, end_num);
    // 删除数据（即释放资源索引）
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F6 = %u", g_label_name, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(stmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
        memset(condStr, 0, sizeof(condStr));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021:同步单个从资源表读取资源类型数据
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int i;

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //写数据
    for (i = 0; i < 1; i++) {
        TestSetVertexPropertyCurrent(stmt, i);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // uint64_t* resourceId;
        // uint32_t bufLen;
        uint32_t bufLen;
        int ret = GmcGetResIdNum(stmt, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        uint64_t resourceId[bufLen];
        ret = GmcGetResIdInfo(stmt, resourceId, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        for (uint32_t j = 0; j < bufLen; j++) {
            uint64_t poolId = 0;
            uint64_t count = 0;
            uint64_t startIndex = 0;
            ret = GmcGetPoolIdResource(resourceId[j], (uint16_t *)&poolId);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetCountResource(resourceId[j], (uint16_t *)&count);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcGetStartIdxResource(resourceId[j], (uint32_t *)&startIndex);

            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 删除数据（即释放资源索引）
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < 1; i++) {
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F6 = %u", g_label_name, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(stmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
        memset(condStr, 0, sizeof(condStr));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
//之前已出现运行第二次内存泄漏，重复可复现
#ifdef MEMORY_LEAKAGE_IS_SOLVEED
    for (int ipos = 0; ipos < 300; ipos++) {
        resPoolJson = NULL;
        ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ASSERT_STREQ(gResPoolTest, resPoolJson);
    }
#endif

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021:同步批量导入资源表和资源池并将资源池绑定到资源表
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {}, errorMsg2[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg, errorMsg2);

    const char *schema_path = "./gmimport_files_batch_json";
    char cmd[1024];
    snprintf(cmd, 1024, "%s/gmimport -c cache -f %s -s %s -ns %s", g_toolPath, schema_path,
        g_connServer, g_testNameSpace);
    sleep(1);
    system(cmd);

    // 接口创建同名VertexLabel
    char labelName[] = "TestResource022";
    ret = GmcCreateVertexLabel(stmt, g_label_schema, cfgJson);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#ifdef MEMORY_LEAKAGE_IS_SOLVEED
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022:同步单个写入资源类型数据
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int i;
    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //写数据
    for (i = 0; i < 1; i++) {
        TestSetVertexPropertyCurrent(stmt, i);
        ret = GmcExecute(stmt);

        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &g_affectRows, sizeof(g_affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, g_affectRows);
    }

    // uint64_t* resourceId;
    // uint32_t bufLen;

    uint32_t bufLen;
    int ret = GmcGetResIdNum(stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(stmt, resourceId, &bufLen);
    // 删除数据（即释放资源索引）
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < 1; i++) {
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F6 = %u", g_label_name, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(stmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
        memset(condStr, 0, sizeof(condStr));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#ifdef MEMORY_LEAKAGE_IS_SOLVEED /*同021*/
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023:同步批量写入资源类型数据
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int i;
    int start_num = 0;
    int end_num = 10;

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批量写数据
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < 5 * end_num; i++) {
        TestSetVertexPropertyCurrent(stmt, i);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(5 * end_num, totalNum);
    AW_MACRO_EXPECT_EQ_INT(5 * end_num, successNum);
    uint32_t resIdNum;
    ret = GmcBatchGetResIdNum(&batchRet, NULL, &resIdNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t resIdBuf[resIdNum];
    ret = GmcBatchGetResIdInfo(&batchRet, NULL, resIdBuf, &resIdNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 删除数据（即释放资源索引）
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < 5 * end_num; i++) {
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F6 = %u", g_label_name, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(stmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
        memset(condStr, 0, sizeof(condStr));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#ifdef MEMORY_LEAKAGE_IS_SOLVEED
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 024 :对资源属性表，同步批量update顶点，获取资源id
TEST_F(ResProTest, HardWare_Offloading_001_DDL_027_261)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);
    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_INVALID_OPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);

    int i;
    int start_num = 0;
    int end_num = 10;

    GmcDestroyResPool(stmt, resPoolTestName);
    ret = GmcCreateResPool(stmt, gResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, g_label_schema, g_labelConfig);
    if (ret == GMERR_DUPLICATE_TABLE) {
        g_expect = NULL;
        ret = testGmcGetLastError(g_expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = (ret == GMERR_DUPLICATE_TABLE ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(stmt, resPoolTestName, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //批量写数据
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        TestSetVertexPropertyCurrent(stmt, i);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), totalNum);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), successNum);

    uint32_t resIdNum;
    ret = GmcBatchGetResIdNum(&batchRet, NULL, &resIdNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t resIdBuf[resIdNum];
    ret = GmcBatchGetResIdInfo(&batchRet, NULL, resIdBuf, &resIdNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    usleep(1000);
    GmcBatchDestroy(batch);

    //批量更新数据
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchPrepare(conn, NULL, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (i = start_num; i < end_num; i++) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        test_setVertexProperty_updt(stmt, i + end_num);
        ret = GmcSetIndexKeyName(stmt, g_lable_PK);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), totalNum);
    AW_MACRO_ASSERT_EQ_INT((end_num - start_num), successNum);

    resIdNum = 0;
    ret = GmcBatchGetResIdNum(&batchRet, NULL, &resIdNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    resIdBuf[resIdNum] = 0;
    ret = GmcBatchGetResIdInfo(&batchRet, NULL, resIdBuf, &resIdNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_OPTION, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchDestroy(batch);

    // 删除数据（即释放资源索引）
    ret = testGmcPrepareStmtByLabelName(stmt, g_label_name, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 2021.11.23: 主键不可更新
    for (i = start_num; i < end_num; i++) {
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F6 = %u", g_label_name, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(stmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
        memset(condStr, 0, sizeof(condStr));
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    resPoolJson = NULL;
    ret = GmcGetResPool(stmt, resPoolTestName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ASSERT_STREQ(gResPoolTest, resPoolJson);

    ret = GmcDestroyResPool(stmt, resPoolTestName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, g_label_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

