/*****************************************************************************
 Description  : 支持resource资源数据类型字段
 Notes        : 异常参数测试.
 History      :
 Author       : liuli lwx1035319
 Modification : [2021.03] codeing.
*****************************************************************************/
extern "C" {
}
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include <cmath>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "../../common/hash_util.h"

using namespace std;
extern "C" {
}

#ifdef HPE
#define DB_COMMON_INIT DbCommonInit(ADPT_HPE, NULL)
#define DB_COMMON_FINALIZE DbCommonFinalize(ADPT_HPE)
#define EN_TYPE string(" -e DAP")
#define SERVER_NAME string(" -s channel:")
#else
#define DB_COMMON_INIT DbCommonInit(ADPT_RTOS_SERVER, NULL)
#define DB_COMMON_FINALIZE DbCommonFinalize(ADPT_RTOS_SERVER)
#define EN_TYPE string(" -e RTOS")
#define SERVER_NAME string(" -s usocket:/run/verona/unix_emserver")
#endif

#define TEST_DEBUG 1
#define SCHEMA_JSON_SIZE 1024
#define LABELNAME_MAX_LENGTH 128
#define CONFIGNAME_MAX_LENGTH 128
char g_SpecialChar[128] = {"~`!@#$%^&*()-+={}[]\\|:;\"',.?/"};

int ret;
GmcConnT *conn = NULL;
GmcStmtT *res_stmt = NULL, *vtx_stmt = NULL, *ext_stmt = NULL;
void *vertexLabel = NULL;
const char *expect = NULL;
class ResPParCheck : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("\n======================TEST:BEGIN======================\n");
        // 创建同步客户端连接
        ret = testGmcConnect(&conn, &res_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        printf("\n======================TEST:END========================\n");
        // 关闭 client connection
        ret = testGmcDisconnect(conn, res_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    };
    virtual void SetUp();
    virtual void TearDown();
};

void ResPParCheck::SetUp()
{
    AW_CHECK_LOG_BEGIN();

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);

    char errorMsg4[128] = {};
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg4);

    char errorMsg5[128] = {};
    (void)snprintf(errorMsg5, sizeof(errorMsg5), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg5);

    char errorMsg6[128] = {};
    (void)snprintf(errorMsg6, sizeof(errorMsg6), "GMERR-%d", GMERR_UNDEFINE_COLUMN);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg6);

    char errorMsg7[128] = {};
    (void)snprintf(errorMsg7, sizeof(errorMsg7), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg7);

    char errorMsg8[128] = {};
    (void)snprintf(errorMsg8, sizeof(errorMsg8), "GMERR-%d", GMERR_INVALID_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg8);

    char errorMsg9[128] = {};
    (void)snprintf(errorMsg9, sizeof(errorMsg9), "GMERR-%d", GMERR_UNDEFINED_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg9);

    char errorMsg10[128] = {};
    (void)snprintf(errorMsg10, sizeof(errorMsg10), "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg10);
}

void ResPParCheck::TearDown()
{
    AW_CHECK_LOG_END();
}

class ResPParCheck_01 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
    };
 
    static void TearDownTestCase()
    {
        //恢复配置文件
        RecoverCfg();
    };
    virtual void SetUp();
    virtual void TearDown();
};
 
void ResPParCheck_01::SetUp()
{}
 
void ResPParCheck_01::TearDown()
{}

/*****************************************************************************************
 * //GmcCreateResPool resPoolName64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_109)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolName = "creates1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb";
    const char *ResPoolTest =
        R"({
        "name" : "creates1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, resPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcCreateResPool startId临界值2^32-1
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_110)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolName01 = "resource_pool_test_maxid01";
    const char *ResPoolTest01 =
        R"({
        "name" : "resource_pool_test_maxid01",
        "pool_id" : 1101,
        "start_id" : 4294967294,
        "capacity" : 1,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(res_stmt, resPoolName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *resPoolName02 = "resource_pool_test_maxid02";
    const char *ResPoolTest02 =
        R"({
        "name" : "resource_pool_test_maxid02",
        "pool_id" : 1102,
        "start_id" : 4294967295,
        "capacity" : 10000,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, resPoolName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *resPoolName03 = "resource_pool_test_maxid03";
    const char *ResPoolTest03 =
        R"({
        "name" : "resource_pool_test_maxid03",
        "pool_id" : 1103,
        "start_id" : 4294967295,
        "capacity" : 0,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, resPoolName03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcCreateResPool capacity临界值2^32-1
 ******************************************************************************************/
TEST_F(ResPParCheck_01, HardWare_Offloading_001_DDL_027_111)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolName = "res_pool_test_maxcap";
    char resDef[256];
    int64_t g_resMaxSize = 4294967295;

    snprintf(resDef, sizeof(resDef),
        "{ \"name\" : \"%s\", \"pool_id\" : 0, \"start_id\" : 0, \"capacity\" : %lld, \"order\" : 0, \"alloc_type\" : 0 "
        "}",
        resPoolName, g_resMaxSize);
//适配 iot用例内存小于40M  40M=40*1024*1024*8(bit)=335544320

 
    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxResPoolShmSize=513\"");
    system("sh $TEST_HOME/tools/start.sh -f");
 
    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    ret = testGmcConnect(&conn, &res_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    ret = GmcCreateResPool(res_stmt, resDef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(res_stmt, resPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    ret = testGmcDisconnect(conn, res_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcDestroyResPool resPoolName64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_112)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolName = "desresc3333gmdb3333gmdb3333gmdb3333gmdb3333gmdb3333gmdb3333gmdb";
    const char *ResPoolTest =
        R"({
        "name" : "desresc3333gmdb3333gmdb3333gmdb3333gmdb3333gmdb3333gmdb3333gmdb",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200000,
        "order" : 0,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, resPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindResPoolToLabel resPoolName64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_113)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool_paranor_paranor_paranor_paranor_paranor_paranor_para113";
    const char *ResPoolTest =
        R"({
        "name" : "respool_paranor_paranor_paranor_paranor_paranor_paranor_para113",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "ResourceLableTestParaNorA";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResourceLableTestParaNorA",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";


    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

#if NOT_EXIST
	ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindResPoolToLabel 绑定一个vertexLabel
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_114)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool_paranor_paranor_paranor_paranor_paranor_paranor_para114";
    const char *ResPoolTest =
        R"({
        "name" : "respool_paranor_paranor_paranor_paranor_paranor_paranor_para114",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "ResourcBparanor_paranor_paranor_paranor_paranor_paranor_para114";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResourcBparanor_paranor_paranor_paranor_paranor_paranor_para114",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";


    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // GmcBindResPoolToLabel 绑定一个vertexLabel
    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindResPoolFromLabel 解绑一个vertexLabel
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_115)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    const char *LabelName = "ResourcCparanor_paranor_paranor_paranor_paranor_paranor_para115";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResourcCparanor_paranor_paranor_paranor_paranor_paranor_para115",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F4", "type":"resource", "nullable":false},
				{"name":"F5", "type":"resource", "nullable":false},
				{"name":"F6", "type":"resource", "nullable":false},
				{"name":"F7", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool_paranor_paranor_paranor_paranor_paranor_paranor_para115";
    const char *ResPoolTest =
        R"({
        "name" : "respool_paranor_paranor_paranor_paranor_paranor_paranor_para115",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";


    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GmcUnbindResPoolFromLabel 解绑一个vertexLabel
    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindExtResPool resPoolName64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_116)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool_paranor_paranor_paranor_paranor_paranor_paranor_para116";
    const char *ResPoolTest =
        R"({
        "name" : "respool_paranor_paranor_paranor_paranor_paranor_paranor_para116",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 绑定到扩展资源池
    ret = GmcBindExtResPool(res_stmt, ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindExtResPool extendedPoolNam64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_117)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolNameExternal = "extende_paranor_paranor_paranor_paranor_paranor_paranor_para117";
    const char *ResPoolExternal =
        R"({
        "name" : "extende_paranor_paranor_paranor_paranor_paranor_paranor_para117",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    const char *ResPoolName = "res_pool_this";
    const char *ResPoolTest =
        R"({
        "name" : "res_pool_this",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 绑定到扩展资源池
    ret = GmcBindExtResPool(res_stmt, ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindExternalPool resPoolName64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_118)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "unbindA_paranor_paranor_paranor_paranor_paranor_paranor_para118";
    const char *ResPoolTest =
        R"({
        "name" : "unbindA_paranor_paranor_paranor_paranor_paranor_paranor_para118",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindExtResPool(res_stmt, ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // GmcUnbindExternalPool resPoolName64字节
    ret = GmcUnbindExtResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResPool resPoolName64字节(原：GmcDumpResPool )
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_119)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *pDumpJson = NULL;

    // 63char + '\0'
    const char *ResPoolName = "paranor_paranor_paranor_paranor_paranor_paranor_paranor_dump119";
    const char *ResPoolTest =
        R"({
        "name" : "paranor_paranor_paranor_paranor_paranor_paranor_paranor_dump119",
        "pool_id" : 130,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetResPool(res_stmt, ResPoolName, &pDumpJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * // GmcSetResPoolProperty参数组合设置资源池属性
 * uint64_t tmpResIdx = 0;
 * ret = GmcSetPoolIdResource( uint64_t poolId, &tmpResIdx);
 * AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 * ret = GmcSetCountResource( uint64_t count, &tmpResIdx);
 * AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 * ret = GmcSetStartIdxResource( uint64_t startIndex, &tmpResIdx);
 * AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
 * ret = GmcSetVertexProperty(GmcStmtT *stmt, const char *propName, GMC_DATATYPE_RESOURCE, &tmpResIdx,
sizeof(tmpResIdx));

 * History 2021-07-16 set接口变更后数据类型有变动，超出数据类型长度将被自动截断
******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_120)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *ResPoolName = "respool_this221";
    const char *ResPoolTest =
        R"({
        "name" : "respool_this221",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "thislable221";
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"thislable221",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false},
				{"name":"F5", "type":"resource", "nullable":false},
				{"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

#if 1
    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t insertVal = 100;
    ret = GmcSetVertexProperty(res_stmt, "F0", GMC_DATATYPE_INT32, &insertVal, sizeof(insertVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F1", GMC_DATATYPE_INT32, &insertVal, sizeof(insertVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F2", GMC_DATATYPE_INT32, &insertVal, sizeof(insertVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t respoolId = 10000;
    uint64_t count = 10000;
    uint64_t startIndex = 299;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 10000;
    count = 99999;
    startIndex = 299;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F4", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 10000;
    count = 10000;
    startIndex = 299;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F5", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 10000;
    count = 99999;
    startIndex = 299;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F5", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 0xFFFF;
    count = 10000;
    startIndex = 0xFFFFFFFF;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F6", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(res_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * // GmcGetResIdInfo 获得资源池ID信息
 * int32_t GmcGetResIdInfo(GmcStmtT *stmt, const char* vertexName, uint64_t** resourceId, uint32_t* bufLen);
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_121)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    uint32_t bufLength = 0;
    // uint64_t* resourceId;

    const char *ResPoolTest =
        R"({
        "name" : "resource_pool_test121",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 300,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolName = "resource_pool_test121";
    const char *LabelName = "ResourceLableTest121";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"ResourceLableTest121",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false},
				{"name":"F5", "type":"resource", "nullable":false},
				{"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";
    const char *LableConfig = R"({"max_record_num":1000})";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int F0Value = 19;
    ret = GmcSetVertexProperty(res_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F1Value = 299;
    ret = GmcSetVertexProperty(res_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F2Value = 3999;
    ret = GmcSetVertexProperty(res_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t respoolId = 10000;
    uint64_t count1 = 52;
    uint64_t startIndex = 0;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count1, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t count2 = 48;
    respoolId = 10000;
    startIndex = startIndex + count1;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count2, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F4", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t count3 = 64;
    respoolId = 10000;
    startIndex = startIndex + count2;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count3, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F5", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t count4 = 38;
    respoolId = 10000;
    startIndex = startIndex + count3;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count4, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F6", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(res_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isBatch = false;
    // 获得资源池ID信息

    uint32_t bufLen = 0;
    ret = GmcGetResIdNum(res_stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(res_stmt, resourceId, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetFilter(res_stmt, "ResourceLableTest121.F0 = 19");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetFilter(res_stmt, "ResourceLableTest121.F1 = 299");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetFilter(res_stmt, "ResourceLableTest121.F2 = 3999");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(res_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResPool resPoolJson为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_122)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *pDumpJson = NULL;

    const char *ResPoolName = "respool130";
    const char *ResPoolTest =
        R"({
        "name" : "respool130",
        "pool_id" : 130,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetResPool(res_stmt, ResPoolName, &pDumpJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcCreateResPool stmt为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_123)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolName = "resource_test";
    const char *ResPoolTest =
        R"({
        "name" : "resource_test",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(NULL, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcCreateResPool resPoolName为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_124)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *ResPoolTest01 =
        R"({
        "name" : ,
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest02 =
        R"({
        "name" : NULL,
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest03 =
        R"({
        "name" : "",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcCreateResPool startId为空,负数，溢出数
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_125)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *ResPoolTest01 =
        R"({
        "name" : "RES_POOL01",
        "pool_id" : 10000,
        "start_id" : ,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest02 =
        R"({
        "name" : "RES_POOL02",
        "pool_id" : 10000,
        "start_id" : -1,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest03 =
        R"({
        "name" : "RES_POOL03",
        "pool_id" : 10000,
        "start_id" : 4294967299,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // order为1
    const char *ResPoolTest11 =
        R"({
        "name" : "RES_POOL11",
        "pool_id" : 10000,
        "start_id" : ,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest12 =
        R"({
        "name" : "RES_POOL12",
        "pool_id" : 10000,
        "start_id" : -1,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest13 =
        R"({
        "name" : "RES_POOL13",
        "pool_id" : 10000,
        "start_id" : 4294967299,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcCreateResPool capacity为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_126)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *ResPoolTest01 =
        R"({
        "name" : "res_pool_test_13400",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : ,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest02 =
        R"({
        "name" : "res_pool_test_13401",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : "",
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest11 =
        R"({
        "name" : "res_pool_test_13411",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : ,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest12 =
        R"({
        "name" : "res_pool_test_13412",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : "",
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcCreateResPool resPoolName名称超过64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_127)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *ResPoolTest01 =
        R"({
        "name" : "createst1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb_addstring1111111111111111111111111111111111111",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 9,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest02 =
        R"({
        "name" : "createst1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb1111gmdb_addstring1111111111111111111111111111111111111",
        "pool_id" : 10002,
        "start_id" : 2,
        "capacity" : 9,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcCreateResPool resPoolName名称特殊字符
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_128)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int pos;
    char ResPoolTest01[256] =
        R"({
        "name" : "q",
        "pool_id" : 10002,
        "start_id" : 2,
        "capacity" : 9,
        "order" : 0,
        "alloc_type" : 0
    })";

    for (pos = 0; g_SpecialChar[pos] != '\0'; pos++) {
        ResPoolTest01[13] = g_SpecialChar[pos];
        ret = GmcCreateResPool(res_stmt, ResPoolTest01);
        if (ResPoolTest01[13] == '\\' || ResPoolTest01[13] == '\"') {
            AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
            expect = NULL;
            ret = testGmcGetLastError(expect);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINE_COLUMN, ret);
            expect = NULL;
            ret = testGmcGetLastError(expect);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcCreateResPool resPoolName名称空格
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_129)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char ResPoolTest[256] =
        R"({
        "name" : "       ",
        "pool_id" : 129,
        "start_id" : 129,
        "capacity" : 129,
        "order" : 0,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, "       ");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcCreateResPool startId负数或溢出
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_130)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *ResPoolTest01 =
        R"({
        "name" : "RES_POOL01",
        "pool_id" : 10000,
        "start_id" : -1,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest02 =
        R"({
        "name" : "RES_POOL02",
        "pool_id" : 10000,
        "start_id" : 99999999999,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // order为1
    const char *ResPoolTest12 =
        R"({
        "name" : "RES_POOL12",
        "pool_id" : 10000,
        "start_id" : -1,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest13 =
        R"({
        "name" : "RES_POOL13",
        "pool_id" : 10000,
        "start_id" : 9999999999999999999999,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcCreateResPool capacity负数或过大
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_131)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *ResPoolTest01 =
        R"({
        "name" : "huawei_gmdb01",
        "pool_id" : 10000,
        "start_id" : -1,
        "capacity" : -99999999990,
        "order" : 0,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *ResPoolTest02 =
        R"({
        "name" : "huawei_gmdb02",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 4294967295999999999999,
        "order" : 0,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // order为1
    const char *ResPoolTest03 =
        R"({
        "name" : "huawei_gmdb03",
        "pool_id" : 10000,
        "start_id" : -1,
        "capacity" : -99999999990,
        "order" : 1,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest03);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest04 =
        R"({
        "name" : "huawei_gmdb04",
        "pool_id" : 10002,
        "start_id" : 1,
        "capacity" : 4294967295999999999999,
        "order" : 1,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest04);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcDestroyResPool stmt为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_132)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *ResPoolTest =
        R"({
        "name" : "huawei_gmdb_test",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 99,
        "order" : 1,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(NULL, "huawei_gmdb_test");
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, "huawei_gmdb_test");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //再次直接传入NULL
    GmcStmtT *tmp_stmt = res_stmt;
    tmp_stmt = NULL;
    ret = GmcDestroyResPool(tmp_stmt, "huawei_gmdb_test");
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcDestroyResPool resPoolName为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_133)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *ResPoolTest =
        R"({
        "name" : "huawei_gmdb_test02",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 99,
        "order" : 1,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, "huawei_gmdb_test02");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *ResPoolTest02 =
        R"({
        "name" : "",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 99,
        "order" : 1,
        "alloc_type" : 0
    })";
    ret = GmcDestroyResPool(res_stmt, ResPoolTest02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcDestroyResPool resPoolName名称超过64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_134)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *ResPoolTest01 =
        R"({
        "name" : "createst2222gmdb2222gmdb2222gmdb2222gmdb2222gmdb2222_toolongstring",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 99,
        "order" : 1,
        "alloc_type" : 0
    })";

    const char *ResPoolName = "createst2222gmdb2222gmdb2222gmdb2222gmdb2222gmdb2222_toolongstring";
    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(
        res_stmt, "createst2222gmdb\02222gmdb2222gmdb2222gmdb2222gmdb2222_toolongstring_toolongstring_toolongstring\n");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt,
        "createst2222gmdb\\02222gmdb2222gmdb2222gmdb2222gmdb2222_toolongstring_toolongstring_toolongstring\n");
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcDestroyResPool resPoolName名称特殊字符
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_135)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *ResPoolTest01 =
        R"({
        "name" : "!@#$%^&*)(&!@#$%^&*&)({}":<>?",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 99,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, "~`!@#$%^&*()-+={}[]\\|:;\"',.?/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int pos = 0;
    char ResPoolTest02[256] =
        R"({
        "name" : "a",
        "pool_id" : 10002,
        "start_id" : 2,
        "capacity" : 9,
        "order" : 0,
        "alloc_type" : 0
    })";

    for (pos; g_SpecialChar[pos] != '\0'; pos++) {
        ResPoolTest02[13] = g_SpecialChar[pos];
        ret = GmcDestroyResPool(res_stmt, ResPoolTest02);
        if (ResPoolTest02[13] == '\\' || ResPoolTest02[13] == '\"') {
            AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
            expect = NULL;
            ret = testGmcGetLastError(expect);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
            expect = NULL;
            ret = testGmcGetLastError(expect);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcDestroyResPool resPoolName名称空格
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_136)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *ResPoolName = "      ";
    const char *ResPoolTest01 =
        R"({
        "name" : "      ",
        "pool_id" : 10002,
        "start_id" : 2,
        "capacity" : 99,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //超过64字节
    const char *ResPoolName02 = "                                                                 ";
    ret = GmcDestroyResPool(res_stmt, ResPoolName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindResPoolToLabel stmt为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_137)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool137";
    const char *ResPoolTest =
        R"({
        "name" : "respool137",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable137";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable137",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *test_stmt = NULL;
    ret = GmcBindResPoolToLabel(test_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(NULL, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindResPoolToLabel vertexLabel为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_138)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool138";
    const char *ResPoolTest =
        R"({
        "name" : "respool138",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable138";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable138",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *testLabelName = NULL;
    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, testLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindResPoolToLabel resPoolName为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_139)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool139";
    const char *ResPoolTest =
        R"({
        "name" : "respool139",
        "pool_id" : 147,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable139";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable139",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *testResPoolName = NULL;
    ret = GmcBindResPoolToLabel(res_stmt, testResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindResPoolToLabel vertexLabel名称超过128字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_140)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool140";
    const char *ResPoolTest =
        R"({
        "name" : "respool140",
        "pool_id" : 148,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable140";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable140",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *testLabelName = "lable148_toolong_toolong_toolong_toolong_toolong_toolong_toolong_toolong";
    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, testLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindResPoolToLabel vertexLabe传入特殊字符
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_141)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool149";
    const char *ResPoolTest =
        R"({
        "name" : "respool149",
        "pool_id" : 149,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable149";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable149",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char res_special[2] = {'\0', '\0'};
    for (int pos = 0; g_SpecialChar[pos] != '\0'; pos++) {
        res_special[0] = g_SpecialChar[pos];
        res_special[1] = '\0';
        ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, res_special);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    if (ret == GMERR_UNDEFINED_TABLE) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindResPoolToLabel vertexLabe名称空格
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_142)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool148";
    const char *ResPoolTest =
        R"({
        "name" : "respool148",
        "pool_id" : 148,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable148";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable148",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *testLabelName = "                         ";
    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, testLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //超过64字节
    const char *testLabelName02 = "                                                                 ";
    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, testLabelName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindResPoolToLabel resPoolName名称超过64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_143)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool151";
    const char *ResPoolTest =
        R"({
        "name" : "respool151",
        "pool_id" : 148,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable151";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable151",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *testResPoolName = "ResPool151_toolong_toolong_toolong_toolong_toolong_toolong_toolong_toolong";
    ret = GmcBindResPoolToLabel(res_stmt, testResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindResPoolToLabel resPoolName名称特殊字符
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_144)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool151";
    const char *ResPoolTest =
        R"({
        "name" : "respool151",
        "pool_id" : 148,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable151";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable151",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char res_special[2] = {'\0', '\0'};
    for (int pos = 0; g_SpecialChar[pos] != '\0'; pos++) {
        res_special[0] = g_SpecialChar[pos];
        res_special[1] = '\0';
        ret = GmcBindResPoolToLabel(res_stmt, res_special, LabelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    if (ret == GMERR_UNDEFINED_TABLE) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindResPoolToLabel resPoolName名称空格
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_145)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool151";
    const char *ResPoolTest =
        R"({
        "name" : "respool151",
        "pool_id" : 148,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable151";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable151",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *testResPoolName = "                    ";
    ret = GmcBindResPoolToLabel(res_stmt, testResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *testResPoolName02 = "                                                                 ";
    ret = GmcBindResPoolToLabel(res_stmt, testResPoolName02, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindResPoolFromLabel stmt为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_146)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool154";
    const char *ResPoolTest =
        R"({
        "name" : "respool154",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable154";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable154",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *test_res_stmt = NULL;
    ret = GmcUnbindResPoolFromLabel(test_res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindResPoolFromLabel vertexLabel为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_147)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool155";
    const char *ResPoolTest =
        R"({
        "name" : "respool155",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable155";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable155",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_LabelName = NULL;
    ret = GmcUnbindResPoolFromLabel(res_stmt, test_LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindResPoolFromLabel vertexLabel为未创建不存在的
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_148)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool156";
    const char *ResPoolTest =
        R"({
        "name" : "respool156",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable156";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable156",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *LabelNameNoExist = "lable156_no_exist";
    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelNameNoExist);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindResPoolFromLabel vertexLabel名称特殊字符
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_149)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool157";
    const char *ResPoolTest =
        R"({
        "name" : "respool157",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "lable157";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"lable157",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char res_special[2] = {'\0', '\0'};
    for (int pos = 0; g_SpecialChar[pos] != '\0'; pos++) {
        res_special[0] = g_SpecialChar[pos];
        res_special[1] = '\0';
        ret = GmcUnbindResPoolFromLabel(res_stmt, res_special);
        AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    }
    if (ret == GMERR_UNDEFINED_TABLE) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindResPoolFromLabel vertexLabel名称空格
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_150)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *ResPoolName = "respool158";
    const char *ResPoolTest =
        R"({
        "name" : "respool158",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "label158";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"label158",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_LabelName01 = "                                                               ";
    ret = GmcUnbindResPoolFromLabel(res_stmt, test_LabelName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_LabelName02 = "                                           ";
    ret = GmcUnbindResPoolFromLabel(res_stmt, test_LabelName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindExtResPool stmt为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_151)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool159";
    const char *ResPoolTest =
        R"({
        "name" : "respool159",
        "pool_id" : 159,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended159";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended159",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *test_res_stmt = NULL;
    ret = GmcBindExtResPool(test_res_stmt, ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindExtResPool(res_stmt, ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindExtResPool resPoolName为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_152)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool160";
    const char *ResPoolTest =
        R"({
        "name" : "respool160",
        "pool_id" : 160,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended160";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended160",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolName = NULL;
    ret = GmcBindExtResPool(res_stmt, test_ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindExtResPool(res_stmt, ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindExtResPool extendedPoolName为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_153)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool161";
    const char *ResPoolTest =
        R"({
        "name" : "respool161",
        "pool_id" : 161,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended161";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended161",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolNameExternal = NULL;
    ret = GmcBindExtResPool(res_stmt, ResPoolName, test_ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindExtResPool resPoolName名称超过64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_154)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool162";
    const char *ResPoolTest =
        R"({
        "name" : "respool162",
        "pool_id" : 162,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended162";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended162",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolName = "respooll_toolong_toolong_toolong_toolong_toolong_toolong_toolong_toolong";
    ret = GmcBindExtResPool(res_stmt, test_ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindExtResPool extendedPoolName名称超过64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_155)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool163";
    const char *ResPoolTest =
        R"({
        "name" : "respool163",
        "pool_id" : 163,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended163";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended163",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolNameExternal = "respooll_toolong_toolong_toolong_toolong_toolong_toolong_toolong_toolong";
    ret = GmcBindExtResPool(res_stmt, ResPoolName, test_ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindExtResPool resPoolName名称特殊字符
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_156)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool164";
    const char *ResPoolTest =
        R"({
        "name" : "respool164",
        "pool_id" : 164,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended164";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended164",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char res_special[2] = {'\0', '\0'};
    for (int pos = 0; g_SpecialChar[pos] != '\0'; pos++) {
        res_special[0] = g_SpecialChar[pos];
        res_special[1] = '\0';
        ret = GmcBindExtResPool(res_stmt, res_special, ResPoolNameExternal);
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    }
    if (ret == GMERR_RESOURCE_POOL_ERROR) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindExtResPool resPoolName名称为空格
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_157)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool165";
    const char *ResPoolTest =
        R"({
        "name" : "respool165",
        "pool_id" : 165,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended165";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended165",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolName01 = "                           ";
    ret = GmcBindExtResPool(res_stmt, test_ResPoolName01, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolName02 = "                                                                 ";
    ret = GmcBindExtResPool(res_stmt, test_ResPoolName02, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindExtResPool extendedPoolName名称特殊字符
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_158)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool166";
    const char *ResPoolTest =
        R"({
        "name" : "respool166",
        "pool_id" : 166,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended166";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended166",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char res_special[2] = {'\0', '\0'};
    for (int pos = 0; g_SpecialChar[pos] != '\0'; pos++) {
        res_special[0] = g_SpecialChar[pos];
        res_special[1] = '\0';
        ret = GmcBindExtResPool(res_stmt, ResPoolName, res_special);
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    }
    if (ret == GMERR_RESOURCE_POOL_ERROR) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcBindExtResPool extendedPoolName名称为空格
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_159)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool167";
    const char *ResPoolTest =
        R"({
        "name" : "respool167",
        "pool_id" : 167,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended167";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended167",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolNameExternal01 = "                           ";
    ret = GmcBindExtResPool(res_stmt, ResPoolName, test_ResPoolNameExternal01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolNameExternal02 = "                                                                 ";
    ret = GmcBindExtResPool(res_stmt, ResPoolName, test_ResPoolNameExternal02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindExternalPool stmt为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_160)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool168";
    const char *ResPoolTest =
        R"({
        "name" : "respool168",
        "pool_id" : 168,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended168";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended168",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindExtResPool(res_stmt, ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *test_res_stmt = NULL;
    ret = GmcUnbindExtResPool(test_res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindExternalPool resPoolName为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_161)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool169";
    const char *ResPoolTest =
        R"({
        "name" : "respool169",
        "pool_id" : 169,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended169";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended169",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindExtResPool(res_stmt, ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolName = NULL;
    ret = GmcUnbindExtResPool(res_stmt, test_ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindExternalPool resPoolName名称超过64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_162)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool170";
    const char *ResPoolTest =
        R"({
        "name" : "respool170",
        "pool_id" : 170,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended170";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended170",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindExtResPool(res_stmt, ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolName = "respool_longstr_longstr_longstr_longstr_longstr_longstr_longstr_longstr";
    ret = GmcUnbindExtResPool(res_stmt, test_ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindExternalPool resPoolName名称特殊字符
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_163)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool170";
    const char *ResPoolTest =
        R"({
        "name" : "respool170",
        "pool_id" : 170,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended170";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended170",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindExtResPool(res_stmt, ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char res_special[2] = {'\0', '\0'};
    for (int pos = 0; g_SpecialChar[pos] != '\0'; pos++) {
        res_special[0] = g_SpecialChar[pos];
        res_special[1] = '\0';
        ret = GmcUnbindExtResPool(res_stmt, res_special);
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    }
    if (ret == GMERR_RESOURCE_POOL_ERROR) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcUnbindExtResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcUnbindExternalPool resPoolName名称空格
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_164)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;

    const char *ResPoolName = "respool170";
    const char *ResPoolTest =
        R"({
        "name" : "respool170",
        "pool_id" : 170,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolNameExternal = "res_pool_extended170";
    const char *ResPoolExternal =
        R"({
        "name" : "res_pool_extended170",
        "pool_id" : 258,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 1,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateResPool(res_stmt, ResPoolExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindExtResPool(res_stmt, ResPoolName, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolName01 = "                                                                 ";
    ret = GmcUnbindExtResPool(res_stmt, test_ResPoolName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolName02 = "                                                               ";
    ret = GmcUnbindExtResPool(res_stmt, test_ResPoolName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolNameExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResPool stmt为空
 * int32_t GmcGetResPool(GmcStmtT *stmt, const char* resPoolName, char** resPoolJson)
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_165)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *pGetJson = NULL;
    const char *ResPoolName = "respool173";
    const char *ResPoolTest =
        R"({
        "name" : "respool173",
        "pool_id" : 173,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *test_res_stmt = NULL;
    ret = GmcGetResPool(test_res_stmt, ResPoolName, &pGetJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResPool resPoolName名称超过64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_166)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *pGetJson = NULL;
    const char *ResPoolName = "respool174";
    const char *ResPoolTest =
        R"({
        "name" : "respool174",
        "pool_id" : 174,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *test_ResPoolName = "pool174_toolong_toolong_toolong_toolong_toolong_toolong_toolong_toolong";
    ret = GmcGetResPool(res_stmt, test_ResPoolName, &pGetJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResPool resPoolName名称特殊字符
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_167)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *pGetJson = NULL;
    const char *ResPoolName = "respool175";
    const char *ResPoolTest =
        R"({
        "name" : "respool175",
        "pool_id" : 175,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char res_special[2] = {'\0', '\0'};
    for (int pos = 0; g_SpecialChar[pos] != '\0'; pos++) {
        res_special[0] = g_SpecialChar[pos];
        res_special[1] = '\0';
        ret = GmcGetResPool(res_stmt, res_special, &pGetJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    }
    if (ret == GMERR_RESOURCE_POOL_ERROR) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcGetResPool(res_stmt, ResPoolName, &pGetJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResPool resPoolName名称空格
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_168)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *pGetJson = NULL;
    const char *ResPoolName = "      ";
    const char *ResPoolTest =
        R"({
        "name" :  "      ",
        "pool_id" : 176,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetResPool(res_stmt, ResPoolName, &pGetJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //超过64字节
    const char *ResPoolName02 = "                                                                 ";
    ret = GmcGetResPool(res_stmt, ResPoolName02, &pGetJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResPool resPoolJson初始化非空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_169)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    char *pGetJson = (char *)R"("test_para")";
    const char *ResPoolName = "respool177";
    const char *ResPoolTest =
        R"({
        "name" : "respool177",
        "pool_id" : 177,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetResPool(res_stmt, ResPoolName, &pGetJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcSetResPoolProperty stmt为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_170)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *ResPoolName = "respool_this222";
    const char *ResPoolTest =
        R"({
        "name" : "respool_this222",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 20000,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "thislable222";
    const char *LableConfig = R"({"max_record_num":10000})";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"thislable222",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false},
				{"name":"F5", "type":"resource", "nullable":false},
				{"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t respoolId = 65534;
    uint64_t count = 10000;
    uint64_t startIndex = 299;

    // stmt为空
    GmcStmtT *test_res_stmt = NULL;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(test_res_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcSetResPoolProperty propName名称超过64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_171)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *ResPoolName = "respool_this223";
    const char *ResPoolTest =
        R"({
        "name" : "respool_this223",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 20000,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "thislable223";
    const char *LableConfig = R"({"max_record_num":10000})";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"thislable223",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false},
				{"name":"F5", "type":"resource", "nullable":false},
				{"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t respoolId = 65534;
    uint64_t count = 10000;
    uint64_t startIndex = 299;

    // propName超过64个字节
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F333tst_F333tst_F333tst_F333tst_F333tst_F333tst_F333tst_F333tst_F333tst",
        GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcSetResPoolProperty propName名称特殊字符
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_172)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *ResPoolName = "respool_this224";
    const char *ResPoolTest =
        R"({
        "name" : "respool_this224",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "thislable224";
    const char *LableConfig = R"({"max_record_num":10000})";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"thislable224",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false},
				{"name":"F5", "type":"resource", "nullable":false},
				{"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t respoolId = 65534;
    uint64_t count = 10000;
    uint64_t startIndex = 299;

    char res_special[2] = {'\0', '\0'};
    for (int pos = 0; g_SpecialChar[pos] != '\0'; pos++) {
        res_special[0] = g_SpecialChar[pos];
        res_special[1] = '\0';
        // propName名称特殊字符
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(count, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(res_stmt, res_special, GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcSetResPoolProperty poolId, count, startIndex异常参数测试
 * History 2021-07-16 set接口变更后数据类型有变动，超出数据类型长度将被自动截断
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_173)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *ResPoolName = "respool_this225";
    const char *ResPoolTest =
        R"({
        "name" : "respool_this225",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "thislable225";
    const char *LableConfig = R"({"max_record_num":10000})";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"thislable225",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false},
				{"name":"F5", "type":"resource", "nullable":false},
				{"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t insertVal = 100;
    ret = GmcSetVertexProperty(res_stmt, "F0", GMC_DATATYPE_INT32, &insertVal, sizeof(insertVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F1", GMC_DATATYPE_INT32, &insertVal, sizeof(insertVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F2", GMC_DATATYPE_INT32, &insertVal, sizeof(insertVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t respoolId = 65537;
    uint64_t count = 10000;
    uint64_t startIndex = 299;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 10000;
    count = 99999999999;
    startIndex = 299;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 65534;
    count = 99999999999;
    startIndex = 299;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F4", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 65535;
    count = 10000;
    startIndex = 29999999999;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F5", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 65535;
    count = 999999999;
    startIndex = 299;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F5", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 0xFFFF;
    count = 99999999999;
    startIndex = 0xFFFFFFFF;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F6", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(res_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcSetResPoolProperty 名称空格
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_174)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    const char *ResPoolName = "respool_this226";
    const char *ResPoolTest =
        R"({
        "name" : "respool_this226",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *LabelName = "thislable226";
    const char *LableConfig = R"({"max_record_num":10000})";
    const char *LabelSchema =
        R"([{
        "type":"record",
        "name":"thislable226",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":false},
                {"name":"F3", "type":"resource", "nullable":false},
				{"name":"F4", "type":"resource", "nullable":false},
				{"name":"F5", "type":"resource", "nullable":false},
				{"name":"F6", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"T35_K0",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t insertVal = 100;
    ret = GmcSetVertexProperty(res_stmt, "F0", GMC_DATATYPE_INT32, &insertVal, sizeof(insertVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F1", GMC_DATATYPE_INT32, &insertVal, sizeof(insertVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F2", GMC_DATATYPE_INT32, &insertVal, sizeof(insertVal));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t respoolId = 65534;
    uint64_t count = 10000;
    uint64_t startIndex = 299;

    // propName空格
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "       ", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // propName空格超过64个字节
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "                                                                 ",
        GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 65534;
    count = 99999;
    startIndex = 299;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F4", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 65535;
    count = 10000;
    startIndex = 299;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F5", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 65535;
    count = 99999;
    startIndex = 299;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F5", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    respoolId = 0xFFFF;
    count = 10000;
    startIndex = 0xFFFFFFFF;
    tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F6", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(res_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResIdInfo stmt为空
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_175)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    uint32_t bufLength = 0;
    // uint64_t* resourceId;

    const char *ResPoolTest =
        R"({
        "name" : "resource_pool_test227",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolName = "resource_pool_test227";
    const char *LabelName = "ResourceLableTest227";
    const char *LabelSchema =

        R"([{
	"type":"record",
	"name":"ResourceLableTest227",
	"fields":
		[
			{"name":"F0", "type":"int32", "nullable":false},
			{"name":"F1", "type":"int32", "nullable":false},
			{"name":"F2", "type":"int32", "nullable":false},
			{"name":"F3", "type":"resource", "nullable":false}
		],
	"keys":
		[
			{
				"name":"T35_K0",
				"fields":["F0"],
				"index":{"type":"primary"},
				"constraints":{"unique":true}
			}
		]
	}])";

    const char *LableConfig = R"({"max_record_num":1000})";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int F0Value = 19;
    ret = GmcSetVertexProperty(res_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F1Value = 299;
    ret = GmcSetVertexProperty(res_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F2Value = 3999;
    ret = GmcSetVertexProperty(res_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint64_t respoolId = 10000;
    uint64_t count = 150;
    uint64_t startIndex = 0;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(res_stmt, "F3", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(res_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // stmt为空
    GmcStmtT *test_res_stmt = NULL;
    bool isBatch = false;
    uint32_t bufLen = 0;
    ret = GmcGetResIdNum(test_res_stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_NULL_VALUE_NOT_ALLOWED);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t resourceId[bufLen] = {0};
    ret = GmcGetResIdInfo(test_res_stmt, resourceId, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t ResBufLen = 0;
    ret = GmcGetResIdNum(res_stmt, &ResBufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetResIdInfo(res_stmt, resourceId, &ResBufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, ResBufLen);
    if (ret != GMERR_OK) {
        expect = NULL;
        ret = testGmcGetLastError(expect);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetFilter(res_stmt, "ResourceLableTest227.F0 = 19");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetFilter(res_stmt, "ResourceLableTest227.F1 = 299");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetFilter(res_stmt, "ResourceLableTest227.F2 = 3999");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(res_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResIdInfo LabelName传入NULL
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_176)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    uint32_t bufLength = 0;
    // uint64_t* resourceId;

    const char *ResPoolTest =
        R"({
        "name" : "resource_pool_test228",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolName = "resource_pool_test228";
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *LabelName = "ResourceLableTest228";
    const char *LabelSchema =
        R"([{
	"type":"record",
	"name":"ResourceLableTest228",
	"fields":
		[
			{"name":"F0", "type":"int32", "nullable":false},
			{"name":"F1", "type":"int32", "nullable":false},
			{"name":"F2", "type":"int32", "nullable":false},
			{"name":"F3", "type":"resource", "nullable":false}
		],
	"keys":
		[
			{
				"name":"T35_K0",
				"fields":["F0"],
				"index":{"type":"primary"},
				"constraints":{"unique":true}
			}
		]
	}])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //传入NULL
    const char *isBatch = NULL;
    uint32_t bufLen = 0;
    ret = GmcGetResIdNum(res_stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(res_stmt, resourceId, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResIdInfo resourceId传入NULL
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_177)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    uint32_t bufLength = 0;
    // uint64_t* resourceId;

    const char *ResPoolTest =
        R"({
        "name" : "resource_pool_test229",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolName = "resource_pool_test229";
    const char *LableConfig = R"({"max_record_num":1000})";
    const char *LabelName = "ResourceLableTest229";
    const char *LabelSchema =
        R"([{
	"type":"record",
	"name":"ResourceLableTest229",
	"fields":
		[
			{"name":"F0", "type":"int32", "nullable":false},
			{"name":"F1", "type":"int32", "nullable":false},
			{"name":"F2", "type":"int32", "nullable":false},
			{"name":"F3", "type":"resource", "nullable":false}
		],
	"keys":
		[
			{
				"name":"T35_K0",
				"fields":["F0"],
				"index":{"type":"primary"},
				"constraints":{"unique":true}
			}
		]
	}])";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isBatch = false;
    uint32_t bufLen = 0;
    ret = GmcGetResIdNum(res_stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(res_stmt, NULL, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResIdInfo bufLength传入NULL
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_178)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    uint32_t bufLength = 0;
    // uint64_t* resourceId;

    const char *ResPoolTest =
        R"({
        "name" : "resource_pool_test230",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolName = "resource_pool_test230";
    const char *LabelName = "ResourceLableTest230";
    const char *LabelSchema =
        R"([{
	"type":"record",
	"name":"ResourceLableTest230",
	"fields":
		[
			{"name":"F0", "type":"int32", "nullable":false},
			{"name":"F1", "type":"int32", "nullable":false},
			{"name":"F2", "type":"int32", "nullable":false},
			{"name":"F3", "type":"resource", "nullable":false}
		],
	"keys":
		[
			{
				"name":"T35_K0",
				"fields":["F0"],
				"index":{"type":"primary"},
				"constraints":{"unique":true}
			}
		]
	}])";

    const char *LableConfig = R"({"max_record_num":1000})";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isBatch = false;
    uint32_t bufLen = 0;
    ret = GmcGetResIdNum(res_stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(res_stmt, resourceId, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResIdInfo LabelName特殊字符
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_179)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    uint32_t bufLength = 0;
    // uint64_t* resourceId;

    const char *ResPoolTest =
        R"({
        "name" : "resource_pool_test231",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolName = "resource_pool_test231";
    const char *LabelName = "ResourceLableTest231";
    const char *LabelSchema =
        R"([{
	"type":"record",
	"name":"ResourceLableTest231",
	"fields":
		[
			{"name":"F0", "type":"int32", "nullable":false},
			{"name":"F1", "type":"int32", "nullable":false},
			{"name":"F2", "type":"int32", "nullable":false},
			{"name":"F3", "type":"resource", "nullable":false}
		],
	"keys":
		[
			{
				"name":"T35_K0",
				"fields":["F0"],
				"index":{"type":"primary"},
				"constraints":{"unique":true}
			}
		]
	}])";

    const char *LableConfig = R"({"max_record_num":1000})";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *isBatch = "~`!@#$%^&*()-+={}[]\\|:;\"',.?/";
    uint32_t bufLen = 0;
    ret = GmcGetResIdNum(res_stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(res_stmt, resourceId, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResIdInfo LabelName超过64字节
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_180)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    uint32_t bufLength = 0;
    // uint64_t* resourceId;

    const char *ResPoolTest =
        R"({
        "name" : "resource_pool_test232",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolName = "resource_pool_test232";
    const char *LabelName = "ResourceLableTest232";
    const char *LabelSchema =
        R"([{
	"type":"record",
	"name":"ResourceLableTest232",
	"fields":
		[
			{"name":"F0", "type":"int32", "nullable":false},
			{"name":"F1", "type":"int32", "nullable":false},
			{"name":"F2", "type":"int32", "nullable":false},
			{"name":"F3", "type":"resource", "nullable":false}
		],
	"keys":
		[
			{
				"name":"T35_K0",
				"fields":["F0"],
				"index":{"type":"primary"},
				"constraints":{"unique":true}
			}
		]
	}])";

    const char *LableConfig = R"({"max_record_num":1000})";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *isBatch = "toolong_toolong_toolong_toolong_toolong_toolong_toolong_toolong_toolong_";
    uint32_t bufLen = 0;
    ret = GmcGetResIdNum(res_stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(res_stmt, resourceId, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************************
 * //GmcGetResIdInfo LabelName名称为空格
 ******************************************************************************************/
TEST_F(ResPParCheck, HardWare_Offloading_001_DDL_027_181)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    void *VertexLabel = NULL;
    uint32_t bufLength = 0;
    // uint64_t* resourceId;

    const char *ResPoolTest =
        R"({
        "name" : "resource_pool_test233",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";
    const char *ResPoolName = "resource_pool_test233";
    const char *LabelName = "ResourceLableTest233";
    const char *LabelSchema =
        R"([{
	"type":"record",
	"name":"ResourceLableTest233",
	"fields":
		[
			{"name":"F0", "type":"int32", "nullable":false},
			{"name":"F1", "type":"int32", "nullable":false},
			{"name":"F2", "type":"int32", "nullable":false},
			{"name":"F3", "type":"resource", "nullable":false}
		],
	"keys":
		[
			{
				"name":"T35_K0",
				"fields":["F0"],
				"index":{"type":"primary"},
				"constraints":{"unique":true}
			}
		]
	}])";

    const char *LableConfig = R"({"max_record_num":1000})";

    ret = GmcCreateResPool(res_stmt, ResPoolTest);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(res_stmt, LabelSchema, LableConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcBindResPoolToLabel(res_stmt, ResPoolName, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(res_stmt, LabelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *isBatch = "    ";
    uint32_t bufLen = 0;
    ret = GmcGetResIdNum(res_stmt, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t resourceId[bufLen];
    ret = GmcGetResIdInfo(res_stmt, resourceId, &bufLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, ret);
    expect = NULL;
    ret = testGmcGetLastError(expect);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(res_stmt, ResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(res_stmt, LabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
