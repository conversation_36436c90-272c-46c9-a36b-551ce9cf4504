/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: 资源池规格测试
 * Author: guopanpan
 * Create: 2021-04-05
 * History:
 */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <errno.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "../../common/hash_util.h"

using namespace std;

#define TEST_ERROR(log, args...)                                               \
    do {                                                                       \
        fprintf(stdout, "Error: %s:%d " log "\n", __FILE__, __LINE__, ##args); \
    } while (0)

#define CHECK_AND_BREAK(ret, log, args...)                                                             \
    if ((ret) != GMERR_OK) {                                                                           \
        fprintf(stdout, "Error: %s:%d " log " failed, ret = %d\n", __FILE__, __LINE__, ##args, (ret)); \
        break;                                                                                         \
    }

// 通用全局变量
GmcConnT *gConnection;
GmcStmtT *gStmt;
static const char *gLabelName = "ResourceLable";
static const char *gLabelConfig = R"({"max_record_num":1000})";
static const char *gLabelConfigCompatible = R"({"max_record_num":1000,  "auto_increment":10000})";
static const char *gLabelSchemaJson =
    R"([{
        "type":"record",
        "name":"ResourceLable",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"resource", "nullable":false},
                {"name":"F2", "type":"int32", "nullable":true}
            ],
        "keys":
            [
                {
                    "node":"ResourceLable",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

// 资源池全局变量
static const char *gResPoolName = "ResourcePool";
static const uint64_t gResPoolId = 10000;
static const char *gResPoolConfigJson =
    R"({
        "name" : "ResourcePool",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

static const char *gExtendResPoolName = "ExtendResourcePool";
static const uint64_t gExtendResPoolId = 10001;
static const char *gExtendResPoolConfigJson =
    R"({
        "name" : "ExtendResourcePool",
        "pool_id" : 10001,
        "start_id" : 10,
        "capacity" : 1000,
        "order" : 1,
        "alloc_type" : 0
    })";

class ResPSpe : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcConnect(&gConnection, &gStmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfig);
        ret = (ret == GMERR_DUPLICATE_OBJECT ? GMERR_OK : ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    static void TearDownTestCase()
    {
        int ret = GmcDropVertexLabel(gStmt, gLabelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(gConnection, gStmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

    virtual void SetUp()
    {
        char errorMsg1[128] = {};
        (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
        AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

        char errorMsg2[128] = {};
        (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_RESOURCE_POOL_ALREADY_BOUND);
        AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

        AW_CHECK_LOG_BEGIN();
    }

    virtual void TearDown()
    {
        AW_CHECK_LOG_END();
    }
};

class ResPSpe_01 : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
    }

    static void TearDownTestCase()
    {   
        //恢复配置文件
        RecoverCfg();
    }

    virtual void SetUp()
    {}

    virtual void TearDown()
    {}
};

/********************************* schema规格 *********************************/
// 创建根节点带资源字段的tree表
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableIllegalRes",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":false}
                ],
            "keys":
                [
                    {
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    int ret = GmcCreateVertexLabel(gStmt, labelSchema, gLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, "ResourceLableIllegalRes");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建1张tree表，该表非根节点带资源字段
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableInvalidRes",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":false},
                    {"name":"G1", "type:"record", "fixed_array":true, "size": 1024, "fields": [
                        {"name":"H0", "type":"int32", "nullable":false},
                        {"name":"H1", "type":"resource", "nullable":false}
                    ]}
                ],
            "keys":
                [
                    {
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    int ret = GmcCreateVertexLabel(gStmt, labelSchema, gLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建1张tree表，该表根节点下带多个资源字段
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableMultiRes",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":false},
                    {"name":"F2", "type":"resource", "nullable":false},
                    {"name":"F3", "type":"resource", "nullable":false},
                    {"name":"F4", "type":"resource", "nullable":false}
                ],
            "keys":
                [
                    {
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    int ret = GmcCreateVertexLabel(gStmt, labelSchema, gLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, "ResourceLableMultiRes");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建1张tree表，该表根节点下资源字段数量超过上限
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableMultiRes",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":false},
                    {"name":"F2", "type":"resource", "nullable":false},
                    {"name":"F3", "type":"resource", "nullable":false},
                    {"name":"F4", "type":"resource", "nullable":false}，
                    {"name":"F5", "type":"resource", "nullable":false}
                ],
            "keys":
                [
                    {
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    int ret = GmcCreateVertexLabel(gStmt, labelSchema, gLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    // expect = "Invalid Json content. Invalid vertex label json.";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 存在一个资源字段和另一个auto_increment属性为true的字段
//自增列配置适配V3格式:config json里面定义auto_increment, schema json定义bool值，表示是否是自增列-xuxinxin(2021.08.06)
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableAutoIncrementField",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":false},
                    {"name":"F2", "type":"uint32", "auto_increment":true}
                ],
            "keys":
                [
                    {
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    int ret = GmcCreateVertexLabel(gStmt, labelSchema, gLabelConfigCompatible);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(gStmt, "ResourceLableAutoIncrementField");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源字段的auto_increment属性为true
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableutoIncrementTrue",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "auto_increment":true}
                ],
            "keys":
                [
                    {
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    int ret = GmcCreateVertexLabel(gStmt, labelSchema, gLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    // expect = "The type of auto increment F1 is not match.";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建1张tree表，该表带资源字段，且该资源字段设置nullable属性值为false
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableNullableFalse",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":false}
                ],
            "keys":
                [
                    {
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    int ret = GmcCreateVertexLabel(gStmt, labelSchema, gLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, "ResourceLableNullableFalse");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建1张tree表，该表带资源字段，且该资源字段设置nullable属性值为true
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableNullableTrue",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "nullable":true}
                ],
            "keys":
                [
                    {
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    int ret = GmcCreateVertexLabel(gStmt, labelSchema, gLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    // expect = "Property F1 should not be null because its type.";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建1张tree表，该表带资源字段，且该资源字段设置default值 
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int ret;
    const char *labelSchema =
        R"([{
            "type":"record",
            "name":"ResourceLableDefaultVal",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "default":false}
                ],
            "keys":
                [
                    {
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ret = GmcCreateVertexLabel(gStmt, labelSchema, gLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    // expect = "Property F1 should not be null because its type.";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *labelSchemaAdd =
        R"([{
            "type":"record",
            "name":"ResourceLableDefaultValAdd",
            "fields":
                [
                    {"name":"F0", "type":"int32", "nullable":false},
                    {"name":"F1", "type":"resource", "default":1, "nullable":false}
                ],
            "keys":
                [
                    {
                        "name":"PK",
                        "fields":["F0"],
                        "index":{"type":"primary"},
                        "constraints":{"unique":true}
                    }
                ]
            }])";

    ret = GmcCreateVertexLabel(gStmt, labelSchemaAdd, gLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    // expect = "Property F1 should not be null because its type.";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/******************************* 资源池配置规格 *******************************/
// 资源池名称为空字符 ""
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "",
            "pool_id" : 10000,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // expect = "The length of name should be in the range : (0, 64].";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池名称不赋值 (非法json文件)
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : ,
            "pool_id" : 10000,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    // expect = "Invalid Json content. The resource pool json is wrong.";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池名称长度为MAX_RES_POOL_NAME_LEN（64）
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolConfigJson =
        R"({
            "name" : "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaab",
            "pool_id" : 10000,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaab");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池名称长度大于MAX_RES_POOL_NAME_LEN（64）
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaabc",
            "pool_id" : 10000,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // expect = "The length of name should be in the range : (0, 64].";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池名称使用特殊字符
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_080)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "?#()$%\",
            "pool_id" : 10000,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    // expect = "Invalid Json content. The resource pool json is wrong.";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池名称相同
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_081)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ALREADY_EXIST);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10000,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, gResPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ALREADY_EXIST, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池pool id为负数
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_082)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : -8,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(ret, GMERR_INVALID_PROPERTY);
    // expect = "The value of pool_id should be in the range : [0, 4294967295].";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池pool id为0
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_083)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10000,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";

    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池pool id等于2^16-1 (uint16_t的最大值: 65535 )
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_084)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 65535,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池pool id大于2^16-1 (uint16_t的最大值: 65535 )
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_085)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PARAMETER_VALUE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 65536,
            "start_id" : 0,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池容量为负数
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_086)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10000,
            "start_id" : 0,
            "capacity" : -8,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // expect = "The value of capacity should be in the range : [0, 4294967295].";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池容量为0
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_087)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10000,
            "start_id" : 0,
            "capacity" : 0,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池容量等于2^32-1 (uint32_t的最大值: 4294967295 )
// FIXME 遗留问题: 资源池按占用内存的总量计算，最大占用50M， 对应数量4亿左右, 后续迭代需增加具体有效规格
TEST_F(ResPSpe_01, HardWare_Offloading_001_DDL_027_088)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char resDef[256];
    int64_t g_resMaxSize = 4294967295;

    snprintf(resDef, sizeof(resDef),
        "{ \"name\" : \"ResourcePool\", \"pool_id\" : 0, \"start_id\" : 0, \"capacity\" : %lld, \"order\" : 0, "
        "\"alloc_type\" : 0 }",
        g_resMaxSize);

    system("sh $TEST_HOME/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxResPoolShmSize=513\"");
    system("sh $TEST_HOME/tools/start.sh -f");

    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = testGmcConnect(&gConnection, &gStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(gStmt, gLabelSchemaJson, gLabelConfig);
    ret = (ret == GMERR_DUPLICATE_OBJECT ? GMERR_OK : ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDestroyResPool(gStmt, "ResourcePool");
    ret = GmcCreateResPool(gStmt, resDef);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, "ResourcePool");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(gStmt, gLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(gConnection, gStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg();
    testEnvClean();
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池容量大于2^32-1 (uint32_t的最大值: 4294967295 )
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_089)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10001,
            "start_id" : 0,
            "capacity" : 4294967296,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // expect = "The value of capacity should be in the range : [0, 4294967295].";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池起始id为负数
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_090)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10000,
            "start_id" : -10,
            "capacity" : 200,
            "order" : 0,
            "alloc_type" : 0
        })";

    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    // expect = "The value of start_id should be in the range : [0, 4294967295].";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池 start_id + capacity == 2^32-1 (uint32_t的最大值: 4294967295 )
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_091)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10000,
            "start_id" : 4294967294,
            "capacity" : 1,
            "order" : 0,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 资源池 start_id + capacity > 2^32-1 (uint32_t的最大值: 4294967295 )
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_092)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10000,
        "start_id" : 4294967294,
        "capacity" : 2,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10001,
        "start_id" : 4294967294,
        "capacity" : 10,
        "order" : 0,
        "alloc_type" : 0
        })";
    ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建资源池，指定资源池order的值在0、1之外取其它非法整形值
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_093)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10000,
            "start_id" : 0,
            "capacity" : 42949672,
            "order" : 3,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    // expect = "The value of capacity should be in the range : [0, 4294967295].";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 创建资源池，指定资源池order的值在0、1之外取其它字符串值
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_094)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolConfigJson =
        R"({
            "name" : "ResourcePool",
            "pool_id" : 10000,
            "start_id" : 0,
            "capacity" : 335544320,
            "order" : invalid,
            "alloc_type" : 0
        })";
    int ret = GmcCreateResPool(gStmt, resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_JSON_CONTENT, ret);
    // expect = "Invalid Json content. The resource pool json is wrong.";
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/********************************* 资源池拓展 *********************************/
// 绑定循环(cycle)分配方式的资源池到循环(cycle)分配的资源池
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_095)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    const char *extendResPoolConfigJson =
        R"({
        "name" : "ExtendResourcePool",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    int ret;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, extendResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, gExtendResPoolName);
        CHECK_AND_BREAK(ret, "bind extend res pool");
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gExtendResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 绑定循环(cycle)分配方式的资源池到顺序(sequence)分配的资源池
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_096)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    const char *extendResPoolConfigJson =
        R"({
        "name" : "ExtendResourcePool",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";

    int ret;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, extendResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, gExtendResPoolName);
        CHECK_AND_BREAK(ret, "bind extend res pool");
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gExtendResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 绑定顺序(sequence)分配方式的资源池到循环(cycle)分配的资源池
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_097)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";

    const char *extendResPoolConfigJson =
        R"({
        "name" : "ExtendResourcePool",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 0,
        "alloc_type" : 0
    })";

    int ret;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, extendResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, gExtendResPoolName);
        CHECK_AND_BREAK(ret, "bind extend res pool");
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gExtendResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 绑定顺序(sequence)分配方式的资源池到顺序分配(sequence)的资源池
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_098)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";

    const char *extendResPoolConfigJson =
        R"({
        "name" : "ExtendResourcePool",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 200,
        "order" : 1,
        "alloc_type" : 0
    })";

    int ret;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, extendResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, gExtendResPoolName);
        CHECK_AND_BREAK(ret, "bind extend res pool");
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gExtendResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对资源字段进行插入删除
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_099)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret;
    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, gResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, gExtendResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, gExtendResPoolName);
        CHECK_AND_BREAK(ret, "bind extend res pool");

        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 10;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");

        // uint64_t* resIdInfo;
        // uint32_t resIdInfoLen;
        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        // TEST_ERROR("resVal=%lu, poolid=%lu, count=%lu, startIndex=%lu", resIdInfo[i], tmpPoolId, tmpCount,
        // tmpStartIndex);

        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 读数据时该接口最后一个参数传NULL
        ret = GmcSetIndexKeyName(gStmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "alloc and get vertex");
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(isFinish, false);
        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gExtendResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 对资源字段进行修改
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_100)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    void *vertexLabel = NULL;
    int ret;
    // 创建、绑定资源池
    do {
        ret = GmcCreateResPool(gStmt, gResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, gExtendResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, gExtendResPoolName);
        CHECK_AND_BREAK(ret, "bind extend res pool");

        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 10;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        ret = GmcSetIndexKeyName(gStmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "alloc and get vertex");
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(isFinish, false);

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 更新资源字段 （不支持, 预期失败）
    do {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        int32_t F0Value = 0;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set index key value");
        ret = GmcSetIndexKeyName(gStmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        CHECK_AND_BREAK(ret, "alloc and get vertex");

        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set index key value");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(2, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetIndexKeyName(gStmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
        // expect = "Feature is not supported. Resource property can not modify.";
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    } while (0);

    // 删除数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gExtendResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 绑定资源池至不含资源字段的label
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_101)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *labelSchemaJson =
        R"([{
        "type":"record",
        "name":"NotResourceLable",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false}
            ],
        "keys":
            [
                {
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";

    int ret = GmcCreateVertexLabel(gStmt, labelSchemaJson, gLabelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateResPool(gStmt, gResPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(gStmt, gResPoolName, "NotResourceLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(gStmt, "NotResourceLable");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 单次申请等于上限的资源索引数量 65535 (0xffff)
// HISTORY 2021-09-27 单词申请资源索引的数量上限变更为512 (陈德辰)
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_102)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 100000,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 1;
    uint64_t resCount = 512;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 读数据时该接口最后一个参数传NULL
        ret = GmcSetIndexKeyName(gStmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "alloc and get vertex");
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(isFinish, false);

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 单次申请超过上限的资源索引数量 65535 (0xffff)
// HISTORY 2021-09-27 单词申请资源索引的数量上限变更为512 (陈德辰)
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_103)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolConfigJson =
        R"({
        "name" : "ResourcePool",
        "pool_id" : 10000,
        "start_id" : 0,
        "capacity" : 100000,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, resPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 1;
    uint64_t resCount = 513;
    do {
        int32_t F0Value = 0;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(gStmt);
        ret = (ret == GMERR_RESOURCE_POOL_ERROR ? GMERR_OK : FAILED);
        CHECK_AND_BREAK(ret, "insert vertex");
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取并校验数据
    bool isNull;
    int32_t PKValue = 0;
    ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(gStmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(gStmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool isFinish = true;
    ret = GmcFetch(gStmt, &isFinish);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(isFinish, true);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 绑定多个资源池到一个资源池上
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_104)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *extendResPoolName1 = "ExtendResourcePool1";
    const char *extendResPoolConfigJson1 =
        R"({
        "name" : "ExtendResourcePool1",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";

    const char *extendResPoolName2 = "ExtendResourcePool2";
    const char *extendResPoolConfigJson2 =
        R"({
        "name" : "ExtendResourcePool2",
        "pool_id" : 10002,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, gResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, extendResPoolConfigJson1);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);

        ret = GmcBindExtResPool(gStmt, gResPoolName, extendResPoolName1);
        CHECK_AND_BREAK(ret, "bind extend res pool");
        ret = GmcCreateResPool(gStmt, extendResPoolConfigJson2);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, extendResPoolName2);
        ret = (ret == GMERR_RESOURCE_POOL_ALREADY_BOUND ? GMERR_OK : ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        CHECK_AND_BREAK(ret, "bind extend res pool");
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 10;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");

        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        // TEST_ERROR("resVal=%lu, poolid=%lu, count=%lu, startIndex=%lu", resIdInfo[i], tmpPoolId, tmpCount,
        // tmpStartIndex);

        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 读数据时该接口最后一个参数传NULL
        ret = GmcSetIndexKeyName(gStmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "alloc and get vertex");
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(isFinish, false);

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, extendResPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, extendResPoolName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 绑定一个资源池到多个资源池上
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_105)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolName1 = "ResourcePool1";
    const char *resPoolConfigJson1 =
        R"({
        "name" : "ResourcePool1",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";

    const char *resPoolName2 = "ResourcePool2";
    const char *resPoolConfigJson2 =
        R"({
        "name" : "ResourcePool2",
        "pool_id" : 10002,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";

    const char *resPoolName3 = "ResourcePool3";
    const char *resPoolConfigJson3 =
        R"({
        "name" : "ResourcePool3",
        "pool_id" : 10003,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, gResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, resPoolConfigJson1);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, resPoolConfigJson2);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, resPoolConfigJson3);
        CHECK_AND_BREAK(ret, "create res pool");

        ret = GmcBindExtResPool(gStmt, gResPoolName, resPoolName3);
        CHECK_AND_BREAK(ret, "bind extend res pool");
        ret = GmcBindExtResPool(gStmt, resPoolName1, resPoolName3);
        CHECK_AND_BREAK(ret, "bind extend res pool");
        ret = GmcBindExtResPool(gStmt, resPoolName2, resPoolName3);
        CHECK_AND_BREAK(ret, "bind extend res pool");

        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 10;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");

        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        // TEST_ERROR("resVal=%lu, poolid=%lu, count=%lu, startIndex=%lu", resIdInfo[i], tmpPoolId, tmpCount,
        // tmpStartIndex);
        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 读数据时该接口最后一个参数传NULL
        ret = GmcSetIndexKeyName(gStmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "alloc and get vertex");
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(isFinish, false);

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, resPoolName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, resPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, resPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, resPoolName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, resPoolName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 为拓展资源池绑定一个拓展资源池
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_106)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *resPoolName1 = "ResourcePool1";
    const char *resPoolConfigJson1 =
        R"({
        "name" : "ResourcePool1",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";

    const char *resPoolName2 = "ResourcePool2";
    const char *resPoolConfigJson2 =
        R"({
        "name" : "ResourcePool2",
        "pool_id" : 10002,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";

    const char *resPoolName3 = "ResourcePool3";
    const char *resPoolConfigJson3 =
        R"({
        "name" : "ResourcePool3",
        "pool_id" : 10003,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, gResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, resPoolConfigJson1);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, resPoolName1);
        CHECK_AND_BREAK(ret, "bind extend res pool");
        ret = GmcCreateResPool(gStmt, resPoolConfigJson2);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, resPoolName1, resPoolName2);
        CHECK_AND_BREAK(ret, "bind extend res pool");
        ret = GmcCreateResPool(gStmt, resPoolConfigJson3);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, resPoolName2, resPoolName3);
        CHECK_AND_BREAK(ret, "bind extend res pool");

        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 80;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");

        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        // TEST_ERROR("resVal=%lu, poolid=%lu, count=%lu, startIndex=%lu", resIdInfo[i], tmpPoolId, tmpCount,
        // tmpStartIndex);

        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 读数据时该接口最后一个参数传NULL
        ret = GmcSetIndexKeyName(gStmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "alloc and get vertex");
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(isFinish, false);

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, resPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, resPoolName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, resPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, resPoolName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, resPoolName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 绑定自己作为自己的拓展资源池
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_107)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, gResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = GmcBindExtResPool(gStmt, gResPoolName, gResPoolName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 80;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");

        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        // TEST_ERROR("resVal=%lu, poolid=%lu, count=%lu, startIndex=%lu", resIdInfo[i], tmpPoolId, tmpCount,
        // tmpStartIndex);

        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        // 读数据时该接口最后一个参数传NULL
        ret = GmcSetIndexKeyName(gStmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "alloc and get vertex");
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(isFinish, false);

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 将资源池绑定成一个环, 即 A -> B -> C -> A
TEST_F(ResPSpe, HardWare_Offloading_001_DDL_027_108)
{
    AW_FUN_Log(LOG_STEP, "test start.");   
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    const char *resPoolName1 = "ResourcePool1";
    const char *resPoolConfigJson1 =
        R"({
        "name" : "ResourcePool1",
        "pool_id" : 10001,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";

    const char *resPoolName2 = "ResourcePool2";
    const char *resPoolConfigJson2 =
        R"({
        "name" : "ResourcePool2",
        "pool_id" : 10002,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";

    const char *resPoolName3 = "ResourcePool3";
    const char *resPoolConfigJson3 =
        R"({
        "name" : "ResourcePool3",
        "pool_id" : 10003,
        "start_id" : 0,
        "capacity" : 20,
        "order" : 0,
        "alloc_type" : 0
    })";
    int ret;

    // 创建、绑定资源池
    void *vertexLabel = NULL;
    do {
        ret = GmcCreateResPool(gStmt, gResPoolConfigJson);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcCreateResPool(gStmt, resPoolConfigJson1);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, gResPoolName, resPoolName1);
        CHECK_AND_BREAK(ret, "bind extend res pool");
        ret = GmcCreateResPool(gStmt, resPoolConfigJson2);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, resPoolName1, resPoolName2);
        CHECK_AND_BREAK(ret, "bind extend res pool");
        ret = GmcCreateResPool(gStmt, resPoolConfigJson3);
        CHECK_AND_BREAK(ret, "create res pool");
        ret = GmcBindExtResPool(gStmt, resPoolName2, resPoolName3);
        CHECK_AND_BREAK(ret, "bind extend res pool");
        ret = GmcBindExtResPool(gStmt, resPoolName3, gResPoolName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_ERROR, ret);
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBindResPoolToLabel(gStmt, gResPoolName, gLabelName);
        CHECK_AND_BREAK(ret, "bind res pool \"%s\" to label \"%s\"", gResPoolName, gLabelName);
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_INSERT);
        CHECK_AND_BREAK(ret, "open vertex label \"%s\"", gLabelName);
    } while (0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写入数据
    uint64_t dataNum = 10;
    uint64_t resCount = 1;
    for (int32_t i = 0; ret == GMERR_OK && i < dataNum; i++) {
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(gStmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        uint64_t tmpResIdx = 0;
        ret = GmcSetPoolIdResource(AUTO_POOL_ID, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetCountResource(resCount, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetStartIdxResource(AUTO_START_IDX, &tmpResIdx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(gStmt, "F1", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));

        CHECK_AND_BREAK(ret, "set res pool property");
        ret = GmcSetVertexProperty(gStmt, "F2", GMC_DATATYPE_INT32, &F0Value, sizeof(F0Value));
        CHECK_AND_BREAK(ret, "set vertex property");
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "insert vertex");
        continue;
        uint32_t bufLen;
        int ret = GmcGetResIdNum(gStmt, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        uint64_t resIdInfo[bufLen];
        ret = GmcGetResIdInfo(gStmt, resIdInfo, &bufLen);
        AW_MACRO_EXPECT_EQ_INT(ret, GMERR_OK);
        CHECK_AND_BREAK(ret, "get res id info");
        if (bufLen != 1) {
            TEST_ERROR("resIdInfoLen is wrong when index = %d, expect 1, actual %u", i, bufLen);
            ret = FAILED;
            break;
        }
        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resIdInfo[0], (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resIdInfo[0], (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resIdInfo[0], (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");

        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取并校验数据
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        bool isNull;
        int32_t PKValue = i;
        ret = GmcSetIndexKeyValue(gStmt, 0, GMC_DATATYPE_INT32, &PKValue, sizeof(PKValue));
        CHECK_AND_BREAK(ret, "set index key value");
        ret = GmcSetIndexKeyName(gStmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "alloc and get vertex");
        bool isFinish = true;
        ret = GmcFetch(gStmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(isFinish, false);

        uint32_t F0Size;
        int32_t F0Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F0", &F0Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F0", &F0Val, F0Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F0Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F0Val);
            break;
        }

        uint32_t F2Size;
        int32_t F2Val;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F2", &F2Size);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F2", &F2Val, F2Size, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");
        if (F2Val != i) {
            ret = FAILED;
            TEST_ERROR("check property failed, expect \"%d\", actual \"%d\"", i, F2Val);
            break;
        }

        uint32_t resSize;
        uint64_t resVal;
        ret = GmcGetVertexPropertySizeByName(gStmt, "F1", &resSize);
        CHECK_AND_BREAK(ret, "get vertex property size");
        ret = GmcGetVertexPropertyByName(gStmt, "F1", &resVal, resSize, &isNull);
        CHECK_AND_BREAK(ret, "get vertex property");

        uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
        ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);

        CHECK_AND_BREAK(ret, "deparse res id buffer");
        if (tmpPoolId != gResPoolId) {
            TEST_ERROR("checek res pool id failed, expect is %lu, actual is %lu", gResPoolId, tmpPoolId);
            ret = FAILED;
            break;
        }
        if (tmpCount != resCount) {
            TEST_ERROR("checek res count failed, expect is %lu, actual is %lu", resCount, tmpCount);
            ret = FAILED;
            break;
        }
        if (tmpStartIndex != (i * resCount)) {
            TEST_ERROR("checek res start index failed, expect is %lu, actual is %lu", i, tmpStartIndex);
            ret = FAILED;
            break;
        }
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除数据（即释放资源索引）
    for (int32_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(gStmt, gLabelName, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        char condStr[128] = {0};
        ret = snprintf(condStr, sizeof(condStr), "%s.F0 = %u", gLabelName, i);
        if (ret < 0) {
            ret = FAILED;
            break;
        }
        ret = GmcSetFilter(gStmt, condStr);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(gStmt);
        CHECK_AND_BREAK(ret, "delete vertex by cond");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, resPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(gStmt, resPoolName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnbindResPoolFromLabel(gStmt, gLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, gResPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, resPoolName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, resPoolName2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDestroyResPool(gStmt, resPoolName3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
