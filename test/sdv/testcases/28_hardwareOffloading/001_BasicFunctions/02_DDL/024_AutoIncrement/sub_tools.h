/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
//
// Created by w00495442 on 2020/10/21.
//

#ifndef SUB_TOOLS_H
#define SUB_TOOLS_H

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define MAX_CONN MAX_CONN_SIZE
#define MAX_NAME_LENGTH 128

typedef enum EnumOpTypeNum {
    OpTypeNum_1 = 1,  // 只有1种dml类型，申请内存时只需申请1个g_data_num的大小
    OpTypeNum_2,      // 有2种dml类型
    OpTypeNum_3,
} OpTypeNumE;

GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL, *g_conn_sub = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL, *g_stmt_sub = NULL;
void *g_label = NULL, *g_label_2 = NULL, *g_label_3 = NULL;
char *g_schema = NULL, *g_schema_2 = NULL, *g_schema_3 = NULL;
char *g_sub_info = NULL;
GmcConnT *g_subChan = NULL;
char g_label_config[] = "{\"max_record_count\":300000}";
char g_EdgeLabel_config[] = "{\"max_record_count\":1000}";
int affectRows;
unsigned int len;
int g_data_num = 50;  // 10000

using namespace std;

void test_setVertexProperty(GmcStmtT *stmt, int pk = 0)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value2 = (1 + pk);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value5 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value6 = pk;  // 联合索引时F6是PK
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value17 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value7 = pk;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value3 = (10 + pk);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value4 = (100 + pk);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_close_and_drop_label(GmcStmtT *stmt, void *label, char *labelName)
{
    int ret;
    if (label) {
        label = 0;
    }
    ret = GmcDropVertexLabel(stmt, labelName);
    if (!(ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE)) {
        printf("GmcDropVertexLabel ret :%d\r\n", ret);
    }
    AW_MACRO_EXPECT_EQ_INT(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
}

void test_setVertexProperty_updt(GmcStmtT *stmt, int pk = 0, bool is_union_pk = 0, bool is_bytes_union_pk = 0)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value2 = (1 + pk);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value3 = (10 + pk);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value4 = (100 + pk);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value5 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!is_union_pk) {
        int32_t value6 = pk;
        ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (!is_bytes_union_pk) {
        char teststr15[10] = "bytes";
        ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, strlen(teststr16));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value17 = (1000 + pk);
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void test_checkVertexProperty(GmcStmtT *stmt, void *label, char *label_pk, int pk = 0, bool is_union_pk = 0,
                              bool is_bytes_union_pk = 0)
{
    int ret;
    char teststr0 = 'a';
    unsigned char teststr1 = 'b';
    int8_t value2 = (1 + pk);
    uint8_t value3 = (10 + pk);
    int16_t value4 = (100 + pk);
    uint16_t value5 = (1000 + pk);
    int32_t value6 = pk;   // 联合索引时F6是pk
    uint32_t value7 = pk;  // F7是pk
    bool value8 = false;
    int64_t value9 = 1000 + pk;
    uint64_t value10 = 1000 + pk;
    float value11 = (float)1.2 + (float)pk;
    double value12 = 10.86 + pk;
    uint64_t value13 = 1000 + pk;
    char teststr14[] = "string";
    char teststr15[10] = "bytes";
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    uint32_t value17 = (1000 + pk);

    if (is_union_pk) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (is_bytes_union_pk) {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_BYTES, teststr15, 5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcSetIndexKeyName(stmt, label_pk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value6);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT32, &value7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value9);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value11);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value13);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr14);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, teststr15);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, teststr16);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &value17);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    GmcFreeIndexKey(stmt);
}

void test_checkVertexProperty_updt(GmcStmtT *stmt, void *label, char *label_pk, uint32_t key, int pk = 0,
                                   bool is_union_pk = 0, bool is_bytes_union_pk = 0)
{
    int ret;
    char teststr0 = 'a';
    unsigned char teststr1 = 'b';
    int8_t value2 = (1 + pk);
    uint8_t value3 = (10 + pk);
    int16_t value4 = (100 + pk);
    uint16_t value5 = (1000 + pk);
    int32_t value6;         // 联合索引时F6是pk
    uint32_t value7 = key;  // F7是pk
    bool value8 = false;
    int64_t value9 = 1000 + pk;
    uint64_t value10 = 1000 + pk;
    float value11 = (float)1.2 + (float)pk;
    double value12 = 10.86 + pk;
    uint64_t value13 = 1000 + pk;
    char teststr14[] = "string";
    char teststr15[10] = "bytes";
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    uint32_t value17 = (1000 + pk);

    if (is_union_pk) {
        value6 = key;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (is_bytes_union_pk) {
        value6 = pk;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_BYTES, teststr15, 5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else {
        value6 = pk;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcSetIndexKeyName(stmt, label_pk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value5);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value6);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT32, &value7);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value8);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value9);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value10);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value11);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value13);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr14);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, teststr15);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, teststr16);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &value17);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    GmcFreeIndexKey(stmt);
}

void test_checkVertexProperty_sub(GmcStmtT *stmt, int pk = 0)
{
    int ret;
    char teststr0 = 'a';
    unsigned char teststr1 = 'b';
    int8_t value2 = (1 + pk);
    uint8_t value3 = (10 + pk);
    int16_t value4 = (100 + pk);
    uint16_t value5 = (1000 + pk);
    int32_t value6 = pk;   // 联合索引时F6是pk
    uint32_t value7 = pk;  // F7是pk
    bool value8 = false;
    int64_t value9 = 1000 + pk;
    uint64_t value10 = 1000 + pk;
    float value11 = (float)1.2 + (float)pk;
    double value12 = 10.86 + pk;
    uint64_t value13 = 1000 + pk;
    char teststr14[] = "string";
    char teststr15[10] = "bytes";
    char teststr16[6] = "fixed";
    AW_MACRO_EXPECT_EQ_INT(6, strlen(teststr16) + 1);
    uint32_t value17 = (1000 + pk);

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value13);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr14);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, teststr15);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, teststr16);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &value17);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void sn_callback_del(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    printf("[INFO] info->labelCount is %d\r\n", info->labelCount);

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            printf("[INFO] vrtxLabelIdx : %d, labelName : %s\r\n", i, labelName);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    pk = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", pk);
                    test_checkVertexProperty_sub(subStmt, pk);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    pk = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", pk);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    pk = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", pk);
                    test_checkVertexProperty_sub(subStmt, pk);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    pk = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", pk);
                    test_checkVertexProperty_sub(subStmt, pk);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}
void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    printf("[INFO] info->labelCount is %d\r\n", info->labelCount);

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            printf("[INFO] vrtxLabelIdx : %d, labelName : %s\r\n", i, labelName);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    pk = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_INSERT new_value is %d\r\n", pk);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    pk = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_DELETE old_value is %d\r\n", pk);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    pk = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE new_value is %d\r\n", pk);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    pk = ((int *)user_data->old_value)[user_data->subIndex];
                    printf("[INFO] GMC_SUB_EVENT_UPDATE old_value is %d\r\n", pk);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

#if NOT_EXIST
void yang_insert_layer1(GmcStmtT * stmtVsys, int v1_id, char *labelName1, int pk = 0)
{
    void *vsysLabel;
    v1_id = v1_id + pk;

    int ret = testGmcPrepareStmtByLabelName(stmtVsys, labelName1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // insert
    ret = GmcSetVertexProperty(stmtVsys, "id", GMC_DATATYPE_INT32, &v1_id, sizeof(v1_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtVsys, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

void yang_insert_layer2(GmcStmtT * stmtRule, int v2_vsys_id, int v2_id, void *v2_name, char *labelName2, int pk = 0){
    void *ruleLabel;
    v2_vsys_id = v2_vsys_id + pk;
    v2_id = v2_id + pk;

    int ret = testGmcPrepareStmtByLabelName(stmtRule, labelName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // insert
    ret = GmcSetVertexProperty(stmtRule, "vsys::id", GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(v2_vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "id", GMC_DATATYPE_INT32, &v2_id, sizeof(v2_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtRule, "name", GMC_DATATYPE_STRING, v2_name, strlen((const char*)v2_name));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtRule);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtRule, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

void yang_insert_layer3(GmcStmtT * stmtS_ip, int v3_vsys_id, int v3_rule_id, int v3_ipLen, int v3_masklen, char *labelName3, int pk = 0){
    void *s_ipLabel;
    v3_vsys_id = v3_vsys_id + pk;
    v3_rule_id = v3_rule_id + pk;
    v3_ipLen = v3_ipLen + pk;
    v3_masklen = v3_masklen + pk;

    int ret = testGmcPrepareStmtByLabelName(stmtS_ip, labelName3, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入insert
    ret = GmcSetVertexProperty(stmtS_ip, "rule::vsys::id", GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(v3_vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "rule::id", GMC_DATATYPE_INT32, &v3_rule_id, sizeof(v3_rule_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "ipLen", GMC_DATATYPE_INT32, &v3_ipLen, sizeof(v3_ipLen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmtS_ip, "maskLen", GMC_DATATYPE_INT32, &v3_masklen, sizeof(v3_masklen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr1 = 'a';
    ret = GmcSetVertexProperty(stmtS_ip, "H0", GMC_DATATYPE_CHAR, &teststr1, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char teststr2 = 'b';
    ret = GmcSetVertexProperty(stmtS_ip, "H1", GMC_DATATYPE_UCHAR, &teststr2, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value1 = 1 + pk;
    ret = GmcSetVertexProperty(stmtS_ip, "H2", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value2 = 10 + pk;
    ret = GmcSetVertexProperty(stmtS_ip, "H3", GMC_DATATYPE_UINT8, &value2, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value3 = 100 + pk;
    ret = GmcSetVertexProperty(stmtS_ip, "H4", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value4 = 1000 + pk;
    ret = GmcSetVertexProperty(stmtS_ip, "H5", GMC_DATATYPE_UINT16, &value4, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value5 = 1000 + pk;
    ret = GmcSetVertexProperty(stmtS_ip, "H6", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value6 = 1000 + pk;
    ret = GmcSetVertexProperty(stmtS_ip, "H7", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool value7 = true;
    ret = GmcSetVertexProperty(stmtS_ip, "H8", GMC_DATATYPE_BOOL, &value7, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value8 = 1000 + pk;
    ret = GmcSetVertexProperty(stmtS_ip, "H9", GMC_DATATYPE_INT64, &value8, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmtS_ip, "H10", GMC_DATATYPE_UINT64, &value9, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value10 = 1.2 + pk;
    ret = GmcSetVertexProperty(stmtS_ip, "H11", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value11 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmtS_ip, "H12", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value12 = 1000 + pk;
    ret = GmcSetVertexProperty(stmtS_ip, "H13", GMC_DATATYPE_TIME, &value12, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr3[]="testver";
    ret = GmcSetVertexProperty(stmtS_ip, "H14", GMC_DATATYPE_STRING, teststr3, (strlen(teststr3)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char F15Value[] = "bytes";
    ret = GmcSetVertexProperty(stmtS_ip, "H15", GMC_DATATYPE_BYTES, F15Value, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char F16Value[] = "fixed";
    ret = GmcSetVertexProperty(stmtS_ip, "H16", GMC_DATATYPE_FIXED, F16Value, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入顶点
    ret = GmcExecute(stmtS_ip);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtS_ip, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

void yang_insert_all_type(GmcConnT * conn, char *labelName1, char *labelName2, char *labelName3, int pk = 0)
{
    int ret;
    printf("[INFO] insert pk = %d\r\n", pk);
    GmcStmtT *stmtVsys = NULL;
    ret = GmcAllocStmt(conn, &stmtVsys);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *vsysLabel;
    ret = testGmcPrepareStmtByLabelName(stmtVsys, labelName1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *ruleLabel;
    ret = testGmcPrepareStmtByLabelName(stmtRule, labelName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *s_ipLabel;
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, labelName3, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert V1 V2 V3
    int v1_id=1;
    yang_insert_layer1(stmtVsys, v1_id, labelName1, pk);
    int v2_vsys_id=1;
    int v2_id=1;
    char v2_name[] = "hei";
    yang_insert_layer2(stmtRule, v2_vsys_id, v2_id, &v2_name, labelName2, pk);

    // 插入v3的值
    int v3_vsys_id = v1_id;
    int v3_rule_id = v2_id;
    int v3_ipLen = 23;
    int v3_masklen = 23;
    yang_insert_layer3(stmtS_ip, v3_vsys_id, v3_rule_id, v3_ipLen, v3_masklen, labelName3, pk);

    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);
}

void yang_query_all_type(GmcConnT* conn, char *labelName1, char *labelName2, char *labelName3, int pk = 0)
{
    int ret;
    printf("[INFO] query pk = %d\r\n", pk);
    GmcStmtT *stmtVsys = NULL;
    ret = GmcAllocStmt(conn, &stmtVsys);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *vsysLabel;
    ret = testGmcPrepareStmtByLabelName(stmtVsys, labelName1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *ruleLabel;
    ret = testGmcPrepareStmtByLabelName(stmtRule, labelName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *s_ipLabel;
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, labelName3, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int v1_id=1 + pk;
    int v2_vsys_id=1 + pk;
    int v2_id=1 + pk;
    char v2_name[] = "hei";

    int v3_vsys_id = v1_id;
    int v3_rule_id = v2_id;
    int v3_ipLen = 23  + pk;
    int v3_masklen = 23  + pk;
    char teststr1 = 'a';
    unsigned char teststr2 = 'b';
    int8_t value1 = 1  + pk;
    uint8_t value2 = 10  + pk;
    int16_t value3 = 100  + pk;
    uint16_t value4 = 1000  + pk;
    int32_t value5 = 1000  + pk;
    uint32_t value6 = 1000  + pk;
    bool value7 = true;
    int64_t value8 = 1000  + pk;
    uint64_t value9 = 1000  + pk;
    float value10 = 1.2  + pk;
    double value11 = 10.86  + pk;
    uint64_t value12 = 1000  + pk;
    char teststr3[]="testver";
    char F15Value[] = "bytes";
    char F16Value[] = "fixed";

    // query V1
    ret = GmcSetIndexKeyValue(stmtVsys, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(v1_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtVsys,  "id");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // query V2
    ret = GmcSetIndexKeyValue(stmtRule, 0, GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(v2_vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtRule, 1, GMC_DATATYPE_INT32, &v2_id, sizeof(v2_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtRule,  "vsys::id_id");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtRule);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isNull;
    unsigned int size_v2name;
    ret = GmcGetVertexPropertySizeByName(stmtRule, "name", &size_v2name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *v2nameValue = (char *)malloc(size_v2name);
    ret = GmcGetVertexPropertyByName(stmtRule, "name", v2nameValue, size_v2name, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret=strcmp(v2_name, v2nameValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(v2nameValue);

    // query V3
    ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(v3_vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 1, GMC_DATATYPE_INT32, &v3_rule_id, sizeof(v3_rule_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 2, GMC_DATATYPE_INT32, &v3_ipLen, sizeof(v3_ipLen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 3, GMC_DATATYPE_INT32, &v3_masklen, sizeof(v3_masklen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtS_ip,  "vsys.rule.source_ip_K0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtS_ip);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H0", GMC_DATATYPE_CHAR, &teststr1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H1", GMC_DATATYPE_UCHAR, &teststr2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H2", GMC_DATATYPE_INT8, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H3", GMC_DATATYPE_UINT8, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H4", GMC_DATATYPE_INT16, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H5", GMC_DATATYPE_UINT16, &value4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H6", GMC_DATATYPE_INT32, &value5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H7", GMC_DATATYPE_UINT32, &value6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H8", GMC_DATATYPE_BOOL, &value7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H9", GMC_DATATYPE_INT64, &value8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H10", GMC_DATATYPE_UINT64, &value9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H11", GMC_DATATYPE_FLOAT, &value10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H12", GMC_DATATYPE_DOUBLE, &value11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H13", GMC_DATATYPE_TIME, &value12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H14", GMC_DATATYPE_STRING, teststr3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H15", GMC_DATATYPE_BYTES, F15Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H16", GMC_DATATYPE_FIXED, F16Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeIndexKey(stmtRule);
    GmcFreeIndexKey(stmtS_ip);
    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);
}

void yang_query_all_type_updt(GmcConnT* conn, char *labelName1, char *labelName2, char *labelName3, int key = 0, int id = 0)
{
    int ret;
    printf("[INFO] query key = %d, id = %d\r\n", key, id);
    GmcStmtT *stmtVsys = NULL;
    ret = GmcAllocStmt(conn, &stmtVsys);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *vsysLabel;
    ret = testGmcPrepareStmtByLabelName(stmtVsys, labelName1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *ruleLabel;
    ret = testGmcPrepareStmtByLabelName(stmtRule, labelName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *s_ipLabel;
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, labelName3, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int v1_id=1 + key;
    int v2_vsys_id=1 + key;
    int v2_id=1 + key;
    char v2_name[] = "hei";

    int v3_vsys_id = v1_id;
    int v3_rule_id = v2_id;
    int v3_ipLen = 23  + key;
    int v3_masklen = 23  + key;
    char teststr1 = 'a';
    unsigned char teststr2 = 'b';
    int8_t value1 = 1  + id;
    uint8_t value2 = 10  + id;
    int16_t value3 = 100  + id;
    uint16_t value4 = 1000  + id;
    int32_t value5 = 1000  + id;
    uint32_t value6 = 1000  + id;
    bool value7 = true;
    int64_t value8 = 1000  + id;
    uint64_t value9 = 1000  + id;
    float value10 = 1.2  + id;
    double value11 = 10.86  + id;
    uint64_t value12 = 1000  + id;
    char teststr3[]="testver";
    char F15Value[] = "bytes";
    char F16Value[] = "fixed";

    // query V1
    ret = GmcSetIndexKeyValue(stmtVsys, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(v1_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtVsys,  "id");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // query V2
    ret = GmcSetIndexKeyValue(stmtRule, 0, GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(v2_vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtRule, 1, GMC_DATATYPE_INT32, &v2_id, sizeof(v2_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtRule,  "vsys::id_id");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtRule);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isNull;
    unsigned int size_v2name;
    ret = GmcGetVertexPropertySizeByName(stmtRule, "name", &size_v2name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *v2nameValue = (char *)malloc(size_v2name);
    ret = GmcGetVertexPropertyByName(stmtRule, "name", v2nameValue, size_v2name, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(false, isNull);
    ret=strcmp(v2_name, v2nameValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(v2nameValue);

    // query V3
    ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(v3_vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 1, GMC_DATATYPE_INT32, &v3_rule_id, sizeof(v3_rule_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 2, GMC_DATATYPE_INT32, &v3_ipLen, sizeof(v3_ipLen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 3, GMC_DATATYPE_INT32, &v3_masklen, sizeof(v3_masklen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtS_ip,  "vsys.rule.source_ip_K0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtS_ip);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H0", GMC_DATATYPE_CHAR, &teststr1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H1", GMC_DATATYPE_UCHAR, &teststr2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H2", GMC_DATATYPE_INT8, &value1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H3", GMC_DATATYPE_UINT8, &value2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H4", GMC_DATATYPE_INT16, &value3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H5", GMC_DATATYPE_UINT16, &value4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H6", GMC_DATATYPE_INT32, &value5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H7", GMC_DATATYPE_UINT32, &value6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H8", GMC_DATATYPE_BOOL, &value7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H9", GMC_DATATYPE_INT64, &value8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H10", GMC_DATATYPE_UINT64, &value9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H11", GMC_DATATYPE_FLOAT, &value10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H12", GMC_DATATYPE_DOUBLE, &value11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H13", GMC_DATATYPE_TIME, &value12);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H14", GMC_DATATYPE_STRING, teststr3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H15", GMC_DATATYPE_BYTES, F15Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmtS_ip, "H16", GMC_DATATYPE_FIXED, F16Value);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeIndexKey(stmtRule);
    GmcFreeIndexKey(stmtS_ip);
    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);
}

void yang_update_layer3(GmcStmtT* stmtS_ip, int v3_vsys_id, int v3_rule_id, int v3_ipLen, int v3_masklen, char *labelName3, int key = 0, int id = 0){
    void *s_ipLabel;
    v3_vsys_id = v3_vsys_id + key;
    v3_rule_id = v3_rule_id + key;
    v3_ipLen = v3_ipLen + key;
    v3_masklen = v3_masklen + key;

    int ret = testGmcPrepareStmtByLabelName(stmtS_ip, labelName3, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(v3_vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 1, GMC_DATATYPE_INT32, &v3_rule_id, sizeof(v3_rule_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 2, GMC_DATATYPE_INT32, &v3_ipLen, sizeof(v3_ipLen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 3, GMC_DATATYPE_INT32, &v3_masklen, sizeof(v3_masklen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr1 = 'a';
    ret = GmcSetVertexProperty(stmtS_ip, "H0", GMC_DATATYPE_CHAR, &teststr1, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned char teststr2 = 'b';
    ret = GmcSetVertexProperty(stmtS_ip, "H1", GMC_DATATYPE_UCHAR, &teststr2, sizeof(unsigned char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int8_t value1 = 1 + id;
    ret = GmcSetVertexProperty(stmtS_ip, "H2", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t value2 = 10 + id;
    ret = GmcSetVertexProperty(stmtS_ip, "H3", GMC_DATATYPE_UINT8, &value2, sizeof(uint8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int16_t value3 = 100 + id;
    ret = GmcSetVertexProperty(stmtS_ip, "H4", GMC_DATATYPE_INT16, &value3, sizeof(int16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint16_t value4 = 1000 + id;
    ret = GmcSetVertexProperty(stmtS_ip, "H5", GMC_DATATYPE_UINT16, &value4, sizeof(uint16_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value5 = 1000 + id;
    ret = GmcSetVertexProperty(stmtS_ip, "H6", GMC_DATATYPE_INT32, &value5, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t value6 = 1000 + id;
    ret = GmcSetVertexProperty(stmtS_ip, "H7", GMC_DATATYPE_UINT32, &value6, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool value7 = true;
    ret = GmcSetVertexProperty(stmtS_ip, "H8", GMC_DATATYPE_BOOL, &value7, sizeof(bool));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t value8 = 1000 + id;
    ret = GmcSetVertexProperty(stmtS_ip, "H9", GMC_DATATYPE_INT64, &value8, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value9 = 1000 + id;
    ret = GmcSetVertexProperty(stmtS_ip, "H10", GMC_DATATYPE_UINT64, &value9, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    float value10 = 1.2 + id;
    ret = GmcSetVertexProperty(stmtS_ip, "H11", GMC_DATATYPE_FLOAT, &value10, sizeof(float));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    double value11 = 10.86 + id;
    ret = GmcSetVertexProperty(stmtS_ip, "H12", GMC_DATATYPE_DOUBLE, &value11, sizeof(double));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint64_t value12 = 1000 + id;
    ret = GmcSetVertexProperty(stmtS_ip, "H13", GMC_DATATYPE_TIME, &value12, sizeof(uint64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char teststr3[]="testver";
    ret = GmcSetVertexProperty(stmtS_ip, "H14", GMC_DATATYPE_STRING, teststr3, (strlen(teststr3)));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char F15Value[] = "bytes";
    ret = GmcSetVertexProperty(stmtS_ip, "H15", GMC_DATATYPE_BYTES, F15Value, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char F16Value[] = "fixed";
    ret = GmcSetVertexProperty(stmtS_ip, "H16", GMC_DATATYPE_FIXED, F16Value, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtS_ip,  "vsys.rule.source_ip_K0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtS_ip);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtS_ip, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

void yang_update_all_type(GmcConnT* conn, char *labelName1, char *labelName2, char *labelName3, int key = 0, int id = 0)
{
    int ret;
    printf("[INFO] update key = %d, id = %d\r\n", key, id);
    GmcStmtT *stmtVsys = NULL;
    ret = GmcAllocStmt(conn, &stmtVsys);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *vsysLabel;
    ret = testGmcPrepareStmtByLabelName(stmtVsys, labelName1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *ruleLabel;
    ret = testGmcPrepareStmtByLabelName(stmtRule, labelName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *s_ipLabel;
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, labelName3, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int v1_id=1;
    int v2_vsys_id=1;
    int v2_id=1;

    // 更新v3的值
    int v3_vsys_id = v1_id;
    int v3_rule_id = v2_id;
    int v3_ipLen = 23;
    int v3_masklen = 23;
    yang_update_layer3(stmtS_ip, v3_vsys_id, v3_rule_id, v3_ipLen, v3_masklen, labelName3, key, id);

    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);
}

void yang_delete_layer1(GmcStmtT* stmtVsys, int v1_id, char *labelName1){
    void *vsysLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtVsys, labelName1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmtVsys, 0, GMC_DATATYPE_INT32, &v1_id, sizeof(v1_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtVsys,  "id");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtVsys);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtVsys, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

void yang_delete_layer2(GmcStmtT* stmtRule, int v2_vsys_id, int v2_id, char *labelName2){
    void *ruleLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtRule, labelName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtRule, 0, GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(v2_vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtRule, 1, GMC_DATATYPE_INT32, &v2_id, sizeof(v2_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtRule,  "vsys::id_id");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtRule);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtRule, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

void yang_delete_layer3(GmcStmtT* stmtS_ip, int v3_vsys_id, int v3_rule_id, int v3_ipLen, int v3_masklen, char *labelName3){
    void *s_ipLabel;
    int ret = testGmcPrepareStmtByLabelName(stmtS_ip, labelName3, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(v3_vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 1, GMC_DATATYPE_INT32, &v3_rule_id, sizeof(v3_rule_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 2, GMC_DATATYPE_INT32, &v3_ipLen, sizeof(v3_ipLen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 3, GMC_DATATYPE_INT32, &v3_masklen, sizeof(v3_masklen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmtS_ip,  "vsys.rule.source_ip_K0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmtS_ip);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetStmtAttr(stmtS_ip, GMC_STMT_ATTR_AFFECTED_ROWS, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

void yang_delete_all_type(GmcConnT* conn, char *labelName1, char *labelName2, char *labelName3, int pk = 0)
{
    int ret;
    printf("[INFO] delete pk = %d\r\n", pk);
    GmcStmtT *stmtVsys = NULL;
    ret = GmcAllocStmt(conn, &stmtVsys);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtRule = NULL;
    ret = GmcAllocStmt(conn, &stmtRule);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtS_ip = NULL;
    ret = GmcAllocStmt(conn, &stmtS_ip);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *vsysLabel;
    ret = testGmcPrepareStmtByLabelName(stmtVsys, labelName1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *ruleLabel;
    ret = testGmcPrepareStmtByLabelName(stmtRule, labelName2, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    void *s_ipLabel;
    ret = testGmcPrepareStmtByLabelName(stmtS_ip, labelName3, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int v1_id=1 + pk;

    int v2_vsys_id=1 + pk;
    int v2_id=1 + pk;

    int v3_vsys_id = v1_id;
    int v3_rule_id = v2_id;
    int v3_ipLen = 23  + pk;
    int v3_masklen = 23  + pk;

    // 删除V1
    yang_delete_layer1(stmtVsys, v1_id, labelName1);

    // 删除V2
    yang_delete_layer2(stmtRule, v2_vsys_id, v2_id, labelName2);
    ret = GmcSetIndexKeyValue(stmtRule, 0, GMC_DATATYPE_INT32, &v2_vsys_id, sizeof(v2_vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtRule, 1, GMC_DATATYPE_INT32, &v2_id, sizeof(v2_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除V3
    yang_delete_layer3(stmtS_ip, v3_vsys_id, v3_rule_id, v3_ipLen, v3_masklen, labelName3);
    ret = GmcSetIndexKeyValue(stmtS_ip, 0, GMC_DATATYPE_INT32, &v3_vsys_id, sizeof(v3_vsys_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 1, GMC_DATATYPE_INT32, &v3_rule_id, sizeof(v3_rule_id));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 2, GMC_DATATYPE_INT32, &v3_ipLen, sizeof(v3_ipLen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmtS_ip, 3, GMC_DATATYPE_INT32, &v3_masklen, sizeof(v3_masklen));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcFreeIndexKey(stmtRule);
    GmcFreeIndexKey(stmtS_ip);
    GmcFreeStmt(stmtVsys);
    GmcFreeStmt(stmtRule);
    GmcFreeStmt(stmtS_ip);
}
#endif

void sn_callback_NULL(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData) {}

void sn_callback_not_cmp(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int pk, i;
    SnUserDataT *user_data = (SnUserDataT *)userData;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret != GMERR_OK || eof == true) {
            break;
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
            default:
                break;
        }
    }
}

#endif /* SUB_TOOLS_H */
