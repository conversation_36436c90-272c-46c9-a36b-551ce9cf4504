#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "./sub_tools.h"
#include "common_base_tools.h"
#include "auto_increment_tools.h"

#include "../../common/hash_util.h"

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];
int WRITE_NUM = 10000;
#define FULLTABLE 0xff

class AutoIncrement : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();

        ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        //恢复配置文件
        RecoverCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void AutoIncrement::SetUp()
{
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char errorMsg2[128] = {};
    (void)snprintf(errorMsg2, sizeof(errorMsg2), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg2);

    char errorMsg3[128] = {};
    (void)snprintf(errorMsg3, sizeof(errorMsg3), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg3);

    char errorMsg4[128] = {};
    (void)snprintf(errorMsg4, sizeof(errorMsg4), "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg4);

    char errorMsg5[128] = {};
    (void)snprintf(errorMsg5, sizeof(errorMsg5), "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg5);

    char errorMsg6[128] = {};
    (void)snprintf(errorMsg6, sizeof(errorMsg6), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg6);

    char errorMsg7[128] = {};
    (void)snprintf(errorMsg7, sizeof(errorMsg7), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg7);

    char errorMsg8[128] = {};
    (void)snprintf(errorMsg8, sizeof(errorMsg8), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg8);

    char errorMsg9[128] = {};
    (void)snprintf(errorMsg9, sizeof(errorMsg9), "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg9);

    char errorMsg10[128] = {};
    (void)snprintf(errorMsg10, sizeof(errorMsg10), "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg10);

    AW_CHECK_LOG_BEGIN();
}
void AutoIncrement::TearDown()
{
    AW_CHECK_LOG_END();
    GmcFreeStmt(g_stmt);
    int ret = testGmcDisconnect(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

/*****************************************************************************
 * Description  : 创建一个表，主键带有自增属性；插入数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，非主键带有自增属性；插入数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_002.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_PK_F0_uint32(g_stmt, i);
        set_Property_F1_AutoIncrement(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，主键带有自增属性；插入数据时设置自增属性的值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_003)  // 2021.09.03 允许用户设置修改自增属性的值
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    int32_t i = 1;
    set_PK_F0_uint32(g_stmt, i);  // 2021.09.03 之前不允许同时对 自增属性（F0） 设值
    set_Property(g_stmt, i, 0);   // 2021.09.03 现在允许修改自增属性值（F0）
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，非主键带有自增属性；插入数据时设置自增属性的值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_002.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    int32_t i = 1;
    set_PK_F0_uint32(g_stmt, i);
    set_Property_F1_AutoIncrement(g_stmt, i, 0);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，主键带有自增属性；插入数据；更新自增属性值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    GmcStmtT *stmtSec = NULL;
    ret = GmcAllocStmt(g_conn, &stmtSec);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcStmtT *stmtVsys = NULL;
    ret = GmcAllocStmt(g_conn, &stmtVsys);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);  //自增列F0从1开始写值
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    //更新自增属性值,更新后表中数据实际未变
    for (int i = 1; i < 101; i++) {
        uint32_t value0 = i;
        uint32_t value1 = i + 999;
        //更新数据前先reset
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value0, sizeof(value0));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        //设置F0属性值
    
        //更新顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");  // 2021.09.03 允许用户设置修改自增属性的值
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // query 读
    ret = query_read_AutoIncrement_001(1, 101, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，非主键带有自增属性；插入数据；更新自增属性值

 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_002.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_PK_F0_uint32(g_stmt, i);
        set_Property_F1_AutoIncrement(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    //更新自增属性值,更新后表中数据实际未变
    for (int i = 0; i < 100; i++) {
        uint32_t value0 = i;
        uint64_t value1 = i + 999;

        //更新数据前先reset
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &value0, sizeof(value0));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        //设置F1属性值
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT64, &value1, sizeof(value1));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        //更新顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");  // 2021.09.03 允许用户设置修改自增属性的值
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // query 读
    ret = query_read_AutoIncrement_002(0, 100, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建deltastore；创建label，带有多个uint32位域字段(占用内存空间=32bit)；执行dml操作
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_007.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增属性设置为uint8数据类型
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_008.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增属性设置为int32数据类型
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_009.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增属性设置为uint32数据类型,自增值设置为0；创建表失败

 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":0,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_010.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);  // 2021.09.18 DTS2021091718142  自增值可以为0,
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_EXCEPTION, ret);  // 2021.9.29日再次修改约束（廖美丰），为0时，报错

    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增属性设置为uint64数据类型；
                  自增值设置为9223372036854775807 ，插入记录。 此处为int64数据类型的范围
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":9223372036854775807,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_011.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        LOG("i = %d", i);
        set_PK_F0_uint32(g_stmt, i);
        set_Property_F1_AutoIncrement(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，superfield带有自增列
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_012.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增字段nullalbe为false属性
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "demo";
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_013.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);  // 2021.09.03 auto_increment不限制nullable属性
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // drop vertex
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增字段设置default值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_014.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增属性设置为uint32数据类型；自增值设置为0xFFFFFFFF，插入两条记录

 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":4294967295,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        if (i == 0) {
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
            ret = testGmcGetLastError(NULL);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，主键为自增属性；插入数据，按主键值过滤
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
    }
    ret = GmcSetFilter(g_stmt, "F0 <=10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetOutputFormat(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，主键为自增属性；插入数据，读取主键值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 1; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 1; i < 100; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // Get F0
        uint32_t size;
        uint32_t value;
        bool isNull;
        ;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F0", &value, size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            AW_MACRO_ASSERT_EQ_INT(0, ret);  //预期获取插入值
        } else {
            AW_MACRO_ASSERT_EQ_INT(0, 1);
        }
    }
    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，主键为自增属性；初始值设为0xFFFF,插入100条数据, UInt16 - [0 : 65535]
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":65535,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，带自增属性, 设置自增初始值为100；truncate表，查询自增列的值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":100,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    //关闭顶点label
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 100; i < 200; i++) {
        //设置过滤条件
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(i));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        //查询顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // Get F0
        uint32_t size;
        uint32_t value;
        bool isNull;
        ;
        while (true) {
            bool isFinish;
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }

            GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
            ret = GmcGetVertexPropertyByName(g_stmt, "F0", &value, size, &isNull);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
        LOG("value = %d, i = %d, isNull = %d", value, i, isNull);
        if (isNull == 0) {
            ret = 1;
            if (value == i) {
                ret = 0;
            }
            AW_MACRO_ASSERT_EQ_INT(0, ret);  //预期获取插入值
        } else {
            AW_MACRO_ASSERT_EQ_INT(0, 1);
        }
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增列是主键；对不存在的记录进行merge
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int value1 = 999;
    uint32_t f0_value = 999;
    /*ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value)); // 2021.09.03
    允许用户设置修改自增属性的值 AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);*/
    ret = GmcSetIndexKeyId(g_stmt, 0);  // 2021.11.15 merge时，必须设置 key value 和 key name
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //设置merge后的属性值
    set_Property(g_stmt, value1, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret); // 20230905 自增列作为主键的表不允许执行merge操作

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增列不是主键；对不存在的记录进行merge
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_021.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_PK_F0_uint32(g_stmt, i);
        set_Property_F1_AutoIncrement(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t value1 = 999;
    uint32_t f0_value = 999;

    // merge
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_MERGE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyId(g_stmt, 0);  // 2021.11.15 merge时，必须设置 key value 和 key name
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //设置merge后的属性值
    set_Property_F1_AutoIncrement(g_stmt, value1, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增列是主键；对不存在的记录进行replace
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);  //写F1，F0未写
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // replace
    for (int i = 101; i < 1001; i++) {
        uint32_t value0 = i;
        uint32_t f0_value = i;

        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(
            g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));  // 2021.09.03 允许用户设置修改自增属性的值
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        //设置replace后其他属性字段值
        set_Property(g_stmt, value0, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // query
    ret = query_read_AutoIncrement_001(1, 1001, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增列不是主键；对不存在的记录进行replace
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_002.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_PK_F0_uint32(g_stmt, i);
        set_Property_F1_AutoIncrement(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // replace
    for (int i = 100; i < 1000; i++) {
        uint32_t value0 = i;
        uint32_t f0_value = i;

        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_REPLACE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        //设置replace后的属性值
        set_Property_F1_AutoIncrement(g_stmt, value0, 1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    //读
    ret = query_read_AutoIncrement_002(0, 1000, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增列是主键；对不存在的记录进行update
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t value0 = 999;
    uint32_t f0_value = 999;

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    set_Property(g_stmt, value0, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个表，自增列不是主键；对不存在的记录进行update
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_021.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_PK_F0_uint32(g_stmt, i);
        set_Property_F1_AutoIncrement(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    uint32_t value0 = 999;
    uint32_t f0_value = 999;

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    set_Property_F1_AutoIncrement(g_stmt, value0, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *pro26(void *args)
{
    void *vertexLabel = NULL;
    const char *labelName = "demo";
    int count = (int)(*(int *)args);  // 通过count构造 来使并发插入不出现相同的值
    GmcConnT *conn_t;
    GmcStmtT *stmt_t;

    int ret = testGmcConnect(&conn_t, &stmt_t);
    //获取顶点label
    // 插入顶点
    for (int i = 0; i < 1000; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        set_Property(
            stmt_t, (i + 52051) * (i + 38092) + (count + 40702) * (count + 38095) + (i + 83074) * (count + 2394), 0);
        ret = GmcExecute(stmt_t);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //关闭顶点label

    ret = testGmcDisconnect(conn_t, stmt_t);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // GmcFreeIndexKey(stmt_t);  testGmcDisconnect 中会释放 stmt ，不需要重复释放
}

/*****************************************************************************
 * Description  : 创建一个表，带有自增属性；多个线程同时插入数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    const char *labelName = "demo";
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int tdNum = 10;
    pthread_t td[tdNum];
    uint32_t a[10];
    for (uint32_t i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&td[i], NULL, pro26, (void *)&a[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < tdNum; i++) {
        pthread_join(td[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *pro27(void *args)
{
    void *vertexLabel = NULL;
    const char *labelName = "demo";
    int count = (int)(*(int *)args);  // 通过count构造 来使并发插入不出现相同的值

    uint32_t value;
    uint32_t f0_value = 1;
    srand(time(0));
    for (int i = 0; i < 1000; i++) {
        // value = rand() % 10000;
        uint32_t index = 1;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &index, sizeof(index));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        //设置update后的属性值
        set_Property(
            g_stmt, (i + 52051) * (i + 38092) + (count + 40702) * (count + 38095) + (i + 83074) * (count + 2394), 1);
        //更新顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}

/*****************************************************************************
 * Description  : 创建一个表，带有自增属性;多个线程同时update,replace,merge一条数据
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    int tdNum = 1;
    pthread_t td[tdNum];
    uint32_t a[1];
    for (uint32_t i = 0; i < tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&td[i], NULL, pro27, (void *)&a[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (uint32_t i = 0; i < tdNum; i++) {
        pthread_join(td[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建一个label，带有自增属性；开启事务，进行dml（增删改）操作,检验自增属性值
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);
    char Label_config[] = "{\"max_record_count\":100000}";
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    srand(time(0));

    // 插入顶点
    for (uint32_t f0_value = 1; f0_value < 2; f0_value++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcTxConfigT config;
        config.transMode = GMC_TRANS_USED_IN_CS;
        config.type = GMC_TX_ISOLATION_COMMITTED;
        config.readOnly = false;
        config.trxType = GMC_DEFAULT_TRX;
        ret = GmcTransStart(g_conn, &config);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        LOG("f0_value = %d", f0_value);
        set_Property(g_stmt, f0_value, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        int32_t value;

        // update
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        value = rand() % 10000;
        uint32_t index = f0_value;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &index, sizeof(index));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        //设置update后的属性值
        set_Property(g_stmt, value, 1);
        //更新顶点
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // delete
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &index, sizeof(index));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        // insert
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        LOG("f0_value = %d", f0_value);
        set_Property(g_stmt, f0_value + 2, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcTransCommit(g_conn);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t index = 2;  // 现在 update 不再改变 自增列的值
    //设置过滤条件
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &index, sizeof(index));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //查询顶点
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);  // 2021.12.01 直连读时需要先GmcFetch再获取数据
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Get F1
    unsigned int sizeF0;
    GmcGetVertexPropertySizeByName(g_stmt, "F0", &sizeF0);
    uint32_t valueF0;
    bool isNull;
    ;
    ret = GmcGetVertexPropertyByName(g_stmt, "F0", &valueF0, sizeF0, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    LOG("valueF0 = %d, index = %d, isNull = %d", valueF0, index, isNull);
    if (isNull == 0) {
        AW_MACRO_ASSERT_EQ_INT(index, valueF0);
    } else {
        AW_MACRO_ASSERT_EQ_INT(0, 1);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 创建deltastore；创建一个label，带有自增属性；进行dml操作

 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    int ret = 0;
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    srand(time(0));

    // 插入顶点
    for (uint32_t f0_value = 1; f0_value < 100; f0_value++) {
        LOG("f0_value = %d", f0_value);
        set_Property(g_stmt, f0_value, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        int32_t value;
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增属性在主键的非第一个字段上
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_030.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);  // 2021.11.06 DTS2021110117284 自增列必须在主键第一个字段
    /*const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);        //F0, F1为主键，自增列建在F1上
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
        ret=testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);*/
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增属性设置为localhash第一个字段上
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_031.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);  // 2021.11.06 DTS2021110117284 自增列必须在主键,不能在其他索引
    free(label_schema);
    /*const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);*/
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增属性设置为localhash非第一个字段上
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_032.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);  // 2021.11.06 DTS2021110117284 自增列必须在主键,不能在其他索引
    free(label_schema);
    /*const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);   // 2021.09.03 允许用户设置修改自增属性的值
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);*/
    AW_FUN_Log(LOG_STEP, "test end.");
}

static const char *gResPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 10001,
        "start_id" : 1,
        "capacity" : 100,
        "order" : 0,
        "alloc_type" : 0
    })";
static const char *resPoolTestName = "resource_pool_test";
static const char *g_label_name = "TestResource";
static const char *g_label_config1 = R"({"max_record_count":1000})";
/*****************************************************************************
 * Description  : resource 属性设置自增属性
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    char *label_schema = NULL;
    const char *g_label_config1 = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    ret = GmcCreateResPool(g_stmt, gResPoolTest);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    void *globleVertexLabel = NULL;

    readJanssonFile("schemaFile/AutoIncrement_033.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt, "resource_pool_test");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增属性设置nullable参数为false
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "demo";
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_034.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);  // 2021.09.03 auto_increment不限制nullable属性
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_PK_F0_uint32(g_stmt, i);
        set_Property_F1_AutoIncrement(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // drop vertex
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 插入后，查询自增属性值增加
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t index = 100;
    //设置过滤条件
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &index, sizeof(index));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //查询顶点
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Get F1
    unsigned int sizeF0;
    GmcGetVertexPropertySizeByName(g_stmt, "F0", &sizeF0);
    uint32_t valueF0;
    bool isNull;
    ;
    ret = GmcGetVertexPropertyByName(g_stmt, "F0", &valueF0, sizeF0, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    LOG("valueF0 = %d, index = %d, isNull = %d", valueF0, index, isNull);
    if (isNull == 0) {
        AW_MACRO_ASSERT_EQ_INT(index, valueF0);
    } else {
        AW_MACRO_ASSERT_EQ_INT(0, 1);
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 更新后，自增属性值不变
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // update
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t index = 10;
    //更新数据前先reset
    ret = GmcResetVertex(g_stmt, false);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &index, sizeof(index));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //设置F2属性值
    int32_t up_val = 200;
    ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &up_val, sizeof(up_val));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //更新顶点
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcFreeIndexKey(g_stmt);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    index = 100;  // 现在 update 不再改变 自增列的值
    //设置过滤条件
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &index, sizeof(index));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //查询顶点
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);  // 2021.12.01 直连读时需要先GmcFetch再获取数据
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Get F1
    unsigned int sizeF0;
    GmcGetVertexPropertySizeByName(g_stmt, "F0", &sizeF0);
    uint32_t valueF0;
    bool isNull;
    ;
    ret = GmcGetVertexPropertyByName(g_stmt, "F0", &valueF0, sizeF0, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    LOG("valueF0 = %d, index = %d, isNull = %d", valueF0, index, isNull);
    if (isNull == 0) {
        AW_MACRO_ASSERT_EQ_INT(index, valueF0);
    } else {
        AW_MACRO_ASSERT_EQ_INT(0, 1);
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 启动事务，插入成功，事务rollback
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);
    char Label_config[] = "{\"max_record_count\":100000}";
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    srand(time(0));

    // 插入顶点

    uint32_t f0_value = 1;
    for (f0_value = 1; f0_value < 100; f0_value++) {
        LOG("f0_value = %d", f0_value);
        set_Property(g_stmt, f0_value, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    LOG("f0_value = %d", f0_value);
    set_Property(g_stmt, f0_value, 0);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransRollBack(g_conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    f0_value++;

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    LOG("f0_value = %d", f0_value);
    set_Property(g_stmt, f0_value, 0);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t index = 101;
    //设置过滤条件
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &index, sizeof(index));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //查询顶点
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);  // 2021.12.01 直连读时需要先GmcFetch再获取数据
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Get F1
    unsigned int sizeF0;
    GmcGetVertexPropertySizeByName(g_stmt, "F0", &sizeF0);
    uint32_t valueF0;
    bool isNull;
    ;
    ret = GmcGetVertexPropertyByName(g_stmt, "F0", &valueF0, sizeF0, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    LOG("valueF0 = %d, index = %d, isNull = %d", valueF0, index, isNull);
    if (isNull == 0) {
        AW_MACRO_ASSERT_EQ_INT(index, valueF0);
    } else {
        AW_MACRO_ASSERT_EQ_INT(0, 1);
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 启动事务，插入失败，事务commit
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);
    char Label_config[] = "{\"max_record_count\":100000}";
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    srand(time(0));

    // 插入顶点

    uint32_t f0_value = 1;
    for (f0_value = 1; f0_value < 100; f0_value++) {
        LOG("f0_value = %d", f0_value);
        set_Property(g_stmt, f0_value, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    LOG("f0_value = %d", f0_value);
    set_PK_F0_uint32(g_stmt, f0_value);  // 2021.09.03 允许用户设置修改自增属性的值
    set_Property(g_stmt, f0_value, 0);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t index = 100;
    //设置过滤条件
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &index, sizeof(index));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //查询顶点
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);  // 2021.12.03 直连读时需要先GmcFetch再获取数据
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Get F1
    unsigned int sizeF0;
    GmcGetVertexPropertySizeByName(g_stmt, "F0", &sizeF0);
    uint32_t valueF0;
    bool isNull;
    ;
    ret = GmcGetVertexPropertyByName(g_stmt, "F0", &valueF0, sizeF0, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    LOG("valueF0 = %d, index = %d, isNull = %d", valueF0, index, isNull);
    if (isNull == 0) {
        AW_MACRO_ASSERT_EQ_INT(index, valueF0);
    } else {
        AW_MACRO_ASSERT_EQ_INT(0, 1);
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 启动事务，删除成功，事务rollback
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    char Label_config[] = "{\"max_record_count\":100000}";
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    srand(time(0));

    // 插入顶点
    uint32_t f0_value = 1;
    for (f0_value = 1; f0_value < 100; f0_value++) {
        LOG("f0_value = %d", f0_value);
        set_Property(g_stmt, f0_value, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;
    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    f0_value--;

    // delete
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcTransCommit(g_conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    f0_value++;

    // insert
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    LOG("f0_value = %d", f0_value);
    set_Property(g_stmt, f0_value, 0);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t index = 100;
    //设置过滤条件
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &index, sizeof(index));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //查询顶点
    ret = GmcSetIndexKeyName(g_stmt, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // query
    bool isFinish = true;
    ret = GmcFetch(g_stmt, &isFinish);  // 2021.12.01 直连读时需要先GmcFetch再获取数据
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Get F1
    unsigned int sizeF0;
    GmcGetVertexPropertySizeByName(g_stmt, "F0", &sizeF0);
    uint32_t valueF0;
    bool isNull;
    ;
    ret = GmcGetVertexPropertyByName(g_stmt, "F0", &valueF0, sizeF0, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    LOG("valueF0 = %d, index = %d, isNull = %d", valueF0, index, isNull);
    if (isNull == 0) {
        AW_MACRO_ASSERT_EQ_INT(index, valueF0);
    } else {
        AW_MACRO_ASSERT_EQ_INT(0, 1);
    }

    //关闭顶点label

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *pro40(void *args)
{
    char *label_schema_40 = NULL;
    void *vertexLabel = NULL;
    char labelName[20];
    char fileName[100];
    GmcConnT *conn_t;
    GmcStmtT *stmt_t;
    uint32_t seq = *(uint32_t *)args;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    snprintf(labelName, 20, "%s%d", "demo", seq);
    LOG("%s", labelName);
    snprintf(fileName, 100, "%s%d%s", "schemaFile/AutoIncrement_040_", seq, ".gmjson");
    LOG("%s", fileName);
    // 读取label schema
    readJanssonFile(fileName, &label_schema_40);

    int ret = testGmcConnect(&conn_t, &stmt_t);
    //获取顶点label
    ret = GmcCreateVertexLabel(stmt_t, label_schema_40, g_label_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 1000; i++) {
        set_Property(stmt_t, i, 0);
        ret = GmcExecute(stmt_t);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // query
    ret = testGmcPrepareStmtByLabelName(stmt_t, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    uint32_t index = 1000;
    //设置过滤条件
    ret = GmcSetIndexKeyValue(stmt_t, 0, GMC_DATATYPE_UINT32, &index, sizeof(index));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //查询顶点
    ret = GmcSetIndexKeyName(stmt_t, "PK");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt_t);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // Get F1
    unsigned int sizeF0;
    GmcGetVertexPropertySizeByName(stmt_t, "F0", &sizeF0);
    uint32_t valueF0;
    bool isNull;
    ;
    ret = GmcGetVertexPropertyByName(stmt_t, "F0", &valueF0, sizeF0, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    LOG("valueF0 = %d, index = %d, isNull = %d", valueF0, index, isNull);

    //关闭顶点label

    ret = GmcDropVertexLabel(stmt_t, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_t, stmt_t);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema_40);
}

/*****************************************************************************
 * Description  : 多表交替插入，各自自增字段值不受影响
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.18
 * Modification : Create function
 *******************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int tdNum = 3;
    pthread_t td[tdNum];
    uint32_t a[4];
    a[0] = 0;
    for (uint32_t i = 1; i <= tdNum; i++) {
        a[i] = i;
        ret = pthread_create(&td[i], NULL, pro40, (void *)&a[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    for (uint32_t i = 1; i <= tdNum; i++) {
        pthread_join(td[i], NULL);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 41.导入schema，主键带有自增属性；插入数据
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "demo";
    const char *keyName = "PK";
    char const *g_filePath = "./schemaFile/AutoIncrement_001.gmjson";

    // 导入 schema
    GmcDropVertexLabel(g_stmt, labelName);

    if (g_envType == 2) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -ns %s -s %s", g_toolPath,
                    g_filePath, g_testNameSpace, g_connServer);
    } else if (g_envType == 1) {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -ns %s -s %s", g_toolPath,
                    g_filePath, g_testNameSpace, g_connServer);
    } else {
        snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vschema -f %s -s %s", g_toolPath, g_filePath, g_connServer);
    }
    ret = executeCommand(g_command, "Command type: import_vschema",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/02_DDL/024_AutoIncrement/schemaFile/AutoIncrement_001.gmjson\" successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < 100; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // drop vertex
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 42.创建一个表，主键带有自增属性；导入数据
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    char const *g_dataPath = "./schemaFile/demo.vertexdata";  //前缀必须是表名
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    const char *labelName = "demo";

    //导入data 插入数据
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c vdata -f %s -s %s -ns %s", g_toolPath, g_dataPath,
        g_connServer, g_testNameSpace);
    
    ret = executeCommand(g_command, "Insert data succeed. totalNum: 1, successNum: 1", "Command type: import_vdata",
        "GMDBV5/test/sdv/testcases/28_hardwareOffloading/001_BasicFunctions/02_DDL/024_AutoIncrement/schemaFile/demo.vertexdata\" successfully");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // drop vertex
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

char *label_schema = NULL;
const char *labelName = "OP_T0";
void *vertexLabel = NULL;
int start_num = 0;
int end_num = 1;
int array_num = 3;
int vector_num = 3;

void TestGmcSetNodePropertyByName_PK(GmcNodeT *root, int i)
{
    int ret = 0;
    uint32_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_R(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"F16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
void TestGmcSetNodePropertyByName_P(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    int8_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"P8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"P13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"P14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"P15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"P16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_A(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    int8_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"A8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"A13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"A14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"A15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"A16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGmcSetNodePropertyByName_V(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    int8_t f0_value = i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V0", GMC_DATATYPE_INT8, &f0_value, sizeof(int8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint8_t f1_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V1", GMC_DATATYPE_UINT8, &f1_value, sizeof(uint8_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int16_t f2_value = 2 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V2", GMC_DATATYPE_INT16, &f2_value, sizeof(int16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint16_t f3_value = 3 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V3", GMC_DATATYPE_UINT16, &f3_value, sizeof(uint16_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int32_t f4_value = 4 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V4", GMC_DATATYPE_INT32, &f4_value, sizeof(int32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint32_t f5_value = 5 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V5", GMC_DATATYPE_UINT32, &f5_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    int64_t f6_value = 6 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V6", GMC_DATATYPE_INT64, &f6_value, sizeof(int64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f7_value = 7 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V7", GMC_DATATYPE_UINT64, &f7_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcNodeSetPropertyByName(root, (char *)"V8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char f12_value = 1 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcNodeSetPropertyByName(root, (char *)"V13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"V14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"V15", GMC_DATATYPE_BYTES, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcNodeSetPropertyByName(root, (char *)"V16", GMC_DATATYPE_FIXED, f14_value, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void TestGmcGetNodePropertyByName_R(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F1", &f1_value, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F2", &f2_value, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F3", &f3_value, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F4", &f4_value, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F5", &f5_value, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F6", &f6_value, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F7", &f7_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"F13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    AW_MACRO_ASSERT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"F14", &propSize);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"F14", &string_value, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"F15", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"F16", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_p(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P0", &f0_value, sizeof(int8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(i, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P1", &f1_value, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P2", &f2_value, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P3", &f3_value, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P4", &f4_value, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P5", &f5_value, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P6", &f6_value, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P7", &f7_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"P13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    AW_MACRO_ASSERT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"P14", &propSize);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"P14", &string_value, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"P15", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"P16", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_A(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A0", &f0_value, sizeof(int8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(i, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A1", &f1_value, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A2", &f2_value, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A3", &f3_value, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A4", &f4_value, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A5", &f5_value, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A6", &f6_value, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A7", &f7_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"A13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    AW_MACRO_ASSERT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"A14", &propSize);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"A14", &string_value, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"A15", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"A16", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);
}

void TestGmcGetNodePropertyByName_V(GmcNodeT *root, int i, bool bool_value, char *f14_value)
{
    int ret = 0;
    bool isNull;
    int8_t f0_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V0", &f0_value, sizeof(int8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(i, f0_value);

    uint8_t f1_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V1", &f1_value, sizeof(uint8_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(1 * i, f1_value);

    int16_t f2_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V2", &f2_value, sizeof(int16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(2 * i, f2_value);

    uint16_t f3_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V3", &f3_value, sizeof(uint16_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(3 * i, f3_value);

    int32_t f4_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V4", &f4_value, sizeof(int32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(4 * i, f4_value);

    uint32_t f5_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V5", &f5_value, sizeof(uint32_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(5 * i, f5_value);

    int64_t f6_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V6", &f6_value, sizeof(int64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(6 * i, f6_value);

    uint64_t f7_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V7", &f7_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(7 * i, f7_value);

    bool f8_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V8", &f8_value, sizeof(bool), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(bool_value, f8_value);

    float f9_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V9", &f9_value, sizeof(float), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(9 * i, f9_value);

    double f10_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V10", &f10_value, sizeof(double), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(10 * i, f10_value);

    uint64_t f11_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V11", &f11_value, sizeof(uint64_t), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(11 * i, f11_value);

    char f12_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V12", &f12_value, sizeof(char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(1 * i, f12_value);

    unsigned char f13_value;
    ret = GmcNodeGetPropertyByName(root, (char *)"V13", &f13_value, sizeof(unsigned char), &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    unsigned char k = (unsigned char)(13 * i);
    AW_MACRO_ASSERT_EQ_INT(k, f13_value);

    unsigned int propSize;
    ret = GmcNodeGetPropertySizeByName(root, (char *)"V14", &propSize);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(propSize, strlen(f14_value) + 1);

    char string_value[7];
    ret = GmcNodeGetPropertyByName(root, (char *)"V14", &string_value, propSize, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"V15", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);

    ret = GmcNodeGetPropertyByName(root, (char *)"V16", &string_value, 7, &isNull);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(strcmp(string_value, f14_value), 0);
}

void TestGmcInsertVertex(GmcStmtT *stmt, int index, bool bool_value, char *f14_value, int start_num, int end_num,
    int array_num, int vector_num, const char *labelName)
{
    int32_t ret = 0;
    void *label = NULL;
    GmcNodeT *root, *T1, *T2, *T3;

    // 插入顶点
    for (int i = start_num; i < end_num; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        //设置根节点
        ret = GmcGetRootNode(stmt, &root);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // TestGmcSetNodePropertyByName_PK(root, i * index); 不能手动设置 F0 的值
        TestGmcSetNodePropertyByName_R(root, i * index, bool_value, f14_value);

        //设置T1节点
        ret = GmcNodeGetChild(root, "T1", &T1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        TestGmcSetNodePropertyByName_P(T1, i * index, bool_value, f14_value);

        // 插入array节点
        ret = GmcNodeGetChild(T1, "T2", &T2);  // T2 是在 T1 下，故第一个参数应该是 T1
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t j = 0; j < array_num; j++) {
            TestGmcSetNodePropertyByName_A(T2, i * index, bool_value, f14_value);
            ret = GmcNodeGetNextElement(T2, &T2);
            if (j < array_num - 1) {
                AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            } else {
                AW_MACRO_ASSERT_EQ_INT(GMERR_NO_DATA, ret);
            }
        }

        // 插入vector节点
        ret = GmcNodeGetChild(root, "T3", &T3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        for (uint32_t j = 0; j < vector_num; j++) {
            ret = GmcNodeAppendElement(T3, &T3);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            TestGmcSetNodePropertyByName_V(T3, i * index, bool_value, f14_value);
        }
        ret = GmcExecute(stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
}
/*****************************************************************************
 * Description  : tree模型，自增字段定义在根节点
 * Input        : None
 * Output       : None
 * Return Value :
 * Notes        :
 * History      :
 * Author       : daipengjie/dwx992801   2021.03.22
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *g_label_config = R"(
    {
        "max_record_count":10000,
        "writers":"abc",
        "auto_increment":1,
        "isFastReadUncommitted":1
    })";

    readJanssonFile("schemaFile/NormalTreeModel.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    char label_name1[] = "OP_T0";
    LOG("label_schema:");
    LOG("%s", label_schema);
    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    TestGmcInsertVertex(g_stmt, 1, 0, (char *)"string", start_num, 21, array_num, vector_num, labelName);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 含自增列属性的表，配置项为0，预期建表成功 DTS2021092325502
 * ，自增列起始值修改为0，范围为[0,类型最大值]，2021.9.24 Author       : wanghongyan/wwx635828   2021.09.23 Modification
 * : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":0,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_001.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);
    LOG("label_schema:");
    LOG("%s", label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema,
        g_label_config);  // 2021.9.29 修改自增列起始值为0，nullable 属性为0时，建表失败，廖美丰(00311335)
    AW_MACRO_ASSERT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    /*
        const char *labelName = "demo";
        //获取顶点label
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

        // 插入顶点
        int32_t i = 1;
        set_PK_F0_uint32(g_stmt, i); // 2021.09.03 之前不允许同时对 自增属性（F0） 设值
        set_Property(g_stmt, i, 0); // 2021.09.03 现在允许修改自增属性值（F0）
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //关闭顶点label
        ret = GmcDropVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    */
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 导入与导出自增属性的配置文件一致
 * Author       : wanghongyan/wwx635828
 * Modification : Create function
 * *****************************************************************************/
TEST_F(AutoIncrement, DISABLED_TODEL_HardWare_Offloading_001_DDL_024_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *expect = (char *)"./compare_log/expect_047.log";
    char *actual = (char *)"actual_047.log";
    char schema_file[128] = "schemaFile/AutoIncrement_001_test.gmjson";
    const char *labelName = "AutoIncrement_001_test";

    //导入的配置文件中自增列属性为0，但表不含自增列属性，预期导入成功
    snprintf(cmd, 1024, "%s/gmimport  -c vschema -f %s -s %s", g_toolPath, schema_file,
        g_connServer);  // 2021.11.24 g_runMode 自动适配各种运行环境
    system(cmd);

    //检查导入表的正确性
    snprintf(cmd, 1024,
        "%s/gmsysview -s %s -q V\\$CATA_VERTEX_LABEL_INFO  -f  "
        "VERTEX_LABEL_NAME=\'AutoIncrement_001_test\' |grep -A32 AutoIncrement_001_test |grep auto_increment > "
        "actual_047.log",
        g_toolPath, g_connServer);  // 2021.11.24 g_runMode 自动适配各种运行环境
    system(cmd);

    ret = compare_file_content(actual, expect, 1024);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : schema中的第二层单节点上的一个整型字段带有auto_increment属性
 * Author       : wanghongyan/wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_003.gmjson", g_label_config, (char *)"AutoIncrement_003",
        GMERR_INVALID_TABLE_DEFINITION);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "AutoIncrement_003", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : gmjson中既有主键（F1）也有localhash(F0)，自增列值只存在localhash上
 * Author       : wanghongyan/wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_015";

    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_015.gmjson", g_label_config, (char *)"AutoIncrement_015",
        GMERR_INVALID_JSON_CONTENT);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : gmjson中有2个localhash(F0，F1)，(F1),自增列值存在F0上
 * Author       : wanghongyan/wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_018";

    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_018.gmjson", g_label_config, (char *)"AutoIncrement_018",
        GMERR_INVALID_TABLE_DEFINITION);  // 2021.11.06 DTS2021110117284 自增列必须在主键,不能在其他索引

    //获取顶点label
    /*ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    // 插入顶点
    for (int i = 0; i < WRITE_NUM; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, "AutoIncrement_018");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);   */
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  :
 * schema中的根节点上的uint32字段带有auto_increment属性，初始值为4294967296,uint32取值范围在0~4294967295之间。 Author :
 * wanghongyan/wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":4294967296,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_001.gmjson", g_label_config, (char *)"AutoIncrement_001",
        GMERR_INVALID_PROPERTY);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : schema中的根节点上的uint64字段带有auto_increment属性，初始值为18446744073709551610, UInt64 ∈ [0 :
 18446744073709551615] Int64 - [-9223372036854775808 : 9223372036854775807] 唐鹏亮(00587092)2021-09-27 11:02
 因为json格式只能支持定义到int64的最大值。 超过int64 json就会报错
 * Author       : wanghongyan/wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":9223372036854775807,
            "isFastReadUncommitted":0
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_002.gmjson", g_label_config, (char *)"demo", GMERR_OK);
    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, "demo");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : schema中的根节点上的uint64字段带有auto_increment属性，初始值为18446744073709551610, UInt64 ∈ [0 :
 * 18446744073709551615] Author       : wanghongyan/wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":9223372036854775808,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_002.gmjson", g_label_config, (char *)"demo",
        GMERR_INVALID_JSON_CONTENT);  //日志打印有问题
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : schema中的根节点上的uint64字段带有auto_increment属性，初始值为18446744073709551610, UInt64 ∈ [0 :
 * 18446744073709551615] Author       : wanghongyan/wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_053_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_JSON_CONTENT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":18446744073709551610,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_002.gmjson", g_label_config, (char *)"demo",
        GMERR_INVALID_JSON_CONTENT);  //日志打印有问题
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : schema中的根节点上的uint64字段带有auto_increment属性，初始值为9223372036854775800
 * Author       : wanghongyan/wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":100000,
            "writers":"abc",
            "auto_increment":9223372036854775800,
            "isFastReadUncommitted":1
        })";
    //当前自增属性初始值超过int64建表会报错，但是写值可以超过int64 的大小    2021.9.27
    create_Lable_Name((char *)"schemaFile/AutoIncrement_002.gmjson", g_label_config, (char *)"demo", GMERR_OK);

    const char *labelName = "demo";
    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int i = 0; i < 10000; i++) {
        set_PK_F0_uint32(g_stmt, i);
        set_Property_F1_AutoIncrement(g_stmt, i, 0);  //当前边界值达不到
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : schema中的根节点上的uint64字段带有auto_increment属性，初始值为-1
 * Author       : wanghongyan/wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":-1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_002.gmjson", g_label_config, (char *)"AutoIncrement_002",
        GMERR_DATATYPE_MISMATCH);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : schema中的根节点上的uint32字段带有auto_increment属性，初始值为-1
 * Author       : wanghongyan/wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":-1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_001.gmjson", g_label_config, (char *)"AutoIncrement_001",
        GMERR_DATATYPE_MISMATCH);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : schema中的根节点上的uint32字段带有auto_increment属性，初始值为NULL
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_056_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":null,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_001.gmjson", g_label_config, (char *)"AutoIncrement_001",
        GMERR_DATATYPE_MISMATCH);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : schema中的根节点上的uint32字段带有auto_increment属性，初始值为1，写入直到最大值
 * 4294967195(硬件卸载新增限制，hash索引上限约为2200w)，并超过最大值 Author       : wanghongyan/wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    char g_label_config[96];

#if defined ENV_RTOSV2X
    uint32_t autoIncStart = 4290000000;  // 2022.01.04 减小用例执行时间
#elif defined ENV_RTOSV2
    uint32_t autoIncStart = 4294900000;  // 2022.01.04 减小用例执行时间
#else
    uint32_t autoIncStart = 4200000000;  // 2022.01.04 减小用例执行时间
#endif


    snprintf(g_label_config, sizeof(g_label_config),
        "{\"max_record_count\":4294967200, \"writers\":\"abc\", \"auto_increment\":%u }", autoIncStart);

    create_Lable_Name((char *)"schemaFile/AutoIncrement_001.gmjson", g_label_config, (char *)"demo", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, "demo", GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 插入顶点
    for (int64_t i = autoIncStart; i < 4294967200; i++) {
        set_Property(g_stmt, i, 0);
        ret = GmcExecute(g_stmt);
        if (i < 4294967196) {
            if (ret != GMERR_OK) {
                break;
            }
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, "demo");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为主键时，启动事务，删除表中的部分数据，事务rollback,再写入数据
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "demo";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";
    char Label_config[] = "{\"max_record_count\":100000}";
    create_Lable_Name((char *)"schemaFile/AutoIncrement_001.gmjson", g_label_config, (char *)"demo", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_001(1, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除头
    ret = remove_AutoIncrement_001(1, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写头
    ret = write_data_AutoIncrement_001(1, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除中间
    ret = remove_AutoIncrement_001(6, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写中间
    ret = write_data_AutoIncrement_001(6, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除尾
    ret = remove_AutoIncrement_001(9, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写尾
    ret = write_data_AutoIncrement_001(9, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为非主键时，启动事务，删除表中的部分数据，事务rollback,再写入数据
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "demo";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";

    char Label_config[] = "{\"max_record_count\":100000}";
    create_Lable_Name((char *)"schemaFile/AutoIncrement_002.gmjson", g_label_config, (char *)"demo", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_002(0, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除头
    ret = remove_AutoIncrement_002(0, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写头
    ret = write_data_AutoIncrement_002(0, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除中间
    ret = remove_AutoIncrement_002(6, 8);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写中间
    ret = write_data_AutoIncrement_002(6, 7);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除尾
    ret = remove_AutoIncrement_002(9, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写尾
    ret = write_data_AutoIncrement_002(9, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为主键时，清空表中的全部数据，再写一条记录,自增列回到起始值
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "demo";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_001.gmjson", g_label_config, (char *)"demo", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_001(1, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //清空表
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_001(1, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为非主键时，清空表中的全部数据，再写一条记录,自增列回到起始值
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "demo";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_002.gmjson", g_label_config, (char *)"demo", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_002(0, 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //清空表
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_002(0, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为主键时，local key扫描，local key为单字段索引
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "demo";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";
    char Label_config[] = "{\"max_record_count\":100000}";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_001.gmjson", g_label_config, (char *)"demo", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_001(0, WRITE_NUM);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localkey扫描
    ret = vertex_scan_by_local_key_uint32(
        g_stmt, (char *)"demo", 1, WRITE_NUM, GMC_COMPARE_RANGE_CLOSED, GMC_COMPARE_RANGE_CLOSED, 3333);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = vertex_scan_by_local_key_uint32(
        g_stmt, (char *)"demo", 1, WRITE_NUM, GMC_COMPARE_RANGE_OPEN, GMC_COMPARE_RANGE_OPEN, 3333);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = vertex_scan_by_local_key_uint32(
        g_stmt, (char *)"demo", 1, WRITE_NUM, GMC_COMPARE_RANGE_CLOSED, GMC_COMPARE_RANGE_OPEN, 3333);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = vertex_scan_by_local_key_uint32(
        g_stmt, (char *)"demo", 1, WRITE_NUM, GMC_COMPARE_RANGE_OPEN, GMC_COMPARE_RANGE_CLOSED, 3333);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //删除部分数据后重新扫描
    ret = remove_AutoIncrement_001(1, WRITE_NUM / 10);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = remove_AutoIncrement_001((WRITE_NUM / 10) * 9 + 1, WRITE_NUM);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = vertex_scan_by_local_key_uint32(
        g_stmt, (char *)"demo", 1, WRITE_NUM, GMC_COMPARE_RANGE_CLOSED, GMC_COMPARE_RANGE_CLOSED, 2334);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列在主键时，且在第二个字段上，local key扫描，local key为单字段自增列索引
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "demo";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name((char *)"schemaFile/AutoIncrement_030.gmjson", g_label_config, (char *)"demo",
        GMERR_INVALID_TABLE_DEFINITION);  // 2021.11.06 DTS2021110117284 自增列必须在主键的第一个字段

    //获取顶点label
    /*ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    ret = write_data_AutoIncrement((char*)"demo", 0, WRITE_NUM, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    //localkey扫描
    ret = vertex_scan_by_local_key_uint64(g_stmt, 1, WRITE_NUM, GMC_COMPARE_RANGE_CLOSED, GMC_COMPARE_RANGE_CLOSED,
    WRITE_NUM); AW_MACRO_ASSERT_EQ_INT(GMERR_OK,ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);    */
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为主键时，且在第一个字段上，local key扫描，local key为单字段索引
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_035";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_035.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 0, WRITE_NUM, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localkey扫描
    ret = vertex_scan_by_local_key_uint32(
        g_stmt, (char *)"AutoIncrement_035", 1, WRITE_NUM, GMC_COMPARE_RANGE_CLOSED, GMC_COMPARE_RANGE_CLOSED, 3333);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为主键时，且在第一个字段上，localhash key扫描，localhash key为单字段索引
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_035";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_035.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 0, WRITE_NUM, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash扫描
    ret = vertex_scan_by_index_key(g_stmt, (char *)"AutoIncrement_035", "localhash_01", 1, GMC_DATATYPE_UINT32, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为主键时，且在第一个字段上，localhash 更新，localhash key为单字段索引
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_035";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_035.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 0, WRITE_NUM, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash更新
    ret = update_by_index(g_stmt, (char *)"AutoIncrement_035", "localhash_01", 1, WRITE_NUM, 999);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列在主键上，localhash key删除部分数据，自增列值不变，再新增数据，自增列值继续单调递增
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_035";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";

    char Label_config[] = "{\"max_record_count\":100000}";
    create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_035.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 0, 3, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash删除，删除开头
    ret = remove_by_index(g_stmt, (char *)"AutoIncrement_035", "localhash_01", 0, 5, GMC_DATATYPE_UINT32);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写
    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 3, 6, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash删除，删除中间
    ret = remove_by_index(g_stmt, (char *)"AutoIncrement_035", "localhash_01", 6, 8, GMC_DATATYPE_UINT32);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写
    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 6, 3, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // localhash删除，删除末尾
    ret = remove_by_index(g_stmt, (char *)"AutoIncrement_035", "localhash_01", 9, 10, GMC_DATATYPE_UINT32);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写
    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 9, 3, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为主键时，且在第一个字段上，hashcluster 更新，hashcluster为单字段索引
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_035";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_036.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 0, WRITE_NUM, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // hashcluster更新
    ret = update_by_index(g_stmt, (char *)"AutoIncrement_035", "hashcluster_01", 1, WRITE_NUM, 999);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列在主键上，hashcluster删除部分数据，自增列值不变，再新增数据，自增列值继续单调递增
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_035";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":0
        })";

    char Label_config[] = "{\"max_record_count\":100000}";
    create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_036.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 0, 3, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // hashcluster删除，删除开头
    ret = remove_by_index(g_stmt, (char *)"AutoIncrement_035", "hashcluster_01", 0, 5, GMC_DATATYPE_UINT32);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写
    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 3, 6, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // hashcluster删除，删除中间
    ret = remove_by_index(g_stmt, (char *)"AutoIncrement_035", "hashcluster_01", 6, 8, GMC_DATATYPE_UINT32);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写
    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 6, 3, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // hashcluster删除，删除末尾
    ret = remove_by_index(g_stmt, (char *)"AutoIncrement_035", "hashcluster_01", 9, 10, GMC_DATATYPE_UINT32);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //重新写
    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 9, 3, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为主键时，且在第一个字段上，hashcluster key扫描，hashcluster key为单字段索引
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_035";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_036.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 0, WRITE_NUM, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // hashcluster扫描
    ret = vertex_scan_by_index_key(g_stmt, (char *)"AutoIncrement_035", "hashcluster_01", 1, GMC_DATATYPE_UINT32, 0);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为主键时，且在第一个字段上，批量操作写入数据，自增列值单调递增
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_035";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_036.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_batch(g_stmt, (char *)"AutoIncrement_035", WRITE_NUM, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为主键时，且在第一个字段上，批量操作更新写入数据，自增列值单调递增
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_035";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_036.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_batch(g_stmt, (char *)"AutoIncrement_035", WRITE_NUM, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = update_AutoIncrement_batch(g_stmt, (char *)"AutoIncrement_035", WRITE_NUM, 888);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 自增列为主键时，且在第一个字段上，批量操作删除写入数据，自增列值单调递增
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    void *vertexLabel = NULL;
    const char *labelName = "AutoIncrement_035";
    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_036.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);

    //获取顶点label
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_batch(g_stmt, (char *)"AutoIncrement_035", WRITE_NUM, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = remove_AutoIncrement_batch(g_stmt, (char *)"AutoIncrement_035", WRITE_NUM);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 条件订阅自增序列字段，F0自增列字段值存在，触发回调函数
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcConnT *g_conn_sub = NULL;
    GmcStmtT *g_stmt_sub = NULL;
    int g_chanRingLen = 256;
    char *g_subName = (char *)"subVertexLabel";
    char *g_subConnName = (char *)"subConnName";

    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_036.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSnMallocUserData(&g_userData, 100);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = create_sub_relation(
        (char *)"schemaFile/subinfo_condition_pk.gmjson", g_subName, stmt, g_conn_sub, vertexSnCallback, g_userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_only_auto((char *)"AutoIncrement_035", 0, 100, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(g_userData);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 条件订阅自增序列字段，F0自增列字段值不存在，不触发回调函数，推送为0
 * Author       : wanghongyan wwx635828
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    GmcConnT *g_conn_sub = NULL;
    GmcStmtT *g_stmt_sub = NULL;
    int g_chanRingLen = 256;
    char *g_subName = (char *)"subVertexLabel";
    char *labelName = (char *)"demo";
    char *g_subConnName = (char *)"subConnName";

    const char *g_label_config = R"(
        {
            "max_record_count":1000000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_conn_sub, &g_stmt_sub, 1, g_epoll_reg_info, g_subConnName, &g_chanRingLen);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_036.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSnMallocUserData(&g_userData, 100);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = create_sub_relation(
        (char *)"schemaFile/subinfo_condition_pk_01.gmjson", g_subName, stmt, g_conn_sub, vertexSnCallback, g_userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 0, 100, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(g_userData, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(g_userData);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : schema中的第二层单节点上的一个整型字段带有auto_increment属性
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    readJanssonFile("schemaFile/AutoIncrement_004.gmjson", &label_schema);
    ASSERT_NE((void *)NULL, label_schema);

    ret = GmcCreateVertexLabel(g_stmt, label_schema, g_label_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  :
 * schema中的根节点上的非uint32/uint64字段（boolean/int8/int16/int32/int64/uint8/uint16/float/double/time/partition//bitmap?/fixed?/bytes/string/record?/resource?）带有auto_increment属性
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    string abnormal_type[13] = {"boolean", "int8", "int16", "int32", "int64", "uint8", "uint16", "float", "double",
        "time", "partition", "bytes", "string"};
    string vertex_label_abnormal_type[13] = {""};
    const char *vertex_label_abnormal_type_ch[13] = {""};
    for (int i = 0; i < 13; i++) {
        vertex_label_abnormal_type[i] =
            R"([ {"type" : "record","name" : "demo",
        "fields" : [
                {"name" : "F0", "type" : "uint32", "nullable" : true},
                {"name":"F1", "type":")" +
            abnormal_type[i] + R"(","nullable":true, "auto_increment" : true},
                {"name":"F2", "type":"int32",   "nullable":true},
                {"name":"F3", "type":"uint32",  "nullable":true},
                {"name":"F4", "type":"int16",   "nullable":true},
                {"name":"F5", "type":"uint16",  "nullable":true},
                {"name":"F6", "type":"int8",    "nullable":true},
                {"name":"F7", "type":"uint8",   "nullable":true},
                {"name":"F8", "type":"boolean", "nullable":true},
                {"name":"F9", "type":"float",   "nullable":true},
                {"name":"F10", "type":"double", "nullable":true},
                {"name":"F11", "type":"time",   "nullable":true},
                {"name":"F12", "type":"char",   "nullable":true}
            ],
            "keys" : [
                {
                    "node" : "demo",
                    "name" : "PK",
                    "fields" : ["F0"],
                    "index" : {"type" : "primary"},
                    "constraints" : {"unique" : true}
                }
            ]
        } ])";
        vertex_label_abnormal_type_ch[i] = vertex_label_abnormal_type[i].c_str();
        // cout<<endl<<vertex_label_abnormal_type_ch[i]<<endl;
    }
    char *label_schema = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    for (int i = 0; i < 13; i++) {
        // cout<<endl<<"i===="<<i<<endl;
        ret = GmcCreateVertexLabel(g_stmt, vertex_label_abnormal_type_ch[i], g_label_config);
        AW_MACRO_EXPECT_EQ_INT(GMERR_DATATYPE_MISMATCH, ret);
    }
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 复杂表schema中有auto_increment属性字段，进行对账操作
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_078)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1
        })";

    // 读取label schema
    ret = create_Lable_Name((char *)"schemaFile/AutoIncrement_001.gmjson", g_label_config, (char *)"demo", GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *labelName = "demo";
    bool isAbnormal = false;

    // 插值1：
    ret = write_data_AutoIncrement_001(1, 2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //开启对账
    ret = GmcBeginCheck(g_stmt, labelName, FULLTABLE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = write_data_AutoIncrement_001(2, 3);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //结束对账
    ret = GmcEndCheck(g_stmt, labelName, FULLTABLE, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;
    ret = GmcGetCheckInfo(g_stmt, labelName, GMC_FULL_TABLE, &checkInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(checkStatus, GMC_CHECK_STATUS_NORMAL);

    //重新写入数据
    ret = write_data_AutoIncrement_001(3, 4);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(label_schema);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/*****************************************************************************
 * Description  : 不共用schema，不同表的自增字段值不共享
 * *****************************************************************************/
TEST_F(AutoIncrement, HardWare_Offloading_001_DDL_024_079)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char *label_schema = NULL;
    const char *g_label_config = R"(
        {
            "max_record_count":10000,
            "writers":"abc",
            "auto_increment":1,
            "isFastReadUncommitted":1
        })";

    // 读取label schema
    ret = create_Lable_Name((char *)"schemaFile/AutoIncrement_001.gmjson", g_label_config, (char *)"demo", GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读取label schema
    ret = create_Lable_Name(
        (char *)"schemaFile/AutoIncrement_035.gmjson", g_label_config, (char *)"AutoIncrement_035", GMERR_OK);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //给AutoIncrement_001写值
    ret = write_data_AutoIncrement_001(0, WRITE_NUM);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //给AutoIncrement_035写值
    ret = write_data_AutoIncrement((char *)"AutoIncrement_035", 0, WRITE_NUM, 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    //关闭顶点label
    ret = GmcDropVertexLabel(g_stmt, (char *)"demo");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, (char *)"AutoIncrement_035");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
