/*
yang表bool类型校验
052 list表中bool类型字段设置为primary key，预期建表成功
053 list表中list_localhash包含bool类型字段，预期建表成功
054 contaier主键字段包含bool类型，预期建表失败
055 case主键字段包含bool类型，预期建表失败
056 choice主键字段包含bool类型，预期建表失败
057 leaflist主键字段包含bool类型，预期建表成功
058 leaflist的PID字段为bool，预期建表失败
059 leaf-list增加list_localhash索引，预期建表失败
060 case增加list_localhash索引，预期建表失败
061 list表主键包含bool类型，进行更新、删除等DML操作，预期成功
062 list表主键包含bool类型，写数据主键冲突，预期成功
063 list表list_localhash包含bool类型，唯一性冲突，预期成功
064 list表list_localhash字段都为bool类型，校验唯一性冲突，预期成功
065 list表16个list_localhash索引都为bool类型
066 list表17个list_localhash索引都为bool类型，预期建表失败
067 list表list_localhash含bool和null值，数据写入成功
068 list表list_localhash含bool且该字段设置为默认值，校验唯一性冲突，预期成功
069 list表list_localhash含bool，字段个数为32，预期成功
070 list表list_localhash含bool，字段个数为33，预期失败
071 list表主键索引含bool，字段个数为32，预期建表成功
072 list表主键索引含bool，字段个数为33，预期建表失败
073 list-container，list_localhash含bool，不同层节点组成唯一性索引
074 多层list-container-choicecase-container含bool，校验字段个数
075 list下的choice有多个case，每个case有bool类型字段，将这些bool字段作为list_locahash索引，建表成功，不报唯一性冲突（多个case写数据只保留最后一个case的数据）
076 list表，list_localhash含bool，通过merge校验唯一性冲突
077 list表，list_localhash含bool，通过update校验唯一性冲突
*/

#include "IndexBool.h"

class BoolYang : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void BoolYang::SetUpTestCase()
{
    int ret = 0;
    // 配置相关环境变量及重启server
    InitCfg();
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void BoolYang::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复配置文件
    RecoverCfg();
}

void BoolYang::SetUp()
{
    // 建连
    const char *namespaceUserName = "abc";
    int ret = testGmcConnect(&g_connAsync, &g_stmtAsync, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcDropVertexLabel(g_stmt, g_labelName3);
    GmcDropNamespace(g_stmt, g_nspName);

    GmcNspCfgT nspCfg;
    nspCfg.tablespaceName = NULL;
    nspCfg.namespaceName = g_nspName;
    nspCfg.userName = namespaceUserName;
    nspCfg.trxCfg = {GMC_OPTIMISTIC_TRX, GMC_TX_ISOLATION_REPEATABLE}; // 可重复读+乐观

    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateNamespaceWithCfgAsync(g_stmtAsync, &nspCfg, create_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    ret = GmcUseNamespaceAsync(g_stmtAsync, g_nspName, use_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
}

void BoolYang::TearDown()
{
    AW_CHECK_LOG_END();
    // 断连
    int ret;
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcDropNamespaceAsync(g_stmtAsync, g_nspName, drop_namespace_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    memset(&data, 0, sizeof(AsyncUserDataT));

    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connAsync, g_stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 052 list表中bool类型字段设置为primary key，预期建表成功
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "PID为bool建表失败");
    readJanssonFile("./schemaFile/052ListPkPidBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    AW_FUN_Log(LOG_STEP, "PID为uint32建表");
    readJanssonFile("./schemaFile/052ListPkBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    
    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabelAsync(g_stmtAsync, g_labelName3, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 053 list表中list_localhash包含bool类型字段，预期建表成功
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "list_localhash包含bool建表");
    readJanssonFile("./schemaFile/053List_localhashBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    
    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabelAsync(g_stmtAsync, g_labelName3, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 054 contaier主键字段包含bool类型，预期建表失败
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "ID为bool建表失败");
    readJanssonFile("./schemaFile/054ContainerIDBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    AW_FUN_Log(LOG_STEP, "containerPk包含bool建表");
    readJanssonFile("./schemaFile/054ContainerSegBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    
    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabelAsync(g_stmtAsync, g_labelName3, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 055 case主键字段包含bool类型，预期建表失败
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "CasePk包含bool建表");
    readJanssonFile("./schemaFile/055CasePKBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 056 choice主键字段包含bool类型，预期建表失败
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "choicePk包含bool建表");
    readJanssonFile("./schemaFile/056ChoicePKBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 057 leaflist主键字段包含bool类型，预期建表成功
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    
    AW_FUN_Log(LOG_STEP, "leaflistPk包含bool建表");
    readJanssonFile("./schemaFile/057LeaflisPKBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    
    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabelAsync(g_stmtAsync, g_labelName3, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 058 leaflist的PID字段为bool，预期建表失败
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "leaflistPID包含bool建表");
    readJanssonFile("./schemaFile/058LeaflistPIDBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 059 leaf-list增加list_localhash索引，预期建表失败
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "leaflist增加list_localhash索引建表");
    readJanssonFile("./schemaFile/059LeaflistAddListlocalhash.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 060 case增加list_localhash索引，预期建表失败
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "case增加list_localhash索引建表");
    readJanssonFile("./schemaFile/060CaseAddListlocalhash.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 061 list表主键包含bool类型，进行更新、删除等DML操作，预期成功
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "PK含bool建表");
    readJanssonFile("./schemaFile/061ListPkBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schemaFile/unique_edgeLabel.gmjson", &g_edgeLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_edgeLabelJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    
    AW_FUN_Log(LOG_STEP, "写数据");
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_connAsync, &batch);

    TestInsertBoolData(g_connAsync, g_stmtAsync, 1, 10, 1);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "061Basic");

    AW_FUN_Log(LOG_STEP, "Replace数据");
    TestReplace(g_connAsync, g_stmtAsync, false, 2, 2, 11, 11);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "061BasicReplace");

    AW_FUN_Log(LOG_STEP, "delete数据");
    TestDelete(g_connAsync, g_stmtAsync, 6, 6);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "061BasicDelete");

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_nspName);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 062 list表主键包含bool类型，写数据主键冲突，预期成功
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0}, errorMsg2[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(2, errorMsg, errorMsg2);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "PK含bool建表");
    readJanssonFile("./schemaFile/061ListPkBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schemaFile/unique_edgeLabel.gmjson", &g_edgeLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_edgeLabelJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    
    AW_FUN_Log(LOG_STEP, "写数据");
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_connAsync, &batch);

    TestInsertBoolData(g_connAsync, g_stmtAsync, 1, 10, 1);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "061Basic");

    AW_FUN_Log(LOG_STEP, "写入一条数据造成主键冲突");
    TestInsertBoolData(g_connAsync, g_stmtAsync, 1, 1, 1);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "061Basic");

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_nspName);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 063 list表list_localhash包含bool类型，唯一性冲突，预期成功
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "list_localhash含bool建表");
    readJanssonFile("./schemaFile/063ListLocalhashBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schemaFile/unique_edgeLabel.gmjson", &g_edgeLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_edgeLabelJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    
    AW_FUN_Log(LOG_STEP, "写数据");
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_connAsync, &batch);

    TestInsertBoolData(g_connAsync, g_stmtAsync, 1, 10, 0, 0, 0, 1, 1);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "063Basic");

    AW_FUN_Log(LOG_STEP, "写入一条数据造成唯一性冲突");
    TestInsertBoolData(g_connAsync, g_stmtAsync, 12, 12, 0, 0, 0, 1, 1, 1);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "063Basic");

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_nspName);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 064 list表list_localhash字段都为bool类型，校验唯一性冲突，预期成功
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "list_localhash含bool建表");
    readJanssonFile("./schemaFile/064ListLocalhashBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schemaFile/unique_edgeLabel.gmjson", &g_edgeLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_edgeLabelJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    
    AW_FUN_Log(LOG_STEP, "写数据");
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_connAsync, &batch);

    TestInsertBoolData(g_connAsync, g_stmtAsync, 1, 2, 0, 0, 0, 1, 1);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "064Basic");

    AW_FUN_Log(LOG_STEP, "写入一条数据造成唯一性冲突");
    TestInsertBoolData(g_connAsync, g_stmtAsync, 3, 3, 0, 0, 0, 1, 1, 1);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "064Basic");

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_nspName);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 065 list表16个list_localhash索引都为bool类型
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "16个含bool的list_localhash索引建表");
    readJanssonFile("./schemaFile/065ListLocalhashIndex16.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schemaFile/unique_edgeLabel.gmjson", &g_edgeLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_edgeLabelJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_nspName);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 066 list表17个list_localhash索引都为bool类型，预期建表失败
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "17个list_localhash索引建表失败");
    readJanssonFile("./schemaFile/066ListLocalhashIndex17.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 067 list表list_localhash含bool和null值，数据写入成功
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "list_localhash含bool建表");
    readJanssonFile("./schemaFile/067ListLocalhashBoolAndNull.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schemaFile/unique_edgeLabel.gmjson", &g_edgeLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_edgeLabelJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    
    AW_FUN_Log(LOG_STEP, "写数据");
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_connAsync, &batch);

    TestInsertBoolData(g_connAsync, g_stmtAsync, 1, 10, 0, 0, 0, 1, 1, 0, 0);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "067Basic");

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_nspName);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 068 list表list_localhash含bool且该字段设置为默认值，校验唯一性冲突，预期成功
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_068)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "list_localhash含bool建表");
    readJanssonFile("./schemaFile/068ListLocalhashBoolAndDefault.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schemaFile/unique_edgeLabel.gmjson", &g_edgeLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_edgeLabelJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    
    AW_FUN_Log(LOG_STEP, "写数据");
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_connAsync, &batch);

    TestInsertBoolData(g_connAsync, g_stmtAsync, 1, 2, 0, 0, 0, 1, 1, 0, 0);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "068Basic");
    system("gmsysview subtree -rn T0 -ns NspBoolYang");

    AW_FUN_Log(LOG_STEP, "再次写入一条数据，默认值不填");  // 报唯一性冲突
    TestInsertBoolData(g_connAsync, g_stmtAsync, 3, 3, 0, 0, 0, 1, 1, 1, 0);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "068Basic");

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_nspName);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 069 list表list_localhash含bool，字段个数为32
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_069)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "主键索引包含32个字段建表");
    readJanssonFile("./schemaFile/069ListlocalhashIndex32Seg.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabelAsync(g_stmtAsync, g_labelName3, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 070 list表list_localhash含bool，字段个数为33，预期失败
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_070)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "list_localhash索引包含33个字段建表失败");
    readJanssonFile("./schemaFile/070ListlocalhashIndex33Seg.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 071 list表主键索引含bool，字段个数为32，预期建表成功
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_071)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "主键索引包含32个字段建表");
    readJanssonFile("./schemaFile/071PKIndex32Seg.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabelAsync(g_stmtAsync, g_labelName3, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 072 list表主键索引含bool，字段个数为33，预期建表失败
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_072)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "主键索引包含33个字段建表失败");
    readJanssonFile("./schemaFile/072PKIndex33Seg.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 073 list-container，list_localhash含bool，不同层节点组成唯一性索引
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_073)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "list_localhash包含多层字段建表");
    readJanssonFile("./schemaFile/073ListloclahashMultiIndex.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabelAsync(g_stmtAsync, g_labelName3, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 074 多层list-container-choicecase-container含bool，校验字段个数
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_074)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_ARRAY_SUBSCRIPT_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "list_localhash包含多层33个字段建表失败");
    readJanssonFile("./schemaFile/074MultiYangTypeErr.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_ARRAY_SUBSCRIPT_ERROR, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;

    AW_FUN_Log(LOG_STEP, "list_localhash包含多层字段建表");
    readJanssonFile("./schemaFile/074MultiYangType.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabelAsync(g_stmtAsync, g_labelName3, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 075 list下的choice有多个case，每个case有bool类型字段，将这些bool字段作为list_locahash索引，建表成功，不报唯一性冲突（多个case写数据只保留最后一个case的数据）
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_075)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char errorMsg[128] = {};
    (void)snprintf(errorMsg, sizeof(errorMsg), "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret;
    AW_FUN_Log(LOG_STEP, "list_localhash含bool建表");
    readJanssonFile("./schemaFile/075ListChoiceCase.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schemaFile/unique_edgeLabel.gmjson", &g_edgeLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_edgeLabelJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, g_edgeLabelJson, g_msConfigTrans, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));

    uint32_t fieldValue;
    uint32_t ID;
    uint32_t PID;
    GmcBatchT *batch = NULL;

    uint32_t i = 0;
    ret = GmcAllocStmt(g_connAsync, &g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (i = 0; i < 10; i++) {
        ret = GmcAllocStmt(g_connAsync, &g_stmt_list[i]);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 启动事务
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_REPEATABLE;
    config.trxType = GMC_OPTIMISTIC_TRX;
    config.readOnly = false;
    ret = testTransStartAsync(g_connAsync, config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置批处理batch参数
    ret = TestBatchPrepare(g_connAsync, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置根节点
    ret = testGmcPrepareStmtByLabelName(g_stmt_root, "T0", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangSetRoot(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_root, &g_rootNode);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 100;
    testYangSetNodeProperty(g_rootNode, fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_root);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置child节点表1
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "T0::T2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 1;
    testYangSetNodeProperty_PK(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点、choice1-case1
    ret = GmcYangEditChildNode(g_childNode[1], "choice1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "case1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool pVal = 0;
    ret = testYangSetNodeField(g_childNode[3], GMC_DATATYPE_BOOL, &pVal, sizeof(pVal), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 编辑子节点、choice1-case2
    ret = GmcYangEditChildNode(g_childNode[2], "case2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pVal = 0;
    ret = testYangSetNodeField(g_childNode[3], GMC_DATATYPE_BOOL, &pVal, sizeof(pVal), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 编辑子节点、choice1-case3
    ret = GmcYangEditChildNode(g_childNode[2], "case1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pVal = 0;
    ret = testYangSetNodeField(g_childNode[3], GMC_DATATYPE_BOOL, &pVal, sizeof(pVal), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 再次写入一条数据，list_localhash索引一样，不报错
    ret = testGmcPrepareStmtByLabelName(g_stmt_list[1], "T0::T2", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcYangBindChild(batch, g_stmt_root, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetRootNode(g_stmt_list[1], &g_childNode[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 设置属性值
    fieldValue = 2;
    testYangSetNodeProperty_PK1(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);
    testYangSetNodePropertyWithoutF0(g_childNode[1], fieldValue, GMC_YANG_PROPERTY_OPERATION_CREATE);

    // 编辑子节点、choice1-case1
    ret = GmcYangEditChildNode(g_childNode[1], "choice1", GMC_OPERATION_INSERT, &g_childNode[2]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcYangEditChildNode(g_childNode[2], "case1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pVal = 0;
    ret = testYangSetNodeField(g_childNode[3], GMC_DATATYPE_BOOL, &pVal, sizeof(pVal), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 编辑子节点、choice1-case2
    ret = GmcYangEditChildNode(g_childNode[2], "case2", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pVal = 0;
    ret = testYangSetNodeField(g_childNode[3], GMC_DATATYPE_BOOL, &pVal, sizeof(pVal), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 编辑子节点、choice1-case3
    ret = GmcYangEditChildNode(g_childNode[2], "case1", GMC_OPERATION_INSERT, &g_childNode[3]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pVal = 0;
    ret = testYangSetNodeField(g_childNode[3], GMC_DATATYPE_BOOL, &pVal, sizeof(pVal), "F0",
        GMC_YANG_PROPERTY_OPERATION_CREATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 添加DML操作
    ret = GmcBatchAddDML(batch, g_stmt_list[1]);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批处理提交
    AsyncUserDataT data = { 0 };
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);  // 再次写入一条数据不报唯一性冲突
    AW_MACRO_EXPECT_EQ_INT(3, data.totalNum);
    AW_MACRO_EXPECT_EQ_INT(3, data.succNum);
    GmcBatchDestroy(batch);
    memset(&data, 0, sizeof(AsyncUserDataT));

    // 提交事务
    ret = testTransCommitAsync(g_connAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "075Basic");

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_nspName);

    GmcFreeStmt(g_stmt_root);
    for (i = 0; i < 10; i++) {
        GmcFreeStmt(g_stmt_list[i]);
    }

    for (i = 0; i < 10; i++) {
        g_stmt_list[i] = NULL;
    }

    g_rootNode = NULL;
    for (i = 0; i < 10; i++) {
        g_childNode[i] = NULL;
    }
    
    AW_FUN_Log(LOG_STEP, "test stop.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 076 list表，list_localhash含bool，通过merge校验唯一性冲突
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_076)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "PK含bool建表");
    readJanssonFile("./schemaFile/061ListPkBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schemaFile/unique_edgeLabel.gmjson", &g_edgeLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_edgeLabelJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    
    AW_FUN_Log(LOG_STEP, "写数据");
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_connAsync, &batch);

    TestInsertBoolData(g_connAsync, g_stmtAsync, 1, 10, 1);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "061Basic");

    AW_FUN_Log(LOG_STEP, "merge数据");
    TestReplace(g_connAsync, g_stmtAsync, false, 2, 2, 11, 11, false);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "061BasicReplace");
    AW_FUN_Log(LOG_STEP, "merge数据导致唯一性冲突");
    TestReplace(g_connAsync, g_stmtAsync, false, 4, 4, 11, 11, false);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "061BasicReplace");

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_nspName);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 077 list表，list_localhash含bool，通过update校验唯一性冲突
TEST_F(BoolYang, HardWare_Offloading_001_DDL_050_077)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_TRANSACTION_ROLLBACK);
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_UNIQUE_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "PK含bool建表");
    readJanssonFile("./schemaFile/061ListPkBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    memset(&data, 0, sizeof(AsyncUserDataT));
    ret = GmcCreateVertexLabelAsync(g_stmtAsync, g_vertexLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(&data, 0, sizeof(AsyncUserDataT));

    readJanssonFile("schemaFile/unique_edgeLabel.gmjson", &g_edgeLabelJson);
    AW_MACRO_ASSERT_NOTNULL(g_edgeLabelJson);
    ret = GmcCreateEdgeLabelAsync(g_stmtAsync, g_edgeLabelJson, g_cfgJson, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(g_edgeLabelJson);
    
    AW_FUN_Log(LOG_STEP, "写数据");
    GmcBatchT *batch = NULL;
    TestBatchPrepare(g_connAsync, &batch);

    TestInsertBoolData(g_connAsync, g_stmtAsync, 1, 10, 1);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "061Basic");

    AW_FUN_Log(LOG_STEP, "Replace数据");
    TestReplace(g_connAsync, g_stmtAsync, false, 2, 2, 11, 11);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "061BasicReplace");
    AW_FUN_Log(LOG_STEP, "Replace数据导致唯一性冲突");
    TestReplace(g_connAsync, g_stmtAsync, false, 4, 4, 11, 11);
    testSubtreeFilter(g_connAsync, g_stmtAsync, g_labelName3, "061BasicReplace");

    AW_FUN_Log(LOG_STEP, "删表");
    testClearNsp(g_stmtAsync, g_nspName);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}
