/*
唯一索引
    001 bool类型字段设置为primary key，预期同步建表失败
    002 bool类型字段设置为localhash key，localhash唯一，预期同步建表失败
    003 bool类型字段设置为local key，local唯一，预期同步建表失败
    004 bool类型字段设置为hashcluster key，hashcluster唯一，预期同步建表失败
    005 bool类型字段设置为LPM4，预期同步建表失败
    006 bool类型字段设置为LPM6，预期同步建表失败
    007 bool类型字段设置为member key，member唯一，预期同步建表成功
    008 多个bool类型字段设置为member key，member唯一，预期同步建表成功

非唯一索引
    009 bool类型字段设置为localhash key，localhash非唯一，预期同步建表失败
    010 bool类型字段设置为local key，local非唯一，预期同步建表失败
    011 bool类型字段设置为hashcluster key，hashcluster非唯一，预期同步建表失败
    012 bool类型字段设置为member key，member非唯一，预期同步建表成功
    013 多个bool类型字段设置为member key，member非唯一，预期同步建表成功

多类型字段索引
    014 bool类型字段和int类型字段设置为localhash key，localhash唯一，预期同步建表失败
    015 bool类型字段和int类型字段设置为local key，local唯一，预期同步建表失败
    016 bool类型字段和int字段类型设置为hashcluster key，hashcluster唯一，预期同步建表失败
    017 bool类型字段和int类型字段设置为LPM4，预期同步建表失败
    018 bool类型字段和int类型字段设置为LPM6，预期同步建表失败
    019 bool类型字段和int类型字段设置为member key，member唯一，预期同步建表成功

*/

#include "IndexBool.h"

class BoolSyn : public testing::Test {
protected:
    static void SetUpTestCase();
    static void TearDownTestCase();

public:
    virtual void SetUp();
    virtual void TearDown();
};

void BoolSyn::SetUpTestCase()
{
    int ret = 0;
    // 配置相关环境变量及重启server
    InitCfg();
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

void BoolSyn::TearDownTestCase()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    //恢复配置文件
    RecoverCfg();
}

void BoolSyn::SetUp()
{
    // 建连
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, g_labelName);
    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_INVALID_TABLE_DEFINITION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);
}

void BoolSyn::TearDown()
{
    AW_CHECK_LOG_END();
    // 断连
    free(g_vertexLabelJson);
    g_vertexLabelJson = NULL;
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 唯一索引
// 001 bool类型字段设置为primary key，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/001PrinmaryBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002 bool类型字段设置为localhash key，localhash唯一，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/002LocalhashBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003 bool类型字段设置为local key，local唯一，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/003LocalBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004 bool类型字段设置为hashcluster key，hashcluster唯一，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/004HashclusterBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 005 bool类型字段设置为LPM4，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/005LPM4Bool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006 bool类型字段设置为LPM6，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/006LPM6Bool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007 bool类型字段设置为member key，member唯一，预期同步建表成功
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    GmcDropVertexLabel(g_stmt, g_labelName4);
    readJanssonFile("./schemaFile/007OneMemberKeyBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008 多个bool类型字段设置为member key，member唯一，预期同步建表成功
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    GmcDropVertexLabel(g_stmt, g_labelName4);
    readJanssonFile("./schemaFile/008MultiMemberKeyBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 非唯一索引
// 009 bool类型字段设置为localhash key，localhash非唯一，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/009LocalhashUniBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 010 bool类型字段设置为local key，local非唯一，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/010LocalUniBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011 bool类型字段设置为hashcluster key，hashcluster非唯一，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/011HashclusterUniBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012 bool类型字段设置为member key，member非唯一，预期同步建表成功
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    GmcDropVertexLabel(g_stmt, g_labelName4);
    readJanssonFile("./schemaFile/012OneMemberKeyUniBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013 多个bool类型字段设置为member key，member非唯一，预期同步建表成功
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    GmcDropVertexLabel(g_stmt, g_labelName4);
    readJanssonFile("./schemaFile/013MultiMemberKeyUniBool.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 多类型字段索引
// 014 bool类型字段和int类型字段设置为localhash key，localhash唯一，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/014LocalhashBoolint.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 015 bool类型字段和int类型字段设置为local key，local唯一，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/015LocalBoolint.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016 bool类型字段和int字段类型设置为hashcluster key，hashcluster唯一，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/016HashclusterBoolint.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017 bool类型字段和int类型字段设置为LPM4，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/017LPM4Boolint.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018 bool类型字段和int类型字段设置为LPM6，预期同步建表失败
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    readJanssonFile("./schemaFile/018LPM6Boolint.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_TABLE_DEFINITION, ret);

    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019 bool类型字段和int类型字段设置为member key，member唯一，预期同步建表成功
TEST_F(BoolSyn, HardWare_Offloading_001_DDL_050_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "建表");
    GmcDropVertexLabel(g_stmt, g_labelName4);
    readJanssonFile("./schemaFile/019MultiMemberKeyBoolint.gmjson", &g_vertexLabelJson);
    ASSERT_NE((void *)NULL, g_vertexLabelJson);
    ret = GmcCreateVertexLabel(g_stmt, g_vertexLabelJson, g_labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "删表");
    ret = GmcDropVertexLabel(g_stmt, g_labelName4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test stops.");
    AW_FUN_Log(LOG_STEP, "test end.");
}
