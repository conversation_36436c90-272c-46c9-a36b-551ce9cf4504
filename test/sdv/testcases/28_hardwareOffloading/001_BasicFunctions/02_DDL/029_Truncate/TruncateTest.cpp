#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "../../common/hash_util.h"

GmcConnT *g_conn;
GmcStmtT *g_stmt;
GmcStmtT *g_stmt1;
unsigned int isNull;
unsigned int posValue;
const char *labelName = "T36";
const char *keyName = "T36_K0";
int affectvalue;
char *delta_json = NULL;
char *delat_config_json = NULL;
unsigned int affectRows, len;
char *truncate_label_schema = NULL;
char *truncate_label_length_schema = NULL;
char *truncate_label_length_abnormal_schema = NULL;
char *sys_label_schema = NULL;
char *vertex_src_fileds_label_schema = NULL;
char *vertex_dest_fileds_label_schema = NULL;
char *edge_label_schema = NULL;
char g_EdgeLabelName[] = "from_T80_to_T90";
char g_EdgeLabel_config[] = "{\"max_record_count\":1000}";
const char *normal_config_json = R"(
    {
        "max_record_count":100000
    }
)";
const char *max_config_json = R"(
    {
        "max_record_count":10000000
    }
)";
class TruncateTest : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
        readJanssonFile("schema_file/truncate_test_schema.gmjson", &truncate_label_schema);
        ASSERT_NE((void *)NULL, truncate_label_schema);
        readJanssonFile("schema_file/truncate_length_test_schema.gmjson", &truncate_label_length_schema);
        ASSERT_NE((void *)NULL, truncate_label_length_schema);
        readJanssonFile("schema_file/truncate_length_abnormal_schema.gmjson", &truncate_label_length_abnormal_schema);
        ASSERT_NE((void *)NULL, truncate_label_length_abnormal_schema);
        readJanssonFile("schema_file/sys.gmjson", &sys_label_schema);
        ASSERT_NE((void *)NULL, sys_label_schema);
        readJanssonFile("schema_file/vertexlabel_dest_schem.gmjson", &vertex_dest_fileds_label_schema);
        ASSERT_NE((void *)NULL, vertex_dest_fileds_label_schema);
        readJanssonFile("schema_file/vertexlabel_src_schem.gmjson", &vertex_src_fileds_label_schema);
        ASSERT_NE((void *)NULL, vertex_src_fileds_label_schema);
        readJanssonFile("schema_file/edgelabel_schema.gmjson", &edge_label_schema);
        ASSERT_NE((void *)NULL, edge_label_schema);
        //读取delta的schema
        readJanssonFile("schema_file/DeltaStore_Full.gmdstore", &delta_json);
        ASSERT_NE((void *)NULL, delta_json);
        //读取delta的config schema
        readJanssonFile("schema_file/deltaStore_config.gmjson", &delat_config_json);
        ASSERT_NE((void *)NULL, delat_config_json);
        //创建epoll监听线程
        int ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        free(truncate_label_schema);
        //恢复配置文件
        RecoverCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
    SnUserDataT *user_data;
};

void TruncateTest::SetUp()
{
    //同步连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, labelName);
    GmcDropVertexLabel(g_stmt, "T80");
    GmcDropVertexLabel(g_stmt, "T90");
    AW_CHECK_LOG_BEGIN();
}

void TruncateTest::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

class Create_Client : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret = testEnvInit();
        ASSERT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }
};

void queryvertex(GmcStmtT *stmt, const char *labelName, uint32_t pk, const char *keyName)
{

    uint32_t F0Value = pk, F1Value = pk + 1, F2Value = pk + 2, F3Value = pk + 3;
    int ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, keyName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
void insertvertex(GmcStmtT *stmt, const char *labelName, unsigned int times, const char *keyName)
{
    uint32_t F0Value, F1Value, F2Value, F3Value;
    int ret = 0;
    for (int i = 0; i < times; i++) {
        F0Value = i;
        F1Value = i + 1;
        F2Value = i + 2;
        F3Value = i + 3;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        queryvertex(stmt, labelName, F0Value, keyName);
    }

    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(times, cnt);
}
void *dropThread(void *arg)
{
    GmcConnT *g_conn7;
    GmcStmtT *g_stmt7;
    void *vertexLabel = NULL;
    int ret = testGmcConnect(&g_conn7, &g_stmt7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt7, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *insertThread(void *arg)
{
    GmcConnT *g_conn3;
    GmcStmtT *g_stmt3;
    int ret = testGmcConnect(&g_conn3, &g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F0Value = 100, F1Value = 101, F2Value = 102, F3Value = 103;
    ret = testGmcPrepareStmtByLabelName(g_stmt3, "T36", GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt3, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt3, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt3, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt3, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt3, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt3, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    printf("cnt=%d\n", cnt);
    if (cnt == 0 || cnt == 21 || cnt == 1) {
        ret = 0;
        printf("cnt=%d\n", cnt);
    } else {
        ret = 1;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn3, g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *openThread(void *arg)
{
    GmcConnT *g_conn3;
    GmcStmtT *g_stmt3;
    int ret = testGmcConnect(&g_conn3, &g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F0Value = 0, F1Value = 1, F2Value = 2, F3Value = 3;
    ret = testGmcPrepareStmtByLabelName(g_stmt3, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn3, g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *queryThread(void *arg)
{
    GmcConnT *g_conn3;
    GmcStmtT *g_stmt3;
    int ret = testGmcConnect(&g_conn3, &g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int F0Value = 0, F1Value = 1, F2Value = 2, F3Value = 3;
    for (int i = 0; i < 20; i++) {
        F0Value = i, F1Value = i + 1;
        F2Value = i + 2, F3Value = i + 3;
        ret = testGmcPrepareStmtByLabelName(g_stmt3, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt3, 0, GMC_DATATYPE_UINT32, &F0Value, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt3, "T36_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt3);
        if (ret == GMERR_OK) {
            while (true) {
                bool isFinish;
                ret = GmcFetch(g_stmt3, &isFinish);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                if (isFinish) {
                    break;
                }
                ret = queryPropertyAndCompare(g_stmt3, "F0", GMC_DATATYPE_UINT32, &F0Value);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = queryPropertyAndCompare(g_stmt3, "F1", GMC_DATATYPE_UINT32, &F1Value);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = queryPropertyAndCompare(g_stmt3, "F2", GMC_DATATYPE_UINT32, &F2Value);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                ret = queryPropertyAndCompare(g_stmt3, "F3", GMC_DATATYPE_UINT32, &F3Value);
                AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            }
        } else {
            printf("truncate success,all vertex is deleted,query fail\n");
            break;
        }
    }
    ret = testGmcDisconnect(g_conn3, g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *dropedgeThread(void *arg)
{
    // drop edgelabel
    GmcConnT *g_conn8;
    GmcStmtT *g_stmt8;
    void *vertexLabel = NULL;
    int ret = testGmcConnect(&g_conn8, &g_stmt8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropEdgeLabel(g_stmt8, g_EdgeLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn8, g_stmt8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *truncateThread(void *arg)
{
    GmcConnT *g_conn2;
    GmcStmtT *g_stmt2;
    void *vertexLabel = NULL;
    int ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt2, labelName);
    if (ret == GMERR_OK) {
        ret = testGmcPrepareStmtByLabelName(g_stmt2, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt2, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            cnt++;
        }
        if (cnt == 0 || cnt == 1) {
            ret = 0;
        } else {
            ret = 1;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    } else if (ret == GMERR_UNEXPECTED_NULL_VALUE) {
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("truncate fail ,label is drop \n");
    } else if (ret == GMERR_RESTRICT_VIOLATION) {
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("truncate fail ,edgelabel exist\n");
    } else {
        printf("ret=%d\n", ret);
    }
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *truncateThread2(void *arg)
{
    GmcConnT *g_conn2;
    GmcStmtT *g_stmt2;
    void *vertexLabel = NULL;
    int ret = testGmcConnect(&g_conn2, &g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt2, "T80");
    if (ret == GMERR_OK) {
        ret = testGmcPrepareStmtByLabelName(g_stmt2, "T80", GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt2, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            cnt++;
        }
        if (cnt == 0 || cnt == 1) {
            ret = 0;
        } else {
            ret = 1;
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    else if (ret == GMERR_UNEXPECTED_NULL_VALUE) {
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("truncate fail ,label is drop \n");
    } else if (ret == GMERR_RESTRICT_VIOLATION) {
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("truncate fail ,edgelabel exist\n");
    } else {
        printf("ret=%d\n", ret);
    }
    ret = testGmcDisconnect(g_conn2, g_stmt2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *updateThread(void *arg)
{

    GmcConnT *g_conn4;
    GmcStmtT *g_stmt4;
    int ret = testGmcConnect(&g_conn4, &g_stmt4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t pk = 0, up_value = 10;
    ret = testGmcPrepareStmtByLabelName(g_stmt4, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt4, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt4, "F1", GMC_DATATYPE_UINT32, &up_value, sizeof(up_value));
    if (ret == GMERR_OK) {
        ret = GmcSetIndexKeyName(g_stmt4, keyName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    if (ret == GMERR_NO_DATA) {
        ret = testGmcGetLastError(NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("truncate success .all vertex is delete.update fail\n");
    }
    ret = testGmcDisconnect(g_conn4, g_stmt4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *deleteThread(void *arg)
{
    GmcConnT *g_conn5;
    GmcStmtT *g_stmt5;
    int ret = testGmcConnect(&g_conn5, &g_stmt5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint32_t pk;
    ret = testGmcPrepareStmtByLabelName(g_stmt5, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(g_stmt5, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt5, keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt5, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt5, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    if (cnt == 19 || cnt == 0) {
        ret = 0;
    }
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(g_conn5, g_stmt5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *insertedgeThread(void *arg)
{
    GmcConnT *g_conn3;
    GmcStmtT *g_stmt3;
    int ret = testGmcConnect(&g_conn3, &g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //建边
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateEdgeLabel(g_stmt, edge_label_schema, vertexLabel_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_conn3, g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void *scanThread(void *arg)
{
    GmcConnT *g_conn3;
    GmcStmtT *g_stmt3;
    int ret = testGmcConnect(&g_conn3, &g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (1) {
        ret = testGmcPrepareStmtByLabelName(g_stmt3, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt3, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            cnt++;
        }
        if (cnt != 100000) {
            printf("scan vertex = %d\n", cnt);
            break;
        }
    }
    ret = testGmcDisconnect(g_conn3, g_stmt3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ((void *)0);
}
void set_VertexProperty(GmcStmtT *stmt, uint32_t pk)
{
    int ret = 0;
    uint32_t f0_value = pk;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t f1_value = pk + 1;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &f1_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t f2_value = pk + 2;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &f2_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t f3_value = pk + 3;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

// 001 参数正常，插入vertex，truncate ，查询vertex是否存在 预期结果：truncate成功
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 002 未close label 就truncate 预期结果返回错误码80020
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    insertvertex(g_stmt, labelName, 20, keyName);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003 stmt为NULL，预期返回错误码GMERR_UNEXPECTED_NULL_VALUE
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004005");
    int ret = GmcTruncateVertexLabel(NULL, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 004 labelName为NULL,返回错误码GMERR_INVALID_PARAMETER_VALUE
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1004005");
    int ret = GmcTruncateVertexLabel(g_stmt, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_NULL_VALUE_NOT_ALLOWED, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005 labelName为xxx,返回错误码
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName = "xxx";
    int ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006 同步连接，插入vertex，更新vertex，truncate，查询数据
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    uint32_t pk = 0, up_value = 10;
    // 更新
    for (int i = 0; i < 20; i++) {
        pk = i;
        up_value = +i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, keyName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &up_value, sizeof(up_value));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 007 同步连接，插入多个vertex，delete删除单个vertex，truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    uint32_t pk = 0, up_value = 10;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(19, cnt);

    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008 同步连接，插入多个vertex，delete删除多个vertex，truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    uint32_t pk = 0;
    for (int i = 0; i < 20; i++) {
        pk = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, keyName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        // get affect row
        ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009 同步连接，创建label，truncate ，插入vertex，查询vertex是否存在 预期结果：truncate成功，插入数据成功
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel

    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);
    insertvertex(g_stmt, labelName, 20, keyName);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010 同步连接，创建label，truncate，插入和更新vertex，预期结果：truncate成功，插入更新数据成功
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);
    insertvertex(g_stmt, labelName, 20, keyName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    uint32_t pk = 0, up_value = 10;
    // 设置主键
    for (int i = 0; i < 20; i++) {
        pk = i;
        up_value = +i;
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT32, &up_value, sizeof(up_value));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, keyName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 011 同步连接，创建label，truncate,插入和删除vertex，预期结果：truncate成功，插入删除数据成功
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);
    insertvertex(g_stmt, labelName, 20, keyName);
    uint32_t pk = 0, up_value = 10;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // get affect row
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(19, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 012 插入vertex，建边，调用接口truncate,返回错误码
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1005001");
    int ret = GmcAllocStmt(g_conn, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, vertex_dest_fileds_label_schema, vertexLabel_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt1, vertex_src_fileds_label_schema, vertexLabel_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //建边
    ret = GmcCreateEdgeLabel(g_stmt, edge_label_schema, vertexLabel_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, "T80", 20, "T80_K0");
    insertvertex(g_stmt1, "T90", 20, "T90_K0");
    // truncate
    ret = GmcTruncateVertexLabel(g_stmt, "T80");
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESTRICT_VIOLATION, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropGraphLabel(g_stmt, "T80");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 013 插入vertex，建边，drop edgelabel，调用接口truncate //约束truncate前不能drop edge，drop
// edge有问题但不暴露在这里，这里是成功的
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = GmcAllocStmt(g_conn, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, vertex_dest_fileds_label_schema, vertexLabel_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt1, vertex_src_fileds_label_schema, vertexLabel_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //建边
    ret = GmcCreateEdgeLabel(g_stmt, edge_label_schema, vertexLabel_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, "T80", 20, "T80_K0");
    insertvertex(g_stmt1, "T90", 20, "T90_K0");
    ret = testGmcPrepareStmtByLabelName(g_stmt, "T80", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "T90", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // drop edgelabel
    ret = GmcDropEdgeLabel(g_stmt, g_EdgeLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // truncate
    ret = GmcTruncateVertexLabel(g_stmt, "T80");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "T80", GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);
    ret = GmcDropVertexLabel(g_stmt, "T80");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt1, "T90");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 014 多线程并发插入vertex和truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    pthread_t tid_insert;
    pthread_t tid_truncate;
    //多线程插入
    ret = pthread_create(&tid_insert, NULL, insertThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //多线程扫描
    ret = pthread_create(&tid_truncate, NULL, truncateThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(tid_insert, NULL);
    pthread_join(tid_truncate, NULL);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 015 多线程并发更新vertex和truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    usleep(100);  // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    pthread_t tid_update;
    pthread_t tid_truncate;
    //
    ret = pthread_create(&tid_update, NULL, updateThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //多线程扫描
    ret = pthread_create(&tid_truncate, NULL, truncateThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid_update, NULL);
    pthread_join(tid_truncate, NULL);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 016 多线程并发删除vertex和truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    usleep(100);
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    pthread_t tid_delete;
    pthread_t tid_truncate;
    //
    ret = pthread_create(&tid_delete, NULL, deleteThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //多线程扫描
    ret = pthread_create(&tid_truncate, NULL, truncateThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(tid_delete, NULL);
    pthread_join(tid_truncate, NULL);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017 多线程并发drop edgelabel和truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    void *vertexLabel, *vertexLabel1;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int ret = GmcAllocStmt(g_conn, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, vertex_dest_fileds_label_schema, vertexLabel_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt1, vertex_src_fileds_label_schema, vertexLabel_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //建边
    ret = GmcCreateEdgeLabel(g_stmt, edge_label_schema, vertexLabel_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, "T80", 20, "T80_K0");
    insertvertex(g_stmt1, "T90", 20, "T90_K0");
    ret = testGmcPrepareStmtByLabelName(g_stmt, "T80", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "T90", GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t tid_dropedge;
    pthread_t tid_truncate2;
    ret = pthread_create(&tid_dropedge, NULL, dropedgeThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //多线程扫描
    ret = pthread_create(&tid_truncate2, NULL, truncateThread2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(tid_dropedge, NULL);
    pthread_join(tid_truncate2, NULL);
    ret = GmcDropVertexLabel(g_stmt, "T80");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt1, "T90");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 018 多线程并发插入edgelabel和truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    void *vertexLabel, *vertexLabel1;
    int ret = GmcAllocStmt(g_conn, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, vertex_dest_fileds_label_schema, vertexLabel_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt1, vertex_src_fileds_label_schema, vertexLabel_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    pthread_t tid_insertedge;
    pthread_t tid_truncate2;
    ret = pthread_create(&tid_insertedge, NULL, insertedgeThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //多线程扫描
    ret = pthread_create(&tid_truncate2, NULL, truncateThread2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(tid_insertedge, NULL);
    pthread_join(tid_truncate2, NULL);

    // drop edgelabel
    ret = GmcDropEdgeLabel(g_stmt, g_EdgeLabelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, "T80");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt1, "T90");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 019 多线程并发查询和truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    pthread_t tid_query;
    pthread_t tid_truncate;
    //
    ret = pthread_create(&tid_query, NULL, queryThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //多线程扫描
    ret = pthread_create(&tid_truncate, NULL, truncateThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid_query, NULL);
    pthread_join(tid_truncate, NULL);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020 superfiled 插入数据，truncate 预期结果 truncate成功
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const char *labelName1 = "truncate_superfiled";
    const char *keyName1 = "truncate_superfiled_K0";
    const char *superfield_label_schema =
        R"(
            [{"type":"record",
             "name":"truncate_superfiled",
             "fields":
                [
                    {"name":"F0", "type":"char","default":"a"},
                    {"name":"F1", "type":"uchar","default":"f"},
                    {"name":"F2", "type":"int8", "default":2},
                    {"name":"F3", "type":"int16", "default":2}
                ],
             "super_fields":
                [
                 {"name":"superfield0",  "comment":"test",
                  "fields":["F0", "F1"]
                 },
                 {"name":"superfield1",  "comment":"test",
                  "fields":["F2", "F3"]
                 }
                ],
                "keys":
                    [
                        {"node":"truncate_superfiled", "name":"truncate_superfiled_K0",
                         "fields":["F0"], "index":{"type":"primary"},"constraints":{"unique":true}
                        }
                    ]
            }])";
    int ret = GmcCreateVertexLabel(g_stmt, superfield_label_schema, normal_config_json);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName1, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // set superfiled by name
    char *sp_1 = (char *)malloc(2);
    *(char *)sp_1 = 'c';
    *(unsigned char *)(sp_1 + 1) = 'd';
    ret = GmcSetSuperfieldById(g_stmt, 0, sp_1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *sp_2 = (char *)malloc(3);
    *(int8_t *)sp_2 = 8;
    *(int16_t *)(sp_2 + 1) = 9;
    ret = GmcSetSuperfieldById(g_stmt, 1, sp_2, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sp_1);
    free(sp_2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //读取F1/F2/F3
    char F0Value[] = "c";
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_CHAR, F0Value, sizeof(char));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt, keyName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    while (true) {
        bool isFinish;
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        char *F1V = (char *)malloc(sizeof(char) * 12);
        int8_t F2V;
        int16_t F3V;
        const char *expectedF1Value = "d";
        int8_t expectedF2Value = 8;
        int16_t expectedF3Value = 9;
        unsigned int isNull;

        bool isnull;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", F1V, 12, &isnull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(false, isnull);
        AW_MACRO_EXPECT_EQ_INT(*expectedF1Value, *F1V);
        free(F1V);
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &F2V, 12, &isnull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(false, isnull);
        AW_MACRO_EXPECT_EQ_INT(expectedF2Value, F2V);

        ret = GmcGetVertexPropertyByName(g_stmt, "F3", &F3V, 12, &isnull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(false, isnull);
        AW_MACRO_EXPECT_EQ_INT(expectedF3Value, F3V);

        // get superfiled by name
        char *sp_1_get = (char *)malloc(2);
        ret = GmcGetSuperfieldById(g_stmt, 0, sp_1_get, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT('c', *(char *)sp_1_get);
        AW_MACRO_EXPECT_EQ_INT('d', *(unsigned char *)(sp_1_get + 1));
        char *sp_2_get = (char *)malloc(3);
        ret = GmcGetSuperfieldById(g_stmt, 1, sp_2_get, 3);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(8, *(int8_t *)sp_2_get);
        AW_MACRO_EXPECT_EQ_INT(9, *(int16_t *)(sp_2_get + 1));
        sleep(2);
        // free
        free(sp_1_get);
        free(sp_2_get);
        GmcFreeIndexKey(g_stmt);
    }

    ret = GmcTruncateVertexLabel(g_stmt, labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName1, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);
    // drop vertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 021 写入100条数据，truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 100, keyName);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 022 写入1000条数据，truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 1000, keyName);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 023 写入10000条数据，truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 10000, keyName);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 024 写入100000条数据，truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 100000, keyName);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 026 drop label 后truncate 预期返回错误码
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 027 truncate不支持事务 预期返回错误码
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, vertexLabel_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // 开启一个事务(cs模式)
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_COMMITTED;
    config.readOnly = false;
    config.trxType = GMC_DEFAULT_TRX;

    ret = GmcTransStart(g_conn, &config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    // truncate
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);  // 2021.6.7 ddl在事务内不报错了，改为在执行ddl之前自动提交事务
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 028 批量插入顶点，truncate 预期结果 truncate成功
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet;
    int start_num = 0;
    int end_num = 100;
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    // 插入顶点
    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        set_VertexProperty(g_stmt, i);
        ret = GmcBatchAddDML(batch, g_stmt);
        if (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) {
            ret = GmcBatchExecute(batch, &batchRet);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_ASSERT_EQ_INT(72, totalNum);
            AW_MACRO_ASSERT_EQ_INT(72, successNum);
            set_VertexProperty(g_stmt, i);
            ret = GmcBatchAddDML(batch, g_stmt);
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(100, totalNum);
    AW_MACRO_ASSERT_EQ_INT(100, successNum);
    ret = GmcBatchDestroy(batch);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    batch = NULL;
    for (int i = start_num; i < end_num; i++) {
        queryvertex(g_stmt, labelName, i, keyName);
    }
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 029 连续调用truncate接口
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 030 多线程并发open vertexlabel和truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    pthread_t tid_open;
    pthread_t tid_truncate;
    //
    ret = pthread_create(&tid_open, NULL, openThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //多线程扫描
    ret = pthread_create(&tid_truncate, NULL, truncateThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_join(tid_open, NULL);
    pthread_join(tid_truncate, NULL);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 031
// 建表，插入多条数据和truncate，然后创建同名label，预期结果：truncate成功，建同名表失败（truncate只是删除表数据，不影响表的元数据）
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009013");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 20, keyName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_DUPLICATE_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 032 建表，循环进行插入多条数据和truncate 预期结果：truncate成功
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    for (int i = 0; i < 2; i++) {
        insertvertex(g_stmt, labelName, 20, keyName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcTruncateVertexLabel(g_stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        isFinish = false;
        cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(g_stmt, &isFinish);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish == true) {
                break;
            }
            cnt++;
        }
        AW_MACRO_EXPECT_EQ_INT(0, cnt);
    }

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
void sn_callback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{}

// 033 开启订阅，插入数据，truncate，预期结果：truncate成功
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *g_conn_sub = NULL, *g_stmt_sub = NULL;
    // Create VertexLabel
    const char *g_subName = "subVertexLabel";
    const char *g_subConnName = "subConnName";
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    char *sub_info = NULL;
    GmcConnT *g_subChan = NULL;
    readJanssonFile("schema_file/simple_schema_subinfo.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);

    ret = testSubConnect(&g_subChan, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = (char *)"subVertexLabel";
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(g_stmt, &tmp_sub_info, g_subChan, sn_callback, user_data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    insertvertex(g_stmt, labelName, 20, keyName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    ret = GmcUnSubscribe(g_stmt, g_subName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(g_subChan);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 034 插入vertex，建边，truncate edgelabel
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    void *vertexLabel, *vertexLabel1;
    int ret = GmcAllocStmt(g_conn, &g_stmt1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char vertexLabel_config[] = "{\"max_record_count\":100000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, vertex_dest_fileds_label_schema, vertexLabel_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(g_stmt1, vertex_src_fileds_label_schema, vertexLabel_config);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //建边
    ret = GmcCreateEdgeLabel(g_stmt, edge_label_schema, vertexLabel_config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, "T80", 20, "T80_K0");
    insertvertex(g_stmt1, "T90", 20, "T90_K0");
    // truncate
    ret = GmcTruncateVertexLabel(g_stmt, g_EdgeLabelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropGraphLabel(g_stmt, "T80");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 035 创建vertexlabel，插入10万条数据，开启线程不断扫描，主线程truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    int ret = GmcCreateVertexLabel(g_stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    insertvertex(g_stmt, labelName, 100000, keyName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_t tid_scan;
    ret = pthread_create(&tid_scan, NULL, scanThread, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid_scan, NULL);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *trunCate(void *args)
{
    int ret = system("./TruncateTest --gtest_also_run_disabled_tests "
                     "--gtest_filter=Create_Client.DISABLED_trunCateLabel >Client2.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 036 起进程的disabled用例
TEST_F(Create_Client, DISABLED_trunCateLabel)
{
    AW_FUN_Log(LOG_STEP, "DISABLED_Client1 start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "DISABLED_Client2 end.");
    g_needCheckWhenSucc = false;
}

// 036 多进程并发truncate同一张表
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_036)
{
    // Create VertexLabel
    AW_FUN_Log(LOG_STEP, "test start.");
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    int ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateVertexLabel(stmt, truncate_label_schema, normal_config_json);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    insertvertex(stmt, labelName, 100, keyName);
    pthread_t tid1;

    ret = pthread_create(&tid1, NULL, trunCate, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcTruncateVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    pthread_join(tid1, NULL);

    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cat Client2.txt");
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 037 labelName参数长度超过128，truncate
TEST_F(TruncateTest, HardWare_Offloading_001_DDL_029_TruncateTest_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AW_ADD_ERR_WHITE_LIST(1, "GMERR-1009020");
    const char *labelName = "T11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
                            "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
                            "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
                            "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
                            "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
                            "111111111111111111111111111111111111111111111111111111111111111";
    int ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_VALUE, ret);
    ret = testGmcGetLastError("Not normal value. labelName length exceeds the limit 512.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const char *labelName1 = "T1111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
                             "11111111111111111111111111111111111111";
    GmcDropVertexLabel(g_stmt, labelName1);
    ret = GmcCreateVertexLabel(g_stmt, truncate_label_length_schema, normal_config_json);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcTruncateVertexLabel(g_stmt, labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
