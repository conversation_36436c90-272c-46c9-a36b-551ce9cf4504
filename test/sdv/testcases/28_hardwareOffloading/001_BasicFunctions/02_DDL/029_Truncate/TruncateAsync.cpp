#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "../../common/hash_util.h"

GmcConnT *g_conn, *g_conn_async = NULL;
GmcStmtT *g_stmt, *g_stmt_async = NULL;
GmcStmtT *g_stmt1;

unsigned int isNull;
unsigned int posValue;
int affectvalue;
char *delta_json = NULL;
char *delat_config_json = NULL;
unsigned int affectRows, len;
char *truncate_label_schema = NULL;
char *vertex_src_fileds_label_schema = NULL;
char *vertex_dest_fileds_label_schema = NULL;
char *edge_label_schema = NULL;
char g_EdgeLabelName[] = "from_T80_to_T90";
char g_EdgeLabel_config[] = "{\"max_record_count\":1000}";
const char *normal_config_json = R"(
    {
        "max_record_count":1000
    }
)";

class TruncateTestAsync : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();
        readJanssonFile("schema_file/truncate_test_schema.gmjson", &truncate_label_schema);
        ASSERT_NE((void *)NULL, truncate_label_schema);
        readJanssonFile("schema_file/vertexlabel_dest_schem.gmjson", &vertex_dest_fileds_label_schema);
        ASSERT_NE((void *)NULL, vertex_dest_fileds_label_schema);
        readJanssonFile("schema_file/vertexlabel_src_schem.gmjson", &vertex_src_fileds_label_schema);
        ASSERT_NE((void *)NULL, vertex_src_fileds_label_schema);
        readJanssonFile("schema_file/edgelabel_schema.gmjson", &edge_label_schema);
        ASSERT_NE((void *)NULL, edge_label_schema);
        //读取delta的schema
        readJanssonFile("schema_file/DeltaStore_Full.gmdstore", &delta_json);
        ASSERT_NE((void *)NULL, delta_json);
        //读取delta的config schema
        readJanssonFile("schema_file/deltaStore_config.gmjson", &delat_config_json);
        ASSERT_NE((void *)NULL, delat_config_json);
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {

        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();
        free(truncate_label_schema);
        //恢复配置文件
        RecoverCfg();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TruncateTestAsync::SetUp()
{
    // 封装创建异步连接
    int ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    //同步连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcDropVertexLabel(g_stmt, "T36");
    AW_CHECK_LOG_BEGIN();
}

void TruncateTestAsync::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void test_close_and_drop_label_async(GmcStmtT *stmt, const char *labelName)
{
    int ret;
    AsyncUserDataT data = {0};
    ret = GmcDropVertexLabelAsync(stmt, labelName, drop_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(true, data.status == GMERR_OK || data.status == GMERR_UNDEFINED_TABLE);
}
void insertvalueAsync(GmcStmtT *stmt, unsigned int times, GmcAsyncRequestDoneContextT requestCtx, const char *labelName,
    AsyncUserDataT *data)
{
    int F0Value, F1Value, F2Value, F3Value, ret;
    for (int i = 0; i < times; i++) {
        F0Value = i;
        F1Value = i;
        F2Value = i;
        F3Value = i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_UINT32, &F0Value, sizeof(F0Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT32, &F1Value, sizeof(F1Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_UINT32, &F2Value, sizeof(F2Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(stmt, &requestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data->status);
        AW_MACRO_EXPECT_EQ_INT(1, data->affectRows);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(times, cnt);
}
// 001 异步连接，创建label，插入vertex，truncate，查询，预期 truncate成功
TEST_F(TruncateTestAsync, HardWare_Offloading_001_DDL_029_TruncateTestAsync_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    const char *labelName = "T36";
    GmcDropVertexLabel(g_stmt, labelName);
    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, truncate_label_schema, normal_config_json, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcAsyncRequestDoneContextT requestCtx;
    requestCtx.userData = &data;
    requestCtx.insertCb = insert_vertex_callback;
    insertvalueAsync(g_stmt_async, 20, requestCtx, labelName, &data);
    GmcFreeStmt(g_stmt);
    ret = GmcTruncateVertexLabelAsync(g_stmt_async, labelName, truncate_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    AW_MACRO_EXPECT_EQ_INT(1, data.affectRows);
    //查询truncate结果
    ret = GmcAllocStmt(g_conn, &g_stmt);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);
    test_close_and_drop_label_async(g_stmt_async, labelName);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 002 异步连接，创建label，插入vertex，更新数据，truncate，查询，预期 truncate成功
TEST_F(TruncateTestAsync, HardWare_Offloading_001_DDL_029_TruncateTestAsync_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    const char *labelName = "T36";
    GmcDropVertexLabel(g_stmt, labelName);
    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, truncate_label_schema, normal_config_json, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcAsyncRequestDoneContextT requestCtx;
    requestCtx.userData = &data;
    requestCtx.insertCb = insert_vertex_callback;
    insertvalueAsync(g_stmt_async, 20, requestCtx, labelName, &data);
    requestCtx.updateCb = update_vertex_callback;
    // 更新
    uint32_t pk, up_value = 10;
    for (int i = 0; i < 20; i++) {
        pk = i;
        up_value = +i;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_UPDATE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, (const char *)"T36_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_UINT32, &up_value, sizeof(up_value));
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecuteAsync(g_stmt_async, &requestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(20, cnt);
    GmcFreeStmt(g_stmt);
    ret = GmcTruncateVertexLabelAsync(g_stmt_async, labelName, truncate_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    ret = GmcAllocStmt(g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    test_close_and_drop_label_async(g_stmt_async, labelName);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003 异步连接，创建label，插入vertex，删除单条数据，truncate，查询，预期 truncate成功
TEST_F(TruncateTestAsync, HardWare_Offloading_001_DDL_029_TruncateTestAsync_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    const char *labelName = "T36";
    GmcDropVertexLabel(g_stmt, labelName);
    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, truncate_label_schema, normal_config_json, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcAsyncRequestDoneContextT requestCtx;
    requestCtx.userData = &data;
    requestCtx.insertCb = insert_vertex_callback;
    insertvalueAsync(g_stmt_async, 20, requestCtx, labelName, &data);
    requestCtx.deleteCb = delete_vertex_callback;
    // 删除一条数据
    uint32_t pk = 10;
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_async, "T36_K0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecuteAsync(g_stmt_async, &requestCtx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(19, cnt);
    GmcFreeStmt(g_stmt);
    ret = GmcTruncateVertexLabelAsync(g_stmt_async, labelName, truncate_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    ret = GmcAllocStmt(g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);
    test_close_and_drop_label_async(g_stmt_async, labelName);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 004 异步连接，创建label，插入vertex，删除多条数据，truncate，查询，预期 truncate成功
TEST_F(TruncateTestAsync, HardWare_Offloading_001_DDL_029_TruncateTestAsync_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    const char *labelName = "T36";
    GmcDropVertexLabel(g_stmt, labelName);
    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, truncate_label_schema, normal_config_json, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcAsyncRequestDoneContextT requestCtx;
    requestCtx.userData = &data;
    requestCtx.insertCb = insert_vertex_callback;
    insertvalueAsync(g_stmt_async, 20, requestCtx, labelName, &data);
    requestCtx.deleteCb = delete_vertex_callback;
    // 删除全部数据
    uint32_t pk, up_value = 10;
    for (int i = 0; i < 20; i++) {
        pk = i;
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &pk, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T36_K0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecuteAsync(g_stmt_async, &requestCtx);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);
    GmcFreeStmt(g_stmt);
    ret = GmcTruncateVertexLabelAsync(g_stmt_async, labelName, truncate_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, data.status);
    ret = GmcAllocStmt(g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isFinish = false;
    cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);
    test_close_and_drop_label_async(g_stmt_async, labelName);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005  异步插入，同步句柄（stmt）truncate 预期 truncate成功
TEST_F(TruncateTestAsync, HardWare_Offloading_001_DDL_029_TruncateTestAsync_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // Create VertexLabel
    const char *labelName = "T36";
    GmcDropVertexLabel(g_stmt, labelName);
    AsyncUserDataT data = {0};
    int ret = GmcCreateVertexLabelAsync(
        g_stmt_async, truncate_label_schema, normal_config_json, create_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, data.status);
    GmcAsyncRequestDoneContextT requestCtx;
    requestCtx.userData = &data;
    requestCtx.insertCb = insert_vertex_callback;
    insertvalueAsync(g_stmt_async, 20, requestCtx, labelName, &data);
    ret = GmcTruncateVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish = false;
    unsigned int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish == true) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
