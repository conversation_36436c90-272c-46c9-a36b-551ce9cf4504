{"version": "2.0", "name": "register_status", "type": "record", "max_record_count": 10000, "fields": [{"name": "id", "type": "uint32", "auto_increment": true}, {"name": "fileName", "type": "string"}, {"name": "status", "type": "string"}, {"name": "faultInfo", "type": "string"}, {"name": "uptime", "type": "time"}], "keys": [{"name": "pk", "node": "register_status", "index": {"type": "primary"}, "fields": ["id"]}]}