{"comment": "Eth-trunk全局表", "version": "2.0", "type": "record", "name": "trunk_global", "config": {"check_validity": false}, "max_record_count": 64, "fields": [{"name": "vrid", "type": "uint32", "comment": "虚拟设备ID"}, {"name": "lag_spec_mode", "type": "uint32", "comment": "LAG模式"}, {"name": "app_source_id", "type": "uint32", "comment": "应用组件源id"}, {"name": "app_serial_id", "type": "uint32", "comment": "应用组件序列id"}, {"name": "app_obj_id", "type": "uint64", "comment": "应用组件目标id"}, {"name": "app_version", "type": "uint32", "comment": "应用组件版本号"}, {"name": "nonucast_hash", "type": "uint32", "comment": "非已知单播负载分担方式"}], "keys": [{"name": "trunk_global_pk", "index": {"type": "primary"}, "node": "trunk_global", "fields": ["vrid"], "constraints": {"unique": true}, "comment": "主键"}]}