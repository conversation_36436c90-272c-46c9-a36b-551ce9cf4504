{"comment": "qos 接口限速", "version": "2.0", "type": "record", "name": "qos_inbound_iflr", "config": {"check_validity": false}, "max_record_count": 2048, "fields": [{"name": "ifindex", "type": "uint32", "comment": "接口索引"}, {"name": "cir", "type": "uint32", "comment": "承诺信息速率"}, {"name": "cbs", "type": "uint32", "comment": "承诺突发尺寸"}, {"name": "pir", "type": "uint32", "comment": "峰值信息速率"}, {"name": "pbs", "type": "uint32", "comment": "峰值突发尺寸"}, {"name": "app_source_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_serial_id", "type": "uint32", "comment": "保留字段"}, {"name": "app_obj_id", "type": "uint64", "comment": "保留字段"}, {"name": "app_version", "type": "uint32", "comment": "保留字段"}], "keys": [{"name": "qos_inbound_iflr_pk", "index": {"type": "primary"}, "node": "qos_inbound_iflr", "fields": ["ifindex"], "constraints": {"unique": true}, "comment": "主键"}]}