{"version": "2.0", "type": "record", "name": "evpn_transport_network", "config": {"check_validity": false}, "max_record_count": 6, "fields": [{"name": "tn_id", "type": "uint32", "comment": "传输网络id"}, {"name": "tn_name", "type": "string", "size": 32, "comment": "传输网络名称"}], "keys": [{"name": "tn_id", "index": {"type": "primary"}, "node": "evpn_transport_network", "fields": ["tn_id"], "constraints": {"unique": true}, "comment": "以传输网络id为主key"}, {"name": "tn_name", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "evpn_transport_network", "fields": ["tn_name"], "constraints": {"unique": true}, "comment": "以传输网络名称为key"}]}