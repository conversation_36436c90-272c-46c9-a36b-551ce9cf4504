{"comment": "l2SubIf car资源", "version": "2.0", "type": "record", "name": "qos_car_subif", "config": {"check_validity": false}, "max_record_count": 32768, "fields": [{"name": "unit", "type": "uint32", "comment": "芯片号"}, {"name": "port", "type": "uint32", "comment": "局部端口号"}, {"name": "direction", "type": "uint32", "comment": "方向"}, {"name": "pevlan", "type": "uint16", "comment": "外层VLAN"}, {"name": "cevlan", "type": "uint16", "comment": "内层VLAN"}, {"name": "car_id", "type": "uint32", "comment": "CAR ID"}], "keys": [{"name": "qos_car_subif_key", "index": {"type": "primary"}, "node": "qos_car_subif", "fields": ["unit", "port", "direction", "pevlan", "cevlan"], "constraints": {"unique": true}, "comment": "主键索引"}]}