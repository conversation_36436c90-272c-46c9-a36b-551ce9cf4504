{"comment": "table of cib_nac_ext_data", "version": "2.0", "type": "record", "name": "cib_nac_ext_data", "special_complex": true, "config": {"check_validity": false}, "max_record_count": 102400, "fields": [{"name": "ulCID", "type": "uint32", "comment": "cid"}, {"name": "ulCidPrefix", "type": "uint32", "comment": "prefix of cid"}, {"name": "patch_reserved", "type": "uint32", "comment": "reserved for patch"}, {"name": "stCmAsInfo", "type": "record", "comment": "Cm As Info", "fields": [{"name": "ulASIP", "type": "uint32", "comment": "AS IP"}, {"name": "ucSlotNo", "type": "uint8", "comment": "Slot No"}, {"name": "ucPicNo", "type": "uint8", "comment": "Pic No"}, {"name": "usPortNo", "type": "uint16", "comment": "Port No"}, {"name": "ulSubIndex", "type": "uint32", "comment": "SubIndex"}, {"name": "ulAsIfIndex", "type": "uint32", "comment": "As IfIndex"}, {"name": "szASMac", "type": "fixed", "size": 6, "comment": "AS Mac Addr"}, {"name": "ucIsRemoteUser", "type": "uint8", "comment": "Remote User"}, {"name": "ucRemoteSyncTimeoutCount", "type": "uint8", "comment": "Remote Sync Timeout Count"}, {"name": "usAsIfPvid", "type": "uint16", "comment": "<PERSON> <PERSON><PERSON>"}, {"name": "szASPortName", "type": "bytes", "size": 512, "comment": "AS Port Name"}]}, {"name": "stAcctData", "type": "record", "comment": "Acct Info", "fields": [{"name": "ulRTAcctInterval", "type": "uint32", "comment": "RT Acct Interval"}, {"name": "ucAcctState", "type": "uint8", "comment": "Acct State"}, {"name": "ucAcctMethod", "type": "uint8", "comment": "Acct Method"}, {"name": "ucSTAcctPolicy", "type": "uint8", "comment": "ST Acct Policy"}, {"name": "ucRTAcctPolicy", "type": "uint8", "comment": "RT Acct Policy"}, {"name": "ucRTAcctFailNumPermit", "type": "uint8", "comment": "RT Acct Fail Num Permit"}, {"name": "ucRTAcctFailTimes", "type": "uint8", "comment": "RT Acct Fail Times"}, {"name": "ucFlowAcctMode", "type": "uint8", "comment": "Flow Acct Mode"}, {"name": "ucAccStart", "type": "uint8", "comment": "Acc Start Status"}, {"name": "ucSendRTAcctFlag", "type": "uint8", "comment": "Send RT Acct Flag"}, {"name": "ucIsNeedModifyRTAcctFlag", "type": "uint8", "comment": "RT Acct Modify Flag"}, {"name": "usNormalRdsTempletID", "type": "uint16", "comment": "Rds TempletID"}, {"name": "usTwoLevelAcctRdsTempletID", "type": "uint16", "comment": "Two Level Acct Rds TempletID"}, {"name": "usNormalTACTempletID", "type": "uint16", "comment": "TAC TempletID"}, {"name": "curServer_ucType", "type": "uint8", "comment": "current Server Type"}, {"name": "curServer_ucTwoLevelType", "type": "uint8", "comment": "current Server Two Level Type"}, {"name": "usHacaTempletID", "type": "uint16", "comment": "Haca TempletID"}, {"name": "curServer_stIPAddress", "type": "bytes", "size": 1024, "comment": "current Server IP Address"}, {"name": "curServer_stTwoLevelIPAddress", "type": "bytes", "size": 1024, "comment": "current Server Two Level IP Address"}]}, {"name": "szAcctMultiSessionId", "type": "bytes", "size": 1024, "comment": "Acct Multi SessionId"}, {"name": "stIPv6Addr", "type": "bytes", "size": 4096, "comment": "IPv6 Addr"}, {"name": "stDataFlow", "type": "record", "array": true, "size": 64, "comment": "Data Flow Info", "fields": [{"name": "duUpLinkForwardBytes", "type": "uint64", "comment": "UpLink Forward Bytes"}, {"name": "duUpLinkForwardPackets", "type": "uint64", "comment": "UpLink Forward Packets"}, {"name": "duDownLinkForwardBytes", "type": "uint64", "comment": "DownLink Forward Bytes"}, {"name": "duDownLinkForwardPackets", "type": "uint64", "comment": "DownLink Forward Packets"}, {"name": "duUpLinkDropBytes", "type": "uint64", "comment": "UpLink Drop Bytes"}, {"name": "duUpLinkDropPackets", "type": "uint64", "comment": "UpLink Drop Packets"}, {"name": "duDownLinkDropBytes", "type": "uint64", "comment": "DownLink Drop Bytes"}, {"name": "duDownLinkDropPackets", "type": "uint64", "comment": "DownLink Drop Packets"}, {"name": "duV6UpLinkForwardBytes", "type": "uint64", "comment": "V6 UpLink Forward Bytes"}, {"name": "duV6UpLinkForwardPackets", "type": "uint64", "comment": "V6 UpLink Forward Packets"}, {"name": "duV6DownLinkForwardBytes", "type": "uint64", "comment": "V6 DownLink Forward Bytes"}, {"name": "duV6DownLinkForwardPackets", "type": "uint64", "comment": "V6 DownLink Forward Packets"}, {"name": "duV6UpLinkDropBytes", "type": "uint64", "comment": "V6 UpLink Drop Bytes"}, {"name": "duV6UpLinkDropPackets", "type": "uint64", "comment": "V6 UpLink Drop Packets"}, {"name": "duV6DownLinkDropBytes", "type": "uint64", "comment": "V6 DownLink Drop Bytes"}, {"name": "duV6DownLinkDropPackets", "type": "uint64", "comment": "V6 DownLink Drop Packets"}]}, {"name": "stUserDetect", "type": "record", "comment": "User Detect Info", "fields": [{"name": "ulDetectTimeLen", "type": "uint32", "comment": "Detect Time Len"}, {"name": "usDetectTimes", "type": "uint16", "comment": "Detect Times"}, {"name": "ucDetectType", "type": "uint8", "comment": "Detect Type"}, {"name": "ucDetectUserState", "type": "uint8", "comment": "Detect User State"}, {"name": "ucEapolHandShakeType", "type": "uint8", "comment": "Eapol HandShake Type"}, {"name": "ucPortalLayerFlag", "type": "uint8", "comment": "Portal Layer Flag"}, {"name": "ucNeedModTimer", "type": "uint8", "comment": "<PERSON> Mod Timer"}, {"name": "ucArpProxyFlag", "type": "uint8", "comment": "Arp Proxy Flag"}, {"name": "ucArpCheckFlag", "type": "uint8", "comment": "Arp Check Flag"}, {"name": "ucEapPktId", "type": "uint8", "comment": "Eap Pkt Id"}, {"name": "ucEapPause", "type": "uint8", "comment": "Eap Pause"}, {"name": "ulReFreshIpTime", "type": "uint32", "comment": "ReFresh Ip Time"}, {"name": "ucDetectFlagForMacMove", "type": "uint8", "comment": "Detect Flag For Mac Move"}, {"name": "ucStaticUserKeepOnlineFlag", "type": "uint8", "comment": "Static User Keep Online Flag"}, {"name": "usDetectTimesForMacMove", "type": "uint16", "comment": "Detect Times For Mac Move"}, {"name": "ulDetectTimeLenForMacMove", "type": "uint32", "comment": "Detect Time Len For Mac Move"}, {"name": "ulDetectDelayTimeLen", "type": "uint32", "comment": "Detect Delay Time Len"}]}, {"name": "szUserNameBuf", "type": "bytes", "size": 1024, "comment": "User Name Buffer"}, {"name": "stCar", "type": "record", "comment": "Car", "fields": [{"name": "inbound_ucCarFlag", "type": "uint8", "comment": "inbound Car Flag"}, {"name": "inbound_ulCir", "type": "uint32", "comment": "inbound Cir"}, {"name": "inbound_ulCbs", "type": "uint32", "comment": "inbound Cbs"}, {"name": "inbound_ulPir", "type": "uint32", "comment": "inbound Pir"}, {"name": "inbound_ulPbs", "type": "uint32", "comment": "inbound Pbs"}, {"name": "outbound_ucCarFlag", "type": "uint8", "comment": "outbound Car Flag"}, {"name": "outbound_ulCir", "type": "uint32", "comment": "outbound Cir"}, {"name": "outbound_ulCbs", "type": "uint32", "comment": "outbound Cbs"}, {"name": "outbound_ulPir", "type": "uint32", "comment": "outbound Pi<PERSON>"}, {"name": "outbound_ulPbs", "type": "uint32", "comment": "outbound Pbs"}]}, {"name": "szServiceSchemeName", "type": "bytes", "size": 256, "comment": "Service Scheme Name"}, {"name": "usPool", "type": "bytes", "size": 4096, "comment": "Pool"}, {"name": "szUserGroupName", "type": "bytes", "size": 12288, "comment": "User Group Name"}, {"name": "szUpdateURL", "type": "bytes", "size": 2048, "comment": "Update URL"}, {"name": "szDNSDomainName", "type": "bytes", "size": 4096, "comment": "DNS Domain Name"}, {"name": "szDaaQosProfileName", "type": "bytes", "size": 4096, "comment": "Daa Qos Profile Name"}, {"name": "stDaaQosProfile", "type": "record", "array": true, "size": 128, "comment": "Daa Qos Profile Info", "fields": [{"name": "ulStatEn", "type": "uint32", "comment": "Stat En"}, {"name": "upCarItem_ulCarFlag", "type": "uint32", "comment": "Car Item Car Flag"}, {"name": "upCarItem_ulCir", "type": "uint32", "comment": "up Car Item Cir"}, {"name": "upCarItem_ulCbs", "type": "uint32", "comment": "up Car Item Cbs"}, {"name": "upCarItem_ulPir", "type": "uint32", "comment": "up Car Item Pir"}, {"name": "upCarItem_ulPbs", "type": "uint32", "comment": "up Car Item Pbs"}, {"name": "downCarItem_ulCarFlag", "type": "uint32", "comment": "down Car Item Car Flag"}, {"name": "downCarItem_ulCir", "type": "uint32", "comment": "down Car Item Cir"}, {"name": "downCarItem_ulCbs", "type": "uint32", "comment": "down Car Item Cbs"}, {"name": "downCarItem_ulPir", "type": "uint32", "comment": "downCar Item Pir"}, {"name": "downCarItem_ulPbs", "type": "uint32", "comment": "down Car Item Pbs"}, {"name": "remark_ucRemark8021p", "type": "uint8", "comment": "remark Remark 8021p"}, {"name": "remark_uc8021p", "type": "uint8", "comment": "remark 8021p"}, {"name": "remark_ucRemarkDscp", "type": "uint8", "comment": "remark Remark Dscp"}, {"name": "remark_ucInDscp", "type": "uint8", "comment": "remark In Dscp"}, {"name": "remark_ucOutDscp", "type": "uint8", "comment": "remark Out Dscp"}, {"name": "userQueue_ucValid", "type": "uint8", "comment": "user Queue Valid"}, {"name": "userQueue_ulPir", "type": "uint32", "comment": "user Queue Pir"}, {"name": "userQueue_ulCir", "type": "uint32", "comment": "user Queue Cir"}, {"name": "usFlowQueueProfileIndex", "type": "uint16", "comment": "Flow Queue Profile Index"}, {"name": "usFlowMappingProfileIndex", "type": "uint16", "comment": "Flow Mapping Profile Index"}]}, {"name": "stProfileUserQueue", "type": "record", "comment": "Profile User Queue Info", "fields": [{"name": "ucValid", "type": "uint8", "comment": "<PERSON><PERSON>"}, {"name": "ulPir", "type": "uint32", "comment": "<PERSON><PERSON>"}, {"name": "ulCir", "type": "uint32", "comment": "Cir"}, {"name": "usFlowQueueProfileIndex", "type": "uint16", "comment": "Flow Queue Profile Index"}, {"name": "usFlowMappingProfileIndex", "type": "uint16", "comment": "Flow Mapping Profile Index"}]}, {"name": "stTunnel", "type": "record", "comment": "Tunnel Info", "fields": [{"name": "ucUsed", "type": "uint8", "comment": "Used Flag"}, {"name": "ulTunnelType", "type": "uint32", "comment": "Tunnel Type"}, {"name": "ulTunnelMediumType", "type": "uint32", "comment": "Tunnel Medium Type"}, {"name": "ulTunnelPreference", "type": "uint32", "comment": "Tunnel Preference"}, {"name": "szTunnelPrivateGroupID", "type": "bytes", "size": 512, "comment": "Tunnel Private GroupID"}, {"name": "szTunnelPassword", "type": "bytes", "size": 512, "default": "512", "comment": "Tunnel Password"}, {"name": "szTunnelGroupName", "type": "bytes", "size": 512, "default": "512", "comment": "Tunnel Group Name"}, {"name": "szTunnelClientAuthID", "type": "bytes", "size": 512, "default": "512", "comment": "Tunnel Client AuthID"}, {"name": "szTunnelServerAuthID", "type": "bytes", "size": 512, "comment": "Tunnel Server AuthID"}, {"name": "szTunnelClientEndpointIp", "type": "bytes", "size": 512, "comment": "Tunnel Client Endpoint Ip"}, {"name": "szTunnelServerEndpointIp", "type": "bytes", "size": 512, "comment": "Tunnel Server Endpoint Ip"}, {"name": "szTunnelAssignmentID", "type": "bytes", "size": 512, "comment": "Tunnel Assignment ID"}]}, {"name": "szState", "type": "bytes", "size": 1024, "comment": "State"}, {"name": "stIncreaseDataFlow", "type": "record", "array": true, "size": 64, "comment": "Increase Data Flow Info", "fields": [{"name": "duUpLinkForwardBytes", "type": "uint64", "comment": "UpLink Forward Bytes"}, {"name": "duUpLinkForwardPackets", "type": "uint64", "comment": "UpLink Forward Packets"}, {"name": "duDownLinkForwardBytes", "type": "uint64", "comment": "DownLink Forward Bytes"}, {"name": "duDownLinkForwardPackets", "type": "uint64", "comment": "DownLink Forward Packets"}, {"name": "duUpLinkDropBytes", "type": "uint64", "comment": "UpLink Drop Bytes"}, {"name": "duUpLinkDropPackets", "type": "uint64", "comment": "UpLink Drop Packets"}, {"name": "duDownLinkDropBytes", "type": "uint64", "comment": "DownLink Drop Bytes"}, {"name": "duDownLinkDropPackets", "type": "uint64", "comment": "DownLink Drop Packets"}, {"name": "duV6UpLinkForwardBytes", "type": "uint64", "comment": "V6 UpLink Forward Bytes"}, {"name": "duV6UpLinkForwardPackets", "type": "uint64", "comment": "V6 UpLink Forward Packets"}, {"name": "duV6DownLinkForwardBytes", "type": "uint64", "comment": "V6 DownLink Forward Bytes"}, {"name": "duV6DownLinkForwardPackets", "type": "uint64", "comment": "V6 DownLink Forward Packets"}, {"name": "duV6UpLinkDropBytes", "type": "uint64", "comment": "V6 UpLink Drop Bytes"}, {"name": "duV6UpLinkDropPackets", "type": "uint64", "comment": "V6 UpLink Drop Packets"}, {"name": "duV6DownLinkDropBytes", "type": "uint64", "comment": "V6 DownLink Drop Bytes"}, {"name": "duV6DownLinkDropPackets", "type": "uint64", "comment": "V6 DownLink Drop Packets"}]}, {"name": "stIdleCut", "type": "record", "comment": "Idle Cut Info", "fields": [{"name": "ulIdleCutTime", "type": "uint32", "comment": "Idle Cut Time"}, {"name": "ulIdleCutLeftTime", "type": "uint32", "comment": "Idle Cut Left Time"}, {"name": "ulIdleCutFlow", "type": "uint32", "comment": "Idle Cut Flow"}, {"name": "duIdleCutFlow", "type": "uint64", "comment": "Idle Cut Flow"}, {"name": "duIdleCutLeftFlow", "type": "uint64", "comment": "Idle Cut Left Flow"}, {"name": "ucIdleCutFlowDirection", "type": "uint8", "comment": "Idle Cut Flow Direction"}, {"name": "ucIdleCutSecond", "type": "uint8", "comment": "Idle Cut Second"}]}, {"name": "szRedirectURL", "type": "bytes", "size": 2048, "comment": "Redirect URL"}, {"name": "stWlanInfo", "type": "record", "comment": "<PERSON><PERSON>", "fields": [{"name": "ucWorkGroupID", "type": "uint8", "comment": "Work GroupID"}, {"name": "ulVapID", "type": "uint32", "comment": "VapID"}, {"name": "ulVACNodeID", "type": "uint32", "comment": "VAC NodeID"}, {"name": "ucVapHaveUpdateCounter", "type": "uint8", "comment": "Vap Have Update Counter"}, {"name": "szApMac", "type": "fixed", "size": 6, "comment": "Ap Mac"}, {"name": "ucRadioId", "type": "uint8", "comment": "Radio Id"}, {"name": "ulApId", "type": "uint32", "comment": "Ap Id"}, {"name": "ucIsFastRoam", "type": "uint8", "comment": "Fast Roam"}, {"name": "stAuthACIP_enIpVersion", "type": "uint32", "comment": "Auth AC IP Ip Version"}, {"name": "stAuthACIP_stIpAddr", "type": "fixed", "size": 16, "comment": "Auth ACIP Ip Addr"}, {"name": "stPeerACIP_enIpVersion", "type": "uint32", "comment": "Peer ACIP IpVersion"}, {"name": "stPeerACIP_stIpAddr", "type": "fixed", "size": 16, "comment": "Peer ACIP Ip Addr"}, {"name": "ulFwdMode", "type": "uint32", "comment": "Fwd Mode"}, {"name": "usServiceVlan", "type": "uint16", "comment": "Service Vlan"}, {"name": "ucWirelessAccessType", "type": "uint8", "comment": "Wireless Access Type"}, {"name": "cNiRssi", "type": "int8", "comment": "<PERSON><PERSON>"}, {"name": "lTerminalPosX", "type": "int32", "comment": "Terminal PosX"}, {"name": "lTerminalPosY", "type": "int32", "comment": "Terminal PosY"}, {"name": "ucProcApFlag", "type": "uint8", "comment": "Proc Ap Flag"}, {"name": "ucInterRoamFlag", "type": "uint8", "comment": "Proc Ap Flag"}, {"name": "ucAuthPointFlag", "type": "uint8", "comment": "Inter Roam Flag"}, {"name": "ucAPAuthorFlag", "type": "uint8", "comment": "AP Author Flag"}, {"name": "ucIfL3Roam", "type": "uint8", "comment": "L3 Roam"}, {"name": "ucIfSameCentralAP", "type": "uint8", "comment": "Same Central AP"}, {"name": "szApName", "type": "bytes", "size": 512, "comment": "Ap Name"}, {"name": "szSsidName", "type": "bytes", "size": 512, "comment": "Ssid Name"}, {"name": "szInterRoamPreDomainBuf", "type": "bytes", "size": 512, "comment": "Inter Roam Pre Domain Buf"}, {"name": "logExtendInfo", "type": "bytes", "size": 512, "comment": "log Extend Info"}, {"name": "szPMK", "type": "bytes", "size": 512, "comment": "PMK"}]}, {"name": "stWebAuth", "type": "record", "comment": "Web Auth Info", "fields": [{"name": "usPortal2PreFlag", "type": "uint16", "comment": "Portal 2 Pre Flag"}, {"name": "ucPortalVer", "type": "uint8", "comment": "Portal Version"}, {"name": "ucSyncUserExitTimes", "type": "uint8", "comment": "Sync User Exit Times"}, {"name": "ucPortalSvrIndex", "type": "uint8", "comment": "Portal Svr Index"}, {"name": "ucBakPortalSvrIndex", "type": "uint8", "comment": "Bak Portal Svr Index"}, {"name": "ulWebAuthServerIP", "type": "uint32", "comment": "Web Auth ServerIP"}, {"name": "ulWebAuthVrf", "type": "uint32", "comment": "Web Auth Vrf"}, {"name": "ulWebServerSrcIP", "type": "uint32", "comment": "Web Server Src IP"}, {"name": "ucWebUserIPType", "type": "uint8", "comment": "Web User IP Type"}, {"name": "ucIfWeChatAuth", "type": "uint8", "comment": "<PERSON><PERSON><PERSON>"}, {"name": "stIPv6AddrWebAuth", "type": "bytes", "size": 1024, "comment": "IPv6 Addr Web Auth"}]}, {"name": "szUserClass", "type": "bytes", "size": 4096, "comment": "User Class"}, {"name": "stAuthSeq", "type": "record", "comment": "Auth Seq Info", "fields": [{"name": "astAuthTrack", "type": "record", "array": true, "size": 32, "comment": "Auth Track", "fields": [{"name": "ucAccessType", "type": "uint8", "comment": "Access Type"}, {"name": "ucFailTimes", "type": "uint8", "comment": "Fail Times"}, {"name": "ucSuccFlag", "type": "uint8", "comment": "Success Flag"}]}, {"name": "ucActiveIndex", "type": "uint8", "comment": "Active Index"}, {"name": "ucTypeFailCount", "type": "uint8", "comment": "Type Fail Count"}]}, {"name": "usAclID", "type": "bytes", "size": 1024, "comment": "AclID"}, {"name": "usAclV6ID", "type": "bytes", "size": 1024, "comment": "AclV6ID"}, {"name": "pszAcl1", "type": "bytes", "size": 12288, "comment": "Acl1"}, {"name": "pszAcl2", "type": "bytes", "size": 12288, "comment": "Acl2"}, {"name": "szPushUrl", "type": "bytes", "size": 2048, "comment": "PushUrl"}, {"name": "stNacPPPInfo", "type": "record", "comment": "Nac PPP Info", "fields": [{"name": "ulVTIfIndex", "type": "uint32", "comment": "VTIfIndex"}, {"name": "ucNeedCutPPP", "type": "uint8", "comment": "Need Cut PPP"}, {"name": "ul<PERSON>s", "type": "uint32", "comment": "Mss"}, {"name": "ulPhyTxIf", "type": "uint32", "comment": "Phy Tx If"}, {"name": "szCallingNumber", "type": "bytes", "size": 1024, "comment": "Calling Number"}, {"name": "szCalledNumber", "type": "bytes", "size": 1024, "comment": "Called Number"}]}, {"name": "szOption82Buf", "type": "bytes", "size": 1024, "comment": "Option82 Buf"}, {"name": "szOption60Buf", "type": "bytes", "size": 1024, "comment": "Option60 Buf"}, {"name": "stSlotFlag_aulBitMap", "type": "bytes", "size": 128, "comment": "Slot Flag Bit Map"}, {"name": "stLastSendSlotFlag_aulBitMap", "type": "bytes", "size": 128, "comment": "Last Send Slot Flag Bit Map"}, {"name": "stDeviceInfo", "type": "record", "comment": "Device Info", "fields": [{"name": "szDeviceMac", "type": "fixed", "size": 6, "comment": "<PERSON><PERSON>"}, {"name": "stDhcpOption", "type": "bytes", "size": 12288, "comment": "Dhcp Option"}, {"name": "szUAInfo", "type": "bytes", "size": 1024, "comment": "UA Info"}, {"name": "szDeviceType", "type": "bytes", "size": 256, "comment": "Device Type"}]}, {"name": "szAccessUserName", "type": "bytes", "size": 1024, "comment": "Access User Name"}, {"name": "szSendKeyPMK", "type": "bytes", "size": 512, "comment": "Send Key PMK"}, {"name": "szChargeableIdentity", "type": "bytes", "size": 1024, "comment": "Chargeable Identity"}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "cib_nac_ext_data", "fields": ["ulCID", "ulCidPrefix"], "constraints": {"unique": true}, "comment": "key of cib_nac_ext_data"}, {"name": "localhash_key", "index": {"type": "<PERSON><PERSON><PERSON>"}, "node": "cib_nac_ext_data", "fields": ["ulCidPrefix"], "constraints": {"unique": false}, "comment": "local key of cib_nac_ext_data"}]}