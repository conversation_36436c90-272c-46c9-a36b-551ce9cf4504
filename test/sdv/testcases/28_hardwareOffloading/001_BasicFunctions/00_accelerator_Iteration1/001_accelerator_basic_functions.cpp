/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-07-05 14:44:19
 * @FilePath: \GMDBV5\test\sdv\testcases\28_hardwareOffloading\001_BasicFunctions\00_accelerator_Iteration1\001_accelerator_basic_functions.cpp
 * @Description: 
 * @LastEditors: t<PERSON><PERSON><PERSON> 
 * @LastEditTime: 2024-07-29 16:32:04
 */
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include "gtest/gtest.h"

#include "t_datacom_lite.h"
#include "t_rd_common.h"
#include "../common/hash_util.h"
#include "acceletatorTest.h"

using namespace std;

#define DB_MAX_UINT32 0xFFFFFFFF
#define THREAD_NUM 100

char g_cfgName[MAX_CMD_SIZE] = {0};
char g_errorMsg[MAX_CMD_SIZE] = {0};

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char g_command[MAX_CMD_SIZE];
pthread_barrier_t g_barrier;

char g_configJson[128] = "{\"max_record_count\" : 100000}";


class HardWare_Offloading_Test : public testing::Test {
public:
    static void SetUpTestCase()
    {
        // 配置相关环境变量及重启server
        InitCfg();

        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    };

    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        testEnvClean();

        // 恢复配置文件
        RecoverCfg();
    };
    virtual void SetUp();
    virtual void TearDown();
};

void HardWare_Offloading_Test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");

    // 创建同步客户端连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN(1);
}

void HardWare_Offloading_Test::TearDown()
{
    printf("\n======================TEST:END========================\n");

    AW_CHECK_LOG_END();
    // 关闭 client connection
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001.轻量化事务下，开启硬件卸载，创建简单表，只包含cceh模式的primary索引，对索引列执行单条增删改查操作，同时查询索引视图STORAGE_HASH_INDEX_STAT和STORAGE_HASH_COLLISION_STAT
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/simple_table_with_cceh_primary_only.gmjson", &labelJson);

    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);

    // 查视图V$STORAGE_HASH_COLLISION_STAT
    char command02[512] = {0};
    char const *viewname02 = "V\\$STORAGE_HASH_COLLISION_STAT";
    (void)snprintf(command02, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output2.txt", g_toolPath, g_connServer,
        viewname02, labelName);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum = 1;
    TestGmsysviewLabelStorageHashIndex(labelName, expectHacHashIndexNum);

    // 查询视图V$STORAGE_HASH_COLLISION_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum01 = 1;
    uint32_t expectMultiHashIndexNum = 0;
    TestGmsysviewLabelStorageHashCollision(labelName, expectHacHashIndexNum01, expectMultiHashIndexNum);
    
    // 插入数据
    int dataNum = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t f1_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 插入唯一性冲突数据
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    }

    // 查询插入的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f1InsertValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    
    for (uint32_t i = 0; i < dataNum; i++) {
        
        ret = GmcSetIndexKeyName(g_stmt, "T1_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新被替换成HacHash后的primary索引数据
        uint32_t f0UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_OBJECT, ret);
        uint8_t f1UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1UpdateValue, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询更新后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < dataNum; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T1_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询删除后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 002.轻量化事务下，开启硬件卸载，创建简单表，只包含chain模式的primary索引，对索引列执行单条增删改查操作，同时查询索引视图STORAGE_HASH_INDEX_STAT和STORAGE_HASH_COLLISION_STAT
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/simple_table_with_chain_primary_only.gmjson", &labelJson);

    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);

    // 查视图V$STORAGE_HASH_COLLISION_STAT
    char command02[512] = {0};
    char const *viewname02 = "V\\$STORAGE_HASH_COLLISION_STAT";
    (void)snprintf(command02, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output2.txt", g_toolPath, g_connServer,
        viewname02, labelName);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum = 1;
    TestGmsysviewLabelStorageHashIndex(labelName, expectHacHashIndexNum);

    // 查询视图V$STORAGE_HASH_COLLISION_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum01 = 1;
    uint32_t expectMultiHashIndexNum = 0;
    TestGmsysviewLabelStorageHashCollision(labelName, expectHacHashIndexNum01, expectMultiHashIndexNum);

    
    // 插入数据
    int dataNum = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t f1_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 插入唯一性冲突数据
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    }

    // 查询插入的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f1InsertValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    
    for (uint32_t i = 0; i < dataNum; i++) {
        
        ret = GmcSetIndexKeyName(g_stmt, "T1_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新被替换成HacHash后的primary索引数据
        uint32_t f0UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_OBJECT, ret);
        uint8_t f1UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1UpdateValue, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询更新后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < dataNum; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T1_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询删除后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 003.轻量化事务下，开启硬件卸载，创建简单表，只包含cceh模式的localhash唯一索引、chain模式的localhash唯一索引和localhash非唯一索引，
//      对索引列执行单条增删改查操作，同时查询索引视图STORAGE_HASH_INDEX_STAT和STORAGE_HASH_COLLISION_STAT
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/simple_table_with_localhash_only.gmjson", &labelJson);

    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);

    // 查视图V$STORAGE_HASH_COLLISION_STAT
    char command02[512] = {0};
    char const *viewname02 = "V\\$STORAGE_HASH_COLLISION_STAT";
    (void)snprintf(command02, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output2.txt", g_toolPath, g_connServer,
        viewname02, labelName);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum = 2;
    TestGmsysviewLabelStorageHashIndex(labelName, expectHacHashIndexNum);

    // 查询视图V$STORAGE_HASH_COLLISION_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum01 = 2;
    uint32_t expectMultiHashIndexNum = 1;
    TestGmsysviewLabelStorageHashCollision(labelName, expectHacHashIndexNum01, expectMultiHashIndexNum);

    // 插入数据
    int dataNum = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t f1_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint16_t f2_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT16, &f2_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 插入唯一性冲突数据
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint16_t f2_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT16, &f2_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNIQUE_VIOLATION, ret);
    }

    // 查询插入的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, f1InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint16_t f2InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &f2InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f2InsertValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (uint32_t i = 0; i < dataNum; i++) {
        ret = GmcSetIndexKeyName(g_stmt, "localhash_key_1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F0字段的数据
        uint32_t f0UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F1字段的数据
        uint8_t f1UpdateValue = i ;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1UpdateValue, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F2字段的数据
        int16_t f2UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT16, &f2UpdateValue, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询更新后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt + 10, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f1UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint16_t f2UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &f2UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt + 10, f2UpdateValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < dataNum; i++) {
        uint32_t deleteData = i + 10;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &deleteData, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "localhash_key_1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询删除后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint16_t f2UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &f2UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt + 10, f2UpdateValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 004.开启v3选项，轻量化事务下，开启硬件卸载，创建简单表，只包含cceh模式的hashcluster唯一索引和cceh模式的hashcluster非唯一索引，
//      对索引列执行单条增删改查操作，同时查询索引视图STORAGE_HASH_INDEX_STAT和STORAGE_HASH_COLLISION_STAT
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/simple_table_with_hashcluster_only_V3.gmjson", &labelJson);

    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);

    // 查视图V$STORAGE_HASH_COLLISION_STAT
    char command02[512] = {0};
    char const *viewname02 = "V\\$STORAGE_HASH_COLLISION_STAT";
    (void)snprintf(command02, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output2.txt", g_toolPath, g_connServer,
        viewname02, labelName);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum = 0;
    TestGmsysviewLabelStorageHashIndex(labelName, expectHacHashIndexNum);

    // 查询视图V$STORAGE_HASH_COLLISION_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum01 = 0;
    uint32_t expectMultiHashIndexNum = 2;
    TestGmsysviewLabelStorageHashCollision(labelName, expectHacHashIndexNum01, expectMultiHashIndexNum);
    
    // 插入数据
    int dataNum = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t f1_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 查询插入的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, f1InsertValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (uint32_t i = 0; i < dataNum; i++) {
        ret = GmcSetIndexKeyName(g_stmt, "hashcluster_key_1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F0字段的数据
        uint32_t f0UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F1字段的数据
        uint8_t f1UpdateValue = i ;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1UpdateValue, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询更新后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt + 10, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f1UpdateValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < dataNum; i++) {
        uint32_t deleteData = i + 10;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &deleteData, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "hashcluster_key_1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询删除后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 006.轻量化事务下，开启硬件卸载，创建复杂表，只包含cceh模式的primary索引，对索引列执行单条增删改查操作，同时查询索引视图STORAGE_HASH_INDEX_STAT和STORAGE_HASH_COLLISION_STAT
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/complex_table_with_cceh_primary_only.gmjson", &labelJson);

    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);

    // 查视图V$STORAGE_HASH_COLLISION_STAT
    char command02[512] = {0};
    char const *viewname02 = "V\\$STORAGE_HASH_COLLISION_STAT";
    (void)snprintf(command02, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output2.txt", g_toolPath, g_connServer,
        viewname02, labelName);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum = 1;
    TestGmsysviewLabelStorageHashIndex(labelName, expectHacHashIndexNum);

    // 查询视图V$STORAGE_HASH_COLLISION_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum01 = 1;
    uint32_t expectMultiHashIndexNum = 0;
    TestGmsysviewLabelStorageHashCollision(labelName, expectHacHashIndexNum01, expectMultiHashIndexNum);
    
    // 插入数据
    int dataNum = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t f1_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 插入唯一性冲突数据
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    }

    // 查询插入的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f1InsertValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    
    for (uint32_t i = 0; i < dataNum; i++) {
        
        ret = GmcSetIndexKeyName(g_stmt, "T1_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新被替换成HacHash后的primary索引数据
        uint32_t f0UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_OBJECT, ret);
        uint8_t f1UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1UpdateValue, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询更新后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < dataNum; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T1_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询删除后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 007.轻量化事务下，开启硬件卸载，创建复杂表，只包含chain模式的primary索引，对索引列执行单条增删改查操作，同时查询索引视图STORAGE_HASH_INDEX_STAT和STORAGE_HASH_COLLISION_STAT
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/complex_table_with_chain_primary_only.gmjson", &labelJson);

    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);

    // 查视图V$STORAGE_HASH_COLLISION_STAT
    char command02[512] = {0};
    char const *viewname02 = "V\\$STORAGE_HASH_COLLISION_STAT";
    (void)snprintf(command02, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output2.txt", g_toolPath, g_connServer,
        viewname02, labelName);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum = 1;
    TestGmsysviewLabelStorageHashIndex(labelName, expectHacHashIndexNum);

    // 查询视图V$STORAGE_HASH_COLLISION_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum01 = 1;
    uint32_t expectMultiHashIndexNum = 0;
    TestGmsysviewLabelStorageHashCollision(labelName, expectHacHashIndexNum01, expectMultiHashIndexNum);

    
    // 插入数据
    int dataNum = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t f1_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 插入唯一性冲突数据
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    }

    // 查询插入的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f1InsertValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    
    for (uint32_t i = 0; i < dataNum; i++) {
        
        ret = GmcSetIndexKeyName(g_stmt, "T1_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新被替换成HacHash后的primary索引数据
        uint32_t f0UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_OBJECT, ret);
        uint8_t f1UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1UpdateValue, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询更新后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < dataNum; i++) {
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T1_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询删除后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 008.轻量化事务下，开启硬件卸载，创建复杂表，只包含cceh模式的localhash唯一索引、chain模式的localhash唯一索引和localhash非唯一索引，
//      对索引列执行单条增删改查操作，同时查询索引视图STORAGE_HASH_INDEX_STAT和STORAGE_HASH_COLLISION_STAT
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/complex_table_with_localhash_only.gmjson", &labelJson);

    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);

    // 查视图V$STORAGE_HASH_COLLISION_STAT
    char command02[512] = {0};
    char const *viewname02 = "V\\$STORAGE_HASH_COLLISION_STAT";
    (void)snprintf(command02, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output2.txt", g_toolPath, g_connServer,
        viewname02, labelName);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum = 2;
    TestGmsysviewLabelStorageHashIndex(labelName, expectHacHashIndexNum);

    // 查询视图V$STORAGE_HASH_COLLISION_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum01 = 2;
    uint32_t expectMultiHashIndexNum = 1;
    TestGmsysviewLabelStorageHashCollision(labelName, expectHacHashIndexNum01, expectMultiHashIndexNum);

    // 插入数据
    int dataNum = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t f1_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint16_t f2_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT16, &f2_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 插入唯一性冲突数据
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint16_t f2_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT16, &f2_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_UNIQUE_VIOLATION, ret);
    }

    // 查询插入的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, f1InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint16_t f2InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &f2InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f2InsertValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (uint32_t i = 0; i < dataNum; i++) {
        ret = GmcSetIndexKeyName(g_stmt, "localhash_key_1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F0字段的数据
        uint32_t f0UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F1字段的数据
        uint8_t f1UpdateValue = i ;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1UpdateValue, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F2字段的数据
        int16_t f2UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT16, &f2UpdateValue, sizeof(uint16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询更新后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt + 10, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f1UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint16_t f2UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &f2UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt + 10, f2UpdateValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < dataNum; i++) {
        uint32_t deleteData = i + 10;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &deleteData, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "localhash_key_1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询删除后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint16_t f2UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &f2UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt + 10, f2UpdateValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 009.开启v3选项，轻量化事务下，开启硬件卸载，创建复杂表，只包含cceh模式的hashcluster唯一索引和cceh模式的hashcluster非唯一索引，
//      对索引列执行单条增删改查操作，同时查询索引视图STORAGE_HASH_INDEX_STAT和STORAGE_HASH_COLLISION_STAT
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/complex_table_with_hashcluster_only_V3.gmjson", &labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);

    // 查视图V$STORAGE_HASH_COLLISION_STAT
    char command02[512] = {0};
    char const *viewname02 = "V\\$STORAGE_HASH_COLLISION_STAT";
    (void)snprintf(command02, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output2.txt", g_toolPath, g_connServer,
        viewname02, labelName);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum = 0;
    TestGmsysviewLabelStorageHashIndex(labelName, expectHacHashIndexNum);

    // 查询视图V$STORAGE_HASH_COLLISION_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum01 = 0;
    uint32_t expectMultiHashIndexNum = 2;
    TestGmsysviewLabelStorageHashCollision(labelName, expectHacHashIndexNum01, expectMultiHashIndexNum);
    
    // 插入数据
    int dataNum = 5;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        uint8_t f1_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 查询插入的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, f1InsertValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (uint32_t i = 0; i < dataNum; i++) {
        ret = GmcSetIndexKeyName(g_stmt, "hashcluster_key_1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F0字段的数据
        uint32_t f0UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F1字段的数据
        uint8_t f1UpdateValue = i ;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1UpdateValue, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询更新后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt + 10, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f1UpdateValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < dataNum; i++) {
        uint32_t deleteData = i + 10;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &deleteData, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "hashcluster_key_1");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询删除后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0UpdateValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1UpdateValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1UpdateValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT((cnt + 10), f1UpdateValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 011.轻量化事务下，开启硬件卸载，创建简单表，包含cceh模式的primary索引、cceh模式的localhash唯一索引、chain模式的localhash唯一索引和localhash非唯一索引、
//      cceh模式的hashcluster唯一索引、chain模式的hashcluster唯一索引、hashcluster非唯一索引，对索引列执行单条增删改查操作，同时查询索引视图STORAGE_HASH_INDEX_STAT和STORAGE_HASH_COLLISION_STAT
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/simple_table_with_primary_localhash_hashcluster.gmjson", &labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);

    // 查视图V$STORAGE_HASH_COLLISION_STAT
    char command02[512] = {0};
    char const *viewname02 = "V\\$STORAGE_HASH_COLLISION_STAT";
    (void)snprintf(command02, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output2.txt", g_toolPath, g_connServer,
        viewname02, labelName);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum = 3;
    TestGmsysviewLabelStorageHashIndex(labelName, expectHacHashIndexNum);

    // 查询视图V$STORAGE_HASH_COLLISION_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum01 = 3;
    uint32_t expectMultiHashIndexNum = 3;
    TestGmsysviewLabelStorageHashCollision(labelName, expectHacHashIndexNum01, expectMultiHashIndexNum);
    
    // 插入数据
    int dataNum = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f2_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &f2_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f3_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(f3_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f4_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_UINT32, &f4_value, sizeof(f4_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f5_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_UINT32, &f5_value, sizeof(f5_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 插入主键冲突数据
    for (int i = 0; i < dataNum; i++) {
        uint32_t updateValue = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &updateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    }

    // 查询插入的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    bool isFinish = false;
    uint64_t cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, f1InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f2InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &f2InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, f2InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F3", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f3InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F3", &f3InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f3InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F4", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f4InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F4", &f4InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f4InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F5", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f5InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F5", &f5InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, f5InsertValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 更新数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    for (uint32_t i = 0; i < dataNum; i++) {
        ret = GmcSetIndexKeyName(g_stmt, "T1_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F0字段的数据
        uint32_t f0UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_OBJECT, ret);
        // 更新F1字段的数据
        uint8_t f1UpdateValue = i ;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1UpdateValue, sizeof(uint8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 更新F2字段的数据
        uint32_t f2UpdateValue = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &f2UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F3字段的数据
        uint32_t f3UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT32, &f3UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 更新F4字段的数据
        uint32_t f4UpdateValue = i + 10;
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_UINT32, &f4UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 更新F5字段的数据
        uint32_t f5UpdateValue = i ;
        ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_UINT32, &f5UpdateValue, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询更新后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f1InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f2InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &f2InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f2InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F3", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f3InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F3", &f3InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt + 10, f3InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F4", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f4InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F4", &f4InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt + 10, f4InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F5", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f5InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F5", &f5InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f5InsertValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 删除数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < dataNum; i++) {
        uint32_t deleteData = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &deleteData, sizeof(uint32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "T1_PK");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 查询删除后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(0, cnt);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 012.轻量化事务下，开启硬件卸载，创建复杂表，包含cceh模式的localhash唯一索引、chain模式的localhash唯一索引和localhash非唯一索引、cceh模式的hashcluster唯一索引、
//      chain模式的hashcluster唯一索引、hashcluster非唯一索引，索引列包含变长字段，查询索引视图STORAGE_HASH_INDEX_STAT和STORAGE_HASH_COLLISION_STAT
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/complex_table_with_primary_localhash_hashcluster.gmjson", &labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);

    // 查视图V$STORAGE_HASH_COLLISION_STAT
    char command02[512] = {0};
    char const *viewname02 = "V\\$STORAGE_HASH_COLLISION_STAT";
    (void)snprintf(command02, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output2.txt", g_toolPath, g_connServer,
        viewname02, labelName);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum = 3;
    TestGmsysviewLabelStorageHashIndex(labelName, expectHacHashIndexNum);

    // 查询视图V$STORAGE_HASH_COLLISION_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum01 = 3;
    uint32_t expectMultiHashIndexNum = 3;
    TestGmsysviewLabelStorageHashCollision(labelName, expectHacHashIndexNum01, expectMultiHashIndexNum);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 013.轻量化事务下，开启硬件卸载，创建简单表，包含chained模式的primary索引、chained模式的localhash唯一索引、chained模式的localhash唯一索引和localhash非唯一索引、
//      cceh模式的hashcluster唯一索引、chain模式的hashcluster唯一索引、hashcluster非唯一索引，插入数据触发扩容
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel，并设置"init_hash_capacity": 10000
    char *labelJson = NULL;
    readJanssonFile("./schema_file/simple_table_with_primary_expansion_capacity.gmjson", &labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);

    // 查视图V$STORAGE_HASH_COLLISION_STAT
    char command02[512] = {0};
    char const *viewname02 = "V\\$STORAGE_HASH_COLLISION_STAT";
    (void)snprintf(command02, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output2.txt", g_toolPath, g_connServer,
        viewname02, labelName);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT获取TOTAL_MEMORY_SIZE的值
    uint32_t firstHashIndexCapacity[3];
    TestIfExpansionCapacity(labelName, firstHashIndexCapacity);

    // 查询视图V$STORAGE_HASH_INDEX_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum = 3;
    TestGmsysviewLabelStorageHashIndex(labelName, expectHacHashIndexNum);

    // 查询视图V$STORAGE_HASH_COLLISION_STAT并校验视图数据正确性
    uint32_t expectHacHashIndexNum01 = 3;
    uint32_t expectMultiHashIndexNum = 3;
    TestGmsysviewLabelStorageHashCollision(labelName, expectHacHashIndexNum01, expectMultiHashIndexNum);
    
    // 插入800条数据
    int dataNum = 800;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f2_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &f1_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f3_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT32, &f0_value, sizeof(f3_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f4_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_UINT32, &f0_value, sizeof(f4_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f5_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_UINT32, &f1_value, sizeof(f5_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    system(command01);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT获取TOTAL_MEMORY_SIZE的值
    uint32_t secondHashIndexCapacity[3];
    TestIfExpansionCapacity(labelName, secondHashIndexCapacity);

    // 插入10001条数据
    dataNum = 10001;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 800; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f2_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &f1_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f3_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT32, &f0_value, sizeof(f3_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f4_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_UINT32, &f0_value, sizeof(f4_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f5_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_UINT32, &f1_value, sizeof(f5_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    system(command01);
    system(command02);
    system("cat output1.txt;cat output2.txt;");

    // 查询视图V$STORAGE_HASH_INDEX_STAT获取TOTAL_MEMORY_SIZE的值
    uint32_t thirdHashIndexCapacity[3];
    TestIfExpansionCapacity(labelName, thirdHashIndexCapacity);
    for (int i = 0; i < 3; i++){
        if(firstHashIndexCapacity[i] >= secondHashIndexCapacity[i] || secondHashIndexCapacity[i] >= thirdHashIndexCapacity[i]){
            AW_MACRO_ASSERT_EQ_INT(GMERR_OK, 1);
        }

    }

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 014.开启v3选项，轻量化事务下，开启硬件卸载，创建简单表，只包含cceh模式的hashcluster唯一索引和cceh模式的hashcluster非唯一索引，
//     查询索引视图STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/simple_table_with_hashcluster_only_V3.gmjson", &labelJson);

    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);
    system("cat output1.txt");

    // 查询视图V$STORAGE_CLUSTERED_HASH_VERTEX_LABEL_STAT
    uint32_t expectHashClusterIndexNum = 0;
    TestGmsysviewLabelStorageHashClusterIndex(labelName, expectHashClusterIndexNum);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 016.轻量化事务下，开启硬件卸载，创建复杂表，只包含cceh模式的localhash唯一索引、chain模式的localhash唯一索引和localhash非唯一索引，
//      查询索引视图STORAGE_HASH_LINKLIST_INDEX_STAT 
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/complex_table_with_localhash_only.gmjson", &labelJson);

    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_LINKLIST_INDEX_STAT ";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);
    system("cat output1.txt");

    // 查询视图V$STORAGE_HASH_LINKLIST_INDEX_STAT并校验视图数据正确性
    uint32_t expectLocalHashIndexNum = 0;
    TestGmsysviewLabelStorageHashLinklistIndex(labelName, expectLocalHashIndexNum);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 017.轻量化事务下，开启硬件卸载，创建简单表，包含cceh模式的primary索引、cceh模式的localhash唯一索引、chain模式的localhash唯一索引和localhash非唯一索引、
//      cceh模式的hashcluster唯一索引、chain模式的hashcluster唯一索引、hashcluster非唯一索引，批量replace存在唯一冲突的数据
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/simple_table_with_primary_localhash_hashcluster.gmjson", &labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 批量replace数据
     uint32_t replaceNum = 20;
    BatchReplaceVertexForPathWithProperties(g_conn, g_stmt, labelName, replaceNum);

    // 查询插入的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    bool isFinish = false;
    uint32_t cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt % 10, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt % 10, f1InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f2InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &f2InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt % 10, f2InsertValue); //nnnn

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F3", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f3InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F3", &f3InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt % 10, f3InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F4", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f4InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F4", &f4InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt % 10, f4InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F5", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f5InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F5", &f5InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt % 10, f5InsertValue); //nnnn

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(10, cnt);

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 018.轻量化事务下，开启硬件卸载，修改multiHashBucketCount配置项为0
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"multiHashBucketCount=0\"");
    system("sh $TEST_HOME/tools/start.sh ");

    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_CHECK_LOG_BEGIN(1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 019.轻量化事务下，开启硬件卸载，修改multiHashBucketCount配置项为10000001
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"multiHashBucketCount=10000001\"");
    system("sh $TEST_HOME/tools/start.sh ");

    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_CHECK_LOG_BEGIN(1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 020.利用gmadmin工具修改multiHashBucketCount配置项为16385
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 使用工具修改配置项
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "multiHashBucketCount");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 16385);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "execute gmadmin corresponding function, ret = %d!", GMERR_DATA_EXCEPTION);
    // 预期报错
    system(g_command);
    int ret = executeCommand(g_command, "set config.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 021.轻量化事务下，开启硬件卸载，修改hacMode配置项为-1
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"hacMode=-1\"");
    system("sh $TEST_HOME/tools/start.sh ");

    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_CHECK_LOG_BEGIN(1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 022.轻量化事务下，开启硬件卸载，修改hacMode配置项为3
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"hacMode=3\"");
    system("sh $TEST_HOME/tools/start.sh ");

    int ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_CONNECTION_FAILURE, ret);
    AW_CHECK_LOG_BEGIN(1);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 023.利用gmadmin工具修改hacMode配置项为1
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    
    // 使用工具修改配置项
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "hacMode");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "execute gmadmin corresponding function, ret = %d!", GMERR_DATA_EXCEPTION);
    // 预期报错
    int ret = executeCommand(g_command, "set config.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}


// 028.轻量化事务下，开启硬件卸载，创建简单表，包含cceh模式的primary索引、cceh模式的localhash唯一索引、chain模式的localhash唯一索引和localhash非唯一索引、
//      cceh模式的hashcluster唯一索引、chain模式的hashcluster唯一索引、hashcluster非唯一索引，插入数据后再删除部分数据触发缩容
TEST_F(HardWare_Offloading_Test, DISABLED_HardWare_Offloading_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    // 开启聚簇容器
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("${TEST_HOME}/tools/modifyCfg.sh memCompactEnable=1");
    system("${TEST_HOME}/tools/modifyCfg.sh  workerHungThreshold=3,4,5");
    system("${TEST_HOME}/tools/modifyCfg.sh minFragmentationRateThreshold=1");
    system("sh $TEST_HOME/tools/start.sh ");

    int ret = close_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);
    testEnvClean();
    ret = testEnvInit();
    EXPECT_EQ(GMERR_OK, ret);
    ret = create_epoll_thread();
    ASSERT_EQ(GMERR_OK, ret);

     // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN(1);

    const char *labelName = "T1";
    char configJson[128] = "{\"max_record_count\" : 100000, \"defragmentation\": true}";

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/simple_table_with_defragmentation.gmjson", &labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 插入数据
    int dataNum = 10000;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < dataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f2_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &f2_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f3_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(f3_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f4_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_UINT32, &f4_value, sizeof(f4_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f5_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_UINT32, &f5_value, sizeof(f5_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 查询插入的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    bool isFinish = false;
    uint64_t cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        uint32_t size = 0;
        bool isNull = false;
        // 通过name获取property
        ret = GmcGetVertexPropertySizeByName(g_stmt, "F0", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f0InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F0", &f0InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f0InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F1", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F1", &f1InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, f1InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F2", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f2InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F2", &f2InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, f2InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F3", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f3InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F3", &f3InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f3InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F4", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f4InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F4", &f4InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(cnt, f4InsertValue);

        ret = GmcGetVertexPropertySizeByName(g_stmt, "F5", &size);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f5InsertValue = 0;
        ret = GmcGetVertexPropertyByName(g_stmt, "F5", &f5InsertValue, size, &isNull);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(2, f5InsertValue);

        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum, cnt);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    char command01[512] = {0};
    char const *viewname01 = "V\\$STORAGE_HASH_INDEX_STAT";
    (void)snprintf(command01, 512, "%s/gmsysview -s %s -q %s -f LABEL_NAME=%s > output1.txt", g_toolPath, g_connServer,
        viewname01, labelName);
    system(command01);
    system("cat output1.txt");

    char cmdOutput[64] = {0};
    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HASH_INDEX_STAT", "TOTAL_MEMORY_SIZE", cmdOutput, 64);
    uint32_t memorySize1 = atoi(cmdOutput);

    // 删除数据, 每10条删9条
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < dataNum; i++) {
        if (i % 10 < 9) {
            uint32_t deleteData = i;
            ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_UINT32, &deleteData, sizeof(uint32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyName(g_stmt, "T1_PK");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcExecute(g_stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    // 查询删除后的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcExecute(g_stmt));
    isFinish = false;
    cnt = 0;
    while (true) {
        ret = GmcFetch(g_stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(dataNum / 10, cnt);

    system(command01);
    system("cat output1.txt");

    (void)memset_s(cmdOutput, sizeof(cmdOutput), 0, sizeof(cmdOutput));
    GetViewFieldResult("V\\$STORAGE_HASH_INDEX_STAT", "TOTAL_MEMORY_SIZE", cmdOutput, 64);
    uint32_t memorySize2 = atoi(cmdOutput);
    EXPECT_LT(memorySize2, memorySize1);

    // 查视图V$STORAGE_HASH_INDEX_STAT
    system(command01);
    system("cat output1.txt");



    // 视图查询1
    AW_FUN_Log(LOG_STEP, "视图查询1");
    usleep(10000);
    system("gmsysview -q V\\$STORAGE_HEAP_STAT -f LABEL_NAME=T1");
    const char *viewName = "V\\$STORAGE_HEAP_STAT";
    uint64_t activeDefragmentation = 0;
    uint64_t defragmentationCnt = 0;
    TestGetDfxFieldIntVal(viewName, labelName, "ACTIVE_DEFRAGMENTATION:", &activeDefragmentation);
    AW_MACRO_EXPECT_EQ_INT(1, activeDefragmentation);

    while (true) {
        usleep(10000);
        system("gmsysview -q V\\$STORAGE_HEAP_STAT -f LABEL_NAME=T1");
        const char *viewName = "V\\$STORAGE_HEAP_STAT";
        TestGetDfxFieldIntVal(viewName, labelName, "ACTIVE_DEFRAGMENTATION:", &activeDefragmentation);
        TestGetDfxFieldIntVal(viewName, labelName, "DEFRAGMENTATION_CNT:", &defragmentationCnt);
        if (activeDefragmentation != 1) {
            break;
        }
    }
    
    AW_MACRO_EXPECT_NE_INT(0, defragmentationCnt);



    // 删除VertexLabel
    ret = GmcDeleteAllFast(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

void *client_thread_029_01(void *args)
{
    pthread_barrier_wait(&g_barrier);
    int status = system("./client_029_01 ");
    AW_MACRO_EXPECT_NE_INT(-1, status);
    if (WIFEXITED(status))
    {
        int ret = WEXITSTATUS(status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
void *client_thread_029_02(void *args)
{
    pthread_barrier_wait(&g_barrier);
    int status = system("./client_029_02 ");
    AW_MACRO_EXPECT_NE_INT(-1, status);
    if (WIFEXITED(status))
    {
        int ret = WEXITSTATUS(status);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
// 029.轻量化事务下，开启硬件卸载，创建简单表，包含cceh模式的primary索引、cceh模式的localhash唯一索引、chain模式的localhash唯一索引和localhash非唯一索引、
//      cceh模式的hashcluster唯一索引、chain模式的hashcluster唯一索引、hashcluster非唯一索引，直连读时客户端异常退出，查看锁资源是否正常释放
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    int ret = 0;
    const char *labelName = "T1";
    char *labelJson = NULL;

    // 创建VertexLabel
    readJanssonFile("./schema_file/simple_table_with_primary_localhash_hashcluster.gmjson", &labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    int firstInsertDataNum = 10000;
    ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    for (int i = 0; i < firstInsertDataNum; i++) {
        uint32_t f0_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_UINT32, &f0_value, sizeof(f0_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint8_t f1_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_UINT8, &f1_value, sizeof(f1_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f2_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_UINT32, &f2_value, sizeof(f2_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f3_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(f3_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f4_value = i;
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_UINT32, &f4_value, sizeof(f4_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        uint32_t f5_value = 2;
        ret = GmcSetVertexProperty(g_stmt, "F5", GMC_DATATYPE_UINT32, &f5_value, sizeof(f5_value));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(g_stmt);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    // 两个进程并发
    pthread_t read_thread, insert_thread;
    pthread_barrier_init(&g_barrier, NULL, 2);

    pthread_create(&read_thread, NULL, client_thread_029_01, NULL);
    pthread_create(&insert_thread, NULL, client_thread_029_02, NULL);

    pthread_join(read_thread, NULL);
    pthread_join(insert_thread, NULL);

    pthread_barrier_destroy(&g_barrier);

    // 删除VertexLabel
    ret = GmcDeleteAllFast(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 030.轻量化事务下，开启硬件卸载，创建简单表，包含cceh模式的primary索引、cceh模式的localhash唯一索引、chain模式的localhash唯一索引和localhash非唯一索引、
//      cceh模式的hashcluster唯一索引、chain模式的hashcluster唯一索引、hashcluster非唯一索引，执行多线程并发读写操作
TEST_F(HardWare_Offloading_Test, HardWare_Offloading_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    const char *labelName = "T1";
    int ret = 0;

    // 创建VertexLabel
    char *labelJson = NULL;
    readJanssonFile("./schema_file/simple_table_with_for_concurrency.gmjson", &labelJson);
    ret = GmcCreateVertexLabel(g_stmt, labelJson, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t threadIds[THREAD_NUM];

    for (int i = 0; i < THREAD_NUM; i++) {

        if (i % 5 == 4){
            ret = pthread_create(&threadIds[i], NULL, QueryData, NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }else {
            ret = pthread_create(&threadIds[i], NULL, InsertData, NULL);
            EXPECT_EQ(GMERR_OK, ret);
        }
        
    }
    for (int i = 0; i < THREAD_NUM; i++) {
        pthread_join(threadIds[i], NULL);
    }

    // 删除VertexLabel
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    free(labelJson);

    AW_FUN_Log(LOG_STEP, "test end.");
}


