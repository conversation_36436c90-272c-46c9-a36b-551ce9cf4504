/*****************************************************************************
 Description  : 异步回调支持超时测试
 Author       : 覃乾宝 qwx995465
 Modification :
 Date         : 2021/09/13
*****************************************************************************/
extern "C" {
}
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
 
using namespace std;

GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
GmcStmtT *g_stmt_async = NULL;
GmcConnT *g_conn_async = NULL;

int affectRows;
unsigned int len;
char *normal_vertexlabel_schema = NULL;
char *normal_graph_vertex_label_schema = NULL;
char *normal_graph_edge_label_schema = NULL;
const char *normal_config_json = R"(
    {
        "max_record_count":100000
    }
)";
const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";

void set_VertexProperty_PK(GmcStmtT *stmt, int i);
void set_VertexProperty_SK(GmcStmtT *stmt, int i);
void set_VertexProperty(GmcStmtT *stmt, int i);
void query_VertexProperty(GmcStmtT *stmt, int i);

class TimeoutEpollCon : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = 0;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void TimeoutEpollCon::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    printf("[INFO] TimeoutEpollCon Start.\n");
    int ret = 0;
    char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    char errorCode3[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_EPOLL_OPERATE_FAILED);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    (void)snprintf(errorCode3, 128, "GMERR-%d", GMERR_DUPLICATE_HEARTBEAT_REGISTER);
    AW_ADD_ERR_WHITE_LIST(3, errorCode1, errorCode2, errorCode3);
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
    ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
}

void TimeoutEpollCon::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    free(normal_vertexlabel_schema);
    printf("[INFO] TimeoutEpollCon End.\n");
    AW_CHECK_LOG_END();
}

// 用户侧创建1个全局epoll对象
// create_epoll_thread 与 close_epoll_thread配套使用
int create_user_epoll_thread(EpollThreadDataT *epollData)
{
    if (epollData->isInited) {
        return 0;
    }
    sem_init(&epollData->sem, 0, 0);

    int ret = pthread_create(&epollData->epollThreadId, NULL, EpollThreadFunc, epollData);
    if (ret != 0) {
        const char *errInfo = strerror(errno);
        printf("create epoll thread failed, errmsg = %s\n", errInfo);
        return ret;
    }
    epollData->isInited = true;
    sem_wait(&epollData->sem);
    return 0;
}

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_SK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F14Value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    EXPECT_EQ(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
}

void query_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    // Get F0
    char F0Value = i;
    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F1
    unsigned char F1Value = i;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F2
    int8_t F2Value = i;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F3
    uint8_t F3Value = i;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F4
    int16_t F4Value = i;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F5
    uint16_t F5Value = i;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F6
    int32_t F6Value = i;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F8
    bool F8Value = false;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F10
    uint64_t F10Value = i;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F11
    float F11Value = i;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F12
    double F12Value = i;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F13
    uint64_t F13Value = i;
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F14
    char F14Value[] = "testver";
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F15
    char F15Value[12] = "12";
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, F15Value);
    EXPECT_EQ(GMERR_OK, ret);
    // Get F16
    char F16Value[12] = "13";
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, F16Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void vertex_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    int ret;
    static int count_timeout = 0;
    AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
    user_data->status = status;
    EXPECT_EQ(GMERR_REQUEST_TIME_OUT, user_data->status);
    // ret = testGmcGetLastError(NULL);  不是连接级 //
    // 是否需要增加lasterror显示当前超时原因，日志log是否需要显示？ EXPECT_EQ(GMERR_OK, ret);
    user_data->affectRows = affectedRows;
    EXPECT_EQ(0, user_data->affectRows);
    user_data->historyRecvNum++;
    user_data->recvNum++;
    // count_timeout++;
    printf("[INFO]epoll_timeout_count: %d\n", count_timeout++);
    printf("[ERROR_MSG_INFO] %s\n", errMsg);
}

void *thread_dml(void *args)
{
    int ret;
    int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    AsyncUserDataT data = {0};
    // 创建异步连接
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[id].userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("thread_dml: thr_id=%d ret=%d\n", id, ret);

    // insert Vertex
    int i = id;
    ret = testGmcPrepareStmtByLabelName(stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    // 关闭业务epoll线程
    ret = close_usr_epoll_thread(&g_epAsync[id]);
    EXPECT_EQ(GMERR_OK, ret);
    set_VertexProperty_PK(stmt_async, i);
    set_VertexProperty_SK(stmt_async, i);
    set_VertexProperty(stmt_async, i);
    data = {0};
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = vertex_callback;
    insertRequestCtx.userData = &data;
    ret = GmcExecuteAsync(stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 001.开启超时检测epoll线程, 关闭业务epoll线程, 多线程进行异步DML
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_001)
{
    int ret = 0;
    pthread_t thr_arr[30];
    int index[30] = {0};
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    int thr_num_epoll;  // 创建的epoll线程数根据async_sn_common.h
#ifdef ENV_RTOSV2X
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(GMERR_OK, ret);
    thr_num_epoll = (MAX_CONN_SIZE - existConnNum) / 2;
    thr_num_epoll = thr_num_epoll > 30 ? 30 : thr_num_epoll;
#else
    thr_num_epoll = 30;
#endif
    for (int i = 0; i < thr_num_epoll; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < thr_num_epoll; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_dml, (void *)&index[i]);
    }

    for (int i = 0; i < thr_num_epoll; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    // system("ipcs");
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = close_timeout_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);  // 线程里已执行close user epoll
}

void truncate_vertex_callback(void *userData, int32_t status, const char *errMsg)
{
    int ret;
    static int count_timeout = 0;
    AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
    user_data->status = status;
    EXPECT_EQ(GMERR_REQUEST_TIME_OUT, user_data->status);
    user_data->historyRecvNum++;
    user_data->recvNum++;
    printf("[INFO]epoll_timeout_count: %d\n", count_timeout++);
    printf("[ERROR_MSG_INFO] %s\n", errMsg);
}
void *thread_ddl(void *args)
{
    int ret;
    int id = *((int *)args);
    char label_schema[1024] = "";
    char labelName[20] = "";
    GmcConnT *conn_async = 0;
    GmcStmtT *stmt_async = 0;
    AsyncUserDataT data = {0};
    // 创建异步连接
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[id].userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("thread_ddl: thr_id=%d ret=%d\n", id, ret);

    // create Vertex
    ret = close_usr_epoll_thread(&g_epAsync[id]);
    EXPECT_EQ(GMERR_OK, ret);
    snprintf(label_schema, 1024,
        "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
        id, id, id);
    ret = GmcCreateVertexLabelAsync(stmt_async, label_schema, normal_config_json, truncate_vertex_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);

    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 002.开启超时检测epoll线程, 关闭业务epoll线程, 多线程进行异步DDL
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_002)
{
    int ret = 0;
    pthread_t thr_arr[30];
    int index[30] = {0};

    int thr_num_epoll;  // 创建的epoll线程数根据async_sn_common.h
#ifdef ENV_RTOSV2X
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(GMERR_OK, ret);
    thr_num_epoll = (MAX_CONN_SIZE - existConnNum) / 2;
    thr_num_epoll = thr_num_epoll > 30 ? 30 : thr_num_epoll;
#else
    thr_num_epoll = 30;
#endif
    for (int i = 0; i < thr_num_epoll; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < thr_num_epoll; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_ddl, (void *)&index[i]);
    }

    for (int i = 0; i < thr_num_epoll; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    char labelName[20] = "";
    for (int i = 0; i < thr_num_epoll; i++) {
        snprintf(labelName, 20, "T%d", i);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = close_timeout_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void batch_callback(void *userData, GmcBatchRetT *batchRet, int32_t status, const char *errMsg)
{
    if (userData) {
        int ret = 0;
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        // ret = GmcBatchDeparseRet(batchRet, (uint32_t*)&(user_data->totalNum), (uint32_t*)&(user_data->succNum));
        // ASSERT_EQ(GMERR_OK, ret); // 暂时屏蔽
        user_data->status = status;
        EXPECT_EQ(GMERR_REQUEST_TIME_OUT, user_data->status);
        user_data->historyRecvNum++;
        user_data->recvNum++;
    }
}
void *thread_batch_ddl(void *args)
{
    int ret;
    int id = *((int *)args);
    char label_schema[1024] = "";
    char labelName[20] = "";
    GmcConnT *conn_async = 0;
    GmcStmtT *stmt_async = 0;
    AsyncUserDataT data = {0};
    // 创建异步连接
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[id].userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("thread_batch_ddl: thr_id=%d ret=%d\n", id, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    ret = GmcBatchPrepare(conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    // create Vertex
    ret = close_usr_epoll_thread(&g_epAsync[id]);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = id * 30; i < id * 30 + 30; i++) {
        snprintf(labelName, 20, "T%d", i);
        snprintf(label_schema, 1024,
            "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
            "{\"name\":\"F1\", \"type\":\"int32\"}],"
            "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
            "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
            id, id, id);

        ret = GmcBatchAddDDL(batch, GMC_OPERATION_CREATE_VERTEX_LABEL, labelName, label_schema, normal_config_json);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // GmcBatchExecuteAsync
    ret = GmcBatchExecuteAsync(batch, batch_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 003.开启超时检测epoll线程, 关闭业务epoll线程, 多线程进行异步batch_DDL
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_003)
{
    int ret = 0;
    pthread_t thr_arr[30];
    int index[30] = {0};

    int thr_num_epoll;  // 创建的epoll线程数根据async_sn_common.h
#ifdef ENV_RTOSV2X
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(GMERR_OK, ret);
    thr_num_epoll = (MAX_CONN_SIZE - existConnNum) / 2;
    thr_num_epoll = thr_num_epoll > 30 ? 30 : thr_num_epoll;
#else
    thr_num_epoll = 30;
#endif
    for (int i = 0; i < thr_num_epoll; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < thr_num_epoll; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_batch_ddl, (void *)&index[i]);
    }

    for (int i = 0; i < thr_num_epoll; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    char labelName[20] = "";
    // 异步消息处理慢增加后台等待时间
    sleep(60);
    for (int i = 0; i < thr_num_epoll * 30; i++) {
        snprintf(labelName, 20, "T%d", i);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = close_timeout_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

void *thread_batch_dml(void *args)
{
    int ret;
    int id = *((int *)args);
    void *label = 0;
    GmcConnT *conn_async = 0;
    GmcStmtT *stmt_async = 0;
    AsyncUserDataT data = {0};
    // 创建异步连接
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[id].userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("thread_batch_dml: thr_id=%d ret=%d\n", id, ret);

    // insert Vertex
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn_async, NULL, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = close_usr_epoll_thread(&g_epAsync[id]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetIndexKeyValue(stmt_async, 0, GMC_DATATYPE_UINT32, &id, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt_async, "T39_K0");
    EXPECT_EQ(GMERR_OK, ret);
    // set_VertexProperty_PK(stmt_async, id);
    set_VertexProperty_SK(stmt_async, id);
    set_VertexProperty(stmt_async, id);
    ret = GmcBatchAddDML(batch, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchExecuteAsync(batch, batch_callback, &data);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchDestroy(batch);
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 004.开启超时检测epoll线程, 关闭业务epoll线程, 多线程进行异步batch_DML
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_004)
{
    int ret = 0;
    pthread_t thr_arr[30];
    int index[30] = {0};
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    int thr_num_epoll;  // 创建的epoll线程数根据async_sn_common.h
#ifdef ENV_RTOSV2X
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(GMERR_OK, ret);
    thr_num_epoll = (MAX_CONN_SIZE - existConnNum) / 2;
    thr_num_epoll = thr_num_epoll > 30 ? 30 : thr_num_epoll;
#else
    thr_num_epoll = 30;
#endif
    for (int i = 0; i < thr_num_epoll; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < thr_num_epoll; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_batch_dml, (void *)&index[i]);
    }

    for (int i = 0; i < thr_num_epoll; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);

    ret = close_timeout_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

// 005.开启超时检测epoll线程, 创建多个连接, 关闭业务epoll线程, 并进行异步DML
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_005)
{
    int ret = 0;
    const int thr_num_epoll = 5;
    GmcConnT *conn_async[thr_num_epoll] = {0};
    GmcStmtT *stmt_async[thr_num_epoll] = {0};
    for (int i = 0; i < thr_num_epoll; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < thr_num_epoll; i++) {
        YangConnOptionT connOptions = {0};
        connOptions.epollFd = &g_epAsync[i].userEpollFd;
        ret = TestYangGmcConnect(&conn_async[i], &stmt_async[i], GMC_CONN_TYPE_ASYNC, &connOptions);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("async_conn: conn_id=%d ret=%d\n", i, ret);

        // insert Vertex
        AsyncUserDataT data = {0};
        ret = testGmcPrepareStmtByLabelName(stmt_async[i], g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        // 关闭业务epoll线程
        ret = close_usr_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(stmt_async[i], i);
        set_VertexProperty_SK(stmt_async[i], i);
        set_VertexProperty(stmt_async[i], i);
        data = {0};
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt_async[i], &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < thr_num_epoll; i++) {
        ret = testGmcDisconnect(conn_async[i], stmt_async[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 006.开启超时检测epoll线程, 创建多个连接, 关闭业务epoll线程, 并进行异步DDL
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_006)
{
    int ret = 0;
    const int thr_num_epoll = 5;
    GmcConnT *conn_async[thr_num_epoll] = {0};
    GmcStmtT *stmt_async[thr_num_epoll] = {0};
    for (int i = 0; i < thr_num_epoll; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(true, ret == GMERR_OK || ret == GMERR_UNDEFINED_TABLE);
    if (ret != GMERR_OK && ret !=GMERR_UNDEFINED_TABLE) {
        cout << endl << "ret:" << ret << endl;
    }
    for (int i = 0; i < thr_num_epoll; i++) {
        YangConnOptionT connOptions = {0};
        connOptions.epollFd = &g_epAsync[i].userEpollFd;
        ret = TestYangGmcConnect(&conn_async[i], &stmt_async[i], GMC_CONN_TYPE_ASYNC, &connOptions);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        printf("async_conn: conn_id=%d ret=%d\n", i, ret);
        AsyncUserDataT data = {0};
        ret = close_usr_epoll_thread(&g_epAsync[i]);  // 不接受来自服务端的消息, 创建label发送给服务端，其实已成功
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcCreateVertexLabelAsync(
            stmt_async[i], normal_vertexlabel_schema, normal_config_json, truncate_vertex_callback, &data);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        // 消息发到服务器了，肯定会创建表成功的，之所以收到的是超时，是因为关闭了epoll线程，忽略了服务器返回的应答，其实本质这条消息在服务器就是成功的
        ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < thr_num_epoll; i++) {
        ret = testGmcDisconnect(conn_async[i], stmt_async[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

#define MAX_CONN MAX_ASYNC_CONN_SIZE_PER_PRO  //
// 007.开启超时检测epoll线程, 创建129个连接, 并进行异步DML
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_007)
{
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_TOO_MANY_CONNECTIONS);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    uint32_t getConnNum = 0;
#if defined(ENV_RTOSV2X)
    ret = testGetConnNum(&getConnNum);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    GmcConnT *conn_async[MAX_CONN + 1] = {0};
    GmcStmtT *stmt_async[MAX_CONN + 1] = {0};
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret == GMERR_UNDEFINED_TABLE ? GMERR_OK : ret);
    ret = GmcCreateVertexLabel(g_stmt, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < MAX_CONN - getConnNum; i++) {
        ret = testGmcConnect(&conn_async[i], &stmt_async[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        if (ret == GMERR_TOO_MANY_CONNECTIONS) {
            printf("GMERR_TOO_MANY_CONNECTIONS\n");
            break;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
        printf("async_conn: conn_id=%d    ret=%d\n", i, ret);
        // insert Vertex
        for (int j = i * MAX_CONN - getConnNum; j < i * (MAX_CONN - getConnNum) + MAX_CONN - getConnNum; j++) {
            AsyncUserDataT data = {0};
            ret = testGmcPrepareStmtByLabelName(stmt_async[i], g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
            EXPECT_EQ(GMERR_OK, ret);
            set_VertexProperty_PK(stmt_async[i], j);
            set_VertexProperty_SK(stmt_async[i], j);
            set_VertexProperty(stmt_async[i], j);
            data = {0};
            GmcAsyncRequestDoneContextT insertRequestCtx;
            insertRequestCtx.insertCb = insert_vertex_callback;
            insertRequestCtx.userData = &data;
            ret = GmcExecuteAsync(stmt_async[i], &insertRequestCtx);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            EXPECT_EQ(GMERR_OK, ret);
        }
    }

#ifdef ENV_RTOSV2X
    ;
#else
    ret = testGmcConnect(&conn_async[MAX_CONN - getConnNum], &stmt_async[MAX_CONN - getConnNum],
                         GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_TOO_MANY_CONNECTIONS, ret);
#endif
    ret = GmcDropVertexLabel(g_stmt, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = 0; i < MAX_CONN - getConnNum; i++) {
        ret = testGmcDisconnect(conn_async[i], stmt_async[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

// 008.开启超时检测epoll线程, 创建129个连接, 并进行异步DDL
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_008)
{
    int ret = 0;
	char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_TOO_MANY_CONNECTIONS);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
#ifdef ENV_RTOSV2X
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(GMERR_OK, ret);
    int conn_num = (MAX_CONN_SIZE - existConnNum) / 2;
#else
    const int conn_num = MAX_CONN;
#endif
    GmcConnT **conn_async = new GmcConnT*[conn_num + 1];
    for (int i = 0; i < conn_num + 1; ++i) {
        conn_async[i] = 0;
    }
    GmcStmtT **stmt_async = new GmcStmtT*[conn_num + 1];
    for (int i = 0; i < conn_num + 1; ++i) {
        stmt_async[i] = 0;
    }
    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    char label_schema[1024] = "";
    char labelName[20] = "";

    int labelNum = 0;
    for (int i = 0; i < conn_num; i++) {
        ret = testGmcConnect(&conn_async[i], &stmt_async[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
        if (ret == GMERR_TOO_MANY_CONNECTIONS) {
            printf("GMERR_TOO_MANY_CONNECTIONS\n");
            break;
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
        printf("async_conn: conn_id=%d    ret=%d\n", i, ret);
        // insert Vertex
        for (int j = i * 8; j < i * 8 + 8; j++) {
            AsyncUserDataT data = {0};
            snprintf(label_schema, 1024,
                "[{\"type\":\"record\", \"name\":\"T%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
                "{\"name\":\"F1\", \"type\":\"int32\"}],"
                "\"keys\":[{\"node\":\"T%d\", \"name\":\"T%d_K0\", \"fields\":[\"F0\"], "
                "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}}]}]",
                j, j, j);
            ret = GmcCreateVertexLabelAsync(
                stmt_async[i], label_schema, normal_config_json, create_vertex_label_callback, &data);
            EXPECT_EQ(GMERR_OK, ret);
            ret = testWaitAsyncRecv(&data);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (data.status == GMERR_OK) {
                labelNum++;
            }
        }
    }
#if defined ENV_RTOSV2X
    ret = testGmcConnect(&conn_async[conn_num], &stmt_async[conn_num], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);
#else
    ret = testGmcConnect(&conn_async[conn_num], &stmt_async[conn_num], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_TOO_MANY_CONNECTIONS, ret);
#endif

    for (int i = 0; i < labelNum; i++) {
        snprintf(labelName, 20, "T%d", i);
        ret = GmcDropVertexLabel(g_stmt, labelName);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < conn_num; i++) {
        ret = testGmcDisconnect(conn_async[i], stmt_async[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    free(conn_async);
    free(stmt_async);
}

void *thread_user_epoll(void *args)
{
    int ret;
    int id = *((int *)args);
    GmcConnT *conn_async[MAX_CONN + 1] = {0};
    GmcStmtT *stmt_async[MAX_CONN + 1] = {0};
    int conn_num = MAX_CONN / 8;
    // 创建异步连接
    for (int i = id * conn_num; i < id * conn_num + conn_num; i++) {
        ret = testGmcConnect(&conn_async[i], &stmt_async[i], GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
#if defined ENV_RTOSV2X
        if (ret != 0) {
            EXPECT_EQ(GMERR_TOO_MANY_CONNECTIONS, ret);
            printf("GMERR_TOO_MANY_CONNECTIONS\n");
        } else {
            EXPECT_EQ(GMERR_OK, ret);
        }
#else
        EXPECT_EQ(GMERR_OK, ret);
#endif
        printf("thread_user_epoll success:%5d%5d%5d\n", id, i, ret);
        ret = testGmcDisconnect(conn_async[i], stmt_async[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return NULL;
}

// 009.开启超时检测epoll线程, 多线程创建异步连接128
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_009)
{
    int ret = 0;
    GmcConnT *conn_async[MAX_CONN + 1] = {0};
    GmcStmtT *stmt_async[MAX_CONN + 1] = {0};
    const int thr_num = 8;
    pthread_t thr_arr[thr_num];
    int index[thr_num] = {0};

    ret = create_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);

    for (int i = 0; i < thr_num; i++) {
        index[i] = i;
        pthread_create(&thr_arr[i], NULL, thread_user_epoll, (void *)&index[i]);
    }

    for (int i = 0; i < thr_num; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
}

#if defined ENV_RTOSV2X
#define SYNC_CONN_NUM 4
#else
#define SYNC_CONN_NUM 112
#endif
void *thread_normal_async(void *args)
{
    int ret;
    int id = *((int *)args);
    GmcConnT *conn_async = NULL;
    GmcStmtT *stmt_async = NULL;
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[id].userEpollFd;
    ret = TestYangGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("thread_normal_async success:%5d%5d\n", id, ret);
    ret = testGmcDisconnect(conn_async, stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

void *thread_normal_sync(void *args)
{
    int ret;
    int id = *((int *)args);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    for (int i = id * SYNC_CONN_NUM; i < id * SYNC_CONN_NUM + SYNC_CONN_NUM; i++) {
        ret = testGmcConnect(&conn, &stmt);
        EXPECT_EQ(GMERR_OK, ret);
        printf("thread_normal_sync success:%5d%5d%5d\n", i, id, ret);
        ret = testGmcDisconnect(conn, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    return NULL;
}
// 010.开启超时检测epoll线程, 多线程创建同步/异步建连断连
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_010)
{
    int ret = 0;
    pthread_t thr_arr[30];
    pthread_t thr_arr1[8];
    int index[30] = {0};
    int thr_num_epoll;  // 创建的epoll线程数根据async_sn_common.h
#ifdef ENV_RTOSV2X
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(GMERR_OK, ret);
    thr_num_epoll = (MAX_CONN_SIZE - existConnNum) / 2;
    thr_num_epoll = thr_num_epoll > 30 ? 30 : thr_num_epoll;
#else
    thr_num_epoll = 30;
#endif
    for (int i = 0; i < thr_num_epoll; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }

    for (int i = 0; i < thr_num_epoll; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr[i], NULL, thread_normal_async, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < 8; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr1[i], NULL, thread_normal_sync, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < thr_num_epoll; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    for (int i = 0; i < 8; i++) {
        pthread_join(thr_arr1[i], NULL);
    }

    for (int i = 0; i < thr_num_epoll; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

void *thread_async_128(void *args)
{
    int ret;
    int thr_id = *(int *)args;
    GmcConnT *conn_async[MAX_CONN] = {0};
    GmcStmtT *stmt_async[MAX_CONN] = {0};
    // 建连
    YangConnOptionT connOptions = {0};
    connOptions.epollFd = &g_epAsync[thr_id].userEpollFd;
    ret = TestYangGmcConnect(&conn_async[thr_id], &stmt_async[thr_id], GMC_CONN_TYPE_ASYNC, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    printf("thread_async_128 success:%5d%5d\n", thr_id, ret);
    ret = testGmcDisconnect(conn_async[thr_id], stmt_async[thr_id]);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}
// 011.开启超时检测epoll线程, 多线程异步建连
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_011)
{
    int ret = 0;
    // int thr_num = ;
    GmcConnT *conn_async[MAX_CONN] = {0};
    GmcStmtT *stmt_async[MAX_CONN] = {0};
    pthread_t thr_arr[30];
    int index[30] = {0};
    int thr_num_epoll;  // 创建的epoll线程数根据async_sn_common.h
#ifdef ENV_RTOSV2X
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(GMERR_OK, ret);
    thr_num_epoll = (MAX_CONN_SIZE - existConnNum) / 2;
    thr_num_epoll = thr_num_epoll > 30 ? 30 : thr_num_epoll;
#else
    thr_num_epoll = 30;
#endif
    // 创建多个用户epoll
    for (int i = 0; i < thr_num_epoll; i++) {
        ret = create_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < thr_num_epoll; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr[i], NULL, thread_async_128, (void *)&index[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    for (int i = 0; i < thr_num_epoll; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    for (int i = 0; i < thr_num_epoll; i++) {
        ret = close_epoll_thread(&g_epAsync[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}

// 012. GmcRegTimeoutEpollFunc参数为NULL
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_012)
{
    char errorCode1[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_EPOLL_REG_FUNC_NULL);
    AW_ADD_ERR_WHITE_LIST(1, errorCode1);
    int ret = GmcRegTimeoutEpollFunc(NULL);
    EXPECT_EQ(GMERR_EPOLL_REG_FUNC_NULL, ret);
    if (ret != 0) {
        testGmcGetLastError(NULL);
        printf("GmcRegTimeoutEpollFunc failed, ret = %d\n", ret);
    }
}

// 013. 未设置超时epoll函数进行异步建连, 预期失败
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_013)
{
    int ret = create_user_epoll_thread(&g_epollData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcConnect(&g_conn_async, &g_stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    EXPECT_EQ(GMERR_OK, ret);  // 用例适配：未设置超时epoll函数不影响异步建连
    ret = close_usr_epoll_thread(&g_epollData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
}

void *threadCount014(void *args)
{
    int ret;
    GmcConnT *conn1 = NULL;
    GmcStmtT *stmt1 = NULL;
    ret = testGmcConnect(&conn1, &stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    char cmd[1024] = {0};
    char labelName[512] = {0};
    memset(labelName, 'a', 128);
    (void)snprintf(cmd, 1024, "%s/gmsysview count %s -ns %s", g_toolPath, labelName, g_testNameSpace);
    // 屏蔽 ret = executeCommand(cmd, "failed");
    system(cmd);
    ret = testGmcDisconnect(conn1, stmt1);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 014.并发循环查询不存在的表的记录数时(gmsysview count), 预期无异常栈
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_FILE_OPERATE_FAILED);
    AW_ADD_TRUNCATION_WHITE_LIST(1, "Create tables on demand, import tables, name");
    int ret = 0;
    char errorMsg1[128] = {0};
    (void)snprintf(errorMsg1, 128, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    uint32_t existConnCnt = 0;
    ret = testGetConnNum(&existConnCnt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int restConnCnt = MAX_CONN_SIZE - existConnCnt;
    int thrNum = restConnCnt > 20 ? 20 : restConnCnt;
    //int index[thrNum] = {0};  // 线程数可调
    pthread_t thr_arr_01[thrNum];
    int *index = (int *)malloc(thrNum * sizeof(int));
    memset(index, 0x00, thrNum * sizeof(int));
    for (int i = 0; i < thrNum; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, threadCount014, (void *)&index[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < thrNum; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }
    free(index);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *threadHashcluster015(void *args)
{
    int ret;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    // int i = *(int *)args;
    ret = testGmcConnect(&conn, &stmt);
    EXPECT_EQ(GMERR_OK, ret);
    const char *labelName = "Tx0";

    // insert
    for (int i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_REPLACE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F3Value = i + 10000;
        ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F4Value = i;
        ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT32, &F4Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);

        // scan
        int32_t pkValue = i;
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pkValue, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "vertex_pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = true;
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t readF2;
        int32_t readF3;
        bool isNull = 1;
        ret = GmcGetVertexPropertyByName(stmt, "F2", &readF2, sizeof(int32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, 0);
        EXPECT_EQ(i, readF2);

        ret = GmcGetVertexPropertyByName(stmt, "F3", &readF3, sizeof(int32_t), &isNull);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(isNull, 0);
        EXPECT_EQ(i + 10000, readF3);
    }
    ret = testGmcDisconnect(conn, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    return NULL;
}

// 015.简单表记录覆盖写,修改其中一个hashcluster键值, 同时并发直连读, 预期可以读到
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    int num = 0;
    char labelName[20] = "";
    char labelSchema[1024] = "";
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},"
        "{\"name\":\"F2\", \"type\":\"int32\"},"
        "{\"name\":\"F3\", \"type\":\"int32\"},"
        "{\"name\":\"F4\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx0\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx0\", \"name\":\"hashcluster_key\", \"fields\":[\"F1\", \"F2\", \"F3\"], "
        "\"index\":{\"type\":\"hashcluster\"},\"constraints\":{ \"unique\":false}}]}]",
        num);
    snprintf(labelName, 20, "Tx%d", num);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, NULL);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // insert
    for (int i = 0; i < 100; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F3Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F3", GMC_DATATYPE_INT32, &F3Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F4Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F4", GMC_DATATYPE_INT32, &F4Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    
#ifdef ENV_RTOSV2X
    const int thrNum = 10;
#else
    const int thrNum = 100;
#endif
    int index[thrNum] = {0};  // 线程数可调
    pthread_t thr_arr_01[thrNum];
    for (int i = 0; i < thrNum; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, threadHashcluster015, (void *)&index[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < thrNum; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }

    // drop vertex
    ret = GmcDropVertexLabel(g_stmt, labelName);
    EXPECT_EQ(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

void *threadClienCluster(void *args)
{
    char string[100] = "";
    int thrId = *(int *)args + 1;
    (void)sprintf(string, "./clusterRead_%d", thrId);
    int ret = system(string);
    return NULL;
}

// 016.非唯一hashcluster扫描是客户端异常退出后, 删除表数据
TEST_F(TimeoutEpollCon, Connect_004_RegTimeoutMultiConn_test_016)
{
    AW_FUN_Log(LOG_STEP, "indexShmOut start.");
    AW_CHECK_LOG_BEGIN(0);
    int ret = 0;
    int num = 0;
    char errorCode1[128] = {0};
    char errorCode2[128] = {0};
    char errorCode3[128] = {0};
    char errorCode4[128] = {0};
    (void)snprintf(errorCode1, 128, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    (void)snprintf(errorCode2, 128, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(errorCode3, 128, "GMERR-%d", GMERR_INTERNAL_ERROR);
    (void)snprintf(errorCode4, 128, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(4, errorCode1, errorCode2, errorCode3, errorCode4);
    char labelSchema[1024] = "";
    snprintf(labelSchema, 1024,
        "[{\"type\":\"record\", \"name\":\"Tx%d\", \"fields\":[{\"name\":\"F0\", \"type\":\"int32\"},"
        "{\"name\":\"F1\", \"type\":\"int32\"},"
        "{\"name\":\"F2\", \"type\":\"int32\"}],"
        "\"keys\":[{\"node\":\"Tx0\", \"name\":\"vertex_pk\", \"fields\":[\"F0\"], "
        "\"index\":{\"type\":\"primary\"},\"constraints\":{ \"unique\":true}},"
        "{\"node\":\"Tx0\", \"name\":\"hashcluster_key\", \"fields\":[\"F1\"], "
        "\"index\":{\"type\":\"hashcluster\"},\"constraints\":{ \"unique\":false}}]}]",
        num);
    ret = GmcCreateVertexLabel(g_stmt, labelSchema, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    char labelName[20] = "";
    snprintf(labelName, 20, "Tx%d", num);
    int dataNum = 1000;
    for (int i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F0Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F0", GMC_DATATYPE_INT32, &F0Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F1Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F1", GMC_DATATYPE_INT32, &F1Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t F2Value = i;
        ret = GmcSetVertexProperty(g_stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    const int thrNum = 2;
    int index[thrNum] = {0};  // 线程数可调
    pthread_t thr_arr_01[thrNum];
    for (int i = 0; i < thrNum; i++) {
        index[i] = i;
        ret = pthread_create(&thr_arr_01[i], NULL, threadClienCluster, (void *)&index[i]);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }

    for (int i = 0; i < thrNum; i++) {
        pthread_join(thr_arr_01[i], NULL);
    }
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    // delete
    for (int i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt, labelName, GMC_OPERATION_DELETE);
        EXPECT_EQ(GMERR_OK, ret);
        int32_t pkVal = i;
        ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt, "vertex_pk");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(g_stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    system("gmsysview -q V\\$STORAGE_VERTEX_COUNT");
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "indexShmOut end.");
}
