/* ****************************************************************************

 Notes        :
 01.参数测试
                001.GmcGetPropTypeByNodeNamePropName中输入INT32类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT32
                002.GmcGetPropTypeByNodeNamePropName中输入INT32类型错误的NodeName和正确的propName,输出fieldId和propertyType失败,预期无法找到
                003.GmcGetPropTypeByNodeNamePropName中输入INT32类型不存在的NodeName和正确的propName,输出fieldId和propertyType失败,预期无法找到
                004.GmcGetPropTypeByNodeNamePropName中输入INT32类型为空的NodeName和正确的propName,输出fieldId和propertyType失败,预期无法找到
                005.GmcGetPropTypeByNodeNamePropName中输入INT32类型正确的NodeName和错误的propName,输出fieldId和propertyType失败,预期无法找到
                006.GmcGetPropTypeByNodeNamePropName中输入INT32类型正确的NodeName和不存在的propName,输出fieldId和propertyType失败,预期无法找到
                007.GmcGetPropTypeByNodeNamePropName中输入INT32类型正确的NodeName和为空的propName,输出fieldId和propertyType失败,预期无法找到
                008.GmcGetPropTypeByNodeNamePropName中输入INT32类型为空的NodeName和为空的propName,输出fieldId和propertyType失败,预期无法找到
                009.验证GmcGetPropTypeByNodeNamePropName不支持查询super_fields的字段类型
                010.GmcGetPropTypeByNodeNamePropId中输入INT32类型正确的NodeName和正确的fieldId,输出fieldName和propertyType成功,预期枚举值GMC_DATATYPE_INT32
                011.GmcGetPropTypeByNodeNamePropId中输入INT32类型错误的NodeName和正确的fieldId,输出fieldName和propertyType失败,预期无法找到
                012.GmcGetPropTypeByNodeNamePropId中输入INT32类型不存在的NodeName和正确的fieldId,输出fieldName和propertyType失败,预期无法找到
                013.GmcGetPropTypeByNodeNamePropId中输入INT32类型为空的NodeName和正确的fieldId,输出fieldName和propertyType失败,预期无法找到
                014.GmcGetPropTypeByNodeNamePropId中输入INT32类型正确的NodeName和位置对应错误的fieldId,输出fieldName和propertyType失败,预期无法找到
                015.GmcGetPropTypeByNodeNamePropId中输入INT32类型正确的NodeName和越界的fieldId,输出fieldId和propertyType失败，预期无法找到
                016.GmcGetPropTypeByNodeNamePropId中输入INT32类型正确的NodeName和为负数的fieldId,输出fieldName和propertyType失败,预期无法找到
                017.GmcGetPropTypeByKeyNamePropOrder中输入INT32类型primary_key正确的keyname和正确的fieldIndex,输出fieldName和fieldType成功,预期枚举值GMC_DATATYPE_INT32
                018.GmcGetPropTypeByKeyNamePropOrder中输入INT32类型primary_key错误的keyname和正确的fieldIndex,预期无法找到
                019.GmcGetPropTypeByKeyNamePropOrder中输入INT32类型primary_key为空的keyname和正确的fieldIndex,预期无法找到
                020.GmcGetPropTypeByKeyNamePropOrder中输入INT32类型primary_key正确的keyname和为负数的fieldIndex,预期无法找到
                021.GmcGetPropTypeByKeyNamePropOrder中输入INT32类型primary_key正确的keyname和越界的fieldIndex,预期无法找到
                022.验证GmcGetPropTypeByNodeNamePropName查询根节点字段类型正确
                023.验证GmcGetPropTypeByNodeNamePropId查询根节点字段类型正确
                024.验证GmcGetPropTypeByNodeNamePropId根节点类型查询,不支持返回record类型
                025.验证GmcGetPropTypeByNodeNamePropName入参stmt句柄和用例上下文所用的句柄不一致,预期报错
                026.验证GmcGetPropTypeByNodeNamePropId入参stmt句柄和用例上下文所用的句柄不一致,预期报错
                027.验证GmcGetPropTypeByKeyNamePropOrder入参stmt句柄和用例上下文所用的句柄不一致,预期报错
 02.功能测试
                025.不创建表,调用接口,查询字段类型失败
                026.创建表,不打开表,调用接口,查询字段类型失败
                027.验证GmcGetPropTypeByNodeNamePropName中输入UCHAR类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UCHAR
                028.验证GmcGetPropTypeByNodeNamePropName中输入INT8类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT8
                029.验证GmcGetPropTypeByNodeNamePropName中输入UINT8类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT8
                030.验证GmcGetPropTypeByNodeNamePropName中输入INT16类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT16
                031.验证GmcGetPropTypeByNodeNamePropName中输入UINT16类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT16
                032.验证GmcGetPropTypeByNodeNamePropName中输入UINT32类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT32
                033.验证GmcGetPropTypeByNodeNamePropName中输入CHAR类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT32
                034.验证GmcGetPropTypeByNodeNamePropName中输入BOOL类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BOOL
                035.验证GmcGetPropTypeByNodeNamePropName中输入INT64类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT64
                036.验证GmcGetPropTypeByNodeNamePropName中输入UINT64类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT64
                037.验证GmcGetPropTypeByNodeNamePropName中输入FLOAT类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_FLOAT
                038.验证GmcGetPropTypeByNodeNamePropName中输入DOUBLE类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_DOUBLE
                039.验证GmcGetPropTypeByNodeNamePropName中输入RESOURCE类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_RESOURCE
                040.验证GmcGetPropTypeByNodeNamePropName中输入TIME类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_TIME
                041.验证GmcGetPropTypeByNodeNamePropName中输入BITFIELD8类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD8
                042.验证GmcGetPropTypeByNodeNamePropName中输入BITFIELD16类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD16
                043.验证GmcGetPropTypeByNodeNamePropName中输入BITFIELD32类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD32
                044.验证GmcGetPropTypeByNodeNamePropName中输入BITFIELD64类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD64
                045.验证GmcGetPropTypeByNodeNamePropName中输入PARTITION类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_PARTITION
                046.验证GmcGetPropTypeByNodeNamePropName中输入STRING类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_STRING
                047.验证GmcGetPropTypeByNodeNamePropName中输入BYTES类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BYTES
                048.验证GmcGetPropTypeByNodeNamePropName中输入FIXED类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_FIXED
                049.验证GmcGetPropTypeByNodeNamePropName中输入BITMAP类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITMAP
                050.验证GmcGetPropTypeByNodeNamePropId中输入UCHAR类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UCHAR
                051.验证GmcGetPropTypeByNodeNamePropId中输入INT8类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT8
                052.验证GmcGetPropTypeByNodeNamePropId中输入UINT8类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT8
                053.验证GmcGetPropTypeByNodeNamePropId中输入INT16类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT16
                054.验证GmcGetPropTypeByNodeNamePropId中输入UINT16类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT16
                055.验证GmcGetPropTypeByNodeNamePropId中输入UINT32类型正确的NodeName和正确的proId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT32
                056.验证GmcGetPropTypeByNodeNamePropId中输入CHAR类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT32
                057.验证GmcGetPropTypeByNodeNamePropId中输入BOOL类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BOOL
                058.验证GmcGetPropTypeByNodeNamePropId中输入INT64类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT64
                059.验证GmcGetPropTypeByNodeNamePropId中输入UINT64类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT64
                060.验证GmcGetPropTypeByNodeNamePropId中输入FLOAT类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_FLOAT
                061.验证GmcGetPropTypeByNodeNamePropId中输入DOUBLE类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_DOUBLE
                062.验证GmcGetPropTypeByNodeNamePropId中输入RESOURCE类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_RESOURCE
                063.验证GmcGetPropTypeByNodeNamePropId中输入TIME类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_TIME
                064.验证GmcGetPropTypeByNodeNamePropId中输入BITFIELD8类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD8
                065.验证GmcGetPropTypeByNodeNamePropId中输入BITFIELD16类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD16
                066.验证GmcGetPropTypeByNodeNamePropId中输入BITFIELD32类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD32
                067.验证GmcGetPropTypeByNodeNamePropId中输入BITFIELD64类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD64
                068.验证GmcGetPropTypeByNodeNamePropId中输入PARTITION类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_PARTITION
                069.验证GmcGetPropTypeByNodeNamePropId中输入STRING类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_STRING
                070.验证GmcGetPropTypeByNodeNamePropId中输入BYTES类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BYTES
                071.验证GmcGetPropTypeByNodeNamePropId中输入FIXED类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_FIXED
                072.验证GmcGetPropTypeByNodeNamePropId中输入BITMAP类型正确的NodeName和正确的propId,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITMAP
                073.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key正确的keyname和正确的fieldIndex,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UCHAR
                074.验证GmcGetPropTypeByKeyNamePropOrder支持localhash索引查询
                075.验证GmcGetPropTypeByKeyNamePropOrder的localhash索引支持labelId关系
                076.验证GmcGetPropTypeByKeyNamePropOrder支持hashcluster索引查询
                077.验证GmcGetPropTypeByKeyNamePropOrder的hashcluster索引,支持labelId关系
                078.验证GmcGetPropTypeByKeyNamePropOrder支持local索引查询
                079.验证GmcGetPropTypeByKeyNamePropOrder的local索引支持labelId关系
                080.验证GmcGetPropTypeByKeyNamePropOrder支持LPM4索引查询
                081.验证GmcGetPropTypeByKeyNamePropOrder的LPM4索引支持labelId关系
                082.验证GmcGetPropTypeByKeyNamePropOrder支持LPM6索引查询
                083.验证GmcGetPropTypeByKeyNamePropOrder的LPM6索引支持labelId关系
                084.验证GmcGetPropTypeByKeyNamePropOrder不支持memberkey索引查询
                085.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为UCHAR正确的keyname和正确的fieldIndex,预期结果正确
                086.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为INT8正确的keyname和正确的fieldIndex,预期结果正确
                087.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为UINT8正确的keyname和正确的fieldIndex,预期结果正确
                088.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为INT16正确的keyname和正确的fieldIndex,预期结果正确
                089.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为UINT16正确的keyname和正确的fieldIndex,预期结果正确
                090.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为UINT32正确的keyname和正确的fieldIndex,预期结果正确
                091.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为BOOL正确的keyname和正确的fieldIndex,预期结果正确
                092.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为INT64正确的keyname和正确的fieldIndex,预期结果正确
                093.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为UINT64正确的keyname和正确的fieldIndex,预期结果正确
                094.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为FLOAT正确的keyname和正确的fieldIndex,预期结果正确
                095.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为DOUBLE正确的keyname和正确的fieldIndex,预期结果正确
                096.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为RESOURCE正确的keyname和正确的fieldIndex,预期结果正确
                097.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为TIME正确的keyname和正确的fieldIndex,预期结果正确
                098.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为STRING正确的keyname和正确的fieldIndex,预期结果正确
                099.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为BYTES正确的keyname和正确的fieldIndex,预期结果正确
                100.验证GmcGetPropTypeByKeyNamePropOrder输入primary_key类型为FIXED正确的keyname和正确的fieldIndex,预期结果正确

 History      :
 Author       : wensiqi wwx1060458
 Modification :
 Date         : 2021/12/27
**************************************************************************** */

#include <stdlib.h>
#include <stdio.h>
#include <pthread.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "tree_tools.h"
#include "tools.h"
#include "t_datacom_lite.h"

#define LABELNAME_MAX 128
int res = 0;
char *g_schema = NULL;
// char g_labelName[LABELNAME_MAX] = "ip4forward";
char g_configJson[512] = "{\"max_record_num\" : 999999}";
void *g_vertexLabel = NULL;
GmcConnT *g_conn_sync = NULL, *g_conn = NULL, *g_conn_2 = NULL, *g_conn_async = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt = NULL, *g_stmt_2 = NULL, *g_stmt_async = NULL;

char label_name1[LABELNAME_MAX] = "OP_T0";
char label_name2[LABELNAME_MAX] = "DST_T0";
char label_name3[LABELNAME_MAX] = "edgelabel_testEdge";
char *test_schema1 = NULL, *test_schema2 = NULL, *test_schema3 = NULL;
const char *g_subName = "subVertexLabel";
const char *edgeLabelName = "edgelabel_testEdge";
char lalable_name_PK1[] = "OP_PK";

void *label = NULL;
char *schema = NULL;
char labelName[] = "test32_deeep";
char const *g_filePath = "./vertexdata/";
char const *g_vertexPath = "./vertexjson/test32_deeep.gmjson";
char const *g_dataPath = "./vertexdata/test32_deeep.gmdata";

class GetPropType_test : public testing::Test {
public:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh -f ");
        // 创建 epoll thread
        res = testEnvInit();
        EXPECT_EQ(GMERR_OK, res);
        res = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
    }

    static void TearDownTestCase()
    {
        res = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, res);
        GmcDetachAllShmSeg();
        testEnvClean();
    };

    virtual void SetUp();
    virtual void TearDown();
};

void GetPropType_test::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    g_vertexLabel = NULL;
    g_conn_sync = NULL;
    g_stmt_sync = NULL;

    // 创建同步连接
    res = testGmcConnect(&g_conn_sync, &g_stmt_sync);
    ASSERT_EQ(GMERR_OK, res);

    //创建vertexlabel
    readJanssonFile("schema_file/test32_deeep.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
#if defined FEATURE_PERSISTENCE
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_FEATURE_NOT_SUPPORTED, res);
#else
    res = testGmcCreateVertexLabel(
        g_stmt_sync, "schema_file/test32_deeep.gmjson", NULL, 0, NULL);
    EXPECT_EQ(GMERR_OK, res);
#endif
    free(schema);

    AW_CHECK_LOG_BEGIN();
}

void GetPropType_test::TearDown()
{
    printf("\n======================TEST:END========================\n");

    AW_CHECK_LOG_END();

    // drop vertexLabel
    res = GmcDropVertexLabel(g_stmt_sync, labelName);

    // 断开同步连接
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
                001.GmcGetPropTypeByNodeNamePropName中输入INT32类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT32
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_001)
{
    int i;
#if defined FEATURE_PERSISTENCE
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_UNDEFINED_TABLE, res);
 
    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T1", "B1", &fieldId, &propType);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, res);
    AW_FUN_Log(LOG_INFO, "GmcGetPropTypeByNodeNamePropName  T1/B1 propType: %d\n", propType);
    AW_FUN_Log(LOG_INFO, "GmcGetPropTypeByNodeNamePropName  T1/B1 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT32) {
        AW_FUN_Log(LOG_INFO, "propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        AW_FUN_Log(LOG_INFO, "propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
#else
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T1", "B1", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T1/B1 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T1/B1 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
#endif
}

/* ****************************************************************************
                002.GmcGetPropTypeByNodeNamePropName中输入INT32类型错误的NodeName和正确的propName,输出fieldId和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_002)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T2", "B1", &fieldId, &propType);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, res);
    printf("GmcGetPropTypeByNodeNamePropName  T2/B1 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T2/B1 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                003.GmcGetPropTypeByNodeNamePropName中输入INT32类型不存在的NodeName和正确的propName,输出fieldId和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_003)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T32", "B1", &fieldId, &propType);
    ASSERT_EQ(GMERR_INVALID_NAME, res);
    printf("GmcGetPropTypeByNodeNamePropName  T32/B1 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T32/B1 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                004.GmcGetPropTypeByNodeNamePropName中输入INT32类型为空的NodeName和正确的propName,输出fieldId和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_004)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, NULL, "B1", &fieldId, &propType);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, res);
    printf("GmcGetPropTypeByNodeNamePropName  NULL/B1 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  NULL/B1 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                005.GmcGetPropTypeByNodeNamePropName中输入INT32类型正确的NodeName和错误的propName,输出fieldId和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_005)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T1", "B2", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T1/B2 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T1/B2 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                006.GmcGetPropTypeByNodeNamePropName中输入INT32类型正确的NodeName和不存在的propName,输出fieldId和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_006)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T1", "B4", &fieldId, &propType);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, res);
    printf("GmcGetPropTypeByNodeNamePropName  T1/B3 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T1/B3 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                007.GmcGetPropTypeByNodeNamePropName中输入INT32类型正确的NodeName和为空的propName,输出fieldId和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_007)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T1", NULL, &fieldId, &propType);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, res);
    printf("GmcGetPropTypeByNodeNamePropName  T1/NULL propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T1/NULL fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                008.GmcGetPropTypeByNodeNamePropName中输入INT32类型为空的NodeName和为空的propName,输出fieldId和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_008)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, NULL, NULL, &fieldId, &propType);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, res);
    printf("GmcGetPropTypeByNodeNamePropName  NULL/NULL propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  NULL/NULL fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                009.验证GmcGetPropTypeByNodeNamePropName不支持查询super_fields的字段类型
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_009)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "LPM4";
    uint32_t labelId = 0;
    uint32_t fieldId;
    const char *labelName_bit = "Lpm4VertexLabel";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    res = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, res);

    // 创建vertexlabel
    readJanssonFile("schema_file/VertexLabelLpm4.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByNodeNamePropName(g_stmt, "SuperFiled", "A2", &fieldId, &propType);
    ASSERT_EQ(GMERR_INVALID_NAME, res);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId LPM4/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_UINT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = GmcDropVertexLabel(g_stmt, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
                010.GmcGetPropTypeByNodeNamePropId中输入INT32类型正确的NodeName和正确的fieldId,输出fieldName和propertyType成功,预期枚举值GMC_DATATYPE_INT32
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_010)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T1", 0};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T1/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                011.GmcGetPropTypeByNodeNamePropId中输入INT32类型错误的NodeName和正确的fieldId,输出fieldName和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_011)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T2", 1};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T2/2 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                012.GmcGetPropTypeByNodeNamePropId中输入INT32类型不存在的NodeName和正确的fieldId,输出fieldName和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_012)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T32", 1};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_INVALID_NAME, res);
    printf("GmcGetPropTypeByNodeNamePropId T32/1 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                013.GmcGetPropTypeByNodeNamePropId中输入INT32类型为空的NodeName和正确的fieldId,输出fieldName和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_013)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {NULL, 1};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, res);
    printf("GmcGetPropTypeByNodeNamePropId NULL/1 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                014.GmcGetPropTypeByNodeNamePropId中输入INT32类型正确的NodeName和位置对应错误的fieldId,输出fieldName和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_014)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T1", 1};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T1/1 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                015.GmcGetPropTypeByNodeNamePropId中输入INT32类型正确的NodeName和越界的fieldId,输出fieldId和propertyType失败，预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_015)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T1", 5};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, res);
    printf("GmcGetPropTypeByNodeNamePropId T1/5 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                016.GmcGetPropTypeByNodeNamePropId中输入INT32类型正确的NodeName和为负数的fieldId,输出fieldName和propertyType失败,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_016)
{
    char g_errorCode[1024] = {0};
    (void)snprintf(g_errorCode, 1024, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode);
    int i;
    uint32_t prop = -1;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T1", prop};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_INVALID_PROPERTY, res);
    printf("GmcGetPropTypeByNodeNamePropId T1/-1 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                017.GmcGetPropTypeByKeyNamePropOrder中输入INT32类型primary_key正确的keyname和正确的fieldIndex,输出fieldName和fieldType成功,预期枚举值GMC_DATATYPE_INT32
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_017)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "primary_key";
    uint32_t labelId = 0;
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary_key/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                018.GmcGetPropTypeByKeyNamePropOrder中输入INT32类型primary_key错误的keyname和正确的fieldIndex,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_018)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "primary_key1";
    uint32_t labelId = 0;
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary_key1/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                019.GmcGetPropTypeByKeyNamePropOrder中输入INT32类型primary_key为空的keyname和正确的fieldIndex,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_019)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = NULL;
    uint32_t labelId = 0;
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_NULL_VALUE_NOT_ALLOWED, res);
    printf("GmcGetPropTypeByKeyNameLabelId NULL/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                020.GmcGetPropTypeByKeyNamePropOrder中输入INT32类型primary_key正确的keyname和为负数的fieldIndex,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_020)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "primary_key";
    uint32_t labelId = -1;
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary_key/-1 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                021.GmcGetPropTypeByKeyNamePropOrder中输入INT32类型primary_key正确的keyname和越界的fieldIndex,预期无法找到
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_021)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "primary_key";
    uint32_t labelId = 4;
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_INVALID_PARAMETER_VALUE, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary_key/4 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                022.验证GmcGetPropTypeByNodeNamePropName查询根节点字段类型正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_022)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"test32_deeep", 2};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId test32_deeep/2 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                023.验证GmcGetPropTypeByNodeNamePropId查询根节点字段类型正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_023)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"test32_deeep", 2};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId test32_deeep/2 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                024.验证GmcGetPropTypeByNodeNamePropId查询根节点字段类型,,不支持返回record类型
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_024)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"test32_deeep", 3};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_DATA_EXCEPTION, res);
    printf("GmcGetPropTypeByNodeNamePropId test32_deeep/3 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                025.不创建表,调用接口,查询字段类型失败
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_025)
{
    int i;
    uint32_t propType;
    char fieldName[128];

    res = GmcDropVertexLabel(g_stmt_sync, labelName);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"test32_deeep", 0};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, res);

    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    readJanssonFile("schema_file/test32_deeep.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);
}

/* ****************************************************************************
                026.创建表,不打开表,调用接口,查询字段类型失败
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_026)
{
    int i;
    // czp
    uint32_t propType;
    uint32_t fieldId;
    uint32_t labelId = 0;
    char fieldName[128];
    const char *keyName = "primary_key";
    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T1", 0};
    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};

    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T1", "B1", &fieldId, &propType);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, res);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, res);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, res);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                027.验证GmcGetPropTypeByNodeNamePropName中输入UCHAR类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UCHAR
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_027)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A13", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A13 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A13 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_UCHAR) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                028.验证GmcGetPropTypeByNodeNamePropName中输入INT8类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT8
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_028)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A6", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A6 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A6 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT8) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                029.验证GmcGetPropTypeByNodeNamePropName中输入UINT8类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT8
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_029)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A7", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A7 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A7 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_UINT8) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                030.验证GmcGetPropTypeByNodeNamePropName中输入INT16类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT16
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_030)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A4", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A4 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A4 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT16) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                031.验证GmcGetPropTypeByNodeNamePropName中输入UINT16类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT16
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_031)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A5", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A5 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A5 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_UINT16) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                032.验证GmcGetPropTypeByNodeNamePropName中输入UINT32类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT32
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_032)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A2", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A2 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A2 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                033.验证GmcGetPropTypeByNodeNamePropName中输入CHAR类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT32
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_033)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A12", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A12 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A12 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_CHAR) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                034.验证GmcGetPropTypeByNodeNamePropName中输入BOOL类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BOOL
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_034)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A8", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A8 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A8 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_BOOL) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                035.验证GmcGetPropTypeByNodeNamePropName中输入INT64类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT64
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_035)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A0", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A0 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A0 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT64) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                036.验证GmcGetPropTypeByNodeNamePropName中输入UINT64类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT64
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_036)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A1", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A1 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A1 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_UINT64) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                037.验证GmcGetPropTypeByNodeNamePropName中输入FLOAT类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_FLOAT
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_037)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A9", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A9 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A9 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_FLOAT) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                038.验证GmcGetPropTypeByNodeNamePropName中输入DOUBLE类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_DOUBLE
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_038)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A10", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A10 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A10 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_DOUBLE) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                039.验证GmcGetPropTypeByNodeNamePropName中输入RESOURCE类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_RESOURCE
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_039)
{
    int i;
    static const char *gResPoolConfigJson =
        R"({
        "name" : "ResourcePool001",
        "pool_id" : 0,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type": 0
    })";
    static const char *gResPoolName = "ResourcePool001";
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);
    ret = GmcCreateResPool(g_stmt_sync, gResPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(g_stmt_sync, gResPoolName, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "test32_deeep", "A17", &fieldId, &propType);  // 需增加
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName test32_deeep/A17 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName test32_deeep/A17 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_RESOURCE) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
                040.验证GmcGetPropTypeByNodeNamePropName中输入TIME类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_TIME
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_040)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A11", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A11 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A11 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_TIME) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                041.验证GmcGetPropTypeByNodeNamePropName中输入BITFIELD8类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD8
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_041)
{
    int i;
    const char *labelName_bit = "T38";

    GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T38", "F3", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F3 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F3 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_BITFIELD8) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                042.验证GmcGetPropTypeByNodeNamePropName中输入BITFIELD16类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD16
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_042)
{
    int i;
    const char *labelName_bit = "T38";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T38", "F2", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F2 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F2 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_BITFIELD16) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                043.验证GmcGetPropTypeByNodeNamePropName中输入BITFIELD32类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD32
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_043)
{
    int i;
    const char *labelName_bit = "T38";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T38", "F4", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F4 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F4 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_BITFIELD32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                044.验证GmcGetPropTypeByNodeNamePropName中输入BITFIELD64类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD64
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_044)
{
    int i;
    const char *labelName_bit = "T38";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T38", "F5", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F5 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F5 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_BITFIELD64) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                045.验证GmcGetPropTypeByNodeNamePropName中输入PARTITION类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_PARTITION
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_045)
{
    int i;
    const char *labelName_bit = "T38";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T38", "F21", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F21 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F21 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_PARTITION) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                046.验证GmcGetPropTypeByNodeNamePropName中输入STRING类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_STRING
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_046)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A14", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A14 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A14 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_STRING) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                047.验证GmcGetPropTypeByNodeNamePropName中输入BYTES类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BYTES
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_047)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A15", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A15 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A15 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_BYTES) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                048.验证GmcGetPropTypeByNodeNamePropName中输入FIXED类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_FIXED
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_048)
{
    int i;
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T31", "A16", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A16 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T31/A16 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_FIXED) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                049.验证GmcGetPropTypeByNodeNamePropName中输入BITMAP类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITMAP
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_049)
{
    int i;
    uint32_t propType;
    uint32_t fieldId;
    const char *labelName_bit = "T38";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_sync, "T38", "F18", &fieldId, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F18 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T38/F18 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_BITMAP) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                050.验证GmcGetPropTypeByNodeNamePropId中输入UCHAR类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UCHAR
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_050)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 13};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/13 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_UCHAR) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                051.验证GmcGetPropTypeByNodeNamePropId中输入INT8类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT8
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_051)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 6};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/6 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT8) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                052.验证GmcGetPropTypeByNodeNamePropId中输入UINT8类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT8
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_052)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 7};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/7 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_UINT8) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                053.验证GmcGetPropTypeByNodeNamePropId中输入INT16类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT16
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_053)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 4};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/4 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT16) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                054.验证GmcGetPropTypeByNodeNamePropId中输入UINT16类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT16
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_054)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 5};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/5 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_UINT16) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                055.验证GmcGetPropTypeByNodeNamePropId中输入UINT32类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT32
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_055)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 12};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/12 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_CHAR) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                056.验证GmcGetPropTypeByNodeNamePropId中输入CHAR类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_CHAR
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_056)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 12};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/3 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_CHAR) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                057.验证GmcGetPropTypeByNodeNamePropId中输入BOOL类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BOOL
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_057)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 8};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/8 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_BOOL) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                058.验证GmcGetPropTypeByNodeNamePropId中输入INT64类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_INT64
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_058)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 0};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT64) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                059.验证GmcGetPropTypeByNodeNamePropId中输入UINT64类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UINT64
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_059)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 1};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/1 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_UINT64) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                060.验证GmcGetPropTypeByNodeNamePropId中输入FLOAT类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_FLOAT
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_060)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 9};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/9 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_FLOAT) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                061.验证GmcGetPropTypeByNodeNamePropId中输入DOUBLE类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_DOUBLE
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_061)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 10};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/10 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_DOUBLE) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                062.验证GmcGetPropTypeByNodeNamePropId中输入RESOURCE类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_RESOURCE
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_062)
{
    int i;
    static const char *gResPoolConfigJson =
        R"({
        "name" : "ResourcePool001",
        "pool_id" : 0,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type": 0
    })";
    static const char *gResPoolName = "ResourcePool001";
    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);
    ret = GmcCreateResPool(g_stmt_sync, gResPoolConfigJson);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(g_stmt_sync, gResPoolName, labelName);
    ASSERT_EQ(GMERR_OK, ret);

    // czp
    uint32_t propType;
    char fieldName[128];
    GmcNodeNameAndPropIdT NodeNameAndPropId = {"test32_deeep", 1};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId test32_deeep/1 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_RESOURCE) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, labelName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
                063.验证GmcGetPropTypeByNodeNamePropId中输入TIME类型正确的NodeName和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_TIME
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_063)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 11};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/11 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_TIME) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                064.验证GmcGetPropTypeByNodeNamePropId中输入BITFIELD8类型正确的BITFIELD8和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD8
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_064)
{
    int i;
    const char *labelName_bit = "T38";

    GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    char fieldName[128];
    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T38", 3};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T38/3 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_BITFIELD8) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                065.验证GmcGetPropTypeByNodeNamePropId中输入BITFIELD16类型正确的BITFIELD8和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD16
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_065)
{
    int i;
    const char *labelName_bit = "T38";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    char fieldName[128];
    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T38", 2};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T38/2 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_BITFIELD16) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                066.验证GmcGetPropTypeByNodeNamePropId中输入BITFIELD32类型正确的BITFIELD8和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD32
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_066)
{

    int i;
    uint32_t propType;
    char fieldName[128];
    const char *labelName_bit = "T38";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T38", 4};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T38/4 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_BITFIELD32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                067.验证GmcGetPropTypeByNodeNamePropId中输入BITFIELD64类型正确的BITFIELD8和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITFIELD64
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_067)
{

    int i;
    uint32_t propType;
    char fieldName[128];
    const char *labelName_bit = "T38";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T38", 5};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T38/5 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_BITFIELD64) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                068.验证GmcGetPropTypeByNodeNamePropId中输入PARTITION类型正确的BITFIELD8和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_PARTITION
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_068)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    const char *labelName_bit = "T38";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T38", 7};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T38/7 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_PARTITION) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                069.验证GmcGetPropTypeByNodeNamePropId中输入STRING类型正确的BITFIELD8和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_STRING
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_069)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 14};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/14 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_STRING) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                070.验证GmcGetPropTypeByNodeNamePropId中输入BYTES类型正确的BITFIELD8和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BYTES
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_070)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 15};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/15 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_BYTES) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                071.验证GmcGetPropTypeByNodeNamePropId中输入FIXED类型正确的BITFIELD8和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_FIXED
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_071)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T31", 16};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T31/16 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_FIXED) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
}

/* ****************************************************************************
                072.验证GmcGetPropTypeByNodeNamePropId中输入BITMAP类型正确的BITFIELD8和正确的propName,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_BITMAP
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_072)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    const char *labelName_bit = "T38";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/u16_bitfield_simple_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T38", 6};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_sync, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByNodeNamePropId T38/6 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_BITMAP) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);

    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                073.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key正确的keyname和正确的fieldIndex,输出fieldId和propertyType成功,预期枚举值GMC_DATATYPE_UCHAR
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_073)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                074.验证GmcGetPropTypeByKeyNamePropOrder支持localhash索引
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_074)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "localhash_key";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId localhash_key/1 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_INT8) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                075.验证GmcGetPropTypeByKeyNamePropOrder的localhash索引支持labelId关系
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_075)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "localhash_key";
    uint32_t labelId = 1;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId localhash_key/1 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_UINT64) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                076.验证GmcGetPropTypeByKeyNamePropOrder支持hashcluster索引
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_076)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "hashcluster_key";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId localhash_key1/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_UINT8) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                077.验证GmcGetPropTypeByKeyNamePropOrder的hashcluster索引,支持labelId关系
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_077)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "hashcluster_key";
    uint32_t labelId = 1;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId localhash_key1/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_TIME) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                078.验证GmcGetPropTypeByKeyNamePropOrder支持local索引
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_078)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "local_key";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId localhash_key1/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                079.验证GmcGetPropTypeByKeyNamePropOrder的local索引支持labelId关系
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_079)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "local_key";
    uint32_t labelId = 1;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId localhash_key1/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_INT64) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                080.验证GmcGetPropTypeByKeyNamePropOrder支持LPM4索引
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_080)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "LPM4";
    uint32_t labelId = 0;
    const char *labelName_bit = "Lpm4VertexLabel";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    res = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, res);

    // 创建vertexlabel
    readJanssonFile("schema_file/VertexLabelLpm4.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId LPM4/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_UINT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
                081.验证GmcGetPropTypeByKeyNamePropOrder的LPM4索引支持labelId关系
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_081)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "LPM4";
    uint32_t labelId = 3;
    const char *labelName_bit = "Lpm4VertexLabel";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    res = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, res);

    // 创建vertexlabel
    readJanssonFile("schema_file/VertexLabelLpm4.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId LPM4/3 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_UINT8) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
                082.验证GmcGetPropTypeByKeyNamePropOrder支持LPM6索引
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_082)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "LPM6";
    uint32_t labelId = 0;
    const char *labelName_bit = "Lpm6VertexLabel";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    res = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, res);

    // 创建vertexlabel
    readJanssonFile("schema_file/VertexLabelLpm6.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId LPM6/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_UINT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
                083.验证GmcGetPropTypeByKeyNamePropOrder的LPM6索引支持labelId关系
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_083)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "LPM6";
    uint32_t labelId = 2;
    const char *labelName_bit = "Lpm6VertexLabel";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    res = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, res);

    // 创建vertexlabel
    readJanssonFile("schema_file/VertexLabelLpm6.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId LPM6/2 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_FIXED) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
/* ****************************************************************************
                084.验证GmcGetPropTypeByKeyNamePropOrder不支持memberkey
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_084)
{
    char errorMsg1[128] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "M0";
    uint32_t labelId = 0;
    const char *labelName_bit = "Lpm4VertexLabel";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    res = testGmcConnect(&g_conn, &g_stmt);
    ASSERT_EQ(GMERR_OK, res);

    // 创建vertexlabel
    readJanssonFile("schema_file/VertexLabelLpm4.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, res);
    res = testGmcGetLastError(NULL);
    EXPECT_EQ(GMERR_OK, res);

    res = GmcDropVertexLabel(g_stmt, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);

    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

/* ****************************************************************************
                085.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为UCHAR正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_085)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_0.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_UCHAR) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                086.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为INT8正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_086)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_1.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_INT8) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                087.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为UINT8正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_087)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_2.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_UINT8) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                088.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为INT16正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_088)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_3.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_INT16) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                089.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为UINT16正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_089)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_4.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_UINT16) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                090.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为UINT32正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_090)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_5.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_UINT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                091.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为BOOL正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_091)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema); // 索引字段不支持bool，用例下架2022/4/21
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_BOOL) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = 0;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                092.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为INT64正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_092)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_7.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_INT64) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                093.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为UINT64正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_093)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_8.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_UINT64) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                094.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为FLOAT正确的keyname和正确的fieldIndex,预期结果正确
*************************************************************************** */
TEST_F(GetPropType_test, Connect_007_094)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema); // 索引字段不支持FLOAT，用例下架2022/4/21
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_FLOAT) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = 0;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                095.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为DOUBLE正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_095)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema.gmjson", &schema); // 索引字段不支持DOUBLE，用例下架2022/4/21
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_DOUBLE) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = 0;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                096.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为RESOURCE正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_096)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "primary_key";
    uint32_t labelId = 0;
    const char *labelName_bit = "test32_deeep1";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/test32_deeep_11.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_RESOURCE) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                097.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为TIME正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_097)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_12.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, test_schema1, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(test_schema1);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_TIME) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                098.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为STRING正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_098)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_18.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_STRING) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                099.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为BYTES正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_099)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_19.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_BYTES) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

/* ****************************************************************************
                100.验证GmcGetPropTypeByKeyNamePropOrder中输入primary_key类型为FIXED正确的keyname和正确的fieldIndex,预期结果正确
**************************************************************************** */
TEST_F(GetPropType_test, Connect_007_100)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "T25_PK";
    uint32_t labelId = 0;
    const char *labelName_bit = "T25";
    GmcDropVertexLabel(g_stmt_sync, labelName_bit);

    // 创建vertexlabel
    readJanssonFile("schema_file/withoutResource_schema_20.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);

    // create table
    res = GmcCreateVertexLabel(g_stmt_sync, schema, NULL);
    EXPECT_EQ(GMERR_OK, res);
    free(schema);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName_bit, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_sync, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_OK, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);

    if (propType == GMC_DATATYPE_FIXED) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_OK, i);
    res = GmcDropVertexLabel(g_stmt_sync, labelName_bit);
    ASSERT_EQ(GMERR_OK, res);
}

// 101.验证GmcGetPropTypeByNodeNamePropName入参stmt句柄和用例上下文所用的句柄不一致,预期报错
TEST_F(GetPropType_test, Connect_007_101)
{
    int i;
    res = testGmcConnect(&g_conn_2, &g_stmt_2);
    ASSERT_EQ(GMERR_OK, res);

    // 打开表
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    // czp
    uint32_t propType;
    uint32_t fieldId;
    res = GmcGetPropTypeByNodeNamePropName(g_stmt_2, "T1", "B1", &fieldId, &propType);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, res);
    printf("GmcGetPropTypeByNodeNamePropName  T1/B1 propType: %d\n", propType);
    printf("GmcGetPropTypeByNodeNamePropName  T1/B1 fieldId: %d\n", fieldId);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);

    ret = testGmcDisconnect(g_conn_2, g_stmt_2);
    EXPECT_EQ(GMERR_OK, ret);
}

// 102.验证GmcGetPropTypeByNodeNamePropId入参stmt句柄和用例上下文所用的句柄不一致,预期报错
TEST_F(GetPropType_test, Connect_007_102)
{
    int i;
    uint32_t propType;
    char fieldName[128];
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    res = testGmcConnect(&g_conn_2, &g_stmt_2);
    ASSERT_EQ(GMERR_OK, res);
    GmcNodeNameAndPropIdT NodeNameAndPropId = {"T1", 0};
    res = GmcGetPropTypeByNodeNamePropId(g_stmt_2, &NodeNameAndPropId, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, res);
    printf("GmcGetPropTypeByNodeNamePropId T1/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);

    ret = testGmcDisconnect(g_conn_2, g_stmt_2);
    EXPECT_EQ(GMERR_OK, ret);
}

// 103.验证GmcGetPropTypeByKeyNamePropOrder入参stmt句柄和用例上下文所用的句柄不一致,预期报错
TEST_F(GetPropType_test, Connect_007_103)
{
    int i;
    char fieldName[128];
    uint32_t propType;
    const char *keyName = "primary_key";
    uint32_t labelId = 0;
    res = testGmcPrepareStmtByLabelName(g_stmt_sync, labelName, GMC_OPERATION_SCAN);
    ASSERT_EQ(GMERR_OK, res);

    res = testGmcConnect(&g_conn_2, &g_stmt_2);
    ASSERT_EQ(GMERR_OK, res);
    GmcKeyNameAndOrderT keyNameAndOrder = {keyName, labelId};
    res = GmcGetPropTypeByKeyNamePropOrder(g_stmt_2, &keyNameAndOrder, fieldName, 128, &propType);
    ASSERT_EQ(GMERR_WRONG_STMT_OBJECT, res);
    printf("GmcGetPropTypeByKeyNameLabelId primary_key/0 %d\n", propType);
    printf("fieldName %s\n", fieldName);
    if (propType == GMC_DATATYPE_INT32) {
        printf("propType is true, propType = %d\n", propType);
        i = 0;
    } else {
        printf("propType is false, propType = %d\n", propType);
        i = GMERR_FEATURE_NOT_SUPPORTED;
    }
    ASSERT_EQ(GMERR_FEATURE_NOT_SUPPORTED, i);

    ret = testGmcDisconnect(g_conn_2, g_stmt_2);
    EXPECT_EQ(GMERR_OK, ret);
}
