[{"version": "2.0", "type": "record", "name": "test32_deeep1", "fields": [{"name": "A17", "type": "resource", "nullable": false}, {"name": "A1", "type": "int32", "nullable": false}, {"name": "A2", "type": "int32", "nullable": true}, {"name": "T1", "type": "record", "fields": [{"name": "B1", "type": "int32"}, {"name": "B2", "type": "uint32"}, {"name": "B3", "type": "int32", "nullable": true}, {"name": "T2", "type": "record", "fields": [{"name": "C1", "type": "int32"}, {"name": "C2", "type": "uint32"}, {"name": "C3", "type": "int32", "nullable": true}, {"name": "T3", "type": "record", "fields": [{"name": "D1", "type": "int32"}, {"name": "D2", "type": "int32", "nullable": true}, {"name": "T4", "type": "record", "fields": [{"name": "E1", "type": "int32"}, {"name": "E2", "type": "int32", "nullable": true}, {"name": "T5", "type": "record", "fields": [{"name": "F1", "type": "int32"}, {"name": "F2", "type": "int32", "nullable": true}, {"name": "T6", "type": "record", "fields": [{"name": "G1", "type": "int32"}, {"name": "G2", "type": "int32", "nullable": true}, {"name": "T7", "type": "record", "fields": [{"name": "H1", "type": "int32"}, {"name": "H2", "type": "int32", "nullable": true}, {"name": "T8", "type": "record", "fields": [{"name": "I1", "type": "int32"}, {"name": "I2", "type": "int32", "nullable": true}, {"name": "T9", "type": "record", "fields": [{"name": "J1", "type": "int32"}, {"name": "J2", "type": "int32", "nullable": true}, {"name": "T10", "type": "record", "fields": [{"name": "K1", "type": "int32"}, {"name": "K2", "type": "int32", "nullable": true}, {"name": "T11", "type": "record", "fields": [{"name": "L1", "type": "int32"}, {"name": "L2", "type": "int32", "nullable": true}, {"name": "T12", "type": "record", "fields": [{"name": "M1", "type": "int32"}, {"name": "M2", "type": "int32", "nullable": true}, {"name": "T13", "type": "record", "fields": [{"name": "N1", "type": "int32"}, {"name": "N2", "type": "int32", "nullable": true}, {"name": "T14", "type": "record", "fields": [{"name": "O1", "type": "int32"}, {"name": "O2", "type": "int32", "nullable": true}, {"name": "T15", "type": "record", "fields": [{"name": "P1", "type": "string", "size": 100, "nullable": true}, {"name": "P2", "type": "int32", "nullable": true}, {"name": "T16", "type": "record", "fields": [{"name": "Q1", "type": "bytes", "size": 7, "nullable": true}, {"name": "Q2", "type": "int32", "nullable": true}, {"name": "T17", "type": "record", "fields": [{"name": "R1", "type": "fixed", "size": 7, "nullable": true}, {"name": "R2", "type": "int32", "nullable": true}, {"name": "T18", "type": "record", "fields": [{"name": "S1", "type": "uchar", "nullable": true}, {"name": "S2", "type": "int32", "nullable": true}, {"name": "T19", "type": "record", "fields": [{"name": "U1", "type": "char", "nullable": true}, {"name": "U2", "type": "int32", "nullable": true}, {"name": "T20", "type": "record", "fields": [{"name": "V1", "type": "double", "nullable": true}, {"name": "V2", "type": "int32", "nullable": true}, {"name": "T21", "type": "record", "fields": [{"name": "W1", "type": "float", "nullable": true}, {"name": "W2", "type": "int32", "nullable": true}, {"name": "T22", "type": "record", "fields": [{"name": "X1", "type": "time", "nullable": true}, {"name": "X2", "type": "int32", "nullable": true}, {"name": "T23", "type": "record", "fields": [{"name": "Y1", "type": "boolean", "nullable": true}, {"name": "Y2", "type": "int32", "nullable": true}, {"name": "T24", "type": "record", "fields": [{"name": "Z1", "type": "uint8", "nullable": true}, {"name": "Z2", "type": "int32", "nullable": true}, {"name": "T25", "type": "record", "fields": [{"name": "AA1", "type": "int8", "nullable": true}, {"name": "AA2", "type": "int32", "nullable": true}, {"name": "T26", "type": "record", "fields": [{"name": "BB1", "type": "uint16", "nullable": true}, {"name": "BB2", "type": "int32", "nullable": true}, {"name": "T27", "type": "record", "fields": [{"name": "CC1", "type": "int16", "nullable": true}, {"name": "CC2", "type": "int32", "nullable": true}, {"name": "T28", "type": "record", "fields": [{"name": "DD1", "type": "int64", "nullable": true}, {"name": "DD2", "type": "int32", "nullable": true}, {"name": "T29", "type": "record", "fields": [{"name": "EE1", "type": "uint64", "nullable": true}, {"name": "EE2", "type": "int32", "nullable": true}, {"name": "T30", "type": "record", "fields": [{"name": "FF1", "type": "uint32", "nullable": true}, {"name": "FF2", "type": "int32", "nullable": true}, {"name": "T31", "type": "record", "fixed_array": true, "size": 3, "fields": [{"name": "A0", "type": "int64", "nullable": true}, {"name": "A1", "type": "uint64", "nullable": true}, {"name": "A2", "type": "int32", "nullable": true}, {"name": "A3", "type": "uint32", "nullable": true}, {"name": "A4", "type": "int16", "nullable": true}, {"name": "A5", "type": "uint16", "nullable": true}, {"name": "A6", "type": "int8", "nullable": true}, {"name": "A7", "type": "uint8", "nullable": true}, {"name": "A8", "type": "boolean", "nullable": true}, {"name": "A9", "type": "float", "nullable": true}, {"name": "A10", "type": "double", "nullable": true}, {"name": "A11", "type": "time", "nullable": true}, {"name": "A12", "type": "char", "nullable": true}, {"name": "A13", "type": "uchar", "nullable": true}, {"name": "A14", "type": "string", "size": 100, "nullable": true}, {"name": "A15", "type": "bytes", "size": 7, "nullable": true}, {"name": "A16", "type": "fixed", "size": 7, "nullable": true}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}]}], "keys": [{"name": "primary_key", "index": {"type": "primary"}, "node": "test32_deeep1", "fields": ["A17"], "constraints": {"unique": true}}]}]