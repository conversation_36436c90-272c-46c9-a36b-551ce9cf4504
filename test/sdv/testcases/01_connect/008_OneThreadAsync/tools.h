/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: 异步单线程epoll测试头文件
 * Author: ynw
 * Create: Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * History:
 */
#ifndef _TOOLS_H_
#define _TOOLS_H_

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "table_struct.h"
#define MAX_CMD_SIZE 1024
GmcConnT *g_conn_sync = NULL, *g_conn_async = NULL, *g_conn_sub = NULL;
GmcStmtT *g_stmt_sync = NULL, *g_stmt_async = NULL, *g_stmt_sub = NULL;

GmcConnT *g_conn_sync2 = NULL, *g_conn_async2 = NULL, *g_conn_sub2 = NULL;
GmcStmtT *g_stmt_sync2 = NULL, *g_stmt_async2 = NULL, *g_stmt_sub2 = NULL;
char *g_schema = NULL, *g_schema_struct = NULL;
int data_num = 100;
void *g_label = NULL;
char g_table_name[] = "TEST_T1", g_table_name2[] = "TEST_T2", g_table_name3[] = "TEST_T3", g_table_name4[] = "TEST_T4",
     g_table_name5[] = "TEST_T5", g_table_name6[] = "TEST_T6", g_table_name7[] = "TEST_T7", g_table_name8[] = "TEST_T8",
     g_table_name9[] = "TEST_T9", g_table_name10[] = "TEST_T10";
char g_pk_name[] = "TEST_PK";
char g_command[MAX_CMD_SIZE] = {0};
int g_data_num = 50; // 10000
pthread_mutex_t g_threadLock;
#define MAX_NAME_LENGTH 128
const char *g_subConnName = "subConnName_my";
const char *g_subName = "subVertexLabel";
GmcCheckInfoT *checkInfo, *checkInfo2;
GmcCheckStatusE checkStatus, checkStatus2;
bool isAbnormal = false;
#define FULLTABLE 0

const char *gResPoolTest =
    R"({
        "name" : "resource_pool_test",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type" : 0
    })";
const char *resPoolTestName = "resource_pool_test";

#define TEST_INFO(info, recordId, mod, threadId)                                                   \
    do {                                                                                           \
        if (1) {                                                                                   \
            if ((recordId) % (mod) == 0) {                                                             \
                fprintf(stdout, "[%s] record Id : %d, threadId : %d\n", info, recordId, threadId); \
            }                                                                                      \
        }                                                                                          \
    } while (0)

void test_setVertexProperty(GmcStmtT *stmt, int pk = 0)
{
    int ret;
    char teststr0 = 'a';
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char teststr1 = 'b';
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t value2 = 1 + pk;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &value2, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t value3 = 10 + pk;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &value3, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t value4 = 100 + pk;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &value4, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t value5 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &value5, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t value6 = pk;  // 联合索引时F6是PK
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &value6, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value7 = pk;  // F7是PK
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &value7, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool value8 = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &value8, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    int64_t value9 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &value9, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value10 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &value10, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float value11 = (float)1.2 + (float)pk;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &value11, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double value12 = 10.86 + pk;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t value13 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &value13, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr14[] = "string";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, teststr14, (strlen(teststr14)));
    EXPECT_EQ(GMERR_OK, ret);
    char teststr15[10] = "bytes";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, teststr15, 10);
    EXPECT_EQ(GMERR_OK, ret);
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, teststr16, (strlen(teststr16)));
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t value17 = 1000 + pk;
    ret = GmcSetVertexProperty(stmt, "F17", GMC_DATATYPE_UINT32, &value17, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}
void test_set_simple_table(GmcStmtT *stmt, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    int rs = 0;
    rs = snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    if (rs < 0) {
        printf("filed\n");
    }
    uint8_t F15Value = index & 0xF;

    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &F0Value, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value, sizeof(F15Value));
    EXPECT_EQ(GMERR_OK, ret);
}
void test_set_simple_table_root(GmcStmtT *stmt, int index, bool bool_value)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = bool_value;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    int rs = 0;
    rs = snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    if (rs < 0) {
        printf("filed\n");
    }
    uint8_t F15Value = index & 0xF;

    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &F2Value, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &F6Value, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &F11Value, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_FIXED, F14Value, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value, sizeof(F15Value));
    EXPECT_EQ(GMERR_OK, ret);
}
void test_read_simple_table(GmcStmtT *stmt, int index, char *label_name)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    int rs = 0;
    rs = snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    if (rs < 0) {
        printf("filed\n");
    }
    uint8_t F15Value = index & 0xF;

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT8, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_TIME, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_FIXED, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void test_struct_read_simple_table(TEST_T1_struct_t *d, int index, char *label_name)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    int rs = 0;
    rs = snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    if (rs < 0) {
        printf("filed\n");
    }
    uint8_t F15Value = index & 0xF;
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    EXPECT_EQ(GMERR_OK, ret);
}

void test_struct_read_simple_table_root2(TEST_T1_struct_t *d, int index, char *label_name)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    int rs = 0;
    rs = snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    if (rs < 0) {
        printf("filed\n");
    }
    uint8_t F15Value = index & 0xF;
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    EXPECT_EQ(GMERR_OK, ret);
}
void db_test_write_simple_table(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("INSERT", i, 1000, 0);
        test_set_simple_table(stmt, i, false);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}
// 普通同步操作
void db_test_struct_write_simple_table(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT INSERT", i, 10000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void db_test_merge_simple_table(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_simple_table(stmt, i, false);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void db_test_replace_simple_table(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_simple_table(stmt, i, false);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            EXPECT_EQ(expected_affectRows, affectRows);
        }
    }
}

void db_test_struct_merge_simple_table(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void db_test_struct_replace_simple_table(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT INSERT", i, 10000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            EXPECT_EQ(expected_affectRows, affectRows);
        }
    }
}
void db_test_struct_replace_simple_table_root2(GmcStmtT *stmt,
                                               int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_T1_value_root2(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void db_test_struct_delete_simple_table(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT DELETE", i, 10000, 0);
        if (strcmp(label_name, g_table_name) == 0 || strcmp(label_name, g_table_name8) == 0 ||
            strcmp(label_name, g_table_name9) == 0) {
            test_set_TEST_T1_primary_key(&obj, i);
        } else if (strcmp(label_name, g_table_name4) == 0) {
            test_set_TEST_T4_primary_key(&obj, i);
        } else {
            test_set_TEST_T1_primary_key(&obj, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            EXPECT_EQ(expected_affectRows, affectRows);
        }
    }
}

void db_test_delete_simple_table(GmcStmtT *stmt, int data_numss,
                                 char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("DELETE", i, 1000, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            EXPECT_EQ(expected_affectRows, affectRows);
        }
    }
}

void db_test_read_simple_table(GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("SCAN", i, 10000, 0);
        if (strcmp(label_name, g_table_name) == 0 || strcmp(label_name, g_table_name9) == 0 ||
            strcmp(label_name, g_table_name3) == 0) {
            int64_t f0_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            EXPECT_EQ(GMERR_OK, ret);
        } else if (strcmp(label_name, g_table_name4) == 0) {
            int64_t f0_value = i;
            uint64_t f1_value = (uint64_t)i + 0xFFFFFFFF;
            int32_t f2_value = i;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_UINT64, &f1_value, sizeof(f1_value));
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT32, &f2_value, sizeof(f2_value));
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_simple_table(stmt, i, label_name);
            if (strcmp(label_name, g_table_name3) == 0) {
                bool isNull;
                uint32_t resSize;
                uint64_t resVal;
                ret = GmcGetVertexPropertySizeByName(stmt, "F16", &resSize);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcGetVertexPropertyByName(stmt, "F16", &resVal, resSize, &isNull);
                ASSERT_EQ(GMERR_OK, ret);

                uint16_t tmpPoolId, tmpCount;
                uint32_t tmpStartIndex;
                ret = GmcGetPoolIdResource(resVal, &tmpPoolId);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcGetCountResource(resVal, &tmpCount);
                ASSERT_EQ(GMERR_OK, ret);
                ret = GmcGetStartIdxResource(resVal, &tmpStartIndex);
                ASSERT_EQ(GMERR_OK, ret);

                ASSERT_EQ(0, tmpPoolId);
                ASSERT_EQ(1, tmpCount);
                ASSERT_EQ(i, tmpStartIndex);
            }
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}
void db_test_read_simple_table_concurrency(GmcStmtT *stmt,
    int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("SCAN", i, 1000, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_simple_table(stmt, i, label_name);
            cnt++;
        }
    }
}

void db_test_read_simple_table_localhash(GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("SCAN", i, 1000, 0);
        uint64_t f1_value = (uint64_t)i + 0xFFFFFFFF;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1_value, sizeof(f1_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_simple_table(stmt, i, label_name);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}

void db_test_read_simple_table_hashcluster(GmcStmtT *stmt, int data_numss,
                                           char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("SCAN", i, 1000, 0);
        int32_t f2_value = (int32_t)i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &f2_value, sizeof(f2_value));
        EXPECT_EQ(GMERR_OK, ret);

        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_simple_table(stmt, i, label_name);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}

void db_test_update_simple_table_localhash(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, affectRows;
    for (i = 0; i < data_numss; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("LOCALHASH UPDATE", i, 500, 0);
        uint64_t f1_value = (uint64_t)i + 0xFFFFFFFF;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1_value, sizeof(f1_value));
        EXPECT_EQ(GMERR_OK, ret);

        int32_t f2_value = i;
        ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(f2_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void db_test_update_simple_table_localhash_full(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, affectRows;
    for (i = 0; i < data_numss; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        TEST_INFO("LOCALHASH UPDATE", i, 500, 0);
        uint64_t f1_value = (uint64_t)i + 0xFFFFFFFF;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_UINT64, &f1_value, sizeof(f1_value));
        EXPECT_EQ(GMERR_OK, ret);
        test_set_simple_table_root(stmt, i, false);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void db_test_delete_simple_table_localkey(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, affectRows;

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("LOCALKEY DELETE", i, 500, 0);

    unsigned int l_val_del = 0;
    unsigned int r_val_del = data_numss;
    unsigned int arrLen = 1;

    GmcPropValueT *leftKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (leftKeyProps_del == NULL) {
        printf("malloc filed\n");
        printf("filed\n");
    }
    leftKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    leftKeyProps_del[0].value = &l_val_del;
    leftKeyProps_del[0].size = sizeof(l_val_del);

    GmcPropValueT *rightKeyProps_del = (GmcPropValueT *)malloc(sizeof(GmcPropValueT) * arrLen);
    if (rightKeyProps_del == NULL) {
        printf("malloc filed\n");
        printf("filed\n");
    }
    rightKeyProps_del[0].type = GMC_DATATYPE_UINT32;
    rightKeyProps_del[0].value = &r_val_del;
    rightKeyProps_del[0].size = sizeof(r_val_del);

    GmcRangeItemT items[arrLen];
    items[0].lValue = &leftKeyProps_del[0];
    items[0].rValue = &rightKeyProps_del[0];
    items[0].lFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].rFlag = GMC_COMPARE_RANGE_CLOSED;
    items[0].order = GMC_ORDER_ASC;

    // 设置删除扫描
    ret = GmcSetKeyRange(stmt, items, arrLen);

    ret = GmcSetIndexKeyName(stmt, keyName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(expected_affectRows, affectRows);
}
void db_test_struct_read_simple_table(GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT SCAN", i, 10000, 0);
        if (strcmp(label_name, g_table_name) == 0 || strcmp(label_name, g_table_name9) == 0 ||
            strcmp(label_name, g_table_name10) == 0 || strcmp(label_name, g_table_name3) == 0) {
            test_set_TEST_T1_primary_key(&obj, i);
        } else if (strcmp(label_name, g_table_name4) == 0) {
            test_set_TEST_T4_primary_key(&obj, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_simple_table(&obj, i, label_name);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_read_simple_table_concurrency(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_simple_table(&obj, i, label_name);
            cnt++;
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_scan_simple_table(GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("SCAN", i, 1000, 0);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        test_read_simple_table(stmt, cnt, label_name);
        cnt++;
    }
    if (read_num) {
        EXPECT_EQ(data_numss, cnt);
    } else {
        EXPECT_EQ(0, cnt);
    }
}

void db_test_scan_simple_table_concurrency(GmcStmtT *stmt, int data_numss,
                                           char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    TEST_INFO("SCAN", i, 1000, 0);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
    }
}

void db_test_struct_scan_simple_table(GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        EXPECT_EQ(GMERR_OK, ret);
        test_struct_read_simple_table(&obj, cnt, label_name);
        cnt++;
    }
    if (read_num) {
        EXPECT_EQ(data_numss, cnt);
    } else {
        EXPECT_EQ(0, cnt);
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_scan_simple_table_concurrency(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    EXPECT_EQ(GMERR_OK, ret);
    bool isFinish = false;
    int cnt = 0;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        EXPECT_EQ(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = testStructGetVertexDeseri(stmt, &deseri);
        EXPECT_EQ(GMERR_OK, ret);
        cnt++;
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_read_simple_table_root2(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        if (strcmp(label_name, g_table_name) == 0 || strcmp(label_name, g_table_name9) == 0) {
            test_set_TEST_T1_primary_key(&obj, i);
        } else if (strcmp(label_name, g_table_name4) == 0) {
            test_set_TEST_T4_primary_key(&obj, i);
        }
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_simple_table_root2(&obj, i, label_name);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

// 批量同步操作
void db_test_struct_write_simple_table_batch(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (j = 0; j < data_numss / num_per_batch; j++) {
        TEST_INFO("STRUCT BATCH INSERT", j, 100, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_value(&obj, num_per_batch * j + i, false);
            ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, totalNum);
        EXPECT_EQ(1, successNum);
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(data_numss, count);
}

void db_test_struct_merge_simple_table_batch(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT BATCH INSERT", i, 1, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    EXPECT_EQ(GMERR_OK, ret);
    uint32_t totalNum, successNum;
    ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(totalNum, data_numss);
    EXPECT_EQ(successNum, data_numss);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(data_numss, count);
}

void db_test_struct_replace_simple_table_batch(GmcStmtT *stmt,
                                               int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (j = 0; j < data_numss / num_per_batch; j++) {
        TEST_INFO("STRUCT BATCH REPLACE", j, 100, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_value(&obj, num_per_batch * j + i, false);
            ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(num_per_batch, totalNum);
        EXPECT_EQ(num_per_batch, successNum);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(data_numss, count);
}

void db_test_struct_delete_simple_table_batch(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_sync, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (j = 0; j < data_numss / num_per_batch; j++) {
        TEST_INFO("STRUCT BATCH DELETE", j, 100, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_primary_key(&obj, num_per_batch * j + i);
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchExecute(batch, &batchRet);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t totalNum, successNum;
        ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(num_per_batch, totalNum);
        EXPECT_EQ(num_per_batch, successNum);
    }
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    uint64_t count = 0;
    ret = GmcGetVertexCount(stmt, label_name, NULL, &count);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(0, count);
}

void db_test_struct_write_big_obj_table(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T6_struct_t *obj = (TEST_T6_struct_t *)malloc(sizeof(TEST_T6_struct_t));
    if (obj == NULL) {
        printf("malloc filed\n");
        printf("filed\n");
    }
    memset(obj, 0, sizeof(TEST_T6_struct_t));
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT INSERT", i, 500, 0);
        test_set_TEST_T6_value(obj, i);
        ret = testStructSetVertexWithBuf(stmt, obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
    free(obj);
}

void db_test_struct_merge_big_obj_table(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T6_struct_t obj = (TEST_T6_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT MERGE", i, 500, 0);
        test_set_TEST_T6_value(&obj, i);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void db_test_struct_replace_big_obj_table(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T6_struct_t *obj = (TEST_T6_struct_t *)malloc(sizeof(TEST_T6_struct_t));
    if (obj == NULL) {
        printf("malloc filed\n");
        printf("filed\n");
    }
    memset(obj, 0, sizeof(TEST_T6_struct_t));
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT REPLACE", i, 500, 0);
        test_set_TEST_T6_value(obj, i);
        ret = testStructSetVertexWithBuf(stmt, obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
    free(obj);
}

void test_read_big_obj_table(GmcStmtT *stmt, int index, char *label_name)
{
    int ret, i;
    char field_value[STRING_MAX_SIZE] = {0};
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    for (i = 0; i < STRING_MAX_SIZE - 1; i++) {
        field_value[i] = 'A';
    }

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F18", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F19", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F20", GMC_DATATYPE_FIXED, field_value);
    EXPECT_EQ(GMERR_OK, ret);
}

void db_test_read_big_obj_table(GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("SCAN", i, 500, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_big_obj_table(stmt, i, label_name);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}

void test_struct_read_big_obj_table(TEST_T6_struct_t *d, int index, char *label_name)
{
    int ret, i;
    char field_value[STRING_MAX_SIZE] = {0};
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index;
    for (i = 0; i < STRING_MAX_SIZE - 1; i++) {
        field_value[i] = 'A';
    };

    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F4, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F5, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F6, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F7, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F8, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F9, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F10, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F11, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F12, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F13, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F14, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F15, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F16, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F17, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F18, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F19, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, field_value, &d->F20, sizeof(field_value));
    EXPECT_EQ(GMERR_OK, ret);
}

void db_test_struct_read_big_obj_table(GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T6_struct_t *obj = (TEST_T6_struct_t *)malloc(sizeof(TEST_T6_struct_t));
    if (obj == NULL) {
        printf("malloc filed\n");
        printf("filed\n");
    }
    memset(obj, 0, sizeof(TEST_T6_struct_t));
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT SCAN", i, 500, 0);
        test_set_TEST_T6_primary_key(obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_big_obj_table(obj, i, label_name);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
    free(obj);
}

void db_test_struct_delete_big_obj_table(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T6_struct_t *obj = (TEST_T6_struct_t *)malloc(sizeof(TEST_T6_struct_t));
    if (obj == NULL) {
        printf("malloc filed\n");
        printf("filed\n");
    }
    memset(obj, 0, sizeof(TEST_T6_struct_t));
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT DELETE", i, 500, 0);
        test_set_TEST_T6_primary_key(obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
    free(obj);
}

// 异步批量结构化写
void db_test_struct_write_simple_table_batch_async(
    GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (j = 0; j < data_numss / num_per_batch; j++) {
        TEST_INFO("STRUCT BATCH INSERT ASYNC", j, 100, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_value(&obj, num_per_batch * j + i, false);
            ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(num_per_batch, data.totalNum);
        EXPECT_EQ(num_per_batch, data.succNum);
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

// 异步批量结构化写
void db_test_struct_merge_simple_table_batch_async(
    GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT BATCH MERGE ASYNC", i, 500, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, data.status);
    EXPECT_EQ(data_numss, data.totalNum);
    EXPECT_EQ(data_numss, data.succNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

// 异步批量结构化写
void db_test_struct_replace_simple_table_batch_async(
    GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (j = 0; j < data_numss / num_per_batch; j++) {
        TEST_INFO("STRUCT BATCH REPLACE ASYNC", j, 100, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_value(&obj, num_per_batch * j + i, false);
            ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(num_per_batch, data.totalNum);
        EXPECT_EQ(num_per_batch, data.succNum);
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

// 异步单个结构化写
void db_test_struct_write_simple_table_async(GmcStmtT *stmt, int data_numss,
                                             char *label_name, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT INSERT ASYNC", i, 10000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecv(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expected_affectRows, data.affectRows);
    }
}

// 异步单个结构化写
void db_test_struct_merge_simple_table_async(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_MERGE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT MERGE ASYNC", i, 10000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = merge_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expected_affectRows, data.affectRows);
    }
}

// 异步单个结构化写
void db_test_struct_replace_simple_table_async(GmcStmtT *stmt,
    int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT REPLACE ASYNC", i, 10000, 0);
        test_set_TEST_T1_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = replace_vertex_callback;
        insertRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expected_affectRows, data.affectRows);
    }
}

// 异步单个结构化删
void db_test_struct_delete_simple_table_async(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT DELETE ASYNC", i, 10000, 0);
        test_set_TEST_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.deleteCb = delete_vertex_callback;
        deleteRequestCtx.userData = &data;
        ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(expected_affectRows, data.affectRows);
    }
}

// 异步批量结构化删
void db_test_struct_delete_simple_table_batch_async(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, int expected_affectRows)
{
    int ret, i, j, num_per_batch = 1024;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    AsyncUserDataT data = {0};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);

    for (j = 0; j < data_numss / num_per_batch; j++) {
        TEST_INFO("STRUCT DELETE BATCH ASYNC", j, 10000, 0);
        for (i = 0; i < num_per_batch; i++) {
            test_set_TEST_T1_primary_key(&obj, num_per_batch * j + i);
            ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
            EXPECT_EQ(GMERR_OK, ret);
            ret = GmcBatchAddDML(batch, stmt);
            EXPECT_EQ(GMERR_OK, ret);
        }
        ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &data);
        ASSERT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&data);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, data.status);
        EXPECT_EQ(1, data.totalNum);
        EXPECT_EQ(1, data.succNum);
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
}

void test_read_TEST_T8(GmcStmtT *stmt, int index, char *label_name)
{
    int ret;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index + 100;  // 自增列起始值为100
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    int rs = 0;
    rs = snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    if (rs < 0) {
        printf("filed\n");
    }
    uint8_t F15Value = index & 0xF;

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_INT64, &F0Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &F1Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &F2Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &F3Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &F4Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT8, &F6Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT8, &F7Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FLOAT, &F9Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_DOUBLE, &F10Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_TIME, &F11Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_CHAR, &F12Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_UCHAR, &F13Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_FIXED, F14Value);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_PARTITION, &F15Value);
    EXPECT_EQ(GMERR_OK, ret);
}

void db_test_read_TEST_T8(GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("SCAN", i, 1000, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_TEST_T8(stmt, i, label_name);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}

void test_struct_read_TEST_T8(TEST_T1_struct_t *d, int index, char *label_name)
{
    int ret = 0;
    int64_t F0Value = (int64_t)index;
    uint64_t F1Value = (uint64_t)index + 0xFFFFFFFF;
    int32_t F2Value = index;
    uint32_t F3Value = index + 100;  // 自增列起始值为100
    int16_t F4Value = index & 0x7FFF;
    uint16_t F5Value = index & 0xFFFF;
    int8_t F6Value = index & 0x7F;
    uint8_t F7Value = index & 0xFF;
    bool F8Value = false;
    float F9Value = index;
    double F10Value = index;
    uint64_t F11Value = index + 0xFFFFFFFF;
    char F12Value = 'a' + (index & 0x1A);
    unsigned char F13Value = 'A' + (index & 0x1A);
    char F14Value[16] = {0};
    int rs = 0;
    rs = snprintf((char *)F14Value, sizeof(F14Value), "aaaaaaa%08d", index);
    if (rs < 0) {
        printf("filed\n");
    }
    uint8_t F15Value = index & 0xF;

    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT64, &F0Value, &d->F0, sizeof(F0Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT64, &F1Value, &d->F1, sizeof(F1Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT32, &F2Value, &d->F2, sizeof(F2Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT32, &F3Value, &d->F3, sizeof(F3Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT16, &F4Value, &d->F4, sizeof(F4Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT16, &F5Value, &d->F5, sizeof(F5Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_INT8, &F6Value, &d->F6, sizeof(F6Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UINT8, &F7Value, &d->F7, sizeof(F7Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_BOOL, &F8Value, &d->F8, sizeof(F8Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FLOAT, &F9Value, &d->F9, sizeof(F9Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_DOUBLE, &F10Value, &d->F10, sizeof(F10Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_TIME, &F11Value, &d->F11, sizeof(F11Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_CHAR, &F12Value, &d->F12, sizeof(F12Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_UCHAR, &F13Value, &d->F13, sizeof(F13Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_FIXED, F14Value, d->F14, sizeof(F14Value));
    EXPECT_EQ(GMERR_OK, ret);
    ret = CompareVertexPropertyValue(GMC_DATATYPE_PARTITION, &F15Value, &d->F15, sizeof(F15Value));
    EXPECT_EQ(GMERR_OK, ret);
}

void db_test_struct_read_TEST_T8(GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_TEST_T8(&obj, i, label_name);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_write_TEST_T8(GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_T8_value(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void test_set_TEST_T1_value_partition(TEST_T1_struct_t *d, int v, bool bool_value)
{
    d->F0 = (int64_t)v;                                          // int64   8
    d->F1 = (uint64_t)v + 0xFFFFFFFF;                            // uint64  8
    d->F2 = v;                                                   // int32   4
    d->F3 = v;                                                   // uint32  4
    d->F4 = v & 0x7FFF;                                          // int16   2
    d->F5 = v & 0xFFFF;                                          // uint16  2
    d->F6 = v & 0x7F;                                            // int8    1
    d->F7 = v & 0xFF;                                            // uint8   1
    d->F8 = bool_value;                                          // boolean 4
    d->F9 = v;                                                   // float   4
    d->F10 = v;                                                  // double  8
    d->F11 = v + 0xFFFFFFFF;                                     // time    8
    d->F12 = 'a' + (v & 0x1A);                                   // char     1
    d->F13 = 'A' + (v & 0x1A);                                   // uchar   1
    int rs = 0;
    rs = snprintf((char *)d->F14, sizeof(d->F14), "aaaaaaa%08d", v);  // fixed  16
    if (rs < 0) {
        printf("filed\n");
    }
    d->F15 = 100;                                                // partition  1
}

void db_test_struct_replace_simple_table_partition(
    GmcStmtT *stmt, int data_numss, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    unsigned int len;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_T1_value_partition(&obj, i, false);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }
}

void db_test_struct_read_res_table(GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T3_struct_t obj = (TEST_T3_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_T3_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            uint64_t resVal = obj.F16;

            uint16_t tmpPoolId, tmpCount;
            uint32_t tmpStartIndex;
            ret = GmcGetPoolIdResource(resVal, &tmpPoolId);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcGetCountResource(resVal, &tmpCount);
            ASSERT_EQ(GMERR_OK, ret);
            ret = GmcGetStartIdxResource(resVal, &tmpStartIndex);
            ASSERT_EQ(GMERR_OK, ret);

            ASSERT_EQ(0, tmpPoolId);
            ASSERT_EQ(1, tmpCount);
            ASSERT_EQ(i, tmpStartIndex);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_struct_get_vertex_count_simple_table(
    GmcStmtT *stmt, int data_numss, char *label_name, char *keyName, bool read_num)
{
    int ret, i;
    TEST_T1_struct_t obj = (TEST_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    
    for (i = 0; i < data_numss; i++) {
        TEST_INFO("STRUCT GmcGetVertexCount", i, 1000, 0);
        test_set_TEST_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        uint64_t count = 0;
        ret = GmcGetVertexCount(stmt, label_name, keyName, &count);
        if (read_num) {
            EXPECT_EQ(1, count);
        } else {
            EXPECT_EQ(0, count);
        }
    }
}
#endif  // _TOOLS_H_
