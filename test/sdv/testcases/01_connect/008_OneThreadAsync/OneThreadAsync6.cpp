/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: 异步单线程测试文件
 * Author: ynw
 * Create: Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * History:
 */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "tools.h"
#define MAX_NAME_LENGTH 128
char g_label_name[] = "T20_all_type", g_label_name_2[] = "T20_all_type_2";
char g_lable_PK[] = "T20_PK";
const char *g_normal_vertexlabel_name = "T39_all_type";
const char *g_normal_pk_name = "T39_K0";
const char *g_normal_sk_name = "T39_hash";
char *normal_vertexlabel_schema = NULL;
char *g_sub_info = NULL;
int malloc_memory = 1000000;


AsyncUserDataT asyncUserData = {0};

int g_subIndex = 0;
const char *normal_config_json = R"(
    {
        "max_record_count":10000000
    }
)";
using namespace std;


class OneThreadAsync06 : public testing::Test {
public:
    SnUserDataT *user_data;
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/start.sh");
        int ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
   
        // 单线程异步标识
        g_isOneThreadEpoll = true;

        ret = createEpollOneThread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {   
        g_isOneThreadEpoll = false;

        closeEpollOneThread();

        testEnvClean();
    }
};

void OneThreadAsync06::SetUp()
{
    printf("OneThreadAsync06 SetUp Start.\n");

    int ret = testGmcConnect(&g_conn_async, &g_stmt_async,
                             GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd); // 原来 g_epoll_reg_info
    EXPECT_EQ(GMERR_OK, ret);
    // 创建同步连接
    ret = testGmcConnect(&g_conn_sync, &g_stmt_sync, GMC_CONN_TYPE_SYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);

    readJanssonFile("schemaFile/all_type_schema.gmjson", &g_schema);
    EXPECT_NE((void *)NULL, g_schema);
    GmcDropVertexLabel(g_stmt_sync, g_label_name);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, g_schema, normal_config_json, create_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    free(g_schema);

    user_data = NULL;
    g_schema = NULL;
    g_label = NULL;
    g_sub_info = NULL;
    g_subIndex = 0;
    ret = testSnMallocUserData(&user_data, malloc_memory);
    EXPECT_EQ(GMERR_OK, ret);

    AW_CHECK_LOG_BEGIN();
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    char errorMsg5[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_CONNECTION_SEND_BUFFER_FULL);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_TOO_MANY_CONNECTIONS);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    (void)snprintf(errorMsg5, errCodeLen, "GMERR-%d", GMERR_NULL_VALUE_NOT_ALLOWED);
    AW_ADD_ERR_WHITE_LIST(5, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5);
}

void OneThreadAsync06::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    GmcDropVertexLabel(g_stmt_sync, g_label_name);
    ret = testGmcDisconnect(g_conn_async, g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(g_conn_sync, g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_sub_info);
    free(g_schema);
    testSnFreeUserData(user_data);
    printf("OneThreadAsync06 SetUp End.\n");
}

void test_checkVertexProperty_sub(GmcStmtT *stmt, int pk = 0)
{
    int ret = 0;
    char teststr0 = 'a';
    unsigned char teststr1 = 'b';
    int8_t value2 = (1 + pk);
    uint8_t value3 = (10 + pk);
    int16_t value4 = (100 + pk);
    uint16_t value5 = (1000 + pk);
    int32_t value6 = pk;   // 联合索引时F6是pk
    uint32_t value7 = pk;  // F7是pk
    bool value8 = false;
    int64_t value9 = 1000 + pk;
    uint64_t value10 = 1000 + pk;
    float value11 = (float)1.2 + (float)pk;
    double value12 = 10.86 + pk;
    uint64_t value13 = 1000 + pk;
    char teststr14[] = "string";
    char teststr15[10] = "bytes";
    char teststr16[6] = "fixed";
    EXPECT_EQ(6, strlen(teststr16) + 1);
    uint32_t value17 = (1000 + pk);

    ret = queryPropertyAndCompare(stmt, "F0", GMC_DATATYPE_CHAR, &teststr0);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UCHAR, &teststr1);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT8, &value2);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT8, &value3);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &value4);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &value5);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT32, &value6);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &value8);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_INT64, &value9);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_UINT64, &value10);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_FLOAT, &value11);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_DOUBLE, &value12);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_TIME, &value13);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, teststr14);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F15", GMC_DATATYPE_BYTES, teststr15);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F16", GMC_DATATYPE_FIXED, teststr16);
    EXPECT_EQ(GMERR_OK, ret);
    ret = queryPropertyAndCompare(stmt, "F17", GMC_DATATYPE_UINT32, &value17);
    EXPECT_EQ(GMERR_OK, ret);
}
void sn_callback_fullsub(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int pk, ret, i;
    uint32_t PK = 0;  // F7是pk
    unsigned int sizeValue = 0;
    bool isNull;
    SnUserDataT *user_data = (SnUserDataT *)userData;

    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK) {
            if (info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
                user_data->scanEofNum++;
                printf("call back user_data->scanEofNum: %d\n", user_data->scanEofNum);
                printf("[INFO] <---GMC_SUB_EVENT_INITIAL_LOAD_EOF IS OVER--->\r\n");
                break;
            } else {
                printf("[INFO] <---Abnormal--!!!-->%d\r\n", ret);
                break;
            }
        } else if (eof) {
            break;
        }
        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    pk = ((int *)user_data->new_value)[g_subIndex];
                    g_subIndex++;
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    pk = ((int *)user_data->old_value)[g_subIndex];
                    test_checkVertexProperty_sub(subStmt, pk);
                    g_subIndex++;
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    pk = ((int *)user_data->new_value)[g_subIndex];
                    test_checkVertexProperty_sub(subStmt, pk);
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    pk = ((int *)user_data->old_value)[g_subIndex];
                    test_checkVertexProperty_sub(subStmt, pk);
                    g_subIndex++;
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_UPDATE: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    pk = ((int *)user_data->new_value)[g_subIndex];
                    test_checkVertexProperty_sub(subStmt, pk);
                    g_subIndex++;
                    break;
                }
                case GMC_SUB_EVENT_REPLACE_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    pk = ((int *)user_data->new_value)[g_subIndex];
                    test_checkVertexProperty_sub(subStmt, pk);
                    g_subIndex++;
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);

                    ret = GmcGetVertexPropertySizeByName(subStmt, "F7", &sizeValue);
                    EXPECT_EQ(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "F7", &PK, sizeValue, &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    test_checkVertexProperty_sub(subStmt, PK);
                    break;
                }
                default: {
                    printf("default: invalid eventType %d\r\n", info->eventType);
                    break;
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_UPDATE: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE_INSERT: {
                user_data->replaceUpdateNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
        }
    }
}
// 一个label最大顶点个数验证
TEST_F(OneThreadAsync06, DISABLED_Connect_008_000)
{
    int32_t dataNum = 100, ret;
    AsyncUserDataT asyncUserDataLocal = {0};
    GmcDropVertexLabel(g_stmt_sync, g_label_name);
    // 插入
    for (size_t i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        uint32_t priK = i;
        test_setVertexProperty(g_stmt_async, priK);
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.insertCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncUserDataLocal;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal, dataNum);
    EXPECT_EQ(GMERR_OK, ret);
    printf("ok 99\n");
}
// 异步单线程创建1024个stmt
TEST_F(OneThreadAsync06, Connect_008_022)
{
    int32_t dataNum = 10, ret;
    GmcStmtT *stmt2[10240] = {NULL};
    // 插入
    for (size_t i = 0; i < 1; i++) {
        ret = GmcAllocStmt(g_conn_async, &(stmt2[i]));
        ASSERT_EQ(GMERR_OK, ret);
    }
    // 此处似乎不会有消息响应
    for (size_t i = 0; i < 1; i++) {
        GmcFreeStmt(stmt2[i]);
    }
    printf("ok 10240\n");
}
int testWaitSnRecvOneThread(
    void *userData, GmcSubEventTypeE eventType, int expRecvNum = 1,
    bool isAutoReset = true, int32_t epollFd = g_epollDataOneThread.userEpollFd)
{
    struct epoll_event events[MAX_EPOLL_EVENT_COUNT];
    SnUserDataT *user_data = (SnUserDataT *)userData;
    switch (eventType) {
        case GMC_SUB_EVENT_INSERT: {
            while (user_data->insertNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                   TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->insertNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_DELETE: {
            while (user_data->deleteNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                   TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->deleteNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_UPDATE: {
            while (user_data->updateNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                   TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->updateNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_REPLACE: {
            while (user_data->replaceNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                   TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->replaceNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_REPLACE_INSERT: {
            while (user_data->replaceInsertNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                   TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->replaceInsertNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_REPLACE_UPDATE: {
            while (user_data->replaceUpdateNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                    TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->replaceUpdateNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_MERGE: {
            while (user_data->mergeNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                    TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->mergeNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_MERGE_INSERT: {
            while (user_data->mergeInsertNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                   TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->mergeInsertNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_MERGE_UPDATE: {
            while (user_data->mergeUpdateNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                    TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->mergeUpdateNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_KV_SET: {
            while (user_data->kvSetNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                   TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->kvSetNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD: {
            while (user_data->scanNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                   TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->scanNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
            while (user_data->scanEofNum != expRecvNum) {
                printf("recv user_data->scanEofNum: %d\n", user_data->scanEofNum);
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                  TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->scanEofNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_AGED: {
            while (user_data->agedNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                   TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->agedNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_TRIGGER_SCAN_BEGIN: {
            while (user_data->triggerScanBeginNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                    TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->triggerScanBeginNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_TRIGGER_SCAN: {
            while (user_data->triggerScanNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                   TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->triggerScanNum = 0;
            }
            break;
        }
        case GMC_SUB_EVENT_TRIGGER_SCAN_END: {
            while (user_data->triggerScanEndNum != expRecvNum) {
                int fdCount = epoll_wait(epollFd, events, MAX_EPOLL_EVENT_COUNT, EPOLL_TIME_OUT_MS);
                if (fdCount < 0) {
                    continue;
                }
                while (fdCount > 0) {
                    --fdCount;
                   TestEpollDoEvent(events[fdCount].data.fd, events[fdCount].events);
                }
            }
            if (isAutoReset) {
                user_data->triggerScanEndNum = 0;
            }
            break;
        }
        default: {
            printf("default: invalid eventType\r\n");
            assert(0);
            break;
        }
    }
    return 0;
}

// 单线程vertex简单表创建全量订阅，先全量订阅，再写数据
// 全量订阅，创建订阅前先搞一些操作

TEST_F(OneThreadAsync06, Connect_008_023)
{
    int ret, i, affectRows;
    int userDataIdx = 0;
    // 创建订阅连接
    int chanRingLen = 256;
    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
    connOptions.connName = g_subConnName;
    ret = TestYangGmcConnect(&g_conn_sub, NULL, GMC_CONN_TYPE_SUB, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    readJanssonFile("schemaFile/all_type_schema_subinfo.gmjson", &g_sub_info);
    ASSERT_NE((void *)NULL, g_sub_info);
    GmcSubConfigT tmp_g_sub_info;
    tmp_g_sub_info.subsName = (char *)"subVertexLabel";
    tmp_g_sub_info.configJson = g_sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_g_sub_info, g_conn_sub, sn_callback_fullsub, user_data);
    EXPECT_EQ(GMERR_OK, ret);
    if (ret != 0) {
        testGmcGetLastError();
    }
    ret = testWaitSnRecvOneThread(user_data, GMC_SUB_EVENT_INITIAL_LOAD_EOF, 1);
    ASSERT_EQ(GMERR_OK, ret);
    // 写数据
    for (i = 0; i < g_data_num; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
        ret = testGmcPrepareStmtByLabelName(g_stmt_sync, g_label_name, GMC_OPERATION_INSERT);
        ASSERT_EQ(GMERR_OK, ret);
        test_setVertexProperty(g_stmt_sync, i);
        ret = GmcExecute(g_stmt_sync);
        ASSERT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(g_stmt_sync, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        ASSERT_EQ(GMERR_OK, ret);

        ASSERT_EQ(1, affectRows);
    }
    ret = testWaitSnRecvOneThread(user_data, GMC_SUB_EVENT_INSERT, g_data_num);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testSubDisConnect(g_conn_sub, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

const char *g_namespace = (const char *)"userA";
const char *g_namespace2 = (const char *)"userB";
char *g_namespace_userName = (char *)"abc";
char kvTableName[128] = "student";
char g_tableName[128] = "KV3";
char key[] = "zhangsan";
uint32_t value = 30;
char key1[] = "lisi";
uint32_t value1 = 40;
// 异步单线程新建命名空间，同时创建一张kv表DML
TEST_F(OneThreadAsync06, Connect_008_026)
{
    int ret = 0;
    ret = GmcCreateNamespaceAsync(g_stmt_async, g_namespace, g_namespace_userName, create_namespace_callback, &asyncUserData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &asyncUserData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    ret = GmcKvCreateTableAsync(g_stmt_async, kvTableName, normal_config_json, create_kv_table_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);

    // 打开kv表
    ret = GmcKvPrepareStmtByLabelName(g_stmt_async, kvTableName);
    EXPECT_EQ(GMERR_OK, ret);
	// kv表中插入数据--张三的信息
    GmcKvTupleT kvInfo = { 0 };
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key);
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSetAsync(g_stmt_async, &kvInfo, set_kv_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);

    ret = GmcKvRemoveAsync(g_stmt_async, key, strlen(key), delete_kv_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);

    ret = GmcKvDropTableAsync(g_stmt_async, kvTableName, drop_kv_table_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
}

void TestSetVertexPKPropertyValue(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t f0_value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_INT64, &f0_value, sizeof(int64_t));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestSetVertexPropertyValue(GmcStmtT *stmt, int i, bool bool_value, char *f14_value)
{
    int ret = 0;

    uint64_t f1_value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT32, &f2_value, sizeof(int32_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value, sizeof(uint32_t));
    ASSERT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &f4_value, sizeof(int16_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value, sizeof(uint16_t));
    ASSERT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT8, &f6_value, sizeof(int8_t));
    ASSERT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value, sizeof(uint8_t));
    ASSERT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value, sizeof(bool));
    ASSERT_EQ(GMERR_OK, ret);

    float f9_value = 9 * i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value, sizeof(float));
    ASSERT_EQ(GMERR_OK, ret);

    double f10_value = 10 * i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value, sizeof(double));
    ASSERT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_TIME, &f11_value, sizeof(uint64_t));
    ASSERT_EQ(GMERR_OK, ret);

    char f12_value = 12 * i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value, sizeof(char));
    ASSERT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value, sizeof(unsigned char));
    ASSERT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, f14_value, (strlen(f14_value)));
    ASSERT_EQ(GMERR_OK, ret);
}

void TestQueryVertexProperty(GmcStmtT *stmt, int index, bool bool_value, char *f14_value)
{
    int ret = 0;
    uint64_t f1_value = 1 * index;
    ret = queryPropertyAndCompare(stmt, "F1", GMC_DATATYPE_UINT64, &f1_value);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t f2_value = 2 * index;
    ret = queryPropertyAndCompare(stmt, "F2", GMC_DATATYPE_INT32, &f2_value);
    EXPECT_EQ(GMERR_OK, ret);

    uint32_t f3_value = 3 * index;
    ret = queryPropertyAndCompare(stmt, "F3", GMC_DATATYPE_UINT32, &f3_value);
    EXPECT_EQ(GMERR_OK, ret);

    int16_t f4_value = 4 * index;
    ret = queryPropertyAndCompare(stmt, "F4", GMC_DATATYPE_INT16, &f4_value);
    EXPECT_EQ(GMERR_OK, ret);

    uint16_t f5_value = 5 * index;
    ret = queryPropertyAndCompare(stmt, "F5", GMC_DATATYPE_UINT16, &f5_value);
    EXPECT_EQ(GMERR_OK, ret);

    int8_t f6_value = 6 * index;
    ret = queryPropertyAndCompare(stmt, "F6", GMC_DATATYPE_INT8, &f6_value);
    EXPECT_EQ(GMERR_OK, ret);

    uint8_t f7_value = 7 * index;
    ret = queryPropertyAndCompare(stmt, "F7", GMC_DATATYPE_UINT8, &f7_value);
    EXPECT_EQ(GMERR_OK, ret);

    bool f8_value = bool_value;
    ret = queryPropertyAndCompare(stmt, "F8", GMC_DATATYPE_BOOL, &f8_value);
    EXPECT_EQ(GMERR_OK, ret);

    float f9_value = 9 * index;
    ret = queryPropertyAndCompare(stmt, "F9", GMC_DATATYPE_FLOAT, &f9_value);
    EXPECT_EQ(GMERR_OK, ret);

    double f10_value = 10 * index;
    ret = queryPropertyAndCompare(stmt, "F10", GMC_DATATYPE_DOUBLE, &f10_value);
    EXPECT_EQ(GMERR_OK, ret);

    uint64_t f11_value = 11 * index;
    ret = queryPropertyAndCompare(stmt, "F11", GMC_DATATYPE_TIME, &f11_value);
    EXPECT_EQ(GMERR_OK, ret);

    char f12_value = 12 * index;
    ret = queryPropertyAndCompare(stmt, "F12", GMC_DATATYPE_CHAR, &f12_value);
    EXPECT_EQ(GMERR_OK, ret);

    unsigned char f13_value = 13 * index;
    ret = queryPropertyAndCompare(stmt, "F13", GMC_DATATYPE_UCHAR, &f13_value);
    EXPECT_EQ(GMERR_OK, ret);

    ret = queryPropertyAndCompare(stmt, "F14", GMC_DATATYPE_STRING, f14_value);
    EXPECT_EQ(GMERR_OK, ret);
}

// 异步单线程新建命名空间，同时创建vertex表进行相关DML操作
TEST_F(OneThreadAsync06, Connect_008_027)
{
    int ret = 0;
    ret = GmcCreateNamespaceAsync(g_stmt_async, g_namespace, g_namespace_userName, create_namespace_callback, &asyncUserData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    ret = GmcUseNamespaceAsync(g_stmt_async, g_namespace, use_namespace_callback, &asyncUserData);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);

    const char *strBatchLabelName1 = "KEY_S0";
    const char *strBatchLabelVertexPK1 = "KEY_PK";
    int start_num = 0, end_num = 1024;
    char *test_schema1 = NULL;
    GmcDropVertexLabel(g_stmt_sync, strBatchLabelName1);
    readJanssonFile("schemaFile/batch_insert_dml_general.gmjson", &test_schema1);
    ASSERT_NE((void *)NULL, test_schema1);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, test_schema1, NULL, create_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    free(test_schema1);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, strBatchLabelName1, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;
    GmcBatchRetT batchRet = {};
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufRecycleSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        TestSetVertexPKPropertyValue(g_stmt_async, i);
        TestSetVertexPropertyValue(g_stmt_async, i, 0, (char *)"string");
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    EXPECT_EQ(1, asyncUserData.totalNum);
    EXPECT_EQ(1, asyncUserData.succNum);
    // 更新顶点
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, strBatchLabelName1, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        TestSetVertexPropertyValue(g_stmt_async, 2 * i, 1, (char *)"string2");
        ret = GmcSetIndexKeyName(g_stmt_async, strBatchLabelVertexPK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    EXPECT_EQ(1, asyncUserData.totalNum);
    EXPECT_EQ(1, asyncUserData.succNum);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, strBatchLabelName1, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = start_num; i < end_num; i++) {
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, strBatchLabelVertexPK1);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    EXPECT_EQ(1, asyncUserData.totalNum);
    EXPECT_EQ(1, asyncUserData.succNum);
    ret = GmcBatchDestroy(batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabelAsync(g_stmt_async, strBatchLabelName1, drop_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    ret = GmcDropNamespaceAsync(g_stmt_async, g_namespace, drop_namespace_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
}

static const char *gLabelName002 = "ResVertexLabel002";
static const char *gLableConfig002 = R"({"max_record_num":100000})";
static const char *gLabelSchemaJson002 =
    R"([{
        "type":"record",
        "name":"ResVertexLabel002",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"resource", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"ResVertexLabel002",
                    "name":"PK",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                }
            ]
        }])";
static const char *gResPoolName = "ResourcePool001";
static const uint64_t gResPoolId = 0;
static const uint64_t gResStartId = 0;
static const uint64_t gResCapacity = 1000000;
static const char *gResPoolConfigJson =
    R"({
        "name" : "ResourcePool001",
        "pool_id" : 65535,
        "start_id" : 0,
        "capacity" : 1000000,
        "order" : 0,
        "alloc_type": 0
    })";
static const char *gResPoolExtendedName = "resource_pool_extended";
static const char *gResPoolExternal =
    R"({
        "name" : "resource_pool_extended",
        "pool_id" : 65535,
        "start_id" : 1,
        "capacity" : 2000,
        "order" : 0,
        "alloc_type" : 0
    })";

void ResourceAsyncInsertCb(
    void *userData, uint32_t affectedRows, GmcResourceInfoT *resInfo, int32_t status, const char *errMsg)
{
    int ret = 0;
    for (uint32_t i = 0; i < resInfo->resIdNum; i++) {
        uint16_t count = 0;
        uint32_t startIndex = 0;
        ret = GmcGetCountResource(resInfo->resIdBuf[i], &count);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStartIdxResource(resInfo->resIdBuf[i], &startIndex);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(1, count);
        EXPECT_EQ(0, startIndex);
    }

    // 原有的回调
    AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
    user_data->status = status;
    user_data->affectRows = affectedRows;
    user_data->recvNum++;
}

// 异步单线程创建资源表，资源池，绑定资源池到资源表，绑定资源池到资源字段，绑定拓展资源池到资源池，删除资源表数据，资源池解绑，删除资源池
TEST_F(OneThreadAsync06, Connect_008_028)
{
    int ret = 0;
    char *test_schema1 = NULL;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    GmcDropVertexLabel(g_stmt_sync, gLabelName002);
    GmcDestroyResPool(g_stmt_sync, gResPoolName);
    GmcDestroyResPool(g_stmt_sync, gResPoolExtendedName);
    ret = GmcCreateVertexLabelAsync(g_stmt_async, gLabelSchemaJson002,
                                    gLableConfig002, create_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
    free(test_schema1);
    ret = GmcCreateResPool(g_stmt_sync, gResPoolConfigJson);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcCreateResPool(g_stmt_sync, gResPoolExternal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindResPoolToLabel(g_stmt_sync, gResPoolName, gLabelName002);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBindExtResPool(g_stmt_sync, gResPoolName, gResPoolExtendedName);
    EXPECT_EQ(GMERR_OK, ret);

    int32_t pkVal = 10;
    uint64_t resId = 2;
    ret = GmcSetPoolIdResource(AUTO_POOL_ID, &resId);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetCountResource(1, &resId);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(AUTO_START_IDX, &resId);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, gLabelName002, GMC_OPERATION_INSERT_WITH_RESOURCE);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcSetVertexProperty(g_stmt_async, "F0", GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F1", GMC_DATATYPE_INT32, &pkVal, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt_async, "F2", GMC_DATATYPE_RESOURCE, &resId, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);

    GmcAsyncRequestDoneContextT insertResourceRequestCtx;
    insertResourceRequestCtx.insertWithResourceCb = ResourceAsyncInsertCb;
    insertResourceRequestCtx.userData = &asyncUserData;
    ret = GmcExecuteAsync(g_stmt_async, &insertResourceRequestCtx); // 回调函数中为什么count为0？
    EXPECT_EQ(GMERR_OK, ret);

    ret = testWaitAsyncRecvOneThread(&asyncUserData, 1);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);

    ret = testGmcPrepareStmtByLabelName(g_stmt_sync, gLabelName002, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt_sync, 0, GMC_DATATYPE_INT32, &pkVal, sizeof(pkVal));
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(g_stmt_sync, "PK");
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcExecute(g_stmt_sync);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnbindResPoolFromLabel(g_stmt_sync, gLabelName002);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcUnbindExtResPool(g_stmt_sync, gResPoolName);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcDestroyResPool(g_stmt_sync, gResPoolName);
    ASSERT_EQ(GMERR_OK, ret);
    ret = GmcDestroyResPool(g_stmt_sync, gResPoolExtendedName);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabelAsync(g_stmt_async, gLabelName002, drop_vertex_label_callback, &asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserData);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserData.status);
}


// 异步单线程结构化简单批量DML操作
TEST_F(OneThreadAsync06, Connect_008_029)
{
    int ret = 0;
    // 创建简单表(单层 无变长字段)
    readJanssonFile("schemaFile/struct_table_001.gmjson", &g_schema_struct);
    EXPECT_NE((void *)NULL, g_schema_struct);
    GmcDropVertexLabel(g_stmt_sync, g_table_name);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema_struct, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema_struct);

    // 创建简单表(单层 有变长字段)
    readJanssonFile("schemaFile/struct_table_002.gmjson", &g_schema_struct);
    EXPECT_NE((void *)NULL, g_schema_struct);
    GmcDropVertexLabel(g_stmt_sync, g_table_name2);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema_struct, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema_struct);

    // 创建简单表(单层 无变长字段,主键多字段)
    readJanssonFile("schemaFile/struct_table_004.gmjson", &g_schema_struct);
    EXPECT_NE((void *)NULL, g_schema_struct);
    GmcDropVertexLabel(g_stmt_sync, g_table_name4);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema_struct, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema_struct);
    
    g_data_num = 102400;
#ifdef ENV_RTOSV2X
    g_data_num = 1024;
#endif
    char expOut[1024] = {0};
    sprintf(expOut, "index = %d", g_data_num - 1);
    memset(g_command, 0, sizeof(g_command));
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -ns %s", g_toolPath, g_table_name, g_testNameSpace);

    // 结构化异步单个插入写
    db_test_struct_replace_simple_table_async(g_stmt_async, g_data_num, g_table_name, 1);

    // 主键读
    db_test_read_simple_table(g_stmt_sync, g_data_num, g_table_name, g_pk_name, true);

    // 结构化读
    db_test_struct_read_simple_table(g_stmt_sync, g_data_num, g_table_name, g_pk_name, true);

    // record视图
    ret = executeCommand(g_command, expOut);
    ASSERT_EQ(GMERR_OK, ret);

    // 结构化异步单个覆盖写
    db_test_struct_replace_simple_table_async(g_stmt_async, g_data_num, g_table_name, 2);

    // 主键读
    db_test_read_simple_table(g_stmt_sync, g_data_num, g_table_name, g_pk_name, true);

    // 结构化读
    db_test_struct_read_simple_table(g_stmt_sync, g_data_num, g_table_name, g_pk_name, true);

    // record视图
    ret = executeCommand(g_command, expOut);
    ASSERT_EQ(GMERR_OK, ret);

    // 结构化异步批量删
    db_test_struct_delete_simple_table_batch_async(g_stmt_async, g_data_num, g_table_name, g_pk_name, 1);

    // 主键读
    db_test_read_simple_table(g_stmt_sync, g_data_num, g_table_name, g_pk_name, false);

    // 结构化读
    db_test_struct_read_simple_table(g_stmt_sync, g_data_num, g_table_name, g_pk_name, false);
}
char g_special_complex_table_name[] = "TEST_SC_T1", g_special_complex_table_name2[] = "TEST_SC_T2",
     g_special_complex_table_name3[] = "TEST_SC_T3", g_special_complex_table_name4[] = "TEST_SC_T4",
     g_special_complex_table_name5[] = "TEST_SC_T5", g_special_complex_table_name6[] = "TEST_SC_T6",
     g_special_complex_table_name7[] = "TEST_SC_T7", g_special_complex_table_name8[] = "TEST_SC_T8",
     g_special_complex_table_name9[] = "TEST_SC_T9", g_special_complex_table_name10[] = "TEST_SC_T10";
// 异步单线程结构化特殊复杂表DML
void db_test_struct_write_special_complex_table(
    GmcStmtT *stmt, int data_numee, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1 t1 = (record_T1){0};
    fixed_array_T2 t2[T2_count];
    vector_T3 t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numee; i++) {
        TEST_INFO("STRUCT INSERT", i, 1000, 0);
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(expected_affectRows, affectRows);
    }

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_replace_special_complex_table(
    GmcStmtT *stmt, int data_numee, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1 t1 = (record_T1){0};
    fixed_array_T2 t2[T2_count];
    vector_T3 t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_REPLACE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numee; i++) {
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            EXPECT_EQ(expected_affectRows, affectRows);
        }
    }

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_delete_special_complex_table(
    GmcStmtT *stmt, int data_numee, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numee; i++) {
        test_set_TEST_SC_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
        EXPECT_EQ(GMERR_OK, ret);
        if (expected_affectRows != 10) {
            EXPECT_EQ(expected_affectRows, affectRows);
        }
    }
}

void db_test_struct_write_special_complex_table_batch_async(
    GmcStmtT *stmt, int data_numee, char *label_name, uint16_t T2_count, uint16_t T3_count, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    record_T1 t1 = (record_T1){0};
    fixed_array_T2 t2[T2_count];
    vector_T3 t3[T3_count];
    test_malloc_TEST_SC_T1(&obj, &t1, t2, t3, T2_count, T3_count);
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numee; i++) {
        TEST_INFO("STRUCT BATCH INSERT ASYNC", i, 1000, 0);
        test_set_TEST_SC_T1_value(&obj, i, false, T2_count, T3_count);
        ret = testStructSetVertexWithBuf(stmt, &obj, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }
    AsyncUserDataT asyncUserDataLocal = {0};
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncUserDataLocal);
    ASSERT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(GMERR_OK, asyncUserDataLocal.status);
    EXPECT_EQ(data_numee, asyncUserDataLocal.totalNum);
    EXPECT_EQ(data_numee, asyncUserDataLocal.succNum);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);

    test_free_TEST_SC_T1(&obj, T2_count, T3_count);
}

void db_test_struct_read_special_complex_table(
    GmcStmtT *stmt, int data_numee, char *label_name, uint16_t T2_count, uint16_t T3_count, bool read_num)
{
    int ret, i;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    structTestCtx deseriCtx = (structTestCtx){0};
    GmcDeseriT deseri = (GmcDeseriT){0};
    testStructSetDeseri(stmt, &obj, &deseri, &deseriCtx, false, &labelInfo);

    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numee; i++) {
        TEST_INFO("STRUCT SCAN", i, 1000, 0);
        test_set_TEST_SC_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = testStructGetVertexDeseri(stmt, &deseri);
            EXPECT_EQ(GMERR_OK, ret);
            test_struct_read_TEST_SC_T1(&obj, i, false, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
    deSeriFreeDynMem(&deseriCtx, true);
}

void db_test_read_special_complex_table(
    GmcStmtT *stmt, int data_numee, char *label_name, char *keyName, uint16_t T2_count,
    uint16_t T3_count, bool read_num)
{
    int ret, i;
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_SCAN);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numee; i++) {
        TEST_INFO("SCAN", i, 500, 0);
        int64_t f0_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT64, &f0_value, sizeof(f0_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, keyName);
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
        bool isFinish = false;
        int cnt = 0;
        while (!isFinish) {
            ret = GmcFetch(stmt, &isFinish);
            EXPECT_EQ(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            test_read_special_complex_table(stmt, i, false, label_name, T2_count, T3_count);
            cnt++;
        }
        if (read_num) {
            EXPECT_EQ(1, cnt);
        } else {
            EXPECT_EQ(0, cnt);
        }
    }
}

void db_test_struct_delete_special_complex_table_async(
    GmcStmtT *stmt, int data_numee, char *label_name, int expected_affectRows)
{
    int ret, i, affectRows;
    TEST_SC_T1_struct_t obj = (TEST_SC_T1_struct_t){0};
    TestLabelInfoT labelInfo = {label_name, 0, g_testNameSpace};
    ret = testGmcPrepareStmtByLabelName(stmt, label_name, GMC_OPERATION_DELETE);
    EXPECT_EQ(GMERR_OK, ret);
    for (i = 0; i < data_numee; i++) {
        TEST_INFO("STRUCT DELETE ASYNC", i, 1000, 0);
        test_set_TEST_SC_T1_primary_key(&obj, i);
        ret = testStructSetIndexKeyWithBuf(stmt, &obj, 0, NULL, &labelInfo);
        EXPECT_EQ(GMERR_OK, ret);
        AsyncUserDataT asyncUserDataLocal = {0};
        GmcAsyncRequestDoneContextT deleteRequestCtx;
        deleteRequestCtx.insertCb = delete_vertex_callback;
        deleteRequestCtx.userData = &asyncUserDataLocal;
        ret = GmcExecuteAsync(stmt, &deleteRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserDataLocal.status);
        EXPECT_EQ(expected_affectRows, asyncUserDataLocal.affectRows);
    }
}

TEST_F(OneThreadAsync06, Connect_008_030)
{
    int ret = 0;
    // 创建特殊复杂表(多层 有变长字段)
    readJanssonFile("schemaFile/special_complex_table_001.gmjson", &g_schema_struct);
    EXPECT_NE((void *)NULL, g_schema_struct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_special_complex_table_name);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema_struct, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema_struct);
    const uint16_t T2_count = 10, T3_count = 10;
#if defined ENV_RTOSV2
    int g_data_num2 = 30;
#else
    int g_data_num2 = 500;
#endif
    // 结构化写
    db_test_struct_write_special_complex_table_batch_async(
        g_stmt_async, g_data_num2, g_special_complex_table_name, T2_count, T3_count, 1);

    // 普通读
    db_test_read_special_complex_table(
        g_stmt_sync, g_data_num2, g_special_complex_table_name, g_pk_name, T2_count, T3_count, true);

    // 结构化读
    db_test_struct_read_special_complex_table(
        g_stmt_sync, g_data_num2, g_special_complex_table_name, T2_count, T3_count, true);

    // 结构化删
    db_test_struct_delete_special_complex_table_async(g_stmt_async, g_data_num2, g_special_complex_table_name, 1);

    // 普通读
    db_test_read_special_complex_table(
        g_stmt_sync, g_data_num2, g_special_complex_table_name, g_pk_name, T2_count, T3_count, false);

    // 结构化读
    db_test_struct_read_special_complex_table(
        g_stmt_sync, g_data_num2, g_special_complex_table_name, T2_count, T3_count, false);
}
void sn_callback_struct(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret;
    int index, i;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen = MAX_NAME_LENGTH;
    void *keyValue = 0, *conn_sync = 0, *stmt_sync = 0;
    char keyName[MAX_NAME_LENGTH] = {0};
    SnUserDataT *user_data = (SnUserDataT *)userData;
    const uint16_t T2_count = 10, T3_count = 10;
    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        if (ret != GMERR_OK && info->eventType == GMC_SUB_EVENT_INITIAL_LOAD_EOF) {
            printf("[info]GMC_SUB_EVENT_INITIAL_LOAD_EOF\n");
            user_data->scanEofNum++;
            break;
        } else if (eof) {
            break;
        }

        for (i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            EXPECT_EQ(GMERR_OK, ret);
            EXPECT_EQ(strlen(labelName), labelNameLen);

            // 默认推送new object和old object
            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_INSERT", index, 1000, 0);
                    GmcNodeT *root;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    bool isNull = 0;
                    int64_t F0Value = 0;
                    ret = GmcNodeGetPropertyByName(root, "F0", &F0Value, sizeof(int64_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "f0 = %d", F0Value);

                    ASSERT_EQ((unsigned int)0, isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    test_read_special_complex_table(
                        subStmt, F0Value, false, g_special_complex_table_name, T2_count, T3_count);
                    break;
                }
                case GMC_SUB_EVENT_DELETE: {
                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_DELETE", index, 1000, 0);
                    GmcNodeT *root;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    bool isNull = 0;
                    int64_t F0Value = 0;
                    
                    ret = GmcNodeGetPropertyByName(root, "F0", &F0Value, sizeof(int64_t), &isNull);
                    AW_FUN_Log(LOG_INFO, "f0 = %d", F0Value);
                    EXPECT_EQ(GMERR_OK, ret);
                    ASSERT_EQ((unsigned int)0, isNull);
                    test_read_special_complex_table(
                        subStmt, F0Value, false, g_special_complex_table_name, T2_count, T3_count);
                    break;
                }
                case GMC_SUB_EVENT_UPDATE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_UPDATE new_value", index, 1000, 0);

                    // 读old
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->old_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_UPDATE old_value", index, 1000, 0);

                    break;
                }
                case GMC_SUB_EVENT_REPLACE: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    TEST_INFO("GMC_SUB_EVENT_REPLACE new_value", index, 1000, 0);
                    GmcNodeT *root;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    bool isNull = 0;
                    int64_t F0Value = 0;
                    ret = GmcNodeGetPropertyByName(root, "F0", &F0Value, sizeof(int64_t), &isNull);
                    ASSERT_EQ((unsigned int)0, isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    AW_FUN_Log(LOG_INFO, "f0 = %d", F0Value);
                    ASSERT_EQ((unsigned int)0, isNull);
                    test_read_special_complex_table(
                        subStmt, F0Value, false, g_special_complex_table_name, T2_count, T3_count);

                    // 读old
                    if (((bool *)user_data->isReplace_insert)[user_data->subIndex]) {
                        TEST_INFO("GMC_SUB_EVENT_REPLACE insert old_value", index, 1000, 0);
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        GmcNodeT *root;
                        ret = GmcGetRootNode(subStmt, &root);
                        EXPECT_EQ(GMERR_OK, ret);
                        bool isNull = 0;
                        int64_t F0Value = 0;
                        ret = GmcNodeGetPropertyByName(root, "F0", &F0Value, sizeof(int64_t), &isNull);
                        ASSERT_EQ((unsigned int)0, isNull);
                        EXPECT_EQ(GMERR_OK, ret);
                        test_read_special_complex_table(
                            subStmt, F0Value, false, g_special_complex_table_name, T2_count, T3_count);
                    } else {
                        ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_OLD);
                        EXPECT_EQ(GMERR_OK, ret);
                        index = ((int *)user_data->old_value)[user_data->subIndex];
                        TEST_INFO("GMC_SUB_EVENT_REPLACE update old_value", index, 1000, 0);
                    }
                    break;
                }
                case GMC_SUB_EVENT_INITIAL_LOAD: {
                    // 读new
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    EXPECT_EQ(GMERR_OK, ret);
                    index = ((int *)user_data->new_value)[user_data->subIndex];
                    printf("[NEW/OLD OBJECT] GMC_SUB_EVENT_REPLACE new_value is %d\r\n", index);
                    GmcNodeT *root;
                    ret = GmcGetRootNode(subStmt, &root);
                    EXPECT_EQ(GMERR_OK, ret);
                    bool isNull = 0;
                    int64_t F0Value = 0;
                    ret = GmcNodeGetPropertyByName(root, "F0", &F0Value, sizeof(int64_t), &isNull);
                    EXPECT_EQ(GMERR_OK, ret);
                    ASSERT_EQ((unsigned int)0, isNull);

                    AW_FUN_Log(LOG_INFO, "f0 = %d", F0Value);
                    test_read_special_complex_table(
                        subStmt, F0Value, false, g_special_complex_table_name, T2_count, T3_count);
                    break;
                }
                default: {
                    printf("default: invalid eventType\r\n");
                    break;
                }
            }
            break;
        }
        user_data->subIndex++;
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                user_data->insertNum++;
                break;
            }
            case GMC_SUB_EVENT_DELETE: {
                user_data->deleteNum++;
                break;
            }
            case GMC_SUB_EVENT_UPDATE: {
                user_data->updateNum++;
                break;
            }
            case GMC_SUB_EVENT_REPLACE: {
                user_data->replaceNum++;
                break;
            }
            case GMC_SUB_EVENT_KV_SET: {
                user_data->kvSetNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD: {
                user_data->scanNum++;
                break;
            }
            case GMC_SUB_EVENT_INITIAL_LOAD_EOF: {
                user_data->scanEofNum++;
                break;
            }
            case GMC_SUB_EVENT_AGED: {
                user_data->agedNum++;
                break;
            }
        }
    }
}

// 用例core在GMC_SUB_EVENT_REPLACE读old上
TEST_F(OneThreadAsync06, Connect_008_025)
{
    int ret, i;
    int userDataIdx = 0;
#if defined ENV_RTOSV2
    int g_data_num2 = 10;
#else
    int g_data_num2 = 100;
#endif
    const uint16_t T2_count = 10, T3_count = 10;
    for (i = 0; i < g_data_num2; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }
    readJanssonFile("schemaFile/special_complex_table_001.gmjson", &g_schema_struct);
    EXPECT_NE((void *)NULL, g_schema_struct);
    ret = GmcDropVertexLabel(g_stmt_sync, g_special_complex_table_name);
    ret = GmcCreateVertexLabel(g_stmt_sync, g_schema_struct, NULL);
    EXPECT_EQ(GMERR_OK, ret);
    free(g_schema_struct);
    // 结构化写
    db_test_struct_write_special_complex_table(
        g_stmt_sync, g_data_num2, g_special_complex_table_name, T2_count, T3_count, 1);

    // 结构化读
    db_test_struct_read_special_complex_table(
        g_stmt_sync, g_data_num2, g_special_complex_table_name, T2_count, T3_count, true);

    // 创建订阅连接
    GmcConnT *g_subChan = NULL;
    YangConnOptionT connOptions = {0};
    connOptions.isOneThreadEpoll = true;
    connOptions.epollRegType = TEST_EPOLLREG_WITH_USD;
    connOptions.epollRegWithUsDFunc = g_epollRegInfoOneThread;
    connOptions.epollFd = &g_epollDataOneThread.userEpollFd;
    connOptions.connName = g_subConnName;
    ret = TestYangGmcConnect(&g_subChan, NULL, GMC_CONN_TYPE_SUB, &connOptions);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile("schemaFile/simple_schema_subinfo2.gmjson", &sub_info);
    ASSERT_NE((void *)NULL, sub_info);

    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = g_subName;
    tmp_sub_info.configJson = sub_info;
    ret = GmcSubscribe(g_stmt_sync, &tmp_sub_info, g_subChan, sn_callback_struct, user_data);
    ASSERT_EQ(GMERR_OK, ret);
    free(sub_info);

    // 等待全量事件推送完成
    ret = testWaitSnRecvOneThread(user_data, GMC_SUB_EVENT_INITIAL_LOAD, g_data_num2);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < g_data_num2; i++) {
        ((int *)(user_data->new_value))[userDataIdx] = i;
        userDataIdx++;
    }
    // 结构化replace
    db_test_struct_replace_special_complex_table(
        g_stmt_sync, g_data_num2, g_special_complex_table_name, T2_count, T3_count, 2);

    // 等待replace事件推送完成
    ret = testWaitSnRecvOneThread(user_data, GMC_SUB_EVENT_REPLACE, g_data_num2);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 0; i < g_data_num2; i++) {
        ((int *)(user_data->old_value))[userDataIdx] = i;
        userDataIdx++;
    }

    // 结构化删
    db_test_struct_delete_special_complex_table(g_stmt_sync, g_data_num2, g_special_complex_table_name, 1);

    // 等待delete事件推送完成
    ret = testWaitSnRecvOneThread(user_data, GMC_SUB_EVENT_DELETE, g_data_num2);
    EXPECT_EQ(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt_sync, g_subName);
    ASSERT_EQ(GMERR_OK, ret);

    ret = testSubDisConnect(g_subChan, g_stmt_sub);
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_PK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    uint32_t F7Value = i;
    ret = GmcSetVertexProperty(stmt, "F7", GMC_DATATYPE_UINT32, &F7Value, sizeof(uint32_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty_SK(GmcStmtT *stmt, int i)
{
    int ret = 0;
    int64_t F9Value = i;
    ret = GmcSetVertexProperty(stmt, "F9", GMC_DATATYPE_INT64, &F9Value, sizeof(int64_t));
    EXPECT_EQ(GMERR_OK, ret);
}

void set_VertexProperty(GmcStmtT *stmt, int i)
{
    int ret = 0;
    char F0Value = i;
    ret = GmcSetVertexProperty(stmt, "F0", GMC_DATATYPE_CHAR, &F0Value, sizeof(char));
    EXPECT_EQ(GMERR_OK, ret);
    unsigned char F1Value = i;
    ret = GmcSetVertexProperty(stmt, "F1", GMC_DATATYPE_UCHAR, &F1Value, sizeof(unsigned char));
    EXPECT_EQ(GMERR_OK, ret);
    int8_t F2Value = i;
    ret = GmcSetVertexProperty(stmt, "F2", GMC_DATATYPE_INT8, &F2Value, sizeof(int8_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint8_t F3Value = i;
    ret = GmcSetVertexProperty(stmt, "F3", GMC_DATATYPE_UINT8, &F3Value, sizeof(uint8_t));
    EXPECT_EQ(GMERR_OK, ret);
    int16_t F4Value = i;
    ret = GmcSetVertexProperty(stmt, "F4", GMC_DATATYPE_INT16, &F4Value, sizeof(int16_t));
    EXPECT_EQ(GMERR_OK, ret);
    uint16_t F5Value = i;
    ret = GmcSetVertexProperty(stmt, "F5", GMC_DATATYPE_UINT16, &F5Value, sizeof(uint16_t));
    EXPECT_EQ(GMERR_OK, ret);
    int32_t F6Value = i;
    ret = GmcSetVertexProperty(stmt, "F6", GMC_DATATYPE_INT32, &F6Value, sizeof(int32_t));
    EXPECT_EQ(GMERR_OK, ret);
    bool F8Value = false;
    ret = GmcSetVertexProperty(stmt, "F8", GMC_DATATYPE_BOOL, &F8Value, sizeof(bool));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F10Value = i;
    ret = GmcSetVertexProperty(stmt, "F10", GMC_DATATYPE_UINT64, &F10Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    float F11Value = i;
    ret = GmcSetVertexProperty(stmt, "F11", GMC_DATATYPE_FLOAT, &F11Value, sizeof(float));
    EXPECT_EQ(GMERR_OK, ret);
    double F12Value = i;
    ret = GmcSetVertexProperty(stmt, "F12", GMC_DATATYPE_DOUBLE, &F12Value, sizeof(double));
    EXPECT_EQ(GMERR_OK, ret);
    uint64_t F13Value = i;
    ret = GmcSetVertexProperty(stmt, "F13", GMC_DATATYPE_TIME, &F13Value, sizeof(uint64_t));
    EXPECT_EQ(GMERR_OK, ret);
    char F14Value[] = "testver";
    ret = GmcSetVertexProperty(stmt, "F14", GMC_DATATYPE_STRING, F14Value, (strlen(F14Value)));
    EXPECT_EQ(GMERR_OK, ret);
    char F15Value[12] = "12";
    ret = GmcSetVertexProperty(stmt, "F15", GMC_DATATYPE_BYTES, F15Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
    char F16Value[12] = "13";
    ret = GmcSetVertexProperty(stmt, "F16", GMC_DATATYPE_FIXED, F16Value, 12);
    EXPECT_EQ(GMERR_OK, ret);
}

void update_vertex_timeout_callback(void *userData, uint32_t affectedRows, int32_t status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->affectRows = affectedRows;
        user_data->historyRecvNum++;
        if (user_data->lastError != NULL) {
            int ret = strcmp(user_data->lastError, errMsg);
            EXPECT_EQ(GMERR_OK, ret);
        }
        EXPECT_EQ(GMERR_REQUEST_TIME_OUT, status);
        printf("status : %d, user_data->recvNum : %d, affectedRows : %d\n", status, user_data->recvNum, affectedRows);
        user_data->recvNum++;
    }
}
// 异步单线程开启epoll后关闭epoll对象，insert，update操作[业务epoll和超时epoll分开，wait超时的epollfd]

TEST_F(OneThreadAsync06, Connect_008_032)
{
    int ret = 0;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    void *vertexLabel1 = NULL;
    readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
    ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    GmcDropVertexLabel(g_stmt_sync, g_normal_vertexlabel_name);
    ret = GmcCreateVertexLabel(g_stmt_sync, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // insert Vertex
    AsyncUserDataT asyncUserDataLocal = {0};
    int insert_num = 50;
    for (int i = 0; i < insert_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_PK(g_stmt_async, i);
        set_VertexProperty_SK(g_stmt_async, i + 1000);
        set_VertexProperty(g_stmt_async, i);
        asyncUserDataLocal = {0};
        GmcAsyncRequestDoneContextT insertRequestCtx;
        insertRequestCtx.mergeCb = insert_vertex_callback;
        insertRequestCtx.userData = &asyncUserDataLocal;
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
        ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal);
        EXPECT_EQ(GMERR_OK, ret);
        EXPECT_EQ(GMERR_OK, asyncUserDataLocal.status);
        EXPECT_EQ(1, asyncUserDataLocal.affectRows);
    }

    asyncUserDataLocal = {0};
    GmcAsyncRequestDoneContextT updateRequestCtx;
    updateRequestCtx.updateCb = update_vertex_timeout_callback;
    updateRequestCtx.userData = &asyncUserDataLocal;
    // update data
    for (uint32_t i = 0; i < insert_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_UPDATE);
        EXPECT_EQ(GMERR_OK, ret);
        if (i == 0) {
            closeUsrEpollOneThread(&g_epollDataOneThread);
            GmcStopHeartbeat(g_conn_sync);
        }
        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt_async, i + 10000);
        set_VertexProperty(g_stmt_async, i + 1000);
        ret = GmcExecuteAsync(g_stmt_async, &updateRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal,insert_num);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcDropVertexLabel(g_stmt_sync, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createEpollOneThread();
    EXPECT_EQ(GMERR_OK, ret);
    free(normal_vertexlabel_schema);
}
void create_vertex_label_timeout_callback(void *userData, int32_t status, const char *errMsg)
{
    if (userData) {
        AsyncUserDataT *user_data = (AsyncUserDataT *)userData;
        user_data->status = status;
        user_data->historyRecvNum++;
        if (user_data->lastError != NULL) {
            int ret = strcmp(user_data->lastError, errMsg);
            EXPECT_EQ(GMERR_OK, ret);
        }
        user_data->recvNum++;
        EXPECT_EQ(GMERR_REQUEST_TIME_OUT, status);
        printf("status : %d, user_data->recvNum : %d\n", status, user_data->recvNum);
    }
}

// 异步单线程开启epoll后关闭epoll对象 DDL操作
TEST_F(OneThreadAsync06, Connect_008_031)
{
    int ret = 0;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AsyncUserDataT asyncUserDataLocal = {0};
    readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
    ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    GmcDropVertexLabel(g_stmt_sync, g_normal_vertexlabel_name);
    // 不接受来自服务端的消息, 创建label发送给服务端，其实已成功
    closeUsrEpollOneThread(&g_epollDataOneThread);
    GmcStopHeartbeat(g_conn_sync);
    ret = GmcCreateVertexLabelAsync(
        g_stmt_async, normal_vertexlabel_schema, normal_config_json, create_vertex_label_timeout_callback, &asyncUserDataLocal);
    EXPECT_EQ(GMERR_OK, ret);
    free(normal_vertexlabel_schema);
    ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal, 1, true, -1, g_timeoutEpollData.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createEpollOneThread();
    EXPECT_EQ(GMERR_OK, ret);
}

// 异步单线程开启epoll后关闭epoll对象 batch操作
TEST_F(OneThreadAsync06, Connect_008_033)
{
    int ret = 0;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    readJanssonFile("schemaFile/NormalVertexLabel.gmjson", &normal_vertexlabel_schema);
    ASSERT_NE((void *)NULL, normal_vertexlabel_schema);
    GmcDropVertexLabel(g_stmt_sync, g_normal_vertexlabel_name);
    ret = GmcCreateVertexLabel(g_stmt_sync, normal_vertexlabel_schema, normal_config_json);
    EXPECT_EQ(GMERR_OK, ret);

    // batch merge Vertex
    int insert_num = 50;
    AsyncUserDataT asyncUserDataLocal = {0};
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    EXPECT_EQ(GMERR_OK, ret);
    GmcBatchT *batch = NULL;

    ret = GmcBatchPrepare(g_conn_async, &batchOption, &batch);
    EXPECT_EQ(GMERR_OK, ret);
    for (uint32_t i = 0; i < insert_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_normal_vertexlabel_name, GMC_OPERATION_MERGE);
        EXPECT_EQ(GMERR_OK, ret);
        if (i == 0) {
            closeUsrEpollOneThread(&g_epollDataOneThread);
            GmcStopHeartbeat(g_conn_sync);
        }

        ret = GmcSetIndexKeyValue(g_stmt_async, 0, GMC_DATATYPE_UINT32, &i, sizeof(uint32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(g_stmt_async, "T39_K0");
        EXPECT_EQ(GMERR_OK, ret);
        set_VertexProperty_SK(g_stmt_async, i);
        set_VertexProperty(g_stmt_async, i);
        ret = GmcBatchAddDML(batch, g_stmt_async);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = GmcBatchExecuteAsync(batch, batch_execute_callback, &asyncUserDataLocal);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal, 1, true, -1, g_timeoutEpollData.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);

    GmcBatchDestroy(batch);

    ret = GmcDropVertexLabel(g_stmt_sync, g_normal_vertexlabel_name);
    EXPECT_EQ(GMERR_OK, ret);
    ret = createEpollOneThread();
    EXPECT_EQ(GMERR_OK, ret);
    free(normal_vertexlabel_schema);
}

// 异步单线程只发送10W条消息，60S后再接收，数据全部接收完后，再插入一条数据，并马上接受
// 欧拉缓冲区默认64K大小，客户端向服务端通道写满后，客户端会等待空位，服务端不消费则每2s丢掉队列首端信息
// HPE环境不会阻塞，发送的时候就会16002
TEST_F(OneThreadAsync06, Connect_008_035)
{
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    int ret = 0;
    void *vertexLabel1 = NULL;
    AsyncUserDataT asyncUserDataLocal = {0};
    int insert_num = 15000;
    uint32_t priK;
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback; // 回调校验返回状态
    insertRequestCtx.userData = &asyncUserDataLocal;
    int i = 0;
    while (insert_num > 0 && !ret) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        priK = i;
        test_setVertexProperty(g_stmt_async, priK);
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        insert_num--;
        i++;
    }
#if defined(ENV_RTOSV2X)
    if (ret == GMERR_OK || ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
        EXPECT_EQ(0, 0);
    }
#else
        EXPECT_EQ(GMERR_CONNECTION_SEND_BUFFER_FULL, ret); // 写到客户端发送队列满为止
#endif
        printf("[insert] %d\n", i);
   
#if defined(ENV_RTOSV2X)
    ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal, i-1, true, 7500); // 等待接收服务端将客户端发送成功的消息处理完发送回来
    EXPECT_EQ(GMERR_OK, ret);
#else
    ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal, i-1, true, 7500);
    EXPECT_EQ(GMERR_OK, ret);
#endif
    ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);
    priK = i;
    test_setVertexProperty(g_stmt_async, priK);
    ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal, 1); // 上面消息
    EXPECT_EQ(GMERR_OK, ret);
}
// 异步单线程只发送1W条消息，60S后再接收，数据全部接收完后，再插入一条数据，并马上接受
// 改为使用100
TEST_F(OneThreadAsync06, Connect_008_034)
{
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    // 超时epoll生效
    AddWhiteList(GMERR_INTERNAL_ERROR);
    int ret = 0;
    void *vertexLabel1 = NULL;
    // insert Vertex
    AsyncUserDataT asyncUserDataLocal = {0};
    int insert_num = 100;
    uint32_t priK;
    GmcAsyncRequestDoneContextT insertRequestCtx;
    insertRequestCtx.insertCb = insert_vertex_callback;
    insertRequestCtx.userData = &asyncUserDataLocal;
    int i;
    for (i = 0;i < insert_num; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        priK = i;
        test_setVertexProperty(g_stmt_async, priK);
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
#if defined(ENV_RTOSV2X) || defined(ENV_RTOSV2)
        if (ret == GMERR_OK || ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
            EXPECT_EQ(0, 0);
        }
        if (ret == GMERR_CONNECTION_SEND_BUFFER_FULL) {
            break;
        }
#else
        EXPECT_EQ(GMERR_OK, ret);
#endif
        printf("[insert] %d\n", i);
    }
    sleep(60);
#if defined(ENV_RTOSV2X) || defined(ENV_RTOSV2)
    ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal, i, true, 200);
    EXPECT_EQ(GMERR_OK, ret);  // 服务端当前不会丢信息
#else
    ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal, insert_num, true, 200); // insert_num预期的数据数量需要看报文大小
    EXPECT_EQ(0, ret);
#endif
    for (int i = 10000; i < 10001; i++) {
        ret = testGmcPrepareStmtByLabelName(g_stmt_async, g_label_name, GMC_OPERATION_INSERT);
        EXPECT_EQ(GMERR_OK, ret);
        priK = i;
        test_setVertexProperty(g_stmt_async, priK);
        ret = GmcExecuteAsync(g_stmt_async, &insertRequestCtx);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testWaitAsyncRecvOneThread(&asyncUserDataLocal, 1);
    EXPECT_EQ(GMERR_OK, ret);
}

#define MAX_CONN MAX_ASYNC_CONN_SIZE_PER_PRO
// 满连接alloc成功，prepare失败，关闭一个连接，prepare成功
TEST_F(OneThreadAsync06, Connect_008_036)
{
    int ret;
    GmcConnT *conn[MAX_CONN + 2] = {0};
    GmcStmtT *stmt[MAX_CONN + 2] = {0};
    int i;
    int value = 1;
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    value = existConnNum;
#endif
    for (i = 0; i < MAX_CONN - value; i++) {
        ret = testGmcConnect(&conn[i], &stmt[i], GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
        EXPECT_EQ(GMERR_OK, ret);
        printf("connect success: %d\n", i);
    }

    ret = testGmcConnect(&conn[MAX_CONN - value], &stmt[MAX_CONN - value],
                         GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    // 受业务连接影响不再校验最后一次释放成功
    // alloc失败
    ret = testGmcPrepareStmtByLabelName(stmt[MAX_CONN - value], g_label_name, GMC_OPERATION_INSERT);
    EXPECT_NE(GMERR_OK, ret);
#if defined(ENV_RTOSV2X)
    ret = testGmcDisconnect(conn[0], stmt[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn[1], stmt[1]);
    EXPECT_EQ(GMERR_OK, ret);
    usleep(5000000);
#else
    ret = testGmcDisconnect(conn[0], stmt[0]);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn[1], stmt[1]);
    EXPECT_EQ(GMERR_OK, ret);
    usleep(1000000);
#endif
    if (conn[MAX_CONN - value] != NULL) {
        ret = testGmcDisconnect(conn[MAX_CONN - value], stmt[MAX_CONN - value]);
        EXPECT_EQ(GMERR_OK, ret);
    }
    ret = testGmcConnect(&conn[MAX_CONN - value],
                         &stmt[MAX_CONN - value], GMC_CONN_TYPE_ASYNC, 1, g_epollRegInfoOneThread, NULL, NULL, NULL, NULL, -1, 0, &g_epollDataOneThread.userEpollFd);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(stmt[MAX_CONN - value], g_label_name, GMC_OPERATION_INSERT);
    EXPECT_EQ(GMERR_OK, ret);

    for (i = 2; i < MAX_CONN - value + 1; i++) {
        ret = testGmcDisconnect(conn[i], stmt[i]);
        EXPECT_EQ(GMERR_OK, ret);
    }
}
