/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : GMDBV503 迭代一precedence关键字区分插入/删除顺序
 Notes        : 执行相关的用例
 History      :
 Author       : youwanyong/ywx1157510
 Create       : [2023.04.11]
*****************************************************************************/

#include "outTableIsOrd.h"
#include "t_datacom_lite.h"
// 订阅对于后续count位一正一负的数据需要将count合并为0两条消息会合并为一条进行推送
// function 一条tuple一次
// agg 一组数据一次
// tbm 每个tuple一次
// msgtbm， 每个msgsize一次
// timeout 一条tuple一次

using namespace std;

class outTableIsOrd : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("mkdir -p /root/_datalog_/");
        system("chmod -R 777 /root/_datalog_/");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void outTableIsOrd::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    system("gmadmin -cfgName enableLogFold -cfgVal 0");
#if (ENV_RTOSV2X) && (RUN_DATACOM_DAP)
    bool envAnos = false;
    if (0 == access("/hpe/libdblog_adapter.so", 0)) {
        envAnos = false;
    } else {
        envAnos = true;
    }
    if (!envAnos) {
        system(" echo \"\" > /opt/vrpv8/home/<USER>/diag.current.log");
    } else {
        system(" echo \"\" > /opt/vrpv8/home/<USER>/hpe_log.csv ");
    }
#else
    system(" echo \"\" > ../../../log/run/rgmserver/rgmserver.log ");
#endif
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN(0);
}
void outTableIsOrd::TearDown()
{
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    system("rm -rf /root/_datalog_/");
}

/* ****************************************************************************
 Description  : 001..d中含输出表A,B, C，表B含local 关键字，precedence 顺序B表在前，
 进行数据插入，删除，插入，查看输出表数据
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_001)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_001";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
 Description  : 002..d中含输出表A,B, C，表B含local 关键字，precedence
顺序B表在中，进行数据插入，删除，插入，查看输出表数据
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_002)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_002";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 003..d中含输出表A,B, C，表B含local 关键字，precedence
顺序B表在后，进行数据插入，删除，插入，查看输出表数据
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_003)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_003";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 004.d中含输出表A,B, C，表B含pubsub 关键字，precedence
顺序B表在前，进行数据插入，删除，插入，查看输出表数据
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_004)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_004";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.B";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 0;
    userData->readIdFunc = DoubleInt4_getId;
    userData->startid = 0;
    userData->endid = 1;
    userData->count = 1;
    ret = GmcSubscribe(g_stmt, &tmp_sub_info, subConn, snCallback, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = userData->startid;
    int endid = userData->endid;    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = CompareValue(0, result, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // update会当成两条处理
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData->data);
    free(userData);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 005.d中含输出表A,B, C，表B含pubsub 关键字，precedence
顺序B表在中，进行数据插入，删除，插入，查看输出表数据
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_005)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_005";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.B";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 0;
    userData->readIdFunc = DoubleInt4_getId;
    userData->startid = 0;
    userData->endid = 1;
    userData->count = 1;
    ret = GmcSubscribe(g_stmt, &tmp_sub_info, subConn, snCallback, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = userData->startid;
    int endid = userData->endid;    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = CompareValue(0, result, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // update会当成两条处理
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData->data);
    free(userData);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 006.d中含输出表A,B, C，表B含pubsub 关键字，precedence
顺序B表在后，进行数据插入，删除，插入查看输出表数据
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_006)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_006";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.B";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 0;
    userData->readIdFunc = DoubleInt4_getId;
    userData->startid = 0;
    userData->endid = 1;
    userData->count = 1;
    ret = GmcSubscribe(g_stmt, &tmp_sub_info, subConn, snCallback, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = userData->startid;
    int endid = userData->endid;    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB, false, GMERR_FEATURE_NOT_SUPPORTED);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = CompareValue(0, result, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // update会当成两条处理
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 4, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData->data);
    free(userData);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 007.d中含输出表A,B, C，均含pubsub 关键字，precedence 顺序为C B
A，进行数据插入，删除，插入，查看输出表数据
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_007)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_007";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "申请B表订阅");
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    SnUserDataWithFuncT *userData;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.B";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConn, &userData, &tmp_sub_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_info);

    int startid = 0;
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    AW_FUN_Log(LOG_STEP, "申请C表订阅");
    GmcConnT *subConnC;
    SnUserDataWithFuncT *userDataC;
    const char *subConnNameC = "testSubC";
    char *sub_infoC = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_C.gmjson", &sub_infoC);
    EXPECT_NE((void *)NULL, sub_infoC);
    const char *subNameC = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_infoC;
    tmp_sub_infoC.subsName = subNameC;
    tmp_sub_infoC.configJson = sub_infoC;
    ret = TestCreateSub(subConn, &userDataC, &tmp_sub_infoC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoC);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");
    // 1 1 -1, 2 2 -1
    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{4, 1, -1}, {3, 3, 1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 14, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 14, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 14, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    // update时输出表没有外部表不存在主键冲突
    testGmcGetLastError();

    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, subStmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData->data);
    testSnFreeUserData(userDataA->data);
    testSnFreeUserData(userDataC->data);
    free(userData);
    free(userDataA);
    free(userDataC);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 008.d中含输出表A,B, C，均含external 关键字，precedence 顺序为C B
A，进行数据插入，删除，插入，查看输出表数据
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_008)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "F";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_008";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // 加载外部表
    CreateOutTable();

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    const char *subConnNameC = "testSubC";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);

    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outA.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    SnUserDataWithFunc1T *userDataA = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataA->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataA->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoA, subConn, snCallback_external, userDataA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);

    char *sub_infoC = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outC.gmjson", &sub_infoC);
    EXPECT_NE((void *)NULL, sub_infoC);
    const char *subNameC = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_infoC;
    tmp_sub_infoC.subsName = subNameC;
    tmp_sub_infoC.configJson = sub_infoC;
    SnUserDataWithFunc1T *userDataC = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataC->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataC->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoC, subConn, snCallback_external, userDataC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoC);

    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};
    record = 2;  // 输入表update逻辑保持不变
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 3;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期count为空
    AW_FUN_Log(LOG_STEP, "5.update table");
    record = 2;
    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    ret = PkUpdate(g_stmt, tableG, count4, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 4;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataF->data);
    free(userDataF);

    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    ret = GmcUnSubscribe(g_stmt, subNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataC->data);
    free(userDataC);

    GmcDropVertexLabel(g_stmt, "A");
    GmcDropVertexLabel(g_stmt, "C");
    GmcDropVertexLabel(g_stmt, "F");

    // 卸载so
    AW_FUN_Log(LOG_STEP, "4.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 009.d中含输出表A,B, C，表B含external 关键字，precedence
顺序B表在前，进行数据插入，删除，插入，查看输出表数据
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_009)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "F";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_009";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema1 = NULL;
    GmcDropVertexLabel(g_stmt, "F");
    readJanssonFile("./datalogDml/F.gmjson", &schema1);
    EXPECT_NE((void *)NULL, schema1);

    int ret = GmcCreateVertexLabel(g_stmt, schema1, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema1);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConn;
    const char *subConnNameC = "testSubC";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);

    AW_FUN_Log(LOG_STEP, "1.load so");    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  // update 更新两条count 不一样的数据预期count为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 只推送了count为正的数据
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "4.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataF->data);
    free(userDataF);

    GmcDropVertexLabel(g_stmt, "F");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 010.d中含输出表A,B, C，表B含external 关键字，precedence
顺序B表在中，进行数据插入，删除，插入，查看输出表数据  merge到外部表count为负不会推送
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_010)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "F";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_010";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema1 = NULL;
    GmcDropVertexLabel(g_stmt, "F");
    readJanssonFile("./datalogDml/F.gmjson", &schema1);
    EXPECT_NE((void *)NULL, schema1);

    int ret = GmcCreateVertexLabel(g_stmt, schema1, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema1);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConn;
    const char *subConnNameC = "testSubC";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);

    AW_FUN_Log(LOG_STEP, "1.load so");    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  // update 更新两条count 不一样的数据预期count为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 只推送了count为正的数据
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "4.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataF->data);
    free(userDataF);

    GmcDropVertexLabel(g_stmt, "F");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 011.d中含输出表A,B, C，表B含external 关键字，precedence
顺序B表在后，进行数据插入，删除，插入，查看输出表数据
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_011)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "F";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_011";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema1 = NULL;
    GmcDropVertexLabel(g_stmt, "F");
    readJanssonFile("./datalogDml/F.gmjson", &schema1);
    EXPECT_NE((void *)NULL, schema1);

    int ret = GmcCreateVertexLabel(g_stmt, schema1, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema1);
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConn;
    const char *subConnNameC = "testSubC";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);

    AW_FUN_Log(LOG_STEP, "1.load so");    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  // update 更新两条count 不一样的数据预期count为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 只推送了count为正的数据
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "4.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataF->data);
    free(userDataF);

    GmcDropVertexLabel(g_stmt, "F");
    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 012.d中含输出表A,B, C，表A, B含notify 关键字，precedence
顺序为ABC，进行数据插入，删除，插入，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_012)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_012";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "申请B表订阅");
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    SnUserDataWithFuncT *userData;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.B";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConn, &userData, &tmp_sub_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_info);

    int startid = 0;  // 为啥不能使用->
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");
    // 1 1 -1, 2 2 -1
    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    // 存在主键冲突
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData->data);
    testSnFreeUserData(userDataA->data);
    free(userData);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 013.d中含输出表A,B, C，表A, B含notify 关键字，precedence
顺序为BCA，进行数据插入，删除，插入，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_013)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_013";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "申请B表订阅");
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    SnUserDataWithFuncT *userData;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.B";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConn, &userData, &tmp_sub_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_info);

    int startid = 0;  // 为啥不能使用->
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");
    // 1 1 -1, 2 2 -1
    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    // 存在主键冲突
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData->data);
    testSnFreeUserData(userDataA->data);
    free(userData);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 014.d中含输出表A,B, C，表A, B含notify 关键字，precedence
顺序为CAB，进行数据插入，删除，插入，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_014)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_014";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "申请B表订阅");
    GmcConnT *subConn;
    GmcStmtT *subStmt;
    SnUserDataWithFuncT *userData;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, &subStmt, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.B";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    AW_FUN_Log(LOG_STEP, "创建订阅");
    ret = TestCreateSub(subConn, &userData, &tmp_sub_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testGmcGetLastError();
    free(sub_info);

    int startid = 0;  // 为啥不能使用->
    int endid = 1;
    AW_FUN_Log(LOG_STEP, "申请A表订阅");
    GmcConnT *subConnA;
    SnUserDataWithFuncT *userDataA;
    const char *subConnNameA = "testSubA";
    char *sub_infoA = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_A.gmjson", &sub_infoA);
    EXPECT_NE((void *)NULL, sub_infoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = sub_infoA;
    ret = TestCreateSub(subConn, &userDataA, &tmp_sub_infoA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoA);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");
    // 1 1 -1, 2 2 -1
    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    // 存在主键冲突
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    ret = GmcUnSubscribe(g_stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData->data);
    testSnFreeUserData(userDataA->data);
    free(userData);
    free(userDataA);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 015.d中含输出表A,B, C，表A B为TBM表，precedence
顺序为ABC，进行数据插入，删除，更新，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_015)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_015";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 按删除走
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 016.d中含输出表A,B, C，表A B为TBM表，precedence 顺序为BCA
进行数据插入，删除，插入，更新，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_016)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_016";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 按删除走
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 017.d中含输出表A,B, C，表A B为TBM表，precedence
顺序为CAB，进行数据插入，删除，插入，更新，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_017)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_017";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 按删除走
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 4;
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(2, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    system("cat /root/_datalog_/TbmRunLog.txt");
    system("> /root/_datalog_/TbmRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 018.d中含输出表A,B, C，表A B为消息通知表，precedence 顺序为ABC，，进行数据插入，删除，插入，更新，
查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_018)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_018";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 按删除走
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :019.d中含输出表A,B, C，表A B为消息通知表，precedence 顺序为BCA，进行数据插入，删除，插入，更新，
查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_019)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_019";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 按删除走
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :020.d中含输出表A,B, C，表A B为消息通知表，precedence 顺序为CAB，进行数据插入，删除，插入，更新，
查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_020)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_020";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // 按删除走
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    system("cat /root/msgNotifyRunLog.txt");
    system("> /root/msgNotifyRunLog.txt");

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :021.d中含输出表A,B, C，表B为可更新表，precedence
顺序B表在前，进行数据插入，删除，插入，更新，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_021)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_021";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");  // 可更新表insert了相同的数据,
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    int32_t result1[][3] = {{1, 2, 1}, {2, 3, 1}, {4, 3, 1}, {3, 3, 1}};
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result1, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  :022.d中含输出表A,B, C，表B为可更新表，precedence 顺序B表在中，进行数据插入，删除，插入，更新，
查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_022)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_022";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");  // 可更新表insert了相同的数据,
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    int32_t result1[][3] = {{1, 2, 1}, {2, 3, 1}, {4, 3, 1}, {3, 3, 1}};
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result1, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
 Description  :023.d中含输出表A,B, C，表B为可更新表，precedence
顺序B表在后，进行数据插入，删除，插入，更新，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_023)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_023";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");  // 可更新表insert了相同的数据,
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    int32_t result1[][3] = {{1, 2, 1}, {2, 3, 1}, {4, 3, 1}, {3, 3, 1}};
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result1, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 024.d中含普通输入表A,B, C，precedence 顺序为A,B, C，进行数据插入 查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_024)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG1[] = "G1";
    char tableG2[] = "G2";
    char tableG3[] = "G3";

    char nsName[128] = "DataLog_034_024";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableA, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsert(g_conn, g_stmt, tableB, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = BatchInsert(g_conn, g_stmt, tableC, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableG2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableG3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableA, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = PkUpdate(g_stmt, tableB, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = PkUpdate(g_stmt, tableC, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableG1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableG2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableG3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableA, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = BatchInsert(g_conn, g_stmt, tableB, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = BatchInsert(g_conn, g_stmt, tableC, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    ret = ReadTable(g_stmt, tableG1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = ReadTable(g_stmt, tableG2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = ReadTable(g_stmt, tableG3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableA, count4, record,0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PkUpdate(g_stmt, tableB, count4, record,0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = PkUpdate(g_stmt, tableC, count4, record,0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    int32_t result[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG1);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableG1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableG2);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableG2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableG3);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, result, tableG3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 025.d中含普通中间表A,B, C，precedence 顺序为A,B, C，进行数据插入 查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_025)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_025";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条数据后预期输出表table为空
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableA);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableB);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableC);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 026.d中含多种输出表（TBM,消息通知表，pubsub表，local，可更新表，外部表）A,B, C, D, E, F  顺序为A,B, C,
D, E, F 不含 precedence进行数据插入，删除，更新，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_026)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableE[] = "E";
    char tableF[] = "F";
    char tableD[] = "D";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_026";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema1 = NULL;
    GmcDropVertexLabel(g_stmt, "F");
    readJanssonFile("./datalogDml/F.gmjson", &schema1);
    EXPECT_NE((void *)NULL, schema1);

    int ret = GmcCreateVertexLabel(g_stmt, schema1, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema1);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // datalog表订阅通道
    GmcConnT *subConn;  
    const char *subConnNameC = "testSubC";
    // 外部表订阅通道
    GmcConnT *subConn2; 
    const char *subConnNameEtn = "testSubEtn";

    // datalog表订阅通道创建
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 外部表订阅通道创建
    ret = testSubConnect(&subConn2, NULL, 1, g_epoll_reg_info, subConnNameEtn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "申请C表订阅");
    SnUserDataWithFuncT *userDataC;
    char *sub_infoC = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_C.gmjson", &sub_infoC);
    EXPECT_NE((void *)NULL, sub_infoC);
    const char *subNameC = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_infoC;
    tmp_sub_infoC.subsName = subNameC;
    tmp_sub_infoC.configJson = sub_infoC;
    ret = TestCreateSub(subConn, &userDataC, &tmp_sub_infoC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoC);

    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn2, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);    // // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看A, B表顺序的
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;  // 可更新表count小于0数据为删除
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // update 更新两条数据后预期输出表table为空
    system("gmsysview record F");
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    int32_t resultD[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    int32_t resultE[][3] = {{1, 2, 1}, {2, 3, 1}, {4, 3, 1}, {3, 3, 1}};
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultE, tableE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataC->data);
    testSnFreeUserData(userDataF->data);
    free(userDataC);
    free(userDataF);
    GmcDropVertexLabel(g_stmt, "F");

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 027.d中含多种输出表（TBM,消息通知表，pubsub表，local，可更新表，外部表）A,B, C, D, E, F  顺序为FEDCBA
不含 precedence进行数据插入，删除，更新，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_027)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableE[] = "E";
    char tableF[] = "F";
    char tableD[] = "D";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_029";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema1 = NULL;
    GmcDropVertexLabel(g_stmt, "F");
    readJanssonFile("./datalogDml/F.gmjson", &schema1);
    EXPECT_NE((void *)NULL, schema1);

    int ret = GmcCreateVertexLabel(g_stmt, schema1, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema1);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // datalog表订阅通道
    GmcConnT *subConn;  
    const char *subConnNameC = "testSubC";
    // 外部表订阅通道
    GmcConnT *subConn2; 
    const char *subConnNameEtn = "testSubEtn";

    // datalog表订阅通道创建
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 外部表订阅通道创建
    ret = testSubConnect(&subConn2, NULL, 1, g_epoll_reg_info, subConnNameEtn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "申请C表订阅");
    SnUserDataWithFuncT *userDataC;
    char *sub_infoC = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_C.gmjson", &sub_infoC);
    EXPECT_NE((void *)NULL, sub_infoC);
    const char *subNameC = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_infoC;
    tmp_sub_infoC.subsName = subNameC;
    tmp_sub_infoC.configJson = sub_infoC;
    ret = TestCreateSub(subConn, &userDataC, &tmp_sub_infoC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoC);

    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn2, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);    // // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看A, B表顺序的
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;  // 可更新表count小于0数据为删除
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // update 更新两条数据后预期输出表table为空
    system("gmsysview record F");
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    int32_t resultD[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    int32_t resultE[][3] = {{1, 2, 1}, {2, 3, 1}, {4, 3, 1}, {3, 3, 1}};
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultE, tableE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataC->data);
    testSnFreeUserData(userDataF->data);
    free(userDataC);
    free(userDataF);
    GmcDropVertexLabel(g_stmt, "F");

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}
/* ****************************************************************************
 Description  : 028.d中含多种输出表（TBM,消息通知表，pubsub表，local，可更新表，外部表）A,B, C, D, E, F  顺序为DCBAFE
不含 precedence进行数据插入，删除，更新，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_028)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableE[] = "E";
    char tableF[] = "F";
    char tableD[] = "D";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_028";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema1 = NULL;
    GmcDropVertexLabel(g_stmt, "F");
    readJanssonFile("./datalogDml/F.gmjson", &schema1);
    EXPECT_NE((void *)NULL, schema1);

    int ret = GmcCreateVertexLabel(g_stmt, schema1, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema1);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // datalog表订阅通道
    GmcConnT *subConn;  
    const char *subConnNameC = "testSubC";
    // 外部表订阅通道
    GmcConnT *subConn2; 
    const char *subConnNameEtn = "testSubEtn";

    // datalog表订阅通道创建
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 外部表订阅通道创建
    ret = testSubConnect(&subConn2, NULL, 1, g_epoll_reg_info, subConnNameEtn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "申请C表订阅");
    SnUserDataWithFuncT *userDataC;
    char *sub_infoC = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_C.gmjson", &sub_infoC);
    EXPECT_NE((void *)NULL, sub_infoC);
    const char *subNameC = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_infoC;
    tmp_sub_infoC.subsName = subNameC;
    tmp_sub_infoC.configJson = sub_infoC;
    ret = TestCreateSub(subConn, &userDataC, &tmp_sub_infoC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoC);

    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn2, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);    // // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看A, B表顺序的
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;  // 可更新表count小于0数据为删除
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // update 更新两条数据后预期输出表table为空
    system("gmsysview record F");
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    int32_t resultD[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    int32_t resultE[][3] = {{1, 2, 1}, {2, 3, 1}, {4, 3, 1}, {3, 3, 1}};
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultE, tableE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataC->data);
    testSnFreeUserData(userDataF->data);
    free(userDataC);
    free(userDataF);
    GmcDropVertexLabel(g_stmt, "F");

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 029.d中含多种输出表（TBM,消息通知表，pubsub表，local，可更新表，外部表）A,B, C, D, E, F
precedence顺序为A,B, C, D, E, F 进行数据插入，删除，更新，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_029)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableE[] = "E";
    char tableF[] = "F";
    char tableD[] = "D";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_029";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema1 = NULL;
    GmcDropVertexLabel(g_stmt, "F");
    readJanssonFile("./datalogDml/F.gmjson", &schema1);
    EXPECT_NE((void *)NULL, schema1);

    int ret = GmcCreateVertexLabel(g_stmt, schema1, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema1);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // datalog表订阅通道
    GmcConnT *subConn;  
    const char *subConnNameC = "testSubC";
    // 外部表订阅通道
    GmcConnT *subConn2; 
    const char *subConnNameEtn = "testSubEtn";

    // datalog表订阅通道创建
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 外部表订阅通道创建
    ret = testSubConnect(&subConn2, NULL, 1, g_epoll_reg_info, subConnNameEtn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "申请C表订阅");
    SnUserDataWithFuncT *userDataC;
    char *sub_infoC = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_C.gmjson", &sub_infoC);
    EXPECT_NE((void *)NULL, sub_infoC);
    const char *subNameC = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_infoC;
    tmp_sub_infoC.subsName = subNameC;
    tmp_sub_infoC.configJson = sub_infoC;
    ret = TestCreateSub(subConn, &userDataC, &tmp_sub_infoC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoC);

    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn2, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);    // // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看A, B表顺序的
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;  // 可更新表count小于0数据为删除
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // update 更新两条数据后预期输出表table为空
    system("gmsysview record F");
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    int32_t resultD[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    int32_t resultE[][3] = {{1, 2, 1}, {2, 3, 1}, {4, 3, 1}, {3, 3, 1}};
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultE, tableE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataC->data);
    testSnFreeUserData(userDataF->data);
    free(userDataC);
    free(userDataF);
    GmcDropVertexLabel(g_stmt, "F");

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 030.d中含多种输出表（TBM,消息通知表，pubsub表，local，可更新表，外部表）A,B, C, D, E, F precedence
顺序为FEDCBA 进行数据插入，删除，更新，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_030)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableE[] = "E";
    char tableF[] = "F";
    char tableD[] = "D";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_030";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema1 = NULL;
    GmcDropVertexLabel(g_stmt, "F");
    readJanssonFile("./datalogDml/F.gmjson", &schema1);
    EXPECT_NE((void *)NULL, schema1);

    int ret = GmcCreateVertexLabel(g_stmt, schema1, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema1);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // datalog表订阅通道
    GmcConnT *subConn;  
    const char *subConnNameC = "testSubC";
    // 外部表订阅通道
    GmcConnT *subConn2; 
    const char *subConnNameEtn = "testSubEtn";

    // datalog表订阅通道创建
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 外部表订阅通道创建
    ret = testSubConnect(&subConn2, NULL, 1, g_epoll_reg_info, subConnNameEtn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "申请C表订阅");
    SnUserDataWithFuncT *userDataC;
    char *sub_infoC = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_C.gmjson", &sub_infoC);
    EXPECT_NE((void *)NULL, sub_infoC);
    const char *subNameC = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_infoC;
    tmp_sub_infoC.subsName = subNameC;
    tmp_sub_infoC.configJson = sub_infoC;
    ret = TestCreateSub(subConn, &userDataC, &tmp_sub_infoC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoC);

    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn2, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);    // // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看A, B表顺序的
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;  // 可更新表count小于0数据为删除
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // update 更新两条数据后预期输出表table为空
    system("gmsysview record F");
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    int32_t resultD[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    int32_t resultE[][3] = {{1, 2, 1}, {2, 3, 1}, {4, 3, 1}, {3, 3, 1}};
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultE, tableE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataC->data);
    testSnFreeUserData(userDataF->data);
    free(userDataC);
    free(userDataF);
    GmcDropVertexLabel(g_stmt, "F");

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

/* ****************************************************************************
 Description  : 031.d中含多种输出表（TBM,消息通知表，pubsub表，local，可更新表，外部表）A,B, C, D, E, F
precedence顺序为DCBAFE 进行数据插入，删除，更新，查看输出表merge数据的顺序
**************************************************************************** */
TEST_F(outTableIsOrd, DataLog_034_031)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";
    char tableE[] = "E";
    char tableF[] = "F";
    char tableD[] = "D";
    char tableG[] = "G";

    char nsName[128] = "DataLog_034_031";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema1 = NULL;
    GmcDropVertexLabel(g_stmt, "F");
    readJanssonFile("./datalogDml/F.gmjson", &schema1);
    EXPECT_NE((void *)NULL, schema1);

    int ret = GmcCreateVertexLabel(g_stmt, schema1, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema1);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

   // datalog表订阅通道
    GmcConnT *subConn;  
    const char *subConnNameC = "testSubC";
    // 外部表订阅通道
    GmcConnT *subConn2; 
    const char *subConnNameEtn = "testSubEtn";

    // datalog表订阅通道创建
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 外部表订阅通道创建
    ret = testSubConnect(&subConn2, NULL, 1, g_epoll_reg_info, subConnNameEtn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "申请C表订阅");
    SnUserDataWithFuncT *userDataC;
    char *sub_infoC = NULL;
    readJanssonFile((char *)"./datalogDml/pubsub_precedence_C.gmjson", &sub_infoC);
    EXPECT_NE((void *)NULL, sub_infoC);
    const char *subNameC = "testSub_ns1.C";
    GmcSubConfigT tmp_sub_infoC;
    tmp_sub_infoC.subsName = subNameC;
    tmp_sub_infoC.configJson = sub_infoC;
    ret = TestCreateSub(subConn, &userDataC, &tmp_sub_infoC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoC);

    AW_FUN_Log(LOG_STEP, "申请F表订阅");
    char *sub_infoF = NULL;
    readJanssonFile((char *)"./datalogDml/pub_outF.gmjson", &sub_infoF);
    EXPECT_NE((void *)NULL, sub_infoF);
    const char *subNameF = "testSub_ns1.F";
    GmcSubConfigT tmp_sub_infoF;
    tmp_sub_infoF.subsName = subNameF;
    tmp_sub_infoF.configJson = sub_infoF;
    SnUserDataWithFunc1T *userDataF = (SnUserDataWithFunc1T *)malloc(sizeof(SnUserDataWithFunc1T));
    userDataF->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userDataF->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(g_stmt, &tmp_sub_infoF, subConn2, snCallback_external, userDataF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_infoF);    // // 向表中写入数据
    AW_FUN_Log(LOG_STEP, "2.batch insert table");
    int32_t count1[][3] = {{1, 1, -1}, {2, 2, -1}};

    // 写入2条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看A, B表顺序的
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  // 全表扫描验证数据
    int record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;  // 可更新表count小于0数据为删除
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    // update 更新两条数据后预期输出表table为空
    system("gmsysview record F");
    AW_FUN_Log(LOG_STEP, "3.update table");

    int32_t count2[][3] = {{1, 2, -1}, {2, 3, -1}};  // 输入表update逻辑保持不变
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count2, record);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);

    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    record = 2;
    // 主键更新count为负的数据时，原数据被删除，新数据未插入
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);

    // insert 写入两条count不一样的数据
    AW_FUN_Log(LOG_STEP, "4.insert different count");
    int32_t count3[][3] = {{1, 2, 1}, {2, 3, 1}, {3, 2, 1}, {4, 3, -1}};

    // 写入4条数据
    ret = BatchInsert(g_conn, g_stmt, tableG, count3, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PRIMARY_KEY_VIOLATION, ret);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 全表扫描验证数据
    record = 2;
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    memset(g_bufTest, 0, 100);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // update 更新两条count 不一样的数据预期输出表主键更新count为负的数据被删除
    AW_FUN_Log(LOG_STEP, "5.update table");

    int32_t count4[][3] = {{3, 3, 1}, {4, 1, -1}};
    record = 2;
    ret = PkUpdate(g_stmt, tableG, count4, record, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    record = 2;
    int32_t resultG[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableG);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultG, tableG);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 2;
    int32_t resultD[][3] = {{1, 1, -1}, {2, 2, -1}};
    ret = ReadTable(g_stmt, tableD);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultD, tableD);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    record = 0;
    int32_t resultE[][3] = {{1, 2, 1}, {2, 3, 1}, {4, 3, 1}, {3, 3, 1}};
    ret = ReadTable(g_stmt, tableE);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    ret = CompareValue(record, resultE, tableE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_bufTest, 0, 100);
    ret = testWaitSnRecv(userDataC->data, GMC_SUB_EVENT_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataF->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("cat /root/_datalog_/RunLog.txt");
    system("> /root/_datalog_/RunLog.txt");

    // 日志校验，预期写入count为负产生告警
    CheckLog(GMERR_DATA_EXCEPTION);
    CheckLog(GMERR_PRIMARY_KEY_VIOLATION);
    testGmcGetLastError();

    ret = GmcUnSubscribe(g_stmt, subNameF);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subNameC);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConn2, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataC->data);
    testSnFreeUserData(userDataF->data);
    free(userDataC);
    free(userDataF);
    GmcDropVertexLabel(g_stmt, "F");

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}

class outTableIsOrdModCfg : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("mkdir -p /root/_datalog_/");
        system("chmod -R 777 /root/_datalog_/");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=2048\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=256\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=1024\"");
        // DTS2024081224883
        system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultHashType=cceh\"");
        system("sh ${TEST_HOME}/tools/start.sh");
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        int ret;
        ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 创建epoll监听线程
        ret = create_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }
};

void outTableIsOrdModCfg::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    system("gmadmin -cfgName enableLogFold -cfgVal 0");
#if (ENV_RTOSV2X) && (RUN_DATACOM_DAP)
    system(" echo \"\" > /opt/vrpv8/home/<USER>/diag.current.log");
#else
    system(" echo \"\" > ../../../log/run/rgmserver/rgmserver.log ");
#endif
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN(0);
}
void outTableIsOrdModCfg::TearDown()
{
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
    system("rm -rf /root/_datalog_/");
}

/* ****************************************************************************
 Description  : 032.笛卡尔积，C :- A(a,b),B(c,d)写入大数据量表A写入1000条，表B写入10000条
 当前并发不支持并发250万以上报错超时 16004
**************************************************************************** */
TEST_F(outTableIsOrdModCfg, DataLog_034_032)
{
    AW_FUN_Log(LOG_STEP, "test begin");
    char tableA[] = "A";
    char tableB[] = "B";
    char tableC[] = "C";

    char nsName[128] = "DataLog_034_032";
    // 卸载同名datalog.so
    TestUninstallDatalog(nsName, NULL, false);

    // .d文件加载：创建表和连接规则
    AW_FUN_Log(LOG_STEP, "1.load so");
    int ret = LoadSoFile(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int cycleNumble = 300;
// 减少数据量避免写入数据超时
#if defined TEST_STATIC_ASAN
    cycleNumble = 5;
#endif
    int32_t count1[cycleNumble][3];

    for (int k = 0; k < 10; k++) {
        for (int i = 0 + k * cycleNumble; i < cycleNumble+ k * cycleNumble; i++) {
            for (int j = 0; j < 3; j++) {
                count1[i % cycleNumble][j] = i+ 1;
                count1[i % cycleNumble][2] = 1;
            }
        }
        // 向表B写入10000条数据
        ret = BatchInsert(g_conn, g_stmt, tableB, count1, cycleNumble);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = BatchInsert(g_conn, g_stmt, tableA, count1, cycleNumble);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    //  // 全表扫描验证数据
    int record = cycleNumble*cycleNumble*10;
    ret = ReadTable(g_stmt, tableC, true);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    record = cycleNumble*10;
    ret = ReadTable(g_stmt, tableB, true);
    AW_MACRO_EXPECT_EQ_INT(record, ret);
    record = cycleNumble;
    ret = ReadTable(g_stmt, tableA, true);
    AW_MACRO_EXPECT_EQ_INT(record, ret);

    // 卸载so
    AW_FUN_Log(LOG_STEP, "6.uninstall so");
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end");
}
