/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: ResProEnhance2.cpp  out recourse
 * Description: Datalog pubsub Resource Support RollBack Resource Property
 * Author: youwanyong ywx1157510
 * Create: 2024-7-10
 */

#include "UniversalTools.h"
#include "t_datacom_lite.h"

using namespace std;

class ResProEnhance : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int32_t ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
    }
    static void TearDownTestCase()
    {}
};

void ResProEnhance::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    g_isCount0 = 0;
    int32_t ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    g_dataNum = 0;
    g_callBackTime = 0;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void ResProEnhance::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

/* ****************************************************************************
 Description  : 031.pubsub资源表作为输出表时不支持资源字段为变长字段，编译报错
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_031)
{
     AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[50] = "不支持资源字段为变长字段str";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_031";
    char datalogFile[FILE_PATH] = "./datalog_file/";
    char completePath[MAX_CMD_SIZE] = {0};

    (void)snprintf(completePath, MAX_CMD_SIZE, "%s%s.d", datalogFile, soName1);
    (void)snprintf(command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s", g_toolPath, completePath);
    system(command);

    AW_FUN_Log(LOG_INFO, "cmd: %s", command);
    ret = executeCommand(command, "the output field:\"proper_62_uint8\" of pubsub resource \"midRes\" should not be "
                                  "variable-length data type near line 3.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(completePath, 0, MAX_CMD_SIZE);
    memset(command, 0, MAX_CMD_SIZE);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 032.pubsub资源表作为输出表时，正向推送的数据和推送响应的数据不一致，（修改主键），写表报错
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "pubsub资源表作为输出表时，正向推送的数据和推送响应的数据不一致，（修改主键）";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_out";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetModifyPk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*2-1);

    AW_FUN_Log(LOG_INFO,"预期收到数据条数%d",g_dataNum);

    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    g_callBackTime = 0;
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum - 1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*3-2);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 033.pubsub资源表作为输出表时，正向推送的数据和推送响应的数据不一致，（对推送的数据少于正向收到的消息条数），写表报错，
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "推送的数据少于正向收到的消息条数";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_out";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetCallBackNoReSend);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*2-1);

    AW_FUN_Log(LOG_INFO,"预期收到数据条数%d",g_dataNum);

    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    g_callBackTime = 0;
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum - 1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*3-2);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 034.pubsub资源输出表推送异常回滚（回填的失败信息数不为0, 传入的数组元素不为0）
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "回填的失败信息数不为0, 传入的数组元素不为0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_out";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallbackSetFailNum, rsc0Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*2);

    AW_FUN_Log(LOG_INFO,"预期收到数据条数%d",g_dataNum);

    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    g_callBackTime = 0;
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum - 1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*3-1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 035.pubsub资源表作为输出表时， pubsub资源表推送异常回滚 订阅回调内超时（回填的失败信息数为0）（不填的推送默认值）
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "回填的失败信息数不为0, 传入的数组元素不为0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_out";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallbackWait3S, rsc0Get1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    sleep(15);
    // 订阅超时能回滚当前批次
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2,120000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*2);

    AW_FUN_Log(LOG_INFO,"预期收到数据条数%d",g_dataNum);

    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    int32_t rollBackNums = 129;
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    g_callBackTime = 0;
    g_dataNum = 0;
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum - 1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum-1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 036.pubsub资源表作为输出表时，pubsub资源表推送异常回滚 订阅回调内响应count和正向不一致 count>0（回填的失败信息数为0））
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "订阅回调内响应count和正向不一致 count>0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_out";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetCount1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum);

    g_dataNum = 0;
    SetArrayValue(objIn1, recordNum,-1);
    // 删除数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2-1,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    int32_t rollBackNums = recordNum;
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    g_callBackTime = 0;
    g_dataNum = 0;
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 037.pubsub资源表作为输出表时，pubsub资源表推送异常回滚 订阅回调内响应count和正向不一致 count=0（回填的失败信息数为0）
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "订阅回调内响应count和正向不一致 count=0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_out";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetCount2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum);

    g_dataNum = 0;
    SetArrayValue(objIn1, recordNum,-1);
    // 删除数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2-1,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    int32_t rollBackNums = recordNum;
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    g_callBackTime = 0;
    g_dataNum = 0;
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 038.pubsub资源表作为输出表时，pubsub资源表推送异常回滚 订阅回调内响应count和正向不一致 count<0（回填的失败信息数为0）
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "订阅回调内响应count和正向不一致 count<0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_out";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetCount3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum * 2 - 1, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum * 2 - 1);

    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    int32_t rollBackNums = recordNum;
    CheckoutArrayIsSame(rollBackNums);
    
    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    g_dataNum = 0;
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 039.pubsub资源表作为输出表时，写过期输入表，触发过期时，订阅异常，预期回滚正常
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "写过期输入表，触发过期时，订阅异常";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_039";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetTimeout);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    // 减小同批数据过期值误差，尽量构造同批过期
    for (int32_t i = 0; i < recordNum; i++) {
        objIn1[i].proper_3_int8 = 1000;
    }

    // 批写数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    AW_FUN_Log(LOG_STEP, "4.订阅接收数据.");
    // // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    // 等待数据过期
    sleep(20);
    ret = readRecord(conn, stmt, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*4-1);
    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致g_callBackTime%d.",g_callBackTime);
    
    int32_t rollBackNums = recordNum;
    CheckoutArrayIsSame(rollBackNums);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 040.pubsub资源表作为输出表时，写相同输入表，外部表记录数超限，写数据失败，pubsub表订阅异常,回滚正常
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "外部表记录数超限";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_040";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetExternal128);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据超过最大记录数.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecord(conn, stmt, "inp", objIn1, 129, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"N000", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    sleep(10);
   AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.",g_callBackTime); 
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*2);
     AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    int32_t rollBackNums = recordNum;
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum-1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"N000", objIn1, recordNum-1, AllTypeTableGet,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class ResProEnhance1 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"subsChannelGlobalShareMemSizeMax=1\" "
               "\"subsChannelGlobalDynamicMemSizeMax=1\"");
        system("sh ${TEST_HOME}/tools/stop.sh");
        system("sh ${TEST_HOME}/tools/start.sh");
        int32_t ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        
    }
    static void TearDownTestCase()
    {}
};

void ResProEnhance1::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    g_dataNum = 0;
    g_callBackTime = 0;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void ResProEnhance1::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  : 041.pubsub资源表作为输出表时，写相同输入表，外部表订阅异常，写数据失败，pubsub表订阅异常,回滚正常
**************************************************************************** */
TEST_F(ResProEnhance1, DataLog_083_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "外部表订阅异常";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    AddWhiteList(GMERR_COMMON_STREAM_OVERLOAD);
    int32_t ret = 0;
    AddWhiteList(GMERR_SUB_PUSH_QUEUE_FULL);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_042";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083NOLimit.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, NULL, 1, g_epoll_reg_info, g_subConnName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 外部表创建订阅连接
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmtSync, g_connSub1, (char *)"datalog_file/DataLog_083_external.gmjson", &userData1,
        1000, g_subName1, snCallbackExternal, rsc0GetExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "3。外部表订阅超时，数据被回滚.");
    int recordNum = 1000;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    g_isFailed = true;
    AW_FUN_Log(LOG_STEP, "输入写入，等待回调执行");
    // 批写数据
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret);
    testGmcGetLastError();
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "输入写入，等待回调执行");

    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "N000", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    sleep(10);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.", g_callBackTime);
    AW_FUN_Log(LOG_STEP, "g_dataNum = %d.", g_dataNum);
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, g_dataNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    int32_t rollBackNums = recordNum;
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
     g_isFailed = false; // 不再阻塞订阅回调
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum/100, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt, "inp", objIn1, recordNum/100, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "midRes", objIn1, recordNum/100, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "N000", objIn1, recordNum/100, AllTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum/100, 120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
        ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = cancelSubscription(g_stmtSync, g_subName1, &userData1, 1, recordNum*2-1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_connSub);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_connSub1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcDisconnect(g_connSync, g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 卸载so
        AW_FUN_Log(LOG_STEP, "8.卸载so.");
        ret = TestUninstallDatalog(soName1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropVertexLabel(stmt, "N000");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "test end.");
}

class ResProEnhance2 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=8\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=24\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=12\"");
        system("sh ${TEST_HOME}/tools/stop.sh");
        system("sh ${TEST_HOME}/tools/start.sh");
        int32_t ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        
    }
    static void TearDownTestCase()
    {}
};

void ResProEnhance2::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    g_dataNum = 0;
    g_callBackTime = 0;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void ResProEnhance2::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  : 042.pubsub资源表作为输出表时，写输入表动态内存满，pubsub表订阅异常,回滚正常
**************************************************************************** */
TEST_F(ResProEnhance2, DataLog_083_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "写输入表动态内存满";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_043";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083NOLimit.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, NULL, 1, g_epoll_reg_info, g_subConnName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetDym);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 外部表创建订阅连接
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmtSync, g_connSub1, (char *)"datalog_file/DataLog_083_external.gmjson", &userData1,
        1000, g_subName1, snCallbackExternal, rsc0GetDym);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "3.写数据内存满.");
    int recordNum = 200;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum/2, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批写数据
    ret = writeRecord(conn, stmt, "inp", objIn1+100, recordNum/2, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    testGmcGetLastError();
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "输入写入，等待回调执行");

    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "inp1", objIn1, recordNum / 2, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "N000", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.", g_callBackTime);
    AW_FUN_Log(LOG_STEP, "g_dataNum = %d.", g_dataNum);
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame(100);

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
     g_isFailed = false; // 不再阻塞订阅回调
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum/100, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt, "inp", objIn1, recordNum/100, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "midRes", objIn1, recordNum / 100, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "N000", objIn1, recordNum / 100, AllTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum / 100, 120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum * 2 - 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync, g_subName1, &userData1, 1, recordNum * 2 - 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

class ResProEnhance3 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=8\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=32\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=12\"");
        system("sh ${TEST_HOME}/tools/stop.sh");
        system("sh ${TEST_HOME}/tools/start.sh");
        int32_t ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        
    }
    static void TearDownTestCase()
    {}
};

void ResProEnhance3::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AddWhiteList(GMERR_UNDEFINED_OBJECT);
    g_dataNum = 0;
    g_callBackTime = 0;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}
void ResProEnhance3::TearDown()
{
    AW_CHECK_LOG_END();
    int32_t ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
/* ****************************************************************************
 Description  : 043.pubsub资源表作为输出表时，写输入表共享内存满，pubsub表订阅异常，回滚正常
**************************************************************************** */
TEST_F(ResProEnhance3, DataLog_083_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "外部表订阅异常";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_042";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083NOLimit.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync1, &g_stmtSync1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, NULL, 1, g_epoll_reg_info, g_subConnName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = false;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        10000, g_subName, snCallback, rsc0GetSysShm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 外部表创建订阅连接
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmtSync1, g_connSub1, (char *)"datalog_file/DataLog_083_external.gmjson", &userData1,
        10000, g_subName1, snCallbackExternal1, rsc0GetExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    int recordNum = 1000;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    int startValue = 0;
    int cycle = 6;
    while (cycle > 0) {
        SetArrayValue(objIn1, recordNum + startValue, 1, startValue);
        // 批写数据
        ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        startValue += 1000;
        cycle--;
    }
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, startValue, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_MERGE_INSERT, startValue, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ret = %d  totalNum = %d.", ret, startValue);
    g_dataNum = 0;
    g_callBackTime = 0;
    AW_FUN_Log(LOG_STEP, "3.写输入表直到内存满.");
    SetArrayValue(objIn1, recordNum + startValue, 1, startValue);
    ret = writeRecord(conn, stmt, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    AW_FUN_Log(LOG_STEP, "ret = %d  totalNum = %d.", ret, startValue);
    testGmcGetLastError();
    sleep(10);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, g_dataNum, 120000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.", g_callBackTime);
    AW_FUN_Log(LOG_STEP, "g_dataNum = %d.", g_dataNum);
    CheckoutArrayIsSame();
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum * 2 - 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync1, g_subName1, &userData1, 1, recordNum * 2 - 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.", g_callBackTime);
    AW_FUN_Log(LOG_STEP, "g_dataNum = %d.", g_dataNum);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testGmcDisconnect(g_connSync1, g_stmtSync1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 044.pubsub资源表作为输出表时，二级索引更新数据，数据异常，通过udf构造
 输出表先输出count为负数据
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "主键更新数据，数据异常";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_043";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083NOLimit.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        10000, g_subName, snCallback, rsc0GetUpdateout);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "3.写数据.");
    int recordNum = 30000;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    int startValue = 0;
    for (int32_t i = 0; i < recordNum; i += 1000) {
        SetArrayValue(objIn1, 1000 + i, 1, i);
        // 批写数据
        ret = writeRecord(conn, stmt, "inp", objIn1, 1000, AllTypeTableSet, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.订阅接收数据.");
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.",g_callBackTime); 
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum);
    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "5.二级索引更新数据.");
    g_callBackTime = 0;
    g_dataNum = 0;
    g_isCount0 = 1;
    ret = UpdateRecord(conn, stmt, "inp", objIn1, 1, UpdateByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, 128*3-20, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.",g_callBackTime); 
    AW_FUN_Log(LOG_STEP, "g_dataNum = %d.",g_dataNum); 

    AW_FUN_Log(LOG_STEP, "7.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame();
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 045.pubsub资源表作为输出表时，主键删除数据，数据异常，通过udf构造
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "主键更新数据，数据异常";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_043";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083NOLimit.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        10000, g_subName, snCallback, rsc0GetDelete);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "3.写数据.");
    int recordNum = 30000;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    int startValue = 0;
    for (int32_t i = 0; i < recordNum; i += 1000) {
        SetArrayValue(objIn1, 1000 + i, 1, i);
        // 批写数据
        ret = writeRecord(conn, stmt, "inp", objIn1, 1000, AllTypeTableSet, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.订阅接收数据.");
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.",g_callBackTime); 
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum);
    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "5.二级索引删除数据.");
    g_callBackTime = 0;
    g_dataNum = 0;
    g_isCount0 = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, "inp", GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &(objIn1[0].proper_0_int1), sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, 364, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.",g_callBackTime); 
    AW_FUN_Log(LOG_STEP, "g_dataNum = %d.",g_dataNum); 

    AW_FUN_Log(LOG_STEP, "7.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame();
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 046.pubsub资源表作为输出表时，使用gmimport导入输入表数据，数据异常
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "使用gmimport导入输入表数据，数据异常";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_043";
    GmcStmtT *stmt;
    GmcConnT *conn;

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083NOLimit.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetGmimport);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "3.导入数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    char cmd[256] = "gmimport -c vdata -f ./datalog_file/inp.gmdata";
    (void)sprintf(cmd, "gmimport -c vdata -f ./datalog_file/inp.gmdata -ns %s", g_testNameSpace);
    ret = executeCommand((char*)cmd,(char*)"insert data unsucc. totalNum: 2048, successNum: 255");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    memset(cmd, 0, sizeof(cmd));
    (void)sprintf(cmd, "gmsysview count inp -ns %s", g_testNameSpace);
    ret = executeCommand((char*)cmd,"255");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));
    (void)sprintf(cmd, "gmsysview count midRes -ns %s", g_testNameSpace);
    ret = executeCommand((char*)cmd,"255");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));
    (void)sprintf(cmd, "gmsysview count N000 -ns %s", g_testNameSpace);
    ret = executeCommand((char*)cmd,"255");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, 639,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, 639);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.",g_callBackTime); 
    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "6.再次导入数据.");
    memset(cmd, 0, sizeof(cmd));
    (void)sprintf(cmd, "gmimport -c vdata -f ./datalog_file/inp.gmdata -ns %s", g_testNameSpace);
    ret = executeCommand((char*)cmd,(char*)"success");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    memset(cmd, 0, sizeof(cmd));
    (void)sprintf(cmd, "gmsysview count inp -ns %s", g_testNameSpace);
    ret = executeCommand((char*)cmd,"2048");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));
    (void)sprintf(cmd, "gmsysview count midRes -ns %s", g_testNameSpace);
    ret = executeCommand((char*)cmd,"2048");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(cmd, 0, sizeof(cmd));
    (void)sprintf(cmd, "gmsysview count N000 -ns %s", g_testNameSpace);
    ret = executeCommand((char*)cmd,"2048");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, 1793,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "7.导入数据失败，数据未发生回滚.");

    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 047.（异步）pubsub资源表作为输出表时，正向推送的数据和推送响应的数据不一致，（修改主键），写表报错
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "pubsub资源表作为输出表时，正向推送的数据和推送响应的数据不一致，（修改主键）";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_out";
    GmcStmtT *stmt;
    GmcConnT *conn;
    GmcConnT *connAsync;
    GmcStmtT *stmtAsync;
    ret = testGmcConnect(&connAsync, &stmtAsync,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetModifyPk);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*2-1);

    AW_FUN_Log(LOG_INFO,"预期收到数据条数%d",g_dataNum);

    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    g_callBackTime = 0;
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum - 1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*3-2);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 048.（异步）pubsub资源表作为输出表时，正向推送的数据和推送响应的数据不一致，（对推送的数据少于正向收到的消息条数），写表报错，
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "推送的数据少于正向收到的消息条数";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_out";
    GmcStmtT *stmt;
    GmcConnT *conn;
    GmcConnT *connAsync;
    GmcStmtT *stmtAsync;
    ret = testGmcConnect(&connAsync, &stmtAsync,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetCallBackNoReSend);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*2-1);

    AW_FUN_Log(LOG_INFO,"预期收到数据条数%d",g_dataNum);

    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    g_callBackTime = 0;
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum - 1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*3-2);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 049.（异步）pubsub资源输出表推送异常回滚（回填的失败信息数不为0, 传入的数组元素不为0）
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "回填的失败信息数不为0, 传入的数组元素不为0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_out";
    GmcStmtT *stmt;
    GmcConnT *conn;
    GmcConnT *connAsync;
    GmcStmtT *stmtAsync;
    ret = testGmcConnect(&connAsync, &stmtAsync,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallbackSetFailNum, rsc0Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*2);

    AW_FUN_Log(LOG_INFO,"预期收到数据条数%d",g_dataNum);

    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    g_callBackTime = 0;
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum - 1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*3-1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 050.（异步）pubsub资源表作为输出表时， pubsub资源表推送异常回滚 订阅回调内超时（回填的失败信息数为0）（不填的推送默认值）
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "回填的失败信息数不为0, 传入的数组元素不为0";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_REQUEST_TIME_OUT);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_out";
    GmcStmtT *stmt;
    GmcConnT *conn;
    GmcConnT *connAsync;
    GmcStmtT *stmtAsync;
    ret = testGmcConnect(&connAsync, &stmtAsync,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallbackWait3S, rsc0Get1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    // 订阅超时能回滚当前批次
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*2);

    AW_FUN_Log(LOG_INFO,"预期收到数据条数%d",g_dataNum);

    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    int32_t rollBackNums = 129;
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    g_callBackTime = 0;
    g_dataNum = 0;
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum - 1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum-1);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 051.（异步）pubsub资源表作为输出表时，写过期输入表，触发过期时，订阅异常，预期回滚正常
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "写过期输入表，触发过期时，订阅异常";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_039";
    GmcStmtT *stmt;
    GmcConnT *conn;
    GmcConnT *connAsync;
    GmcStmtT *stmtAsync;
    ret = testGmcConnect(&connAsync, &stmtAsync,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetTimeout);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    // 减小同批数据过期值误差，尽量构造同批过期
    for (int32_t i = 0; i < recordNum; i++) {
        objIn1[i].proper_3_int8 = 1000;
    }

    // 批写数据
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "4.订阅接收数据.");
    // // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    // 等待数据过期
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum * 3 - 1, 120000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum * 4 - 1);
    AW_MACRO_EXPECT_EQ_INT(2 + 2 + 1 + 2, g_callBackTime);
    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致g_callBackTime%d.", g_callBackTime);
    ret = readRecord(conn, stmt, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    
    int32_t rollBackNums = recordNum;
    CheckoutArrayIsSame(rollBackNums);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 052.（异步）pubsub资源表作为输出表时，写相同输入表，外部表记录数超限，写数据失败，pubsub表订阅异常,回滚正常
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "外部表记录数超限";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_040";
    GmcStmtT *stmt;
    GmcConnT *conn;
    GmcConnT *connAsync;
    GmcStmtT *stmtAsync;
    ret = testGmcConnect(&connAsync, &stmtAsync,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetExternal128);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "3.批写数据超过最大记录数.");
    int recordNum = 129;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);

    // 批写数据
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, 129, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt,"inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"N000", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    sleep(10);
   AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.",g_callBackTime); 
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum*2,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum*2);
     AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    int32_t rollBackNums = recordNum;
    CheckoutArrayIsSame();

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum-1, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt,"inp", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"midRes", objIn1, recordNum-1, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt,"N000", objIn1, recordNum-1, AllTypeTableGet,true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum-1,120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 053.（异步）pubsub资源表作为输出表时，写输入表动态内存满，pubsub表订阅异常,回滚正常
**************************************************************************** */
TEST_F(ResProEnhance2, DataLog_083_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "写输入表动态内存满";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    AddWhiteList(GMERR_MEMORY_OPERATE_FAILED);
    AddWhiteList(GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_043";
    GmcStmtT *stmt;
    GmcConnT *conn;
    GmcConnT *connAsync;
    GmcStmtT *stmtAsync;
    ret = testGmcConnect(&connAsync, &stmtAsync,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083NOLimit.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, NULL, 1, g_epoll_reg_info, g_subConnName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        1000, g_subName, snCallback, rsc0GetDym);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 外部表创建订阅连接
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmtSync, g_connSub1, (char *)"datalog_file/DataLog_083_external.gmjson", &userData1,
        1000, g_subName1, snCallbackExternal, rsc0GetDym);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "3.写数据内存满.");
    int recordNum = 200;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    SetArrayValue(objIn1, recordNum);
    ret = writeRecordAsync(connAsync, stmtAsync, "inp1", objIn1, recordNum/2, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 批写数据
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1 + 100, recordNum / 2, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    testGmcGetLastError();
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "输入写入，等待回调执行");

    AW_FUN_Log(LOG_STEP, "4.读数据.");
    ret = readRecord(conn, stmt, "inp", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "inp1", objIn1, recordNum / 2, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "midRes", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "N000", objIn1, 0, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "5.订阅接收数据.");
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.", g_callBackTime);
    AW_FUN_Log(LOG_STEP, "g_dataNum = %d.", g_dataNum);
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "6.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame(100);

    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "7.再次批写数据.");
     g_isFailed = false; // 不再阻塞订阅回调
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum/100, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    ret = readRecord(conn, stmt, "inp", objIn1, recordNum/100, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "midRes", objIn1, recordNum/100, AllTypeTableGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "N000", objIn1, recordNum/100, AllTypeTableGet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum/100, 120);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (objIn1) {
        free(objIn1);
    }
        ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

        ret = cancelSubscription(g_stmtSync, g_subName1, &userData1, 1, recordNum*2-1);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_connSub);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(g_connSub1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = testGmcDisconnect(g_connSync, g_stmtSync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 卸载so
        AW_FUN_Log(LOG_STEP, "8.卸载so.");
        ret = TestUninstallDatalog(soName1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcDropVertexLabel(stmt, "N000");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  : 054.（异步）pubsub资源表作为输出表时，写输入表共享内存满，pubsub表订阅异常，回滚正常
**************************************************************************** */
TEST_F(ResProEnhance3, DataLog_083_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "外部表订阅异常";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_OUT_OF_MEMORY);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_042";
    GmcStmtT *stmt;
    GmcConnT *conn;
    GmcConnT *connAsync;
    GmcStmtT *stmtAsync;
    ret = testGmcConnect(&connAsync, &stmtAsync,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083NOLimit.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync1, &g_stmtSync1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_connSub1, NULL, 1, g_epoll_reg_info, g_subConnName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = false;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        10000, g_subName, snCallback, rsc0GetSysShm);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 外部表创建订阅连接
    SnUserDataWithFuncT userData1 = {0};
    ret = createSubscription(g_stmtSync1, g_connSub1, (char *)"datalog_file/DataLog_083_external.gmjson", &userData1,
        10000, g_subName1, snCallbackExternal1, rsc0GetExternal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写数据
    int recordNum = 1000;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    int startValue = 0;
    int cycle = 6;
    while (cycle > 0) {
        SetArrayValue(objIn1, recordNum + startValue, 1, startValue);
        // 批写数据
        ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum, AllTypeTableSet, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        startValue += 1000;
        cycle--;
    }
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, startValue, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_MERGE_INSERT, startValue, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ret = %d  totalNum = %d.", ret, startValue);
    g_dataNum = 0;
    g_callBackTime = 0;
    AW_FUN_Log(LOG_STEP, "3.写输入表直到内存满.");
    SetArrayValue(objIn1, recordNum + startValue, 1, startValue);
    ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, recordNum, AllTypeTableSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    AW_FUN_Log(LOG_STEP, "ret = %d  totalNum = %d.", ret, startValue);
    testGmcGetLastError();
    sleep(10);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, g_dataNum, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.", g_callBackTime);
    AW_FUN_Log(LOG_STEP, "g_dataNum = %d.", g_dataNum);
    CheckoutArrayIsSame();
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum * 2 - 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    ret = cancelSubscription(g_stmtSync1, g_subName1, &userData1, 1, recordNum * 2 - 1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.", g_callBackTime);
    AW_FUN_Log(LOG_STEP, "g_dataNum = %d.", g_dataNum);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   ret = testGmcDisconnect(g_connSync1, g_stmtSync1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 055.（异步）pubsub资源表作为输出表时，二级索引更新数据，数据异常，通过udf构造
 输出表先输出count为负数据
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "主键更新数据，数据异常";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_043";
    GmcStmtT *stmt;
    GmcConnT *conn;
    GmcConnT *connAsync;
    GmcStmtT *stmtAsync;
    ret = testGmcConnect(&connAsync, &stmtAsync,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083NOLimit.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        10000, g_subName, snCallback, rsc0GetUpdateout);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "3.写数据.");
    int recordNum = 30000;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    int startValue = 0;
    for (int32_t i = 0; i < recordNum; i += 1000) {
        SetArrayValue(objIn1, 1000 + i, 1, i);
        // 批写数据
        ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, 1000, AllTypeTableSet, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.订阅接收数据.");
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.",g_callBackTime); 
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum);
    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "5.二级索引更新数据.");
    g_callBackTime = 0;
    g_dataNum = 0;
    g_isCount0 = 1;
    ret = UpdateRecord(conn, stmt, "inp", objIn1, 1, UpdateByKeyID, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, 128*3-20, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.",g_callBackTime); 
    AW_FUN_Log(LOG_STEP, "g_dataNum = %d.",g_dataNum); 

    AW_FUN_Log(LOG_STEP, "7.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame();
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 056.（异步）pubsub资源表作为输出表时，主键删除数据，数据异常，通过udf构造
**************************************************************************** */
TEST_F(ResProEnhance, DataLog_083_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char testcasesInfo[200] = "主键更新数据，数据异常";
    AW_FUN_Log(LOG_STEP, testcasesInfo);
    int32_t ret = 0;
    AddWhiteList(GMERR_DATA_EXCEPTION);
    char command[MAX_CMD_SIZE] = {0};
    char soName1[FILE_PATH] = "DataLog_083_043";
    GmcStmtT *stmt;
    GmcConnT *conn;
    GmcConnT *connAsync;
    GmcStmtT *stmtAsync;
    ret = testGmcConnect(&connAsync, &stmtAsync,GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载so
    AW_FUN_Log(LOG_STEP, "1.加载so.");
    ret = TestYangGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 建表N000
    GmcDropVertexLabel(stmt, "N000");
    DatalogCreateExternalTable(stmt, "datalog_file/DataLog_083NOLimit.gmjson");
    ret = LoadSoFile(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcConnect(&g_connSync, &g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testSubConnect(&g_connSub, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 创建订阅
    AW_FUN_Log(LOG_STEP, "2.创建订阅.");
    g_isFailed = true;
    g_failedDataNum = 1;
    SnUserDataWithFuncT userData = {0};
    ret = createSubscription(g_stmtSync, g_connSub, (char *)"datalog_file/pubsub.gmjson", &userData,
        10000, g_subName, snCallback, rsc0GetDelete);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 写数据
    AW_FUN_Log(LOG_STEP, "3.写数据.");
    int recordNum = 30000;
    char strT[10] = {0};
    SmallAllTypeTableStruct *objIn1 = (SmallAllTypeTableStruct *)malloc(sizeof(SmallAllTypeTableStruct) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(SmallAllTypeTableStruct) * recordNum);
    int startValue = 0;
    for (int32_t i = 0; i < recordNum; i += 1000) {
        SetArrayValue(objIn1, 1000 + i, 1, i);
        // 批写数据
        ret = writeRecordAsync(connAsync, stmtAsync, "inp", objIn1, 1000, AllTypeTableSet, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    SystemSnprintf("gmsysview count -ns %s", g_testNameSpace);
    AW_FUN_Log(LOG_STEP, "4.订阅接收数据.");
    // 订阅接收
    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, recordNum,12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.",g_callBackTime); 
    AW_MACRO_EXPECT_EQ_INT(g_dataNum, recordNum);
    // 再次批写数据
    AW_FUN_Log(LOG_STEP, "5.二级索引删除数据.");
    g_callBackTime = 0;
    g_dataNum = 0;
    g_isCount0 = 1;
    ret = testGmcPrepareStmtByLabelName(stmt, "inp", GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(stmt, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT8, &(objIn1[0].proper_0_int1), sizeof(int8_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    ret = testWaitSnRecv(userData.data, GMC_SUB_EVENT_INSERT, 364, 12000);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int affectRows = 0;
    ret = GmcGetStmtAttr(stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, affectRows);
    AW_FUN_Log(LOG_STEP, "g_callBackTime = %d.",g_callBackTime); 
    AW_FUN_Log(LOG_STEP, "g_dataNum = %d.",g_dataNum); 

    AW_FUN_Log(LOG_STEP, "7.校验正向数据和回滚数据一致.");
    CheckoutArrayIsSame();
    if (objIn1) {
        free(objIn1);
    }
    ret = cancelSubscription(g_stmtSync, g_subName, &userData, 1, recordNum*2-1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_connSub);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(g_connSync, g_stmtSync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(connAsync, stmtAsync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载so
    AW_FUN_Log(LOG_STEP, "8.卸载so.");
    ret = TestUninstallDatalog(soName1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcDropVertexLabel(stmt, "N000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
