%version v0.0.0
// pusub资源表，输出字段可以有多个
%resource midRes(proper_0_int1:int1,proper_1_int2:int2,proper_2_int4:int4,proper_3_int8:int8,proper_4_uint1:uint1,proper_5_uint2:uint2,proper_6_uint4:uint4,proper_7_uint8:uint8,
proper_8_int1:int1,proper_9_int2:int2,proper_10_int4:int4,proper_11_int8:int8,proper_12_uint1:uint1,proper_13_uint2:uint2,proper_14_uint4:uint4,proper_15_uint8:uint8,
proper_16_int1:int1,proper_17_int2:int2,proper_18_int4:int4,proper_19_int8:int8,proper_20_uint1:uint1,proper_21_uint2:uint2,proper_22_uint4:uint4,proper_23_uint8:uint8,
proper_24_int1:int1,proper_25_int2:int2,proper_26_int4:int4,proper_27_int8:int8,proper_28_uint1:uint1,proper_29_uint2:uint2,proper_30_byte10:byte10->
proper_31_int1:int1,proper_32_int2:int2,proper_33_int4:int4,proper_34_int8:int8,proper_35_uint1:uint1,proper_36_uint2:uint2,proper_37_uint4:uint4,proper_38_uint8:uint8,
proper_39_int1:int1,proper_40_int2:int2,proper_41_int4:int4,proper_42_int8:int8,proper_43_uint1:uint1,proper_44_uint2:uint2,proper_45_uint4:uint4,proper_46_uint8:uint8,
proper_47_int1:int1,proper_48_int2:int2,proper_49_int4:int4,proper_50_int8:int8,proper_51_uint1:uint1,proper_52_uint2:uint2,proper_53_uint4:uint4,proper_54_uint8:uint8,
proper_55_int1:int1,proper_56_int2:int2,proper_57_int4:int4,proper_58_int8:int8,proper_59_uint1:uint1,proper_60_uint2:uint2,proper_61_uint4:uint4,proper_62_uint8:uint8)
{
index(0(proper_0_int1,proper_1_int2,proper_2_int4,proper_3_int8,proper_4_uint1,proper_5_uint2,proper_6_uint4,proper_7_uint8,
proper_8_int1,proper_9_int2,proper_10_int4,proper_11_int8,proper_12_uint1,proper_13_uint2,proper_14_uint4,proper_15_uint8,
proper_16_int1,proper_17_int2,proper_18_int4,proper_19_int8,proper_20_uint1,proper_21_uint2,proper_22_uint4,proper_23_uint8,
proper_24_int1,proper_25_int2,proper_26_int4,proper_27_int8,proper_28_uint1,proper_29_uint2,proper_30_byte10)),pending_id(127,130,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127,127)
}

// 资源表输入表
%table inp(proper_0_int1:int1,proper_1_int2:int2,proper_2_int4:int4,proper_3_int8:int8,proper_4_uint1:uint1,proper_5_uint2:uint2,proper_6_uint4:uint4,proper_7_uint8:uint8,
proper_8_int1:int1,proper_9_int2:int2,proper_10_int4:int4,proper_11_int8:int8,proper_12_uint1:uint1,proper_13_uint2:uint2,proper_14_uint4:uint4,proper_15_uint8:uint8,
proper_16_int1:int1,proper_17_int2:int2,proper_18_int4:int4,proper_19_int8:int8,proper_20_uint1:uint1,proper_21_uint2:uint2,proper_22_uint4:uint4,proper_23_uint8:uint8,
proper_24_int1:int1,proper_25_int2:int2,proper_26_int4:int4,proper_27_int8:int8,proper_28_uint1:uint1,proper_29_uint2:uint2,proper_30_byte10:byte10,proper_31_int1:int1,proper_32_int2:int2,proper_33_int4:int4,proper_34_int8:int8,proper_35_uint1:uint1,proper_36_uint2:uint2,proper_37_uint4:uint4,proper_38_uint8:uint8,
proper_39_int1:int1,proper_40_int2:int2,proper_41_int4:int4,proper_42_int8:int8,proper_43_uint1:uint1,proper_44_uint2:uint2,proper_45_uint4:uint4,proper_46_uint8:uint8,
proper_47_int1:int1,proper_48_int2:int2,proper_49_int4:int4,proper_50_int8:int8,proper_51_uint1:uint1,proper_52_uint2:uint2,proper_53_uint4:uint4,proper_54_uint8:uint8,
proper_55_int1:int1,proper_56_int2:int2,proper_57_int4:int4,proper_58_int8:int8,proper_59_uint1:uint1,proper_60_uint2:uint2,proper_61_uint4:uint4,proper_62_uint8:uint8)
{
index(0(proper_0_int1,proper_1_int2,proper_2_int4,proper_3_int8,proper_4_uint1,proper_5_uint2,proper_6_uint4,proper_7_uint8,
proper_8_int1,proper_9_int2,proper_10_int4,proper_11_int8,proper_12_uint1,proper_13_uint2,proper_14_uint4,proper_15_uint8,
proper_16_int1,proper_17_int2,proper_18_int4,proper_19_int8,proper_20_uint1,proper_21_uint2,proper_22_uint4,proper_23_uint8,
proper_24_int1,proper_25_int2,proper_26_int4,proper_27_int8,proper_28_uint1,proper_29_uint2,proper_30_byte10))
}

// 资源表输出表
%table N000(proper_0_int1:int1,proper_1_int2:int2,proper_2_int4:int4,proper_3_int8:int8,proper_4_uint1:uint1,proper_5_uint2:uint2,proper_6_uint4:uint4,proper_7_uint8:uint8,
proper_8_int1:int1,proper_9_int2:int2,proper_10_int4:int4,proper_11_int8:int8,proper_12_uint1:uint1,proper_13_uint2:uint2,proper_14_uint4:uint4,proper_15_uint8:uint8,
proper_16_int1:int1,proper_17_int2:int2,proper_18_int4:int4,proper_19_int8:int8,proper_20_uint1:uint1,proper_21_uint2:uint2,proper_22_uint4:uint4,proper_23_uint8:uint8,
proper_24_int1:int1,proper_25_int2:int2,proper_26_int4:int4,proper_27_int8:int8,proper_28_uint1:uint1,proper_29_uint2:uint2,proper_30_byte10:byte10,proper_31_int1:int1,proper_32_int2:int2,proper_33_int4:int4,proper_34_int8:int8,proper_35_uint1:uint1,proper_36_uint2:uint2,proper_37_uint4:uint4,proper_38_uint8:uint8,
proper_39_int1:int1,proper_40_int2:int2,proper_41_int4:int4,proper_42_int8:int8,proper_43_uint1:uint1,proper_44_uint2:uint2,proper_45_uint4:uint4,proper_46_uint8:uint8,
proper_47_int1:int1,proper_48_int2:int2,proper_49_int4:int4,proper_50_int8:int8,proper_51_uint1:uint1,proper_52_uint2:uint2,proper_53_uint4:uint4,proper_54_uint8:uint8,
proper_55_int1:int1,proper_56_int2:int2,proper_57_int4:int4,proper_58_int8:int8,proper_59_uint1:uint1,proper_60_uint2:uint2,proper_61_uint4:uint4,proper_62_uint8:uint8)
{
index(0(proper_0_int1,proper_1_int2,proper_2_int4,proper_3_int8,proper_4_uint1,proper_5_uint2,proper_6_uint4,proper_7_uint8,
proper_8_int1,proper_9_int2,proper_10_int4,proper_11_int8,proper_12_uint1,proper_13_uint2,proper_14_uint4,proper_15_uint8,
proper_16_int1,proper_17_int2,proper_18_int4,proper_19_int8,proper_20_uint1,proper_21_uint2,proper_22_uint4,proper_23_uint8,
proper_24_int1,proper_25_int2,proper_26_int4,proper_27_int8,proper_28_uint1,proper_29_uint2,proper_30_byte10)),external
}

// 其它输出表
%table out(proper_0_int1:int1,proper_1_int2:int2,proper_2_int4:int4,proper_3_int8:int8,proper_4_uint1:uint1,proper_5_uint2:uint2,proper_6_uint4:uint4,proper_7_uint8:uint8,
proper_8_int1:int1,proper_9_int2:int2,proper_10_int4:int4,proper_11_int8:int8,proper_12_uint1:uint1,proper_13_uint2:uint2,proper_14_uint4:uint4,proper_15_uint8:uint8,
proper_16_int1:int1,proper_17_int2:int2,proper_18_int4:int4,proper_19_int8:int8,proper_20_uint1:uint1,proper_21_uint2:uint2,proper_22_uint4:uint4,proper_23_uint8:uint8,
proper_24_int1:int1,proper_25_int2:int2,proper_26_int4:int4,proper_27_int8:int8,proper_28_uint1:uint1,proper_29_uint2:uint2,proper_30_byte10:byte10,proper_31_int1:int1,proper_32_int2:int2,proper_33_int4:int4,proper_34_int8:int8,proper_35_uint1:uint1,proper_36_uint2:uint2,proper_37_uint4:uint4,proper_38_uint8:uint8,
proper_39_int1:int1,proper_40_int2:int2,proper_41_int4:int4,proper_42_int8:int8,proper_43_uint1:uint1,proper_44_uint2:uint2,proper_45_uint4:uint4,proper_46_uint8:uint8,
proper_47_int1:int1,proper_48_int2:int2,proper_49_int4:int4,proper_50_int8:int8,proper_51_uint1:uint1,proper_52_uint2:uint2,proper_53_uint4:uint4,proper_54_uint8:uint8,
proper_55_int1:int1,proper_56_int2:int2,proper_57_int4:int4,proper_58_int8:int8,proper_59_uint1:uint1,proper_60_uint2:uint2,proper_61_uint4:uint4,proper_62_uint8:uint8)
{
index(0(proper_0_int1,proper_1_int2,proper_2_int4,proper_3_int8,proper_4_uint1,proper_5_uint2,proper_6_uint4,proper_7_uint8,
proper_8_int1,proper_9_int2,proper_10_int4,proper_11_int8,proper_12_uint1,proper_13_uint2,proper_14_uint4,proper_15_uint8,
proper_16_int1,proper_17_int2,proper_18_int4,proper_19_int8,proper_20_uint1,proper_21_uint2,proper_22_uint4,proper_23_uint8,
proper_24_int1,proper_25_int2,proper_26_int4,proper_27_int8,proper_28_uint1,proper_29_uint2,proper_30_byte10))
}

midRes(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-).
N000(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9,a10) :- midRes(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9,a10).
out(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9,a10) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,b2,c2,d2,e2,f2,g2,a3,b3,c3,d3,e3,f3,g3,a4,b4,c4,e4,f4,g4,a5,b5,c5,d5,e5,f5,g5,a6,b6,c6,d6,e6,f6,g6,a7,b7,c7,d7,e7,f7,g7,a8,b8,c8,d8,e8,f8,a9,a10).
