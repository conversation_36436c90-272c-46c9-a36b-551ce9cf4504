namespace ns1{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out2(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out3(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out4(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out5(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out6(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out7(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out8(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out9(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out10(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out11(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out12(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out13(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out14(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out15(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out16(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out17(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out18(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out19(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out20(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }

out1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}

namespace ns2{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out2(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out3(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out4(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out5(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out6(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out7(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out8(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out9(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out10(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out11(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out12(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out13(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out14(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out15(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out16(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out17(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out18(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out19(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out20(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }

out1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}

namespace ns3{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out2(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out3(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out4(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out5(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out6(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out7(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out8(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out9(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out10(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out11(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out12(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out13(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out14(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out15(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out16(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out17(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out18(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out19(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out20(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }

out1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}

namespace ns4{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out2(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out3(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out4(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out5(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out6(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out7(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out8(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out9(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out10(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out11(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out12(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out13(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out14(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out15(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out16(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out17(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out18(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out19(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out20(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }

out1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}

namespace ns5{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out2(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out3(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out4(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out5(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out6(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out7(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out8(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out9(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out10(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out11(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out12(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out13(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out14(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out15(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out16(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out17(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out18(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out19(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out20(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }

out1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}

namespace ns6{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out2(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out3(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out4(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out5(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out6(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out7(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out8(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out9(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out10(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out11(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out12(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out13(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out14(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out15(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out16(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out17(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out18(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out19(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out20(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }

out1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}

namespace ns7{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out2(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out3(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out4(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out5(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out6(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out7(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out8(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out9(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out10(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out11(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out12(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out13(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out14(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out15(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out16(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out17(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out18(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out19(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out20(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }

out1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}

namespace ns8{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out2(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out3(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out4(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out5(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out6(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out7(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out8(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out9(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out10(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out11(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out12(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out13(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out14(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out15(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out16(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out17(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out18(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out19(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out20(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }

out1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}

namespace ns9{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out2(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out3(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out4(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out5(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out6(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out7(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out8(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out9(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out10(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out11(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out12(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out13(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out14(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out15(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out16(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out17(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out18(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out19(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out20(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }

out1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}

namespace ns10{
%table inp(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out2(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out3(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out4(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out5(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out6(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out7(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out8(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out9(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out10(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out11(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out12(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out13(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out14(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out15(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out16(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out17(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out18(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out19(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }
%table out20(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a,b,c)),update,status_merge_sub(true)  }

out1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out2(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out3(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out4(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out5(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out6(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out7(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out8(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out9(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out10(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out11(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out12(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out13(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out14(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out15(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out16(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out17(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out18(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out19(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
out20(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- inp(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4).
}
