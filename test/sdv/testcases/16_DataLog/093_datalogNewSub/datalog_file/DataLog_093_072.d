%resource rs(a:int1, b:int2, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8 -> c:int4){
        sequential(max_size(100)), index(0(a,b,d,e,f,g,a1,b1,c1,d1)),max_size(10)
}

%table inp1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a)),update,status_merge_sub(false),max_size(2) }
%table out1(a:int1, b:int2, c:int4, d:int8, e:byte1, f:byte128, g:byte256, a1:uint1, b1:uint2, c1:uint4, d1:uint8, e1:uint1_8, f1:uint2_16, g1:uint4_32, a2:uint8_64,a3:str,a4:byte){index(0(a))}

rs(a,b,d,e,f,g,a1,b1,c1,d1,-) :- inp1(a,b,-,d,e,f,g,a1,b1,c1,d1,-,-,-,-,-,-).
out1(a,b,c,d,e,f,g,a1,b1,c1,d1,e1,f1,g1,a2,a3,a4) :- rs(a,b,d,e,f,g,a1,b1,c1,d1,c),inp1(-,-,-,-,-,-,-,-,-,-,-,e1,f1,g1,a2,a3,a4).
