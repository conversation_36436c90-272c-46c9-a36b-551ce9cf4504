/* ****************************************************************************
 Description  : 规则中输出表支持外部定义表
 Node      :
 01 接口测试
    001 外部表显示定义index0
    002 外部表隐式定义index0
    003 %table定义外部表存在其他非法选项
    004 %resource定义存在external选项
    005 %function定义存在external选项
    006 %aggregate定义存在external选项
    007 %function选项access对象为外部表
    008 %aggregate选项access对象为外部表
    009 外部表在规则中作为中间表
    010 外部表在规则中作为输入表
    011 外部表作为聚合规则的左表
 02 基本功能
    001 加载so时，外部表未创建
    002 加载so时，已创建外部表与.d定义表类型不匹配(yang)
    003 加载so时，已创建外部表与.d定义表类型不匹配(tree)
    004 加载so时，已创建外部表与.d定义表类型不匹配(kv)
    005 加载so时，已创建外部表与.d定义表字段个数不匹配
    006 加载so时，已创建外部表与.d定义表字段类型不匹配
    007 加载so时，已创建外部表与.d定义表字段索引不匹配
    008 规则中包含外部表，写表触发输入表记录超限
    009 规则中包含外部表，写表触发中间表记录超限
    010 规则中包含外部表，写表触发输出表（外部表）记录超限
    011 规则中包含外部表，写表触发输出表（非外部表）记录超限
    012 投影规则中，输出表为外部表
    013 多表join规则中，输出表为外部表
    014 函数join规则中，输出表为外部表
    015 NOTjoin规则中，输出表为外部表
    016 相同主键一条记录时，验证触发规则写外部表的merge行为
    017 相同主键多条记录时，验证触发规则写外部表的merge行为
    018 相同主键一条记录时，验证触发规则写外部表的推送行为
    019 相同主键多条记录时，验证触发规则写外部表的推送行为
    020 加载多个so文件，不同的so操作同一个外部表(同一个namespace)
    021 加载多个so文件，不同的so操作同一个外部表(不同namespace)
    022 删除外部表后，触发规则写外部表
    023 删除外部表后，卸载包含外部表的so
 03 特性交互
    001 通过全表扫描的方式读外部表
    002 通过主键索引的方式读外部表
    003 通过自定义索引的方式读外部表
    004 通过显示事务的方式触发规则写外部表
    005 没有授予外部表写对象权限，触发规则写外部表预期失败
    006 授予外部表写对象权限，触发规则写外部表预期成功
    007 外部表表升级成功后，触发datalog规则预期失败
    008 对账老化后，触发datalog规则写表
    009 可靠订阅至触发反压
    010 升级后加载成功，降级后触发规则写表
    011 升级后加载失败，降级后加载成功
 Author       : 黄楚灿 hwx1007418
 Modification :
 Date         : 2022/12/26
 node :
**************************************************************************** */
extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "DatalogRun.h"
#include "DatalogSubFunc.h"
#include "t_datacom_lite.h"

class EMPTY_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret;
        char result[64] = {0};
        ret = TestGetResultCommand(
            "cat sysconfig.txt |grep runMode |awk -F '[:]' '{print $2}' |sed 's/ //g'", NULL, result, 64);
        EXPECT_EQ(0, ret);
        if (strncmp(result, "check", 6) == 0) {
            g_usrMode = 0;
        } else if (strncmp(result, "debug", 6) == 0) {
            g_usrMode = 1;
        }
    }
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp()
    {
        system("sh $TEST_HOME/tools/start.sh");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    }

    virtual void TearDown()
    {}
};

class DtlogExTable : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret;
        char result[64] = {0};
        ret = TestGetResultCommand(
            "cat sysconfig.txt |grep runMode |awk -F '[:]' '{print $2}' |sed 's/ //g'", NULL, result, 64);
        EXPECT_EQ(0, ret);
        if (strncmp(result, "check", 6) == 0) {
            g_usrMode = 0;
        } else if (strncmp(result, "debug", 6) == 0) {
            g_usrMode = 1;
        }
    }
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DtlogExTable::SetUp()
{
    system("sh $TEST_HOME/tools/start.sh");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void DtlogExTable::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = 0;
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}

void snCallback_external(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    int ret = snCallbackCheck(subStmt, info, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001 外部表显示定义index0
TEST_F(DtlogExTable, DataLog_015_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    

    const char *configJson1 = "{\"isFastReadUncommitted\":false, \"status_merge_sub\":true}";
    GmcDropVertexLabel(stmt, labelName_out);
    ret = GmcCreateVertexLabel(stmt, schema, configJson1);
    EXPECT_EQ(GMERR_OK, ret);

    (void)TestUninstallDatalog(libName);
    // 外部表支持为状态合并表
    ret = TestLoadDatalog(libNamePath);
    EXPECT_EQ(GMERR_OK, ret);

    const char *configJson2 = "{\"isFastReadUncommitted\":false}";
    GmcDropVertexLabel(stmt, labelName_out);
    ret = GmcCreateVertexLabel(stmt, schema, configJson2);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 1000, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 1000;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C2Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 002 外部表隐式定义index0
TEST_F(DtlogExTable, DataLog_015_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_withoutIndex0.so";
    char libName[] = "external_withoutIndex0";
    char nsName[] = "external_withoutIndex0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C1Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA2");
    sid = 0, eid = 1000, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 1000;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C1Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 003 %table定义外部表存在其他非法选项
TEST_F(DtlogExTable, DataLog_015_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "prefile/table_invalid_option.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128 - offset, "%s", ".c");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmprecompiler -f %s %s \n", g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command, "Error: external table only supports index or external option near line 4.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 004 %resource定义存在external选项
TEST_F(DtlogExTable, DataLog_015_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "prefile/resource_external.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128 - offset, "%s", ".c");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmprecompiler -f %s %s \n", g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command, "Error: invalid option in resource \"rsc\": external near line 2.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 005 %function定义存在external选项
TEST_F(DtlogExTable, DataLog_015_001_005)  // DTS2022122607461
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "prefile/function_external.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128 - offset, "%s", ".c");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmprecompiler -f %s %s \n", g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command, "Error: invalid option in function \"ns1.func\": external near line 5.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 006 %aggregate定义存在external选项
TEST_F(DtlogExTable, DataLog_015_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "prefile/aggregate_external.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128 - offset, "%s", ".c");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmprecompiler -f %s %s \n", g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command, "Error: invalid option in aggregate \"funcA\": external near line 4.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 007 %function选项access对象为外部表
TEST_F(DtlogExTable, DataLog_015_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "prefile/function_external_access.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128 - offset, "%s", ".c");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmprecompiler -f %s %s \n", g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: wrong access table of relation \"ns1.func\": External table \"ns1.outA02\" is not supported near line "
        "6.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 008 %aggregate选项access对象为外部表
TEST_F(DtlogExTable, DataLog_015_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "prefile/aggregate_external_access.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128 - offset, "%s", ".c");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmprecompiler -f %s %s \n", g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command,
        "Error: wrong access table of relation \"funcA\": External table \"outA\" is not supported near line 4.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 009 外部表在规则中作为中间表
TEST_F(DtlogExTable, DataLog_015_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "prefile/external_mid.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128 - offset, "%s", ".c");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmprecompiler -f %s %s \n", g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command, "Error: external table \"ns1.midA01\" should be an output table near line 5.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 010 外部表在规则中作为输入表
TEST_F(DtlogExTable, DataLog_015_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "prefile/external_in.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128 - offset, "%s", ".c");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmprecompiler -f %s %s \n", g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command, "Error: external table \"inA\" should be an output table near line 1.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 011 外部表作为聚合规则的左表
TEST_F(DtlogExTable, DataLog_015_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "prefile/external_aggregate_left.d";
    char CfileName[128] = {};
    int offset = strlen(fileName) - strlen(".d");
    (void)memcpy(CfileName, fileName, offset);
    (void)snprintf(CfileName + offset, 128 - offset, "%s", ".c");

    (void)snprintf(g_command, sizeof(g_command), "%s/gmprecompiler -f %s %s \n", g_toolPath, fileName, CfileName);
    ret = Debug_executeCommand(g_command, "Error: external table \"C\" should be an output table near line 3.",
        "Fail to verify datalog file. Exit with code 1004009.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 001 加载so时，外部表未创建
TEST_F(DtlogExTable, DataLog_015_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -c datalog -f %s \n", g_toolPath, libNamePath);
    ret = Debug_executeCommand(g_command, "Import datalog file unsucc", "ret = 1009010");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 002 加载so时，已创建外部表与.d定义表类型不匹配(yang)
TEST_F(DtlogExTable, DataLog_015_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn_async;
    GmcStmtT *stmt_async;
    ret = testGmcConnect(&conn_async, &stmt_async, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AsyncUserDataT userData = {0};
    char *schema = NULL;
    readJanssonFile("schema_file/yang.gmjson", &schema);
    ASSERT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabelAsync(stmt_async, schema, NULL, create_vertex_label_callback, &userData);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, userData.status);  // public下不支持创建yang表
    free(schema);

    ret = testGmcDisconnect(conn_async, stmt_async);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 003 加载so时，已创建外部表与.d定义表类型不匹配(tree)
TEST_F(DtlogExTable, DataLog_015_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/tree.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -c datalog -f %s \n", g_toolPath, libNamePath);
    ret = Debug_executeCommand(g_command, "Import datalog file unsucc", "ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 004 加载so时，已创建外部表与.d定义表类型不匹配(kv)
TEST_F(DtlogExTable, DataLog_015_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcKvDropTable(stmt, labelName_out);
    ret = GmcKvCreateTable(stmt, labelName_out, NULL);
    EXPECT_EQ(GMERR_OK, ret);

    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -c datalog -f %s -ns %s\n", g_toolPath, libNamePath,
        g_testNameSpace);
    ret = Debug_executeCommand(g_command, "Import datalog file unsucc", "ret = 1009010");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcKvDropTable(stmt, labelName_out);
    EXPECT_EQ(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 005 加载so时，已创建外部表与.d定义表字段个数不匹配
TEST_F(DtlogExTable, DataLog_015_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C1Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -c datalog -f %s \n", g_toolPath, libNamePath);
    ret = Debug_executeCommand(g_command, "Import datalog file unsucc", "ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 006 加载so时，已创建外部表与.d定义表字段类型不匹配
TEST_F(DtlogExTable, DataLog_015_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int1.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -c datalog -f %s \n", g_toolPath, libNamePath);
    ret = Debug_executeCommand(g_command, "Import datalog file unsucc", "ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 007 加载so时，已创建外部表与.d定义表字段索引不匹配
TEST_F(DtlogExTable, DataLog_015_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4_all.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -c datalog -f %s \n", g_toolPath, libNamePath);
    ret = Debug_executeCommand(g_command, "Import datalog file unsucc", "ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 008 规则中包含外部表，写表触发输入表记录超限
TEST_F(DtlogExTable, DataLog_015_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_normal.so";
    char libName[] = "external_normal";
    char nsName[] = "external_normal";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C1Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA");
    sid = 0, eid = 1200, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    if (ret != GMERR_CONFIGURATION_LIMIT_EXCEEDED) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestWaitDatalogQueue(uData, 100, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 1000;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C1Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 009 规则中包含外部表，写表触发中间表记录超限
TEST_F(DtlogExTable, DataLog_015_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_normal.so";
    char libName[] = "external_normal";
    char nsName[] = "external_normal";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C1Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    sid = 0, eid = 1200, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    if (ret != GMERR_CONFIGURATION_LIMIT_EXCEEDED) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestWaitDatalogQueue(uData, 100, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 1000;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C1Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 010 规则中包含外部表，写表触发输出表（外部表）记录超限
TEST_F(DtlogExTable, DataLog_015_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_normal.so";
    char libName[] = "external_normal";
    char nsName[] = "external_normal";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    char configJson[128] = "{\"max_record_count\":1000, \"isFastReadUncommitted\":false}";
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C1Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA02");
    sid = 0, eid = 1200, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);
    ret = TestWaitDatalogQueue(uData, 100, OUTPUT_EXEC, uData->outputQueue.sucessTimes + 1000);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 1000;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C1Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 011 规则中包含外部表，写表触发输出表（非外部表）记录超限
TEST_F(DtlogExTable, DataLog_015_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_normal.so";
    char libName[] = "external_normal";
    char nsName[] = "external_normal";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C1Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA03");
    sid = 0, eid = 1200, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    if (ret != GMERR_CONFIGURATION_LIMIT_EXCEEDED) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestWaitDatalogQueue(uData, 100, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 1000;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C1Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 012 投影规则中，输出表为外部表
TEST_F(DtlogExTable, DataLog_015_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_rule_project.so";
    char libName[] = "external_rule_project";
    char nsName[] = "external_rule_project";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4_all.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA");
    sid = 0, eid = 100, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C2Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 100;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C2Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 视图查询
    ret = query_Server_Memrty_OverHead_View("SERVER_DYM", "SERVER_SHM");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = query_Com_Table_Mem_Sumarry_View("TABLE_NAME");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 013 多表join规则中，输出表为外部表
TEST_F(DtlogExTable, DataLog_015_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_rule_join.so";
    char libName[] = "external_rule_join";
    char nsName[] = "external_rule_join";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4_all.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA");
    sid = 0, eid = 100, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C2Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 014 函数join规则中，输出表为外部表
TEST_F(DtlogExTable, DataLog_015_002_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_rule_function.so";
    char libName[] = "external_rule_function";
    char nsName[] = "external_rule_function";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4_all.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA");
    sid = 0, eid = 100, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C2Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 100;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C2Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 015 NOTjoin规则中，输出表为外部表
TEST_F(DtlogExTable, DataLog_015_002_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_rule_notjoin.so";
    char libName[] = "external_rule_notjoin";
    char nsName[] = "external_rule_notjoin";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4_all.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA");
    sid = 0, eid = 100, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C2Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 100;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C2Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    sid = 0, eid = 100, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C2Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 016 相同主键一条记录时，验证触发规则写外部表的merge行为
TEST_F(DtlogExTable, DataLog_015_002_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);    // insert
    int objInLen = 10;
    C2Int4T objIn[objInLen] = {};
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = i;
        objIn[i].a[1] = i;
        objIn[i].dtlReservedCount = 1;
    }
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + objInLen);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    int objOutLen = 10;
    C2Int4T objOut[objOutLen] = {};
    for (int i = 0; i < objOutLen; i++) {
        objOut[i].a[0] = i;
        objOut[i].a[1] = i;
        objOut[i].dtlReservedCount = 0;
    }
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, objOutLen, C2Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // update
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = i;
        objIn[i].a[1] = i + 1;
        objIn[i].dtlReservedCount = 1;
    }
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + objInLen);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    for (int i = 0; i < objOutLen; i++) {
        objOut[i].a[0] = i;
        objOut[i].a[1] = i + 1;
        objOut[i].dtlReservedCount = 0;
    }
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, objOutLen, C2Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // delete
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = i;
        objIn[i].a[1] = i + 1;
        objIn[i].dtlReservedCount = -1;
    }
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + objInLen);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, 0, C2Int4Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 017 相同主键多条记录时，验证触发规则写外部表的merge行为
TEST_F(DtlogExTable, DataLog_015_002_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);    // insert '+' count
    int objInLen = 10;
    C2Int4T objIn[objInLen] = {};
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = 1;
        objIn[i].a[1] = i;
        objIn[i].dtlReservedCount = 1;
    }
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set);
    if (ret != GMERR_PRIMARY_KEY_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    int objOutLen = 10;
    C2Int4T objOut[objOutLen] = {};
    for (int i = 0; i < objOutLen; i++) {
        objOut[i].a[0] = i;
        objOut[i].a[1] = i;
        objOut[i].dtlReservedCount = 0;
    }
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, 1, C2Int4Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // insert '-' count
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = 1;
        objIn[i].a[1] = i;
        objIn[i].dtlReservedCount = -1;
    }
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set);
    if (ret != GMERR_PRIMARY_KEY_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, 0, C2Int4Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // insert '+' or '-' count
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = 1;
        objIn[i].a[1] = i;
        objIn[i].dtlReservedCount = (i % 2 == 0) ? 1 : -1;
    }
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set);
    if (ret != GMERR_PRIMARY_KEY_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, 1, C2Int4Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 018 相同主键一条记录时，验证触发规则写外部表的推送行为
TEST_F(DtlogExTable, DataLog_015_002_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConn;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *sub_info = NULL;
    readJanssonFile((char *)"schema_file/pub_outA01.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_outA01";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback_external, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);    // insert
    int objInLen = 10;
    C2Int4T objIn[objInLen] = {};
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = i;
        objIn[i].a[1] = i;
        objIn[i].dtlReservedCount = 1;
    }
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + objInLen);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    int objOutLen = 10;
    C2Int4T objOut[objOutLen] = {};
    for (int i = 0; i < objOutLen; i++) {
        objOut[i].a[0] = i;
        objOut[i].a[1] = i;
        objOut[i].dtlReservedCount = 0;
    }
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, objOutLen, C2Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_MERGE_INSERT, objOutLen, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // update
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = i;
        objIn[i].a[1] = i + 1;
        objIn[i].dtlReservedCount = 1;
    }
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + objInLen);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    for (int i = 0; i < objOutLen; i++) {
        objOut[i].a[0] = i;
        objOut[i].a[1] = i + 1;
        objOut[i].dtlReservedCount = 0;
    }
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, objOutLen, C2Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_MERGE_UPDATE, objOutLen, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // delete
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = i;
        objIn[i].a[1] = i + 1;
        objIn[i].dtlReservedCount = -1;
    }
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + objInLen);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, 0, C2Int4Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_DELETE, objOutLen, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(userData->data);
    free(userData);

    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 019 相同主键多条记录时，验证触发规则写外部表的推送行为
TEST_F(DtlogExTable, DataLog_015_002_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_PRIMARY_KEY_VIOLATION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConn;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *sub_info = NULL;
    readJanssonFile((char *)"schema_file/pub_outA01.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_outA01";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback_external, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);    // insert '+' count
    int objInLen = 10;
    C2Int4T objIn[objInLen] = {};
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = 1;
        objIn[i].a[1] = i;
        objIn[i].dtlReservedCount = 1;
    }
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set);
    if (ret != GMERR_PRIMARY_KEY_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    int objOutLen = 10;
    C2Int4T objOut[objOutLen] = {};
    for (int i = 0; i < objOutLen; i++) {
        objOut[i].a[0] = i;
        objOut[i].a[1] = i;
        objOut[i].dtlReservedCount = 0;
    }
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, 1, C2Int4Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // insert '-' count
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = 1;
        objIn[i].a[1] = i;
        objIn[i].dtlReservedCount = -1;
    }
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set);
    if (ret != GMERR_PRIMARY_KEY_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, 0, C2Int4Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_DELETE, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // insert '+' or '-' count
    for (int i = 0; i < objInLen; i++) {
        objIn[i].a[0] = 1;
        objIn[i].a[1] = i;
        objIn[i].dtlReservedCount = (i % 2 == 0) ? 1 : -1;
    }
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecord(conn, stmt, labelName_in, objIn, objInLen, C2Int4Set);
    if (ret != GMERR_PRIMARY_KEY_VIOLATION) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = TestWaitDatalogQueue(uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + 1);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = readRecord(conn, stmt, labelName_out, objOut, 1, C2Int4Get, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_MERGE_INSERT, 0, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(userData->data);
    free(userData);

    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 020 加载多个so文件，不同的so操作同一个外部表(同一个namespace)
TEST_F(DtlogExTable, DataLog_015_002_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath1[] = "prefile/external_index0.so";
    char libName1[] = "external_index0";
    char libNamePath2[] = "prefile/external_index0_same.so";
    char libName2[] = "external_index0_same";
    char nsName[] = "same_ns";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName1);
    ret = TestLoadDatalog(libNamePath1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmimport -c datalog -f %s -ns %s\n", g_toolPath, libNamePath2,
        g_testNameSpace);
    ret = Debug_executeCommand(g_command, "Import datalog file unsucc", "ret = 1009013");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName1));
    }
}
// 021 加载多个so文件，不同的so操作同一个外部表(不同namespace)
TEST_F(DtlogExTable, DataLog_015_002_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath1[] = "prefile/external_index0.so";
    char libName1[] = "external_index0";
    char nsName1[] = "external_index0";
    char libNamePath2[] = "prefile/external_index0_same.so";
    char libName2[] = "external_index0_same";
    char nsName2[] = "external_index0_same";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName1);
    ret = TestLoadDatalog(libNamePath1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    (void)TestUninstallDatalog(libName2);
    ret = TestLoadDatalog(libNamePath2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 1000, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA2");
    sid = 1000, eid = 2000, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 2000;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C2Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName1));
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName2));
    }
}
// 022 删除外部表后，触发规则写外部表
TEST_F(DtlogExTable, DataLog_015_002_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 1000, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_UNDEFINED_TABLE, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, 0, uData->outputQueue.failTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 023 删除外部表后，卸载包含外部表的so
TEST_F(DtlogExTable, DataLog_015_002_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// DTS2024121919830问题单用例加固
// 024 schema中未定义主键，.d中定义主键，加载so
TEST_F(DtlogExTable, DataLog_015_002_024)
{
    AddWhiteList(GMERR_DATA_EXCEPTION);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char soPath[] = "prefile/external_table.so";
    char soName[] = "external_table";
    char labelName[] = "B";

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    char *schema = NULL;
    GmcDropVertexLabel(stmt, labelName);
    readJanssonFile("schema_file/B.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    (void)snprintf(g_command, 1024, "%s/gmimport -s %s -c datalog -f %s -ns %s", g_toolPath, g_connServer, soPath,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcDropVertexLabel(stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 001 通过全表扫描的方式读外部表
TEST_F(DtlogExTable, DataLog_015_003_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 1000, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 1000;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C2Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 002 通过主键索引的方式读外部表
TEST_F(DtlogExTable, DataLog_015_003_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 1000, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);    sid = 0, eid = 1000, count = 1;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_out, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = sid; i < eid; i++) {
        int32_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pk, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish;
        int cnt = 0;
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = C2Int4GetId(stmt, pk, pk + 1, 0);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            cnt++;
        }
        AW_MACRO_EXPECT_EQ_INT(1, cnt);
    }

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 003 通过自定义索引的方式读外部表
TEST_F(DtlogExTable, DataLog_015_003_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 1000, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);    sid = 0, eid = 1000, count = 1;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_out, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = sid; i < eid; i++) {
        int32_t pk = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pk, sizeof(int32_t));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "local");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isFinish;
        int cnt = 0;
        while (true) {
            ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            if (isFinish) {
                break;
            }
            ret = C2Int4GetId(stmt, pk, pk + 1, 0);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            cnt++;
        }
        AW_MACRO_EXPECT_EQ_INT(1, cnt);
    }

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 004 通过显示事务的方式触发规则写外部表
TEST_F(DtlogExTable, DataLog_015_003_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    ret = GmcTransStart(conn, &config);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int sid, eid, count, stepCnt;
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 1000, count = 1;
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false, false, -1, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcTransRollBack(conn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 1000;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C2Int4GetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
}
// 005 没有授予外部表写对象权限，触发规则写外部表预期失败
TEST_F(EMPTY_test, DataLog_015_003_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/modifyCfg.sh \"userPolicyMode=2\" ");
    system("sh ${TEST_HOME}/tools/start.sh");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh ${TEST_HOME}/tools/modifyCfg.sh recover");
    
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertexWithout.gmpolicy";

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);

    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "test_priv";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, ObjPrivFile);
    system(g_command);    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 1000, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 0;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C2Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    ret = GmcDropVertexLabel(stmt, labelName_out);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (!(g_usrMode)) {
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, ObjPrivFile,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath,
        allowListFile, g_connServer);
    system(g_command);
    system("sh ${TEST_HOME}/tools/modifyCfg.sh recover");
    testEnvClean();
}
// 006 授予外部表写对象权限，触发规则写外部表预期成功
TEST_F(EMPTY_test, DataLog_015_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/modifyCfg.sh \"userPolicyMode=2\" ");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh ${TEST_HOME}/tools/modifyCfg.sh recover");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char allowListFile[128] = "privFile/allow_list.gmuser";
    char SysPrivFile[128] = "privFile/sysVertex.gmpolicy";
    char ObjPrivFile[128] = "privFile/ObjVertex.gmpolicy";

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_allowlist -f %s", g_toolPath, allowListFile);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, SysPrivFile);
    system(g_command);

    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "test_priv";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, ObjPrivFile);
    system(g_command);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c import_policy  -f %s", g_toolPath, ObjPrivFile);
    system(g_command);    int sid, eid, count, stepCnt;
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 1000, count = 1;
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabel(stmt, labelName_out));
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, ObjPrivFile,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, sizeof(g_command), "%s/gmrule -c remove_allowlist -f %s -s %s ", g_toolPath,
        allowListFile, g_connServer);
    system(g_command);
    system("sh ${TEST_HOME}/tools/modifyCfg.sh recover");
    testEnvClean();
}
int C2Int4SetId_003_007(GmcStmtT *stmt, int64_t value, int32_t count)
{
    C2Int4T obj = {};
    obj.a[0] = (int32_t)value;
    obj.a[1] = (int32_t)value + 1;
    obj.dtlReservedCount = count;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a0", GMC_DATATYPE_INT32, &obj.a[0], sizeof(obj.a[0]));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C2Int4SetId_003_007] a0: %d, ret = %d.", obj.a[0], ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT32, &obj.a[1], sizeof(obj.a[1]));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C2Int4SetId_003_007] a1: %d, ret = %d.", obj.a[1], ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj.dtlReservedCount, sizeof(obj.dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C2Int4SetId_003_007] count: %d, ret = %d.", obj.dtlReservedCount, ret);
        return ret;
    }
    return ret;
}
// 007 外部表表升级成功后，触发datalog规则预期失败
TEST_F(DtlogExTable, DataLog_015_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 表升级
    (void)snprintf(g_command, sizeof(g_command), "%s/gmddl  -c alter -t outA01 -u online -f %s -ns %s", g_toolPath,
        "schema_file/extern_outA01_C2Int4_Upgrade.gmjson", g_testNameSpace);
    system(g_command);    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 10, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 10;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C2Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // V5接口更新升级后的表的新增字段
    sid = 0, eid = 10;
    uint32_t schemaVersion = 2;
    // 变更为更新指定版本
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, labelName_out, schemaVersion, GMC_OPERATION_UPDATE);
    EXPECT_EQ(GMERR_OK, ret);
    for (int i = sid; i < eid; i++) {
        int32_t pk_value = i;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &pk_value, sizeof(pk_value));
        EXPECT_EQ(GMERR_OK, ret);
        int32_t a2_value = i;
        ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT32, &a2_value, sizeof(a2_value));
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcSetIndexKeyName(stmt, "PK");
        EXPECT_EQ(GMERR_OK, ret);
        ret = GmcExecute(stmt);
        EXPECT_EQ(GMERR_OK, ret);
    }

    // 触发datalog规则更新

    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA01");
    sid = 0, eid = 10, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C2Int4SetId_003_007, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);

    if (!(g_usrMode)) {
        snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabel(stmt, labelName_out));
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
int checkAccountStatus(GmcStmtT *stmt, char *labelName)
{
    int ret = 0;
    GmcCheckInfoT *checkInfo;
    ret = GmcGetCheckInfo(stmt, labelName, GMC_FULL_TABLE, &checkInfo);
    EXPECT_EQ(GMERR_OK, ret);
    GmcCheckStatusE checkStatus;
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    EXPECT_EQ(GMERR_OK, ret);
    EXPECT_EQ(checkStatus, GMC_CHECK_STATUS_NORMAL);
    return ret;
}
// 008 对账老化后，触发datalog规则写表
TEST_F(DtlogExTable, DataLog_015_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 1000, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, uData->outputQueue.sucessTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 1000;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 0, C2Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 对账老化
    ret = GmcBeginCheck(stmt, labelName_out, 0xff);
    EXPECT_EQ(GMERR_OK, ret);
    ret = GmcEndCheck(stmt, labelName_out, 0xff, false);
    EXPECT_EQ(GMERR_OK, ret);
    ret = checkAccountStatus(stmt, labelName_out);
    EXPECT_EQ(GMERR_OK, ret);

    sleep(10);
    system("gmsysview -q V\\$QRY_AGE_TASK");
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    sid = 0, eid = 0;
    ret = readRecordId(conn, stmt, labelName_out, sid, eid, 1, C2Int4GetId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabel(stmt, labelName_out));
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(uData);
}
void snCallback_externalDelay(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    usleep(100000);
}
// 009 可靠订阅至触发反压 
// 2025.4.16 当前外部表会触发流控会阻塞订阅接收速率
TEST_F(EMPTY_test, DataLog_015_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/modifyCfg.sh \"clientServerFlowControl=0\" ");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh ${TEST_HOME}/tools/modifyCfg.sh recover");
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg, errorMsg1, errorMsg2, errorMsg3);
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConn;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *sub_info = NULL;
    readJanssonFile((char *)"schema_file/pub_outA01.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_outA01";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback_externalDelay, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 100000, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret);
    system("gmsysview count");

    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(userData->data);
    free(userData);
    free(uData);

    if (!(g_usrMode)) {
        snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabel(stmt, labelName_out));
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("sh ${TEST_HOME}/tools/modifyCfg.sh recover");
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
// 010 升级后加载成功，降级后触发规则写表
TEST_F(DtlogExTable, DataLog_015_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};
    uint32_t schemaVersion = 0;
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_INVALID_PROPERTY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_v0.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    // 表升级
    (void)snprintf(g_command, sizeof(g_command), "%s/gmddl  -c alter -t outA01 -u online -f %s -ns %s", g_toolPath,
        "schema_file/extern_outA01_v1.gmjson", g_testNameSpace);
    system(g_command);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 表降级
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    schemaVersion = 0;
    char *expectValue2 = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName_out, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int sid, eid, count, stepCnt;
    DatalogUserDataT *uData = (DatalogUserDataT *)malloc(sizeof(DatalogUserDataT));
    snprintf(labelName_in, sizeof(labelName_in), "%s", "inA1");
    sid = 0, eid = 10, count = 1;
    (void)TestWaitDatalogQueue(uData, 1);
    ret = writeRecordId(conn, stmt, labelName_in, sid, eid, count, C1Int4SetId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PROPERTY, ret);
    ret = TestWaitDatalogQueue(
        uData, DATALOG_QUEUE_WAIT_TIMES, OUTPUT_EXEC, 0, uData->outputQueue.failTimes + (eid - sid));
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(uData);

    if (!(g_usrMode)) {
        snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabel(stmt, labelName_out));
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 011 升级后加载失败，降级后加载成功
TEST_F(DtlogExTable, DataLog_015_003_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char libNamePath[] = "prefile/external_index0.so";
    char libName[] = "external_index0";
    char nsName[] = "external_index0";
    char labelName_in[64] = {};
    char labelName_mid[64] = {};
    char labelName_out[64] = {};
    uint32_t schemaVersion = 0;
    const int errCodeLen = 1024;
    char errorMsg[errCodeLen] = {0};
    (void)snprintf(errorMsg, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *schema = NULL;
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    GmcDropVertexLabel(stmt, labelName_out);
    readJanssonFile("schema_file/extern_outA01_C2Int4.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"isFastReadUncommitted\":false}";
    ret = GmcCreateVertexLabel(stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);

    // 表升级
    (void)snprintf(g_command, sizeof(g_command), "%s/gmddl  -c alter -t outA01 -u online -f %s -ns %s", g_toolPath,
        "schema_file/extern_outA01_C2Int4_Upgrade.gmjson", g_testNameSpace);
    system(g_command);

    (void)TestUninstallDatalog(libName);
    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

    // 表降级
    snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
    char *expectValue2 = (char *)"degrade successfully";
    ret = TestDownGradeVertexLabel(labelName_out, schemaVersion, expectValue2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestLoadDatalog(libNamePath);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    if (!(g_usrMode)) {
        snprintf(labelName_out, sizeof(labelName_out), "%s", "outA01");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, GmcDropVertexLabel(stmt, labelName_out));
        AW_MACRO_EXPECT_EQ_INT(0, TestUninstallDatalog(libName));
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
