/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: PrePostTest4.cpp
 * Description: 输出表前后支持udf调用
 * Author: <PERSON><PERSON><PERSON><PERSON> ywx1252574
 * Create: 2024-03-12
 */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "t_rd_sn.h"
#include "DatalogRunEnhance.h"

#define MAX_CMD_SIZE 1024


class PrePostTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase()
    {

    }
    static void TearDownTestCase()
    {
        
    }

};
void PrePostTest::SetUp()
{
    int ret = 0;
    system("rm -rf /root/_datalog_");
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void PrePostTest::TearDown()
{
    AW_CHECK_LOG_END();
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
// 053.强鉴权模式下，不赋予pre_invoke/post_invoke函数权限，写入数据
TEST_F(PrePostTest, DataLog_074_004_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFile4/policy53.d";
    char libName[] = "datalogFile4/policy53.so";
    char soName[] = "policy53";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    system("sh stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmrule -c import_allowlist -f datalogFile4/allow_list.gmuser");
    system("gmrule -c import_policy -f datalogFile4/noinvoke.gmpolicy");

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,1,4};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    ret = executeCommand(g_command, "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 054.强鉴权模式下，赋予相关权限，写入数据
TEST_F(PrePostTest, DataLog_074_004_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFile4/policy53.d";
    char libName[] = "datalogFile4/policy53.so";
    char soName[] = "policy53";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    system("sh stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmrule -c import_allowlist -f datalogFile4/allow_list.gmuser");
    system("gmrule -c import_policy -f datalogFile4/allpolicy.gmpolicy");

    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,1,4};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 055.弱鉴权模式下，不赋予pre_invoke/post_invoke权限，写入数据
TEST_F(PrePostTest, DataLog_074_004_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFile4/policy53.d";
    char libName[] = "datalogFile4/policy53.so";
    char soName[] = "policy53";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    system("sh stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=1\"");
    system("start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmrule -c import_allowlist -f datalogFile4/allow_list.gmuser");
    system("gmrule -c import_policy -f datalogFile4/noinvoke.gmpolicy");

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,1,4};

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    // 查看视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat $TEST_HOME/log/run/rgmserver/rgmserver.log");
    ret = executeCommand(g_command, " GMERR-1018004,","root : root : PrePostTest4",
        "operation continue in tolerant mode when check udf privilege.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 056.弱鉴权模式下，赋予相关权限，写入数据
TEST_F(PrePostTest, DataLog_074_004_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFile4/policy53.d";
    char libName[] = "datalogFile4/policy53.so";
    char soName[] = "policy53";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    system("stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=1\"");
    system("start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmrule -c import_allowlist -f datalogFile4/allow_list.gmuser");
    system("gmrule -c import_policy -f datalogFile4/allpolicy.gmpolicy");

    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,1,4};

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    ret = executeCommand(g_command, "\"a\": 1","\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 057.强鉴权模式下，单独对pre_invoke函数赋予权限，写入数据
TEST_F(PrePostTest, DataLog_074_004_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFile4/policy53.d";
    char libName[] = "datalogFile4/policy53.so";
    char soName[] = "policy53";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    system("stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=2\"");
    system("start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmrule -c import_allowlist -f datalogFile4/allow_list.gmuser");
    system("gmrule -c import_policy -f datalogFile4/noinvoke.gmpolicy");
    
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,1,4};
    system("gmsysview -q V\\$CATA_UDF_INFO");
    system("gmrule -c import_policy -f datalogFile4/tarpre.gmpolicy");

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    ret = executeCommand(g_command, "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 058.弱鉴权模式下，单独对pre_invoke函数赋予权限，写入数据
TEST_F(PrePostTest, DataLog_074_004_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFile4/policy53.d";
    char libName[] = "datalogFile4/policy53.so";
    char soName[] = "policy53";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    system("sh stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=1\"");
    system("start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmrule -c import_allowlist -f datalogFile4/allow_list.gmuser");
    system("gmrule -c import_policy -f datalogFile4/noinvoke.gmpolicy");
    
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,1,4};
    system("gmsysview -q V\\$CATA_UDF_INFO");
    system("gmrule -c import_policy -f datalogFile4/tarpre.gmpolicy");

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    ret = executeCommand(g_command, "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat $TEST_HOME/log/run/rgmserver/rgmserver.log");
    ret = executeCommand(g_command, " GMERR-1018004,","root : root : PrePostTest4",
        "operation continue in tolerant mode when check udf privilege.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 059.弱鉴权模式下，单独对post_invoke函数赋予权限，写入数据
TEST_F(PrePostTest, DataLog_074_004_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "datalogFile4/policy53.d";
    char libName[] = "datalogFile4/policy53.so";
    char soName[] = "policy53";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    system("sh stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"userPolicyMode=1\"");
    system("start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmrule -c import_allowlist -f datalogFile4/allow_list.gmuser");
    system("gmrule -c import_policy -f datalogFile4/noinvoke.gmpolicy");
    
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_INSUFFICIENT_PRIVILEGE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    ret = TestLoadDatalog(libName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,1,4};
    system("gmsysview -q V\\$CATA_UDF_INFO");
    system("gmrule -c import_policy -f datalogFile4/tarpost.gmpolicy");

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    // 插入数据
    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看视图
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s",&labelName_out);
    ret = executeCommand(g_command, "\"b\": 4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat $TEST_HOME/log/run/rgmserver/rgmserver.log");
    ret = executeCommand(g_command, " GMERR-1018004,","root : root : PrePostTest4",
        "operation continue in tolerant mode when check udf privilege.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 060.配置为不使用udf时，写入数据
TEST_F(PrePostTest, DataLog_074_004_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    uint32_t ret = 0;
    char fileName[] = "datalogFile4/policy53.d";
    char libName[] = "datalogFile4/policy53.so";
    char soName[] = "policy53";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    system("sh stop.sh -f");
    system("sh $TEST_HOME/tools/modifyCfg.sh  \"udfEnable=0\"");
    system("start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -f %s",&libName);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1003000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
