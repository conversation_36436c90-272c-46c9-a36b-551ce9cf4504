/* ****************************************************************************
 Description  : datalog动态卸载
 Node      :

 Author       : 杨柠蔚 ywx1037054
 Modification :
 Date         : 2022/10/12
 node : 
**************************************************************************** */

extern "C" {
}

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "DatalogUninstall.h"

class DatalogUninstall : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret;
        char result[64] = { 0 };
        ret = GetPrintBycmd("cat sysconfig.txt |grep libDir |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, g_libDir, sizeof(g_libDir));
        EXPECT_EQ(0, ret);
        ret = GetPrintBycmd("cat sysconfig.txt |grep runMode |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, result, 64);
        EXPECT_EQ(0, ret);
        SystemSnprintf("mkdir -p %s", g_libDir);
        if (strncmp(result, "check", 6) == 0) {
            g_usrMode = 0;
        } else if (strncmp(result, "debug", 6) == 0) {
            g_usrMode = 1;
        }
    }
    static void TearDownTestCase()
    {
        SystemSnprintf("rm -rf %s", g_libDir);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DatalogUninstall::SetUp()
{
    if (g_usrMode == 1) {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/start.sh");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    int ret = 0;
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void DatalogUninstall::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcDetachAllShmSeg ();
    testEnvClean();
    system("modifyCfg.sh recover");
}

class DatalogUninstallBig : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret;
        char result[64] = { 0 };
        ret = GetPrintBycmd("cat sysconfig.txt |grep libDir |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, g_libDir, sizeof(g_libDir));
        EXPECT_EQ(0, ret);
        ret = GetPrintBycmd("cat sysconfig.txt |grep runMode |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, result, 64);
        EXPECT_EQ(0, ret);
        SystemSnprintf("mkdir -p %s", g_libDir);
        if (strncmp(result, "check", 6) == 0) {
            g_usrMode = 0;
        } else if (strncmp(result, "debug", 6) == 0) {
            g_usrMode = 1;
        }
    }
    static void TearDownTestCase()
    {
        SystemSnprintf("rm -rf %s", g_libDir);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DatalogUninstallBig::SetUp()
{
    int ret = 0;
    if (g_usrMode == 1) {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=4096\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=2048\"");
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=4096\"");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=2048\"");
        system("sh $TEST_HOME/tools/start.sh");
    }
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void DatalogUninstallBig::TearDown()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    GmcDetachAllShmSeg ();
    
    system("modifyCfg.sh recover");
}

class DatalogUninstallmem : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret;
        char result[64] = { 0 };
        ret = GetPrintBycmd("cat sysconfig.txt |grep libDir |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, g_libDir, sizeof(g_libDir));
        EXPECT_EQ(0, ret);
        ret = GetPrintBycmd("cat sysconfig.txt |grep runMode |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, result, 64);
        EXPECT_EQ(0, ret);
        SystemSnprintf("mkdir -p %s", g_libDir);
        if (strncmp(result, "check", 6) == 0) {
            g_usrMode = 0;
        } else if (strncmp(result, "debug", 6) == 0) {
            g_usrMode = 1;
        }
    }
    static void TearDownTestCase()
    {
        SystemSnprintf("rm -rf %s", g_libDir);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DatalogUninstallmem::SetUp()
{
    int ret = 0;
    if (g_usrMode == 1) {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogQueueMaxDynaMemSize=128\"");
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"datalogQueueMaxDynaMemSize=128\"");
        system("sh $TEST_HOME/tools/start.sh");
    }
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void DatalogUninstallmem::TearDown()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
    GmcDetachAllShmSeg ();
   
    system("modifyCfg.sh recover");
}

class DatalogUninstallDefault : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        int ret;
        char result[64] = { 0 };
        ret = GetPrintBycmd("cat sysconfig.txt |grep libDir |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, g_libDir, sizeof(g_libDir));
        EXPECT_EQ(0, ret);
        ret = GetPrintBycmd("cat sysconfig.txt |grep runMode |awk -F '[:]' '{print $2}' |sed 's/ //g'",
            NULL, result, 64);
        EXPECT_EQ(0, ret);
        SystemSnprintf("mkdir -p %s", g_libDir);
        if (strncmp(result, "check", 6) == 0) {
            g_usrMode = 0;
        } else if (strncmp(result, "debug", 6) == 0) {
            g_usrMode = 1;
        }
    }
    static void TearDownTestCase()
    {
        SystemSnprintf("rm -rf %s", g_libDir);
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DatalogUninstallDefault::SetUp()
{

}

void DatalogUninstallDefault::TearDown()
{
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    testEnvClean();
    GmcDetachAllShmSeg ();
}

/*******************************************************************************
100:加载常规表后卸载
    $CATA_VERTEX_LABEL_INFO" 视图等同于普通表
    $CATA_NAMESPACE_INFO" 可检测到NAMESPACE_NAME
    $DATALOG_PLAN_EXPLAIN_INFO" 全有
    $STORAGE_FSM_STAT"LABEL变多,没有实际指明的字段可以砍掉
    $STORAGE_HEAP_STAT"LABEL变多
    $STORAGE_ART_INDEX_STAT"无
    $STORAGE_INDEX_GLOBAL_STAT"INDEX_PK_CACHELINE_LENGTH主键索引的最大冲突链长度为什么会增加 无
    $STORAGE_HASH_CLUSTER_INDEX_STAT"与 STORAGE_HASH_INDEX_STAT 重复取消 插入数据前后无变化。启动加载有变化
    $STORAGE_HASH_COLLISION_STAT"HASH_INSERT_COUNT: 11插入数据 实际意义不大
    $STORAGE_HASH_INDEX_STAT"整体 
    $STORAGE_HASH_LINKLIST_INDEX_STAT"不显示
*******************************************************************************/

// 001. 加载只指定so名称, 在配置项中指定路径, 预期成功(本用例IOT不更改配置项，需要手动挪移文件确认HPE服务so位置)
TEST_F(DatalogUninstallDefault, DataLog_009_001_001)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "test start.");
    if (g_usrMode == 1) {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"defaultDatalogDynLibLoc=.\\/..\\/..\\/..\\/test\\/sdv\\/testcases\\/16_DataLog\\/009_datalogUninstall\\/loadlib/\"");
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"defaultDatalogDynLibLoc=.\\/..\\/..\\/..\\/test\\/sdv\\/testcases\\/16_DataLog\\/009_datalogUninstall\\/loadlib/\"");
        system("sh $TEST_HOME/tools/start.sh");
    }
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, MAX_CMD_SIZE);
    ret = TestLoadDatalog("base_prefile/normal.so");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ERROR]datalog -f ERROR\n");
    }
    memset(g_command, 0, MAX_CMD_SIZE);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
}
// 002. 加载只指定so名称, 在配置项中指定路径, 总长度超过256, 预期失败
TEST_F(DatalogUninstallDefault, DataLog_009_001_002)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "test start.");
    char fileName[220] = {0};
    char address[512] = {0};
    char cmdMkdir[512] = {0};
    memset(fileName, 'a', 218);
    fileName[219] = '\0';
    snprintf((char *)address, 512, "sh $TEST_HOME/tools/modifyCfg.sh  "
    "\"defaultDatalogDynLibLoc=.\\/..\\/..\\/..\\/test\\/sdv\\/testcases\\/16_DataLog\\/009_datalogUninstall\\/%s/\"", (char *)fileName);
    snprintf(cmdMkdir, 512, "mkdir %s", fileName);
    system(cmdMkdir);
    if (g_usrMode == 1) {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system(address);
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system(address);
        system("sh $TEST_HOME/tools/start.sh");
    }
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmimport -c datalog -f base_prefile/normal.so >202.log");
    ret = executeCommand((char *)"cat ./202.log", (char *)"2002");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf ./202.log");
    system("rm -rf aaaa*");
    
}
// 003. 加载只指定so名称, 在配置项中指定的路径不存在, 预期失败
TEST_F(DatalogUninstallDefault, DataLog_009_001_003)
{
    int ret = 0;
    AW_FUN_Log(LOG_STEP, "test start.");
    if (g_usrMode == 1) {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"defaultDatalogDynLibLoc=.\\/bin\\/loadliberror/\"");
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"defaultDatalogDynLibLoc=.\\/bin\\/loadliberror/\"");
        system("sh $TEST_HOME/tools/start.sh");
    }
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmimport -c datalog -f normal.so >203.log");
    ret = executeCommand((char *)"cat ./203.log", (char *)"13000");
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "datalog -f ERROR\n");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf ./203.log");
}
// 004. 在配置项中指定的路径中不存在指定的so名称, 预期失败
TEST_F(DatalogUninstallDefault, DataLog_009_001_004)
{
    int ret = 0;
    if (g_usrMode == 1) {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"defaultDatalogDynLibLoc=.\\/..\\/..\\/..\\/test\\/sdv\\/testcases\\/16_DataLog\\/009_datalogUninstall\\/loadlib/\"");
        system("sh $TEST_HOME/tools/start.sh");
    } else {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh  \"defaultDatalogDynLibLoc=.\\/..\\/..\\/..\\/test\\/sdv\\/testcases\\/16_DataLog\\/009_datalogUninstall\\/loadlib/\"");
        system("sh $TEST_HOME/tools/start.sh");
    }
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmimport -c datalog -f base_prefile/error.so >204.log");
    ret = executeCommand((char *)"cat ./204.log", (char *)"13000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "datalog -f ERROR\n");
    }
    system("rm -rf ./204.log");
}
// 005. -d指定的参数为空
TEST_F(DatalogUninstall, DataLog_009_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "public";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    (void)TestUninstallDatalog("resource");
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *resPoolName1 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(5);


    int writeCount = 11;
    SingleInt4St *Obj_in = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount + 1);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, SingleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount + 1);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = rsc_value;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DoubleInt4St *Obj_out = Obj_mid;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_mid);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_mid, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_mid, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewSource(resPoolName1, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(resPoolName1);
    // 卸载并检查视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -d \n", g_toolPath);
    ret = executeCommand((char *)g_command, "Unsucc to parse option.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)TestUninstallDatalog("resource");
}
// 006. -d指定的文件后缀不为so[11.3更改不使用后缀]
TEST_F(DatalogUninstall, DataLog_009_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "test";

    (void)TestUninstallDatalog("normal");
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(Obj_out);
    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -d normal -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand((char *)g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 007. 指定的so名称为包含路径
TEST_F(DatalogUninstall, DataLog_009_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "test";
    (void)TestUninstallDatalog("normal");

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(Obj_out);
    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -d ./normal", g_toolPath);
    ret = executeCommand((char *)g_command, "ret = 1009001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)TestUninstallDatalog("normal");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 008. 指定的so名称不存在(未加载过), 预期失败
TEST_F(DatalogUninstall, DataLog_009_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "test";
    (void)TestUninstallDatalog("normal");

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool ctlSearch = true;
    free(Obj_out);
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -d ERROR", g_toolPath);
    ret = executeCommand((char *)g_command, "ret = 1001000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)TestUninstallDatalog("normal");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 009. 指定的so名称不存在(加载后已卸载), 预期失败
TEST_F(DatalogUninstall, DataLog_009_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -d normal -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand((char *)g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -d normal -ns %s", g_toolPath, g_testNameSpace);
    ret = executeCommand((char *)g_command, "1000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 010. 指定的so名称含特殊字符, 预期失败
TEST_F(DatalogUninstall, DataLog_009_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "test";

    (void)TestUninstallDatalog("normal");
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(Obj_out);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -d tes*t", g_toolPath);
    ret = executeCommand((char *)g_command, "ret = 1009001");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)TestUninstallDatalog("normal");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 011. 指定的so名称长度超过256, 预期失败
TEST_F(DatalogUninstall, DataLog_009_001_011)
{
    char soName[259] = {0};
    memset(soName, 'a', 257);
    soName[258] = '\0';
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "test";

    (void)TestUninstallDatalog("normal");
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(Obj_out);
    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -d %s", g_toolPath, soName);
    ret = executeCommand((char *)g_command, "Unsucc");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)TestUninstallDatalog("normal");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 012. 指定的so名称为非datalog的ns的名称, 预期失败
TEST_F(DatalogUninstall, DataLog_009_001_012)
{
    char soName[259] = {0};
    memset(soName, 'a', 257);
    soName[258] = '\0';
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1,  g_errorCode02);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "test";

    (void)TestUninstallDatalog("normal");

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool ctlSearch = true;
    free(Obj_out);
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -d public", g_toolPath);
    ret = executeCommand((char *)g_command, "successfully");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    ctlSearch = true;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    (void)TestUninstallDatalog("normal");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 013. 加载常规表后卸载
TEST_F(DatalogUninstall, DataLog_009_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1,  g_errorCode02);
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(Obj_out);
    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 014. 加载transient(tuple)表后卸载定义表在规则中作中间表包含聚合规则
TEST_F(DatalogUninstall, DataLog_009_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1,  g_errorCode02);
    char fileName[] = "base_prefile/transient_tuple_mid.d";
    char udfFileName[] = "base_prefile/transient_tuple_mid_udf.c";
    char libName[] = "base_prefile/transient_tuple_mid.so";
    char nsName[] = "transient_tuple_mid";
    char labelName_in[] = "ns3.C";
    char labelName_mid[] = "ns3.B";
    char labelName_out[] = "ns3.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t groupId = 1;
    int32_t sum = 0;
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = groupId;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
        sum += Obj_in[i].b;
    }
    Obj_out[0].a = groupId;
    Obj_out[0].b = sum;
    Obj_out[0].dtlReservedCount = 1;
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, 0, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("transient_tuple_mid");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 015. 加载transient(field)表后卸载定义表在规则中作中间表多表join规则中包含function
TEST_F(DatalogUninstall, DataLog_009_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1,  g_errorCode02);
    char fileName[] = "base_prefile/transient_field_mid.d";
    char udfFileName[] = "base_prefile/transient_field_mid_udf.c";
    char libName[] = "base_prefile/transient_field_mid.so";
    char nsName[] = "transient_field_mid";
    char labelName_in[] = "ns5.C";
    char labelName_mid[] = "ns5.B";
    char labelName_out[] = "ns5.A";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = 0;
        Obj_mid[i].dtlReservedCount = 1;
    }
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = 0;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_in);
    free(Obj_mid);
    free(Obj_out);

    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("transient_field_mid");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 016. 加载固定资源池表后卸载(资源未分配)
TEST_F(DatalogUninstall, DataLog_009_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1,  g_errorCode02);
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(5);


    ret = TestUninstallDatalog("resource");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(10);

    bool ctlSearch = false;

    ret = Check_sysviewStorage(labelName_mid, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_mid, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewSource(labelName_mid, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 017. 加载固定资源池表后卸载(资源已分配)
TEST_F(DatalogUninstall, DataLog_009_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2,  g_errorCode01, g_errorCode02);
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char *resPoolName1 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(5);


    int writeCount = 11;
    SingleInt4St *Obj_in = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount + 1);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, SingleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    // 当前资源字段无法构造为-1数据
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount + 1);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = rsc_value;
        Obj_mid[i].dtlReservedCount = 1;
    }
    
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DoubleInt4St *Obj_out = Obj_mid;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount - 1, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_mid);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_mid, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_mid, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewSource(resPoolName1, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("resource");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);

    ctlSearch = false;
    ret = Check_sysviewStorage(labelName_mid, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_mid, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewSource(resPoolName1, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(resPoolName1);
}
// 018. 加载pubsub型资源表后卸载
TEST_F(DatalogUninstall, DataLog_009_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/pubsubResource.d";
    char udfFileName[] = "base_prefile/pubsubResource_udf.c";
    char libName[] = "base_prefile/pubsubResource.so";
    char nsName[] = "pubsubResource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    GmcConnT *subConn;
    GmcStmtT *subStmt;
    const char *subConnName = "testSub";
    ret = testSubConnect(&subConn, NULL, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char *sub_info = NULL;
    readJanssonFile((char *)"schemaFile/pubResource_ns1.gmjson", &sub_info);
    EXPECT_NE((void *)NULL, sub_info);
    const char *subName = "testSub_ns1.rsc0";
    GmcSubConfigT tmp_sub_info;
    tmp_sub_info.subsName = subName;
    tmp_sub_info.configJson = sub_info;
    SnUserDataWithFuncT *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData->data, 0, sizeof(SnUserDataT));
    userData->funcType = 0;
    userData->tableType = 1;
    userData->readIdFunc = DoubleInt4_getId100;
    userData->startid = 0;
    userData->endid = 11;
    userData->count = 1;
    ret = GmcSubscribe(stmt, &tmp_sub_info, subConn, snCallback, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info);

    int startid = userData->startid;
    int endid = userData->endid;
    int32_t count = userData->count;
    ret = writeRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_setId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData->data, GMC_SUB_EVENT_INSERT, (endid - startid), RECV_TIMEOUT/10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int readCount = endid - startid;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = -1;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    readCount = endid - startid;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = -1;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = GmcUnSubscribe(stmt, subName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(userData->data);
    free(userData);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_mid, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_mid, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = TestUninstallDatalog("pubsubResource");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);

    ctlSearch = false;
    ret = Check_sysviewStorage(labelName_mid, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_mid, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewSource(labelName_mid, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = testGmcDisconnect(subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 019. 加载function表后卸载
TEST_F(DatalogUninstall, DataLog_009_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("function");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 020. 加载agg表后卸载
TEST_F(DatalogUninstall, DataLog_009_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/aggregate_toMany.d";
    char udfFileName[] = "base_prefile/aggregate_toMany_udf.c";
    char libName[] = "base_prefile/aggregate_toMany.so";
    char nsName[] = "aggregate_toMany";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue(NULL, 2000));
    sleep(2);
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue(NULL, 2000));
    sleep(2);
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    int readCount = sizeof(groupOut) / sizeof(int32_t);
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * readCount);
    for (int i = 0; i < readCount; i++) {
        Obj_out[i].a = (i < readCount/2) ? groupId[0] : groupId[1];
        Obj_out[i].b = (i < readCount/2) ? groupOut[0][i%3] : groupOut[1][i%3];
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_out, Obj_out, readCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("aggregate_toMany");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 021. 包含投影, 连接, 笛卡尔积, not join等所有规则的卸载
TEST_F(DatalogUninstall, DataLog_009_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2,  g_errorCode01, g_errorCode02);
    char fileName[] = "base_prefile/resource_all.d";
    char udfFileName[] = "base_prefile/resource_all_udf.c";
    char libName[] = "base_prefile/resource_all.so";
    char nsName[] = "resource_all";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    SingleInt4St *Obj_in = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, SingleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = (rsc_value < 10) ? rsc_value : -1;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DoubleInt4St *Obj_out = Obj_mid;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    free(Obj_mid);

    char labelName_in11[] = "ns2.A";
    char labelName_in22[] = "ns2.B";
    char labelName_mid2[] = "ns2.rsc0";
    char labelName_out2[] = "ns2.C";

    int writeCount2 = 10;
    DoubleInt4St *Obj_in11 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount2);
    for (int i = 0; i < writeCount2; i++) {
        Obj_in11[i].a = i;
        Obj_in11[i].b = i;
        Obj_in11[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in11, Obj_in11, writeCount2, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in11, Obj_in11, writeCount2, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in11);

    DoubleInt4St *Obj_mid2 = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount2);
    for (int i = 0, rsc_value = 0; i < writeCount2; i++, rsc_value++) {
        Obj_mid2[i].a = i;
        Obj_mid2[i].b = (rsc_value < 10) ? rsc_value : -1;
        Obj_mid2[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid2, Obj_mid2, writeCount2, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid2);

    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("resource_all");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 022. 卸载后操作ns
TEST_F(DatalogUninstall, DataLog_009_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2,  g_errorCode01, g_errorCode02);
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "normal";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_out);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);
    // so ap ac设备，前置用例干扰，nsp 为gmdbtest
#if defined(ENV_RTOSV2X) || defined(ENV_RTOSV2)
#else
    ret = GmcDropNamespace(stmt, nsName);
    AW_MACRO_EXPECT_EQ_INT(1009009, ret);
#endif
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 023. 切换ns后, 卸载再操作表
TEST_F(DatalogUninstall, DataLog_009_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_out);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 024. 卸载后创建订阅[转移]
// 025. 创建订阅后卸载[转移]
// 026. 卸载后, 可创建同名的非datalog的ns
TEST_F(DatalogUninstall, DataLog_009_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool ctlSearch = true;
    free(Obj_out);
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = GmcCreateNamespace(stmt, "normal", "root");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

const char *labelSchemaJson =
    R"([{
        "type":"record",
        "name":"VertexLabel",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":false},
                {"name":"F4", "type":"uint32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"VertexLabel",
                    "name":"PrimaryKey",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"VertexLabel",
                    "name":"LocalHashKey",
                    "fields":["F1"],
                    "index":{"type":"localhash"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"VertexLabel",
                    "name":"HashClusterKey",
                    "fields":["F2"],
                    "index":{"type":"hashcluster"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"VertexLabel",
                    "name":"LocalKey",
                    "fields":["F3"],
                    "index":{"type":"local"},
                    "constraints":{"unique":false}
                }
            ]
        }])";
const char *labelConfigJson = R"({"max_record_count":100000})";
// 027. 卸载后, 可创建同名的非datalog的表
TEST_F(DatalogUninstall, DataLog_009_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "VertexLabel";
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcCreateNamespace(stmt, "test2", "root");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_out);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = GmcCreateVertexLabel(stmt, labelSchemaJson, labelConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog("normal");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 028. 卸载区分大小写的ns
TEST_F(DatalogUninstall, DataLog_009_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    sleep(5);

    int writeCount = 11;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i;
        Obj_out[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount-1, DoubleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(Obj_out);
    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = executeCommand((char *)"gmimport -c datalog -d norMal", "ret = 1001000");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 029. .d文件中包含最大namespace数量(255)卸载
TEST_F(DatalogUninstall, DataLog_009_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/namespace_Maxnum.d";
    char libName[] = "base_prefile/namespace_Maxnum.so";
    char labelName_in[] = "ns1.A0";
    char labelName_out[] = "ns1.B0";

    char nsName[] = "namespace_Maxnum";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog("namespace_Maxnum");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 030. .d文件中包含最大表数量(1024)卸载(包括%table和%resource)[重写]a
TEST_F(DatalogUninstallmem, DataLog_009_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/resource_maxNum.d";
    char libName[] = "base_prefile/resource_maxNum.so";
    char nsName[] = "resource_maxNum";
    char labelName_in[] = "A";
    char labelName_mid[128] = "rsc0000";
    char labelName_out[] = "B";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int64_t startid = 0;
    int64_t endid = 1000;
    int32_t count = 1;
    ret = writeRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_setId, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecordId(conn, stmt, labelName_in, startid, endid, count, SingleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < 998; i++) {
        memset(labelName_mid, 0, sizeof(labelName_mid));
        snprintf(labelName_mid, sizeof(labelName_mid), "rsc%04d", i);
        ret = readRecordId(conn, stmt, labelName_mid, startid, endid, count, DoubleInt4_getId);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    count = 998;
    ret = readRecordId(conn, stmt, labelName_out, startid, endid, count, DoubleInt4_getId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog("resource_maxNum");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 031. .d文件中表包含全类型字段卸载
TEST_F(DatalogUninstall, DataLog_009_001_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/allFieldType.d";
    char libName[] = "base_prefile/allFieldType.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "allFieldType";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog("allFieldType");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 032. .d文件中表包含最大字段数量(32)卸载
TEST_F(DatalogUninstall, DataLog_009_001_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/field_maxNum_32.d";
    char libName[] = "base_prefile/field_maxNum_32.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "field_maxNum_32";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog("field_maxNum_32");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 033. .d文件中表包含最大索引数量(32)卸载
TEST_F(DatalogUninstall, DataLog_009_001_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/index_maxNum.d";
    char libName[] = "base_prefile/index_maxNum.so";
    char labelName_in[] = "ns1.A";
    char labelName_out[] = "ns1.B";

    char nsName[] = "index_maxNum";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog("index_maxNum");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 034. .d文件中表包含最大规则数量(5000)卸载
TEST_F(DatalogUninstall, DataLog_009_001_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/ruleCount_multi.d";
    char libName[] = "base_prefile/ruleCount_multi.so";
    char labelName_in[] = "ns1.A0";
    char labelName_out[] = "ns1.B0";

    char nsName[] = "ruleCount_multi";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog("ruleCount_multi");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 035. 3M的.d文件卸载
TEST_F(DatalogUninstallBig, DataLog_009_001_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/U3M.d";
    char libName[] = "base_prefile/U3M.so";
    char labelName_in[] = "ns1.aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
    "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa0";
    char labelName_out[] = "ns1.B";

    char nsName[] = "U3M";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog("U3M");
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 036. so最大数量(64)卸载
TEST_F(DatalogUninstall, DataLog_009_001_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "normal.so";
    char libName2[128];

    int maxNamespaceNum = 64;
    for (int i = 0; i < maxNamespaceNum + 1; i++) {
        (void)snprintf(libName2, 128, "normal_%d.so", i);
        
        SystemSnprintf("cp base_prefile/%s %s/%s \n", libName, g_libDir, libName2);

        (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s/%s -ns %s\n",
            g_toolPath, g_libDir, libName2, libName2);
        
        if (i == maxNamespaceNum) {
            ret = Debug_executeCommand(g_command,
                "Import datalog file failed, filePath",
                "ret = 1011000");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = Debug_executeCommand(g_command,
                "Command type: import_datalog, Import datalog file",
                "successfully");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    for (int i = 0; i < maxNamespaceNum; i++) {
        (void)snprintf(libName2, 128, "normal_%d", i);
        (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -d %s ", libName2);
        ret = executeCommand(g_command, "successfully");
        AW_MACRO_EXPECT_EQ_INT(0, ret);
    }
    SystemSnprintf("sh getLib.sh %s %s clean \n", fileName, libName);
}
// 037. 加载64个so后, 创建非datalog的ns失败, 卸载后, 可创建64个非datalog的ns
TEST_F(DatalogUninstall, DataLog_009_001_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "normal.so";
    char libName2[128];
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_PROGRAM_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    int maxNamespaceNum = 64;
    for (int i = 0; i < maxNamespaceNum + 1; i++) {
        (void)snprintf(libName2, 128, "normal_%d.so", i);
        
        SystemSnprintf("cp base_prefile/%s %s/%s \n", libName, g_libDir, libName2);

        (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s/%s -ns %s\n",
            g_toolPath, g_libDir, libName2, libName2);
        
        if (i == maxNamespaceNum) {
            ret = Debug_executeCommand(g_command,
                "Import datalog file failed, filePath",
                "ret = 1011000");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = Debug_executeCommand(g_command,
                "Command type: import_datalog, Import datalog file",
                "successfully");
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char nsName[128] = "nsTest";
    char usrName[] = "myName";
    ret = GmcCreateNamespace(stmt, nsName, usrName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_PROGRAM_LIMIT_EXCEEDED, ret);

    for (int i = 0; i < maxNamespaceNum; i++) {
        (void)snprintf(libName2, 128, "normal_%d", i);
        (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -d %s ", libName2);
        ret = executeCommand(g_command, "successfully");
        AW_MACRO_EXPECT_EQ_INT(0, ret);
    }
    SystemSnprintf("sh getLib.sh %s %s clean \n", fileName, libName);
    for (int i = 0; i < maxNamespaceNum; i++) {
        (void)snprintf(libName2, 128, "normal_%d", i);
        ret = GmcCreateNamespace(stmt, libName2, usrName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
const char *labelSchemaJsons =
    R"([{
        "type":"record",
        "name":"vertex%d",
        "fields":
            [
                {"name":"F0", "type":"int32", "nullable":false},
                {"name":"F1", "type":"int32", "nullable":false},
                {"name":"F2", "type":"uint32", "nullable":false},
                {"name":"F3", "type":"uint32", "nullable":false},
                {"name":"F4", "type":"uint32", "nullable":false}
            ],
        "keys":
            [
                {
                    "node":"vertex%d",
                    "name":"PrimaryKey",
                    "fields":["F0"],
                    "index":{"type":"primary"},
                    "constraints":{"unique":true}
                },
                {
                    "node":"vertex%d",
                    "name":"LocalHashKey",
                    "fields":["F1"],
                    "index":{"type":"localhash"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"vertex%d",
                    "name":"HashClusterKey",
                    "fields":["F2"],
                    "index":{"type":"hashcluster"},
                    "constraints":{"unique":false}
                },
                {
                    "node":"vertex%d",
                    "name":"LocalKey",
                    "fields":["F3"],
                    "index":{"type":"local"},
                    "constraints":{"unique":false}
                }
            ]
        }])";
// 038. 加载1024个表后, 创建非datalog的表失败, 卸载后, 可创建1024个非datalog的表
// 当前datalog极限是1000，vertex是2000【2023.3.16】
TEST_F(DatalogUninstall, DataLog_009_001_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/table_maxNum_1.d";
    char libName[] = "base_prefile/table_maxNum_1.so";
    char Schemas[12800];
    char labelName[128];
    char nsName[] = "table_maxNum_1";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcCreateVertexLabel(stmt, labelSchemaJson, labelConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog("table_maxNum_1");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
   for (int i = 0; i < 1024; i++) {
        (void)snprintf(Schemas, 12800, labelSchemaJsons, i, i, i, i, i);
        ret = GmcCreateVertexLabel(stmt, Schemas, labelConfigJson);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
   for (int i = 0; i < 1024; i++) {
        (void)snprintf(labelName, 128, "vertex%d", i);
        ret = GmcDropVertexLabel(stmt, labelName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 039. 加载function表，卸载后直接提交事务[报错]
TEST_F(DatalogUninstall, DataLog_009_001_039)
{
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;

    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";
    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    GmcTransStart(conn, &config);
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_out);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = TestUninstallDatalog("function");
    AW_MACRO_EXPECT_NE_INT(0, ret);
    sleep(5);
    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = GmcTransCommit(conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog("function");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 040. 卸载后继续写数据再提交事务[报错]
TEST_F(DatalogUninstall, DataLog_009_001_040)
{
    GmcTxConfigT config;
    config.transMode = GMC_TRANS_USED_IN_CS;
    config.type = GMC_TX_ISOLATION_SERIALIZABLE;
    config.readOnly = false;
    config.trxType = GMC_PESSIMISITIC_TRX;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNDEFINED_OBJECT);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    (void)TestUninstallDatalog(nsName);

    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    GmcTransStart(conn, &config);
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);
    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    free(Obj_mid);
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog("function");
    AW_MACRO_EXPECT_NE_INT(0, ret);
    free(Obj_out);
    sleep(5);
    writeCount = 25;
    Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 15; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_in);

    Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 15; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    free(Obj_mid);

    Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 15; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_out);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out,  ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ctlSearch = false;

    ret = Check_sysviewStorage(labelName_out, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_out, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = GmcTransCommit(conn);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog("function");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 041. 卸载和卸载并发,同一个so
TEST_F(DatalogUninstall, DataLog_009_001_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("gmsysview -q V$\\CATA_NAMESPACE_INFO");
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    int multiCount = 5;
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh UnistallTest.sh %s %s %d \n", "unistall", "normal", multiCount);
    ret = Debug_executeCommand(g_command, "test finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    system("gmsysview -q V$\\CATA_NAMESPACE_INFO");
}
// 042. 卸载和卸载并发,不同so
TEST_F(DatalogUninstall, DataLog_009_001_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    system("gmsysview -q V$\\CATA_NAMESPACE_INFO");
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char fileName2[] = "base_prefile/allFieldType2.d";
    char libName2[] = "base_prefile/allFieldType2.so";
    char nsName2[] = "allFieldType";
    (void)TestUninstallDatalog(nsName2);
    (void)SystemSnprintf("sh getLib.sh %s", fileName2);
    ret = TestLoadDatalog(libName2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);


    int multiCount = 1;
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh UnistallTest.sh %s %s %d \n", "unistalldiff", "normal", multiCount);
    ret = Debug_executeCommand(g_command, "test finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    system("gmsysview -q V$\\CATA_NAMESPACE_INFO");
}
// 043. 卸载和加载并发,同一个so
TEST_F(DatalogUninstall, DataLog_009_001_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);
    int multiCount = 1;
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh UnistallTest.sh %s %s %d \n", "unst", "normal", multiCount);
    ret = Debug_executeCommand(g_command, "test finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V$\\CATA_NAMESPACE_INFO");
}
// 044. 卸载和加载并发,不同so
TEST_F(DatalogUninstall, DataLog_009_001_044)
{
    char errorMsg1[1024] = {};
    (void)snprintf(errorMsg1, sizeof(errorMsg1), "GMERR-%d", GMERR_DUPLICATE_TABLE);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/normal.d";
    char libName[] = "base_prefile/normal.so";
    char nsName[] = "normal";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    char fileName2[] = "base_prefile/allFieldType.d";
    char libName2[] = "base_prefile/allFieldType.so";
    char nsName2[] = "allFieldType";
    (void)TestUninstallDatalog(nsName2);
    (void)SystemSnprintf("sh getLib.sh %s", fileName2);

    int multiCount = 1;
    (void)snprintf(g_command, MAX_CMD_SIZE, "sh UnistallTest.sh %s %s %d \n", "unstdiff", "normal", multiCount);
    ret = Debug_executeCommand(g_command, "test finish");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmimport -c datalog -d allFieldType");
    system("gmsysview -q V$\\CATA_NAMESPACE_INFO");
}
// 045. 重复卸载10000次
TEST_F(DatalogUninstall, DataLog_009_001_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int writeCount = 10;
    DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].b = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    writeCount = 10;
    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = i;
        Obj_mid[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_mid);

    writeCount = 10;
    DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_out[i].a = i;
        Obj_out[i].b = i + 1;
        Obj_out[i].dtlReservedCount = 1;
    }

    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_out);
    char cmd[256] = "gmimport -c datalog -d function";
    (void)sprintf(cmd, "gmimport -c datalog -d function -ns %s", g_testNameSpace);
    for (int i = 0; i < 100; i++) {
        ret = executeCommand((char *)cmd, "ret = 1001000");
        if (i == 0) {
            AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
        } else {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
// 046. 反复加载卸载10000次(包括资源池, udf等所有资源, 且有预置数据)
TEST_F(DatalogUninstall, DataLog_009_001_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "base_prefile/function.d";
    char udfFileName[] = "base_prefile/function_udf.c";
    char libName[] = "base_prefile/function.so";
    char nsName[] = "function";
    char labelName_in[] = "ns9_rr.A";
    char labelName_mid[] = "ns9_rr.B";
    char labelName_out[] = "ns9_rr.C";

    for (int i = 0; i < 100; i++) {
        (void)TestUninstallDatalog(nsName);
        ret = TestLoadDatalog(libName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcConnT *conn;
        GmcStmtT *stmt;
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        int writeCount = 10;
        DoubleInt4St *Obj_in = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
        for (int i = 0; i < writeCount; i++) {
            Obj_in[i].a = i;
            Obj_in[i].b = i;
            Obj_in[i].dtlReservedCount = 1;
        }
        ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_set);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
        ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, DoubleInt4_get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(Obj_in);

        writeCount = 10;
        DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
        for (int i = 0; i < writeCount; i++) {
            Obj_mid[i].a = i;
            Obj_mid[i].b = i;
            Obj_mid[i].dtlReservedCount = 1;
        }

        ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(Obj_mid);

        writeCount = 10;
        DoubleInt4St *Obj_out = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
        for (int i = 0; i < writeCount; i++) {
            Obj_out[i].a = i;
            Obj_out[i].b = i + 1;
            Obj_out[i].dtlReservedCount = 1;
        }

        ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(Obj_out);
        ret = TestUninstallDatalog("function", NULL, false);
        AW_MACRO_EXPECT_EQ_INT(0, ret);
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
// 047. 1024个表反复加载卸载10000次
TEST_F(DatalogUninstall, DataLog_009_001_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESTRICT_VIOLATION);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode02);
    char fileName[] = "base_prefile/table_maxNum_1.d";
    char libName[] = "base_prefile/table_maxNum_1.so";
    char Schemas[12800];
    char labelName[128];
    char nsName[] = "table_maxNum_1";
    (void)TestUninstallDatalog(nsName);
    for (int i = 0; i < 500; i++) {
        ret = TestLoadDatalog(libName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcConnT *conn;
        GmcStmtT *stmt;
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestUninstallDatalog("table_maxNum_1", NULL, false);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
}
// 048. 满连接时卸载失败, 释放一个连接后卸载成功
TEST_F(DatalogUninstall, DataLog_009_001_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    char g_errorCode03[MAX_CMD_SIZE] = {0};
    char g_errorCode04[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_UNEXPECTED_NULL_VALUE);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_INSUFFICIENT_RESOURCES);
    (void)snprintf(g_errorCode04, MAX_CMD_SIZE, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    AW_ADD_ERR_WHITE_LIST(4, g_errorCode01, g_errorCode02, g_errorCode03, g_errorCode04);
    GmcConnT *conn_t[MAX_CONN_SIZE + 1] = {0};
    GmcStmtT *stmt_t[MAX_CONN_SIZE + 1] = {0};
    int i;
    int value = 0;
#if defined(ENV_RTOSV2X)
    uint32_t existConnNum = 0;
    ret = testGetConnNum(&existConnNum);
    EXPECT_EQ(0, ret);
    value = existConnNum;
#elif defined(ENV_RTOSV2)  // AC环境预留的2个逃生通道普通用户无法使用，报18004
    value = 2;
#endif
    char fileName[] = "base_prefile/table_maxNum_1.d";
    char libName[] = "base_prefile/table_maxNum_1.so";
    char Schemas[12800];
    char labelName[128];
    char nsName[] = "table_maxNum_1";
    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    for (i = 0; i < MAX_CONN_SIZE - value; i++) {
        ret = testGmcConnect(&conn_t[i], &stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        AW_FUN_Log(LOG_ERROR, "connect_1024_002 connect success: %d\n", i);
    }
    ret = TestUninstallDatalog("table_maxNum_1");
    AW_MACRO_EXPECT_NE_INT(0, ret);

    ret = testGmcDisconnect(conn_t[0], stmt_t[0]);
    EXPECT_EQ(GMERR_OK, ret);

    ret = TestUninstallDatalog("table_maxNum_1");
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    for (i = 1; i < MAX_CONN_SIZE - value; i++) {
        ret = testGmcDisconnect(conn_t[i], stmt_t[i]);
        EXPECT_EQ(GMERR_OK, ret);
        AW_FUN_Log(LOG_ERROR, "connect_1024_002 disconnect success: %d\n", i);
    }
}
// 049. 验证审计日志
TEST_F(DatalogUninstall, DataLog_009_001_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char g_errorCode01[MAX_CMD_SIZE] = {0};
    char g_errorCode02[MAX_CMD_SIZE] = {0};
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2,  g_errorCode01, g_errorCode02);
    char fileName[] = "base_prefile/resource.d";
    char udfFileName[] = "base_prefile/resource_udf.c";
    char libName[] = "base_prefile/resource.so";
    char nsName[] = "resource";
    char labelName_in[] = "ns1.A";
    char labelName_mid[] = "ns1.rsc0";
    char labelName_out[] = "ns1.B";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(5);


    int writeCount = 11;
    SingleInt4St *Obj_in = (SingleInt4St *)malloc(sizeof(SingleInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = i;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount - 1, SingleInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in + 10), 1, SingleInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RESOURCE_POOL_NOT_ENOUGH, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    writeCount = 10;
    ret = readRecord(conn, stmt, labelName_in, Obj_in, writeCount, SingleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    DoubleInt4St *Obj_mid = (DoubleInt4St *)malloc(sizeof(DoubleInt4St) * writeCount);
    for (int i = 0, rsc_value = 0; i < writeCount; i++, rsc_value++) {
        Obj_mid[i].a = i;
        Obj_mid[i].b = (rsc_value < 10) ? rsc_value : -1;
        Obj_mid[i].dtlReservedCount = 1;
    }
    ret = readRecord(conn, stmt, labelName_mid, Obj_mid, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    DoubleInt4St *Obj_out = Obj_mid;
    ret = readRecord(conn, stmt, labelName_out, Obj_out, writeCount, DoubleInt4_get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());
    free(Obj_mid);
    bool ctlSearch = true;
    ret = Check_sysviewStorage(labelName_mid, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);

    ret = Check_sysviewIndex(labelName_mid, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    char *resPoolName1 = NULL;
    ret =
        GtExecSysviewCmd(&resPoolName1, "V$STORAGE_RESOURCE_ALL_POOL_STAT", "| grep POOL_NAME | awk NR==1'{print $2}'");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview -q V\\$V$STORAGE_RESOURCE_ALL_POOL_STAT");
    ret = Check_sysviewSource(resPoolName1, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    free(resPoolName1);
    // 卸载并检查视图
    ret = TestUninstallDatalog("resource");
    AW_MACRO_EXPECT_EQ_INT(0, ret);
    sleep(5);

    ctlSearch = false;
    ret = Check_sysviewStorage(labelName_mid, nsName, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewIndex(labelName_mid, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = Check_sysviewSource(labelName_mid, ctlSearch);
    AW_MACRO_EXPECT_EQ_INT(-1, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = executeCommand((char *)"cat $TEST_HOME/log/secure/sgmserver/sgmserver.log","Unload datalog");// 错误日志跳转
    EXPECT_EQ(GMERR_OK, ret);
}
