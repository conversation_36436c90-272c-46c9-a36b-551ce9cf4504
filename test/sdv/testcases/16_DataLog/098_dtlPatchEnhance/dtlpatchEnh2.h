/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name:
 * Description:
 * Author: luyang 00618033
 * Create: 2024-12-30
 */

#ifndef DTLPATCHENH2_H
#define DTLPATCHENH2_H
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"

#define MAX_TRY_COUNT 10

using namespace std;

#if (ENV_RTOSV2X) && (RUN_DATACOM_DAP)
char g_serverlogPath[256] = "/opt/vrpv8/home/<USER>/diag.log";
#else
char g_serverlogPath[256] = "$TEST_HOME/log/run/rgmserver/*";
#endif

int batchSingleWrite(
    GmcStmtT *stmt, GmcConnT *conn, char *labelName, int32_t startNum, int32_t endNum, int32_t dtlReservedCount = 1)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = startNum; i <= endNum; i++) {
        int64_t value = i;
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &value, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlReservedCount, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

int primaryScan(GmcStmtT *stmt, char *labelName, int64_t primaryIndex, int32_t upgradeVersionIndex, int64_t expectValue,
    int32_t expectUpgradeVersionValue = 0, int32_t expectDtlReservedCount = 1, bool isAgg = false)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyName(stmt, "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionIndex, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT64, &primaryIndex, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT64, &primaryIndex, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    bool isFinish = false;
    ret = GmcFetch(stmt, &isFinish);
    EXPECT_EQ(GMERR_OK, ret);

    bool isNull;
    int64_t aValue = 0;
    int64_t bValue = 0;
    int32_t dtlReservedCountValue = 0;
    int64_t cValue = 0;
    int32_t upgradeVersionValue = 0;
    ret = GmcGetVertexPropertyByName(stmt, "a", &aValue, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(expectValue, aValue);
    ret = GmcGetVertexPropertyByName(stmt, "b", &bValue, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(expectValue, bValue);
    ret = GmcGetVertexPropertyByName(stmt, "c", &cValue, sizeof(int64_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
        if (isAgg == true) {
                int64_t expectCValue = 1;
            AW_MACRO_EXPECT_EQ_INT(expectCValue, cValue);
        } else {
    AW_MACRO_EXPECT_EQ_INT(expectValue, cValue);
        }


    ret = GmcGetVertexPropertyByName(stmt, "upgradeVersion", &upgradeVersionValue, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(expectUpgradeVersionValue, upgradeVersionValue);
    ret = GmcGetVertexPropertyByName(stmt, "dtlReservedCount", &dtlReservedCountValue, sizeof(int32_t), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_FALSE(isNull);
    AW_MACRO_EXPECT_EQ_INT(expectDtlReservedCount, dtlReservedCountValue);

    return ret;
}

int readCount(GmcStmtT *stmt, char *labelName)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int cnt = 0;
    bool isFinish = false;
    while (!isFinish) {
        ret = GmcFetch(stmt, &isFinish);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        cnt++;
    }
    return cnt;
}

const char *g_expectfun1 = R"(
dtl_ext_func_func1, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func1, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func1, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func2, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func3, a=2, b=3, c=5, dtlReservedCount=1.
)";

const char *g_expectfun2 = R"(
dtl_ext_func_func1, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func3, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func4, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func1, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func5, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func1, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func3, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func4, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func1, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func5, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func1, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func2, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func3, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func4, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func1, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func2, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func3, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func5, a=2, b=3, c=5, dtlReservedCount=1.
)";

const char *g_expectfun3 = R"(
dtl_ext_func_func4, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func1, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func2, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func3, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func5, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func4, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func1, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func2, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func3, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func5, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func4, a=1, b=8, c=9, dtlReservedCount=1.
dtl_ext_func_func1, a=1, b=8, c=9, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=8, c=9, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=8, c=9, dtlReservedCount=1.
dtl_ext_func_func5, a=1, b=8, c=9, dtlReservedCount=1.
)";

const char *g_expectfun4 = R"(
dtl_ext_func_func4, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func1, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func3, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func1, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func4, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func1, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func2, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func3, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func5, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func1, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func2, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func3, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func4, a=4, b=5, c=6, dtlReservedCount=-1.
dtl_ext_func_func1, a=4, b=5, c=6, dtlReservedCount=-1.
dtl_ext_func_func2, a=4, b=5, c=6, dtlReservedCount=-1.
dtl_ext_func_func3, a=4, b=5, c=6, dtlReservedCount=-1.
dtl_ext_func_func5, a=4, b=5, c=6, dtlReservedCount=-1.
dtl_ext_func_func1, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func2, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func3, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func4, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func1, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func3, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func1, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func4, a=5, b=6, c=7, dtlReservedCount=-1.
dtl_ext_func_func1, a=5, b=6, c=7, dtlReservedCount=-1.
dtl_ext_func_func2, a=5, b=6, c=7, dtlReservedCount=-1.
dtl_ext_func_func3, a=5, b=6, c=7, dtlReservedCount=-1.
dtl_ext_func_func5, a=5, b=6, c=7, dtlReservedCount=-1.
dtl_ext_func_func1, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func2, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func3, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func4, a=1, b=8, c=9, dtlReservedCount=-1.
dtl_ext_func_func1, a=1, b=8, c=9, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=8, c=9, dtlReservedCount=-1.
dtl_ext_func_func3, a=1, b=8, c=9, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=8, c=9, dtlReservedCount=-1.
dtl_ext_func_func1, a=1, b=8, c=9, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=8, c=9, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=8, c=9, dtlReservedCount=1.
)";

const char *g_expectfun5 = R"(
dtl_ext_func_func1, a=10, b=10, c=6, dtlReservedCount=1.
dtl_ext_func_func2, a=10, b=10, c=6, dtlReservedCount=1.
dtl_ext_func_func3, a=10, b=10, c=6, dtlReservedCount=1.
)";

const char *g_expectfun6 = R"(
dtl_ext_func_func2, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func4, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func5, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func4, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func5, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func2, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func4, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func2, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func5, a=2, b=3, c=5, dtlReservedCount=1.
)";

const char *g_expectfun7 = R"(
dtl_ext_func_func4, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func1, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func2, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func3, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func5, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func4, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func1, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func2, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func3, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func5, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func4, a=1, b=8, c=9, dtlReservedCount=1.
dtl_ext_func_func1, a=1, b=8, c=9, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=8, c=9, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=8, c=9, dtlReservedCount=1.
dtl_ext_func_func5, a=1, b=8, c=9, dtlReservedCount=1.
)";

const char *g_expectfun8 = R"(
dtl_ext_func_func4, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=1, c=1, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=1, c=1, dtlReservedCount=1.
dtl_ext_func_func4, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=2, c=3, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=2, c=3, dtlReservedCount=1.
dtl_ext_func_func4, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func2, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func5, a=2, b=3, c=5, dtlReservedCount=-1.
dtl_ext_func_func2, a=2, b=3, c=5, dtlReservedCount=1.
dtl_ext_func_func4, a=4, b=5, c=6, dtlReservedCount=-1.
dtl_ext_func_func2, a=4, b=5, c=6, dtlReservedCount=-1.
dtl_ext_func_func5, a=4, b=5, c=6, dtlReservedCount=-1.
dtl_ext_func_func2, a=4, b=5, c=6, dtlReservedCount=1.
dtl_ext_func_func4, a=5, b=6, c=7, dtlReservedCount=-1.
dtl_ext_func_func2, a=5, b=6, c=7, dtlReservedCount=-1.
dtl_ext_func_func5, a=5, b=6, c=7, dtlReservedCount=-1.
dtl_ext_func_func2, a=5, b=6, c=7, dtlReservedCount=1.
dtl_ext_func_func4, a=1, b=8, c=9, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=8, c=9, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=8, c=9, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=8, c=9, dtlReservedCount=1.
)";

const char *g_expectfun9 = R"(
dtl_ext_func_func1, a=10, b=10, c=6, dtlReservedCount=1.
dtl_ext_func_func2, a=10, b=10, c=6, dtlReservedCount=1.
dtl_ext_func_func3, a=10, b=10, c=6, dtlReservedCount=1.
)";

const char *g_expectfun10 = R"(
dtl_ext_func_func1, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func1, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func1, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=2, b=3, c=4, d=0, dtlReservedCount=1.
)";

const char *g_expectfun11 = R"(
dtl_ext_func_func1, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func3, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func1, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func4, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func5, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func1, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func3, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func1, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func4, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func5, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func1, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func3, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func1, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func4, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func5, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=2, b=3, c=4, d=0, dtlReservedCount=1.
)";

const char *g_expectfun12 = R"(
dtl_ext_func_func1, a=4, b=5, c=6, d=0, dtlReservedCount=1.
dtl_ext_func_func4, a=4, b=5, c=6, d=0, dtlReservedCount=1.
dtl_ext_func_func5, a=4, b=5, c=6, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=4, b=5, c=6, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=4, b=5, c=6, d=0, dtlReservedCount=1.
dtl_ext_func_func1, a=5, b=6, c=7, d=0, dtlReservedCount=1.
dtl_ext_func_func4, a=5, b=6, c=7, d=0, dtlReservedCount=1.
dtl_ext_func_func5, a=5, b=6, c=7, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=5, b=6, c=7, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=5, b=6, c=7, d=0, dtlReservedCount=1.
dtl_ext_func_func1, a=1, b=8, c=9, d=0, dtlReservedCount=1.
dtl_ext_func_func4, a=1, b=8, c=9, d=0, dtlReservedCount=1.
dtl_ext_func_func5, a=1, b=8, c=9, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=8, c=9, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=8, c=9, d=0, dtlReservedCount=1.
)";

const char *g_expectfun13 = R"(
dtl_ext_func_func1, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func4, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func3, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func1, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func1, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func4, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func3, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func1, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func1, a=4, b=5, c=6, d=0, dtlReservedCount=-1.
dtl_ext_func_func4, a=4, b=5, c=6, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=4, b=5, c=6, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=4, b=5, c=6, d=0, dtlReservedCount=-1.
dtl_ext_func_func3, a=4, b=5, c=6, d=0, dtlReservedCount=-1.
dtl_ext_func_func1, a=4, b=5, c=6, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=4, b=5, c=6, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=4, b=5, c=6, d=0, dtlReservedCount=1.
dtl_ext_func_func1, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func4, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func3, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func1, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func1, a=5, b=6, c=7, d=0, dtlReservedCount=-1.
dtl_ext_func_func4, a=5, b=6, c=7, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=5, b=6, c=7, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=5, b=6, c=7, d=0, dtlReservedCount=-1.
dtl_ext_func_func3, a=5, b=6, c=7, d=0, dtlReservedCount=-1.
dtl_ext_func_func1, a=5, b=6, c=7, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=5, b=6, c=7, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=5, b=6, c=7, d=0, dtlReservedCount=1.
dtl_ext_func_func1, a=1, b=8, c=9, d=0, dtlReservedCount=-1.
dtl_ext_func_func4, a=1, b=8, c=9, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=8, c=9, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=8, c=9, d=0, dtlReservedCount=-1.
dtl_ext_func_func3, a=1, b=8, c=9, d=0, dtlReservedCount=-1.
dtl_ext_func_func1, a=1, b=8, c=9, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=8, c=9, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=1, b=8, c=9, d=0, dtlReservedCount=1.
)";

const char *g_expectfun14 = R"(
dtl_ext_func_func1, a=10, b=10, c=6, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=10, b=10, c=6, d=0, dtlReservedCount=1.
dtl_ext_func_func3, a=10, b=10, c=6, d=0, dtlReservedCount=1.
)";

const char *g_expectfun15 = R"(
dtl_ext_func_func2, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func4, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func5, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func4, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func5, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func4, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func5, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func2, a=2, b=3, c=4, d=0, dtlReservedCount=1.
)";

const char *g_expectfun16 = R"(
dtl_ext_func_func4, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=1, c=1, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=1, c=1, d=0, dtlReservedCount=1.
dtl_ext_func_func4, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=2, c=3, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=2, c=3, d=0, dtlReservedCount=1.
dtl_ext_func_func4, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=2, b=3, c=4, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=2, b=3, c=4, d=0, dtlReservedCount=1.
dtl_ext_func_func4, a=4, b=5, c=6, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=4, b=5, c=6, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=4, b=5, c=6, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=4, b=5, c=6, d=0, dtlReservedCount=1.
dtl_ext_func_func4, a=5, b=6, c=7, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=5, b=6, c=7, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=5, b=6, c=7, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=5, b=6, c=7, d=0, dtlReservedCount=1.
dtl_ext_func_func4, a=1, b=8, c=9, d=0, dtlReservedCount=-1.
dtl_ext_func_func5, a=1, b=8, c=9, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=8, c=9, d=0, dtlReservedCount=-1.
dtl_ext_func_func2, a=1, b=8, c=9, d=0, dtlReservedCount=1.
)";

#endif
