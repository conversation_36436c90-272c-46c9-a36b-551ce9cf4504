/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 506.0.0 迭代四Datalog热升级增强-执行测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2025.01.04]
*****************************************************************************/
#include "dtlpatchEnh.h"
#include "dtlpatchEnh2.h"
#include "DatalogRun.h"

using namespace std;

class dtlpatchenh_002_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtlpatchenh_002_test::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void dtlpatchenh_002_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
}

// 按需建表
class dtlpatchenh_002_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        char file_path1[200] = "./testcases/16_DataLog/098_dtlPatchEnhance/schema_file";
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"schemaLoader=2\"");
        (void)snprintf(g_command, MAX_CMD_SIZE, "sh $TEST_HOME/tools/modifyCfg.sh \"schemaPath=%s;%s;%s;\"", file_path1,
            file_path1, file_path1);
        system(g_command);
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
        // 恢复配置项
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
        system("sh ${TEST_HOME}/tools/stop.sh -f");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtlpatchenh_002_test2::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void dtlpatchenh_002_test2::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
}

/* ****************************************************************************
 Description  : 001.%block
1,原始.d含一个function，返回no_data,patch.d修改规则，join原始.d中的function，预期重做数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    C3Int8T objIn3[0] = {};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    // out2中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn4[recordNum] = {{3, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {2, 0, 1, 8, 9}};
    C3Int8T objIn5[2 * recordNum] = {
        {1, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {1, 0, 1, 8, 9}, {1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn4, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输入表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.%block
0,原始.d错误使用join表，修改规则和新增规则来实现，使用的表都是原始.d，预期数据符合预期，利用tbm表观测重做顺序
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 5}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 5}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn5[recordNum - 1] = {{1, upVerVal, 1, 1, 10}, {1, upVerVal, 1, 2, 10}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 5}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn7[recordNum] = {{3, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {2, 0, 1, 8, 9}};
    C3Int8T objIn8[2 * recordNum] = {{1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9},
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 5}};
    C3Int8T objIn9[2 * recordNum - 1] = {{1, upVerVal, 4, 5, 10}, {1, upVerVal, 5, 6, 10}, {1, upVerVal, 1, 8, 10},
        {1, upVerVal, 1, 1, 10}, {1, upVerVal, 1, 2, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn9, 2 * recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn8, 2 * recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);

    C3Int8T objIn10[2 * recordNum] = {{1, upVerVal1, 4, 5, 6}, {1, upVerVal1, 5, 6, 7}, {1, upVerVal1, 1, 8, 9},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 3, 5}};
    C3Int8T objIn11[2 * recordNum] = {{1, upVerVal1, 4, 5, 6}, {1, upVerVal1, 5, 6, 7}, {1, upVerVal1, 1, 8, 9},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 5}};

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn11, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");

    // 插入数据
    C3Int8T objIn12[1] = {{1, upVerVal1, 10, 10, 6}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn13[2 * recordNum + 1] = {{1, upVerVal1, 4, 5, 6}, {1, upVerVal1, 5, 6, 7}, {1, upVerVal1, 1, 8, 9},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 3, 5}, {1, upVerVal1, 10, 10, 6}};
    C3Int8T objIn14[2 * recordNum + 1] = {{1, upVerVal1, 4, 5, 6}, {1, upVerVal1, 5, 6, 7}, {1, upVerVal1, 1, 8, 9},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 5}, {1, upVerVal1, 10, 10, 6}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn13, recordNum * 2 + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn14, recordNum * 2 + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.%block 1,原始.d错误使用join表，修改规则和新增规则来实现，使用的表都是原始.d，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 5}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 5}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS", "RULE_NAME: rN1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn5[recordNum - 1] = {{1, upVerVal, 1, 1, 10}, {1, upVerVal, 1, 2, 10}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 5}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn7[recordNum] = {{3, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {2, 0, 1, 8, 9}};
    C3Int8T objIn8[2 * recordNum] = {{1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9},
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 5}};
    C3Int8T objIn9[2 * recordNum - 1] = {{1, upVerVal, 4, 5, 10}, {1, upVerVal, 5, 6, 10}, {1, upVerVal, 1, 8, 10},
        {1, upVerVal, 1, 1, 10}, {1, upVerVal, 1, 2, 10}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn9, 2 * recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn8, 2 * recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);

    C3Int8T objIn10[2 * recordNum] = {{1, upVerVal1, 4, 5, 6}, {1, upVerVal1, 5, 6, 7}, {1, upVerVal1, 1, 8, 9},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 3, 5}};
    C3Int8T objIn11[2 * recordNum] = {{1, upVerVal1, 4, 5, 6}, {1, upVerVal1, 5, 6, 7}, {1, upVerVal1, 1, 8, 9},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 5}};

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn11, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");

    // 插入数据
    C3Int8T objIn12[1] = {{1, upVerVal1, 10, 10, 6}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn13[2 * recordNum + 1] = {{1, upVerVal1, 4, 5, 6}, {1, upVerVal1, 5, 6, 7}, {1, upVerVal1, 1, 8, 9},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 3, 5}, {1, upVerVal1, 10, 10, 6}};
    C3Int8T objIn14[2 * recordNum + 1] = {{1, upVerVal1, 4, 5, 6}, {1, upVerVal1, 5, 6, 7}, {1, upVerVal1, 1, 8, 9},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 5}, {1, upVerVal1, 10, 10, 6}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn13, recordNum * 2 + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn14, recordNum * 2 + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.%block 1和%redo
REDO_OFF,原始.d错误使用join表，修改规则和新增规则来实现，使用的表都是原始.d，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 5}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    C3Int8T objIn4[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 5}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS", "RULE_NAME: rN1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 5}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn7[recordNum] = {{3, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {2, 0, 1, 8, 9}};
    C3Int8T objIn8[2 * recordNum] = {{1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9},
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 5}};
    C3Int8T objIn9[2 * recordNum] = {{1, upVerVal, 4, 5, 10}, {1, upVerVal, 5, 6, 10}, {1, upVerVal, 1, 8, 10},
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn9, 2 * recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn8, 2 * recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);

    C3Int8T objIn10[2 * recordNum] = {{1, upVerVal1, 4, 5, 10}, {1, upVerVal1, 5, 6, 10}, {1, upVerVal1, 1, 8, 10},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 3, 5}};
    C3Int8T objIn11[2 * recordNum] = {{1, upVerVal1, 4, 5, 6}, {1, upVerVal1, 5, 6, 7}, {1, upVerVal1, 1, 8, 9},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 5}};

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn11, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");

    // 插入数据
    C3Int8T objIn12[1] = {{1, upVerVal1, 10, 10, 6}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn13[2 * recordNum + 1] = {{1, upVerVal1, 4, 5, 10}, {1, upVerVal1, 5, 6, 10}, {1, upVerVal1, 1, 8, 10},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 3, 5}, {1, upVerVal1, 10, 10, 6}};
    C3Int8T objIn14[2 * recordNum + 1] = {{1, upVerVal1, 4, 5, 6}, {1, upVerVal1, 5, 6, 7}, {1, upVerVal1, 1, 8, 9},
        {1, upVerVal1, 1, 1, 1}, {1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 5}, {1, upVerVal1, 10, 10, 6}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn13, recordNum * 2 + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn14, recordNum * 2 + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.%block
0,原始.d错误使用join函数（func），修改规则和新增规则来实现，使用的函数是原始.d，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum - 1] = {{1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    C3Int8T objIn3[recordNum - 2] = {{1, 0, 1, 1, 1}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    C3Int8T objIn5[recordNum - 2] = {{1, upVerVal, 1, 1, 1}};
    C3Int8T objIn6[recordNum - 2] = {{1, upVerVal1, 1, 1, 1}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn7[recordNum] = {{3, 0, 4, 4, 16}, {1, 0, 5, 6, 11}, {2, 0, 1, 8, 9}};
    C3Int8T objIn8[recordNum - 1] = {{1, upVerVal, 4, 4, 16}, {1, upVerVal, 1, 1, 1}};
    C3Int8T objIn9[recordNum - 1] = {{1, upVerVal1, 4, 4, 16}, {1, upVerVal1, 1, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn9, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    C3Int8T objIn10[recordNum + 1] = {
        {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 2, 3}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 5, 6, 11}};
    C3Int8T objIn11[recordNum - 1] = {{1, upVerVal2, 4, 4, 16}, {1, upVerVal2, 1, 1, 1}};

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn11, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");

    // 插入数据
    C3Int8T objIn12[1] = {{1, upVerVal2, 10, 10, 20}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn13[recordNum + 2] = {{1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 2, 3}, {1, upVerVal2, 2, 3, 5},
        {1, upVerVal2, 5, 6, 11}, {1, upVerVal2, 10, 10, 20}};
    C3Int8T objIn14[recordNum - 1] = {{1, upVerVal2, 4, 4, 16}, {1, upVerVal2, 1, 1, 1}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn13, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn14, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.%block
1,原始.d错误使用join函数（func），修改规则和新增规则来实现，使用的函数是原始.d，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum - 1] = {{1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    C3Int8T objIn3[recordNum - 2] = {{1, 0, 1, 1, 1}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    C3Int8T objIn5[recordNum - 2] = {{1, upVerVal, 1, 1, 1}};
    C3Int8T objIn6[recordNum - 2] = {{1, upVerVal1, 1, 1, 1}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn7[recordNum] = {{3, 0, 4, 4, 16}, {1, 0, 5, 6, 11}, {2, 0, 1, 8, 9}};
    C3Int8T objIn8[recordNum - 1] = {{1, upVerVal, 4, 4, 16}, {1, upVerVal, 1, 1, 1}};
    C3Int8T objIn9[recordNum - 1] = {{1, upVerVal1, 4, 4, 16}, {1, upVerVal1, 1, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn9, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    C3Int8T objIn10[recordNum + 1] = {
        {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 2, 3}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 5, 6, 11}};
    C3Int8T objIn11[recordNum - 1] = {{1, upVerVal2, 4, 4, 16}, {1, upVerVal2, 1, 1, 1}};

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn11, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");

    // 插入数据
    C3Int8T objIn12[1] = {{1, upVerVal2, 10, 10, 20}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn13[recordNum + 2] = {{1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 2, 3}, {1, upVerVal2, 2, 3, 5},
        {1, upVerVal2, 5, 6, 11}, {1, upVerVal2, 10, 10, 20}};
    C3Int8T objIn14[recordNum - 1] = {{1, upVerVal2, 4, 4, 16}, {1, upVerVal2, 1, 1, 1}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn13, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn14, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.%block 1和%redo
REDO_OFF,原始.d错误使用join函数（func），修改规则和新增规则来实现，使用的函数是原始.d，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule006";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum - 1] = {{1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    C3Int8T objIn3[recordNum - 2] = {{1, 0, 1, 1, 1}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal1);
    C3Int8T objIn5[recordNum - 1] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}};
    C3Int8T objIn6[recordNum - 2] = {{1, upVerVal1, 1, 1, 1}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn7[recordNum] = {{3, 0, 4, 4, 16}, {1, 0, 5, 6, 11}, {2, 0, 1, 8, 9}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal, 4, 4, 16}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}};
    C3Int8T objIn9[recordNum - 1] = {{1, upVerVal1, 4, 4, 16}, {1, upVerVal1, 1, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn7, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn9, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    C3Int8T objIn10[recordNum] = {{1, upVerVal, 4, 4, 16}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}};
    C3Int8T objIn11[recordNum - 1] = {{1, upVerVal2, 4, 4, 16}, {1, upVerVal2, 1, 1, 1}};

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn10, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn11, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out2 read complete!!!");

    // 插入数据
    C3Int8T objIn12[1] = {{1, upVerVal2, 10, 10, 20}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn12, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn13[recordNum + 1] = {
        {1, upVerVal, 4, 4, 16}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}, {1, upVerVal2, 10, 10, 20}};
    C3Int8T objIn14[recordNum - 1] = {{1, upVerVal2, 4, 4, 16}, {1, upVerVal2, 1, 1, 1}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn13, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn14, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.%block
0,原始.d中agg规则使用错误，agg规则左表是输入表，通过新增两个中间表、修改规则和新增规则来实现，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule007";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C4Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1}, {2, 0, 2, 3, 5, 1}};
    C4Int8T objIn2[recordNum - 1] = {{1, 0, 3, 1, 1, 1}, {1, 0, 5, 5, 2, 3}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C4Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1, 1}, {1, upVerVal, 1, 1, 1, 3}, {1, upVerVal, 2, 2, 3, 5}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 对输入表写入数据
    C4Int8T objIn4[recordNum] = {{3, 0, 5, 1, 1, 10}, {1, 0, 10, 3, 5, 1}, {2, 0, 1, 8, 9, 5}};
    C4Int8T objIn5[recordNum + 1] = {
        {1, upVerVal, 5, 1, 1, 1}, {1, upVerVal, 10, 2, 3, 5}, {1, upVerVal, 1, 1, 1, 3}, {1, upVerVal, 1, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum + 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    C4Int8T objIn6[recordNum + 2] = {{1, upVerVal2, 3, 1, 1, 1}, {1, upVerVal2, 5, 5, 2, 3}, {1, upVerVal2, 1, 1, 5, 1},
        {1, upVerVal2, 5, 5, 10, 3}, {1, upVerVal2, 9, 9, 1, 8}};

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");

    // 插入数据
    C4Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 8, 4, 1}, {3, upVerVal2, 10, 3, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum - 1, C4Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C4Int8T objIn8[recordNum + 2] = {{1, upVerVal2, 3, 1, 1, 1}, {1, upVerVal2, 5, 5, 2, 3}, {1, upVerVal2, 1, 1, 5, 1},
        {1, upVerVal2, 5, 1, 10, 3}, {1, upVerVal2, 9, 4, 1, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024123104051
/* ****************************************************************************
 Description  : 009.%block
1,原始.d中agg规则使用错误，agg规则左表是输入表，通过新增两个中间表、修改规则和新增规则来实现，添加-supUpgErr选项，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule008";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C4Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1}, {2, 0, 2, 3, 5, 1}};
    C4Int8T objIn2[recordNum - 1] = {{1, 0, 3, 1, 1, 1}, {1, 0, 5, 5, 2, 3}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C4Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1, 1}, {1, upVerVal, 1, 1, 1, 3}, {1, upVerVal, 2, 2, 3, 5}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 对输入表写入数据
    C4Int8T objIn4[recordNum] = {{3, 0, 5, 1, 1, 10}, {1, 0, 10, 3, 5, 1}, {2, 0, 1, 8, 9, 5}};
    C4Int8T objIn5[recordNum + 1] = {
        {1, upVerVal, 5, 1, 1, 1}, {1, upVerVal, 10, 2, 3, 5}, {1, upVerVal, 1, 1, 1, 3}, {1, upVerVal, 1, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum + 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    C4Int8T objIn6[recordNum + 2] = {{1, upVerVal2, 3, 1, 1, 1}, {1, upVerVal2, 5, 5, 2, 3}, {1, upVerVal2, 1, 1, 5, 1},
        {1, upVerVal2, 5, 5, 10, 3}, {1, upVerVal2, 9, 9, 1, 8}};

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");

    // 插入数据
    C4Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 8, 4, 1}, {3, upVerVal2, 10, 3, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum - 1, C4Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C4Int8T objIn8[recordNum + 2] = {{1, upVerVal2, 3, 1, 1, 1}, {1, upVerVal2, 5, 5, 2, 3}, {1, upVerVal2, 1, 1, 5, 1},
        {1, upVerVal2, 5, 1, 10, 3}, {1, upVerVal2, 9, 4, 1, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.%block 1和%redo REDO_OFF,原始.d中agg规则使用错误，agg规则左表是输入表，
 通过新增两个中间表、修改规则和新增规则来实现，添加-supUpgErr选项，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule009";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C4Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1}, {2, 0, 2, 3, 5, 1}};
    C4Int8T objIn2[recordNum - 1] = {{1, 0, 3, 1, 1, 1}, {1, 0, 5, 5, 2, 3}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C4Int8T objIn3[recordNum - 1] = {{1, upVerVal, 3, 1, 1, 1}, {1, upVerVal, 5, 5, 2, 3}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum - 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 对输入表写入数据
    C4Int8T objIn4[recordNum] = {{3, 0, 5, 1, 1, 10}, {1, 0, 10, 3, 5, 1}, {2, 0, 1, 8, 9, 5}};
    C4Int8T objIn5[recordNum + 2] = {{1, upVerVal, 5, 5, 1, 1}, {1, upVerVal, 10, 10, 3, 5}, {1, upVerVal, 1, 1, 8, 9},
        {1, upVerVal, 3, 1, 1, 1}, {1, upVerVal, 5, 5, 2, 3}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");

    // 插入数据
    C4Int8T objIn6[recordNum - 1] = {{1, upVerVal2, 1, 1, 4, 1}, {3, upVerVal2, 2, 3, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn6, recordNum - 1, C4Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C4Int8T objIn7[recordNum + 2] = {{1, upVerVal, 5, 5, 1, 1}, {1, upVerVal, 10, 10, 3, 5}, {1, upVerVal, 1, 1, 8, 9},
        {1, upVerVal, 4, 1, 1, 1}, {1, upVerVal, 5, 1, 2, 3}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.%block
0,原始.d中agg规则使用错误，agg规则左表是中间表，通过新增两个中间表、修改规则和新增规则来实现，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule010";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C4Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1}, {2, 0, 2, 3, 5, 1}};
    C4Int8T objIn2[recordNum - 1] = {{1, 0, 3, 1, 1, 1}, {1, 0, 5, 5, 2, 3}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C4Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1, 1}, {1, upVerVal, 1, 1, 1, 3}, {1, upVerVal, 2, 2, 3, 5}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 对输入表写入数据
    C4Int8T objIn4[recordNum] = {{3, 0, 5, 1, 1, 10}, {1, 0, 10, 3, 5, 1}, {2, 0, 1, 8, 9, 5}};
    C4Int8T objIn5[recordNum + 1] = {
        {1, upVerVal, 5, 1, 1, 1}, {1, upVerVal, 10, 2, 3, 5}, {1, upVerVal, 1, 1, 1, 3}, {1, upVerVal, 1, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum + 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    C4Int8T objIn6[recordNum + 2] = {{1, upVerVal2, 3, 1, 1, 1}, {1, upVerVal2, 5, 5, 2, 3}, {1, upVerVal2, 1, 1, 5, 1},
        {1, upVerVal2, 5, 5, 10, 3}, {1, upVerVal2, 9, 9, 1, 8}};

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");

    // 插入数据
    C4Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 8, 4, 1}, {3, upVerVal2, 10, 3, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum - 1, C4Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C4Int8T objIn8[recordNum + 2] = {{1, upVerVal2, 3, 1, 1, 1}, {1, upVerVal2, 5, 5, 2, 3}, {1, upVerVal2, 1, 1, 5, 1},
        {1, upVerVal2, 5, 1, 10, 3}, {1, upVerVal2, 9, 4, 1, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.%block
1,原始.d中agg规则使用错误，agg规则左表是中间表，通过新增两个中间表、修改规则和新增规则来实现，添加-supUpgErr选项，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule011";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C4Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1}, {2, 0, 2, 3, 5, 1}};
    C4Int8T objIn2[recordNum - 1] = {{1, 0, 3, 1, 1, 1}, {1, 0, 5, 5, 2, 3}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C4Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1, 1}, {1, upVerVal, 1, 1, 1, 3}, {1, upVerVal, 2, 2, 3, 5}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 对输入表写入数据
    C4Int8T objIn4[recordNum] = {{3, 0, 5, 1, 1, 10}, {1, 0, 10, 3, 5, 1}, {2, 0, 1, 8, 9, 5}};
    C4Int8T objIn5[recordNum + 1] = {
        {1, upVerVal, 5, 1, 1, 1}, {1, upVerVal, 10, 2, 3, 5}, {1, upVerVal, 1, 1, 1, 3}, {1, upVerVal, 1, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum + 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    C4Int8T objIn6[recordNum + 2] = {{1, upVerVal2, 3, 1, 1, 1}, {1, upVerVal2, 5, 5, 2, 3}, {1, upVerVal2, 1, 1, 5, 1},
        {1, upVerVal2, 5, 5, 10, 3}, {1, upVerVal2, 9, 9, 1, 8}};

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");

    // 插入数据
    C4Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 8, 4, 1}, {3, upVerVal2, 10, 3, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn7, recordNum - 1, C4Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C4Int8T objIn8[recordNum + 2] = {{1, upVerVal2, 3, 1, 1, 1}, {1, upVerVal2, 5, 5, 2, 3}, {1, upVerVal2, 1, 1, 5, 1},
        {1, upVerVal2, 5, 1, 10, 3}, {1, upVerVal2, 9, 4, 1, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.%block 1和%redo REDO_OFF,原始.d中agg规则使用错误，agg规则左表是中间表，通过新增两个中间表、
 修改规则和新增规则来实现，添加-supUpgErr选项，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addrule012";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C4Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1, 1}, {1, 0, 1, 1, 3, 1}, {2, 0, 2, 3, 5, 1}};
    C4Int8T objIn2[recordNum - 1] = {{1, 0, 3, 1, 1, 1}, {1, 0, 5, 5, 2, 3}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C4Int8T objIn3[recordNum - 1] = {{1, upVerVal, 3, 1, 1, 1}, {1, upVerVal, 5, 5, 2, 3}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum - 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 对输入表写入数据
    C4Int8T objIn4[recordNum] = {{3, 0, 5, 1, 1, 10}, {1, 0, 10, 3, 5, 1}, {2, 0, 1, 8, 9, 5}};
    C4Int8T objIn5[recordNum + 2] = {{1, upVerVal, 5, 5, 1, 1}, {1, upVerVal, 10, 10, 3, 5}, {1, upVerVal, 1, 1, 8, 9},
        {1, upVerVal, 3, 1, 1, 1}, {1, upVerVal, 5, 5, 2, 3}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback out1 read complete!!!");

    // 插入数据
    C4Int8T objIn6[recordNum - 1] = {{1, upVerVal2, 1, 1, 4, 1}, {3, upVerVal2, 2, 3, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn6, recordNum - 1, C4Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C4Int8T objIn7[recordNum + 2] = {{1, upVerVal, 5, 5, 1, 1}, {1, upVerVal, 10, 10, 3, 5}, {1, upVerVal, 1, 1, 8, 9},
        {1, upVerVal, 4, 1, 1, 1}, {1, upVerVal, 5, 1, 2, 3}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.%block 0，中间表定义有问题，新增transient(tuple)中间表，修改规则和新增规则，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_014)
{
    char soName[] = "enh002_014";
    char patchSoName1[] = "./datalogFile/midtable/enh002_014_patchV2.so";
    char rollBackPatchSoName1[] = "./datalogFile/midtable/enh002_014_rollbackV2.so";
    int ret = 0;

    ret = TestLoadDatalog("./datalogFile/midtable/enh002_014.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写数据，读数据");
    system("rm -rf /root/dtlpatchenh.txt");
    char tableInput1[] = "ns1.inp1";
    char tableMid1[] = "ns2.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableMid3[] = "ns2.mid3";
    char tableMid4[] = "ns2.newmid2";
    char tableNewMid4[] = "ns2.newmid4";
    char tableNewMid5[] = "ns2.newmid5";
    char tableOutput1[] = "ns3.out1";
    char tableOutput2[] = "ns3.out2";
    char tableOutput3[] = "ns3.out3";
    char tableOutput4[] = "ns3.newout4";
    char tableOutput5[] = "ns3.newout5";

    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun1, result));
    if (!strstr(g_expectfun1, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun2, result));
    if (!strstr(g_expectfun2, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    int32_t upVerVal = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    C3Int8T objIn3[3] = {{3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn4[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5}, {3, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn5[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    recordNum = 6;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput4, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput5, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    count = readCount(g_stmt, tableMid2);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid5);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun3, result));
    if (!strstr(g_expectfun3, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁降级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollBackPatchSoName1));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun4, result));
    if (!strstr(g_expectfun4, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn6[1] = {{1, upVerVal, 10, 10, 6}};
    recordNum = 1;
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn6, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn7[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5},
        {3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    C3Int8T objIn8[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    recordNum = 7;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun5, result));
    if (!strstr(g_expectfun5, result)) {
        system(g_command);
    }
    free(result);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.%block 1，中间表定义有问题，新增transient(tuple)中间表，修改规则和新增规则，添加-supUpgErr选项预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_015)
{
    char soName[] = "enh002_015";
    char patchSoName1[] = "./datalogFile/midtable/enh002_015_patchV2.so";
    char rollBackPatchSoName1[] = "./datalogFile/midtable/enh002_015_rollbackV2.so";
    int ret = 0;

    ret = TestLoadDatalog("./datalogFile/midtable/enh002_015.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写数据，读数据");
    system("rm -rf /root/dtlpatchenh.txt");
    char tableInput1[] = "ns1.inp1";
    char tableMid1[] = "ns2.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableMid3[] = "ns2.mid3";
    char tableMid4[] = "ns2.newmid2";
    char tableNewMid4[] = "ns2.newmid4";
    char tableNewMid5[] = "ns2.newmid5";
    char tableOutput1[] = "ns3.out1";
    char tableOutput2[] = "ns3.out2";
    char tableOutput3[] = "ns3.out3";
    char tableOutput4[] = "ns3.newout4";
    char tableOutput5[] = "ns3.newout5";

    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun1, result));
    if (!strstr(g_expectfun1, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun6, result));
    if (!strstr(g_expectfun6, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    int32_t upVerVal = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn3[3] = {{3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn4[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5}, {3, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn5[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    recordNum = 6;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput4, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput5, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    count = readCount(g_stmt, tableMid2);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid5);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun7, result));
    if (!strstr(g_expectfun7, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁降级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollBackPatchSoName1));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun8, result));
    if (!strstr(g_expectfun8, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn6[1] = {{1, upVerVal, 10, 10, 6}};
    recordNum = 1;
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn6, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn7[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5},
        {3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    C3Int8T objIn8[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    recordNum = 7;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun9, result));
    if (!strstr(g_expectfun9, result)) {
        system(g_command);
    }
    free(result);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.%block 1和%redo REDO_OFF，中间表定义有问题，新增transient(tuple)中间表，修改规则和新增规则，添加-supUpgErr选项预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_016)
{
    char soName[] = "enh002_016";
    char patchSoName1[] = "./datalogFile/midtable/enh002_016_patchV2.so";
    char rollBackPatchSoName1[] = "./datalogFile/midtable/enh002_016_rollbackV2.so";
    int ret = 0;

    ret = TestLoadDatalog("./datalogFile/midtable/enh002_016.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写数据，读数据");
    system("rm -rf /root/dtlpatchenh.txt");
    char tableInput1[] = "ns1.inp1";
    char tableMid1[] = "ns2.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableMid3[] = "ns2.mid3";
    char tableMid4[] = "ns2.newmid2";
    char tableNewMid4[] = "ns2.newmid4";
    char tableNewMid5[] = "ns2.newmid5";
    char tableOutput1[] = "ns3.out1";
    char tableOutput2[] = "ns3.out2";
    char tableOutput3[] = "ns3.out3";
    char tableOutput4[] = "ns3.newout4";
    char tableOutput5[] = "ns3.newout5";

    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun1, result));
    if (!strstr(g_expectfun1, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn3[3] = {{3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn33[3] = {{1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}};
    C3Int8T objIn4[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5}, {3, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn5[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    recordNum = 3;
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput4, objIn33, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput5, objIn33, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    recordNum = 6;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, tableMid3, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    count = readCount(g_stmt, tableMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid5);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun7, result));
    if (!strstr(g_expectfun7, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁降级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollBackPatchSoName1));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn6[1] = {{1, upVerVal, 10, 10, 6}};
    recordNum = 1;
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn6, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn77[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 10, 10, 6}};
    C3Int8T objIn7[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5},
        {3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    C3Int8T objIn8[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};

    recordNum = 4;
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn77, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    recordNum = 7;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun9, result));
    if (!strstr(g_expectfun9, result)) {
        system(g_command);
    }
    free(result);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.%block 0，中间表定义有问题，新增transient(finish)中间表，修改规则和新增规则，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_017)
{
    char soName[] = "enh002_017";
    char patchSoName1[] = "./datalogFile/midtable/enh002_017_patchV2.so";
    char rollBackPatchSoName1[] = "./datalogFile/midtable/enh002_017_rollbackV2.so";
    int ret = 0;

    ret = TestLoadDatalog("./datalogFile/midtable/enh002_017.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写数据，读数据");
    system("rm -rf /root/dtlpatchenh.txt");
    char tableInput1[] = "ns1.inp1";
    char tableMid1[] = "ns2.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableMid3[] = "ns2.mid3";
    char tableMid4[] = "ns2.newmid2";
    char tableNewMid4[] = "ns2.newmid4";
    char tableNewMid5[] = "ns2.newmid5";
    char tableOutput1[] = "ns3.out1";
    char tableOutput2[] = "ns3.out2";
    char tableOutput3[] = "ns3.out3";
    char tableOutput4[] = "ns3.newout4";
    char tableOutput5[] = "ns3.newout5";

    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun1, result));
    if (!strstr(g_expectfun1, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun2, result));
    if (!strstr(g_expectfun2, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    int32_t upVerVal = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    C3Int8T objIn3[3] = {{3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn4[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5}, {3, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn5[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    recordNum = 6;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput4, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput5, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    count = readCount(g_stmt, tableMid2);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid5);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun3, result));
    if (!strstr(g_expectfun3, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁降级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollBackPatchSoName1));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun4, result));
    if (!strstr(g_expectfun4, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn6[1] = {{1, upVerVal, 10, 10, 6}};
    recordNum = 1;
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn6, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn7[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5},
        {3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    C3Int8T objIn8[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    recordNum = 7;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun5, result));
    if (!strstr(g_expectfun5, result)) {
        system(g_command);
    }
    free(result);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.%block 1，中间表定义有问题，新增transient(finish)中间表，修改规则和新增规则，添加-supUpgErr选项预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_018)
{
    char soName[] = "enh002_018";
    char patchSoName1[] = "./datalogFile/midtable/enh002_018_patchV2.so";
    char rollBackPatchSoName1[] = "./datalogFile/midtable/enh002_018_rollbackV2.so";
    int ret = 0;

    ret = TestLoadDatalog("./datalogFile/midtable/enh002_018.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写数据，读数据");
    system("rm -rf /root/dtlpatchenh.txt");
    char tableInput1[] = "ns1.inp1";
    char tableMid1[] = "ns2.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableMid3[] = "ns2.mid3";
    char tableMid4[] = "ns2.newmid2";
    char tableNewMid4[] = "ns2.newmid4";
    char tableNewMid5[] = "ns2.newmid5";
    char tableOutput1[] = "ns3.out1";
    char tableOutput2[] = "ns3.out2";
    char tableOutput3[] = "ns3.out3";
    char tableOutput4[] = "ns3.newout4";
    char tableOutput5[] = "ns3.newout5";

    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun1, result));
    if (!strstr(g_expectfun1, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun6, result));
    if (!strstr(g_expectfun6, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    int32_t upVerVal = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn3[3] = {{3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn4[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5}, {3, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn5[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    recordNum = 6;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput4, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput5, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    count = readCount(g_stmt, tableMid2);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid5);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun7, result));
    if (!strstr(g_expectfun7, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁降级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollBackPatchSoName1));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun8, result));
    if (!strstr(g_expectfun8, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn6[1] = {{1, upVerVal, 10, 10, 6}};
    recordNum = 1;
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn6, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn7[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5},
        {3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    C3Int8T objIn8[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    recordNum = 7;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun9, result));
    if (!strstr(g_expectfun9, result)) {
        system(g_command);
    }
    free(result);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.%block 1和%redo REDO_OFF，中间表定义有问题，新增transient(finish)中间表，修改规则和新增规则，添加-supUpgErr选项预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_019)
{
    char soName[] = "enh002_019";
    char patchSoName1[] = "./datalogFile/midtable/enh002_019_patchV2.so";
    char rollBackPatchSoName1[] = "./datalogFile/midtable/enh002_019_rollbackV2.so";
    int ret = 0;

    ret = TestLoadDatalog("./datalogFile/midtable/enh002_019.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写数据，读数据");
    system("rm -rf /root/dtlpatchenh.txt");
    char tableInput1[] = "ns1.inp1";
    char tableMid1[] = "ns2.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableMid3[] = "ns2.mid3";
    char tableMid4[] = "ns2.newmid2";
    char tableNewMid4[] = "ns2.newmid4";
    char tableNewMid5[] = "ns2.newmid5";
    char tableOutput1[] = "ns3.out1";
    char tableOutput2[] = "ns3.out2";
    char tableOutput3[] = "ns3.out3";
    char tableOutput4[] = "ns3.newout4";
    char tableOutput5[] = "ns3.newout5";

    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun1, result));
    if (!strstr(g_expectfun1, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn3[3] = {{3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn33[3] = {{1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}};
    C3Int8T objIn4[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5}, {3, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn5[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    recordNum = 3;
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput4, objIn33, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput5, objIn33, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    recordNum = 6;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, tableMid3, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    count = readCount(g_stmt, tableMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid4);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableNewMid5);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun7, result));
    if (!strstr(g_expectfun7, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁降级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollBackPatchSoName1));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn6[1] = {{1, upVerVal, 10, 10, 6}};
    recordNum = 1;
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn6, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn77[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 10, 10, 6}};
    C3Int8T objIn7[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5}, {3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7},
        {2, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    C3Int8T objIn8[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7},
        {1, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};

    recordNum = 4;
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn77, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    recordNum = 7;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun9, result));
    if (!strstr(g_expectfun9, result)) {
        system(g_command);
    }
    free(result);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.%block 0，中间表定义有问题，新增transient(field)中间表，修改规则和新增规则，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_020)
{
    char soName[] = "enh002_020";
    char patchSoName1[] = "./datalogFile/midtable/enh002_020_patchV2.so";
    char rollBackPatchSoName1[] = "./datalogFile/midtable/enh002_020_rollbackV2.so";
    int ret = 0;

    ret = TestLoadDatalog("./datalogFile/midtable/enh002_020.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写数据，读数据");
    system("rm -rf /root/dtlpatchenh.txt");
    char tableInput1[] = "ns1.inp1";
    char tableMid1[] = "ns2.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableMid3[] = "ns2.mid3";
    char tableMid4[] = "ns2.newmid2";
    char tableNewMid4[] = "ns2.newmid4";
    char tableNewMid5[] = "ns2.newmid5";
    char tableOutput1[] = "ns3.out1";
    char tableOutput2[] = "ns3.out2";
    char tableOutput3[] = "ns3.out3";
    char tableOutput4[] = "ns3.newout4";
    char tableOutput5[] = "ns3.newout5";

    int recordNum = 3;
    TagC3Int8C1Int4 objIn1[recordNum] = {{1, 0, 1, 1, 1, 0}, {1, 0, 1, 2, 3, 0}, {2, 0, 2, 3, 4, 0}};
    TagC3Int8C1Int4 objIn2[recordNum] = {{1, 0, 1, 1, 1, 0}, {1, 0, 1, 2, 3, 0}, {1, 0, 2, 3, 4, 0}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8C1Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun10, result));
    if (!strstr(g_expectfun10, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun11, result));
    if (!strstr(g_expectfun11, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    int32_t upVerVal = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    TagC3Int8C1Int4 objIn3[3] = {{3, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {2, upVerVal, 1, 8, 9, 0}};
    TagC3Int8C1Int4 objIn4[6] = {{1, upVerVal, 1, 1, 1, 0}, {1, upVerVal, 1, 2, 3, 0}, {2, upVerVal, 2, 3, 4, 0},
        {3, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {2, upVerVal, 1, 8, 9, 0}};
    TagC3Int8C1Int4 objIn5[6] = {{1, upVerVal, 1, 1, 1, 0}, {1, upVerVal, 1, 2, 3, 0}, {1, upVerVal, 2, 3, 4, 0},
        {1, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {1, upVerVal, 1, 8, 9, 0}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn3, recordNum, C3Int8C1Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    recordNum = 6;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn4, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid4, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableNewMid4, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableNewMid5, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput4, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput5, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    count = readCount(g_stmt, tableMid2);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun12, result));
    if (!strstr(g_expectfun12, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁降级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollBackPatchSoName1));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun13, result));
    if (!strstr(g_expectfun13, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    TagC3Int8C1Int4 objIn6[1] = {{1, upVerVal, 10, 10, 6, 0}};
    recordNum = 1;
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn6, recordNum, C3Int8C1Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TagC3Int8C1Int4 objIn7[7] = {{1, upVerVal, 1, 1, 1, 0}, {1, upVerVal, 1, 2, 3, 0}, {2, upVerVal, 2, 3, 4, 0},
        {3, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {2, upVerVal, 1, 8, 9, 0}, {1, upVerVal, 10, 10, 6, 0}};
    TagC3Int8C1Int4 objIn8[7] = {{1, 0, 1, 1, 1, 0}, {1, 0, 1, 2, 3, 0}, {1, 0, 2, 3, 4, 0}, {1, 0, 4, 5, 6, 0},
        {1, upVerVal, 5, 6, 7, 0}, {1, upVerVal, 1, 8, 9, 0}, {1, upVerVal, 10, 10, 6, 0}};
    recordNum = 7;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn7, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun14, result));
    if (!strstr(g_expectfun14, result)) {
        system(g_command);
    }
    free(result);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.%block 1，中间表定义有问题，新增transient(field)中间表，修改规则和新增规则，添加-supUpgErr选项预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_021)
{
    char soName[] = "enh002_021";
    char patchSoName1[] = "./datalogFile/midtable/enh002_021_patchV2.so";
    char rollBackPatchSoName1[] = "./datalogFile/midtable/enh002_021_rollbackV2.so";
    int ret = 0;

    ret = TestLoadDatalog("./datalogFile/midtable/enh002_021.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写数据，读数据");
    system("rm -rf /root/dtlpatchenh.txt");
    char tableInput1[] = "ns1.inp1";
    char tableMid1[] = "ns2.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableMid3[] = "ns2.mid3";
    char tableMid4[] = "ns2.newmid2";
    char tableNewMid4[] = "ns2.newmid4";
    char tableNewMid5[] = "ns2.newmid5";
    char tableOutput1[] = "ns3.out1";
    char tableOutput2[] = "ns3.out2";
    char tableOutput3[] = "ns3.out3";
    char tableOutput4[] = "ns3.newout4";
    char tableOutput5[] = "ns3.newout5";

    int recordNum = 3;
    TagC3Int8C1Int4 objIn1[recordNum] = {{1, 0, 1, 1, 1, 0}, {1, 0, 1, 2, 3, 0}, {2, 0, 2, 3, 4, 0}};
    TagC3Int8C1Int4 objIn2[recordNum] = {{1, 0, 1, 1, 1, 0}, {1, 0, 1, 2, 3, 0}, {1, 0, 2, 3, 4, 0}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8C1Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun10, result));
    if (!strstr(g_expectfun10, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun15, result));
    if (!strstr(g_expectfun15, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    int32_t upVerVal = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    TagC3Int8C1Int4 objIn3[3] = {{3, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {2, upVerVal, 1, 8, 9, 0}};
    TagC3Int8C1Int4 objIn4[6] = {{1, upVerVal, 1, 1, 1, 0}, {1, upVerVal, 1, 2, 3, 0}, {2, upVerVal, 2, 3, 4, 0},
        {3, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {2, upVerVal, 1, 8, 9, 0}};
    TagC3Int8C1Int4 objIn5[6] = {{1, upVerVal, 1, 1, 1, 0}, {1, upVerVal, 1, 2, 3, 0}, {1, upVerVal, 2, 3, 4, 0},
        {1, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {1, upVerVal, 1, 8, 9, 0}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn3, recordNum, C3Int8C1Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    recordNum = 6;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn4, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid4, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableNewMid4, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableNewMid5, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput4, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput5, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    count = readCount(g_stmt, tableMid2);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun12, result));
    if (!strstr(g_expectfun12, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁降级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollBackPatchSoName1));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun16, result));
    if (!strstr(g_expectfun16, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    TagC3Int8C1Int4 objIn6[1] = {{1, upVerVal, 10, 10, 6, 0}};
    recordNum = 1;
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn6, recordNum, C3Int8C1Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TagC3Int8C1Int4 objIn7[7] = {{1, upVerVal, 1, 1, 1, 0}, {1, upVerVal, 1, 2, 3, 0}, {2, upVerVal, 2, 3, 4, 0},
        {3, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {2, upVerVal, 1, 8, 9, 0}, {1, upVerVal, 10, 10, 6, 0}};
    TagC3Int8C1Int4 objIn8[7] = {{1, 0, 1, 1, 1, 0}, {1, 0, 1, 2, 3, 0}, {1, 0, 2, 3, 4, 0}, {1, 0, 4, 5, 6, 0},
        {1, upVerVal, 5, 6, 7, 0}, {1, upVerVal, 1, 8, 9, 0}, {1, upVerVal, 10, 10, 6, 0}};
    recordNum = 7;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn7, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun14, result));
    if (!strstr(g_expectfun14, result)) {
        system(g_command);
    }
    free(result);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.%block 1和%redo REDO_OFF，中间表定义有问题，新增transient(field)中间表，修改规则和新增规则，添加-supUpgErr选项预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_022)
{
    char soName[] = "enh002_022";
    char patchSoName1[] = "./datalogFile/midtable/enh002_022_patchV2.so";
    char rollBackPatchSoName1[] = "./datalogFile/midtable/enh002_022_rollbackV2.so";
    int ret = 0;

    ret = TestLoadDatalog("./datalogFile/midtable/enh002_022.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写数据，读数据");
    system("rm -rf /root/dtlpatchenh.txt");
    char tableInput1[] = "ns1.inp1";
    char tableMid1[] = "ns2.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableMid3[] = "ns2.mid3";
    char tableMid4[] = "ns2.newmid2";
    char tableNewMid4[] = "ns2.newmid4";
    char tableNewMid5[] = "ns2.newmid5";
    char tableOutput1[] = "ns3.out1";
    char tableOutput2[] = "ns3.out2";
    char tableOutput3[] = "ns3.out3";
    char tableOutput4[] = "ns3.newout4";
    char tableOutput5[] = "ns3.newout5";

    int recordNum = 3;
    TagC3Int8C1Int4 objIn1[recordNum] = {{1, 0, 1, 1, 1, 0}, {1, 0, 1, 2, 3, 0}, {2, 0, 2, 3, 4, 0}};
    TagC3Int8C1Int4 objIn2[recordNum] = {{1, 0, 1, 1, 1, 0}, {1, 0, 1, 2, 3, 0}, {1, 0, 2, 3, 4, 0}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8C1Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun10, result));
    if (!strstr(g_expectfun10, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t upVerVal = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    TagC3Int8C1Int4 objIn3[3] = {{3, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {2, upVerVal, 1, 8, 9, 0}};
    TagC3Int8C1Int4 objIn33[3] = {{1, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {1, upVerVal, 1, 8, 9, 0}};
    TagC3Int8C1Int4 objIn4[6] = {{1, upVerVal, 1, 1, 1, 0}, {1, upVerVal, 1, 2, 3, 0}, {2, upVerVal, 2, 3, 4, 0},
        {3, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {2, upVerVal, 1, 8, 9, 0}};
    TagC3Int8C1Int4 objIn5[6] = {{1, upVerVal, 1, 1, 1, 0}, {1, upVerVal, 1, 2, 3, 0}, {1, upVerVal, 2, 3, 4, 0},
        {1, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {1, upVerVal, 1, 8, 9, 0}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn3, recordNum, C3Int8C1Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    recordNum = 3;
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid4, objIn33, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableNewMid4, objIn33, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableNewMid5, objIn33, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput4, objIn33, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput5, objIn33, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    recordNum = 6;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn4, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn5, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun12, result));
    if (!strstr(g_expectfun12, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁降级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollBackPatchSoName1));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    TagC3Int8C1Int4 objIn6[1] = {{1, upVerVal, 10, 10, 6, 0}};
    recordNum = 1;
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn6, recordNum, C3Int8C1Int4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TagC3Int8C1Int4 objIn7[7] = {{1, upVerVal, 1, 1, 1, 0}, {1, upVerVal, 1, 2, 3, 0}, {2, upVerVal, 2, 3, 4, 0},
        {3, upVerVal, 4, 5, 6, 0}, {1, upVerVal, 5, 6, 7, 0}, {2, upVerVal, 1, 8, 9, 0}, {1, upVerVal, 10, 10, 6, 0}};
    TagC3Int8C1Int4 objIn8[7] = {{1, 0, 1, 1, 1, 0}, {1, 0, 1, 2, 3, 0}, {1, 0, 2, 3, 4, 0}, {1, 0, 4, 5, 6, 0},
        {1, upVerVal, 5, 6, 7, 0}, {1, upVerVal, 1, 8, 9, 0}, {1, upVerVal, 10, 10, 6, 0}};
    TagC3Int8C1Int4 objIn9[4] = {{1, 0, 1, 1, 1, 0}, {1, 0, 1, 2, 3, 0}, {1, 0, 2, 3, 4, 0},
        {1, upVerVal, 10, 10, 6, 0}};
    recordNum = 4;
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn9, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    recordNum = 7;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn7, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn8, recordNum, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun14, result));
    if (!strstr(g_expectfun14, result)) {
        system(g_command);
    }
    free(result);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.%block 1，中间表定义有问题，新增普通中间表，修改规则和新增规则，添加-supUpgErr选项，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_023)
{
    char soName[] = "enh002_023";
    char patchSoName1[] = "./datalogFile/midtable/enh002_023_patchV2.so";
    char rollBackPatchSoName1[] = "./datalogFile/midtable/enh002_023_rollbackV2.so";
    int ret = 0;

    ret = TestLoadDatalog("./datalogFile/midtable/enh002_023.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "写数据，读数据");
    system("rm -rf /root/dtlpatchenh.txt");
    char tableInput1[] = "ns1.inp1";
    char tableMid1[] = "ns2.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableMid3[] = "ns2.mid3";
    char tableNewMid2[] = "ns2.newmid2";
    char tableNewMid4[] = "ns2.newmid4";
    char tableNewMid5[] = "ns2.newmid5";
    char tableOutput1[] = "ns3.out1";
    char tableOutput2[] = "ns3.out2";
    char tableOutput3[] = "ns3.out3";
    char tableOutput4[] = "ns3.newout4";
    char tableOutput5[] = "ns3.newout5";

    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun1, result));
    if (!strstr(g_expectfun1, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁升级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun6, result));
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    int32_t upVerVal = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn3[3] = {{3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn4[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5}, {3, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {2, upVerVal, 1, 8, 9}};
    C3Int8T objIn5[6] = {
        {1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 4, 5, 6},
        {1, upVerVal, 5, 6, 7}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    recordNum = 6;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableNewMid2, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableNewMid4, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableNewMid5, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput4, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput5, objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    count = readCount(g_stmt, tableMid2);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun7, result));
    if (!strstr(g_expectfun7, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    AW_FUN_Log(LOG_STEP, "热补丁降级，写数据，读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollBackPatchSoName1));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun8, result));
    if (!strstr(g_expectfun8, result)) {
        system(g_command);
    }
    free(result);
    system("rm -rf /root/dtlpatchenh.txt");

    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput1, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn6[1] = {{1, upVerVal, 10, 10, 6}};
    recordNum = 1;
    ret = writeRecord(g_conn, g_stmt, tableInput1, objIn6, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn7[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 3, 5},
        {3, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7},
        {2, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    C3Int8T objIn8[7] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 4, 5, 6}, {1, upVerVal, 5, 6, 7},
        {1, upVerVal, 1, 8, 9}, {1, upVerVal, 10, 10, 6}};
    recordNum = 7;
    ret = readRecord(g_conn, g_stmt, tableInput1, objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableMid3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput1, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput2, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, tableOutput3, objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/dtlpatchenh.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectfun9, result));
    if (!strstr(g_expectfun9, result)) {
        system(g_command);
    }
    free(result);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 024.%block 0，新增外部表，未创建fastpath表对应的外部表，加载升级so，报错
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test001";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so，加载报错
    ret = TestLoadUpgradeSo(patchSoName);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: LOAD_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 025.%block
0，新增外部表，未创建fastpath表对应的外部表，开启按需建表配置项并弄好路径，加载升级so，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test2, DataLog_098_002_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 按需建表，加载升级so成功
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 1, 3}, {1, upVerVal, 2, 3, 5}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn4[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn5[recordNum * 2] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 1, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn4, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum * 2, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn6[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 3, 1}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn6, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn7[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 1, 3}, {1, upVerVal2, 2, 3, 5},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 3, 1}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 026.%block 0，输出表定义有问题，新增外部表含创建fastpath表，修改规则和新增规则，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public


    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "out2");
    readJanssonFile("./schema_file/out2.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 1, 3}, {1, upVerVal, 2, 3, 5}};
    C3Int8T objIn4[0] = {};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 1, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn5, recordNum * 2, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 3, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 1, 3}, {1, upVerVal2, 2, 3, 5},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 3, 1}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, 0, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out2 read complete!!!");

    // 删fastpath表
    ret = GmcDropVertexLabel(g_stmt, "out2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.%block 1,输出表定义有问题，新增外部表含创建fastpath表，修改规则和新增规则，添加-supUpgErr选项，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public


    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "out2");
    readJanssonFile("./schema_file/out2.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表

    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 1, 3}, {1, upVerVal, 2, 3, 5}};
    C3Int8T objIn4[0] = {};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 1, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn5, recordNum * 2, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 3, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 1, 3}, {1, upVerVal2, 2, 3, 5},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 3, 1}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, 0, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out2 read complete!!!");

    // 删fastpath表
    ret = GmcDropVertexLabel(g_stmt, "out2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 028.%block 1和%redo REDO_OFF,输出表定义有问题，新增外部表含创建fastpath表，修改规则和新增规则，添加-supUpgErr选项，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public


    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "out2");
    readJanssonFile("./schema_file/out2.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    
    // 对输入表写入数据
    C3Int8T objIn3[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn4[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 1, 3}, {1, upVerVal, 2, 3, 5}};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn4, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn5, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn6[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 3, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn6, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn6, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn8[recordNum + 2] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 1, 3}, {1, upVerVal2, 2, 3, 5},
        {1, upVerVal2, 1, 1, 4}, {1, upVerVal2, 2, 3, 1}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn5, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert out2 read complete!!!");

    // 删fastpath表
    ret = GmcDropVertexLabel(g_stmt, "out2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 029.%block 0，输出表定义有问题，新增tbm表，原始.d有tbm表，修改规则和新增规则，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test005";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 对输入表写入数据
    C3Int8T objIn2[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    C3Int8T objIn3[recordNum - 1] = {{1, 0, 1, 1, 4}, {3, 0, 2, 3, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn3, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	(void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
	(void)snprintf(g_command, MAX_CMD_SIZE, "md5sum test.txt");
	system(g_command);
#if defined RUN_INDEPENDENT
	ret = executeCommand(g_command, "c90f8622c71842352df676c8dc772063");
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	if (ret != 0) {
		(void)SystemSnprintf("cat test.txt");
	}
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 030.%block 1,输出表定义有问题，新增tbm表，原始.d有tbm表，修改规则和新增规则，添加-supUpgErr选项，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test006";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 对输入表写入数据
    C3Int8T objIn2[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    C3Int8T objIn3[recordNum - 1] = {{1, 0, 1, 1, 4}, {3, 0, 2, 3, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn3, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	(void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
	(void)snprintf(g_command, MAX_CMD_SIZE, "md5sum test.txt");
	system(g_command);
#if defined RUN_INDEPENDENT
	ret = executeCommand(g_command, "6956a53afca2f344fac9bfcfb21e9950");
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	if (ret != 0) {
		(void)SystemSnprintf("cat test.txt");
	}
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 031.%block 1和%redo REDO_OFF,输出表定义有问题，新增tbm表，原始.d有tbm表，修改规则和新增规则，添加-supUpgErr选项，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test007";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 对输入表写入数据
    C3Int8T objIn2[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    C3Int8T objIn3[recordNum - 1] = {{1, 0, 1, 1, 4}, {3, 0, 2, 3, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn3, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	(void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
	(void)snprintf(g_command, MAX_CMD_SIZE, "md5sum test.txt");
	system(g_command);
#if defined RUN_INDEPENDENT
	ret = executeCommand(g_command, "af592e411301c80dbcf029f6781b48d6");
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	if (ret != 0) {
		(void)SystemSnprintf("cat test.txt");
	}
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 补丁1：新增notify和空的输入表；创建订阅关系；补丁2：新增规则将存量输入表inp1投影到新增的notify表
/* ****************************************************************************
 Description  : 032.%block 0,输出表定义有问题，新增notify表，修改规则和新增规则，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test008";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[13] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {-1, 0, 1, 1, 1}, {-1, 0, 1, 1, 3},
        {-1, 0, 2, 3, 5}, {1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {1, 0, 5, 1, 1}, {1, 0, 10, 3, 5},
        {1, 0, 1, 8, 9}, {1, 0, 10, 1, 1}};
    
    C3Int8T objPub02[12] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {1, 1, 5, 1, 1}, {1, 1, 10, 3, 5},
        {1, 1, 1, 8, 9}, {-1, 0, 1, 1, 1}, {-1, 0, 1, 1, 3}, {-1, 0, 2, 3, 5}, {-1, 0, 5, 1, 1}, {-1, 0, 10, 3, 5},
        {-1, 0, 1, 8, 9}};

    GmcConnT *conn_sn = NULL;
    GmcStmtT *stmt_sn = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subNotifynewout1";
    ret = testSubConnect(&conn_sn, &stmt_sn, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData01 = {0}, userData02 = {0};
    userData01.funcType = 1;
    userData01.objLen = 13;
    userData01.obj = objPub;
    userData01.isResourcePubSub = false;

    userData02.funcType = 1;
    userData02.objLen = 12;
    userData02.obj = objPub02;
    userData02.isResourcePubSub = false;

    // 创建订阅关系
    ret = createSubscription(g_stmt, conn_sn, (char *)"schema_file/subinfo01.gmjson", &userData01,
        1000, subName01, snCallback, C3Int8RescGet);

    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待推送条数
    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_INSERT, recordNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 创建订阅关系
    ret = createSubscription(g_stmt, conn_sn, (char *)"schema_file/subinfo02.gmjson", &userData02,
        1000, subName02, snCallback, C3Int8RescGet02);
    // 加载补丁2
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_INSERT, recordNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    C3Int8T objIn2[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData02.data, GMC_SUB_EVENT_INSERT, recordNum * 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_INSERT, recordNum * 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData02.data, GMC_SUB_EVENT_INSERT, recordNum * 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅关系
    ret = cancelSubscription(g_stmt, subName02, &userData02, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    C3Int8T objIn3[1] = {{3, 0, 10, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn3, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_INSERT, 1, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅关系
    ret = cancelSubscription(g_stmt, subName01, &userData01, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开订阅连接
    ret = testGmcDisconnect(conn_sn, stmt_sn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 033.%block 1,输出表定义有问题，新增notify表，修改规则和新增规则，添加-supUpgErr选项，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test009";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[13] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {-1, 0, 1, 1, 1}, {-1, 0, 1, 1, 3},
        {-1, 0, 2, 3, 5}, {1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {1, 0, 5, 1, 1}, {1, 0, 10, 3, 5},
        {1, 0, 1, 8, 9}, {1, 0, 10, 1, 1}};
    
    C3Int8T objPub02[12] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {1, 0, 5, 1, 1}, {1, 0, 10, 3, 5},
        {1, 0, 1, 8, 9}, {-1, 0, 1, 1, 1}, {-1, 0, 1, 1, 3}, {-1, 0, 2, 3, 5}, {-1, 0, 5, 1, 1}, {-1, 0, 10, 3, 5},
        {-1, 0, 1, 8, 9}};

    GmcConnT *conn_sn = NULL;
    GmcStmtT *stmt_sn = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subNotifynewout1";
    ret = testSubConnect(&conn_sn, &stmt_sn, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData01 = {0}, userData02 = {0};
    userData01.funcType = 1;
    userData01.objLen = 13;
    userData01.obj = objPub;
    userData01.isResourcePubSub = false;

    userData02.funcType = 1;
    userData02.objLen = 12;
    userData02.obj = objPub02;
    userData02.isResourcePubSub = false;

    // 创建订阅关系
    ret = createSubscription(g_stmt, conn_sn, (char *)"schema_file/subinfo01.gmjson", &userData01,
        1000, subName01, snCallback, C3Int8RescGet);

    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待推送条数
    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_INSERT, recordNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 创建订阅关系
    ret = createSubscription(g_stmt, conn_sn, (char *)"schema_file/subinfo02.gmjson", &userData02,
        1000, subName02, snCallback, C3Int8RescGet02);
    // 加载补丁2
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_INSERT, recordNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    C3Int8T objIn2[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData02.data, GMC_SUB_EVENT_INSERT, recordNum * 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_INSERT, recordNum * 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData02.data, GMC_SUB_EVENT_INSERT, recordNum * 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅关系
    ret = cancelSubscription(g_stmt, subName02, &userData02, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    C3Int8T objIn3[1] = {{3, 0, 10, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn3, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_INSERT, 1, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅关系
    ret = cancelSubscription(g_stmt, subName01, &userData01, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开订阅连接
    ret = testGmcDisconnect(conn_sn, stmt_sn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 034.%block 1和%redo REDO_OFF,输出表定义有问题，新增notify表，修改规则和新增规则，添加-supUpgErr选项，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test010";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[4] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5},  {1, 0, 10, 1, 1}};
    
    C3Int8T objPub02[3] = {{1, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {1, 0, 1, 8, 9}};

    GmcConnT *conn_sn = NULL;
    GmcStmtT *stmt_sn = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subNotifynewout1";
    ret = testSubConnect(&conn_sn, &stmt_sn, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData01 = {0}, userData02 = {0};
    userData01.funcType = 1;
    userData01.objLen = 4;
    userData01.obj = objPub;
    userData01.isResourcePubSub = false;

    userData02.funcType = 1;
    userData02.objLen = 3;
    userData02.obj = objPub02;
    userData02.isResourcePubSub = false;

    // 创建订阅关系
    ret = createSubscription(g_stmt, conn_sn, (char *)"schema_file/subinfo01.gmjson", &userData01,
        1000, subName01, snCallback, C3Int8RescGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待推送条数
    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_INSERT, recordNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
   
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = createSubscription(g_stmt, conn_sn, (char *)"schema_file/subinfo02.gmjson", &userData02,
        1000, subName02, snCallback, C3Int8RescGet02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    C3Int8T objIn2[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData02.data, GMC_SUB_EVENT_INSERT, recordNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview -q V\\$CATA_LABEL_SUBS_INFO");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    C3Int8T objIn3[1] = {{3, 0, 10, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn3, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_INSERT, 1, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅关系
    ret = cancelSubscription(g_stmt, subName01, &userData01, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放userData02内存
    testSnFreeUserData(userData02.data);

    // 断开订阅连接
    ret = testGmcDisconnect(conn_sn, stmt_sn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 035.%block 0,输出表定义有问题，新增普通表，修改规则和新增规则，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test011";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 1, 3}, {1, upVerVal, 2, 3, 5}};
    C3Int8T objIn4[0] = {};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.newout1", objIn3, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.newout1 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 1, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.newout1", objIn5, recordNum * 2, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.newout1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 3, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 1, 3}, {1, upVerVal2, 2, 3, 5},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 3, 1}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");

    // ns2.newout1无数据
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "ns2.newout1", g_connServer);
    ret = executeCommand(g_command, "[WARN] Open vertex label for record unsuccessful");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 036.%block 1，输出表定义有问题，新增可更新表，修改规则和新增规则，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test012";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 1, 3}, {1, upVerVal, 2, 3, 5}};
    C3Int8T objIn4[0] = {};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.newout1", objIn3, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.newout1 read complete!!!");

    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 1, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.newout1", objIn5, recordNum * 2, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.newout1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 3, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 1, 3}, {1, upVerVal2, 2, 3, 5},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 3, 1}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");

    // ns2.newout1无数据
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "ns2.newout1", g_connServer);
    ret = executeCommand(g_command, "[WARN] Open vertex label for record unsuccessful");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 037.%bock 1和%redo REDO_OFF，patch.d,同时修改多个关联规则，access_delta(重做输出表)，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test013";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{2, 0, 1, 1, 1}, {2, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn3[recordNum * 2] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 2, 2},
        {1, 0, 2, 2, 4}, {1, 0, 3, 4, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out1 read complete!!!");

    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn3, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out2 read complete!!!");


    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    // 对输入表写入数据
    C3Int8T objIn5[1] = {{3, 0, 5, 1, 1}};
    C3Int8T objIn6[recordNum + 1] = {{2, 0, 1, 1, 1}, {2, 0, 1, 1, 3}, {2, 0, 2, 3, 5}, {2, 0, 5, 1, 1}};
    C3Int8T objIn7[recordNum * 2 + 2] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 2, 2},
        {1, 0, 2, 2, 4}, {1, 0, 3, 4, 6}, {1, 0, 5, 1, 100}, {1, 0, 6, 2, 2}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn5, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn7, recordNum * 2 + 2, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    C3Int8T objIn8[1] = {{3, 0, 10, 1, 1}};
    C3Int8T objIn9[recordNum + 2] = {{2, 0, 1, 1, 1}, {2, 0, 1, 1, 3}, {2, 0, 2, 3, 5}, {2, 0, 5, 1, 1},
        {2, 0, 10, 1, 1}};
    C3Int8T objIn10[recordNum * 2 + 4] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {1, 0, 2, 2, 2},
        {1, 0, 2, 2, 4}, {1, 0, 3, 4, 6}, {1, 0, 5, 1, 100}, {1, 0, 6, 2, 2}, {1, 0, 10, 1, 1}, {1, 0, 11, 2, 2}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn8, 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn8, 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn9, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");

    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn10, recordNum * 2 + 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out2 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 038.%block 0，同一topo图，其中一个规则新增表，另外一条规则新增function，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test014";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{2, 0, 1, 1, 2}, {2, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum + 2] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 1, 100},
        {1, upVerVal, 1, 3, 100}, {1, upVerVal, 2, 3, 100}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 3] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 8, 9},
        {1, upVerVal, 1, 1, 100}, {1, upVerVal, 1, 3, 100}, {1, upVerVal, 2, 3, 100}, {1, upVerVal, 5, 1, 100},
        {1, upVerVal, 10, 3, 100}, {1, upVerVal, 1, 8, 100}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum * 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 3, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn8[recordNum * 2 + 2] = {{2, upVerVal2, 1, 1, 2}, {2, upVerVal2, 1, 3, 3}, {2, upVerVal2, 2, 3, 5},
        {2, upVerVal2, 5, 1, 1}, {2, upVerVal2, 10, 3, 5}, {2, upVerVal2, 1, 8, 9}, {2, upVerVal2, 1, 1, 4},
        {2, upVerVal2, 2, 3, 1}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 039.%block 1，同一topo图，其中一个规则新原始.d输入表，另外一条规则新增function，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test015";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{2, 0, 1, 1, 2}, {2, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum + 2] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 1, 100},
        {1, upVerVal, 1, 3, 100}, {1, upVerVal, 2, 3, 100}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 3] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 8, 9},
        {1, upVerVal, 1, 1, 100}, {1, upVerVal, 1, 3, 100}, {1, upVerVal, 2, 3, 100}, {1, upVerVal, 5, 1, 100},
        {1, upVerVal, 10, 3, 100}, {1, upVerVal, 1, 8, 100}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum * 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 3, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn8[recordNum * 2 + 2] = {{2, upVerVal2, 1, 1, 2}, {2, upVerVal2, 1, 3, 3}, {2, upVerVal2, 2, 3, 5},
        {2, upVerVal2, 5, 1, 1}, {2, upVerVal2, 10, 3, 5}, {2, upVerVal2, 1, 8, 9}, {2, upVerVal2, 1, 1, 4},
        {2, upVerVal2, 2, 3, 1}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 同039，数据有问题，待确认
/* ****************************************************************************
 Description  : 040.%block 1，同一topo图，其中一个规则新增表，另外一条规则删除一个function，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test016";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum - 1] = {{2, 0, 1, 1, 2}, {2, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum * 2] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 4},
        {1, upVerVal, 1, 1, 100}, {1, upVerVal, 1, 3, 100}, {1, upVerVal, 2, 3, 100}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 4] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 4},
        {1, upVerVal, 1, 1, 100}, {1, upVerVal, 1, 3, 100}, {1, upVerVal, 2, 3, 100}, {1, upVerVal, 5, 1, 100},
        {1, upVerVal, 10, 3, 100}, {1, upVerVal, 1, 8, 100}, {1, upVerVal, 5, 1, 6},{1, upVerVal, 10, 3, 13},
        {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum * 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn8[recordNum + 1] = {{2, upVerVal2, 1, 1, 2}, {2, upVerVal2, 2, 3, 5}, {2, upVerVal2, 1, 8, 9},
        {2, upVerVal2, 2, 4, 6}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 041.%block 0，patch.d，新增规则覆盖，新增输入表、新增中间表、新增输出表，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test017";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    // 对新增输入表插入数据
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 2}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 3, 3},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 4, 6}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "ns2.out2", g_connServer);
    ret = executeCommand(g_command, "[WARN] Open vertex label for record unsuccessful");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 042.%block 1，patch.d，新增规则覆盖，新增输入表、新增中间表、新增输出表，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test018";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    // 对新增输入表插入数据
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 2}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 3, 3},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 4, 6}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "ns2.out2", g_connServer);
    ret = executeCommand(g_command, "[WARN] Open vertex label for record unsuccessful");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 043.%block 0，patch.d，新增规则覆盖，原始.d输入表、新增中间表、新增输出表,预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test019";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 2}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 3, 3},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 4, 6}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "ns2.out2", g_connServer);
    ret = executeCommand(g_command, "[WARN] Open vertex label for record unsuccessful");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 044.%block 1，patch.d，新增规则覆盖，原始.d输入表、新增中间表、新增输出表，添加-supUpgErr选项,预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test020";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 2}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 3, 3},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 4, 6}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "ns2.out2", g_connServer);
    ret = executeCommand(g_command, "[WARN] Open vertex label for record unsuccessful");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 045.%block 0，patch.d，新增规则覆盖，原始.d输入表、中间表、新增输出表,预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test021";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 2}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 3, 3},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 4, 6}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "ns2.out2", g_connServer);
    ret = executeCommand(g_command, "[WARN] Open vertex label for record unsuccessful");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 046.%block 1，patch.d，新增规则覆盖，原始.d输入表、中间表、新增输出表，添加-supUpgErr选项,预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test022";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 2, 3, 5}, {1, upVerVal, 1, 3, 3},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 2}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 3, 3},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 4, 6}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "ns2.out2", g_connServer);
    ret = executeCommand(g_command, "[WARN] Open vertex label for record unsuccessful");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 047.%block 0，patch.d，新增规则覆盖，原始.d输入表、中间表、输出表,预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test023";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    C3Int8T objIn3[recordNum * 2] = {{2, upVerVal, 1, 1, 2}, {2, upVerVal, 2, 3, 5}, {2, upVerVal, 1, 3, 3},
        {2, upVerVal, 1, 1, 100}, {2, upVerVal, 2, 3, 100}, {2, upVerVal, 1, 3, 100}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 4] = {{2, upVerVal, 1, 1, 2}, {2, upVerVal, 2, 3, 5}, {2, upVerVal, 1, 3, 3},
        {2, upVerVal, 1, 1, 100}, {2, upVerVal, 2, 3, 100}, {2, upVerVal, 1, 3, 100}, {2, upVerVal, 5, 1, 1},
        {2, upVerVal, 10, 3, 5}, {2, upVerVal, 1, 8, 9}, {2, upVerVal, 5, 1, 100}, {2, upVerVal, 10, 3, 100},
        {2, upVerVal, 1, 8, 100}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum * 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 2}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 3, 3},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 4, 6}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
  
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 048.%block 1，patch.d，新增规则覆盖，原始.d输入表、中间表、输出表，添加-supUpgErr选项,预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test024";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 3, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn3[recordNum * 2] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 1, 3, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 1, 1, 100}, {1, upVerVal, 2, 3, 100}, {1, upVerVal, 1, 3, 100}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn3, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 对输入表写入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    C3Int8T objIn6[recordNum * 4] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 1, 3, 3}, {1, upVerVal, 2, 3, 5},
        {1, upVerVal, 5, 1, 1}, {1, upVerVal, 10, 3, 5}, {1, upVerVal, 1, 8, 9}, {1, upVerVal, 1, 1, 100},
        {1, upVerVal, 2, 3, 100}, {1, upVerVal, 1, 3, 100}, {1, upVerVal, 5, 1, 100}, {1, upVerVal, 10, 3, 100},
        {1, upVerVal, 1, 8, 100}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn6, recordNum * 4, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    C3Int8T objIn8[recordNum * 2 + 2] = {{1, upVerVal2, 1, 1, 2}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 3, 3},
        {1, upVerVal2, 5, 1, 1}, {1, upVerVal2, 10, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 1, 1, 4},
        {1, upVerVal2, 2, 4, 6}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum * 2 + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
  
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 049.%block 0，patch.d，覆盖namespace、precedence、修改规则、新增规则、新增输入表、中间表、输出表、
 agg、function，新增和修改规则含原始.d中的表和新增的表,预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test025";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public


    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/extern.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 2, 3, 5}};
    C4Int8T objIn3[recordNum] = {{1, 0, 1, 1, 2, 1}, {1, 0, 1, 1, 3, 1}, {1, 0, 2, 3, 5, 2}};
    C4Int8T objIn4[recordNum - 1] = {{1, 0, 3, 2, 1, 1}, {1, 0, 5, 5, 2, 3}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn3, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn4, recordNum - 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    // 插入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 1, 1, 9}, {1, 0, 2, 3, 6}, {2, 0, 1, 8, 9}};
    C4Int8T objIn6[recordNum] = {{3, 0, 5, 1, 2, 1}, {1, 0, 10, 3, 5, 2}, {2, 0, 1, 8, 9, 3}};
    C3Int8T objIn7[recordNum + 2] = {{2, upVerVal, 1, 1, 1}, {1, upVerVal, 2, 3, 5}, {2, upVerVal, 2, 3, 6},
        {1, upVerVal, 1, 8, 9}, {1, upVerVal, 1, 1, 9}};
    C4Int8T objIn8[recordNum + 1] = {{1, upVerVal, 1, 1, 8, 9}, {1, upVerVal, 1, 1, 1, 3}, {1, upVerVal, 5, 1, 1, 2},
        {1, upVerVal, 10, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn6, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn7, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn8, recordNum + 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "extern", objIn5, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade extern read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn9[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    C4Int8T objIn10[recordNum - 1] = {{1, upVerVal2, 1, 1, 10, 10}, {3, upVerVal2, 1, 8, 1, 1}};
    C3Int8T objIn11[recordNum] = {{1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 2, 4, 6}};
    C4Int8T objIn12[recordNum + 2] = {{1, upVerVal2, 10, 2, 1, 1}, {1, upVerVal2, 5, 5, 2, 3},
        {1, upVerVal2, 2, 2, 5, 1}, {1, upVerVal2, 5, 5, 10, 3}, {1, upVerVal2, 9, 1, 1, 8}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn9, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn10, recordNum - 1, C4Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn11, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn12, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out2 read complete!!!");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "ns1.mid3", g_connServer);
    ret = executeCommand(g_command, "[WARN] Open vertex label for record unsuccessful");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删fastpath表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	(void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
	(void)snprintf(g_command, MAX_CMD_SIZE, "md5sum test.txt");
	system(g_command);
#if defined RUN_INDEPENDENT
	ret = executeCommand(g_command, "e7d2298f28864bdba9147a604f776711");
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	if (ret != 0) {
		(void)SystemSnprintf("cat test.txt");
	}
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 050.%block 1，patch.d，覆盖namespace、precedence、修改规则、新增规则、新增输入表、中间表、输出表、agg、
 function，新增和修改规则含原始.d中的表和新增的表，添加-supUpgErr选项,预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test026";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public


    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/extern.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 2, 3, 5}};
    C4Int8T objIn3[recordNum] = {{1, 0, 1, 1, 2, 1}, {1, 0, 1, 1, 3, 1}, {1, 0, 2, 3, 5, 2}};
    C4Int8T objIn4[recordNum - 1] = {{1, 0, 3, 2, 1, 1}, {1, 0, 5, 5, 2, 3}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn3, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn4, recordNum - 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    // 插入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 1, 1, 9}, {1, 0, 2, 3, 6}, {2, 0, 1, 8, 9}};
    C4Int8T objIn6[recordNum] = {{3, 0, 5, 1, 2, 1}, {1, 0, 10, 3, 5, 2}, {2, 0, 1, 8, 9, 3}};
    C3Int8T objIn7[recordNum + 2] = {{2, upVerVal, 1, 1, 1}, {1, upVerVal, 2, 3, 5}, {2, upVerVal, 2, 3, 6},
        {1, upVerVal, 1, 8, 9}, {1, upVerVal, 1, 1, 9}};
    C4Int8T objIn8[recordNum + 1] = {{1, upVerVal, 1, 1, 8, 9}, {1, upVerVal, 1, 1, 1, 3}, {1, upVerVal, 5, 1, 1, 2},
        {1, upVerVal, 10, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn6, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn7, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn8, recordNum + 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "extern", objIn5, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade extern read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn9[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    C4Int8T objIn10[recordNum - 1] = {{1, upVerVal2, 1, 1, 10, 10}, {3, upVerVal2, 1, 8, 1, 1}};
    C3Int8T objIn11[recordNum] = {{1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 8, 9}, {1, upVerVal2, 2, 4, 6}};
    C4Int8T objIn12[recordNum + 2] = {{1, upVerVal2, 10, 2, 1, 1}, {1, upVerVal2, 5, 5, 2, 3},
        {1, upVerVal2, 2, 2, 5, 1}, {1, upVerVal2, 5, 5, 10, 3}, {1, upVerVal2, 9, 1, 1, 8}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn9, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn10, recordNum - 1, C4Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn11, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn12, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out2 read complete!!!");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "ns1.mid3", g_connServer);
    ret = executeCommand(g_command, "[WARN] Open vertex label for record unsuccessful");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删fastpath表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	(void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
	(void)snprintf(g_command, MAX_CMD_SIZE, "md5sum test.txt");
	system(g_command);
#if defined RUN_INDEPENDENT
	ret = executeCommand(g_command, "317f2450786cba20d319b52656498eab");
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	if (ret != 0) {
		(void)SystemSnprintf("cat test.txt");
	}
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 051.%block 1和%redo_REDO_OFF，patch.d，覆盖namespace、precedence、修改规则、新增规则、新增输入表、中间表、
 输出表、agg、function，新增和修改规则含原始.d中的表和新增的表，添加-supUpgErr选项,预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test027";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public


    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/extern.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 2, 3, 5}};
    C4Int8T objIn3[recordNum] = {{1, 0, 1, 1, 2, 1}, {1, 0, 1, 1, 3, 1}, {1, 0, 2, 3, 5, 2}};
    C4Int8T objIn4[recordNum - 1] = {{1, 0, 3, 2, 1, 1}, {1, 0, 5, 5, 2, 3}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn3, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn4, recordNum - 1, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 1", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    // 插入数据
    C3Int8T objIn5[recordNum] = {{3, 0, 1, 1, 9}, {1, 0, 2, 3, 6}, {2, 0, 1, 8, 9}};
    C4Int8T objIn6[recordNum] = {{3, 0, 5, 1, 2, 1}, {1, 0, 10, 3, 5, 2}, {2, 0, 1, 8, 9, 3}};
    C3Int8T objIn7[recordNum + 1] = {{1, upVerVal, 2, 3, 5}, {2, upVerVal, 2, 3, 6}, {1, upVerVal, 1, 8, 9},
        {1, upVerVal, 1, 1, 9}};
    C4Int8T objIn8[recordNum + 2] = {{1, upVerVal, 3, 2, 1, 1}, {1, upVerVal, 5, 5, 2, 3}, {1, upVerVal, 5, 5, 1, 2},
        {1, upVerVal, 10, 10, 3, 5}, {1, upVerVal, 1, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn5, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn6, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn7, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn8, recordNum + 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "extern", objIn5, recordNum, C3Int8Get, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade extern read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn9[recordNum - 1] = {{1, upVerVal2, 1, 1, 4}, {3, upVerVal2, 2, 4, 6}};
    C4Int8T objIn10[recordNum - 1] = {{1, upVerVal2, 1, 1, 10, 10}, {3, upVerVal2, 1, 8, 1, 1}};
    C3Int8T objIn11[recordNum + 2] = {{1, upVerVal2, 2, 3, 5}, {2, upVerVal2, 2, 3, 6}, {1, upVerVal2, 1, 8, 9},
        {1, upVerVal2, 1, 1, 9}, {1, upVerVal2, 2, 4, 6}};
    C4Int8T objIn12[recordNum * 2] = {{1, upVerVal2, 1, 1, 8, 9}, {1, upVerVal2, 5, 5, 2, 3},
        {1, upVerVal2, 5, 5, 1, 2}, {1, upVerVal2, 10, 2, 1, 1}, {1, upVerVal2, 9, 1, 1, 8},
        {1, upVerVal2, 10, 10, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn9, recordNum - 1, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn10, recordNum - 1, C4Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn11, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn12, recordNum * 2, C4Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out2 read complete!!!");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s ", g_toolPath, "ns1.mid3", g_connServer);
    ret = executeCommand(g_command, "[WARN] Open vertex label for record unsuccessful");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删fastpath表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	(void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
	(void)snprintf(g_command, MAX_CMD_SIZE, "md5sum test.txt");
	system(g_command);
#if defined RUN_INDEPENDENT
	ret = executeCommand(g_command, "b7ddde5066dac16627602615cebe62da");
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	if (ret != 0) {
		(void)SystemSnprintf("cat test.txt");
	}
#else
    (void)SystemSnprintf("cat test.txt");
#endif

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 052.%block 0，原始.d含access_delta、access_current；patch.d新增规则含原始.d中的function、
 输入表为过期表、transient（tuple）、transient(finish)，预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test028";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, -2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 2, 2, 10}, {1, 0, 1, 3, 3}, {1, 0, 2, 3, 5}};
    C3Int8T objIn3[recordNum + 1] = {{1, 0, 2, 2, 2}, {1, 0, 2, 4, 4}, {1, 0, 3, 4, 4}, {1, 0, 3, 3, 3}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8TimeoutSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待数据过期
    sleep(3);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8TimeoutGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn3, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    // 对输入表写入数据
    C3Int8T objIn4[1] = {{3, 0, 5, 1, -1}};
    C3Int8T objIn5[recordNum + 1] = {{1, upVerVal, 2, 2, 100}, {1, upVerVal, 2, 3, 100}, {1, upVerVal, 1, 3, 100},
        {1, upVerVal, 10, 2, 100}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 2, 2, 2}, {1, upVerVal, 2, 4, 4}, {1, upVerVal, 3, 4, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 6, 2, 2}, {1, upVerVal, 5, 1, -1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn4, 1, C3Int8TimeoutSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn4, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn4, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(3);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn5, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);


    // 插入数据
    C3Int8T objIn7[1] = {{1, upVerVal2, 1, 1, 100}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, 1, C3Int8TimeoutSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn8[recordNum + 2] = {{1, upVerVal2, 2, 2, 8}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 3, 3},
        {1, upVerVal2, 10, 2, 9}, {1, upVerVal2, 1, 1, 100}};
    // transient(tuple)表不存heap
    C3Int8T objIn9[recordNum * 2 + 1] = {{1, upVerVal2, 2, 2, 2}, {1, upVerVal2, 2, 4, 4}, {1, upVerVal2, 3, 4, 4},
        {1, upVerVal2, 3, 3, 3}, {1, upVerVal2, 6, 2, 2}, {1, 1, 5, 1, -1}, {1, upVerVal2, 11, 3, 3}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum + 2, C3Int8TimeoutGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn9, recordNum * 2 + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out2 read complete!!!");
  
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 053.%block 1，原始.d含access_delta、access_current；patch.d新增规则含原始.d中的function、
 输入表为过期表、transient（tuple）、transient(finish)，添加-supUpgErr预期数据符合预期
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test029";
    int ret = 0, cnt = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, -2}, {1, 0, 1, 3, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 2, 2, 10}, {1, 0, 1, 3, 3}, {1, 0, 2, 3, 5}};
    C3Int8T objIn3[recordNum + 1] = {{1, 0, 2, 2, 2}, {1, 0, 2, 4, 4}, {1, 0, 3, 4, 4}, {1, 0, 3, 3, 3}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8TimeoutSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 等待数据过期
    sleep(3);
    // 校验输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn2, recordNum, C3Int8TimeoutGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn3, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns2.out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    // 校验热补丁视图重做状态
    cnt = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    while(cnt < TRY_CNT) {
        usleep(1000 * 500);
        ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
        if (ret == GMERR_OK) {
            break;
        }
        cnt++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 加载升级so
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    // 对输入表写入数据
    C3Int8T objIn4[1] = {{3, 0, 5, 1, -1}};
    C3Int8T objIn5[recordNum + 1] = {{1, upVerVal, 2, 2, 100}, {1, upVerVal, 2, 3, 100}, {1, upVerVal, 1, 3, 100},
        {1, upVerVal, 10, 2, 100}};
    C3Int8T objIn6[recordNum * 2] = {{1, upVerVal, 2, 2, 2}, {1, upVerVal, 2, 4, 4}, {1, upVerVal, 3, 4, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 6, 2, 2}, {1, upVerVal, 5, 1, -1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn4, 1, C3Int8TimeoutSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn4, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn4, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(3);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn5, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn6, recordNum * 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade ns2.out2 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns2.out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);

    // 插入数据
    C3Int8T objIn7[1] = {{1, upVerVal2, 1, 1, 100}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn7, 1, C3Int8TimeoutSet, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    C3Int8T objIn8[recordNum + 2] = {{1, upVerVal2, 2, 2, 8}, {1, upVerVal2, 2, 3, 5}, {1, upVerVal2, 1, 3, 3},
        {1, upVerVal2, 10, 2, 9}, {1, upVerVal2, 1, 1, 100}};
    // transient(tuple)表不存heap
    C3Int8T objIn9[recordNum * 2 + 1] = {{1, upVerVal2, 2, 2, 2}, {1, upVerVal2, 2, 4, 4}, {1, upVerVal2, 3, 4, 4},
        {1, upVerVal2, 3, 3, 3}, {1, upVerVal2, 6, 2, 2}, {1, 0, 5, 1, -1}, {1, upVerVal2, 11, 3, 3}};
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn8, recordNum + 2, C3Int8TimeoutGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "ns2.out2", objIn9, recordNum * 2 + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out2 read complete!!!");
  
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 054.%block 0，patch1.d新增输出是外部表和空的输入表，订阅外部表，patch2.d新增规则投影到新增外部表
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test030";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);

    // 切换namespace为public


    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "out2");
    readJanssonFile("./schema_file/out2.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[6] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {3, 0, 5, 1, 1}, {1, 0, 10, 3, 5},
        {2, 0, 1, 8, 9}};
    
    GmcConnT *conn_sn = NULL;
    GmcStmtT *stmt_sn = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subExternout2";
    ret = testSubConnect(&conn_sn, &stmt_sn, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData01 = {0};
    userData01.objLen = 6;
    userData01.obj = objPub;

    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 创建订阅关系
    ret = createSubscription02(g_stmt, conn_sn, (char *)"schema_file/subinfo03.gmjson", &userData01,
        1000, subName01, snExternCallback, C3Int8Get);
    // 加载补丁2
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_MERGE, recordNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    C3Int8T objIn2[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_MERGE, recordNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_DELETE, recordNum * 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅关系
    ret = cancelSubscription(g_stmt, subName01, &userData01, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    C3Int8T objIn3[1] = {{3, 0, 10, 1, 1}};
    C3Int8T objIn4[7] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {1, 0, 5, 1, 1}, {1, 0, 10, 3, 5},
        {1, 0, 1, 8, 9}, {1, 0, 10, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn3, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn4, 7, C3Int8TimeoutGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");

    // 断开订阅连接
    ret = testGmcDisconnect(conn_sn, stmt_sn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除fastpath表
    ret = GmcDropVertexLabel(g_stmt, "out2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 055.%block 1，patch1.d新增输出是外部表和空的输入表，订阅外部表，patch2.d新增规则投影到新增外部表
**************************************************************************** */
TEST_F(dtlpatchenh_002_test, DataLog_098_002_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test031";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);

    // 切换namespace为public


    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "out2");
    readJanssonFile("./schema_file/out2.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[6] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {3, 0, 5, 1, 1}, {1, 0, 10, 3, 5},
        {2, 0, 1, 8, 9}};
    
    GmcConnT *conn_sn = NULL;
    GmcStmtT *stmt_sn = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subExternout2";
    ret = testSubConnect(&conn_sn, &stmt_sn, 1, g_epoll_reg_info, subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData01 = {0};
    userData01.objLen = 6;
    userData01.obj = objPub;

    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    // 创建订阅关系
    ret = createSubscription02(g_stmt, conn_sn, (char *)"schema_file/subinfo03.gmjson", &userData01,
        1000, subName01, snExternCallback, C3Int8Get);
    // 加载补丁2
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_MERGE, recordNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    C3Int8T objIn2[recordNum] = {{3, 0, 5, 1, 1}, {1, 0, 10, 3, 5}, {2, 0, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_MERGE, recordNum, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testWaitSnRecv(userData01.data, GMC_SUB_EVENT_DELETE, recordNum * 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 取消订阅关系
    ret = cancelSubscription(g_stmt, subName01, &userData01, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    C3Int8T objIn3[1] = {{3, 0, 10, 1, 1}};
    C3Int8T objIn4[7] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {1, 0, 2, 3, 5}, {1, 0, 5, 1, 1}, {1, 0, 10, 3, 5},
        {1, 0, 1, 8, 9}, {1, 0, 10, 1, 1}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn3, 1, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "ns2.out1", objIn4, 7, C3Int8TimeoutGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after rollback insert ns2.out1 read complete!!!");

    // 断开订阅连接
    ret = testGmcDisconnect(conn_sn, stmt_sn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 删除fastpath表
    ret = GmcDropVertexLabel(g_stmt, "out2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
