/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 506.0.0 迭代四Datalog热升级增强-编译测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2024.12.23]
*****************************************************************************/
#include "dtlpatchEnh.h"
#include "DatalogRun.h"

using namespace std;

class dtlpatchenh_001_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtlpatchenh_001_test::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so

    AW_CHECK_LOG_BEGIN();
}
void dtlpatchenh_001_test::TearDown()
{
    AW_CHECK_LOG_END();
    system("rm -rf ./datalogFile/planStrFile/*");
}

// %block 1/%block 0 + %redo REDO_OFF和%block 1 统一不支持夸topo join，加-supUpgErr参数，支持夸topo join
// %block 0支持夸topo join
// %block 1，同时修改关联规则报错, 若重叠只有输出表，不报错；%redo REDO_OFF或者%block 0同时修改关联规则不报错

/* ****************************************************************************
 Description  : 001.%block 0，patch.d修改规则，使用原始.d中function，在规则最后进行join，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test001";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.%block 1，patch.d修改规则，使用原始.d中function，在规则最后进行join，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test002";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.%block 1和%redo REDO_OFF，patch.d修改规则，使用原始.d中function，在规则最后进行join，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test003";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.%block 1，patch.d修改规则，使用原始.d中function替换规则中的func，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test004";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: right structure of rule \"r0\" has changed: origin name "
        "\"ns1.func2\" ==> new name \"ns1.func1\" near line 4",
        g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.%block 1，patch.d修改规则，使用原始.d中function，在规则倒数第二个位置进行join，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test005";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: right structure of rule \"r0\" has changed: origin name "
        "\"ns1.func1\" ==> new name \"ns1.func2\" near line 4",
        g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024122314882
/* ****************************************************************************
 Description  : 006.%block 0，patch.d修改规则，删除规则中的最后一个function，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test006";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.%block 1，patch.d修改规则，删除规则中的最后一个function，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test007";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.%block 1和%redo REDO_OFF，patch.d修改规则，删除规则中的最后一个function，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test008";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.%block 1，patch.d修改规则，删除规则中的两个function，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test009";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: only allowed to add one table or function, or remove a func "
        "at the end of rule \"r0\" near line 4.",
        g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.%block 1, patch.d修改规则，删除规则中的最后一个表，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test010";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command, "Error: rule \"r0\" should remove function at the end near line 4.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.%block 1, patch.d修改规则，删除规则中的最后一个表替换成原始.d中的function，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test011";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: right structure of rule \"r0\" has changed: origin name "
        "\"ns1.inp3\" ==> new name \"ns1.func1\" near line 4",
        g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.%block 1, patch.d修改规则，在规则后面join一个原始.d中间表，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test012";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(
        g_command, "Error: rule \"r3\" should add updatable input table or function to join near line 4.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024122321883
/* ****************************************************************************
 Description  : 013.%block 0, patch.d修改规则，在规则后面join一个patch.d新增的中间表，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test013";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(
        g_command, "Error: rule \"r1\" should add updatable input table or function to join near line 7.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.%block 1, patch.d修改规则，在规则后面join一个patch.d新增的中间表，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test014";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(
        g_command, "Error: rule \"r1\" should add updatable input table or function to join near line 7.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.%block 1和%redo REDO_OFF,
patch.d修改规则，在规则后面join一个patch.d新增的中间表，添加-supUpgErr选项，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test015";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(
        g_command, "Error: rule \"r1\" should add updatable input table or function to join near line 8.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.%block
0，patch.d新增类型为普通表的中间表，规则符合约束（输入表为已有表，输出表为已有表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test016";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// DTS2024122420890
// 属于跨topo  进行join，不加-supUpgErr选项，应该编译报错
/* ****************************************************************************
 Description  : 017.%block
1，patch.d新增类型为普通表的中间表，规则符合约束（输入表为已有表，输出表为已有表），不添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test017";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: \"%block 1\" not support topo combine, and rule:\"rN\" involve topo combine ns2.mid1 and ns1.inp1 "
        "are in different topo near line 4.",
        g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.%block
1，patch.d新增类型为普通表的中间表，规则符合约束（输入表为已有表，输出表为已有表），添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test017";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.%block 1和%redo
REDO_OFF，patch.d新增类型为普通表的中间表，规则符合约束（输入表为已有表，输出表为已有表），不添加-supUpgErr选项，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test018";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: table/function:\"ns1.inp1\" and table/function:\"ns2.mid1\" should be in same topo "
        "in rule near line 5.",
        g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.%block 1和%redo
REDO_OFF，patch.d新增类型为普通表的中间表，规则符合约束（输入表为已有表，输出表为已有表），添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test018";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.%block 0和%redo
REDO_OFF，patch.d新增类型为普通表的中间表，规则符合约束（输入表为已有表，输出表为已有表），不添加-supUpgErr选项，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test019";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: table/function:\"ns1.inp1\" and table/function:\"ns2.mid1\" should be in same topo "
        "in rule near line 5.",
        g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.%block 0和%redo
REDO_OFF，patch.d新增类型为普通表的中间表，规则符合约束（输入表为已有表，输出表为已有表），添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test019";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.%block
0，patch.d新增类型为普通表的中间表，规则符合约束（输入表为已有表，输出表为已有表），添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test016";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 024.%block
0，patch.d新增类型为transient(tuple)的中间表，规则符合约束（输入表为已有表，输出表为已有表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test020";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 025.%block
1，patch.d新增类型为transient(tuple)的中间表，规则符合约束（输入表为已有表，输出表为已有表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test021";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 026.%block
0，patch.d新增类型为transient(finish)的中间表，规则符合约束（输入表为已有表，输出表为已有表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test022";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.%block
1，patch.d新增类型为transient(finish)的中间表，规则符合约束（输入表为已有表，输出表为已有表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test023";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 028.%block
0，patch.d新增类型为transient(field)的中间表，规则符合约束（输入表为已有表，输出表为已有表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test024";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 029.%block
1，patch.d新增类型为transient(field)的中间表，规则符合约束（输入表为已有表，输出表为已有表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test025";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 030.%block 0，patch.d新增类型为tbm的输出表，规则符合约束（输入表为已有表，中间表为已有表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test025";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 031.%block
1，patch.d新增类型为tbm的输出表，规则符合约束（输入表为已有表，中间表为已有表）,不添加-supUpgErr选项，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test026";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: \"%block 1\" not support topo combine, and rule:\"rN1\" involve topo combine ns2.out2 "
        "and ns1.mid1 are in different topo near line 5.",
        g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 032.%block
1，patch.d新增类型为tbm的输出表，规则符合约束（输入表为已有表，中间表为已有表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test026";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 033.%block 1和%redo
REDO_OFF，patch.d新增类型为tbm的输出表，规则符合约束（输入表为已有表，中间表为已有表）,不添加-supUpgErr选项，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test027";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: table/function:\"ns1.mid1\" and table/function:\"ns2.out2\" should be in same topo "
        "in rule near line 6.",
        g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 034.%block 1和%redo
REDO_OFF，patch.d新增类型为tbm的输出表，规则符合约束（输入表为已有表，中间表为已有表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test027";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 035.%block 0和%redo
REDO_OFF，patch.d新增类型为tbm的输出表，规则符合约束（输入表为已有表，中间表为已有表）,不添加-supUpgErr选项，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test028";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: table/function:\"ns1.mid1\" and table/function:\"ns2.out2\" should be in same topo "
        "in rule near line 6.",
        g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 036.%block 0和%redo
REDO_OFF，patch.d新增类型为tbm的输出表，规则符合约束（输入表为已有表，中间表为已有表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test028";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 037.%block
0，patch.d新增类型为notify的输出表，规则符合约束（输入表为已有表，中间表为已有表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test029";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 038.%block
1，patch.d新增类型为notify的输出表，规则符合约束（输入表为已有表，中间表为已有表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test030";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 039.%block
0，patch.d新增类型为外部表的输出表，规则符合约束（输入表为已有表，中间表为已有表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test031";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 040.%block
1，patch.d新增类型为外部表的输出表，规则符合约束（输入表为已有表，中间表为已有表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test032";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 041.%block
0，patch.d新增类型为可更新的输出表，规则符合约束（输入表为已有表，中间表为已有表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test033";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 042.%block
1，patch.d新增类型为可更新的输出表，规则符合约束（输入表为已有表，中间表为已有表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test034";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 043.%block
1，patch.d新增类型为普通表的中间表，规则符合约束（输入表为已有表，输出表为新增表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test035";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 044.%block 1和%redo
REDO_OFF，patch.d新增类型为transient(tuple)的中间表，规则符合约束（输入表为已有表，输出表为新增表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test036";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 045.%block
1，patch.d新增类型为transient(tuple)的中间表，规则符合约束（输入表为已有表，输出表为新增表）,不添加-supUpgErr选项，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test037";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: \"%block 1\" not support topo combine, and rule:\"rN\" involve topo combine ns2.mid1 and ns1.inp1 "
        "are in different topo near line 4.",
        g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 046.%block
0，patch.d新增类型为transient(finish)的中间表，规则符合约束（输入表为已有表，输出表为新增表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test038";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 047.%block
1，patch.d新增类型为transient(field)的中间表，规则符合约束（输入表为已有表，输出表为新增表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_047)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test039";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 048.%block 1和%redo
REDO_OFF，patch.d新增类型为transient(tuple)的中间表，规则符合约束（输入表为新增表，输出表为新增表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_048)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test040";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 049.%block
0，patch.d新增类型为transient(finish)的中间表，规则符合约束（输入表为新增表，输出表为新增表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_049)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test041";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 050.%block 0，patch.d新增类型为tbm的输出表，规则符合约束（输入表为已有表，中间表为新增表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_050)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test042";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 051.%block
1，patch.d新增类型为tbm的输出表，规则符合约束（输入表为新增表，中间表为新增表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_051)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test043";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 052.%block
0，patch.d新增类型为notify的输出表，规则符合约束（输入表为已有表，中间表为新增表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test044";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 053.%block
1，patch.d新增类型为notify的输出表，规则符合约束（输入表为新增表，中间表为新增表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_053)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test045";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 054.%block
0，patch.d新增类型为外部表的输出表，规则符合约束（输入表为已有表，中间表为新增表），预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_054)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test046";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 055.%block
1，patch.d新增类型为外部表的输出表，规则符合约束（输入表为新增表，中间表为新增表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_055)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test047";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 056.%block
1，patch.d新增类型为可更新的输出表，规则符合约束（输入表为新增表，中间表为新增表）,添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_056)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test048";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 057.原始.d含namespace ns1和ns2同时含agg函数，patch.d为block
0，覆盖agg函数(many_to_one,many_to_many比较函数)，
 修改原始.d中agg函数左表条件，含%read_write,含新增precedence；含新增输入表、中间表、输出表（tbm，notify，外部表），各种字段类型，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_057)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test049";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 058.原始.d含namespace ns1和ns2同时含agg函数，patch.d为block
1，覆盖agg函数(many_to_one,many_to_many比较函数)，
 修改原始.d中agg函数左表条件，含%read_write,含新增precedence；含新增输入表、中间表、输出表（tbm，notify，外部表），各种字段类型，添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_058)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test050";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 059.原始.d含namespace ns1和ns2同时含agg函数，patch.d为block 1和%redo
REDO_OFF，覆盖agg函数(many_to_one,many_to_many比较函数)，
 修改原始.d中agg函数左表条件，含%read_write,含新增precedence；含新增输入表、中间表、输出表（tbm，notify，外部表），各种字段类型，添加-supUpgErr选项，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_059)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test051";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 060.%block 0，输出表定义有问题，新增tbm表（不含init和unit函数），原始.d无tbm表，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_060)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test052";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command, "Error: init and uninit functions must be defined for TBM table", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 061.%block 0，输出表定义有问题，新增tbm表（含init和unit函数），原始.d无tbm表，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_061)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test053";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(g_command,
        "Error: unsupported upgrade for function \"init\" , patch op type is \"create\", near line 14.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 062.%block 0,patch.d含新增agg函数，agg函数含access_current和access_delta、access_kv，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_062)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test054";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(
        g_command, "Error: create agg function \"ns2.agg2\" with access is not allowed near line 20.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 063.%block 1,patch.d含新增agg函数，agg函数含access_current和access_delta、access_kv，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_063)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test055";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(
        g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(
        g_command, "Error: create agg function \"ns2.agg2\" with access is not allowed near line 20.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 064.%block 0，patch.d新增规则，使用原始.d中的agg，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_064)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test056";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 065.%block 1，patch.d新增规则，使用原始.d中的agg，添加-supUpgErr选项，预期成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_065)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test057";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s -supUpgErr", g_toolPath, ruleFile,
        patchFile, patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 066.%block 0，patch.d修改规则，删除规则中的最后一个function，使得原始.d中的function未使用，预期编译失败
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_066)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test058";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file. Exit with code %d.", GMERR_SEMANTIC_ERROR);
    // 预期编译失败
    ret = executeCommand(
        g_command, "Error: function \"ns1.func2\" is defined but not used in the rules near line 25.", g_errorMsg);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 067.%block
0，patch.d修改规则，删除规则中的最后一个function，新增规则将删除的function用起来，预期编译成功
**************************************************************************** */
TEST_F(dtlpatchenh_001_test, DataLog_098_001_067)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char outputFilePath[FILE_PATH] = "./datalogFile/compiler";
    char soName[FILE_PATH] = "test059";
    char inputFile[FILE_PATH] = "./datalogFile/compiler";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    // 预期编译成功
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
