/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 506.0.0 迭代四Datalog热升级增强-可靠性测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2025.01.06]
*****************************************************************************/
#include "dtlpatchEnh.h"
#include "DatalogRun.h"

using namespace std;

class dtlpatchenh_004_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtlpatchenh_004_test::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void dtlpatchenh_004_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

class dtlpatchenh_004_test2 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        // 先停服务，避免受到其它用例影响
        system("sh ${TEST_HOME}/tools/stop.sh -f");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void dtlpatchenh_004_test2::SetUp()
{
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void dtlpatchenh_004_test2::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
}
/* ****************************************************************************
 Description  : 001.%block 0并允许并发，补丁含新增中间表，新增规则输出表和输入表属于原始.d,加载降级so的同时，
 并发对新增规则输入表写数据（拿锁大于事务锁时间），预期dml拿不到锁
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 屏蔽错误码
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test001";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改配置项
    // enableDatalogDmlWhenUpgrading配置项设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    int recordNum = 12;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "inp1 insert data complete!!!");

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    sleep(1);
    int threadNum = 1;
    pthread_t thr_arr[threadNum];
    // 并发执行dml操作，预期拿不到锁
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable1, NULL);
    pthread_join(thr_arr[0], NULL);

    // 等待重做完成
#ifdef RUN_INDEPENDENT
    sleep(35);
#else
    sleep(6);
#endif

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "inp1", "12");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.%block 0并允许并发，补丁含新增中间表，新增规则输出表和输入表属于原始.d,加载降级so的同时，
 并发对新增规则输入表写数据（拿锁小于事务锁时间），预期dml拿到锁
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test001";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改配置项
    // enableDatalogDmlWhenUpgrading配置项设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    int recordNum = 2;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 insert data complete!!!");

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    // sleep5.5s
    usleep(1000 * 5500);
    int threadNum = 1;
    pthread_t thr_arr[threadNum];
    // 并发执行dml操作，预期拿到锁
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable2, NULL);
    pthread_join(thr_arr[0], NULL);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "inp1", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.%block 0不并允许并发，fetchsize设置为最大，补丁含新增中间表，
 新增规则输出表和输入表属于原始.d,加载降级so的同时，并发对新增规则输入表写数据（拿锁小于5*一级hung时间），预期dml拿到锁
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 屏蔽错误码
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test001";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    int recordNum = 6;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 insert data complete!!!");

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    // sleep3.5s
    usleep(1000 * 3500);
    int threadNum = 1;
    pthread_t thr_arr[threadNum];
    // 并发执行dml操作，预期拿到锁
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable2, NULL);
    pthread_join(thr_arr[0], NULL);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "inp1", "7");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.%block
1并允许并发，fetchsize设置为最大，补丁含新增中间表，新增规则输出表和输入表属于原始.d,加载降级so的同时，
 并发对新增规则输入表写数据（拿锁小于5*一级hung时间），预期dml拿到锁
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 屏蔽错误码
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test002";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改配置项
    // enableDatalogDmlWhenUpgrading配置项设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    int recordNum = 6;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 insert data complete!!!");

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    // sleep3.5s
    usleep(1000 * 3500);
    int threadNum = 1;
    pthread_t thr_arr[threadNum];
    // 并发执行dml操作，预期拿到锁
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable2, NULL);
    pthread_join(thr_arr[0], NULL);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "inp1", "7");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.%block 1并允许并发，fetchsize设置为最大，补丁含新增中间表，新增规则输出表和输入表属于原始.d,
 加载降级so的同时，并发卸载so（拿锁大于一级hung时间），预期卸载so失败
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 屏蔽错误码
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test002";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改配置项
    // enableDatalogDmlWhenUpgrading配置项设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 insert data complete!!!");

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    sleep(1);
    int threadNum = 1;
    pthread_t thr_arr[threadNum];
    // 并发卸载so，预期拿不到data service锁
    pthread_create(&thr_arr[0], NULL, ThreadUnLoadSo, (void *)soName);
    pthread_join(thr_arr[0], NULL);

    sleep(15);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "inp1", "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.%block 0并允许并发，补丁含新增输出表、中间表，新增规则输入表属于原始.d,加载降级so的同时，
 并发对新增规则输入表写数据（拿锁大于事务锁时间），预期dml拿不到锁
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 屏蔽错误码
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test003";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改配置项
    // enableDatalogDmlWhenUpgrading配置项设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    int recordNum = 12;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "inp1 insert data complete!!!");

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    sleep(1);
    int threadNum = 1;
    pthread_t thr_arr[threadNum];
    // 并发执行dml操作，预期拿不到锁
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable1, NULL);
    pthread_join(thr_arr[0], NULL);

    // 等待重做完成
#ifdef RUN_INDEPENDENT
    sleep(35);
#else
    sleep(6);
#endif

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "inp1", "12");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.%block 0并允许并发，补丁含新增输出表、中间表，新增规则输入表属于原始.d,加载降级so的同时，
 并发对新增规则输入表写数据（拿锁小于事务锁时间），预期dml拿到锁
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test003";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改配置项
    // enableDatalogDmlWhenUpgrading配置项设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    int recordNum = 2;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 insert data complete!!!");

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    // sleep5.5s
    usleep(1000 * 5500);
    int threadNum = 1;
    pthread_t thr_arr[threadNum];
    // 并发执行dml操作，预期拿到锁
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable2, NULL);
    pthread_join(thr_arr[0], NULL);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "inp1", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.%block 0并允许并发，补丁含新增输出表，新增规则输入表属于原始.d,加载降级so的同时，
 并发对新增规则输入表写数据（拿锁大于事务锁时间），预期dml拿不到锁
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 屏蔽错误码
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test004";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改配置项
    // enableDatalogDmlWhenUpgrading配置项设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    int recordNum = 12;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "inp1 insert data complete!!!");

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    sleep(1);
    int threadNum = 1;
    pthread_t thr_arr[threadNum];
    // 并发执行dml操作，预期拿不到锁
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable1, NULL);
    pthread_join(thr_arr[0], NULL);

    // 等待重做完成
#ifdef RUN_INDEPENDENT
    sleep(35);
#else
    sleep(6);
#endif

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "inp1", "12");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.%block 0并允许并发补丁含新增输出表，新增规则输入表属于原始.d,加载降级so的同时，
 并发对新增规则输入表写数据（拿锁小于事务锁时间），预期dml拿到锁
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test004";
    int ret = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改配置项
    // enableDatalogDmlWhenUpgrading配置项设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "BLOCK_MODE: 0", "REDO_OFF_CONFIG: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    // 插入数据
    int recordNum = 2;
    C3Int8T objIn1[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].dtlReservedCount = 1;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 insert data complete!!!");

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");

    // sleep5.5s
    usleep(1000 * 5500);
    int threadNum = 1;
    pthread_t thr_arr[threadNum];
    // 并发执行dml操作，预期拿到锁
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable2, NULL);
    pthread_join(thr_arr[0], NULL);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command, "inp1", "3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count out1");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.%block
0，patch.d，覆盖namespace、precedence、修改规则、新增规则、新增输入表、中间表、输出表、agg、function，
 新增和修改规则含原始.d中的表和新增的表,预期数据符合预期，循环加载升降级so1000次，无内存泄漏
**************************************************************************** */
TEST_F(dtlpatchenh_004_test2, DataLog_098_004_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test025";
    int ret = 0, cnt = 0, cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    int tableShareMem01 = 0;
    int tableShareMem02 = 0;
    int udfDynMem01 = 0;
    int udfDynMem02 = 0;
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 1000;
#endif

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public

    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/extern.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C4Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2, 1}, {1, 0, 1, 1, 3, 1}, {1, 0, 2, 3, 5, 2}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn2, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)ThreadScanPatchView((void *)soName);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);
    // 查热补丁视图，查询视图会导致内存增加，gmsysview查视图要先建视图表
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > /dev/null 2>&1", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = TestGetCTXVal(&tableShareMem01, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXVal(&udfDynMem01, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %d", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %d", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    pthread_t thr_arr[1];
    // 循环加载升降级so，1000次，无内存泄漏
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
    }
    sleep(2);
    ret = TestGetCTXVal(&tableShareMem02, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXVal(&udfDynMem02, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after Upgrade And Rollback");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %d", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %d", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);

#ifdef RUN_INDEPENDENT
    EXPECT_GE(tableShareMem01, tableShareMem02);
    EXPECT_GE(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
#endif

    // 删fastpath表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.%block
1，patch.d，覆盖namespace、precedence、修改规则、新增规则、新增输入表、中间表、输出表、agg、function，
 新增和修改规则含原始.d中的表和新增的表，添加-supUpgErr选项,预期数据符合预期，循环加载升降级so1000次，无内存泄漏
**************************************************************************** */
TEST_F(dtlpatchenh_004_test2, DataLog_098_004_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test026";
    int ret = 0, cnt = 0, cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    int tableShareMem01 = 0;
    int tableShareMem02 = 0;
    int udfDynMem01 = 0;
    int udfDynMem02 = 0;
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 1000;
#endif

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public

    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/extern.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C4Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2, 1}, {1, 0, 1, 1, 3, 1}, {1, 0, 2, 3, 5, 2}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn2, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)ThreadScanPatchView((void *)soName);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);
    // 查热补丁视图，查询视图会导致内存增加，gmsysview查视图要先建视图表
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > /dev/null 2>&1", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = TestGetCTXVal(&tableShareMem01, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXVal(&udfDynMem01, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %d", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %d", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    pthread_t thr_arr[1];
    // 循环加载升降级so，1000次，无内存泄漏
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
    }
    sleep(2);
    ret = TestGetCTXVal(&tableShareMem02, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXVal(&udfDynMem02, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after Upgrade And Rollback");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %d", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %d", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);

#ifdef RUN_INDEPENDENT
    EXPECT_GE(tableShareMem01, tableShareMem02);
    EXPECT_GE(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
#endif

    // 删fastpath表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.%block
1和%redo_REDO_OFF，patch.d，覆盖namespace、precedence、修改规则、新增规则、新增输入表、中间表、
 输出表、agg、function，新增和修改规则含原始.d中的表和新增的表，添加-supUpgErr选项,预期数据符合预期，循环加载升降级so1000次，无内存泄漏
**************************************************************************** */
TEST_F(dtlpatchenh_004_test2, DataLog_098_004_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test027";
    int ret = 0, cnt = 0, cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 1000;
#endif

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public

    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/extern.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 1, 3}, {2, 0, 2, 3, 5}};
    C4Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2, 1}, {1, 0, 1, 1, 3, 1}, {1, 0, 2, 3, 5, 2}};

    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn2, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)ThreadScanPatchView((void *)soName);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);
    // 查热补丁视图，查询视图会导致内存增加，gmsysview查视图要先建视图表
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > /dev/null 2>&1", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    pthread_t thr_arr[1];
    // 循环加载升降级so，1000次，无内存泄漏
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
    }
    sleep(2);
    ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after Upgrade And Rollback");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);

#ifdef RUN_INDEPENDENT
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
#endif

    // 删fastpath表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.%block
0，patch.d，覆盖namespace、precedence、修改规则、新增规则、新增输入表、中间表、输出表、agg、function，
 新增和修改规则含原始.d中的表和新增的表,构造大数据量，fetchsize设置为1000，触发升级重做，预期服务正常
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test025";
    int ret = 0, cnt = 0, recordNum = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    // 插入数据
#ifdef ENV_RTOSV2X
    recordNum = 3000;
#else
    recordNum = 300000;
#endif
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改配置项
    // enableDatalogDmlWhenUpgrading配置项设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1000);
    system(g_command);

    // 切换namespace为public

    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/extern.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 构造输入表的数据
    C3Int8T *objIn1 = (C3Int8T *)malloc(sizeof(C3Int8T) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C3Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 100;
    }
    C4Int8T *objIn2 = (C4Int8T *)malloc(sizeof(C4Int8T) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C4Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i;
        objIn2[i].b = i;
        objIn2[i].c = i;
        objIn2[i].d = i;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 100;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn2, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count ns1.inp1");
    system("gmsysview count ns1.inp2");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)ThreadScanPatchView((void *)soName);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    // 删fastpath表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    free(objIn1);
    free(objIn2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.%block
1，patch.d，覆盖namespace、precedence、修改规则、新增规则、新增输入表、中间表、输出表、agg、function，
 新增和修改规则含原始.d中的表和新增的表，添加-supUpgErr选项,预期数据符合预期，构造大数据量，fetchsize设置为1000，触发升级重做，预期服务正常
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    char inputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char outputFilePath[FILE_PATH] = "./datalogFile/outputtable";
    char soName[FILE_PATH] = "test026";
    int ret = 0, cnt = 0, recordNum = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    // 插入数据
#ifdef ENV_RTOSV2X
    recordNum = 3000;
#else
    recordNum = 300000;
#endif
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 修改配置项
    // enableDatalogDmlWhenUpgrading配置项设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1000);
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath,
        "upgradeMemActualAndEstimatedPercentage", 0);
    system(g_command);

    // 切换namespace为public

    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/extern.gmjson", &schema);
    AW_MACRO_EXPECT_NOTNULL(schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 构造输入表的数据
    C3Int8T *objIn1 = (C3Int8T *)malloc(sizeof(C3Int8T) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C3Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 100;
    }
    C4Int8T *objIn2 = (C4Int8T *)malloc(sizeof(C4Int8T) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn2 malloc failed !!!");
    }
    memset(objIn2, 0, sizeof(C4Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i;
        objIn2[i].b = i;
        objIn2[i].c = i;
        objIn2[i].d = i;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 100;
    }
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn2, recordNum, C4Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count ns1.inp1");
    system("gmsysview count ns1.inp2");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)ThreadScanPatchView((void *)soName);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    // 校验热补丁视图重做状态
    (void)ThreadScanPatchView((void *)soName);

    // 删fastpath表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath,
        "upgradeMemActualAndEstimatedPercentage", 300);
    system(g_command);

    free(objIn1);
    free(objIn2);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.%block 0 , 补丁新增输出表，规则到输出表max_size超限，测试回滚
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 屏蔽错误码
    AddWhiteList(GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    system("gmsysview count out1");

    // 对输入表写入数据
    C3Int8T objIn3[recordNum] = {{3, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {2, 0, 1, 8, 9}};
    C3Int8T objIn4[recordNum * 2] = {
        {1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}, {1, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {1, 0, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 2 * recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    system("gmsysview count out1");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.%block 1 ,补丁新增输出表，规则到输出表max_size超限，测试回滚
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 屏蔽错误码
    AddWhiteList(GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test006";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }
    system("gmsysview count out1");

    // 对输入表写入数据
    C3Int8T objIn3[recordNum] = {{3, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {2, 0, 1, 8, 9}};
    C3Int8T objIn4[recordNum * 2] = {
        {1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}, {1, 0, 4, 5, 6}, {1, 0, 5, 6, 7}, {1, 0, 1, 8, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 读输出表数据
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 2 * recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "after upgrade out1 read complete!!!");

    system("gmsysview count out1");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.%block 0 , 补丁新增输出表和中间表，加载降级so触发回滚，测试回滚
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 屏蔽错误码
    AddWhiteList(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test007";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.%block 1 , 补丁新增输出表和中间表，加载降级so触发回滚，测试回滚
**************************************************************************** */
TEST_F(dtlpatchenh_004_test, DataLog_098_004_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    // 屏蔽错误码
    AddWhiteList(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "test008";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 3;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 3, 5}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 3, 5}};

    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 3, 5}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        system(g_command);
    }

    ret = readRecord(g_conn, g_stmt, "out1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out2", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}
