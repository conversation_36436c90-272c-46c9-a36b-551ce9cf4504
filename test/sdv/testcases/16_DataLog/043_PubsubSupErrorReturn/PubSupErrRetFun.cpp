/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: PubSupErrRetFun.cpp
 * Description: 功能测试
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2023-07-07
 */
#include "PubSupErrRet.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

using namespace std;

class PubSupErrRetFun : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void PubSupErrRetFun::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    g_conn = NULL;
    g_stmt = NULL;
    g_subConn = NULL;
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubConnect(&g_subConn, NULL, 1, g_epoll_reg_info, g_subConnName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}
void PubSupErrRetFun::TearDown()
{
    int ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(g_subConn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

// 007.普通pubsub创建订阅关系，回调函数未调接口填充错误码，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_007)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    char g_errorCode2[1024] = {0};
    (void)snprintf(g_errorCode2, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode1, g_errorCode2);

    char libName[] = "notify";
    char tableInput[] = "input1";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "";
    AW_FUN_Log(LOG_STEP, "加载Datalog");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    AW_FUN_Log(LOG_STEP, "普通pubsub创建新订阅，报错");
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1NewSub.gmjson", &userData1, 200, g_subName,
        snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData1.data);

    AW_FUN_Log(LOG_STEP, "创建订阅关系");
    ret = createSubscription(
        g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 008.普通pubsub创建订阅关系，回调函数先填充错误码为1000，再填充为0，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_008)
{
    char libName[] = "notify";
    char tableInput[] = "input1";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetTwice, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 009.普通pubsub创建订阅关系，回调函数填充错误码为1000，非结构化单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_009)
{
    AW_CHECK_LOG_END();
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 1");
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify";
    char tableInput[] = "input1";
    int expectErrorCode = 1000;
    char expectErrorLabelName[] = "out1";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCode, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "校验日志");
#if defined(ENV_EULER)
#else
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep 1000 %s", g_serverlogPath);
    ret = executeCommand(g_command, "pubsub callback set blunder code:1000");
    if (ret != GMERR_OK) {
        sleep(1);
        ret = executeCommand(g_command, "pubsub callback set blunder code:1000");
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 0");
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
}

// 010.普通pubsub（表名为512长度）创建订阅关系，回调函数填充错误码为1000，单写数据，批量结构化写数据，申请10B内存接收labelName
TEST_F(PubSupErrRetFun, DataLog_043_010)
{
    AW_ADD_TRUNCATION_WHITE_LIST(2, "Internal field overflow", "Data exception occurs");

    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notifyMaxLabelName";
    char tableInput[] = "input1";
    int expectErrorCode = 1000;
    char expectErrorLabelName[] = "aaaaaaaaa";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notifyMaxLabelName.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/MaxLabelName.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCode, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[10] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 011.普通pubsub（表名为512长度）创建订阅关系，回调函数填充错误码为1000，单写数据，批量结构化写数据，申请520B内存接收labelName
TEST_F(PubSupErrRetFun, DataLog_043_011)
{
    AW_ADD_TRUNCATION_WHITE_LIST(2, "Internal field overflow", "Data exception occurs");

    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notifyMaxLabelName";
    char tableInput[] = "input1";
    int expectErrorCode = 1000;
    char expectErrorLabelName[520] = {0};
    memset(expectErrorLabelName, 'a', sizeof(expectErrorLabelName));
    expectErrorLabelName[512 - 1] = '\0';
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notifyMaxLabelName.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/MaxLabelName.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCode, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[520] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 520);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 520);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 012.普通pubsub创建订阅关系，回调函数在发送respnse后填充错误码为1000，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_012)
{
    char libName[] = "notify";
    char tableInput[] = "input1";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCodeAfterSend, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 013.普通pubsub创建订阅关系，回调内不进行应答，未填充错误码，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_013)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    char libName[] = "notify";
    char tableInput[] = "input1";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "out1";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackWithoutResponse, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 014.普通pubsub创建订阅关系，回调填充错误码为-10000，订阅回调内应答全失败，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_014)
{
    AW_CHECK_LOG_END();
    system("gmadmin -cfgName enableLogFold -cfgVal 0");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 1");
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify";
    char tableInput[] = "input1";
    int expectErrorCode = -10000;
    char expectErrorLabelName[] = "out1";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCodeAndFailedDataNum, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "校验日志");
#if defined(ENV_EULER)
#else
    system("logctrl --cachefilestore=diaglog");
#endif
    (void)snprintf(g_command, MAX_CMD_SIZE, "grep 10000 %s", g_serverlogPath);
    ret = executeCommand(g_command, "pubsub callback set blunder code:-10000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 0");
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
}

// 015.可更新表pubsub创建订阅关系，回调函数未调接口用填充错误码，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_015)
{
    char libName[] = "updateNotify";
    char tableInput[] = "input2";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "";
    AW_FUN_Log(LOG_STEP, "加载Datalog，创建订阅关系");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/updateNotify.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(
        g_stmt, g_subConn, (char *)"schemaFile/out2Sub.gmjson", &userData1, 200, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 016.可更新表pubsub创建订阅关系，回调函数先填充错误码为1000，再填充为0，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_016)
{
    char libName[] = "updateNotify";
    char tableInput[] = "input2";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/updateNotify.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out2Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetTwice, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    unsigned int totalNum = 0;
    unsigned int successNum = 0;
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 017.可更新表pubsub创建订阅关系，回调函数填充错误码为1000，非结构化单化写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_017)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "updateNotify";
    char tableInput[] = "input2";
    int expectErrorCode = 1000;
    char expectErrorLabelName[] = "out2";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/updateNotify.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out2Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCode, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 018.可更新表pubsub创建订阅关系，回调内不进行应答，未填充错误码，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_018)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    char libName[] = "updateNotify";
    char tableInput[] = "input2";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "out2";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/updateNotify.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out2Sub.gmjson", &userData1, 200, g_subName,
        snCallbackWithoutResponse, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 019.可更新表pubsub创建订阅关系，回调填充错误码为-10000，订阅回调内应答全失败，单写数据，批写数据，
// GmcBatchDeparseRetDtlError获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_019)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "updateNotify";
    char tableInput[] = "input2";
    int expectErrorCode = -10000;
    char expectErrorLabelName[] = "out2";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/updateNotify.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out2Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCodeAndFailedDataNum, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 020.资源型pubsub创建订阅关系，回调函数未调接口用填充错误码，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_020)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATATYPE_MISMATCH);
    char g_errorCode2[1024] = {0};
    (void)snprintf(g_errorCode2, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode1, g_errorCode2);

    char libName[] = "rsc";
    char tableInput[] = "input3";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "";
    AW_FUN_Log(LOG_STEP, "加载Datalog");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/rsc.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    userData1.isPubsubRsc = true;
    const char *g_subName = "subVertexLabel";
    AW_FUN_Log(LOG_STEP, "资源型pubsub创建新订阅，报错");
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/rsc3NewSub.gmjson", &userData1, 200, g_subName,
        snCallbackRscPubSub, rsc3Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testGmcGetLastError(NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userData1.data);

    AW_FUN_Log(LOG_STEP, "创建订阅关系");
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/rsc3Sub.gmjson", &userData1, 200, g_subName,
        snCallbackRscPubSub, rsc3Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 021.资源型pubsub创建订阅关系，回调函数先填充错误码为1000，再填充为0，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_021)
{
    char libName[] = "rsc";
    char tableInput[] = "input3";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "";
    AW_FUN_Log(LOG_STEP, "加载Datalog，创建订阅关系");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/rsc.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    userData1.isPubsubRsc = true;
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/rsc3Sub.gmjson", &userData1, 200, g_subName,
        snCallbackRscPubSubSetTwice, rsc3Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 022.资源型pubsub创建订阅关系，回调函数填充错误码为1000，非结构化单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_022)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "rsc";
    char tableInput[] = "input3";
    int expectErrorCode = 1000;
    char expectErrorLabelName[] = "rsc3";
    AW_FUN_Log(LOG_STEP, "加载Datalog，创建订阅关系");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/rsc.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    userData1.isPubsubRsc = true;
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/rsc3Sub.gmjson", &userData1, 200, g_subName,
        snCallbackRscPubSubSetErrorCode, rsc3Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 023.资源型pubsub创建订阅关系，回调内不进行应答，未填充错误码，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_023)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);

    char libName[] = "rsc";
    char tableInput[] = "input3";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "rsc3";
    AW_FUN_Log(LOG_STEP, "加载Datalog，创建订阅关系");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/rsc.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    userData1.isPubsubRsc = true;
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/rsc3Sub.gmjson", &userData1, 200, g_subName,
        snCallbackRscPubSubWithoutResponse, rsc3Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_REQUEST_TIME_OUT, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 024.资源型pubsub创建订阅关系，回调填充错误码为-10000，订阅回调内未回填字段，单写数据，批写数据，
// GmcBatchDeparseRetDtlError获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_024)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "rsc";
    char tableInput[] = "input3";
    int expectErrorCode = -10000;
    char expectErrorLabelName[] = "rsc3";
    AW_FUN_Log(LOG_STEP, "加载Datalog，创建订阅关系");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/rsc.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    userData1.isPubsubRsc = true;
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/rsc3Sub.gmjson", &userData1, 200, g_subName,
        snCallbackRscPubSubSetErrorCodeAndNoSetRscField, rsc3NoSetRscField);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 025.tbm表单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_025)
{
    char libName[] = "tbm1";
    char tableInput[] = "input4";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "";
    AW_FUN_Log(LOG_STEP, "加载Datalog");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/tbm1.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    system("rm -rf /root/TbmRunLog.txt");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/TbmRunLog.txt");
    ret = executeCommand(g_command, "a = 10", "b = 10", "c = 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "a = 1", "a = 2", "a = 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "卸载Datalog");
    TestUninstallDatalog(libName);
}

// 026.tbm表udf返回1000，非结构化单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_026)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "tbm2";
    char tableInput[] = "input4";
    int expectErrorCode = 2000;
    char expectErrorLabelName[] = "tbm2";
    AW_FUN_Log(LOG_STEP, "加载Datalog");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/tbm2.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "卸载Datalog");
    TestUninstallDatalog(libName);
}

// 027.precedence先处理tmb后处理pubsub，tbm_udf执行成功，pubsub填充了错误为1000，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_027)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify_tbm1";
    char tableInput[] = "input1";
    int expectErrorCode = 1000;
    char expectErrorLabelName[] = "out1";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify_tbm1.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCode, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    system("rm -rf /root/TbmRunLog.txt");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/TbmRunLog.txt");
    ret = executeCommand(g_command, "dtlReservedCount = -1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 028.precedence先处理tmb后处理pubsub，tbm_udf失败返回错误码2000，pubsub回调成功未填错误码，单写数据，批量结构化写数据，
// 获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_028)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify_tbm2";
    char tableInput[] = "input1";
    int expectErrorCode = 2000;
    char expectErrorLabelName[] = "tbm1";
    AW_FUN_Log(LOG_STEP, "加载Datalog，创建订阅关系");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify_tbm2.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(
        g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 029.precedence先处理pubsub后处理tmb，tbm_udf执行成功，pubsub填充了错误1000，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_029)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify_tbm3";
    char tableInput[] = "input1";
    int expectErrorCode = 1000;
    char expectErrorLabelName[] = "out1";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify_tbm3.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCode, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 030.precedence先处理pubsub后处理tmb，tbm_udf失败返回错误码2000，pubsub回调成功未填错误码，单写数据，批量结构化写数据，
// 获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_030)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify_tbm4";
    char tableInput[] = "input1";
    int expectErrorCode = 2000;
    char expectErrorLabelName[] = "tbm1";
    AW_FUN_Log(LOG_STEP, "加载Datalog，创建订阅关系");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify_tbm4.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(
        g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 031.precedence先处理tbm后处理pubsub，普通pubsub创建订阅关系，回调函数填充错误码为1000，tmb_udf返回2000，单写数据，
// 获取错误码及表名，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_031)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify_tbm2";
    char tableInput[] = "input1";
    int expectErrorCode = 2000;
    char expectErrorLabelName[] = "tbm1";
    AW_FUN_Log(LOG_STEP, "加载Datalog，创建订阅关系");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify_tbm2.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCode, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 032.precedence先处理pubsub后处理tmb，普通pubsub创建订阅关系，回调函数填充错误码为1000，tmb_udf返回2000，单写数据，
// 获取错误码及表名，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_032)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify_tbm4";
    char tableInput[] = "input1";
    int expectErrorCode = 1000;
    char expectErrorLabelName[] = "out1";
    AW_FUN_Log(LOG_STEP, "加载Datalog，创建订阅关系");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify_tbm4.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCode, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 033.tbm_udf，pubsub正向流程成功（未填错误码），写外部表写超出maxsize导致回滚，回滚流程中tbm_udf，pubsub填充错误码，
// 获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_033)
{
    char g_errorCode1[1024] = {0};
    char g_errorCode2[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_RECORD_COUNT_LIMIT_EXCEEDED);
    (void)snprintf(g_errorCode2, 1024, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode1, g_errorCode2);

    char *schemaFile = NULL;
    char const *labelName = "external";
    char const *labelConfig = "{\"max_record_count\":1, \"isFastReadUncommitted\":0}";
    readJanssonFile("schemaFile/external.gmjson", &schemaFile);
    ASSERT_NE((void *)NULL, schemaFile);
    int ret = 0;    ret = GmcCreateVertexLabel(g_stmt, schemaFile, labelConfig);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schemaFile);

    char libName[] = "notify_tbm_external";
    char tableInput[] = "input1";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "";
    AW_FUN_Log(LOG_STEP, "加载Datalog，创建订阅关系");
    ret = TestLoadDatalog("./datalogFile/notify_tbm_external.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    userData1.isPubsubRsc = false;
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackRollBack, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    insertValue = 11;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_RECORD_COUNT_LIMIT_EXCEEDED, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
    ret = GmcDropVertexLabel(g_stmt, labelName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 034.msgnotify表单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_034)
{
    char libName[] = "msgNotify1";
    char tableInput[] = "input1";
    int expectErrorCode = 0;
    char expectErrorLabelName[] = "";
    AW_FUN_Log(LOG_STEP, "加载Datalog");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/msgNotify1.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    system("rm -rf /root/msgNotifyRunLog.txt");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/msgNotifyRunLog.txt");
    ret = executeCommand(g_command, "a: 10", "b: 10", "c: 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 1;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "a: 1", "a: 2", "a: 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 1;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "卸载Datalog");
    TestUninstallDatalog(libName);
}

// 035.msgnotify表udf返回2000，非结构化单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_035)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "msgNotify2";
    char tableInput[] = "input1";
    int expectErrorCode = 2000;
    char expectErrorLabelName[] = "msgNotify2";
    AW_FUN_Log(LOG_STEP, "加载Datalog");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/msgNotify2.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    system("rm -rf /root/msgNotifyRunLog.txt");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "卸载Datalog");
    TestUninstallDatalog(libName);
}

// 036.普通pubsub创建订阅关系，msgnotify执行成功，回调函数填充错误码为1000，单写数据，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_036)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify_msgNotify1";
    char tableInput[] = "input1";
    int expectErrorCode = 1000;
    char expectErrorLabelName[] = "out1";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify_msgNotify1.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCode, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    system("rm -rf /root/msgNotifyRunLog.txt");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat /root/msgNotifyRunLog.txt");
    ret = executeCommand(g_command, "a: 10", "b: 10", "c: 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 037.普通pubsub创建订阅关系，回调函数不填充错误码，msgnotify_udf返回2000，单写数据，获取错误码及表名，批量结构化写数据，
// 获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_037)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify_msgNotify2";
    char tableInput[] = "input1";
    int expectErrorCode = 2000;
    char expectErrorLabelName[] = "msgNotify2";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify_msgNotify2.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(
        g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName, snCallback, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 038.precedence先处理msgnotify后处理pubsub，，普通pubsub创建订阅关系，回调函数填充错误码为2000，msgnotify_udf返回1000，
// 单写数据，获取错误码及表名，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_038)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify_msgNotify2";
    char tableInput[] = "input1";
    int expectErrorCode = 2000;
    char expectErrorLabelName[] = "msgNotify2";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify_msgNotify2.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCode, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// 039.precedence先处理pubsub后处理msgnotify，普通pubsub创建订阅关系，回调函数填充错误码为2000，msgnotify_udf返回1000，
// 单写数据，获取错误码及表名，批量结构化写数据，获取错误码及表名
TEST_F(PubSupErrRetFun, DataLog_043_039)
{
    char g_errorCode1[1024] = {0};
    (void)snprintf(g_errorCode1, 1024, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode1);

    char libName[] = "notify_msgNotify3";
    char tableInput[] = "input1";
    int expectErrorCode = 1000;
    char expectErrorLabelName[] = "out1";
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/notify_msgNotify3.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT userData1 = {0};
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/out1Sub.gmjson", &userData1, 200, g_subName,
        snCallbackSetErrorCode, inp3GetProject);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "单写数据数据");
    int32_t insertValue = 10;
    ret = singleRecordInsert(g_stmt, tableInput, insertValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcGetStmtAttr获取错误码及表名");
    int64_t errorCode = 0;
    char errorLabelName[512] = {0};
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_CODE, &errorCode, sizeof(int64_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_DTL_ERROR_LABEL_NAME, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "GmcBatchDeparseRetDtlError获取错误码及表名");
    errorCode = 0;
    memset(errorLabelName, '\0', sizeof(errorLabelName));
    ret = GmcBatchDeparseRetDtlError(&batchRet, &errorCode, errorLabelName, 512);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(expectErrorCode, errorCode);
    EXPECT_STREQ(expectErrorLabelName, errorLabelName);
    GmcBatchUnbindStmt(batch, g_stmt);
    GmcBatchDestroy(batch);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}

// DTS2024112023655
// 040.资源型pubsub表，插入数据，回调函数回填数据比插入数据少，插入失败
TEST_F(PubSupErrRetFun, DataLog_043_040)
{
    AddWhiteList(GMERR_DATA_EXCEPTION);

    char libName[] = "rsc";
    char tableInput[] = "input3";
    AW_FUN_Log(LOG_STEP, "加载Datalog");
    int ret = 0;    ret = TestLoadDatalog("./datalogFile/rsc.so");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "创建订阅关系");
    SnUserDataWithFuncT userData1 = {0};
    userData1.isPubsubRsc = true;
    const char *g_subName = "subVertexLabel";
    ret = createSubscription(g_stmt, g_subConn, (char *)"schemaFile/rsc3Sub.gmjson", &userData1, 200, g_subName,
        snCallbackRscPubSub2, rsc3Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "批量结构化插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}, {3, 3, 3, 1}};
    TEST_INPUT_01 objIn = (TEST_INPUT_01){0};
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableInput, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;

    ret = GmcBatchPrepare(g_conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson1, tableInput, tableInput);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {(char *)tableInput, 0, g_testNameSpace};

    for (int i = 0; i < 3; i++) {
        TestInput01Value(&objIn, count1[i]);
        ret = testStructSetVertexWithBuf(g_stmt, &objIn, &labelInfo);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchAddDML(batch, g_stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_DATA_EXCEPTION, ret);
    ret = testWaitSnRecv(userData1.data, GMC_SUB_EVENT_INSERT, 5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "取消订阅关系，卸载Datalog");
    ret = cancelSubscription(g_stmt, g_subName, &userData1, 0, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestUninstallDatalog(libName);
}
