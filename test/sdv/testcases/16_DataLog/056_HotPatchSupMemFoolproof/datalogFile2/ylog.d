%function NotEqual1(a: int1, b: int1)
%function NotEqual2(a: int2, b: int2)
%function NotEqual4(a: int4, b: int4)
%function CmpInt1(a: int1, b: int1, c: int1)
%function CmpInt4(a: int4, b: int4, c: int1)
%function CmpInt8(a: int8, b: int8, c: int1)
%function CirToCbs(a: int8 -> b: int8)
%function I1ToI4(a: int1 -> b: int4)
%function I2ToI4(a: int2 -> b: int4)
%function I4ToI1(a: int4 -> b: int1)
%function I4ToI2(a: int4 -> b: int2)
%function I4ToI8(a: int4 -> b: int8)
%function CheckTupleCount(dummy: int4, is_delete: int1)
%function AddI4(a: int4, b: int4 -> c: int4)
%table state(state: int4)
%table tbl_init(i: int4)
%table null0(i: int4)
tbl_init(0):- Ifm.ConfigIf(-, -, 0, -, -, -, -).
tbl_init(0):- state(0).
null(0) :- null0(-).
%table TbmPatchInput(type: int4, data: byte256) { transient(tuple) }
%table TbmPatch(type: int4, data: byte256) { index(0(type, data)), tbm }
%rule r0 TbmPatch(type, data) :- TbmPatchInput(type, data).
null(0) :- tbl_init(a), I4ToI1(a, a1), I1ToI4(a1, a), I4ToI2(a, a2), I2ToI4(a2, a), I4ToI8(a, a3), CmpInt1(a1, 0, 0), CmpInt4(a, 0, 0), NotEqual2(a2, 0), NotEqual1(a1, 0), CirToCbs(a3, a4), CmpInt8(a4, 0, 0).
null(0) :- tbl_init(a), AddI4(a, 1, c), NotEqual4(a, 0).
%table product_s380(i: int1)
%table product_s310s(i: int1)
%table product_um(i: int1)
%table product_wlan(i: int1)
%function init(){
    access_kv(capset)
}
%function uninit(){
    access_kv(capset)
}
%function NotZerobyte200(a: byte200)
namespace Acl {
%readonly GroupCfg, AdvRule, EthRule, PortPoolCfg, IpPoolCfg, TimeRangeCfg
%table GroupCfg(
    aclGroupId:int4,
    aclGroupType:int1,
    aclNumber:int4, aclName:byte65, isNameAcl:int1,
    isIPv6:int1, aclVrId: int4, vsysid:int4
)
{
    index(0(aclGroupId)),
    update_partial
}
%table AdvRule(
    aclGroupId:int4, aclIndex:int4,
    actionType:int1,
    aclCondMask:int4,
    srcIpAddr:int4, srcIpMask:int4, srcIpPool:int4,
    dstIpAddr:int4, dstIpMask:int4, dstIpPool:int4,
    srcPortBeg:int2, srcPortEnd:int2, srcPortOp:int1, srcPortPool:int4,
    dstPortBeg:int2, dstPortEnd:int2, dstPortOp:int1, dstPortPool:int4,
    anyFlag:int1, protocol:int1, fragType:int1, tos:int1, tcpFlag:int1, icmpType:int1, icmpCode:int1, dscp:int1, igmpType:int1, ipPre: int1, srcUclIndex:int2, uclSrcType:int1, dstUclIndex:int2, uclDstType:int1,
    srcIpv6Addr:byte16, srcIpv6Mask:byte16,
    dstIpv6Addr:byte16, dstIpv6Mask:byte16,
    l4Version:int1,
    logFlag:int1, aclVpnIndex:int4, trngStatus:int1, trngId:int4, pktLenOp:int1,
    pktLenBgn:int2, pktLenEnd:int2, tcpEstablished:int1, tcpFlagMask:int1, ttlOp:int1,
    ttlBgn:int1, ttlEnd:int1, vni:int4, tableType:int1, aclVrId: int4,
    aclPriority:int4
)
{
    index(0(aclGroupId, aclIndex)),
    index(1(aclVrId, aclGroupId)),
    update_partial
}
%table EthRule(
    aclGroupId:int4, aclIndex:int4,
    actionType:int1,
    aclCondMask:int4,
    frameType:int2, frameMask:int2, srcMac:byte6, srcMacMask:byte6, dstMac:byte6, dstMacMask:byte6, vlanId:int2, vlanIdMask:int2, value8021p:int1,
    aclVrId:int4, trngStatus:int1, trngId:int4, aclPriority:int4)
{
    index(0(aclGroupId, aclIndex)),
    index(1(aclVrId, aclGroupId)),
    update_partial
}
%table PortPoolCfg(
    poolId:int4, portId:int4,
    startPort:int2, endPort:int2, rangeOp:int1,
    vrId:int4
)
{
    index(0(poolId, portId)),
    update_partial
}
%table IpPoolCfg(
    poolId:int4, ipId:int4,
    ipAddr:int4, ipMask:int4,
    vrId:int4
)
{
    index(0(poolId, ipId)),
    update_partial
}
%table TimeRangeCfg(
    trngId:int4, vrId:int4, trngInnerId:int4, trngStatus:int1) {
    index(0(trngId)),
    update_partial
}
}
namespace Arp {
%readonly ConfigArp, VlanArpReplay, ArpCfg, ArpGatewayDupBlock, IfArpReplay, IfArpProxy
%table ConfigArp(
    addr: int4, ifIndex: int4, type: int1, mac: byte6,
    fakeFlag: int1, vlanId: int2, workIfIndex: int4, detectCount:int4, agingTime: int8)
{
    index(0(addr, ifIndex)),
    index(1(ifIndex)),
    index(2(ifIndex, type)),
    index(3(addr)),
    index(4(addr, ifIndex, type)),
    index(5(type)),
    index(6(addr, workIfIndex, type)),
    index(7(workIfIndex, type)),
    update_partial,
    timeout(field(agingTime), state_function),
    update_by_rank
}
%table VlanArpReplay(ns_id: int4, br_id: int4, vlan_id: int2, arp_reply: int1)
{
    index(0(ns_id, br_id, vlan_id)), update
}
%table ArpCfg(index: int1, arpGatewayDupEn: int1)
{
    index(0(index)), update
}
%table ArpGatewayDupBlock(ifIndex: int4, brId: int4, srcMac: byte6, vlanId: int2, agingTime: int8)
{
    index(0(ifIndex, brId, srcMac, vlanId)),
    update_partial,
    timeout(field(agingTime))
}
%table IfArpReplay(ifIndex: int4, arpReplyEn: int1) {
    index(0(ifIndex)), update
}
%table IfArpProxy(ifIndex: int4, arpProxyEn: int1) {
    index(0(ifIndex)), update
}
%precedence Ifm.PublishNif, ConfigArp
%precedence Ifm.ConfigIf, ConfigArp
}
namespace Nd {
%readonly ConfigNd, IfNdProxy
%table ConfigNd(
    addr: byte16, ifIndex: int4, type: int1, mac: byte6,
    fakeFlag: int1, vlanId: int2, workIfIndex: int4)
{
    index(0(addr, ifIndex)),
    index(1(ifIndex)),
    index(2(ifIndex, type)),
    index(3(addr)),
    index(4(addr, ifIndex, type)),
    index(5(type)),
    index(6(addr, workIfIndex, type)),
    index(7(workIfIndex, type)),
    update_partial
}
%table IfNdProxy(ifIndex: int4, ndProxyEn: int1) {
    index(0(ifIndex)), update
}
%precedence Ifm.PublishNif, ConfigNd
%precedence Ifm.ConfigIf, ConfigNd
}
namespace BR {
%readonly Bridge, BridgeMacAttr, Vlan, VlanMacAttr, Port, PortMacAttr, PortSecurity
%readonly MacOper, MstpIfBlocking, MstpIfDynMacFlush, L2ProtocolTunnel
%readonly Instance, PortInstanceState, PortInstanceFlush
%readonly ErpsRule, ErpsRingPort, ErpsRingInstance, ErpsRingInstanceBitmap, ErpsRingSFPktTemplate, ErpsRingBullet
%table Bridge(ns_id: int4, br_id: int4, br_name: byte16, vlan_filter: int1, br_if_index: int4) {index(0(ns_id, br_id)), update_partial}
%table BridgeMacAttr(ns_id: int4, br_id: int4, mac_age_time: int4, mac_thrd_up: int1, mac_thrd_down: int1, macLimitNum: int4) {index(0(ns_id, br_id)), update_partial}
%table Vlan(ns_id: int4, br_id: int4, vlan_id: int2, vlan_type: int1, name: byte32, desc: byte81, br_vlan_if: int4, is_ctrl: int1, inst_id: int2) {index(0(ns_id, br_id, vlan_id)), update_partial}
%table VlanMacAttr(ns_id: int4, br_id: int4, vlan_id: int2, learn: int1, limit: int4, limit_act: int1, limit_alm: int1) {index(0(ns_id, br_id, vlan_id)), update_partial}
%table VlanTagPort(ns_id: int4, br_id: int4, vlan_id: int2, port_index: int4, if_index: int4) {index(0(ns_id, br_id, vlan_id, port_index)), index(1(if_index)), update_partial}
%table TagAllIf(ifIndex: int4)
%table VlanUntagPort(ns_id: int4, br_id: int4, vlan_id: int2, port_index: int4, if_index: int4) {index(0(ns_id, br_id, vlan_id, port_index)), index(1(if_index)), update_partial}
%table Port(if_index: int4, port_index: int4, ns_id: int4, br_id: int4, pvid: int2, link_type: int1, stp_enable: int1) {index(0(if_index)), index(1(port_index)), update_partial}
%table PortIsolateGrp(if_index: int4, port_index: int4, grp: int1) {index(0(if_index, port_index, grp)), index(1(if_index)), update_partial}
%table PortMacAttr(if_index: int4, port_index: int4, learn: int1, learn_act: int1, limit: int4, limit_act: int1, limit_alm: int1) {index(0(if_index)), index(1(port_index)), update_partial}
%table PortSecurity(if_index: int4, port_index: int4, mac_sec: int1, mac_sec_max: int4, mac_sec_act: int1, mac_sec_age_time: int4) { index(0(if_index)), update_partial}
%table MacOper(ns_id: int4, br_id: int4, vlan_id: int2, mac: byte6, if_index: int4, type: int1, flag: int4) {index(0(ns_id, br_id, vlan_id, mac)), transient(tuple)}
%table L2ProtocolTunnel(ifIndex: int4, prot: int1){index(0(ifIndex, prot)), update_partial}
%table MstpIfBlocking(ifIndex:int4,stpInstance:int2) {index(0(ifIndex, stpInstance)),update_partial}
%table MstpIfDynMacFlush(ifIndex:int4,stpInstance:int2) {transient(tuple)}
%table Instance(instId:int2, isReservedInst: int1, topChgNo: int1, vlanAddNo: int1, vlanDelNo: int1) {index(0(instId)), update_partial}
%table PortInstanceState(ifIndex: int4, instId: int2, state: int1) {index(0(ifIndex, instId)), update_partial}
%table PortInstanceFlush(ifIndex:int4, instId:int2) {index(0(ifIndex, instId)),transient(finish)}
%table ErpsRule(srcMac: byte6, srcMacMask: byte6, dstMac: byte6, dstMacMask: byte6, ethType: int2, vlanId: int2, priority: int1, action: int1) {
    index(0(srcMac, srcMacMask, dstMac, dstMacMask, ethType, vlanId)), update_partial }
%table ErpsRingPort(ifIndex: int4, ringId: int4, ctrlVlan: int2, enable: int1, vers: int1, portRole: int1, vcMode: int1, isSubRing: int1) {
    index(0(ifIndex, ringId)), update_partial }
%table ErpsRingInstance(ringId: int4, instances: byte128, instNum: int1) {
    index(0(ringId)), update_partial }
%table ErpsRingInstanceBitmap(ringId: int4, instances: byte512) {
    index(0(ringId)), update_partial }
%table ErpsRingSFPktTemplate(ringId: int4, ifIndex: int4, ctrlVlan: int2, pktLen: int2, templateType: int1, pktTemplate: byte64) {
    index(0(ringId, ifIndex)), update_partial }
%table ErpsRingBullet(ringId: int4, ifIndex: int4, bullet: byte4) {
    index(0(ringId, ifIndex)), update_partial }
}
namespace Capture {
%readonly Config
%table Config(
    id: int1, aclGroupId: int4, direction: int1, localhost: int1, vlanId: int2, ifIndexList: byte32,
    packetNumber: int2, packetLength: int2, timeout: int8 )
    { index(0(id)), update_partial }
}
namespace Fib {
    %readonly ConfigIpv4Fwd, ConfigNhpGroup, ConfigNhpGroupNode, ConfigLoadBalance
    %readonly ConfigNhpBasic, ConfigNhpStandard, ConfigInterfaceWeight, ConfigGlobalUcmpSwitch
    %readonly ConfigInterfaceWeightUcmpSwitch
%table ConfigIpv4Fwd(
    nsId: int4, vrfId: int4, dstIp: int4, maskLen: int4,
    nhpGroupFlag: int1, routeAttr: int2, routeFlags: int2, pathFlags: int4,
    nhpGroupId: int4, primaryLabel: int4, attributeId: int4, qosId: int2)
{
    index(0(nsId, vrfId, dstIp, maskLen)), update, fast_insert
}
%table ConfigNhpGroup(
    nsId: int4, nhpGroupId: int4, nhpNum: int4, vrfId: int4)
{
    index(0(nsId, nhpGroupId)), update, fast_insert
}
%table ConfigNhpGroupNode(
    nsId: int4, nhpGroupId: int4, attributeId: int4, primaryNhpId: int4,
    primaryLabel: int4, backupNhpId: int4, backupLabel: int4, vrfId: int4)
{
    index(0(nsId, nhpGroupId, attributeId, primaryNhpId, primaryLabel, backupNhpId, backupLabel)), update, fast_insert
}
%table ConfigNhpBasic(
    nhpIndex: int4, vrfId: int4, originNhp: int4, iidFlags: int4, nsId: int4)
{
    index(0(nhpIndex)), update, fast_insert
}
%table ConfigNhpStandard(
    nhpIndex: int4, nextHop: int4, outIfIndex: int4, vrfId: int4, iidFlags: int4, nsId: int4)
{
    index(0(nhpIndex, nextHop, outIfIndex)), update, fast_insert
}
%table ConfigInterfaceWeight(ifIndex: int4, weight: int4)
{
    index(0(ifIndex)),
    update
}
%table ConfigGlobalUcmpSwitch(dummyKey: int4, defaultBandwidthEnable: int1, configBandwidthEnable: int1)
{
    index(0(dummyKey)),
    update_partial
}
%table ConfigInterfaceWeightUcmpSwitch(ifIndex: int4, isEnable: int1)
{
    index(0(ifIndex)),
    update
}
%table ConfigLoadBalance(vr: int4, mode: int1, srcIp: int1, dstIp: int1, srcPort: int1, dstPort: int1)
{
    index(0(vr)),
    update_partial
}
}
namespace Ib {
    %readonly ConfigInterfaceBackup
%table ConfigInterfaceBackup(
    primaryIfIndex: int4, primaryState: int4, standbyIfIndexes: byte12, standbyStates: byte12, standbyIfsLen: int4, freshFwdIfIndex: int4)
{
    index(0(primaryIfIndex)),
    update
}
}
namespace Fib6 {
    %readonly ConfigIpv6Fwd, ConfigNhpGroup, ConfigNhpGroupNode
    %readonly ConfigNhpBasic, ConfigNhpStandard
%table ConfigIpv6Fwd(
    nsId: int4, vrfId: int4, dstIp: byte16, prefix: int4,
    nhpGroupFlag: int1, routeAttr: int2, routeFlags: int2, pathFlags: int4,
    nhpGroupId: int4, primaryLabel: int4, attributeId: int4, qosId: int2)
{
    index(0(nsId, vrfId, dstIp, prefix)), update, fast_insert
}
%table ConfigNhpGroup(
    nsId: int4, nhpGroupId: int4, nhpNum: int4, vrfId: int4)
{
    index(0(nsId, nhpGroupId)), update, fast_insert
}
%table ConfigNhpGroupNode(
    nsId: int4, nhpGroupId: int4, attributeId: int4, primaryNhpId: int4,
    primaryLabel: int4, backupNhpId: int4, backupLabel: int4, vrfId: int4)
{
    index(0(nsId, nhpGroupId, attributeId, primaryNhpId, primaryLabel, backupNhpId, backupLabel)), update, fast_insert
}
%table ConfigNhpBasic(
    nhpIndex: int4, vrfId: int4, originNhp: byte16, iidFlags: int4, nsId: int4)
{
    index(0(nhpIndex)), update, fast_insert
}
%table ConfigNhpStandard(
    nhpIndex: int4, nextHop: byte16, outIfIndex: int4, vrfId: int4, iidFlags: int4, nsId: int4)
{
    index(0(nhpIndex, nextHop, outIfIndex)), update, fast_insert
}
}
namespace Hsec {
%readonly PolicyFilterCfg, PolicyCpcar, AppliedPolicyCfg, AutoDefend, AttackSource, StormControlRate, StormControlAction, AntiAttack,
    DefaultCpcar, AdjustCpcar, ProtocolEnable, PublicHostCar, CpcarAttr, CpcarPriority, TcpSession, DnsRequestDenyFromWan, ProtocolEnableForVlan
%table TcpSession(srcIpAddr:int4, dstIpAddr:int4, srcPort:int2, dstPort:int2, protocol:int1, pid:int4)
    { index(0(srcIpAddr, dstIpAddr, srcPort, dstPort, protocol)), update }
%table PublicHostCar(nsId: int4, arpEnable: int1, dhcpEnable: int1, ieee8021xEnable: int1, pps: int4)
    { index(0(nsId)), update_partial }
%table PolicyFilterCfg(policyId:int4, filterId:int4, groupId:int4)
    { index(0(policyId, filterId)), update_partial }
%table PolicyCpcar(policyId:int4, protocolType:int4, flags:int1, pps:int4)
    { index(0(policyId, protocolType, flags)), update_partial }
%table ProtocolEnable(protocolType:int4, flags:int1, ifIndex:int4)
    { index(0(protocolType, flags, ifIndex)), update_partial }
%table ProtocolEnableForVlan(protocolType:int4, flags:int1, vlanId:int2)
    { index(0(protocolType, flags, vlanId)), update_partial }
%table CpcarAttr(protocolType:int4, subProtocolType:int1, protocolNum:int4, srcIpAddr:int4, srcIpMask:int4, dstIpAddr:int4, dstIpMask:int4,
    srcPort:int2, srcPortMask:int2, dstPort:int2, dstPortMask:int2, frameType:int2, frameMask:int2, srcMac:byte6,
    srcMacMask:byte6, dstMac:byte6, dstMacMask:byte6, pppType:int2, capwapData:byte4, condMask:byte4, action: int1, priority: int1, flags:byte1)
    { index(0(protocolType, subProtocolType)), update_partial }
%table CpcarPriority(protocolType:int4, priority: int1 )
    { index(0(protocolType)), update_partial }
%table DefaultCpcar(protocolType:int4, flags:int1, pps:int4 )
    { index(0(protocolType, flags)), update_partial }
%table AdjustCpcar(protocolType:int4, flags:int1, pps:int4 )
    { index(0(protocolType, flags)), update_partial }
%table AppliedPolicyCfg(policyId:int4, type:int4)
    { index(0(policyId)), update_partial }
%table AntiAttack(attackType:int4, enable:int1, pps:int4)
    { index(0(attackType)), update_partial }
%table StormControlRate(ifIndex:int4, packetType:int4, pps_max_rate:int4)
    { index(0(ifIndex, packetType)), update_partial }
%table StormControlRateEx(ifIndex:int4, packetType:int4, isPps: int1, pps: int4, cir: int4, pir: int4, cbs: int4, pbs: int4)
    { index(0(ifIndex, packetType)), update_partial }
%table StormControlAction(ifIndex:int4, action:int1)
    { index(0(ifIndex)), update_partial }
%table AutoDefend(policyId:int4, enable:int1, alarmEnable:int1, alarmThreshold:int4, penaltyEnable:int1, penaltyThreshold:int4, defendMask:int1)
    { index(0(policyId)), update_partial }
%table AttackSource(sourceType:int1, ipv4Addr:int4, ipv6Addr:byte16, mac:byte6, isIpv6:int1, timeLeft:int8)
    { index(0(sourceType, ipv4Addr, ipv6Addr, mac, isIpv6)), update_partial, timeout(field(timeLeft)) }
%table DnsRequestDenyFromWan(noUse: int4) { index(0(noUse)), update_partial }
%precedence Ifm.PublishNif, AntiAttack
%precedence Ifm.ConfigIf, AntiAttack
}
namespace Ifm {
    %readonly ConfigVapIf, PublishRadioName, ConfigIf, PublishNif, ConfigAttributes
    %readonly ConfigIfIpv4Addr, ConfigIfIpv6Addr, ConfigIfIpLearn, ConfigIfIpv6Learn, ConfigProtocolEnable, ConfigTcpMssAdjust
    %readonly ConfigLinkUpDownMode, PortAttrChg, ConfigIfWmpPortal, ConfigIfWmpWebProxy, Pppoe
    %readonly ConfigFreeRuleTemplate, EthTrunk, EthTrunkMembers, ConfigIsolateMode, EthTrunkMacSyncPort, ConfigDialerBundle
    %readonly EthTrunkLoadBalanceMode
    %table PortAttrChg(ifIndex: int4, attr: int1, value: int4) { index(0(ifIndex, attr)), update_partial }
    %table Pppoe(ifIndex: int4, code: int1, ver: int1, type: int1, sessionId: int2, dmac: byte6) { index(0(ifIndex)), update }
    %table ConfigTcpMssAdjust(ifIndex: int4, tcpMss: int2) { index(0(ifIndex)), update_partial }
   %table ConfigVapIf(nsId: int4, ifIndex: int4, radioName: byte64, adminState: int1, vapType: int1, vapIndex: int1, linkedId: int1, mac: byte6,
        userIdentify: int1, userService: int1, userAuth: int1, authType: int1, isEapolKeyToCp: int1, protocolTunnelFwd: int1, ssid: byte33,
        globalIdx:int2, vapFlag:int1, broadPeerId:int4, broadPfeVapIdx: int4, broadSessionInfo: int2, mainLinkFlag: int1,
        multiMediaEn: int1, tcpWndTurn: int1, arpTest: int1) {
        index(0(ifIndex)), index(1(vapIndex)), update_partial,
        union_delete(Qos.InterfaceMcBcDeny, Qos.InterfaceMdnsDeny, Qos.InterfaceMcBc2UcMissDrop, Arp.IfArpReplay, Wmp.ConfigPriEn, ConfigTcpMssAdjust, ConfigIfIpLearn, ConfigIsolateMode)
    }
    %table PublishRadioName(radioName: byte64, type: byte16, tp: int4, phy_port: int4, port_id: int4, cpu_type: int1, drvPortType: int1, drvSubType: int1) {
        index(0(radioName)), update_partial
    }
    %table ConfigIf(
        nsId: int4, ifName: byte64, ifIndex: int4, type: int2, phyPort: int4, masterIfIndex: int4, state: int1)
        { index(0(nsId, ifName)), update_partial }
    %table PublishNif(
        ifIndex: int4, ifName: byte64, type: byte16, tp: int4, chip_unit: int1, oda_phy_port: int1,
        state: int1, mac: byte6, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
        unit_id: int4, port_id: int4, cpu_type: int1, drvPortType: int1, drvSubType: int1)
        { index(0(ifIndex)), update_partial}
    %table ConfigAttributes(
        nsId: int4, ifIndex: int4, phyState: int1, mac: byte6, mtu: int4,
        if_cfg_bandwidth: int4,
        mtu6: int4, autoneg: int1,
        speed: int8, duplex: int1, combo: int1, loopback: int1, hasV4Addr: int1, alias: byte64,
        service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1, mru: int2,
        link_type: int1, eth_class: int1, hasV6Addr: int1)
        { index(0(nsId, ifIndex)), update_partial }
    %table ConfigIfIpv4Addr(ifIndex: int4, vrfIndex: int4, address: int4, maskLen: int2, type: int2)
        { index(0(ifIndex, vrfIndex, address, maskLen, type)),
          index(1(ifIndex)),
          update_partial
    }
    %table ConfigIfIpv6Addr(ifIndex: int4, vrfIndex: int4, address: byte16, prefix: int4, type: int2)
        { index(0(ifIndex, vrfIndex, address, prefix, type)),
          index(1(ifIndex)),
          update_partial
    }
    %table ConfigIfIpLearn(ifIndex: int4, ipLearnEnable: int1, dhcpStrict: int1, addBlackList: int1) {
        index(0(ifIndex)), update_partial
    }
    %table ConfigIfIpv6Learn(ifIndex: int4, ipv6LearnEnable: int1, dhcpv6Strict: int1, dhcpv6Slaac: int1, addBlackList: int1) {
        index(0(ifIndex)), update_partial
    }
    %table ConfigIfWmpPortal(ifIndex: int4, apPortalEn: int1, freeRuleName: byte65) {
        index(0(ifIndex)), update_partial
    }
    %table ConfigFreeRuleTemplate(templateName: byte65, ipv4GroupId: int4, ipv6GroupId: int4, staticGroupName: byte65) {
        index(0(templateName)), update_partial
    }
    %table ConfigIfWmpWebProxy(ifIndex: int4, webProxyPortalParse: int1, proxyPort:int2, url:byte200) {
        index(0(ifIndex)), update_partial
    }
    %table ConfigProtocolEnable(ifIndex: int4, proto: int1, enable: int1) {
        index(0(ifIndex)), update_partial
    }
    %table ConfigLinkUpDownMode(ifIndex: int4, mode: int1) {
        index(0(ifIndex)), update_partial
    }
    %table ConfigIsolateMode(ifIndex: int4, mode: int1) {
        index(0(ifIndex)), update_partial
    }
    %table EthTrunk(ifIndex: int4, nsId: int4, trunkId: int4, domain: int1){
        index(0(ifIndex, nsId)),
        update
    }
    %table EthTrunkMembers(memIfIndex: int4, trunkId: int4, nsId: int4, ifIndex: int4, weight: int2){
        index(0(memIfIndex)),
        index(1(trunkId)),
        update
    }
    %table EthTrunkMacSyncPort(vlan_id: int2, mac: byte6, if_index: int4, type: int1) {
        index(0(vlan_id, mac)), transient(tuple)
    }
    %table EthTrunkLoadBalanceMode(hashType: int1, hashField: int4) { index(0(hashType)), update_partial }
    %table ConfigDialerBundle(dialerIfIndex: int4, vlanIfIndex: int4) {
        index(0(dialerIfIndex)), update_partial
    }
}
namespace Mir {
    %readonly ObserverIndex, PortMirror
    %readonly MirrorObserve, MirrorMirror
    %table ObserverIndex(observeIndex: int2, ifIndex: int4)
    {
        index(0(observeIndex)),
        update_partial
    }
    %table PortMirror(ifIndex: int4, observeIndex: int2, direction: int1)
    {
        index(0(ifIndex, observeIndex, direction)),
        update_partial
    }
    %table MirrorObserve(ifIndex: int4, mirrorId: int4)
    {
        index(0(ifIndex, mirrorId)), update_partial
    }
    %table MirrorMirror(ifIndex: int4, direction: int1, mirrorId: int4)
    {
        index(0(ifIndex, direction)), update_partial
    }
}
namespace Sacl {
%readonly TrafficInstance, TrafficRedirectNextHop
%table TrafficInstance(
    policyType:int1, direction:int1, protoFamily:int1, appType:int1, appValue:int4,
    aclL2GroupId:int4, aclL4GroupId: int4,
    order: int4,
    actionType:int4, actionValue:int4,
    cir:int4, pir:int4, cbs:int4, pbs:int4, greenAction:int1, greenRemarkType:int1, greenRemarkValue:int1, yellowAction:int1, yellowRemarkType:int1, yellowRemarkValue:int1, redAction:int1, redRemarkType:int1, redRemarkValue:int1,
    vrId:int4, isPhyType:int1, groupStage:int4, ifType:int1, applyVlan:int2,
    ifVlanId:int2)
{
    index(0(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, aclL4GroupId)),
    index(1(policyType, appType, appValue)),
    update_partial
}
%table TrafficRedirectNextHop(ifIndex: int4, nextHop: int4)
{
    index(0(ifIndex)), update_partial
}
}
namespace Wmp {
    %readonly ConfigCapwapTunnel, ConfigGlobalHostMode, ConfigWiredTunnel
    %table ConfigCapwapTunnel(sip: int4, dip: int4, sport: int2, dport: int2, mtu: int2, ifIndex: int4, dtlsIndex: int4,
        preamble: int1, tflag: int1, mflag: int1,
        server_or_client: int1, encrypt: int1, apMac: byte6) {
        index(0(sip, dip, sport, dport)), update_partial
    }
    %table ConfigGlobalHostMode(nsId: int1, value: int1) { index(0(nsId)), update_partial }
    %table ConfigWiredTunnel(ifIndex: int4, fwdMode: int1, portId: int4, portType: int4, apMac: byte6) { index(0(ifIndex)), update_partial }
}
namespace Wmp {
    %readonly ConfigMacIp, ConfigMacIpv6
    %table ConfigMacIp(mac: byte6, ipAddr: int4, learnType: int1) {
        index(0(mac, ipAddr)), update_partial
    }
    %table ConfigMacIpv6(mac: byte6, ipv6Addr: byte16, learnType: int1) {
        index(0(mac, ipv6Addr)), update_partial
    }
}
namespace Wmp {
    %readonly ConfigPriority, ConfigPriEn
    %table ConfigPriority(vapIndex: int4, type: int1, val: byte64) {
        index(0(vapIndex, type)), index(1(vapIndex)), update
    }
    %table ConfigPriEn(
        ifIndex: int4, ingUp2CosEn: int1, ingUp2TosEn: int1, ingUpTo8021pEn: int1, ingDscpTo8021pEn: int1,
        ingSvpPriEn: int1, ingUp2TnlDscpEn: int1, ingUp2Tnl8021pEn: int1, ingDscpToTnl8021pEn: int1,
        ingDscpToTnlDscpEn: int1, up2CosMode: int1, up2CosFixVal: int4, egrTos2UpEn: int1,
        egrCos2Up: int1, egr8021p2Up: int1, egrDscp2Up: int1) {
        index(0(ifIndex)), update_partial
    }
}
namespace Qos {
    %readonly GlobalMngPort, VapFloodThreshold, VapFloodBlacklist, InterFaceMcBcCar, GlobalMcBcCar, UserRemark
    %readonly InterfaceMcBc2Uc, InterfaceMcBcDeny, InterfaceMcBc2UcMissDrop, VapUserCar, VapCar, InterfaceMdnsDeny
    %table GlobalMngPort(protoType: int1, port: int2) {
        index(0(protoType)), transient(tuple)
    }
    %table VapFloodThreshold(ifIndex: int4, type: int4, pps: int4) {
        index(0(ifIndex, type)), index(1(ifIndex)), update_partial
    }
    %table VapFloodBlacklist(ifIndex: int4, type: int4, enable: int1) {
        index(0(ifIndex, type)), index(1(ifIndex)), update_partial
    }
    %table InterFaceMcBcCar(ifIndex: int4, type: int4, pps: int4) {
        index(0(ifIndex, type)), index(1(ifIndex)), update_partial
    }
    %table GlobalMcBcCar(type: int4, pps: int4) {
        index(0(type)), update_partial
    }
    %table InterfaceMcBc2Uc(ifIndex: int4, type: int4, toUcEn: int1) {
        index(0(ifIndex, type)), index(1(ifIndex)), update_partial
    }
    %table InterfaceMcBcDeny(ifIndex: int4, mcbcDeny: int1) {
        index(0(ifIndex)), update
    }
    %table InterfaceMdnsDeny(ifIndex: int4, mdnsDeny: int1) {
        index(0(ifIndex)), update
    }
    %table InterfaceMcBc2UcMissDrop(ifIndex: int4, drop: int1) {
        index(0(ifIndex)), update_partial
    }
    %table VapUserCar(ifIndex: int4, direction: int1, cir: int8) {
        index(0(ifIndex, direction)), index(1(ifIndex)), update
    }
    %table VapCar(ifIndex: int4, direction: int4, cir: int8) {
        index(0(ifIndex, direction)), index(1(ifIndex)), update_partial
    }
    %table UserRemark(
        staMac:byte6,
        ing8021pEn: int1, ingDscpEn: int1, ingLpEn: int1,
        ing8021pVal: int4, ingDscpVal: int4, ingLpVal: int4,
        egr8021pEn: int1, egrDscpEn: int1, egrLpEn: int1,
        egr8021pVal: int4, egrDscpVal: int4, egrLpVal: int4) {
        index(0(staMac)), update
    }
}
namespace Usf {
    %readonly PublicAuth, PublicPortal, PublicUsfConfig, PublicSta
    %readonly PublicAcl, IngCar, EgrCar, PublicROAM
    %readonly PublicStationTrace
    %table IngCar(mac: byte6, pktNumOrPktLen: int1, carType: int1, colorMode: int1,
        cir: int8, cbs: int8, pir: int8, pbsEbs: int8,
        greenPermit: int1, yellowPermit: int1, redPermit: int1)
        {index(0(mac)), update}
    %table EgrCar(mac: byte6, pktNumOrPktLen: int1, carType: int1, colorMode: int1,
        cir: int8, cbs: int8, pir: int8, pbsEbs: int8,
        greenPermit: int1, yellowPermit: int1, redPermit: int1)
        {index(0(mac)), update}
    %table PublicROAM(mac:byte6, roamRole:int1, interIsolate:int1, innerIsolate:int1, tunnelDstRole:int1,
        usrGroupId:int2, homeVapFwdif:int4, foreignVapFwdif:int4, tunnelId:int4, userIsolate:int1, vlanId:int2, ipAddr:byte16, hapMac:byte6)
        {
            index(0(mac)),
            update_partial
        }
    %table PublicSta(staMac:byte6, pktCacheType:int1, vapIfIndex:int4, globalIdx:int2, staFlag: int1, peerId:int4, pfeVapIdx: int4, sessionInfo: int2, radioId: int1, authId: int4, mainVapIfIndex: int4, startParse: int1, upload: int1)
        {index(0(staMac)), update_partial, union_delete(PublicPortal, PublicAcl, PublicAuth, EgrCar, IngCar, PublicROAM, Sac.SacProfileUsf, Qos.UserRemark)}
    %table PublicPortal(staMac:byte6, portalPushEn:int1, denyPushEn:int1, httpSendToL4:int1, denyAll:int1, preAuth:int1, portalEscape:int1, checkUserIp:int1,
        userIpv4Addr:int4, denyArpEn:int1)
        {index(0(staMac)), update_partial}
    %table PublicAcl(staMac:byte6,
        ipv4AuthAclGroupIds:byte16, ipv4AuthAclNum:int1,
        ipv4RedirectAclGroupIds:byte16, ipv4RedirectAclNum:int1)
        {index(0(staMac)), update_partial}
    %table PublicAuth(staMac:byte6, authStatus:int1, authVlanEn:int1, authVlan:int2, userVipFlag:int1, ingStatEn:int1,
        egrStatEn:int1, statId:int4, userGroupId:int2, userGroupEn:int1, needSendUpMsg:int1, userPreRoamIpv4Addr:int4,
        innerIsolateEn:int1, interIsolateEn:int1)
        {index(0(staMac)), update_partial}
    %table PublicUsfConfig(configIndex:int1, ipv6PortalFreeRuleGid:int2, httpsPortalEn:int1, httpsPortalDstPort:int2,
        daaAclGid:int2, ipv6DaaAclGid:int2, localManageEn:int1, manageIpAddr:int4, manageIpv6Addr:byte16, portalDstIp:int4,
        portalDstPort:int2, portalEnable:int1, portalDnsDisable:int4, httpPortalPort:byte32, httpPortalPortNum:int1, portalFreeRuleAclName:byte65, staIpv6SvcEnable:int1)
        {index(0(configIndex)), update_partial}
    %table PublicStationTrace(configIndex:int1, enable: int1, isAll: int1, staNum:int1, staMac:byte60) {index(0(configIndex)), update_partial}
}
namespace Nat {
    %readonly nat_policy
    %readonly nat_policy_address_mask, nat_policy_address_masklen, nat_policy_address_range
    %readonly nat_policy_service
    %readonly nat_policy_ifIndex_array
    %readonly nat_policy_action, nat_policy_action_staticMap, nat_policy_action_dnat, nat_policy_action_nonat
    %readonly nat_server
    %readonly nat_pool_group, nat_pool_section, nat_pool_exclude_ip, nat_pool_exclude_port, nat_pool_group_type
    %readonly nat_arp_enable, nat_arp_pool_section, nat_arp_pool_exclude, nat_host_arp
    %readonly nat_aspf_config, nat_interface_enable, nat_config_aging_time, nat_policy_service_srcPort
    %readonly nat_policy_service_dstPort, nat_policy_interface
    %table nat_interface_enable(ifIndex: int4, enable: int1) {
        index(0(ifIndex)), update_partial
    }
    %table nat_policy(policyName: byte64, policyType: int2, ruleId: int4) {
        index(0(policyName, policyType)), update_partial
    }
    %table nat_policy_address_mask(policyName: byte64, policyType:int2, ruleConSerial: int2, any: int2, except: int2, ipAddr: int4, mask: int4) {
        index(0(policyName, policyType, ruleConSerial, any, except, ipAddr, mask)), update_partial
    }
    %table nat_policy_address_masklen(policyName: byte64, policyType:int2, ruleConSerial: int2, any: int2, except: int2, ipAddr: int4, maskLen: int4) {
        index(0(policyName, policyType, ruleConSerial, any, except, ipAddr, maskLen)), update_partial
    }
    %table nat_policy_address_range(policyName: byte64, policyType:int2, ruleConSerial: int2, any: int2, except: int2, ipAddr1: int4, ipAddr2: int4) {
        index(0(policyName, policyType, ruleConSerial, any, except, ipAddr1, ipAddr2)), update_partial
    }
    %table nat_policy_service_srcPort(policyName: byte64, policyType:int2, ruleConSerial: int2, condId: int2, any: int2, except: int2, is_proto: int1, proto: int1,
        startSrcPort: int2, endSrcPort: int2, srcSystemFlag: int1) {
        index(0(policyName, policyType, ruleConSerial, condId, any, except, is_proto, proto, startSrcPort, endSrcPort, srcSystemFlag)), update_partial
    }
    %table nat_policy_service_dstPort(policyName: byte64, policyType:int2, ruleConSerial: int2, condId: int2, any: int2, except: int2, is_proto: int1, proto: int1,
        startDstPort: int2, endDstPort: int2, dstSystemFlag: int1) {
        index(0(policyName, policyType, ruleConSerial, condId, any, except, is_proto, proto, startDstPort, endDstPort, dstSystemFlag)), update_partial
    }
    %table nat_policy_service(policyName: byte64, policyType:int2, ruleConSerial: int2, condId: int2, any: int2, except: int2, is_proto: int1, proto: int1,
        srcItemNum: int2, srcPortItems: byte128, dstItemNum: int2, dstPortItems: byte128) {
        index(0(policyName, policyType, ruleConSerial, condId, any, except, is_proto, proto, srcItemNum, srcPortItems, dstItemNum, dstPortItems)), update_partial
    }
    %table nat_policy_interface(policyName: byte64, policyType:int2, ruleConSerial: int2, condId: int2, any: int2, ifIndex: int4) {
        index(0(policyName, policyType, ruleConSerial, condId, any, ifIndex)), update_partial
    }
    %table nat_policy_action(policyName: byte64, policyType:int2, actionType:int2, profileId:int4) {
        index(0(policyName, policyType)), update_partial
    }
    %table nat_policy_action_staticMap(policyName: byte64, policyType:int2, profileId: int4) {
        index(0(policyName, policyType)), update_partial
    }
    %table nat_policy_action_dnat(policyName: byte64, policyType:int2, natType: int2, profileId: int4, dstIp: int4, startPort: int2, endPort: int2) {
        index(0(policyName, policyType)), update_partial
    }
    %table nat_policy_action_nonat(policyName: byte64, policyType:int2) {
        index(0(policyName, policyType)), update_partial
    }
    %table nat_server(trans_type: int1, natsvr_noreverse: int1, sec_len: int4,
        global_vrf_idx: int2, host_vrf_idx: int2, global_ip_start: int4, host_ip_start: int4, global_port_start: int2, host_port_start: int2, natsvr_proto: int1) {
        index(0(trans_type, natsvr_noreverse, sec_len, global_vrf_idx, host_vrf_idx, global_ip_start, host_ip_start, global_port_start, host_port_start, natsvr_proto)),
        update_partial
    }
    %table nat_pool_group(pool_id: int2, type: int1, ref_count: int4) {
        index(0(pool_id)), update_partial
    }
    %table nat_pool_section(pool_id: int2, sect_id: int2, ip: int4, num: int4) {
        index(0(pool_id, sect_id)), update_partial
    }
    %table nat_pool_exclude_ip(pool_id: int2, start_addr: int4, end_addr: int4) {
        index(0(pool_id, start_addr)), update_partial
    }
    %table nat_pool_exclude_port(pool_id: int2, start_port: int2, end_port: int2) {
        index(0(pool_id, start_port)), update_partial
    }
    %table nat_pool_group_type(pool_id: int2, new_type: int1, ref_count: int4) {
        index(0(pool_id)), update_partial
    }
    %table nat_arp_enable(index: int4) {
        index(0(index)), update_partial
    }
    %table nat_arp_pool_section(serviceId: int2, startIp: int4, ipNum: int4) {
        index(0(serviceId, startIp)), update_partial
    }
    %table nat_arp_pool_exclude(serviceId: int2, excludeIp: int4, excludeIpNum: int4) {
        index(0(serviceId, excludeIp)), update_partial
    }
    %table nat_host_arp(serviceId: int2, startIp: int4, ipNum: int4) {
        index(0(serviceId, startIp)), update_partial
    }
    %table nat_aspf_config(index: int4, ftp: int1, dns: int1, pptp: int1, rtsp: int1, sip: int1) {
        index(0(index)), update_partial
    }
    %table nat_config_aging_time(agingType: int2, protocolType: int2, agingTime: int2) {
        index(0(agingType, protocolType)), update_partial
    }
    %aggregate nat_policy_interface_agg(ifIndex: int4 -> count: int4, ifIdxArray: byte32)
    %table nat_policy_ifIndex_array(policyName: byte64, policyType:int2, ruleConSerial: int2, condId: int2, any: int2, count: int4, ifIdxArray: byte32)
    nat_policy_ifIndex_array(policyName, policyType, ruleConSerial, condId, any, count, ifIdxArray):-
        nat_policy_interface(policyName, policyType, ruleConSerial, condId, any, ifIndex)
        GROUP-BY(policyName, policyType, ruleConSerial, condId, any) nat_policy_interface_agg(ifIndex, count, ifIdxArray).
    null(0):- nat_policy_service_srcPort(-,-,-,-,-,-,-,-,-,-,-).
    null(0):- nat_policy_service_dstPort(-,-,-,-,-,-,-,-,-,-,-).
    %precedence nat_policy, nat_policy_address_mask
    %precedence nat_policy, nat_policy_address_masklen
    %precedence nat_policy, nat_policy_address_range
    %precedence nat_policy, nat_policy_service_srcPort
    %precedence nat_policy, nat_policy_service_dstPort
    %precedence nat_policy, nat_policy_interface
    %precedence nat_policy, nat_policy_action
    %precedence nat_policy, nat_policy_action_staticMap
    %precedence nat_policy, nat_policy_action_dnat
    %precedence nat_policy, nat_policy_action_nonat
    %precedence nat_pool_group, nat_pool_section
    %precedence nat_pool_group, nat_pool_exclude_ip
    %precedence nat_pool_group, nat_pool_exclude_port
    %precedence nat_pool_group, nat_pool_group_type
    %precedence nat_pool_section, nat_policy_action_staticMap
    %precedence nat_pool_section, nat_policy_action_dnat
}
namespace Igmpsnp {
    %readonly IgmpSnooping, IgmpFib, IgmpMc2uc
    %readonly GlobalState, VlanState, McFwdTbl, RoutePort, RoutePortTransPacket
    %table IgmpSnooping(ifIndex: int4) { index(0(ifIndex)), update_partial }
    %table IgmpFib(vlanId: int2, groupAddr: int4, sourceAddr: int4, ifIndex: int4, usrMac: byte6) {
        index(0(vlanId, groupAddr, sourceAddr, ifIndex, usrMac)), update_partial }
    %table IgmpMc2uc(ifIndex:int4, enable:int1) {index(0(ifIndex)), update_partial }
    %table GlobalState(domain: int1, state: int1) { index(0(domain)), update_partial }
    %table VlanState(vlanId: int2, state: int1) { index(0(vlanId)), update_partial }
    %table McFwdTbl(vlanId: int2, groupAddr: int4, sourceAddr: int4, ifIndex: int4) { index(0(vlanId, groupAddr, sourceAddr, ifIndex)) }
    %table RoutePort(vlanId: int2, ifIndex: int4) { index(0(vlanId, ifIndex)), update_partial }
    %table RoutePortTransPacket(vlanId: int2) { index(0(vlanId)), update_partial }
}
namespace Dhcpsnp {
    %readonly StaticUserBind, DynamicUserBind, TrustIf
    %table StaticUserBind(macAddr: byte6, ipv4Addr: int4, vlanId: int2, ifIndex: int4, vrfIndex: int4, ipv6Addr: byte16, isPrefix: int1, prefixLen: int1, protoType: int1) {
        index(0(macAddr, ipv4Addr, vlanId, ifIndex, vrfIndex, ipv6Addr, isPrefix, prefixLen, protoType)), update_partial
    }
    %table DynamicUserBind(macAddr: byte6, ipv4Addr: int4, vlanId: int2, ifIndex: int4, vrfIndex: int4, serverIp:int4, lifeTime:int4, ipv6Addr: byte16, isPrefix: int1, prefixLen: int1, protoType: int1, clientRequestTime: int4) {
        index(0(macAddr, ipv4Addr, vlanId, ifIndex, vrfIndex, ipv6Addr, isPrefix, prefixLen, protoType)), update_partial
    }
    %table TrustIf(ifIndex: int4, trust: int1) { index(0(ifIndex)), update_partial }
}
namespace Iptable {
%readonly IPTableCfg
%table IPTableCfg(
    appType: int1, appValue: int4, direction: int1,
    aclL2GroupId: int4, aclL4GroupId: int4,
    toPort: int2, order: int4)
{
    index(0(appType, appValue, direction, aclL2GroupId, aclL4GroupId)),
    index(1(aclL2GroupId, aclL4GroupId)),
    update_partial
}
}
namespace Dnssnp {
    %readonly DnsSnooping
    %readonly DnsDomain
    %table DnsSnooping(noUse: int4) { index(0(noUse)), update_partial }
    %table DnsDomain(domain:byte128, domainMatchType:int1, domainID:int1) { index(0(domain, domainMatchType)), update_partial }
}
namespace Sac {
    %readonly SacLoad, SacProfileIf, SacProfileUsf, SacRemarkDefaultIf, SacRemarkDefaultUsf
    %table SacLoad(nsId: int1, path: str) {
        index(0(nsId)), update_partial
    }
    %table SacProfileIf(ifIndex:int4, sacProfileData: byte388) {
        index(0(ifIndex)),
        update_partial
    }
    %table SacProfileUsf(mac:byte6, ifIndex:int4, sacProfileData: byte388) {
        index(0(mac)),
        update_partial
    }
    %table SacRemarkDefaultIf(ifIndex:int4, remarkDefaultEn: int1) {
        index(0(ifIndex)),
        update_partial
    }
    %table SacRemarkDefaultUsf(mac:byte6, remarkDefaultEn: int1) {
        index(0(mac)),
        update_partial
    }
}
namespace Ndsnp {
    %readonly TrustIf
    %table TrustIf(ifIndex: int4, trust: int1) { index(0(ifIndex)), update_partial }
}
namespace BR {
%readonly MidVlanTagPort, MidVlanUntagPort, VlanPort, PortIsolate, PortAttrMap, VlanIfLinkState
%table VlanPort(brId: int4, vlanId: int2, ifIndex: int4)
%table MidVlanUntagPort(brId: int4, vlanId: int2, ifIndex: int4)
%table MidVlanTagPort(brId: int4, vlanId: int2, ifIndex: int4)
%table PortAttrMap(ifIndex: int4, dstIfIndex: int4)
%table PortIsolate(ifIndex: int4, isolateIfIndex: int4)
%table VlanIfUp(ifIndex: int4)
%table VlanIfLinkState(ifIndex: int4, state: int1)
PortAttrMap(ifIndex, ifIndex) :- Port(ifIndex, -, -, -, -, -, -), NOT Ifm.EthTrunkMembers(ifIndex, -, -, -, -).
PortAttrMap(memIfIndex, ifIndex) :- Port(ifIndex, -, -, -, -, -, -), Ifm.EthTrunkMembers(memIfIndex, -, -, ifIndex, -).
MidVlanUntagPort(brId, vlanId, ifIndex) :- Vlan(-, brId, vlanId, -, -, -, -, -, -), VlanUntagPort(-, brId, vlanId, -, ifIndex).
VlanPort(brId, vlanId, ifIndex) :- Vlan(-, brId, vlanId, -, -, -, -, -, -), VlanTagPort(-, brId, vlanId, -, ifIndex).
VlanPort(brId, vlanId, ifIndex) :- MidVlanUntagPort(brId, vlanId, ifIndex).
VlanPort(brId, vlanId, ifIndex) :- Vlan(-, brId, vlanId, -, -, -, -, -, -), TagAllIf(ifIndex), PortAttrMap(-, ifIndex).
MidVlanTagPort(brId, vlanId, ifIndex) :- VlanPort(brId, vlanId, ifIndex), NOT MidVlanUntagPort(brId, vlanId, ifIndex).
VlanIfUp(vlanIfIndex) :-
    Vlan(-, -, vlanId, -, -, -, vlanIfIndex, -, -),
    VlanPort(-, vlanId, ifIndex),
    Ifm.Agg_Attributes(-, ifIndex, -, 1, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).
VlanIfLinkState(ifIndex, 1) :- VlanIfUp(ifIndex).
VlanIfLinkState(ifIndex, 0) :- Ifm.Agg_Attributes(-, ifIndex, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -), NOT VlanIfUp(ifIndex).
%table PortIsolateGrp_Copy(ifIndex: int4, portIndex: int4, grp: int1)
%table PortAttrMap_Copy(ifIndex: int4, dstIfIndex: int4)
PortAttrMap_Copy(ifIndex, dstIfIndex) :- PortAttrMap(ifIndex, dstIfIndex).
PortIsolateGrp_Copy(ifIndex, portIndex, grp) :- PortIsolateGrp(ifIndex, portIndex, grp).
PortIsolate(ifIndex1, ifIndex2):-
    PortIsolateGrp(ifIndex3, -, grp),
    PortIsolateGrp_Copy(ifIndex4, -, grp),
    PortAttrMap(ifIndex1, ifIndex3),
    PortAttrMap_Copy(ifIndex2, ifIndex4),
    NotEqual4(ifIndex3, ifIndex4),
    NotEqual1(grp, 0).
}
namespace Fib {
    %readonly InterfaceMetrics, InterfaceWeightUcmpSwitch, InterfaceBackup, AggNhpFwdIndexArray, NhpIndexIsInNhpGroup
%table InterfaceMetrics(ifIndex: int4, weight: int4, defaultBandwidth: int4, configBandwidth: int4)
%table InterfaceWeight(ifIndex: int4, weight: int4)
%table InterfaceDefaultBandwidth(ifIndex: int4, defaultBandwidth: int4)
%table InterfaceWeightUcmpSwitch(ifIndex: int4, isEnable: int1)
%table InterfaceBackup(primaryIfIndex: int4, primaryState: int4, standbyIfIndexes: byte12, standbyStates: byte12, standbyIfsLen: int4, freshFwdIfIndex: int4)
InterfaceWeight(ifIndex, weight) :-
    Ifm.IfName(ifIndex, -, -, -, -, -, -, -),
    ConfigInterfaceWeight(ifIndex, weight).
InterfaceWeight(ifIndex, 0) :-
    Ifm.IfName(ifIndex, -, -, -, -, -, -, -),
    NOT ConfigInterfaceWeight(ifIndex, -).
InterfaceDefaultBandwidth(ifIndex, defaultBandwidth) :-
    Ifm.IfName(ifIndex, -, -, -, -, -, -, -),
    Ifm.DefaultInterfaceBandwidth(ifIndex, defaultBandwidth).
InterfaceDefaultBandwidth(ifIndex, 0) :-
    Ifm.IfName(ifIndex, -, -, -, -, -, -, -),
    NOT Ifm.DefaultInterfaceBandwidth(ifIndex, -).
InterfaceBackup(outIfIndex, primaryState, standbyIfIndexes, standbyStates, standbyIfsLen, freshFwdIfIndex) :-
    Ifm.IfName(outIfIndex, -, -, -, -, -, -, -),
    Ib.ConfigInterfaceBackup(outIfIndex, primaryState, standbyIfIndexes, standbyStates, standbyIfsLen, freshFwdIfIndex).
InterfaceBackup(outIfIndex, 0, "00", "00", 0, 0) :-
    Ifm.IfName(outIfIndex, -, -, -, -, -, -, -),
    NOT Ib.ConfigInterfaceBackup(outIfIndex, -, -, -, -, -).
InterfaceWeightUcmpSwitch(outIfIndex, isEnable) :-
    Ifm.IfName(outIfIndex, -, -, -, -, -, -, -),
    ConfigInterfaceWeightUcmpSwitch(outIfIndex, isEnable).
InterfaceWeightUcmpSwitch(outIfIndex, 0) :-
    Ifm.IfName(outIfIndex, -, -, -, -, -, -, -),
    NOT ConfigInterfaceWeightUcmpSwitch(outIfIndex, -).
InterfaceMetrics(ifIndex, weight, defaultBandwidth, configBandwidth) :-
    Ifm.ConfigAttributes(-, ifIndex, -, -, -, configBandwidth, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    InterfaceWeight(ifIndex, weight),
    InterfaceDefaultBandwidth(ifIndex, defaultBandwidth).
}
namespace Fib {
    %readonly get_fwd_nhp_index, NhpGroupAggregate, IsMulNhp, Ipv4Func, IpRoute4ForArp
    %readwrite res_fwd_nhp_index
%table NhpGroupJoinNhp(nhpGroupId: int4, nhpId: int4, fwdNhpIndex: int4,
    outIfIndex: int4, ifType: int2, primaryState: int4, standbyIfsLen: int4, standbyIfIndexes: byte12, standbyStates: byte12, freshFwdIfIndex: int4)
%table NhpGroupJoinNhpUcmp(
    nhpGroupId: int4, nhpId: int4, fwdNhpIndex: int4,
    weight: int4, defaultBandwidth: int4, configBandwidth: int4,
    weightEnable: int1, defaultBandwidthEnable: int1, configBandwidthEnable: int1,
    ifType: int2, outIfIndex: int4, ipState: int1, primaryState: int4, standbyIfsLen: int4, standbyIfIndexes: byte12, standbyStates: byte12, freshFwdIfIndex: int4)
%table NhpGroupAggregate(nhpGroupId: int4, firstFwdNhpIndex: int4, fwdNhpIndexesLen: int1, fwdNhpIndexes: byte128, primaryNphIndexes: int4)
%table get_fwd_nhp_index(nhpGroupId: int4, nhpId: int4, nextHop: int4, outIfIndex: int4, vrfId: int4, iidFlags: int4, fwdNhpIndex: int4)
%table IpRoute4ForArp(
    vrfId: int4, dstIp: byte4, maskLen: int4, nhpGroupId: int4, nhpIndex: int4, mulNhp: int1,
    routeFlags: int2, pathFlags: int4, dir_route: int1,
    def_route: int1)
%function IsMulNhp(nhpNum: int1)
%function NhpIndexIsInNhpGroup(resIndex: int4, fwdNhpIndexesLen: int1, fwdNhpIndexes: byte128)
%function Ipv4Func(routeAttr: int2, dstIp:int4 -> dir_route: int1, def_route: int1, dip: byte4)
%resource res_fwd_nhp_index(nhpId: int4, nextHop: int4 -> fwdNhpIndex: int4) {
        index(2(nextHop)),
        sequential(max_size(10000))
}
%aggregate AggNhpFwdIndexArray(fwdNhpIndex: int4,
    weight: int4, defaultBandwidth: int4, configBandwidth: int4,
    weightEnable: int1, defaultBandwidthEnable: int1, configBandwidthEnable: int1,
    ifType: int2, outIfIndex: int4, ipState: int1, primaryState: int4, standbyIfsLen: int4, standbyIfIndexes: byte12, standbyStates: byte12, freshFwdIfIndex: int4
    -> firstFwdNhpIndex: int4, fwdNhpIndexesLen: int1, fwdNhpIndexes: byte128, primaryNphIndexes: int4)
{
    ordered
}
res_fwd_nhp_index(nhpId, nextHop, -) :-
    ConfigNhpBasic(nhpId, -, -, -, -),
    ConfigNhpStandard(nhpId, nextHop, -, -, -, -).
%table get_fwd_nhp_index_not_arpnhp(nhpId: int4, nextHop: int4, fwdNhpIndex: int4)
get_fwd_nhp_index_not_arpnhp(nhpId, nextHop, fwdNhpIndex) :-
    res_fwd_nhp_index(nhpId, nextHop, fwdNhpIndex),
    CmpInt4(nhpId, 65536, 5).
NhpGroupJoinNhp(nhpGroupId, nhpId, fwdNhpIndex, outIfIndex, ifType, primaryState, standbyIfsLen, standbyIfIndexes, standbyStates, freshFwdIfIndex) :-
    ConfigNhpGroup(-, nhpGroupId, -, -),
    ConfigNhpGroupNode(-, nhpGroupId, -, nhpId, -, -, -, -),
    ConfigNhpBasic(nhpId, -, -, -, -),
    ConfigNhpStandard(nhpId, nextHop, outIfIndex, -, -, -),
    Ifm.Agg_Attributes(-, outIfIndex, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, ifType, -, -, -, -, -, -, -, -, -, -),
    InterfaceBackup(outIfIndex, primaryState, standbyIfIndexes, standbyStates, standbyIfsLen, freshFwdIfIndex),
    get_fwd_nhp_index_not_arpnhp(nhpId, nextHop, fwdNhpIndex).
NhpGroupJoinNhpUcmp(nhpGroupId, nhpId, fwdNhpIndex,
                    weight, defaultBandwidth, configBandwidth,
                    weightEnable, defaultBandwidthEnable, configBandwidthEnable, ifType,
                    outIfIndex, ipv4State, primaryState, standbyIfsLen, standbyIfIndexes, standbyStates, freshFwdIfIndex) :-
    NhpGroupJoinNhp(nhpGroupId, nhpId, fwdNhpIndex, outIfIndex, ifType, primaryState, standbyIfsLen, standbyIfIndexes, standbyStates, freshFwdIfIndex),
    InterfaceMetrics(outIfIndex, weight, defaultBandwidth, configBandwidth),
    ConfigGlobalUcmpSwitch(-, defaultBandwidthEnable, configBandwidthEnable),
    InterfaceWeightUcmpSwitch(outIfIndex, weightEnable),
    Ifm.IfNet(outIfIndex, -, -, -, ipv4State).
NhpGroupAggregate(nhpGroupId, firstFwdNhpIndex, fwdNhpIndexesLen, fwdNhpIndexes, primaryNphIndexes) :-
    NhpGroupJoinNhpUcmp(nhpGroupId, -, fwdNhpIndex,
                        weight, defaultBandwidth, configBandwidth,
                        weightEnable, defaultBandwidthEnable, configBandwidthEnable, ifType,
                        outIfIndex, ipv4State, primaryState, standbyIfsLen, standbyIfIndexes, standbyStates, freshFwdIfIndex)
    GROUP-BY (nhpGroupId)
    AggNhpFwdIndexArray(fwdNhpIndex,
                        weight, defaultBandwidth, configBandwidth,
                        weightEnable, defaultBandwidthEnable, configBandwidthEnable,
                        ifType, outIfIndex, ipv4State, primaryState, standbyIfsLen, standbyIfIndexes, standbyStates, freshFwdIfIndex,
                        firstFwdNhpIndex, fwdNhpIndexesLen, fwdNhpIndexes, primaryNphIndexes).
get_fwd_nhp_index(nhpGroupId, nhpId, nextHop, outIfIndex, vrfId, iidFlags, fwdNhpIndex) :-
    ConfigNhpGroup(-, nhpGroupId, -, -),
    ConfigNhpGroupNode(-, nhpGroupId, -, nhpId, -, -, -, -),
    ConfigNhpBasic(nhpId, -, -, -, -),
    ConfigNhpStandard(nhpId, nextHop, outIfIndex, vrfId, iidFlags, -),
    get_fwd_nhp_index_not_arpnhp(nhpId, nextHop, fwdNhpIndex),
    NhpGroupAggregate(nhpGroupId, -, fwdNhpIndexesLen, fwdNhpIndexes, -),
    NhpIndexIsInNhpGroup(fwdNhpIndex, fwdNhpIndexesLen, fwdNhpIndexes).
}
namespace Fib6 {
    %readonly get_fwd_nhp_index, NhpGroupAggregate, Ipv6Func
%table NhpGroupJoinNhp(nhpGroupId: int4, nhpId: int4, fwdNhpIndex: int4,
    outIfIndex: int4, ifType: int2, primaryState: int4, standbyIfsLen: int4, standbyIfIndexes: byte12, standbyStates: byte12, freshFwdIfIndex: int4)
%table NhpGroupJoinNhpUcmp(
    nhpGroupId: int4, nhpId: int4, fwdNhpIndex: int4,
    weight: int4, defaultBandwidth: int4, configBandwidth: int4,
    weightEnable: int1, defaultBandwidthEnable: int1, configBandwidthEnable: int1,
    ifType: int2, outIfIndex: int4, ipv6State: int1, primaryState: int4, standbyIfsLen: int4, standbyIfIndexes: byte12, standbyStates: byte12, freshFwdIfIndex: int4)
%table NhpGroupAggregate(nhpGroupId: int4, firstFwdNhpIndex: int4, fwdNhpIndexesLen: int1, fwdNhpIndexes: byte128, primaryNphIndexes: int4)
%table get_fwd_nhp_index_not_ndnhp(nhpId: int4, nextHop: byte16, fwdNhpIndex: int4)
%table get_fwd_nhp_index(nhpId: int4, nextHop: byte16, outIfIndex: int4, vrfId: int4, iidFlags: int4, fwdNhpIndex: int4)
%function Ipv6Func(routeAttr: int2 -> dir_route: int1, def_route: int1)
%resource res_fwd_nhp_index(nhpId: int4, nextHop: byte16 -> fwdNhpIndex: int4) {
        index(2(nextHop)),
        sequential(max_size(10000))
}
res_fwd_nhp_index(nhpId, nextHop, -) :-
    Fib6.ConfigNhpBasic(nhpId, -, -, -, -),
    Fib6.ConfigNhpStandard(nhpId, nextHop, -, -, -, -).
get_fwd_nhp_index_not_ndnhp(nhpId, nextHop, fwdNhpIndex) :-
    res_fwd_nhp_index(nhpId, nextHop, fwdNhpIndex),
    CmpInt4(nhpId, 65536, 5).
NhpGroupJoinNhp(nhpGroupId, nhpId, fwdNhpIndex, outIfIndex, ifType, primaryState, standbyIfsLen, standbyIfIndexes, standbyStates, freshFwdIfIndex) :-
    Fib6.ConfigNhpGroup(-, nhpGroupId, -, -),
    Fib6.ConfigNhpGroupNode(-, nhpGroupId, -, nhpId, -, -, -, -),
    Fib6.ConfigNhpBasic(nhpId, -, -, -, -),
    Fib6.ConfigNhpStandard(nhpId, nextHop, outIfIndex, -, -, -),
    Ifm.Agg_Attributes(-, outIfIndex, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, ifType, -, -, -, -, -, -, -, -, -, -),
    Fib.InterfaceBackup(outIfIndex, primaryState, standbyIfIndexes, standbyStates, standbyIfsLen, freshFwdIfIndex),
    get_fwd_nhp_index_not_ndnhp(nhpId, nextHop, fwdNhpIndex).
NhpGroupJoinNhpUcmp(nhpGroupId, nhpId, fwdNhpIndex,
                    weight, defaultBandwidth, configBandwidth,
                    weightEnable, defaultBandwidthEnable, configBandwidthEnable, ifType,
                    outIfIndex, ipv6State, primaryState, standbyIfsLen, standbyIfIndexes, standbyStates, freshFwdIfIndex) :-
    NhpGroupJoinNhp(nhpGroupId, nhpId, fwdNhpIndex, outIfIndex, ifType, primaryState, standbyIfsLen, standbyIfIndexes, standbyStates, freshFwdIfIndex),
    Fib.InterfaceMetrics(outIfIndex, weight, defaultBandwidth, configBandwidth),
    Fib.ConfigGlobalUcmpSwitch(-, defaultBandwidthEnable, configBandwidthEnable),
    Fib.InterfaceWeightUcmpSwitch(outIfIndex, weightEnable),
    Ifm.IfNet6(outIfIndex, -, -, ipv6State).
NhpGroupAggregate(nhpGroupId, firstFwdNhpIndex, fwdNhpIndexesLen, fwdNhpIndexes, primaryNphIndexes) :-
    NhpGroupJoinNhpUcmp(nhpGroupId, -, fwdNhpIndex,
                        weight, defaultBandwidth, configBandwidth,
                        weightEnable, defaultBandwidthEnable, configBandwidthEnable, ifType,
                        outIfIndex, ipv6State, primaryState, standbyIfsLen, standbyIfIndexes, standbyStates, freshFwdIfIndex)
    GROUP-BY (nhpGroupId)
    Fib.AggNhpFwdIndexArray(fwdNhpIndex,
                        weight, defaultBandwidth, configBandwidth,
                        weightEnable, defaultBandwidthEnable, configBandwidthEnable,
                        ifType, outIfIndex, ipv6State, primaryState, standbyIfsLen, standbyIfIndexes, standbyStates, freshFwdIfIndex,
                        firstFwdNhpIndex, fwdNhpIndexesLen, fwdNhpIndexes, primaryNphIndexes).
get_fwd_nhp_index(nhpId, nextHop, outIfIndex, vrfId, iidFlags, fwdNhpIndex) :-
    Fib6.ConfigNhpGroup(-, nhpGroupId, -, -),
    Fib6.ConfigNhpGroupNode(-, nhpGroupId, -, nhpId, -, -, -, -),
    Fib6.ConfigNhpBasic(nhpId, -, -, -, -),
    Fib6.ConfigNhpStandard(nhpId, nextHop, outIfIndex, vrfId, iidFlags, -),
    get_fwd_nhp_index_not_ndnhp(nhpId, nextHop, fwdNhpIndex),
    NhpGroupAggregate(nhpGroupId, -, fwdNhpIndexesLen, fwdNhpIndexes, -),
    Fib.NhpIndexIsInNhpGroup(fwdNhpIndex, fwdNhpIndexesLen, fwdNhpIndexes).
}
namespace Hsec {
%readonly Applied_PolicyCpcar, Mid_PolicyCpcar, Mid_AttackSource, Mid_StormControlRate, ProtocolEnableForVlanM
%readwrite Cause
%table Mid_PolicyCpcar( protocolType:int4, flags:int1, pps:int4) { index(1(protocolType, flags)) }
%table Applied_PolicyCpcar( protocolType:int4, flags:int1, pps:int4) { index(1(protocolType, flags)) }
%table Mid_StormControlRate(ifIndex:int4, packetType:int4, action: int1, isPps: int1, pps: int4, cir: int4, pir: int4, cbs: int4, pbs: int4)
%table Mid_AttackSource(
    policyId:int4,
    sourceType:int1,
    ipv4Addr:int4,
    ipv6Addr:byte16,
    mac:byte6,
    isIpv6:int1)
%table Cause(protocolType:int4, subProtocolType:int4, paCauseId: int4, cpCauseId: int4, cpuPortIdx:int1, qIndex: int1, l3FibHit: int1)
%table ProtocolEnableForVlanM(protocolType:int4, flags:int1, vlanId:int2)
Mid_AttackSource(policyId, sourceType, ipv4Addr, ipv6Addr, mac, isIpv6) :-
    AttackSource(sourceType, ipv4Addr, ipv6Addr, mac, isIpv6, -),
    AutoDefend(policyId, -, -, -, 1, -, -),
    AppliedPolicyCfg(policyId, -).
Applied_PolicyCpcar(protocolType, flags, pps) :-
    PolicyCpcar(policy_id, protocolType, flags, pps),
    AppliedPolicyCfg(policy_id, -).
ProtocolEnableForVlanM(protocolType, flags, vlanId) :- ProtocolEnableForVlan(protocolType, flags, vlanId), NOT ProtocolEnable(protocolType, flags, -).
ProtocolEnableForVlanM(12, 1, vlanId) :- Igmpsnp.VlanState(vlanId, 1).
ProtocolEnableForVlanM(181, 1, vlanId) :- Igmpsnp.VlanState(vlanId, 1).
ProtocolEnableForVlanM(13, 1, vlanId) :- Igmpsnp.VlanState(vlanId, 1).
Mid_PolicyCpcar(protocolType, flags, pps) :-
    DefaultCpcar(protocolType, flags, pps),
    ProtocolEnable(protocolType, flags, -),
    NOT Applied_PolicyCpcar(protocolType, flags, -),
    NOT AdjustCpcar(protocolType, flags, -).
Mid_PolicyCpcar(protocolType, flags, pps) :-
    DefaultCpcar(protocolType, flags, pps),
    ProtocolEnableForVlanM(protocolType, flags, -),
    NOT Applied_PolicyCpcar(protocolType, flags, -),
    NOT AdjustCpcar(protocolType, flags, -).
Mid_PolicyCpcar(protocolType, flags, pps) :-
    Applied_PolicyCpcar(protocolType, flags, pps),
    NOT AdjustCpcar(protocolType, flags, -).
Mid_PolicyCpcar(protocolType, flags, pps) :-
    AdjustCpcar(protocolType, flags, pps).
Mid_StormControlRate(ifIndex, packetType, action, isPps, pps, cir, pir, cbs, pbs) :-
    Hsec.StormControlRateEx(ifIndex, packetType, isPps, pps, cir, pir, cbs, pbs),
    Hsec.StormControlAction(ifIndex, action).
Mid_StormControlRate(ifIndex, packetType, 3, isPps, pps, cir, pir, cbs, pbs) :-
    Hsec.StormControlRateEx(ifIndex, packetType, isPps, pps, cir, pir, cbs, pbs),
    NOT Hsec.StormControlAction(ifIndex, -).
Mid_StormControlRate(ifIndex, packetType, action, 1, pps, 0, 0, 0, 0) :-
    Hsec.StormControlRate(ifIndex, packetType, pps),
    Hsec.StormControlAction(ifIndex, action),
    NOT Hsec.StormControlRateEx(ifIndex, packetType, -, -, -, -, -, -).
Mid_StormControlRate(ifIndex, packetType, 3, 1, pps, 0, 0, 0, 0) :-
    Hsec.StormControlRate(ifIndex, packetType, pps),
    NOT Hsec.StormControlAction(ifIndex, -),
    NOT Hsec.StormControlRateEx(ifIndex, packetType, -, -, -, -, -, -).
}
namespace Ifm {
    %readonly Agg_Attributes, CalculateIfLinkState, DefaultInterfaceBandwidth, IfName, IfPhy, IfLink, IfNet, IfNet6, IfL3
    %readonly CalculateIfIpState, VapAclGroup, VapAclGroupIds, EthTrunkMembers_Copy
    %table DefaultInterfaceBandwidth(ifIndex: int4, defaultBandwidth: int4)
    %table Agg_Attributes(
        nsId: int4, ifIndex: int4, ifName: byte64, phyState: int1, adminState: int1,
        onboard_state: int1, if_bw: int8, tp: int4, tb: int4, chip_unit: int1, oda_phy_port: int1, mac: byte6,
        autoneg: int1, speed: int8, duplex: int1, combo: int1, loopback: int1, mtu: int4, if_cfg_bandwidth: int4, mtu6: int4,
        type: int2, hasV4Addr: int1, service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1,
        mru: int2, link_type: int1, eth_class: int1, cpu_type: int1, hasV6Addr: int1)
    %table IfName(
        if_index: int4, namespace_id: int4, if_name: byte64,
        if_type: int2, if_cfg_bandwidth: int4, if_show_type: int2, if_alias: byte64, description: byte64)
    %table IfPhy(
        if_index: int4, if_bw: int8, phy_state: int1, admin_state: int1, onboard_state: int1,
        tb: int4, tp: int4, chip_unit: int1, oda_phy_port: int1, phy_mac: byte6, eth_autoneg: int1, eth_speed: int8,
        eth_duplex: int1, eth_combo: int1, eth_loopback: int1, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
        unit_id: int4, port_id: int4, service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1,
        mru: int2, link_type: int1, eth_class: int1, cpu_type: int1, drvPortType: int1, drvSubType: int1)
    %table IfLink(
        if_index: int4, link_type: int4, link_state: int1, oper_state: int1)
    %table IfNet(if_index: int4, vrf_id: int4, df_flag: int4, mtu_v4: int4, ipv4_state: int1)
    %table IfNet6(if_index: int4, vrf_id: int4, mtu_v6: int4, ipv6_state: int1)
    %table IfL3(ifIndex: int4)
    %table EthTrunkMembers_Copy(memIfIndex: int4, trunkId: int4, nsId: int4, ifIndex: int4, weight: int2)
    %function GetIfType(ifType: byte16 -> type: int2)
    %function CalculateIfBw(speed: int8 -> if_bw: int8)
    %function CalculateIfPhyState(type: int2, srcPhyState: int1, adminState: int1 -> phyState: int1)
    %function CalculateIfLinkState(ifIndex: int4, type: int2, phyState: int1, vlanIfLinkState: int1 -> linkState: int1)
    %function CalculateIfIpState(type: int2, linkState: int1, hasAddr: int1 -> ipState: int1)
    %function GetIfMac(initMac: byte6, userMac: byte6 -> mac: byte6)
    %function IsGeInterface(ifType: byte16)
    Agg_Attributes(
        nsId, ifIndex, ifName, phyState, adminState, 0, if_bw, tp, 0, 0, 0, mac,
        autoneg, speed, duplex, combo, loopback, mtu, cfgBandwidth, mtu6, type, hasV4Addr, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, 0, hasV6Addr) :-
        ConfigIf(
            nsId, ifName, ifIndex, type,
            tp, - , srcPhyState),
        ConfigAttributes(
            nsId, ifIndex, adminState, mac, mtu, cfgBandwidth, mtu6,
            autoneg, speed, duplex, combo, loopback, hasV4Addr, - , service_type, autoneg_cap,
            autoneg_adv_cap, medium, mru, link_type, eth_class, hasV6Addr),
        CalculateIfBw(speed, if_bw),
        CalculateIfPhyState(type, srcPhyState, adminState, phyState).
    Agg_Attributes(
        0, ifIndex, ifName, phyState, adminState, 0, if_bw, tp, 0, chip_unit, oda_phy_port, mac,
        autoneg, speed, duplex, combo, loopback, mtu, cfgBandwidth, mtu6, type, hasV4Addr, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type, hasV6Addr) :-
        PublishNif(
            ifIndex, ifName, ifType, tp, chip_unit, oda_phy_port, srcPhyState, initMac, -, -, -, -, -, -, cpu_type, -, -),
        ConfigAttributes(
            -, ifIndex, adminState, userMac, mtu, cfgBandwidth, mtu6,
            autoneg, speed, duplex, combo, loopback, hasV4Addr, - , service_type,
            autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, hasV6Addr),
        GetIfType(ifType, type),
        CalculateIfBw(speed, if_bw),
        CalculateIfPhyState(type, srcPhyState, adminState, phyState),
        GetIfMac(initMac, userMac, mac).
    DefaultInterfaceBandwidth(ifIndex, 1000000) :-
        PublishNif(ifIndex, -, ifType, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        IsGeInterface(ifType).
    IfName(ifIndex, nsId, ifName, type, if_cfg_bandwidth, 0, "00", "00") :-
        Agg_Attributes(nsId, ifIndex, ifName, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, if_cfg_bandwidth, -, type, -, -, -, -, -, -, -, -, -, -).
    IfPhy(
        ifIndex, ifBw, phyState, adminState, onboardState,
        tb, tp, chip_unit, oda_phy_port, mac, autoeng, speed, duplex, combo, loopback, dev_id, chassis_id, slot_id, card_id, unit_id, port_id, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type, drvPortType, drvSubType) :-
        PublishNif(ifIndex, -, -, -, chip_unit, oda_phy_port, -, -, dev_id, chassis_id, slot_id, card_id, unit_id, port_id, -, drvPortType, drvSubType),
        Agg_Attributes(
            -, ifIndex, -, phyState, adminState, onboardState, ifBw,
            tp, tb, chip_unit, oda_phy_port, mac, autoeng, speed, duplex, combo, loopback, -, -, -, -, -, service_type,
            autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type, -).
    IfPhy(
        ifIndex, ifBw, phyState, adminState, onboardState,
        tb, tp, 0, 0, mac, autoeng, speed, duplex, combo, loopback, 0, 0, 0, 0, 0, 0, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type, 0, 0) :-
        ConfigIf(-, -, ifIndex, -, -, -, -),
        Agg_Attributes(
            -, ifIndex, -, phyState, adminState, onboardState, ifBw,
            tp, tb, -, -, mac, autoeng, speed, duplex, combo, loopback, -, -, -, -, -, service_type,
            autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type, -).
    IfLink(ifIndex, 0, linkState, linkState) :-
        Agg_Attributes( -, ifIndex, -, phyState, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, type, -, -, -, -, -, -, -, -, -, -),
        BR.VlanIfLinkState(ifIndex, vlanIfLinkState),
        CalculateIfLinkState(ifIndex, type, phyState, vlanIfLinkState, linkState).
    IfNet(ifIndex, 0, 0, mtu, ipv4State) :-
        Agg_Attributes(-, ifIndex, -, phyState, -, -, -, -, -, -, -, -, -, -, -, -, -, mtu, -, -, type, hasV4Addr, -, -, -, -, -, -, -, -, -),
        BR.VlanIfLinkState(ifIndex, vlanIfLinkState),
        CalculateIfLinkState(ifIndex, type, phyState, vlanIfLinkState, linkState),
        CalculateIfIpState(type, linkState, hasV4Addr, ipv4State).
    IfNet6(ifIndex, 0, mtu6, ipv6State) :-
        Agg_Attributes(-, ifIndex, -, phyState, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, mtu6, type, -, -, -, -, -, -, -, -, -, hasV6Addr),
        BR.VlanIfLinkState(ifIndex, vlanIfLinkState),
        CalculateIfLinkState(ifIndex, type, phyState, vlanIfLinkState, linkState),
        CalculateIfIpState(type, linkState, hasV6Addr, ipv6State).
    IfL3(ifIndex) :-
        Ifm.IfName(ifIndex, -, -, -, -, -, -, -),
        NOT BR.Port(ifIndex, -, -, -, -, -, -).
    %table VapAclGroupIds(ifIndex: int4, v4Gid: int4, v6Gid: int4, staticGid: int4)
    %table VapAclGroup(gid: int4, type: int4)
    %table VapStaticAclNotPresent(templateName: byte65, v4Gid: int4, v6Gid: int4)
    %readonly GroupCfgCpy
    %table GroupCfgCpy(
        aclGroupId:int4,
        aclGroupType:int1,
        aclNumber:int4, aclName:byte65, isNameAcl:int1,
        isIPv6:int1, aclVrId: int4, vsysid:int4
    )
    GroupCfgCpy(aclGroupId, aclGroupType, aclNumber, aclName, isNameAcl, isIPv6, aclVrId, vsysid):-
        Acl.GroupCfg(aclGroupId, aclGroupType, aclNumber, aclName, isNameAcl, isIPv6, aclVrId, vsysid).
    VapAclGroupIds(ifIndex, v4Gid, v6Gid, 0) :-
        ConfigIfWmpPortal(ifIndex, -, templateName),
        VapStaticAclNotPresent(templateName, v4Num, v6Gid),
        Acl.GroupId2Num(v4Gid, v4Num).
    VapStaticAclNotPresent(templateName, v4Gid, v6Gid) :-
        ConfigFreeRuleTemplate(templateName, v4Gid, v6Gid, staticName),
        NOT Acl.GroupCfg(-, 3, -, staticName, 1, -, -, -),
        NOT GroupCfgCpy(-, 7, -, staticName, 1, -, -, -).
    VapAclGroupIds(ifIndex, v4Gid, v6Gid, staticGid) :-
        ConfigIfWmpPortal(ifIndex, -, templateName),
        ConfigFreeRuleTemplate(templateName, v4Num, v6Gid, staticName),
        Acl.GroupCfg(staticGid, 3, -, staticName, 1, -, -, -),
        Acl.GroupId2Num(v4Gid, v4Num).
    VapAclGroupIds(ifIndex, v4Gid, v6Gid, staticGid) :-
        ConfigIfWmpPortal(ifIndex, -, templateName),
        ConfigFreeRuleTemplate(templateName, v4Num, v6Gid, staticName),
        Acl.GroupCfg(staticGid, 7, -, staticName, 1, -, -, -),
        Acl.GroupId2Num(v4Gid, v4Num).
    VapAclGroup(gid, 0) :- VapAclGroupIds(-, gid, -, -), NotEqual4(gid, 0).
    VapAclGroup(gid, 0) :- VapAclGroupIds(-, -, gid, -), NotEqual4(gid, 0).
    VapAclGroup(gid, 0) :- VapAclGroupIds(-, -, -, gid), NotEqual4(gid, 0).
    EthTrunkMembers_Copy(memIfIndex, trunkId, nsId, ifIndex, weight) :-
        EthTrunkMembers(memIfIndex, trunkId, nsId, ifIndex, weight).
}
namespace Dhcpsnp {
    %table UserBindNotify(macAddr: byte6, ipv4Addr: int4, vlanId: int2, ifIndex: int4,
                          serverIp:int4, lifeTime:int4, bindType: int4, ipv6Addr: byte16, isPrefix: int1, prefixLen: int1, protoType: int1) {
        index(0(macAddr, ipv4Addr, vlanId, ifIndex)),
        update, notify
    }
}
namespace Igmpsnp {
%readwrite MFibTrafficStatisticsId
%table MFibTrafficStatisticsId(vlanId: int2, groupAddr: int4, sourceAddr: int4, statType: int1, mgId: int4, statId: int4)
    { index(0(vlanId, groupAddr, sourceAddr, statType, mgId, statId)), index(1(vlanId, groupAddr, sourceAddr)), update, notify }
}
namespace BR {
%readwrite PortVlanFwdState, VlanTopChange
%table PortVlanFwdState(ifIndex: int4, vlan: int2, state:int1) {index(0(ifIndex, vlan)), update, notify}
%table VlanTopChange(vlan: int2) {index(0(vlan)), update, notify}
}
namespace Hsec {
%readwrite CpCarIdForLsw
%table CpCarIdForLsw(protocolType: int4, subProtocolType: int4, carId: int4) { notify }
}
%table arp(ip_address: byte4, if_index: int4, arp_type: int1, mac_address: byte6, fake_flag: int1, work_if_index: int4, vlanid: int2)
{
    index(0(ip_address, if_index, arp_type)),
    external
}
%table aging_detect_message(addr: int4, ifIndex: int4, vlanId: int2, workIfIndex: int4, count: int4)
{
    index(0(addr, ifIndex, vlanId, workIfIndex)),
    external
}
%table if_base(if_index: int4, namespace_id: int4, if_name: byte64,
    if_alias: byte64, if_type: int2, if_cfg_bandwidth: int4, if_show_type: int2, description: byte243)
{
    index(0(if_index)),
    external
}
%table if_phy(
    if_index: int4, if_bw: int8, phy_state: int1, admin_state: int1, onboard_state: int1,
    tb: int4, tp: int4, phy_mac: byte6, eth_autoneg: int1, eth_speed: int8,
    eth_duplex: int1, eth_combo: int1, eth_loopback: int1, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
    unit_id: int4, port_id: int4, chip_unit: int1, oda_phy_port: int1, service_type: int1, autoneg_cap: int1,
    autoneg_adv_cap: int2, medium: int1, mru: int2, eth_class: int1, eth_type: int1,
    nat_enable: int1, nat64_enable: int1, stp_enable: int1)
{
    index(0(if_index)),
    external
}
%table if_link(if_index: int4, link_type: int4, link_state: int1, oper_state: int1)
{
    index(0(if_index)),
    external
}
%table if_route(if_index: int4, vrf_id: int4, ipv4_state: int1, mtu_v4: int4, df_flag: int4, ipv6_state: int1, mtu_v6: int4)
{
    index(0(if_index)),
    external
}
%table if_eth_trunk_member(member_if_index: int4, trunk_id: int4)
{
    index(0(member_if_index)),
    external
}
%table acl_group(
    acl_groupid:int4, isipv6:int1, vsid:int2, vsysid:int4, acl_number:int4, acl_group_type:int4, acl_name:byte65)
{
    index(0(acl_groupid, isipv6)),
    external
}
%table acl_base(
    vrid:int4, acl_index:int4, acl_group_id:int4, vrf_index:int4, acl_priority:int4, cond_mask:int4,
    time_range_id:int4, time_range_status:int1, acl_action_type:int1, any_flag:int1, frag_type:int1, src_ip:int4,
    src_ip_mask:int4, log_flag:int1)
{
    index(0(vrid, acl_index, acl_group_id)),
    external
}
%table acl_adv(
    vrid: int4, acl_index:int4, acl_group_id:int4, vrf_index:int4, acl_priority:int4, cond_mask:int4,
    time_range_id:int4, time_range_status:int1, acl_action_type:int1, src_ip:int4, src_ip_mask:int4, dest_ip:int4,
    dest_ip_mask:int4, s_ippool_index:int4, d_ippool_index:int4, b_src_port:int2, e_src_port:int2, b_dst_port:int2,
    e_dst_port:int2, s_port_pool_id:int4, d_port_pool_id:int4, log_flag:int1, any_flag:int1, protocol:int1,
    frag_type:int1, tos:int1, tcp_flag:int1, icmp_type:int1, icmp_code:int1, dscp:int1,
    ip_prec:int1, igmp_type:int1, ttl_expired:int1, established:int1, tcp_flag_mask:int1, tcp_established:int1,
    ttl_op:int1, ttl_bgn:int1, ttl_end:int1)
{
    index(0(vrid, acl_index, acl_group_id)),
    external
}
%table acl_eth(
    vrid:int4, acl_index:int4, acl_group_id:int4,
    acl_action_type:int1, frame_type:int2, frame_mask:int2,
    src_mac:byte6, src_mac_mask:byte6, dest_mac:byte6, dest_mac_mask:byte6,
    vlan_id:int2, vlan_id_mask:int2,
    value_8021p:int1,
    cond_mask:int4, time_range_status:int1, time_range_id:int4, acl_priority:int4)
{
    index(0(vrid, acl_index, acl_group_id)),
    external
}
%table acl_time_range(time_range_id:int4, vrid:int4, inner_id:int4, active_status:int1)
{
    index(0(time_range_id)),
    external
}
%table acl_port_pool(
    vrid:int4, port_index:int4, pool_index:int4, port_begin:int4, port_end:int4, app_source_id:int4,
    app_serial_id:int4, app_obj_id:int8, app_version:int4)
{
    index(0(vrid, port_index, pool_index)),
    external
}
%table acl_ip_pool(
    vrid:int4, ip_index:int4, pool_index:int4, pool_ip:int4, ip_mask:int4, app_source_id:int4,
    app_serial_id:int4, app_obj_id:int8, app_version:int4)
{
    index(0(vrid, ip_index, pool_index)),
    external
}
%table sacl_inst_ext(
    app_type:int1, direction:int1, policy_type:int1, rsv:int1, app_value:int4,
    vsid:int4, group_stage:int4, acl_family:int4, acl_group:int4, if_type:int1,
    apply_vlan:int2, if_vlanId:int2, behaviorId:int4, vp_index:int4, action_type:int4,
    action_value:int4, mult_acl_group:int4)
{
    index(0(app_type, direction, policy_type, rsv, app_value, vsid, group_stage, acl_family, acl_group, if_type, apply_vlan, if_vlanId, behaviorId, vp_index, action_type, action_value, mult_acl_group)),
    external
}
%table sacl_rule_status(sacl_inst_gid: int4, acl_group: int4, acl_index: int4,
    slot: int4, statisticId: int4, status: int1)
{
    index(0(sacl_inst_gid, acl_group, acl_index, slot, statisticId)),
    external
}
%table hsec_policy_filter(
    policy_id:int4, filter_id:int4, group_id:int4)
{
    index(0(policy_id, filter_id)),
    external
}
%table hsec_applied_policy(
    policy_id:int4, applied_type:int4)
{
    index(0(policy_id)),
    external
}
%table if_eth_trunk(if_index:int4,trunk_id:int4, domain:int1)
{
    index(0(if_index)),
    external
}
namespace LswOda {
    %table PortAttrChg(unit: int1, port: int1, attr: int1, value: int4) { notify }
    %table EnablePhyPort(port: int1) { notify }
    %table PhyPort(ifIndex: int4, unit: int1, port: int1)
    PhyPort(ifIndex, unit, port) :-
        Ifm.PublishNif(ifIndex, -, -, -, unit, port, -, -, -, -, -, -, -, -, -, -, -).
    PortAttrChg(unit, port, attr, value) :-
        Ifm.PortAttrChg(ifIndex, attr, value),
        PhyPort(ifIndex, unit, port),
        state(1).
    EnablePhyPort(port) :-
        Ifm.PublishNif(-, -, -, -, -, port, -, -, -, -, -, -, -, -, -, -, -),
        Hsec.AntiAttack(-, 1, -).
}
namespace Arp {
%readonly ConvertIpAddress2Bytes,CheckSendingArp
%function ConvertIpAddress2Bytes(addr: int4 -> addr_bytes: byte4)
%function CheckSendingArp(count: int4)
}
arp(ip_address, if_index, arp_type, mac_address, fake_flag, workIfIndex, vlanid) :-
    Arp.ConfigArp(addr, if_index, arp_type, mac_address, fake_flag, vlanid, workIfIndex, -, -),
    Arp.ConvertIpAddress2Bytes(addr, ip_address).
aging_detect_message(addr, ifIndex, vlanId, workIfIndex, count) :-
    Arp.ConfigArp(addr, ifIndex, -, -, -, vlanId, workIfIndex, count, -),
    Arp.CheckSendingArp(count).
if_base(if_index, namespace_id, if_name, if_alias, if_type, if_cfg_bandwidth, if_show_type, "00") :-
    Ifm.IfName(if_index, namespace_id, if_name, if_type, if_cfg_bandwidth, if_show_type, if_alias, -).
if_phy(
    if_index, if_bw, phy_state, admin_state, onboard_state,
    tb, tp, phy_mac, eth_autoneg, eth_speed,
    eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
    unit_id, port_id, chip_unit, oda_phy_port, service_type, autoneg_cap, autoneg_adv_cap, medium,
    mru, eth_class, 0, 0, 0, 0) :-
Ifm.IfPhy(
    if_index, if_bw, phy_state, admin_state, onboard_state,
    tb, tp, chip_unit, oda_phy_port, phy_mac, eth_autoneg, eth_speed,
    eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
    unit_id, port_id, service_type, autoneg_cap, autoneg_adv_cap, medium,
    mru, -, eth_class, -, -, -) .
if_link(if_index, link_type, link_state, oper_state) :-
Ifm.IfLink(if_index, link_type, link_state, oper_state).
if_route(if_index, vrf_id, ipv4_state, mtu_v4, df_flag, ipv6_state, mtu_v6) :-
Ifm.IfNet(if_index, vrf_id, df_flag, mtu_v4, ipv4_state),
Ifm.IfNet6(if_index, -, mtu_v6, ipv6_state).
if_eth_trunk_member(member_if_index, trunk_id) :- Ifm.EthTrunkMembers(member_if_index, trunk_id, -, -, -).
acl_group(aclGroupId, isIPv6, 0, vsysid, aclNumber, aclGroupType, aclName) :-
    Acl.GroupCfg(aclGroupId, aclGroupType1, aclNumber, aclName, -, isIPv6, -, vsysid),
    I1ToI4(aclGroupType1, aclGroupType).
acl_base(vrid, acl_index, acl_group_id, vrf_index, acl_priority, cond_mask,
        0, time_range_status, actionType, anyFlag, fragType, srcIpAddr,
        srcIpMask, logFlag) :-
    Acl.AdvRule(acl_group_id, acl_index, actionType, cond_mask,
                srcIpAddr, srcIpMask, -, -, -, -,
                -, -, -, -, -, -, -, -,
                anyFlag, -, fragType, -, -,
                -, -, -, -, -,
                -, -, -, -,
                -, -, -, -, -,
                logFlag, vrf_index, time_range_status, 0, -,
                -, -, -, -, -,
                -, -, -, 0, vrid, acl_priority).
acl_base(vrid, acl_index, acl_group_id, vrf_index, acl_priority, cond_mask,
        time_range_id, time_range_status, actionType, anyFlag, fragType, srcIpAddr,
        srcIpMask, logFlag) :-
    Acl.AdvRule(acl_group_id, acl_index, actionType, cond_mask,
                srcIpAddr, srcIpMask, -, -, -, -,
                -, -, -, -, -, -, -, -,
                anyFlag, -, fragType, -, -,
                -, -, -, -, -,
                -, -, -, -,
                -, -, -, -, -,
                logFlag, vrf_index, -, time_range_id, -,
                -, -, -, -, -,
                -, -, -, 0, vrid, acl_priority),
    Acl.TimeRangeCfg(time_range_id, -, -, time_range_status).
acl_adv(aclVrId, aclIndex, aclGroupId, aclVpnIndex, aclPriority, aclCondMask,
        0, time_range_status, actionType, srcIpAddr, srcIpMask, dstIpAddr,
        dstIpMask, srcIpPool, dstIpPool, srcPortBeg, srcPortEnd, dstPortBeg,
        dstPortEnd, srcPortPool, dstPortPool, logFlag, anyFlag, protocol,
        fragType, tos, tcpFlag, icmpType, icmpCode, dscp,
        ipPre, igmpType, 0, 0, tcpFlagMask, tcpEstablished,
        ttlOp, ttlBgn, ttlEnd) :-
    Acl.AdvRule(aclGroupId, aclIndex, actionType, aclCondMask,
                srcIpAddr, srcIpMask, srcIpPool, dstIpAddr, dstIpMask, dstIpPool,
                srcPortBeg, srcPortEnd, -, srcPortPool, dstPortBeg, dstPortEnd, -, dstPortPool,
                anyFlag, protocol, fragType, tos, tcpFlag,
                icmpType, icmpCode, dscp, igmpType, ipPre,
                -, -, -, -,
                -, -, -, -, -,
                logFlag, aclVpnIndex, time_range_status, 0, -,
                -, -, tcpEstablished, tcpFlagMask, ttlOp,
                ttlBgn, ttlEnd, -, 1, aclVrId, aclPriority).
acl_adv(aclVrId, aclIndex, aclGroupId, aclVpnIndex, aclPriority, aclCondMask,
        time_range_id, time_range_status, actionType, srcIpAddr, srcIpMask, dstIpAddr,
        dstIpMask, srcIpPool, dstIpPool, srcPortBeg, srcPortEnd, dstPortBeg,
        dstPortEnd, srcPortPool, dstPortPool, logFlag, anyFlag, protocol,
        fragType, tos, tcpFlag, icmpType, icmpCode, dscp,
        ipPre, igmpType, 0, 0, tcpFlagMask, tcpEstablished,
        ttlOp, ttlBgn, ttlEnd) :-
    Acl.AdvRule(aclGroupId, aclIndex, actionType, aclCondMask,
                srcIpAddr, srcIpMask, srcIpPool, dstIpAddr, dstIpMask, dstIpPool,
                srcPortBeg, srcPortEnd, -, srcPortPool, dstPortBeg, dstPortEnd, -, dstPortPool,
                anyFlag, protocol, fragType, tos, tcpFlag,
                icmpType, icmpCode, dscp, igmpType, ipPre,
                -, -, -, -,
                -, -, -, -, -,
                logFlag, aclVpnIndex, -, time_range_id, -,
                -, -, tcpEstablished, tcpFlagMask, ttlOp,
                ttlBgn, ttlEnd, -, 1, aclVrId, aclPriority),
    Acl.TimeRangeCfg(time_range_id, -, -, time_range_status).
acl_eth(vrid, acl_index, acl_group_id,
        acl_action_type, frame_type, frame_mask,
        src_mac, src_mac_mask, dest_mac, dest_mac_mask,
        vlan_id, vlan_id_mask, value_8021p,
        cond_mask, time_range_status, 0, acl_priority) :-
    Acl.EthRule(acl_group_id, acl_index, acl_action_type, cond_mask,
                frame_type, frame_mask, src_mac, src_mac_mask, dest_mac, dest_mac_mask,
                vlan_id, vlan_id_mask, value_8021p,
                vrid, time_range_status, 0, acl_priority).
acl_eth(vrid, acl_index, acl_group_id,
        acl_action_type, frame_type, frame_mask,
        src_mac, src_mac_mask, dest_mac, dest_mac_mask,
        vlan_id, vlan_id_mask, value_8021p,
        cond_mask, time_range_status, time_range_id, acl_priority) :-
    Acl.EthRule(acl_group_id, acl_index, acl_action_type, cond_mask,
                frame_type, frame_mask, src_mac, src_mac_mask, dest_mac, dest_mac_mask,
                vlan_id, vlan_id_mask, value_8021p,
                vrid, time_range_status, time_range_id, acl_priority),
    Acl.TimeRangeCfg(time_range_id, -, -, time_range_status).
acl_time_range(trngId, 0, 0, trngStatus) :-
    Acl.TimeRangeCfg(trngId, -, -, trngStatus).
acl_port_pool(vrId, portId, poolId, startPort, endPort, 0, 0, 0, 0) :-
    Acl.PortPoolCfg(poolId, portId, startPort2, endPort2, -, vrId),
    I2ToI4(startPort2, startPort),
    I2ToI4(endPort2, endPort).
acl_ip_pool(vrId, ipId, poolId, ipAddr, ipMask, 0, 0, 0, 0) :-
    Acl.IpPoolCfg(poolId, ipId, ipAddr, ipMask, vrId).
hsec_policy_filter(policy_id, filter_id, group_id) :-
    Hsec.PolicyFilterCfg(policy_id, filter_id, group_id),
    Hsec.AppliedPolicyCfg(policy_id, -).
hsec_applied_policy(policy_id, applied_type) :-
   Hsec.AppliedPolicyCfg(policy_id, applied_type).
if_eth_trunk(trunkIndex, trunkId, domain):-
    Ifm.EthTrunk(trunkIndex, -, trunkId, domain).
namespace Dhcpsnp {
    UserBindNotify(macAddr, ipv4Addr, vlanId, ifIndex, 0, 0, 0, ipv6Addr, isPrefix, prefixLen, protoType) :-
        Dhcpsnp.StaticUserBind(macAddr, ipv4Addr, vlanId, ifIndex, -, ipv6Addr, isPrefix, prefixLen, protoType).
    UserBindNotify(macAddr, ipv4Addr, vlanId, ifIndex, serverIp, lifeTime, 1, ipv6Addr, isPrefix, prefixLen, protoType) :-
        Dhcpsnp.DynamicUserBind(macAddr, ipv4Addr, vlanId, ifIndex, -, serverIp, lifeTime, ipv6Addr, isPrefix, prefixLen, protoType, -).
}
namespace Hpf {
    %readonly hpf_if_index
    %readonly ctrlif_map_fwdif
    %table hpf_if_index(fwdIfIdx: int4, ctrlIfIdx: int4, flowIfIdx: int4) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_name(fwdIfIdx: int4, namespace_id: int4, if_name: byte64,
        hpf_if_type: int4, if_show_type: int2, if_alias: byte64, description: byte64, virt_if:int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_phy(fwdIfIdx: int4, if_bw: int8, phy_state: int1, admin_state: int1, onboard_state: int1,
        tb: int4, tp: int4, chip_unit: int1, oda_phy_port: int1, phy_mac: byte6, eth_autoneg: int1, eth_speed: int8,
        eth_duplex: int1, eth_combo: int1, eth_loopback: int1, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
        unit_id: int4, port_id: int4, service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1,
        mru: int2, link_type: int1, eth_class: int1, cpu_type: int1, drvPortType: int1, drvSubType: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_link(fwdIfIdx: int4, link_type: int4, link_state: int1, oper_state: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_net(fwdIfIdx: int4, vrf_id: int4, df_flag: int4, mtu_v4: int4, ipv4_state: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_net6(fwdIfIdx: int4, vrf_id: int4, mtu_v6: int4, ipv6_state: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table CAP_TABLE_CTRLIF_MAP_FWDIF(ctrlIfIdx: int4, fwdIfIdx: int4) {
        index(0(ctrlIfIdx)),
        tbm
    }
    %table CAP_TABLE_FLOWIF_MAP_FWDIF(flowIfIdx: int4, fwdIfIdx: int4) {
        index(0(flowIfIdx)),
        tbm
    }
    %table CAP_TABLE_PORT_MAP_TAGTYPE(portId: int4, portEncType: int1) {
        index(0(portId)),
        tbm
    }
    %table hpf_if_trunk_member(memIfIndex: int4, trunkId: int4, trunkIndex: int4, weight: int2)
    %table hpf_if_trunk_member_array(trunkId: int4, trunkFwdIndex: int4, memIfIndexesLen: int1, memIfIndexes: byte256)
    %table hpf_if_trunk_members(trunkId: int4, trunkFwdIndex: int4, memIfIndexesLen: int1, memIfIndexes: byte256) {
        index(0(trunkId, trunkFwdIndex)),
        tbm
    }
    %table hpf_if_eth_trunk(trunkId: int4, trunkIndex: int4) {
        index(0(trunkId, trunkIndex)),
        tbm
    }
    %table hpf_if_fwdif_trunk_attribute(fwdIfIdx: int4, trunkId: int4, isTrunkMember:int1) {
        index(0(fwdIfIdx, trunkId, isTrunkMember)),
        tbm
    }
    %table hpf_if_fwdif_father(fwdIfIdx: int4, fatherifIndex: int4) {
        index(0(fwdIfIdx, fatherifIndex)),
        tbm
    }
    %table hpf_if_trunk_load_balance_mode(hashType: int1, hashField: int4) {index(0(hashType)), tbm}
    %table hpf_if_host(fwdIfIdx: int4) {
        index(0(fwdIfIdx)),
        tbm
    }
    %function GetFlowIfIndex(if_name: byte64, hpfIfType: int4, tb: int4, tp: int4 -> flowIfIdx: int4)
    %function GetFlowIfIndexForTrunk(vapIndex: int4, trunkId: int4 -> flowIfIdx: int4)
    %function GetHpfIfType(ifType: int2 -> hpfIfType: int4)
    %function GetHpfIfVirtFlag(ifType: int2 -> virtIf: int1)
    %aggregate EthTrunkMemberArrayAgg(memFwdIfIndex: int4, weight: int2 -> memIfIndexesLen: int1, memIfIndexes: byte256)
    {
        ordered
    }
    %resource ctrlif_map_fwdif(ctrlIfIdx: int4 -> fwdIfIdx: int4) { sequential(max_size(1024)) }
    ctrlif_map_fwdif(ctrlIfIdx, -) :- Ifm.IfName(ctrlIfIdx, -, -, -, -, -, -, -).
    %table ctrlif_map_fwdif_copy(ctrlIfIdx: int4, fwdIfIdx: int4)
    ctrlif_map_fwdif_copy(ctrlIfIdx, fwdIfIdx) :- ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    hpf_if_index(fwdIfIdx, ctrlIfIdx, flowIfIdx) :-
        Ifm.IfPhy(ctrlIfIdx, -, -, -, -, tb, tp, -, -, -,
            -, -, -, -, -, -, -, -, -, -,
            -, -, -, -, -, -, -, -, -, -, -),
        Ifm.IfName(ctrlIfIdx, -, ifName, ifType, -, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        GetHpfIfType(ifType, hpfIfType),
        NotEqual4(hpfIfType, 24),
        NotEqual4(hpfIfType, 6),
        NotEqual4(hpfIfType, 23),
        GetFlowIfIndex(ifName, hpfIfType, tb, tp, flowIfIdx),
        NotEqual4(flowIfIdx, -1).
    hpf_if_index(fwdIfIdx, ifIndex, flowIfIdx) :-
        Ifm.EthTrunk(ifIndex, -, trunkId, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        GetFlowIfIndexForTrunk(ifIndex, trunkId, flowIfIdx).
    hpf_if_name(fwdIfIdx, namespace_id, if_name, hpf_if_type, if_show_type, if_alias, description, virt_if) :-
        Ifm.IfName(if_index, namespace_id, if_name,
            if_type, -, if_show_type, if_alias, description),
        GetHpfIfType(if_type, hpf_if_type),
        GetHpfIfVirtFlag(if_type, virt_if),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_phy(fwdIfIdx, if_bw, phy_state, admin_state, onboard_state,
            tb, tp, chip_unit, oda_phy_port, phy_mac, eth_autoneg, eth_speed,
            eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
            unit_id, port_id, service_type, autoneg_cap, autoneg_adv_cap, medium,
            mru, link_type, eth_class, cpu_type, drvPortType, drvSubType) :-
        Ifm.IfPhy(if_index, if_bw, phy_state, admin_state, onboard_state,
            tb, tp, chip_unit, oda_phy_port, phy_mac, eth_autoneg, eth_speed,
            eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
            unit_id, port_id, service_type, autoneg_cap, autoneg_adv_cap, medium,
            mru, link_type, eth_class, cpu_type, drvPortType, drvSubType),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_link(fwdIfIdx, link_type, link_state, oper_state) :-
        Ifm.IfLink(if_index, link_type, link_state, oper_state),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    %rule r2 hpf_if_net(fwdIfIdx, vrf_id, df_flag, mtu_v4, ipv4_state) :-
        Ifm.IfNet(if_index, vrf_id, df_flag, mtu_v4, ipv4_state),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_net6(fwdIfIdx, vrf_id, mtu_v6, ipv6_state) :-
        Ifm.IfNet6(if_index, vrf_id, mtu_v6, ipv6_state),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    CAP_TABLE_CTRLIF_MAP_FWDIF(ctrlIfIdx, fwdIfIdx) :-
        Ifm.IfName(ctrlIfIdx, -, -, -, -, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_FLOWIF_MAP_FWDIF(flowIfIdx, fwdIfIdx) :-
        Ifm.IfPhy(ctrlIfIdx, -, -, -, -,
            tb, tp, -, -, -, -, -,
            -, -, -, -, -, -, -,
            -, -, -, -, -, -,
            -, -, -, -, -, -),
        Ifm.IfName(ctrlIfIdx, -, if_name, ifType, -, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        GetHpfIfType(ifType, hpfIfType),
        NotEqual4(hpfIfType, 24),
        NotEqual4(hpfIfType, 6),
        GetFlowIfIndex(if_name, hpfIfType, tb, tp, flowIfIdx),
        NotEqual4(flowIfIdx, -1).
    CAP_TABLE_FLOWIF_MAP_FWDIF(flowIfIdx, fwdIfIdx) :-
        Ifm.EthTrunk(ifIndex, -, trunkId, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        GetFlowIfIndexForTrunk(ifIndex, trunkId, flowIfIdx).
    CAP_TABLE_PORT_MAP_TAGTYPE(tp, portEncType) :-
        Ifm.IfPhy(ctrlIfIdx, -, -, -, -,
            -, tp, -, -, -, -, -,
            -, -, -, -, -, -, -,
            -, -, -, -, -, -,
            -, -, -, portEncType, -, -),
        Ifm.IfName(ctrlIfIdx, -, -, ifType, -, -, -, -),
        GetHpfIfType(ifType, hpfIfType),
        CmpInt4(hpfIfType, 0, 2).
    ctrlif_map_fwdif(251658240, -) :- tbl_init(-).
    hpf_if_trunk_members(trunkId, trunkFwdIndex, memIfIndexesLen, memIfIndexes):-
        hpf_if_trunk_member_array(trunkId, trunkFwdIndex, memIfIndexesLen, memIfIndexes).
    hpf_if_trunk_member_array(trunkId, trunkFwdIndex, memIfIndexesLen, memIfIndexes):-
        hpf_if_trunk_member(memFwdIfIndex, trunkId, trunkFwdIndex, weight)
        GROUP-BY (trunkId, trunkFwdIndex) EthTrunkMemberArrayAgg(memFwdIfIndex, weight, memIfIndexesLen, memIfIndexes).
    hpf_if_trunk_member(memFwdIfIndex, trunkId, trunkFwdIndex, weight):-
        Ifm.EthTrunkMembers(memIfIndex, trunkId, -, trunkIndex, weight),
        ctrlif_map_fwdif(memIfIndex, memFwdIfIndex),
        ctrlif_map_fwdif_copy(trunkIndex, trunkFwdIndex),
        Ifm.Agg_Attributes(-, memIfIndex, -, 1, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).
    hpf_if_eth_trunk(trunkId, trunkFwdIndex):-
        ctrlif_map_fwdif(trunkIndex, trunkFwdIndex),
        Ifm.EthTrunk(trunkIndex, -, trunkId, -).
    hpf_if_fwdif_trunk_attribute(fwdIfIndex, trunkId, 1):-
        ctrlif_map_fwdif(ifIndex, fwdIfIndex),
        Ifm.EthTrunkMembers(ifIndex, trunkId, -, -, -).
    hpf_if_fwdif_trunk_attribute(fwdIfIndex, trunkId, 0):-
        ctrlif_map_fwdif(ifIndex, fwdIfIndex),
        Ifm.EthTrunk(ifIndex, -, trunkId, -).
    hpf_if_fwdif_father(memFwdIfIndex, fatherFwdifIndex):-
        Ifm.EthTrunkMembers(memIfIndex, -, -, fatherifIndex, -),
        ctrlif_map_fwdif(memIfIndex, memFwdIfIndex),
        ctrlif_map_fwdif_copy(fatherifIndex, fatherFwdifIndex).
    hpf_if_trunk_load_balance_mode(hashType, hashField) :-
        Ifm.EthTrunkLoadBalanceMode(hashType, hashField).
    hpf_if_host(fwdIfIdx) :-
        Ifm.IfName(ctrlIfIdx, -, -, 1, -, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    %precedence hpf_if_eth_trunk, hpf_if_trunk_members
    %precedence hpf_if_name, hpf_if_host
}
namespace Hpf {
    %table hpf_if_protocol(fwdIfIdx: int4, proto: int1, enable: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_pppoe(fwdIfIdx: int4, code: int1, ver: int1, type: int1, sessionId: int2, dmac: byte6, ori_link_type: int4) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_dialer(dialerIfIndex: int4, VlanIfIndex: int4) {
        index(0(dialerIfIndex)),
        tbm
    }
    hpf_if_protocol(fwdIfIdx, proto, enable) :-
        Ifm.ConfigProtocolEnable(ifIndex, proto, enable),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_pppoe(fwdIfIdx, code, ver, type, sessionId, dmac, ori_link_type) :-
        Ifm.Pppoe(ifIndex, code, ver, type, sessionId, dmac),
        Ifm.IfLink(ifIndex, ori_link_type, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_dialer(dialerIfIndex, vlanIfIndex) :-
        Ifm.ConfigDialerBundle(dialerIndex, vlanIndex),
        ctrlif_map_fwdif(dialerIndex, dialerIfIndex),
        ctrlif_map_fwdif_copy(vlanIndex, vlanIfIndex).
}
namespace Hpf {
    %table hpf_if_vap(fwdIfIdx: int4, adminState: int1, vapType: int1, vapIndex: int1, linkedId: int1, mac: byte6,
        userIdentify: int1, userService: int1, userAuth: int1, authType: int1, isEapolKeyToCp: int1, protocolTunnelFwd: int1,
        tp: int4, cpu_type: int1, drvPortType: int1, drvSubType: int1, globalIdx: int2, vapFlag:int1, broadPeerId:int4, broadPfeVapIdx: int4, broadSessionInfo: int2, multiMediaEn: int1, tcpWndTurn: int1, arpTest: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_capwap(fwdIfIdx: int4, tunnelId: int4) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_ip_learn(fwdIfIdx: int4, ipLearnEnable: int1, dhcpStrict: int1, addBlackList: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_ipv6_learn(fwdIfIdx: int4, ipv6LearnEnable: int1, dhcpv6Strict: int1, dhcpv6Slaac: int1, ipv6AddBlackList: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table CAP_TABLE_USRID_MAP_FWDIF(usrId: int4, fwdIfIdx: int4) {
        index(0(usrId)),
        tbm
    }
    %table CAP_TABLE_LINK_UP_DOWN_MODE(fwdIfIdx: int4, mode: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_wmp_portal(fwdIfIdx: int4, apPortalEn: int1, webProxyPortalParse: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_vap_freerule(fwdIfIdx: int4, ipv4GroupId: int4, ipv6GroupId: int4, staticGroupId: int4) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_wmp_porxy(fwdIfIdx: int4, port: int2, url: byte200) {
        index(0(fwdIfIdx, port, url)),
        tbm
    }
    %table CAP_TABLE_VAP_LOCAL_MNG(fwdIfIdx: int4, localManageEn:int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_l2_if_tcpmss_adjust(fwdIfIdx: int4, enable: int1, tcpMss: int2) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_l3_if_tcpmss_adjust(fwdIfIdx: int4, enable: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_if_isolate_mode(fwdIfIdx: int4, mode: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %function GetFlowIfIndexForVap(vapIndex: int1, linkedId: int1 -> flowIfIdx: int4)
    %function GetFlowIfIndexForCapwap(tunnelId: int4 -> flowIfIdx: int4)
    hpf_if_index(fwdIfIdx, ifIndex, flowIfIdx) :-
        Ifm.ConfigVapIf(-, ifIndex, -, -, -, vapIndex, linkedId, -,
            -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        GetFlowIfIndexForVap(vapIndex, linkedId, flowIfIdx).
    hpf_if_index(fwdIfIdx, ifIndex, flowIfIdx) :-
        Wmp.ConfigCapwapTunnel(sip, dip, sport, dport, -, ifIndex, -,
            -, -, -,
            -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        capwap_tunnel_id_pool(sip, dip, sport, dport, tunnelId),
        GetFlowIfIndexForCapwap(tunnelId, flowIfIdx).
    hpf_if_vap(fwdIfIdx, adminState, vapType, vapIndex, linkedId, mac,
        userIdentify, userService, userAuth, authType, isEapolKeyToCp, protocolTunnelFwd,
        tp, cpu_type, drvPortType, drvSubType, globalIdx, flag, broadPeerId, broadPfeVapIdx, broadSessionInfo,
        multiMediaEn, tcpWndTurn, arpTest) :-
        Ifm.ConfigVapIf(-, ifIndex, radioName, -, vapType, vapIndex, linkedId, mac,
            userIdentify, userService, userAuth, authType, isEapolKeyToCp, protocolTunnelFwd,
            -, globalIdx, flag, broadPeerId, broadPfeVapIdx, broadSessionInfo, -, multiMediaEn, tcpWndTurn, arpTest),
        Ifm.PublishRadioName(radioName, -, tp, -, -, cpu_type, drvPortType, drvSubType),
        Ifm.IfPhy(ifIndex, -, -, adminState, -,
            -, -, -, -, -, -, -,
            -, -, -, -, -, -, -,
            -, -, -, -, -, -,
            -, -, -, -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_capwap(fwdIfIdx, tunnelId) :-
        Wmp.ConfigCapwapTunnel(sip, dip, sport, dport, -, ifIndex, -,
            -, -, -,
            -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        capwap_tunnel_id_pool(sip, dip, sport, dport, tunnelId).
    hpf_if_ip_learn(fwdIfIdx, ipLearnEnable, dhcpStrict, addBlackList) :-
        Ifm.ConfigIfIpLearn(ifIndex, ipLearnEnable, dhcpStrict, addBlackList),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_ipv6_learn(fwdIfIdx, ipv6LearnEnable, dhcpv6Strict, dhcpv6Slaac, ipv6AddBlackList) :-
        Ifm.ConfigIfIpv6Learn(ifIndex, ipv6LearnEnable, dhcpv6Strict, dhcpv6Slaac, ipv6AddBlackList),
         ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_wmp_portal(fwdIfIdx, apPortalEn, webProxyPortalParse) :-
        Ifm.ConfigIfWmpPortal(ifIndex, apPortalEn, -),
        Ifm.ConfigIfWmpWebProxy(ifIndex, webProxyPortalParse, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_vap_freerule(fwdIfIdx, d4, 0, ds) :-
        Ifm.VapAclGroupIds(ifIndex, v4Gid, -, staticGid),
        Acl.GetHpfGroupId(0, v4Gid, d4),
        Acl.GetHpfGroupId(0, staticGid, ds),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    Acl.GroupInUseOne(hpfGid, cfgGid, cfgType) :-
        Ifm.VapAclGroup(cfgGid, -),
        Acl.GroupCfg(cfgGid, cfgType, -, -, -, -, -, -),
        Acl.GetHpfGroupId(0, cfgGid, hpfGid).
    hpf_if_wmp_porxy(fwdIfIdx, port, url) :-
        Ifm.ConfigIfWmpWebProxy(ifIndex, -, port, url),
        NotEqual2(port, 0),
        NotZerobyte200(url),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_l2_if_tcpmss_adjust(fwdIfIdx, 1, 1380) :-
        BR.Port(ctrlIfIdx, -, -, -, -, -, -),
        Ifm.ConfigTcpMssAdjust(ctrlIfIdx, -1),
        Wmp.ConfigWiredTunnel(ctrlIfIdx, 1, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    hpf_l2_if_tcpmss_adjust(fwdIfIdx, 1, 1380) :-
        BR.Port(ctrlIfIdx, -, -, -, -, -, -),
        Ifm.ConfigTcpMssAdjust(ctrlIfIdx, -1),
        Ifm.ConfigVapIf(-, ctrlIfIdx, -, -, 1, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    hpf_l2_if_tcpmss_adjust(fwdIfIdx, 0, 0) :-
        BR.Port(ctrlIfIdx, -, -, -, -, -, -),
        Ifm.ConfigTcpMssAdjust(ctrlIfIdx, -1),
        Wmp.ConfigWiredTunnel(ctrlIfIdx, 0, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    hpf_l2_if_tcpmss_adjust(fwdIfIdx, 0, 0) :-
        BR.Port(ctrlIfIdx, -, -, -, -, -, -),
        Ifm.ConfigTcpMssAdjust(ctrlIfIdx, -1),
        Ifm.ConfigVapIf(-, ctrlIfIdx, -, -, 0, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    hpf_l2_if_tcpmss_adjust(fwdIfIdx, 1, tcpMss) :-
        BR.Port(ctrlIfIdx, -, -, -, -, -, -),
        Ifm.ConfigTcpMssAdjust(ctrlIfIdx, tcpMss),
        NotEqual2(tcpMss, -1),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    hpf_l3_if_tcpmss_adjust(fwdIfIdx, 1) :-
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        Ifm.IfL3(ctrlIfIdx).
    CAP_TABLE_FLOWIF_MAP_FWDIF(flowIfIdx, fwdIfIdx) :-
        Ifm.ConfigVapIf(-, ifIndex, -, -, -, vapIndex, linkedId, -,
            -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        GetFlowIfIndexForVap(vapIndex, linkedId, flowIfIdx).
    CAP_TABLE_USRID_MAP_FWDIF(usrId, fwdIfIdx) :-
        Usf.PublicSta(-, -, vapIfIndex, -, -, usrId, -, -, -, -, -, -, -),
        ctrlif_map_fwdif(vapIfIndex, fwdIfIdx).
    CAP_TABLE_PORT_MAP_TAGTYPE(tp, portEncType) :-
        Ifm.PublishRadioName(-, -, tp, -, -, portEncType, -, -).
    CAP_TABLE_LINK_UP_DOWN_MODE(fwdIfIdx, mode) :-
        Ifm.ConfigLinkUpDownMode(ctrlIfIdx, mode),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_VAP_LOCAL_MNG(fwdIfIdx, localManageEn) :-
        Ifm.ConfigVapIf(-, ifIndex, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        Usf.PublicUsfConfig(-, -, -, -, -, -, localManageEn, -, -, -, -, -, -, -, -, -, -).
    hpf_if_isolate_mode(fwdIfIdx, mode) :-
        Ifm.ConfigIsolateMode(ctrlIfIdx, mode),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    %precedence hpf_if_index, CAP_TABLE_LINK_UP_DOWN_MODE
    %precedence hpf_if_vap, CAP_TABLE_VAP_LOCAL_MNG
    %precedence hpf_l2_if_tcpmss_adjust, hpf_l3_if_tcpmss_adjust
    %precedence hpf_if_name, hpf_if_isolate_mode
    %precedence hpf_l3_if_tcpmss_adjust, hpf_fwdif_ing_l1svc
    %precedence hpf_if_phy, hpf_if_vap
}
namespace Hpf {
    %readonly CAP_TABLE_IPV4_NHP, DecideMulNhp
    %table CAP_TABLE_IPV4_FIB(prefix: int4, vpn: int4, dip: byte4, opcode: int2, mulNhp: int1, dirRoute: int1, defRoute: int1, nhpIdx: int4) {
        index(0(prefix, vpn, dip)),
        tbm
    }
    %table CAP_TABLE_IPV4_NHP(nhpIdx: int4, ctrlIfIdx: int4, fwdIfIdx: int4, nextHop: int4) {
        index(0(nhpIdx)),
        tbm
    }
    %table CAP_TABLE_IPV4_NHP_GROUP_ID(nhpGroupId: int4, nhpGroupHpfIndex: int4)
    %resource hpf_allocated_nhp_group_index(nhpGroupId: int4 -> nhpGroupHpfIndex: int4) { sequential(max_size(10000)) }
    %table CAP_TABLE_IPV4_NHP_GROUP(nhpGrpIdx: int4, nhpNum: int1, nhpIndexes: byte128, primaryNphIndexes: int4) {
        index(0(nhpGrpIdx)),
        tbm
    }
    %table CAP_TABLE_GLOBAL_LOADBALANCE(vr: int4, mode: int1, srcIp: int1, dstIp: int1, srcPort: int1, dstPort: int1)
    {
        index(0(vr)),
        tbm
    }
    %table CAP_TABLE_IPV4_ARP(vpn: int4, nextHop: int4, fake: int1, ctrlIfIdx: int4, l3CtrlIfIdx: int4, fwdIfIdx: int4, l3FwdIfIdx: int4, dmac: byte6, vlanId: int2) {
        index(0(vpn, nextHop)),
        tbm
    }
    %table CAP_TABLE_REPLY_ARP(brId: int4, vlanId: int2, arpReplyEn: int1) {
        index(0(brId, vlanId)),
        tbm
    }
    %table CAP_TABLE_GLOBAL_ARP(index: int1, arpGatewayDupEn: int1) {
        index(0(index)),
        tbm
    }
    %table NOTIFY_TABLE_IPV4_ARP(ip: int4, fake: int1) {
        tbm,
        index(0(ip))
    }
    %table CAP_TABLE_ARP_GW_DUP_BLOCK(ifIndex: int4, brId: int4, srcMac: byte6, vlanId: int2, agingTime: int8)
    {
        index(0(ifIndex, brId, srcMac, vlanId)),
        tbm
    }
    %table CAP_TABLE_IPV4_PORTIP(ctrlIfIdx: int4, fwdIfIdx: int4, index: int4) {
        index(0(ctrlIfIdx)),
        tbm
    }
    %table CAP_TABLE_IPV4_PORTIP_COUNT(ctrlIfIdx: int4, fwdIfIdx: int4, index: int4)
    %table CAP_TABLE_IPV4_PORTIPLIST(index: int4, vrf_index: int4, address: int4, mask_len: int2, type: int2) {
        index(0(index)),
        tbm
    }
    %table NhpAttribute(nhpGroupId: int4, mulNhp: int1, outNhpHpfIndex: int4)
    %resource portip_index_pool(ctrlIfIdx: int4 -> index: int4) { sequential(max_size(512)) }
    portip_index_pool(ctrlIfIdx, -) :- Ifm.ConfigIfIpv4Addr(ctrlIfIdx, -, -, -, -).
    hpf_allocated_nhp_group_index(nhpGroupId, -) :-
        Fib.NhpGroupAggregate(nhpGroupId, -, nhpNum, -, -),
        Fib.IsMulNhp(nhpNum).
    CAP_TABLE_IPV4_NHP_GROUP_ID(nhpGroupId, nhpGroupHpfIndex) :- hpf_allocated_nhp_group_index(nhpGroupId, nhpGroupHpfIndex).
    CAP_TABLE_IPV4_NHP_GROUP_ID(nhpGroupId, -1) :- Fib.NhpGroupAggregate(nhpGroupId, -, 1, -, -).
    %function DecideMulNhp(nhpNum: int1, nhpHpfIndex: int4, nhpGroupHpfIndex:int4 -> mul_nhp: int1, outNhpHpfIndex: int4)
    NhpAttribute(nhpGroupId, mulNhp, outNhpHpfIndex) :-
        Fib.NhpGroupAggregate(nhpGroupId, firstNhpHpfIndex, nhpNum, -, -),
        CAP_TABLE_IPV4_NHP_GROUP_ID(nhpGroupId, nhpGroupHpfIndex),
        DecideMulNhp(nhpNum, firstNhpHpfIndex, nhpGroupHpfIndex, mulNhp, outNhpHpfIndex).
    CAP_TABLE_IPV4_FIB(maskLen, vrfId, dip, routeFlags, mulNhp, dir_route, def_route, outNhpHpfIndex) :-
        Fib.ConfigIpv4Fwd(-, vrfId, dstIp, maskLen, -, routeAttr, routeFlags, -, nhpGroupId, -, -, -),
        NhpAttribute(nhpGroupId, mulNhp, outNhpHpfIndex),
        Fib.Ipv4Func(routeAttr, dstIp, dir_route, def_route, dip).
    CAP_TABLE_IPV4_NHP(nhpHpfIndex, ctrlIfIdx, fwdIfIdx, nextHop) :-
        Fib.get_fwd_nhp_index(-, -, nextHop, ctrlIfIdx, -, -, nhpHpfIndex),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_IPV4_NHP_GROUP(nhpGroupHpfIndex, nhpNum, nhpIndexes, primaryNhpIndexes) :-
        Fib.ConfigNhpGroup(-, nhpGroupId, -, -),
        Fib.NhpGroupAggregate(nhpGroupId, -, nhpNum, nhpIndexes, primaryNhpIndexes),
        hpf_allocated_nhp_group_index(nhpGroupId, nhpGroupHpfIndex).
    CAP_TABLE_GLOBAL_LOADBALANCE(vr, mode, srcIp, dstIp, srcPort, dstPort) :-
        Fib.ConfigLoadBalance(vr, mode, srcIp, dstIp, srcPort, dstPort).
    %precedence CAP_TABLE_IPV4_ARP, NOTIFY_TABLE_IPV4_ARP
    %precedence CAP_TABLE_IPV4_NHP, CAP_TABLE_IPV4_NHP_GROUP, CAP_TABLE_IPV4_FIB
    CAP_TABLE_IPV4_ARP(-1, -1, 1, -1, -1, -1, -1, "0x00", 0) :- tbl_init(-1).
    NOTIFY_TABLE_IPV4_ARP(0, 1) :- tbl_init(-1).
    CAP_TABLE_REPLY_ARP(brId, vlanId, arpReplyEn) :-
        Arp.VlanArpReplay(0, brId, vlanId, arpReplyEn).
    CAP_TABLE_GLOBAL_ARP(index, arpGatewayDupEn) :-
        Arp.ArpCfg(index, arpGatewayDupEn).
    CAP_TABLE_ARP_GW_DUP_BLOCK(fwdIfIdx, brId, srcMac, vlanId, agingTime) :-
        Arp.ArpGatewayDupBlock(ifIndex, brId, srcMac, vlanId, agingTime),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    CAP_TABLE_IPV4_PORTIP_COUNT(ctrlIfIdx, fwdIfIdx, index) :-
        Ifm.ConfigIfIpv4Addr(ctrlIfIdx, -, -, -, -),
        portip_index_pool(ctrlIfIdx, index),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_IPV4_PORTIP(ctrlIfIdx, fwdIfIdx, index) :-
        CAP_TABLE_IPV4_PORTIP_COUNT(ctrlIfIdx, fwdIfIdx, index).
    CAP_TABLE_IPV4_PORTIPLIST(index, vrf_index, address, mask_len, type) :-
        Ifm.ConfigIfIpv4Addr(ctrlIfIdx, vrf_index, address, mask_len, type),
        portip_index_pool(ctrlIfIdx, index).
}
namespace Hpf {
    %table hpf_fwdif_ing_l1svc(fwdif_index: int4, temp: int4) {
        index(0(fwdif_index, temp)), tbm
    }
    hpf_fwdif_ing_l1svc(l3FwdifIdx, temp):-
        Nat.nat_interface_enable(l3if_index, enable),
        ctrlif_map_fwdif(l3if_index, l3FwdifIdx),
        I1ToI4(enable, temp).
    hpf_fwdif_ing_l1svc(fwdIfIdx, 0):-
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        Ifm.IfL3(ctrlIfIdx).
    %function hpf_fwdif_port_attr(brId: int4, pvid: int2 -> temp: int4)
    hpf_fwdif_ing_l1svc(fwdIfIdx, temp):-
        BR.Port(ctrlIfIdx, -, -, brId, pvid, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        hpf_fwdif_port_attr(brId, pvid, temp).
    %precedence hpf_if_name, hpf_fwdif_ing_l1svc
    %precedence CAP_TABLE_FWDIF_PORT_ATTR, hpf_fwdif_ing_l1svc
}
null(0) :- Fib.IpRoute4ForArp(-, -, -, -, -, -, -, -, -, -).
namespace Pcp {
%readwrite vNhpChangedByOthers
%table vNhpChangedByOthers(if_index: int4) { transient(finish), variant }
%table hpf_nhp_ext_update(hpf_nhp_index: int4, change_type: int1, nhp_index: int4,
    next_hop: int4, tag_type: int1, if_type: int2,
    l1if_ctrl_index: int4, l3if_ctrl_index: int4, l1if_index: int4, l3if_index: int4,
    arp_encoded: byte32, l1if_encoded: byte32, l3if_encoded: byte32)
    { index(0(hpf_nhp_index, change_type)), tbm }
%precedence Hpf.CAP_TABLE_IPV4_NHP, hpf_nhp_ext_update
%function IsDelete(dummy: int4)
%function GetArpChangeType(dummy: int4 -> change_type: int1)
%table vArpNhpChanged(hpf_nhp_index: int4, next_hop: int4, change_type: int1, nhp_index: int4)
    { transient(finish), variant }
Fib.res_fwd_nhp_index(65536, next_hop, -):-
    Arp.ConfigArp(next_hop, -, -, -, -, -, -, -, -).
vArpNhpChanged(hpf_nhp_index, next_hop, change_type, 65536) :-
    Fib.res_fwd_nhp_index(65536, next_hop, hpf_nhp_index),
    GetArpChangeType(next_hop, change_type).
%table NhpActive(hpf_nhp_index: int4, nhp_index: int4, next_hop: int4)
NhpActive(hpf_nhp_index, nhp_index, next_hop) :-
    Fib.get_fwd_nhp_index(-, nhp_index, next_hop, -, -, -, hpf_nhp_index),
    Arp.ConfigArp(next_hop, -, -, -, -, -, -, -, -).
NhpActive(hpf_nhp_index, nhp_index, next_hop) :-
    Fib.get_fwd_nhp_index(-, nhp_index, next_hop, if_index, -, -, hpf_nhp_index),
    Ifm.Pppoe(if_index, -, -, -, -, -).
vArpNhpChanged(hpf_nhp_index, next_hop, change_type, nhp_index) :-
    NhpActive(hpf_nhp_index, nhp_index, next_hop),
    GetArpChangeType(next_hop, change_type).
%table vArpNhpChanged0(next_hop: int4) { transient(finish), variant }
%table vArpNhpChangedNew(addr: int4, ifIndex: int4, type: int1, mac: byte6, fakeFlag: int1, vlanId: int2, workIfIndex: int4) { transient(finish) }
vArpNhpChanged0(next_hop) :-
    vArpNhpChangedNew(next_hop, -, -, -, -, -, -),
    IsDelete(next_hop).
vArpNhpChangedNew(next_hop, ifIndex, type, mac, fakeFlag, vlanId, workIfIndex) :-
    Arp.ConfigArp(next_hop, ifIndex, type, mac, fakeFlag, vlanId, workIfIndex, -, -).
vArpNhpChanged0(next_hop) :-
    vNhpChangedByOthers(l3if_index),
    Arp.ConfigArp(next_hop, l3if_index, -, -, -, -, -, -, -).
vArpNhpChanged0(next_hop) :-
    vNhpChangedByOthers(l1if_index),
    Arp.ConfigArp(next_hop, -, -, -, -, -, l1if_index, -, -).
vArpNhpChanged0(next_hop) :-
    Fib.get_fwd_nhp_index(-, -, next_hop, -, -, -, -).
vArpNhpChanged(hpf_nhp_index, next_hop, 1, nhp_index) :-
    vArpNhpChanged0(next_hop),
    Fib.res_fwd_nhp_index(nhp_index, next_hop, hpf_nhp_index),
    CheckTupleCount(nhp_index, 0).
%table tNhpChanged(hpf_nhp_index: int4, next_hop: int4, change_type: int1, nhp_index: int4,
                      l3if_index: int4, l1if_index: int4, encoded: byte32) { transient(finish) }
%function EncodeArpAttr(arp_type: int1, dmac: byte6, fake: int1, vlan_id: int2 -> encoded: byte32)
%function GetVlanL1If(l3_type: int1, l1if_index: int4, l3if_index: int4, vlan_id: int2
                   -> intf: int4, vlan: int2)
tNhpChanged(hpf_nhp_index, next_hop, 1, nhp_index, l3if_index, l1if_index, encoded) :-
    vArpNhpChanged(hpf_nhp_index, next_hop, 1, nhp_index),
    Arp.ConfigArp(next_hop, l3if_index, arp_type, dmac, fake, vlan_id, l1if_index, -, -),
    EncodeArpAttr(arp_type, dmac, fake, vlan_id, encoded).
%table vPppoeChanged(if_index: int4) { transient(finish), variant }
vPppoeChanged(if_index) :-
    Fib.get_fwd_nhp_index(-, -, -, if_index, -, -, -),
    Ifm.Pppoe(if_index, -, -, -, -, -).
vPppoeChanged(if_index) :-
    vNhpChangedByOthers(if_index),
    Ifm.Pppoe(if_index, -, -, -, -, -).
tNhpChanged(hpf_nhp_index, next_hop, 1, nhp_index, if_index, if_index, "0x00") :-
    vPppoeChanged(if_index),
    Fib.get_fwd_nhp_index(-, nhp_index, next_hop, if_index, -, -, hpf_nhp_index),
    CheckTupleCount(nhp_index, 0).
hpf_nhp_ext_update(hpf_nhp_index, change_type, nhp_index, next_hop,
    tag_type, ifType, l1if_index, l3if_index, l1fwd_index, l3fwd_index, arp_encoded, l1_encoded, l3_encoded) :-
    tNhpChanged(hpf_nhp_index, next_hop, change_type, nhp_index, l3if_index, l1if_index, arp_encoded),
    IfPCP(l1if_index, l1fwd_index, -, ifType, l1_encoded),
    IfPCP_copy(l3if_index, l3fwd_index, l3_type, vlan_id, l3_encoded),
    GetVlanL1If(l3_type, l1if_index, l3if_index, vlan_id, intf, vlan),
    vlanif_tag_type(intf, -, vlan, tag_type).
hpf_nhp_ext_update(hpf_nhp_index, 3, nhp_index, next_hop,
    1, 0, 0, 0, 0, 0, "0x00", "0x00", "0x00") :-
    vArpNhpChanged(hpf_nhp_index, next_hop, 3, nhp_index).
%table IfPCP(ifIndex: int4, fwdIfIndex: int4, type: int1, l3if_type: int2, encoded: byte32)
%table IfPCP_copy(ifIndex: int4, fwdIfIndex: int4, type: int1, l3if_type: int2, encoded: byte32)
IfPCP(251658240, 0, 0, 0, "0x00") :- tbl_init(-).
IfPCP_copy(_0, _1, _2, _3, _4) :- IfPCP(_0, _1, _2, _3, _4).
%function EncodeIfPppoeAttr(code: int1, ver: int1, type: int1, sessionId: int2, dmac: byte6,
                            smac: byte6, tp: int4, mtu_v4: int4, cpuType: int1
                         -> encoded: byte32)
%function EncodeIfNormalAttr(tp: int4, mtu_v4: int4, cpuType: int1, mac: byte6, if_type: int2
                          -> encoded: byte32)
%function EncodeIfVlanAttr(vlan_id: int2, mtu_v4: int4, mac: byte6, if_type: int2 -> encoded: byte32)
%table vlanif_tag_type(if_index: int4, brId: int4, vlanId: int2, tag_type: int1)
vlanif_tag_type(251658240, -1, 0, 1) :- tbl_init(-).
vlanif_tag_type(if_index, brId, vlanId, 0) :-
    BR.MidVlanUntagPort(brId, vlanId, if_index), BR.Vlan(-, brId, vlanId, -, -, -, -, -, -).
vlanif_tag_type(if_index, brId, vlanId, 1) :-
    BR.MidVlanTagPort(brId, vlanId, if_index), BR.Vlan(-, brId, vlanId, -, -, -, -, -, -).
%table IfNotPppoe(ifIndex: int4)
%table IfNotVlan(ifIndex: int4)
IfNotPppoe(ifIndex) :- Ifm.IfName(ifIndex, -, -, -, -, -, -, -), NOT Ifm.Pppoe(ifIndex, -, -, -, -, -).
IfNotVlan(ifIndex) :- IfNotPppoe(ifIndex), NOT BR.Vlan(-, -, -, -, -, -, ifIndex, -, -).
IfPCP(ifIndex, fwdIfIndex, 3, 0, encoded) :-
    Ifm.Pppoe(ifIndex, code, ver, type, sessionId, dmac),
    Ifm.Agg_Attributes(
        -, ifIndex, -, -, -, -, -, tp, -, -,
        -, smac, -, -, -, -, -, mtu_v4, -, -,
        -, -, -, -, -, -, -, -, -, cpuType, -),
    EncodeIfPppoeAttr(code, ver, type, sessionId, dmac, smac, tp, mtu_v4, cpuType, encoded),
    Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIndex).
IfPCP(ifIndex, fwdIfIndex, 2, vlan_id, encoded) :-
    BR.Vlan(-, -, vlan_id, -, -, -, ifIndex, -, -),
    IfNotPppoe(ifIndex),
    Ifm.Agg_Attributes(
        -, ifIndex, -, -, -, -, -, -, -, -,
        -, mac, -, -, -, -, -, mtu_v4, -, -,
        ifType, -, -, -, -, -, -, -, -, -, -),
    EncodeIfVlanAttr(vlan_id, mtu_v4, mac, ifType, encoded),
    Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIndex),
    NotEqual4(ifIndex, -1),
    NotEqual2(vlan_id, 0).
IfPCP(ifIndex, fwdIfIndex, 1, ifType, encoded) :-
    IfNotVlan(ifIndex),
    Ifm.Agg_Attributes(
        -, ifIndex, -, -, -, -, -, tp, -, -,
        -, mac, -, -, -, -, -, mtu_v4, -, -,
        ifType, -, -, -, -, -, -, -, -, cpuType, -),
    EncodeIfNormalAttr(tp, mtu_v4, cpuType, mac, ifType, encoded),
    Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIndex).
vNhpChangedByOthers(if_index) :- Ifm.IfL3(if_index).
vNhpChangedByOthers(if_index) :- IfPCP(if_index, -, -, -, -).
vNhpChangedByOthers(if_index) :- Nat.nat_interface_enable(if_index, -).
vNhpChangedByOthers(if_index) :- vlanif_tag_type(if_index, -, -, -).
}
namespace Hpf {
%readwrite dummy
%table dummy(a: int4, b: byte4)
%table dummy128(a: byte128)
dummy128(pb128) :-dummy(a, -),
    hpf_nhp_encap_eth(a, 0, "0x00", "0x00", 0, 0, p1, pb64, pb32),
    hpf_nhp_encap_pppoe(a, 0, "0x00", "0x00", 0, 0, 0, 0, 0, p1, pb64, pb32),
    hpf_nhp_l3svc(p1, pb32, p1_1, pb64),
    hpf_nhp_l1svc(p1, pb32, p1_1, pb128),
    hpf_nhp_outFwdIfs_eth(p1, 0, 0, 0, 0, p1_1, p1_2, pb32),
    hpf_nhp_outFwdIfs_pppoe(p1, 0, 0, 0, 0, p1_1, p1_2, pb32).
%function hpf_nhp_encap_eth(tp: int4, cpu_type: int1, smac: byte6, dmac: byte6, vlanId: int2, vlanTag: int2 -> encapSize: int1, encap: byte64, l2aclKey: byte32)
%function hpf_nhp_encap_pppoe(tp: int4, cpu_type: int1, smac: byte6, dmac: byte6, vlanId: int2, code: int1, ver: int1, type: int1, sessionId: int2 -> encapSize: int1, encap: byte64, l2aclKey: byte32)
%function hpf_nhp_l3svc(fwdIfNum: int1, outFwdIfsAddr: byte32 -> l3SvcNum: int1, l3SvcSet: byte64)
%function hpf_nhp_l1svc(fwdIfNum: int1, outFwdIfsAddr: byte32 -> l1SvcNum: int1, l1SvcSet: byte128)
%function hpf_nhp_outFwdIfs_eth(cpu_type: int1, fwdIfIdx: int4, hpf_if_type: int4, l3FwdIfIdx: int4, hpf_l3if_type: int4 -> encapType: int1, fwdIfNum: int1, outFwdIfsAddr: byte32)
%function hpf_nhp_outFwdIfs_pppoe(cpu_type: int1, fwdIfIdx: int4, hpf_if_type: int4, l3FwdIfIdx: int4, hpf_l3if_type: int4 -> encapType: int1, fwdIfNum: int1, outFwdIfsAddr: byte32)
}
namespace Pcp {
%table not_used(a: int4)
null(0) :- not_used(-).
}
namespace Hpf {
    %table CAP_TABLE_IPV6_FIB(prefix: int4, vpn: int4, dip: byte16, opcode: int2, mulNhp: int1, dirRoute: int1, defRoute: int1, nhpIdx: int4) {
        index(0(prefix, vpn, dip)),
        tbm
    }
    %table CAP_TABLE_IPV6_NHP(nhpIdx: int4, ctrlIfIdx: int4, fwdIfIdx: int4, nextHop: byte16) {
        index(0(nhpIdx)),
        tbm
    }
    %readonly CAP_TABLE_IPV6_NHP
    %table CAP_TABLE_IPV6_NHP_GROUP_ID(nhpGroupId: int4, nhpGroupHpfIndex: int4)
    %resource hpf_allocated_nhp6_group_index(nhpGroupId: int4 -> nhpGroupHpfIndex: int4) { sequential(max_size(10000)) }
    %table CAP_TABLE_IPV6_NHP_GROUP(nhpGrpIdx: int4, nhpNum: int1, nhpIndexes: byte128, primaryNphIndexes: int4) {
        index(0(nhpGrpIdx)),
        tbm
    }
    %table CAP_TABLE_IPV6_ND(vpn: int4, nextHop: byte16, fake: int1, ctrlIfIdx: int4, l3CtrlIfIdx: int4, fwdIfIdx: int4, l3FwdIfIdx: int4, dmac: byte6, vlanId: int2) {
        index(0(vpn, nextHop)),
        tbm
    }
    %table NOTIFY_TABLE_IPV6_ND(nextHop: byte16, fake: int1) {
        tbm,
        index(0(nextHop))
    }
    %table CAP_TABLE_IPV6_PORTIP(ctrlIfIdx: int4, fwdIfIdx: int4, index: int4) {
        index(0(ctrlIfIdx)),
        tbm
    }
    %table CAP_TABLE_IPV6_PORTIP_COUNT(ctrlIfIdx: int4, fwdIfIdx: int4, index: int4)
    %table CAP_TABLE_IPV6_PORTIPLIST(index: int4, vrf_index: int4, address: byte16, prefix: int4, type: int2) {
        index(0(index)),
        tbm
    }
    %table Nhp6Attribute(nhpGroupId: int4, mulNhp: int1, outNhpHpfIndex: int4)
    %resource portipv6_index_pool(ctrlIfIdx: int4 -> index: int4) { sequential(max_size(512)) }
    portipv6_index_pool(ctrlIfIdx, -) :- Ifm.ConfigIfIpv6Addr(ctrlIfIdx, -, -, -, -).
    hpf_allocated_nhp6_group_index(nhpGroupId, -) :-
    Fib6.NhpGroupAggregate(nhpGroupId, -, nhpNum, -, -),
    Fib.IsMulNhp(nhpNum).
    CAP_TABLE_IPV6_NHP_GROUP_ID(nhpGroupId, nhpGroupHpfIndex) :- hpf_allocated_nhp6_group_index(nhpGroupId, nhpGroupHpfIndex).
    CAP_TABLE_IPV6_NHP_GROUP_ID(nhpGroupId, -1) :- Fib6.NhpGroupAggregate(nhpGroupId, -, 1, -, -).
    Nhp6Attribute(nhpGroupId, mulNhp, outNhpHpfIndex) :-
        Fib6.NhpGroupAggregate(nhpGroupId, firstNhpHpfIndex, nhpNum, -, -),
        CAP_TABLE_IPV6_NHP_GROUP_ID(nhpGroupId, nhpGroupHpfIndex),
        Hpf.DecideMulNhp(nhpNum, firstNhpHpfIndex, nhpGroupHpfIndex, mulNhp, outNhpHpfIndex).
    CAP_TABLE_IPV6_FIB(prefix, vrfId, dip, routeFlags, mulNhp, dir_route, def_route, outNhpHpfIndex) :-
        Fib6.ConfigIpv6Fwd(-, vrfId, dip, prefix, -, routeAttr, routeFlags, -, nhpGroupId, -, -, -),
        Nhp6Attribute(nhpGroupId, mulNhp, outNhpHpfIndex),
        Fib6.Ipv6Func(routeAttr, dir_route, def_route).
    CAP_TABLE_IPV6_NHP(nhpHpfIndex, ctrlIfIdx, fwdIfIdx, nextHop) :-
        Fib6.get_fwd_nhp_index(-, nextHop, ctrlIfIdx, -, -, nhpHpfIndex),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_IPV6_NHP_GROUP(nhpGroupHpfIndex, nhpNum, nhpIndexes, primaryNhpIndexes) :-
        Fib6.ConfigNhpGroup(-, nhpGroupId, -, -),
        Fib6.NhpGroupAggregate(nhpGroupId, -, nhpNum, nhpIndexes, primaryNhpIndexes),
        hpf_allocated_nhp6_group_index(nhpGroupId, nhpGroupHpfIndex).
    %precedence CAP_TABLE_IPV6_ND, NOTIFY_TABLE_IPV6_ND
    %precedence CAP_TABLE_IPV6_NHP, CAP_TABLE_IPV6_NHP_GROUP, CAP_TABLE_IPV6_FIB
    CAP_TABLE_IPV6_ND(0, nextHop, fake, ifIndex, l3CtrlIfIdx, fwdIfIdx, l3FwdIfIdx, mac, vlanId) :-
        Nd.ConfigNd(nextHop, l3CtrlIfIdx, -, mac, fake, vlanId, ifIndex),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx), ctrlif_map_fwdif_copy(l3CtrlIfIdx, l3FwdIfIdx).
    NOTIFY_TABLE_IPV6_ND(nextHop, fake) :-
        Nd.ConfigNd(nextHop, -, -, -, fake, -, -).
    CAP_TABLE_IPV6_PORTIP_COUNT(ctrlIfIdx, fwdIfIdx, index) :-
        Ifm.ConfigIfIpv6Addr(ctrlIfIdx, -, -, -, -),
        portipv6_index_pool(ctrlIfIdx, index),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_IPV6_PORTIP(ctrlIfIdx, fwdIfIdx, index) :-
        CAP_TABLE_IPV6_PORTIP_COUNT(ctrlIfIdx, fwdIfIdx, index).
    CAP_TABLE_IPV6_PORTIPLIST(index, vrf_index, address, prefix, type) :-
        Ifm.ConfigIfIpv6Addr(ctrlIfIdx, vrf_index, address, prefix, type),
        portipv6_index_pool(ctrlIfIdx, index).
}
namespace Hpf {
    %table CAP_TABLE_VLAN(brId: int4, vlanId: int2) {
        index(0(brId, vlanId)),
        tbm
    }
    %table CAP_TABLE_VLAN_ATTR(brId: int4, vlanId: int2, vlanifFwdIdx: int4, bcMid: int2, bcStatMid: int2, macLearnEn: int1, missUcAction: int1,
        macLimitEn: int1, macLimitAlarmEn: int1, macLimitNum: int4, macLimitAction: int1, ipv4McEn: int1, igmpMode: int1) {
        index(0(brId, vlanId)),
        tbm
    }
    %table CAP_TABLE_FWDIF_PORT_ATTR(fwdIfIdx: int4, brId: int4, pvid: int2, stpEnable: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table CAP_TABLE_FWDIF_VLANIF(vlanifFwdIdx: int4, brId: int4, vlanId: int2) {
        index(0(vlanifFwdIdx)),
        tbm
    }
    %table CAP_TABLE_FWDIF_STP_ENABLE(fwdIfIdx: int4) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table CAP_TABLE_PORT_INST_STATE(brId: int4, fwdIfIdx: int4, instId: int2, state: int1) {
        index(0(brId, fwdIfIdx, instId)),
        tbm
    }
    %table hpf_vlan_igmpsnp_enable(brId: int4, vlanId: int2, state: int1) {
        index(0(brId, vlanId)), tbm
    }
    CAP_TABLE_VLAN(brId, vlanId) :-
        BR.Vlan(-, brId, vlanId, -, -, -, -, -, -).
    CAP_TABLE_VLAN_ATTR(brId, vlanId, vlanifFwdIdx, vlanId, vlanId, macLearn, 0, 0, macLimitAlarmEn, macLimitNum, macLimitAction, 0, 0) :-
        BR.Vlan(-, brId, vlanId, -, -, -, brVlanif, -, -),
        BR.VlanMacAttr(-, brId, vlanId, macLearn, macLimitNum, macLimitAction, macLimitAlarmEn),
        Hpf.ctrlif_map_fwdif(brVlanif, vlanifFwdIdx).
    CAP_TABLE_VLAN_ATTR(brId, vlanId, -1, vlanId, vlanId, macLearn, 0, 0, macLimitAlarmEn, macLimitNum, macLimitAction, 0, 0) :-
        BR.Vlan(-, brId, vlanId, -, -, -, -1, -, -),
        BR.VlanMacAttr(-, brId, vlanId, macLearn, macLimitNum, macLimitAction, macLimitAlarmEn).
    CAP_TABLE_FWDIF_PORT_ATTR(fwdIfIdx, brId, pvid, stpEnable) :-
        BR.Port(ctrlIfIdx, -, -, brId, pvid, -, stpEnable),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_FWDIF_STP_ENABLE(fwdIfIdx) :-
         Hsec.ProtocolEnable(22, -, ctrlIfIdx),
         Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_FWDIF_VLANIF(vlanifFwdIdx, brId, vlanId) :-
        BR.Vlan(-, brId, vlanId, -, -, -, brVlanif, -, -),
        Hpf.ctrlif_map_fwdif(brVlanif, vlanifFwdIdx).
    CAP_TABLE_PORT_INST_STATE(0, fwdIfIdx, instId, state) :-
        BR.PortInstanceState(ifIndex, instId, state),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_vlan_igmpsnp_enable(0, vlanId, state) :-
        Igmpsnp.VlanState(vlanId, state).
    %precedence CAP_TABLE_VLAN, CAP_TABLE_VLAN_ATTR
    %precedence Hpf.hpf_if_name, CAP_TABLE_FWDIF_STP_ENABLE
    %precedence Hpf.hpf_if_name, CAP_TABLE_FWDIF_VLANIF
    %precedence Hpf.hpf_if_name, CAP_TABLE_FWDIF_PORT_ATTR
    %precedence CAP_TABLE_VLAN, hpf_vlan_igmpsnp_enable
}
namespace Hpf {
    %table CAP_TABLE_BRIDGE_INFO(brId: int4, vlanFilter: int1, macLimitEn: int1, macAgeTime: int4, brFwdIfIdx: int4, macLimitNum: int4) {
        index(0(brId)),
        tbm
    }
    %table CAP_TABLE_SSW_BC_ELB(brId: int4, vlanId: int2, fwdIfIdx: int4) {
        index(0(brId, vlanId, fwdIfIdx)),
        tbm
    }
    %table CAP_TABLE_VLAN_BITMAP(fwdIfIdx: int4, brId: int4, vlanId: int2) {
        index(0(fwdIfIdx, brId, vlanId)),
        tbm
    }
    %table CAP_TABLE_VLAN_UNTAG_BITMAP(fwdIfIdx: int4, brId: int4, untagVlanId: int2) {
        index(0(fwdIfIdx, brId, untagVlanId)),
        tbm
    }
    %table CAP_TABLE_PORT_ISOLATE(srcFwdIfIndex: int4, dstFwdIfIndex: int4) {
        index(0(srcFwdIfIndex, dstFwdIfIndex)),
        tbm
    }
    %table CAP_TABLE_FWDIF_PORT_MAC_LEARN_LIMIT(fwdIfIdx: int4, learn: int1, learnAct: int1, limitEn: int1, limit: int4, limitAct: int1, limitAlm: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table CAP_TABLE_SSW_MAC(brId: int4, vlanId: int2, mac: byte6, staticMac: int1, drop: int1, local: int1, sec: int1, fwdIfIdx:int4) {
        index(0(brId, vlanId, mac)),
        tbm
    }
    %table CAP_TABLE_SSW_GLOBAL_BC_ELB(brId: int4, fwdIfIdx: int4) {
        index(0(brId, fwdIfIdx)),
        tbm
    }
    %function GetMacFlag(type: int1, flag: int4 -> staticMac: int1, drop: int1, local: int1, sec: int1)
    CAP_TABLE_BRIDGE_INFO(brId, vlanFilter, 0, macAgeTime, brFwdIfIdx, macLimitNum) :-
        BR.Bridge(-, brId, -, vlanFilter, brIfIdx),
        BR.BridgeMacAttr(-, brId, macAgeTime, -, -, macLimitNum),
        Hpf.ctrlif_map_fwdif(brIfIdx, brFwdIfIdx).
    CAP_TABLE_BRIDGE_INFO(brId, vlanFilter, 0, macAgeTime, -1, macLimitNum) :-
        BR.Bridge(-, brId, -, vlanFilter, -1),
        BR.BridgeMacAttr(-, brId, macAgeTime, -, -, macLimitNum).
    CAP_TABLE_SSW_BC_ELB(brId, vlanId, fwdIfIdx) :-
        BR.VlanPort(brId, vlanId, ifIndex),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    CAP_TABLE_VLAN_BITMAP(fwdIfIdx, brId, vlanId) :-
        BR.MidVlanTagPort(brId, vlanId, ifIndex),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    CAP_TABLE_VLAN_UNTAG_BITMAP(fwdIfIdx, brId, vlanId) :-
        BR.MidVlanUntagPort(brId, vlanId, ifIndex),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    CAP_TABLE_PORT_ISOLATE(srcFwdIfIndex, dstFwdIfIndex) :-
        BR.PortIsolate(srcIfIndex, dstIfIndex),
        Hpf.ctrlif_map_fwdif(srcIfIndex, srcFwdIfIndex),
        Hpf.ctrlif_map_fwdif_copy(dstIfIndex, dstFwdIfIndex).
    CAP_TABLE_FWDIF_PORT_MAC_LEARN_LIMIT(fwdIfIdx, 1, 0, 0, 0, limitAct, limitAlm) :-
        BR.PortMacAttr(ctrlIfIdx, -, -, -, -, limitAct, limitAlm),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        NOT BR.PortSecurity(ctrlIfIdx, -, -, -, -, -).
    CAP_TABLE_FWDIF_PORT_MAC_LEARN_LIMIT(fwdIfIdx, 1, 0, limitEn, limit, limitAct, 0) :-
        BR.PortSecurity(ctrlIfIdx, -, limitEn, limit, limitAct, -),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_SSW_MAC(brId, vlanId, mac, staticMac, drop, local, sec, fwdIfIdx) :-
        BR.MacOper(-, brId, vlanId, mac, ctrlIfIdx, type, flag),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        GetMacFlag(type, flag, staticMac, drop, local, sec).
    CAP_TABLE_SSW_GLOBAL_BC_ELB(brId, fwdIfIdx) :-
        BR.Port(ctrlIfIdx, -, -, brId, -, -, -),
        Ifm.IfName(ctrlIfIdx, -, -, 0, -, -, -, -),
        Ifm.Agg_Attributes(-, ctrlIfIdx, -, 1, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        NOT Ifm.EthTrunkMembers(ctrlIfIdx, -, -, -, -).
    CAP_TABLE_SSW_GLOBAL_BC_ELB(brId, fwdIfIdx) :-
        BR.Port(ctrlIfIdx, -, -, brId, -, -, -),
        Ifm.IfName(ctrlIfIdx, -, -, 14, -, -, -, -),
        Ifm.Agg_Attributes(-, ctrlIfIdx, -, 1, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        NOT Ifm.EthTrunkMembers(ctrlIfIdx, -, -, -, -).
    CAP_TABLE_SSW_GLOBAL_BC_ELB(brId, fwdIfIdx) :-
        BR.Port(ctrlIfIdx, -, -, brId, -, -, -),
        Ifm.IfName(ctrlIfIdx, -, -, 15, -, -, -, -),
        Ifm.Agg_Attributes(-, ctrlIfIdx, -, 1, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        NOT Ifm.EthTrunkMembers(ctrlIfIdx, -, -, -, -).
    CAP_TABLE_SSW_GLOBAL_BC_ELB(brId, fwdIfIdx) :-
        BR.Port(ctrlIfIdx, -, -, brId, -, -, -),
        Ifm.IfName(ctrlIfIdx, -, -, 13, -, -, -, -),
        Ifm.Agg_Attributes(-, ctrlIfIdx, -, 1, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    %precedence CAP_TABLE_BRIDGE_INFO, CAP_TABLE_VLAN
    %precedence CAP_TABLE_VLAN, CAP_TABLE_VLAN_BITMAP
    %precedence CAP_TABLE_VLAN, CAP_TABLE_VLAN_UNTAG_BITMAP
    %precedence CAP_TABLE_VLAN, CAP_TABLE_SSW_BC_ELB
    %precedence CAP_TABLE_BRIDGE_INFO, CAP_TABLE_FWDIF_PORT_ATTR
    %precedence CAP_TABLE_FWDIF_PORT_ATTR, CAP_TABLE_VLAN_BITMAP
    %precedence CAP_TABLE_FWDIF_PORT_ATTR, CAP_TABLE_VLAN_UNTAG_BITMAP
    %precedence CAP_TABLE_FWDIF_PORT_ATTR, CAP_TABLE_PORT_ISOLATE
    %precedence CAP_TABLE_FWDIF_PORT_ATTR, CAP_TABLE_FWDIF_PORT_MAC_LEARN_LIMIT
    %precedence Hpf.hpf_if_name, CAP_TABLE_VLAN_BITMAP
    %precedence Hpf.hpf_if_name, CAP_TABLE_FWDIF_PORT_MAC_LEARN_LIMIT
    %precedence Hpf.hpf_if_name, CAP_TABLE_SSW_GLOBAL_BC_ELB
}
namespace Hpf {
    %table hpf_capwap_decap(sip: int4, dip: int4, sport: int2, dport: int2, tunnelId: int4, fwdIfIdx: int4) {
        index(0(sip, dip, sport, dport)),
        tbm
    }
    %table hpf_capwap_tunnel(tunnelId: int4, sip: int4, dip: int4, sport: int2, dport: int2, mtu: int2, fwdIfIdx: int4, dtlsIndex: int4,
        preamble: int1, tflag: int1, mflag: int1,
        server_or_client: int1, encrypt: int1, apMac: byte6) {
        index(0(tunnelId)),
        tbm
    }
    %table CAP_TABLE_WIRED_TUNNEL(fwdIfIdx: int4, fwdMode: int1, portId: int4, portType: int4, apMac: byte6) {
        index(0(fwdIfIdx)),
        tbm
    }
    %resource capwap_tunnel_id_pool(sip: int4, dip: int4, sport: int2, dport: int2 -> tunnelId: int4) {
        sequential(max_size(128))
    }
    capwap_tunnel_id_pool(sip, dip, sport, dport, -) :-
        Wmp.ConfigCapwapTunnel(sip, dip, sport, dport, -, -, -,
        -, -, -,
        -, -, -).
    ctrlif_map_fwdif(ifIndex, -) :-
        Wmp.ConfigCapwapTunnel(-, -, -, -, -, ifIndex, -,
        -, -, -,
        -, -, -).
    hpf_capwap_decap(sip, dip, sport, dport, tunnelId, fwdIfIdx) :-
        Wmp.ConfigCapwapTunnel(sip, dip, sport, dport, -, ifIndex, -,
        -, -, -,
        -, -, -),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        capwap_tunnel_id_pool(sip, dip, sport, dport, tunnelId).
    hpf_capwap_tunnel(tunnelId, sip, dip, sport, dport, mtu, fwdIfIdx, dtlsIndex,
        preamble, tflag, mflag,
        server_or_client, encrypt, apMac) :-
        Wmp.ConfigCapwapTunnel(sip, dip, sport, dport, mtu, ifIndex, dtlsIndex,
        preamble, tflag, mflag,
        server_or_client, encrypt, apMac),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        capwap_tunnel_id_pool(sip, dip, sport, dport, tunnelId).
    %precedence hpf_if_capwap, hpf_capwap_tunnel
    %table hpf_global_capwap_fwdifindex(index:int4, capwapFwdifIndex:int4){
        index(0(index)),
        tbm
    }
    hpf_global_capwap_fwdifindex(0, capwapFwdifIndex):-
        Wmp.ConfigCapwapTunnel(-, -, -, -, -, ifIndex, -,
        -, -, -,
        -, -, -),
        ctrlif_map_fwdif(ifIndex, capwapFwdifIndex).
    CAP_TABLE_WIRED_TUNNEL(fwdIfIdx, fwdMode, portId, portType, apMac) :-
        Wmp.ConfigWiredTunnel(ctrlIfIdx, fwdMode, portId, portType, apMac),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
}
namespace Hpf {
    %readonly CarId
    %table CAP_TABLE_CAR(
        carId: int4, paramType: int1, pktNumOrPktLen: int1, carType: int1, colorMode: int1,
        tc: int8, tpTe: int8, cir: int8, cbs: int8, pir: int8,
        pbsEbs: int8,
        greenPermit: int1, greenRemarkType:int1, greenRemarkValue:int1,
        yellowPermit: int1, yellowRemarkType:int1, yellowRemarkValue:int1,
        redPermit: int1, redRemarkType:int1, redRemarkValue:int1)
        {index(0(carId)), tbm}
    %resource CarId(svc: int4, id: int4 -> carId: int4) { sequential(max_size(32742)), index(1(id)) }
}
namespace Hpf {
%table CAP_TABLE_CPCAR_PRO(protocolType: int4, flags: int1, carId: int4, priority: int1)
    { index(0(protocolType, flags)), tbm }
%table CAP_TABLE_HSEC_BLACKLIST(policy_id:int4, filter_id:int4, group_id:int4, group_type:int4)
    { index(0(policy_id, filter_id)), tbm }
%table CAP_TABLE_HSEC_TOTALCAR(carId:int4)
    { index(0(carId)), tbm }
%table CAP_TABLE_HSEC_SESSCAR_CARID(index:int4, carId:int4)
    { index(0(index)), tbm }
%table hpf_dns_global_deny_req_from_wan_enable(noUse: int4) { index(0(noUse)), tbm }
%resource CpCarId(protocolType: int4, flags: int1 -> carId: int4) { sequential(max_size(32742)) }
CpCarId(protocolType, flags, -) :- Hsec.Mid_PolicyCpcar(protocolType, flags, pps), NotEqual4(pps, 0).
CarId(1, id, -) :- CpCarId(-, -, id).
CAP_TABLE_CAR(carId, 1,
    0, 0, 0, p, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0) :-
    Hsec.Mid_PolicyCpcar(protocolType, flags, pps),
    CpCarId(protocolType, flags, id), I4ToI8(pps, p),
    CarId(1, id, carId).
CAP_TABLE_CPCAR_PRO(protocolType, flags, carId, priority) :-
    CpCarId(protocolType, flags, id), Hsec.CpcarPriority(protocolType, priority), CarId(1, id, carId).
CAP_TABLE_HSEC_BLACKLIST(policy_id, filter_id, downGroupId, downGroupType) :-
    Hsec.PolicyFilterCfg(policy_id, filter_id, group_id),
    Hsec.AppliedPolicyCfg(policy_id, -),
    Acl.GroupCfg(group_id, groupType, -, -, -, -, -, -),
    Acl.GroupType2HpfType(groupType, dt),
    I1ToI4(dt, downGroupType),
    Acl.GetHpfGroupId(0, group_id, downGroupId).
Acl.GroupInUseOne(downGroupId, aclGroupId, groupType) :-
    Hsec.PolicyFilterCfg(policy_id, -, aclGroupId),
    Hsec.AppliedPolicyCfg(policy_id, -),
    Acl.GroupCfg(aclGroupId, groupType, -, -, -, -, -, -),
    Acl.GetHpfGroupId(0, aclGroupId, downGroupId).
%table TotalCar(nsId:int4, pps: int8) { index(0(nsId)) }
CarId(5, 0, -) :- TotalCar(0, -).
CAP_TABLE_CAR(carId, 1,
    0, 0, 0, pps, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0) :-
    CarId(5, -, carId),
    TotalCar(0, pps).
CAP_TABLE_HSEC_TOTALCAR(carId) :- CarId(5, -, carId).
%function InitTotalCar(id: int1) { access_kv(capset), access_delta(TotalCar) }
null0(0):- tbl_init(-), InitTotalCar(0).
CAP_TABLE_CAR(carId, 1,
    0, 0, 0, 1024, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0) :-
    CarId(10, -, carId).
null0(0) :- tbl_init(-), GenCarId(10, 5).
CAP_TABLE_HSEC_SESSCAR_CARID(index, carId) :- CarId(10, index, carId).
hpf_dns_global_deny_req_from_wan_enable(noUse) :-
    Hsec.DnsRequestDenyFromWan(noUse).
}
namespace Hpf {
%table CAP_TABLE_HSEC_ATKSRC(id:int4, enable:int1, alarmEnable:int1, alarmThreshold:int4, penaltyEnable:int1,
    penaltyThreshold:int4, defendMask:int1)
    { index(0(id)), tbm }
%table CAP_TABLE_SEC_ATK_SIP(ipv4Addr:int4, ipv6Addr:byte16, isIpv6:int1)
    { index(0(ipv4Addr, ipv6Addr, isIpv6)), tbm }
%table CAP_TABLE_SEC_ATK_SMAC(mac:byte6)
    { index(0(mac)), tbm }
%table CAP_TABLE_HSEC_ANTIATK(attackType:int4, enable:int1, carId:int4)
    { index(0(attackType)), tbm }
%table CAP_TABLE_HSEC_HOSTCAR_CARID(index:int4, carId:int4)
    { index(0(index)), tbm }
%table CAP_TABLE_HSEC_HOSTCAR_CFG(arpEnable: int1, dhcpEnable: int1, ieee8021xEnable: int1, pps:int4)
    { index(0(arpEnable, dhcpEnable, ieee8021xEnable)), tbm }
CAP_TABLE_HSEC_ATKSRC(policyId, _1, _2, _3, _4, _5, _6) :-
    Hsec.AutoDefend(policyId, _1, _2, _3, _4, _5, _6), Hsec.AppliedPolicyCfg(policyId, -).
CAP_TABLE_SEC_ATK_SIP(ipv4Addr, ipv6Addr, isIpv6) :- Hsec.Mid_AttackSource(-, 2, ipv4Addr, ipv6Addr, -, isIpv6).
CAP_TABLE_SEC_ATK_SIP(ipv4Addr, ipv6Addr, isIpv6) :- Hsec.Mid_AttackSource(-, 3, ipv4Addr, ipv6Addr, -, isIpv6).
CAP_TABLE_SEC_ATK_SMAC(mac) :- Hsec.Mid_AttackSource(-, 1, - , -, mac, -).
CAP_TABLE_SEC_ATK_SMAC(mac) :- Hsec.Mid_AttackSource(-, 3, - , -, mac, -).
CarId(2, 3, -) :- Hsec.AntiAttack(3, -, -).
CarId(2, 4, -) :- Hsec.AntiAttack(4, -, -).
CarId(2, 5, -) :- Hsec.AntiAttack(5, -, -).
CAP_TABLE_CAR(carId, 1,
    0, 0, 0, p, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0) :-
    Hsec.AntiAttack(attackType, 1, pps), CarId(2, attackType, carId),
    I4ToI8(pps, p).
CAP_TABLE_HSEC_ANTIATK(1, enable, 0) :- Hsec.AntiAttack(1, enable, -).
CAP_TABLE_HSEC_ANTIATK(2, enable, 0) :- Hsec.AntiAttack(2, enable, -).
CAP_TABLE_HSEC_ANTIATK(3, enable, carId) :- Hsec.AntiAttack(3, enable, -), CarId(2, 3, carId).
CAP_TABLE_HSEC_ANTIATK(4, enable, carId) :- Hsec.AntiAttack(4, enable, -), CarId(2, 4, carId).
CAP_TABLE_HSEC_ANTIATK(5, enable, carId) :- Hsec.AntiAttack(5, enable, -), CarId(2, 5, carId).
%function GenCarId(svc: int4, num: int4){ access_delta(CarId) }
CAP_TABLE_CAR(carId, 1,
    0, 0, 0, p, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0, 0, 0) :-
    Hsec.PublicHostCar(-, -, -, -, pps), CarId(4, -, carId),
    I4ToI8(pps, p).
CAP_TABLE_HSEC_HOSTCAR_CARID(index, carId) :- CarId(4, index, carId).
CAP_TABLE_HSEC_HOSTCAR_CFG(arpEnable, dhcpEnable, ieee8021xEnable, pps) :-
    Hsec.PublicHostCar(0, arpEnable, dhcpEnable, ieee8021xEnable, pps), GenCarId(4, 128).
%table CAP_TABLE_CPCAR_PORT_MAP(protocolType: int4, ipProtocol: int4, srcPort:int2, dstPort:int2)
    { index(0(protocolType, ipProtocol, srcPort, dstPort)), tbm }
CAP_TABLE_CPCAR_PORT_MAP(1183, ipProtocol, srcPort, dstPort) :-
    Hsec.CpcarAttr(1183, -, ipProtocol, -, -, -, -, srcPort, -, dstPort, -, -, -, -, -, -, -, -, -, -, -, -, -),
    product_s380(-).
CAP_TABLE_CPCAR_PORT_MAP(1183, ipProtocol, srcPort, dstPort) :-
    Hsec.CpcarAttr(1183, -, ipProtocol, -, -, -, -, srcPort, -, dstPort, -, -, -, -, -, -, -, -, -, -, -, -, -),
    product_s310s(-).
CAP_TABLE_CPCAR_PORT_MAP(protocolType , ipProtocol, srcPort, dstPort) :-
    Hsec.CpcarAttr(protocolType , -, ipProtocol, -, -, -, -, srcPort, -, dstPort, -, -, -, -, -, -, -, -, -, -, -, -, -),
    product_um(-).
CAP_TABLE_CPCAR_PORT_MAP(protocolType , ipProtocol, srcPort, dstPort) :-
    Hsec.CpcarAttr(protocolType , -, ipProtocol, -, -, -, -, srcPort, -, dstPort, -, -, -, -, -, -, -, -, -, -, -, -, -),
    product_wlan(-) .
}
namespace Hpf {
%table CAP_TABLE_MIRR_OBSERVE(
    observeId: int2, ctrlIfIdx: int4, fwdIfIdx: int4)
{
    index(0(observeId)), tbm
}
CAP_TABLE_MIRR_OBSERVE(observeIndex, ctrlIfIdx, fwdIfIdx) :-
    Mir.ObserverIndex(observeIndex, ctrlIfIdx), ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
}
namespace Capture {
    Acl.GroupInUseOne(hpfGid, cfgGid, t) :-
        Capture.Config(-, cfgGid, -, -, -, -, -, -, -),
        Acl.GroupCfg(cfgGid, t, -, -, -, -, -, -),
        Acl.GetHpfGroupId(0, cfgGid, hpfGid).
}
namespace Hpf {
%table CAP_TABLE_FWD_CAPT(id: int1, aclValid: int1, aclGroupId: int4, aclGroupType: int4, direction: int1, localhost: int1,
    vlanId: int2, ifIndexList: byte32,
    packetNumber: int2, packetLength: int2, timeout: int8)
    {
        index(0(id)), tbm
    }
%table CaptureIfIndex(ifIndex: int4) { index(0(ifIndex)), tbm }
CaptureIfIndex(ifIndex) :- Ifm.PublishNif(ifIndex, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    Capture.Config(-, -, -, -, -, -, -, -, -).
CaptureIfIndex(ifIndex) :- Ifm.EthTrunk(ifIndex, -, -, -),
    Capture.Config(-, -, -, -, -, -, -, -, -).
CaptureIfIndex(0) :- Capture.Config(-, -, -, -, -, -, -, -, -).
CAP_TABLE_FWD_CAPT(id, 0, 0, 0, direction, _1, _2, _3, _4, _5, _6) :-
    Capture.Config(id, 0, direction, _1, _2, _3, _4, _5, _6).
CAP_TABLE_FWD_CAPT(id, 1, downGroupId, 0, direction, _1, _2, _3, _4, _5, _6) :-
    Capture.Config(id, aclGroupId, direction, _1, _2, _3, _4, _5, _6),
    Acl.GroupCfg(aclGroupId, -, -, -, -, -, -, -),
    Acl.GetHpfGroupId(0, aclGroupId, downGroupId).
%precedence Hpf.Hpf_ActivateGroup, CAP_TABLE_FWD_CAPT
}
namespace Hpf {
    %table hpf_debug_dump(type: int4, id: int4, a: int4, b: int4) { transient(tuple) }
    %table hpf_debug_dump_out(type: int4, id: int4, a: int4, b: int4)
        { index(0(type, id, a, b)), tbm }
    hpf_debug_dump_out(a, b, c, d) :- hpf_debug_dump(a, b, c, d).
}
namespace Acl {
%readonly GroupId2Num
%table GroupId2Num(id: int4, num: int4)
GroupId2Num(id, num) :- Acl.GroupCfg(id, -, num, -, -, -, -, -), CmpInt4(num, 0, 5).
GroupId2Num(0, 0) :- tbl_init(-).
}
namespace Acl {
%readonly vb_RuleChanged
%readwrite HpfGroupInUse, vf_UseHpfGroup, tf_MappingList, tf_MappingL2L4List
%readonly GroupType2HpfType, GetHpfGroupId, GetCfgGroupId, GetIntArrayElem3_4
%table vb_RuleChanged(cfgGid: int4) { transient(finish), variant }
%table HpfGroupInUse(hpfGid: int4, type: int1, useStat: int1)
%table vf_UseHpfGroup(hpfGid: int4) { transient(finish), variant }
%table tf_MappingList(
    hpfGid: int4, type: int1, cfgGid: int4, prio: int4, action: byte40)
    { transient(finish) }
%table tf_MappingL2L4List(
    hpfGid: int4, cfgGidL2: int4, cfgGidL4: int4, prio: int4,
    denyType: int1, action: byte40)
    { transient(finish) }
%function GroupType2HpfType(cfgType: int1 -> hpfType: int1)
%function GetHpfGroupId(type: int1, cfgId: int4 -> hpfGid: int4)
%function GetCfgGroupId(type: int1, hpfGid: int4 -> cfgId: int4)
%function GetIntArrayElem3_4(a: byte12, i: int4, dummy: int4 -> e: int4)
}
namespace Acl {
%readonly tRuleHpf, tGroupHpf
%table tGroupHpf(hpfGid: int4, newType: int1, newUseStat: int1,
    oldType: int1, oldUseStat: int1, op: int1) { transient(finish) }
%table tRuleHpf(hpfGid: int4, type: int1, prio: int4,
    match: byte100, action: byte40, useStat: int1, orgIndexes: byte12)
    { transient(finish) }
}
namespace Acl {
%table TimeRangeStatusL2(gid: int4, index: int4, timeStatus: int1)
%table TimeRangeStatusL4(gid: int4, index: int4, timeStatus: int1)
TimeRangeStatusL4(gid, index, timeStatus) :-
    AdvRule(gid, index, -, -,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -,
        -, -, -, -, -,
        -, -, timeStatus, 0, -, -, -, -, -, -, -, -, -, -, -, -).
TimeRangeStatusL4(gid, index, timeStatus) :-
    AdvRule(gid, index, -, -,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -,
        -, -, -, -, -,
        -, -, -, timeId, -, -, -, -, -, -, -, -, -, -, -, -),
    TimeRangeCfg(timeId, -, -, timeStatus).
TimeRangeStatusL2(gid, index, timeStatus) :-
    EthRule(gid, index, -, -, -, -, -, -, -, -, -, -, -, -, -, timeId, -),
    TimeRangeCfg(timeId, -, -, timeStatus).
TimeRangeStatusL2(gid, index, timeStatus) :-
    EthRule(gid, index, -, -, -, -, -, -, -, -, -, -, -, -, timeStatus, 0, -).
vb_RuleChanged(cfgGid) :- TimeRangeStatusL2(cfgGid, -, -).
vb_RuleChanged(cfgGid) :- TimeRangeStatusL4(cfgGid, -, -).
%table RuleWithPool(cfgGid: int4, index: int4, cond: int4, type: int1,
    sIpPoolId: int4, dIpPoolId: int4, sPortPoolId: int4, dPortPoolId: int4)
%table tNoRuleIDs(cfgGid: int4, index: int4, cond: int4, idMask: int1)
    { transient(finish) }
%table tRuleFixed(cfgGid: int4, index: int4, match: byte100, action: byte8)
    { transient(finish) }
%table tRulePortRange(cfgGid: int4, index: int4, cond: int4,
    sPortBgn: int2, sPortEnd: int2, sPortPool: int4,
    dPortBgn: int2, dPortEnd: int2, dPortPool: int4)
    { transient(finish) }
%table tRuleCfgMatchAction(cfgGid: int4, index: int4, match: byte100, action: byte8)
    { transient(finish) }
%table tRuleHpfMatchAction(hpfGid: int4, type: int1,
    index0: int4, index1: int4, index2: int4,
    match: byte100, action: byte40, useStat: int1)
    { transient(finish) }
%function GetRuleIDType(cond: int4,
    sIpPoolId: int4, dIpPoolId: int4, sPortPoolId: int4, dPortPoolId: int4 ->
    type: int1)
%table RuleWithIpPool(cfgGid: int4, index: int4, sIppUsed: int1, dIppUsed: int1)
%function GetRuleIPIDType(cond: int4 -> sIppUsed: int1, dIppUsed: int1)
RuleWithPool(gid, index, cond, type, sIp, dIp, sPort, dPort) :-
    AdvRule(gid, index, -, cond,
        -, -, sIp, -, -, dIp, -, -, -, sPort, -, -, -, dPort,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    GetRuleIDType(cond, sIp, dIp, sPort, dPort, type),
    CmpInt1(type, 0, 5).
RuleWithIpPool(gid, index, sIppUsed, dIppUsed) :-
    AdvRule(gid, index, -, cond,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    GetRuleIPIDType(cond, sIppUsed, dIppUsed).
tNoRuleIDs(gid, index, cond, 0) :-
    vUseGroupL4(gid),
    AdvRule(gid, index, -, cond,
        -, -, sIp, -, -, dIp, -, -, -, sPort, -, -, -, dPort,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    GetRuleIDType(cond, sIp, dIp, sPort, dPort, type),
    CmpInt1(type, 0, 2).
%function GetIPV4BaseCfgAction(mask: int4, deny: int1, dipp: int4 -> action: byte8)
%function GetRuleV4Fixed(
    aclCondMask: int4, srcIpAddr: int4, srcIpMask: int4, dstIpAddr: int4, dstIpMask: int4,
    anyFlag:int1, protocol:int1, fragType:int1, tos:int1, tcpFlag:int1, icmpType:int1, icmpCode:int1, dscp:int1, igmpType:int1, ipPre: int1, srcUclIndex:int2, uclSrcType:int1, dstUclIndex:int2, uclDstType:int1, srcIpv6Addr:byte16, srcIpv6Mask:byte16, dstIpv6Addr:byte16, dstIpv6Mask:byte16, l4Version:int1 -> match: byte100)
tRuleFixed(gid, index, match, cfg_action) :-
    vUseGroupL4(gid),
    AdvRule(gid, index, deny, mask,
        sipv, sipm, -, dipv, dipm, dipp, -, -, -, -, -, -, -, -,
        anyFlag, protocol, fragType, tos, tcpFlag, icmpType, icmpCode, dscp, igmpType, ipPre, srcUclIndex, uclSrcType, dstUclIndex, uclDstType, srcIpv6Addr, srcIpv6Mask, dstIpv6Addr, dstIpv6Mask, l4Version, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    GetIPV4BaseCfgAction(mask, deny, dipp, cfg_action),
    GetRuleV4Fixed(mask, sipv, sipm, dipv, dipm, anyFlag, protocol, fragType, tos, tcpFlag, icmpType, icmpCode, dscp, igmpType, ipPre, srcUclIndex, uclSrcType, dstUclIndex, uclDstType, srcIpv6Addr, srcIpv6Mask, dstIpv6Addr, dstIpv6Mask, l4Version, match).
tRulePortRange(gid, index, cond, spb, spe, sid, dpb, dpe, did) :-
    vUseGroupL4(gid),
    AdvRule(gid, index, -, cond,
        -, -, -, -, -, -, spb, spe, -, sid, dpb, dpe, -, did,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).
vb_RuleChanged(cfgGid) :- GroupCfg(cfgGid, -, -, -, -, -, -, -).
vb_RuleChanged(cfgGid) :- EthRule(cfgGid, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).
vb_RuleChanged(cfgGid) :- AdvRule(cfgGid, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -,
    -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).
%function GetEthCfgRule(aclCondMask: int4, deny: int1, frameType:int2, frameMask:int2, srcMac:byte6, srcMacMask:byte6, dstMac:byte6, dstMacMask:byte6, vlanId:int2, vlanIdMask:int2, value8021p:int1 ->
    match: byte100, action: byte8)
tRuleCfgMatchAction(gid, index, match, cfg_action) :-
    vUseGroupL2(gid),
    TimeRangeStatusL2(gid, index, 0),
    EthRule(gid, index, deny, mask, frameType, frameMask, srcMac, srcMacMask, dstMac, dstMacMask, vlanId, vlanIdMask, value8021p, -, -, -, -),
    GetEthCfgRule(mask, deny, frameType, frameMask, srcMac, srcMacMask, dstMac, dstMacMask, vlanId, vlanIdMask, value8021p, match, cfg_action).
%function CheckPortRangeUse(aclCondMask: int4, isSrc: int1, isPool: int1,
    id: int4, b0: int2, e0: int2 -> use: int1, b1: int2, e1: int2)
%table tSrcPortRange(cfgGid: int4, index: int4, use: int1, b: int2, e: int2) { transient(finish) }
%table tDstPortRange(cfgGid: int4, index: int4, use: int1, b: int2, e: int2) { transient(finish) }
tSrcPortRange(gid, index, use, b, e) :-
    tRulePortRange(gid, index, cond, spb, spe, sid, -, -, -),
    CheckPortRangeUse(cond, 1, 0, sid, spb, spe, use, b, e).
tDstPortRange(gid, index, use, b, e) :-
    tRulePortRange(gid, index, cond, -, -, -, dpb, dpe, did),
    CheckPortRangeUse(cond, 0, 0, did, dpb, dpe, use, b, e).
%table tSrcPortExpanded(cfgGid: int4, index: int4, val: int2, mask: int2) { transient(finish) }
%table tDstPortExpanded(cfgGid: int4, index: int4, val: int2, mask: int2) { transient(finish) }
%aggregate ExpandPortRange(use: int1,
    portBeg:int2, portEnd:int2 -> portValue: int2, portMask: int2)
    { many_to_many }
tSrcPortExpanded(gid, index, val, mask) :-
    tSrcPortRange(gid, index, use, b, e) GROUP-BY(gid, index)
    ExpandPortRange(use, b, e, val, mask).
tDstPortExpanded(gid, index, val, mask) :-
    tDstPortRange(gid, index, use, b, e) GROUP-BY(gid, index)
    ExpandPortRange(use, b, e, val, mask).
%table tPortExpanded(
    cfgGid: int4, index: int4,
    sVal: int2, sMask: int2, dVal: int2, dMask: int2)
    { transient(finish) }
tPortExpanded(gid, index, sVal, sMask, dVal, dMask) :-
    tSrcPortExpanded(gid, index, sVal, sMask),
    tDstPortExpanded(gid, index, dVal, dMask).
%function GetIPV4CfgRule(match0: byte100,
    sVal: int2, sMask: int2, dVal: int2, dMask: int2 -> match: byte100)
tRuleCfgMatchAction(gid, index, match, cfg_action) :-
    tNoRuleIDs(gid, index, -, 0),
    tRuleFixed(gid, index, match0, cfg_action),
    tPortExpanded(gid, index, sVal, sMask, dVal, dMask),
    TimeRangeStatusL4(gid, index, 0),
    GetIPV4CfgRule(match0, sVal, sMask, dVal, dMask, match).
%table vUseGroupL2(cfgGid: int4) { transient(finish), variant }
%table vUseGroupL4(cfgGid: int4) { transient(finish), variant }
vUseGroupL2(g) :- tf_MappingList(-, 2, g, -, -), Acl.GroupCfg(g, -, -, -, -, -, -, -).
vUseGroupL4(g) :- tf_MappingList(-, 0, g, -, -), Acl.GroupCfg(g, -, -, -, -, -, -, -).
vUseGroupL2(g) :- tf_MappingL2L4List(-, g, -, -, -, -), Acl.GroupCfg(g, -, -, -, -, -, -, -).
vUseGroupL4(g) :- tf_MappingL2L4List(-, -, g, -, -, -), Acl.GroupCfg(g, -, -, -, -, -, -, -).
%function GetTupCount(dummy: int1 -> c: int4)
%table vHpfGroupInUse(hpfGid: int4, type: int1, useStat: int1, c: int4) { transient(finish), variant }
vHpfGroupInUse(h, t, us, c) :- HpfGroupInUse(h, t, us), GetTupCount(t, c).
%table tHpfGroupInUse(hpfGid: int4, type: int1, useStat: int1) { transient(finish) }
tHpfGroupInUse(hpfGid, type, useStat) :-
    vf_UseHpfGroup(hpfGid),
    HpfGroupInUse(hpfGid, type, useStat).
%table tHpfGroupTypeNoChange(hpfGid: int4) { transient(finish) }
%table tHpfGroupTouched(hpfGid: int4) { transient(finish) }
tHpfGroupTouched(h) :- vHpfGroupInUse(h, -, -, -).
tHpfGroupTypeNoChange(h) :- vf_UseHpfGroup(h), NOT tHpfGroupTouched(h).
%aggregate CheckTypeChange(t: int1, s: int1, c: int4 ->
    nt: int1, ns: int1, ot: int1, os: int1, nc: int1)
tGroupHpf(h, nt, ns, ot, os, nc) :-
    vHpfGroupInUse(h, t, s, c) GROUP-BY (h) CheckTypeChange(t, s, c, nt, ns, ot, os, nc).
tGroupHpf(h, nt, ns, nt, ns, 2) :- tHpfGroupTypeNoChange(h), tHpfGroupInUse(h, nt, ns).
%precedence tHpfGroupTypeNoChange, tGroupHpf
%precedence vHpfGroupInUse, tGroupHpf
%precedence tHpfGroupInUse, tGroupHpf
%precedence Acl.tRuleCfgMatchActionCopyL2, Acl.tRuleHpfMatchAction
%precedence Acl.tRuleCfgMatchAction, Acl.tRuleHpfMatchAction
%precedence Acl.tRuleCfgMatchAction, Acl.tHpfGroupInUse
%precedence Acl.tHpfGroupTouched, Acl.tGroupHpf
%function GetHpfAction(type: int1, cfgAction: byte8, action: byte40
    -> hpfAction: byte40)
tRuleHpfMatchAction(hpfGid, type, index0, index1, 0, match, hpfAction, useStat) :-
    tf_MappingList(hpfGid, type, cfgGid, index0, action),
    tHpfGroupInUse(hpfGid, type, useStat),
    tRuleCfgMatchAction(cfgGid, index1, match, cfgAction),
    GetHpfAction(type, cfgAction, action, hpfAction).
%table tRuleCfgMatchActionCopyL2(cfgGid: int4, index: int4, match: byte100, action: byte8)
    { transient(finish) }
tRuleCfgMatchAction(0, 0, "0x00", "0x00") :- vf_UseHpfGroup(-).
tRuleCfgMatchActionCopyL2(0, 0, "0x00", "0x00") :- vf_UseHpfGroup(-).
tRuleCfgMatchActionCopyL2(cfgGidL2, index, match, action) :-
    tf_MappingL2L4List(-, cfgGidL2, -, -, -, -),
    tRuleCfgMatchAction(cfgGidL2, index, match, action).
%function GetHpfL2L4Rule(
    type: int1, action: byte40,
    matchL2: byte100, denyL2: byte8,
    matchL4: byte100, denyL4: byte8, denyType: int1 ->
    hpfMatch: byte100, hpfAction: byte40)
tRuleHpfMatchAction(hpfGid, type, index, indexL2, indexL4, hpfMatch, hpfAction, useStat) :-
    vf_UseHpfGroup(hpfGid),
    tHpfGroupInUse(hpfGid, type, useStat),
    tf_MappingL2L4List(hpfGid, cfgGidL2, cfgGidL4, index, denyType, action),
    tRuleCfgMatchAction(cfgGidL4, indexL4, matchL4, denyL4),
    tRuleCfgMatchActionCopyL2(cfgGidL2, indexL2, matchL2, denyL2),
    GetHpfL2L4Rule(type, action, matchL2, denyL2, matchL4, denyL4, denyType,
        hpfMatch, hpfAction).
%aggregate ArrangeRuleOrder(
    index0: int4, index1: int4, index2: int4,
    match: byte100, action: byte40, useStat: int1 ->
    hIndex: int4, hMatch: byte100, hAction: byte40,
    hUseStat: int1, orgIndexes: byte12) { ordered, many_to_many }
tRuleHpf(hpfGid, type, hIndex, hMatch, hAction, hUseStat, orgIndexes) :-
    tRuleHpfMatchAction(hpfGid, type, index0, index1, index2, match, action, s) GROUP-BY(hpfGid, type)
    ArrangeRuleOrder(index0, index1, index2, match, action, s,
                     hIndex, hMatch, hAction, hUseStat, orgIndexes).
%table vIpPoolChanged(pid: int4) { transient(finish), variant }
%table vPortPoolChanged(pid: int4) { transient(finish), variant }
vIpPoolChanged(pid) :- IpPoolCfg(pid, -, -, -, -).
vPortPoolChanged(pid) :- PortPoolCfg(pid, -, -, -, -, -).
vb_RuleChanged(cfgGid) :- RuleWithPool(cfgGid, -, -, -, sIpPool, -, -, -), vIpPoolChanged(sIpPool).
vb_RuleChanged(cfgGid) :- RuleWithPool(cfgGid, -, -, -, -, dIpPool, -, -), vIpPoolChanged(dIpPool).
vb_RuleChanged(cfgGid) :- RuleWithPool(cfgGid, -, -, -, -, -, sPortPool, -), vPortPoolChanged(sPortPool).
vb_RuleChanged(cfgGid) :- RuleWithPool(cfgGid, -, -, -, -, -, -, dPortPool), vPortPoolChanged(dPortPool).
%table vUsePool(cfgGid: int4, index: int4) { transient(finish), variant }
vUsePool(g, i) :- vUseGroupL2(g), RuleWithPool(g, i, -, -, -, -, -, -).
vUsePool(g, i) :- vUseGroupL4(g), RuleWithPool(g, i, -, -, -, -, -, -).
%table tSrcIpExpanded(gid: int4, index: int4, val: int4, mask: int4) { transient(finish) }
%table tDstIpExpanded(gid: int4, index: int4, val: int4, mask: int4) { transient(finish) }
tSrcIpExpanded(g, i, 0, 0) :- vUsePool(g, i), RuleWithIpPool(g, i, 0, -).
tDstIpExpanded(g, i, 0, 0) :- vUsePool(g, i), RuleWithIpPool(g, i, -, 0).
tSrcIpExpanded(gid, index, ip, mask) :-
    vUsePool(gid, index),
    RuleWithPool(gid, index, -, -, sIp, -, -, -),
    RuleWithIpPool(gid, index, e, -),
    CmpInt1(e, 0, 5),
    IpPoolCfg(sIp, -, ip, mask, -).
tDstIpExpanded(gid, index, ip, mask) :-
    vUsePool(gid, index),
    RuleWithPool(gid, index, -, -, -, dIp, -, -),
    RuleWithIpPool(gid, index, -, e),
    CmpInt1(e, 0, 5),
    IpPoolCfg(dIp, -, ip, mask, -).
%function GetPortRangeFromOp(rangeOp: int1,
    startPort: int2, endPort: int2 -> portBeg: int2, portEnd: int2)
tSrcPortRange(gid, index, use, b1, e1) :-
    vUsePool(gid, index),
    RuleWithPool(gid, index, cond, -, -, -, sPort, -),
    PortPoolCfg(sPort, -, bgn, end, op, -),
    GetPortRangeFromOp(op, bgn, end, b0, e0),
    CheckPortRangeUse(cond, 1, 1, sPort, b0, e0, use, b1, e1).
tDstPortRange(gid, index, use, b1, e1) :-
    vUsePool(gid, index),
    RuleWithPool(gid, index, cond, -, -, -, -, dPort),
    PortPoolCfg(dPort, -, bgn, end, op, -),
    GetPortRangeFromOp(op, bgn, end, b0, e0),
    CheckPortRangeUse(cond, 0, 1, dPort, b0, e0, use, b1, e1).
%function GetIPV4CfgRuleFromPool(match0: byte100,
    spVal: int2, spMask: int2, dpVal: int2, dpMask: int2,
    sIp: int4, sMask: int4, dIp: int4, dMask: int4
    -> match: byte100)
tRuleCfgMatchAction(gid, index, match, cfg_action) :-
    tRuleFixed(gid, index, match0, cfg_action),
    tPortExpanded(gid, index, spVal, spMask, dpVal, dpMask),
    tSrcIpExpanded(gid, index, sIp, sMask),
    tDstIpExpanded(gid, index, dIp, dMask),
    TimeRangeStatusL4(gid, index, 0),
    GetIPV4CfgRuleFromPool(match0,
        spVal, spMask, dpVal, dpMask, sIp, sMask, dIp, dMask, match).
}
namespace Acl {
%readwrite GroupInUseOne
%table GroupInUseOne(hpfGid: int4, cfgGid: int4, cfgType: int1)
}
namespace Acl {
%table tUseGroupOne(hpfGid: int4, type: int1) { transient(finish) }
tUseGroupOne(h, t) :- GroupInUseOne(h, -, ct), GroupType2HpfType(ct, t).
HpfGroupInUse(h, t, 0) :- tUseGroupOne(h, t).
vf_UseHpfGroup(h) :- tUseGroupOne(h, -).
vf_UseHpfGroup(h) :- vb_RuleChanged(c), GroupInUseOne(h, c, -).
tf_MappingList(h, t, c, 0, "0x00") :-
    vf_UseHpfGroup(h),
    GroupInUseOne(h, c, ct),
    GroupType2HpfType(ct, t).
}
namespace Hpf {
%readonly Hpf_ActivateGroup
%precedence Hpf_Group, Hpf_Rule, Hpf_ActivateGroup
%table Hpf_Group(groupId: int4, groupType: int1, useStat: int1,
    oldGroupType: int1, oldUseStat: int1, op: int1)
    { index(0(groupId, groupType, useStat, oldGroupType, oldUseStat)), tbm }
%table Hpf_ActivateGroup(groupId: int4, groupType: int1, useStat: int1,
    oldGroupType: int1, oldUseStat: int1, op: int1)
    { index(0(groupId, groupType, useStat, oldGroupType, oldUseStat)), tbm }
%table Hpf_Rule(
    groupId: int4, priority: int4,
    protoFamily: int1, match: byte100, action: byte40, useStat: int1)
    { index(0(groupId, priority)), tbm }
Hpf_Group(gid, nt, ns, ot, os, op) :- Acl.tGroupHpf(gid, nt, ns, ot, os, op).
Hpf_ActivateGroup(gid, nt, ns, ot, os, op) :- Acl.tGroupHpf(gid, nt, ns, ot, os, op).
Hpf_Rule(gid, prio, protoFamily, match, action, useStat) :-
    Acl.tRuleHpf(gid, protoFamily, prio, match, action, useStat, -).
}
namespace Acl {
}
namespace Sacl {
%readonly GetL2L4AclGroupId
%readonly SaclStatMapping, TrafficParamsToMultiAclGroup, TrafficLimitCarMap, HpfAclGidType
%table Hpf_ApplySacl(
    fwdIf: int4, appType: int1, policyType: int1, direction: int1, protoFamily: int1, aclGroupType:int1,
    downGroupId: int4, flip: int4)
    { index(0(fwdIf, appType, policyType, direction, protoFamily, aclGroupType, downGroupId, flip)), tbm }
%table Hpf_SvcEnable(fwdIf: int4, appType: int1, direction: int1)
    { index(0(fwdIf, appType, direction)), tbm }
%precedence Hpf_ApplySacl, Hpf_SvcEnable
%readonly Hpf_ApplySacl
%resource TrafficParamsToMultiAclGroup(policyType:int1, direction:int1, protoFamily:int1, appType:int1, appValue:int4, l2L4AclGroup: int4 -> multiAclGroupId: int4)
    { sequential(max_size(100000)) }
%resource TrafficLimitCarMap(policyType:int1, direction:int1, protoFamily:int1, appType:int1, appValue:int4, aclL2GroupId:int4, aclL4GroupId: int4, order: int4 -> id: int4)
    { sequential(max_size(100000)) }
%table MGroupType(mgid: int4, taskType: int1, aclGroupType:int1)
%table MultiIDMapping(mgid: int4, order: int4, gidL2: int4, gidL4: int4, ipType:int1, l4Version: int1)
%table FwdifToIFMap(mgid: int4, fwdif: int4)
%table RedirectToIFMap(mgid: int4, order: int4, redirectId: int4)
%table TrafficInstanceCarId(mgid: int4, order: int4, carId: int4)
%table TrafficRedirectNextHopMGid(mgid: int4, order: int4, actionValue:int4, nextHop: int4)
%table HpfGroupTypeFlip(hpfGid: int4, flip: int4) { transient(field(flip)) }
%table HpfAclGidType(type: int1)
HpfAclGidType(1) :- product_s380(-).
HpfAclGidType(2) :- product_s310s(-).
HpfAclGidType(2) :- product_um(-).
HpfAclGidType(2) :- product_wlan(-).
%function GetL2L4AclGroupId(product: int1, aclL2GroupId:int4, aclL4GroupId:int4-> l2L4AclGroup:int4)
TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, l2l4, -) :-
    TrafficInstance(policyType, direction, protoFamily, appType, appValue, l2, l4, -, -, -,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    HpfAclGidType(product),
    GetL2L4AclGroupId(product, l2, l4, l2l4).
%function GetFwdifID(appType: int1, appValue: int4 -> outIfIndex: int4)
FwdifToIFMap(mgid, fwdIf) :-
    TrafficParamsToMultiAclGroup(-, -, -, appType, appValue, -, mgid),
    GetFwdifID(appType, appValue, outIfIndex),
    Hpf.ctrlif_map_fwdif(outIfIndex, fwdIf).
%function GetRedirectID(policyType: int1, actionType: int4, actionValue: int4 -> outIfIndex: int4)
RedirectToIFMap(mgid, order, fwdIf) :-
    TrafficInstance(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, aclL4GroupId,
        order, actionType, actionValue, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    HpfAclGidType(product),
    GetL2L4AclGroupId(product, aclL2GroupId, aclL4GroupId, l2l4),
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, l2l4, mgid),
    GetRedirectID(policyType, actionType, actionValue, outIfIndex),
    Hpf.ctrlif_map_fwdif(outIfIndex, fwdIf).
TrafficLimitCarMap(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, aclL4GroupId, order, -) :-
    TrafficInstance(policyType, direction, protoFamily, appType, appValue,
        aclL2GroupId, aclL4GroupId, order, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    CmpInt1(policyType, 5, 2).
}
namespace Hpf {
CarId(3, id, -) :- Sacl.TrafficLimitCarMap(-, -, -, -, -, -, -, -, id).
CAP_TABLE_CAR(carId, 3, 0, 0, 0,
        0, 0, cir8, cbs8, pir8, pbs8,
        greenAction, greenRemarkType, greenRemarkValue,
        yellowAction, yellowRemarkType, yellowRemarkValue,
        redAction, redRemarkType, redRemarkValue) :-
    Sacl.TrafficLimitCarMap(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, aclL4GroupId, order, id0),
    CarId(3, id0, carId),
    Sacl.TrafficInstance(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, aclL4GroupId, order, -, -,
        cir, pir, cbs, pbs, greenAction, greenRemarkType, greenRemarkValue, yellowAction, yellowRemarkType, yellowRemarkValue, redAction, redRemarkType, redRemarkValue, -, -, -, -, -, -),
    I4ToI8(cir, cir8), I4ToI8(pir, pir8), I4ToI8(cbs, cbs8), I4ToI8(pbs, pbs8).
}
namespace Sacl {
TrafficInstanceCarId(mgid, order, carId) :-
    TrafficLimitCarMap(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, aclL4GroupId, order, id0),
    Sacl.HpfAclGidType(product),
    GetL2L4AclGroupId(product, aclL2GroupId, aclL4GroupId, l2l4),
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, l2l4, mgid),
    Hpf.CarId(3, id0, carId).
TrafficInstanceCarId(mgid, order, 0) :-
    TrafficInstance(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, aclL4GroupId,
        order, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    Sacl.HpfAclGidType(product),
    GetL2L4AclGroupId(product, aclL2GroupId, aclL4GroupId, l2l4),
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, l2l4, mgid),
    CmpInt1(policyType, 5, 5).
%table L4GroupType(cfgGid: int4, hasIp: int1)
%aggregate CheckNoIPPresent(l4Version:int1, condMask: int4-> ipType: int1)
%function CheckGidVaild(gidL4: int4)
%function GetL4Version(isIPv6: int1 -> l4Version: int1)
L4GroupType(cfgGid, ipType) :-
    Acl.AdvRule(cfgGid, -, -, condMask,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -,
        -, -, -, -, l4Version,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -) GROUP-BY(cfgGid)
    CheckNoIPPresent(l4Version, condMask, ipType).
MultiIDMapping(mgid, order, gidL2, gidL4, ipType, l4Version) :-
    TrafficInstance(policyType, direction, protoFamily, appType, appValue, gidL2, gidL4, order, -, -,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    Sacl.HpfAclGidType(product),
    GetL2L4AclGroupId(product, gidL2, gidL4, l2l4),
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, l2l4, mgid),
    CheckGidVaild(gidL4),
    Acl.GroupCfg(gidL4, -, -, -, -, isIPv6, -, -),
    GetL4Version(isIPv6, l4Version),
    L4GroupType(gidL4, ipType).
MultiIDMapping(mgid, order, gidL2, 0, -1, -1) :-
    TrafficInstance(policyType, direction, protoFamily, appType, appValue, gidL2, 0, order, -, -,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    Sacl.HpfAclGidType(product),
    GetL2L4AclGroupId(product, gidL2, 0, l2l4),
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, l2l4, mgid).
%aggregate GetMGroupType(gidL2: int4, gidL4: int4, ipType: int1, l4Version: int1 -> taskType: int1, aclGroupType: int1)
MGroupType(mgid, taskType, aclGroupType) :-
    MultiIDMapping(mgid, -, gidL2, gidL4, ipType, l4Version) GROUP-BY(mgid)
    GetMGroupType(gidL2, gidL4, ipType, l4Version, taskType, aclGroupType).
%table SaclApplyReady(mgid: int4, hpfGid: int4)
SaclApplyReady(mgid, hpfGid) :-
    FwdifToIFMap(mgid, -),
    RedirectToIFMap(mgid, order, -),
    TrafficInstanceCarId(mgid, order, -),
    MGroupType(mgid, -, -),
    Acl.GetHpfGroupId(4, mgid, hpfGid).
%table vAclGid0(hpfGid: int4, ggid: int4) { transient(finish), variant }
%function GetStatFlag(policyType: int1, actionType: int4, actionValue: int4 -> useStat: int1)
Acl.HpfGroupInUse(hpfGid, type, useStat) :-
    TrafficInstance(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, aclL4GroupId, -, actionType, actionValue,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    Sacl.HpfAclGidType(product),
    GetL2L4AclGroupId(product, aclL2GroupId, aclL4GroupId, l2l4),
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, l2l4, mgid),
    MGroupType(mgid, -, type),
    SaclApplyReady(mgid, hpfGid),
    GetStatFlag(policyType, actionType, actionValue, useStat).
vAclGid0(hpfGid, mgid) :-
    TrafficInstance(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, aclL4GroupId, -, -, -,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    Sacl.HpfAclGidType(product),
    GetL2L4AclGroupId(product, aclL2GroupId, aclL4GroupId, l2l4),
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, l2l4, mgid),
    Acl.GetHpfGroupId(4, mgid, hpfGid).
vAclGid0(hpfGid, mgid) :-
    Acl.vb_RuleChanged(gid),
    Acl.GroupCfg(gid, 1, -, -, -, -, -, -),
    MultiIDMapping(mgid, -, gid, -, -, -),
    Acl.GetHpfGroupId(4, mgid, hpfGid).
vAclGid0(hpfGid, mgid) :-
    Acl.vb_RuleChanged(gid),
    Acl.GroupCfg(gid, t, -, -, -, -, -, -),
    CmpInt1(t, 2, 6),
    MultiIDMapping(mgid, -, -, gid, -, -),
    Acl.GetHpfGroupId(4, mgid, hpfGid).
vAclGid0(hpfGid, mgid) :- SaclApplyReady(mgid, hpfGid).
Acl.vf_UseHpfGroup(hpfGid) :- vAclGid0(hpfGid, -).
%function GetSaclAction(policyType:int1, direction:int1, protoFamily:int1, appType:int1, appValue:int4, actionType: int4, actionValue: int4,
    carId: int4, redirectId: int4 -> action: byte40, denyType: int1)
%function IsNotRedirectOutIf(policyType:int1, actionType:int4)
%table TrafficRedirectNextHopEx(ifIndex: int4, nextHop: int4)
TrafficRedirectNextHopEx(redirectOutIf, nextHop) :-
    TrafficRedirectNextHop(redirectOutIf, nextHop),
    Ifm.IfNet(redirectOutIf, -, -, -, 1).
TrafficRedirectNextHopEx(redirectOutIf, 0) :-
    TrafficInstance(-, -, -, -, -, -, -, -, 2, redirectOutIf,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    Ifm.IfNet(redirectOutIf, -, -, -, 1),
    NOT TrafficRedirectNextHop(redirectOutIf, -).
TrafficRedirectNextHopMGid(mgid, order, redirectOutIf, nextHop) :-
    TrafficInstance(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, aclL4GroupId, order, 2, redirectOutIf,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    Sacl.HpfAclGidType(product),
    GetL2L4AclGroupId(product, aclL2GroupId, aclL4GroupId, l2l4),
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, l2l4, mgid),
    CmpInt1(policyType, 3, 2),
    TrafficRedirectNextHopEx(redirectOutIf, nextHop).
TrafficRedirectNextHopMGid(mgid, order, -1, actionValue) :-
    TrafficInstance(policyType, direction, protoFamily, appType, appValue, aclL2GroupId, aclL4GroupId, order, actionType, actionValue,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    Sacl.HpfAclGidType(product),
    GetL2L4AclGroupId(product, aclL2GroupId, aclL4GroupId, l2l4),
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, l2l4, mgid),
    IsNotRedirectOutIf(policyType, actionType).
vAclGid0(hpfGid, mgid) :-
    TrafficRedirectNextHopMGid(mgid, -, -, -),
    Acl.GetHpfGroupId(4, mgid, hpfGid).
Acl.tf_MappingL2L4List(hpfGid, cfgGidL2, cfgGidL4, order, denyType, action) :-
    vAclGid0(hpfGid, mgid),
    TrafficInstance(policyType, direction, protoFamily, appType, appValue,
        cfgGidL2, cfgGidL4, order, actionType, -,
        -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
    Sacl.HpfAclGidType(product),
    GetL2L4AclGroupId(product, cfgGidL2, cfgGidL4, l2l4),
    TrafficParamsToMultiAclGroup(policyType, direction, protoFamily, appType, appValue, l2l4, mgid),
    TrafficRedirectNextHopMGid(mgid, order, -, actionValue),
    TrafficInstanceCarId(mgid, order, carId),
    RedirectToIFMap(mgid, order, redirectId),
    GetSaclAction(policyType, direction, protoFamily, appType, appValue, actionType, actionValue, carId, redirectId, action, denyType),
    Acl.GetHpfGroupId(4, mgid, hpfGid).
%function ClearStatTuples(hpfGid: int4) {
    access_current(SaclStatMapping),
    access_delta(SaclStatMapping)
}
%table tSaclStatMappingCleared(hpfGid: int4) { transient(finish) }
%table SaclStatMapping(hpfGid: int4, cfgGid: int4, cfgIndex: int4, statId: int4)
    { index(0(hpfGid, cfgGid, cfgIndex, statId)),
      index(1(hpfGid))
    }
%table tClearStat(hpfGid: int4) { transient(finish) }
tClearStat(hpfGid) :- Acl.tGroupHpf(hpfGid, -, -, -, -, -).
tSaclStatMappingCleared(hpfGid) :- tClearStat(hpfGid), ClearStatTuples(hpfGid).
%function GetStatId(useStat: int1, inStatId: int4 -> outStatId: int4)
SaclStatMapping(hpfGid, gidL2, l2Index, statId) :-
    tSaclStatMappingCleared(hpfGid),
    Acl.HpfGroupInUse(hpfGid, type, -),
    Acl.tRuleHpf(hpfGid, type, inStatId, -, -, useStat, orgIndexes),
    GetStatId(useStat, inStatId, statId),
    Acl.GetIntArrayElem3_4(orgIndexes, 0, 0, index0),
    Acl.GetIntArrayElem3_4(orgIndexes, 1, 0, l2Index),
    MultiIDMapping(mgid, index0, gidL2, -, -, -),
    CmpInt4(gidL2, 0, 5),
    Acl.GetCfgGroupId(4, hpfGid, mgid),
    Acl.GetHpfGroupId(4, mgid, hpfGid).
SaclStatMapping(hpfGid, gidL4, l4Index, statId) :-
    tSaclStatMappingCleared(hpfGid),
    Acl.HpfGroupInUse(hpfGid, type, -),
    Acl.tRuleHpf(hpfGid, type, inStatId, -, -, useStat, orgIndexes),
    GetStatId(useStat, inStatId, statId),
    Acl.GetIntArrayElem3_4(orgIndexes, 0, 0, index0),
    Acl.GetIntArrayElem3_4(orgIndexes, 2, 0, l4Index),
    MultiIDMapping(mgid, index0, -, gidL4, -, -),
    CmpInt4(gidL4, 0, 5),
    Acl.GetCfgGroupId(4, hpfGid, mgid),
    Acl.GetHpfGroupId(4, mgid, hpfGid).
HpfGroupTypeFlip(hpfGid, 0) :-
    TrafficParamsToMultiAclGroup(-, -, -, -, -, -, mgid),
    Acl.GetHpfGroupId(4, mgid, hpfGid).
HpfGroupTypeFlip(hpfGid, 1) :- Acl.tGroupHpf(hpfGid, -, -, -, -, 3).
%table SvcEnable(fwdIf: int4, appType: int1, direction: int1)
Hpf_SvcEnable(fwdIf, appType, direction) :- SvcEnable(fwdIf, appType, direction).
SvcEnable(fwdIf, appType, direction) :-
    TrafficParamsToMultiAclGroup(-, direction, -, appType, -, -, mgid),
    SaclApplyReady(mgid, -),
    FwdifToIFMap(mgid, fwdIf).
Hpf_ApplySacl(fwdIf, appType, policyType, direction, taskType, aclGroupType, hpfGid, flip) :-
    TrafficParamsToMultiAclGroup(policyType, direction, -, appType, -, -, mgid),
    MGroupType(mgid, taskType, aclGroupType),
    FwdifToIFMap(mgid, fwdIf),
    SaclApplyReady(mgid, hpfGid),
    HpfGroupTypeFlip(hpfGid, flip).
%readonly SaclIfChanged
%table SaclIfChanged(fwdIf: int4, direction: int1) { transient(tuple), variant }
SaclIfChanged(fwdIf, direction) :-
    TrafficParamsToMultiAclGroup(-, direction, -, 0, -, -, -),
    Ifm.IfName(if_index, -, -, -, -, -, -, -),
    Hpf.ctrlif_map_fwdif(if_index, fwdIf).
SaclIfChanged(fwdIf, direction) :-
    TrafficParamsToMultiAclGroup(-, direction, -, 2, -, -, mgid),
    SaclApplyReady(mgid, -),
    FwdifToIFMap(mgid, fwdIf),
    HpfAclGidType(1).
Pcp.vNhpChangedByOthers(if_index) :-
    SaclIfChanged(fwdIf, 2),
    Hpf.ctrlif_map_fwdif(if_index, fwdIf).
%precedence Hpf.Hpf_ActivateGroup, Hpf_ApplySacl
%precedence Sacl.MultiIDMapping, Sacl.MGroupType, Acl.tGroupHpf, Acl.tRuleHpf
}
namespace Pcp {
%precedence Sacl.Hpf_ApplySacl, hpf_nhp_ext_update
}
namespace Hpf {
hpf_fwdif_ing_l1svc(fwdIf, 0):-
    Sacl.SaclIfChanged(fwdIf, 1).
%precedence Sacl.Hpf_ApplySacl, hpf_fwdif_ing_l1svc
}
sacl_inst_ext(
    appType, direction, policyType, 0, appValue,
    0, groupStage, protoFamily, g2, ifType,
    applyVlan, ifVlanId, 0, 0, actionType,
    actionValue, mgid) :-
    Sacl.TrafficInstance(
        policyType, direction, protoFamily1, appType, appValue,
        g2, g4, -, actionType, actionValue, -, -, -, -, -, -, -, -, -, -, -, -, -, 0, 0, groupStage, ifType, applyVlan, ifVlanId),
    Sacl.HpfAclGidType(product),
    Sacl.GetL2L4AclGroupId(product, g2, g4, l2l4),
    Sacl.TrafficParamsToMultiAclGroup(policyType, direction, protoFamily1, appType, appValue, l2l4, mgid),
    I1ToI4(protoFamily1, protoFamily),
    I4ToI1(protoFamily, protoFamily1),
    CmpInt4(g2, 0, 5).
sacl_inst_ext(
    appType, direction, policyType, 0, appValue,
    0, groupStage, protoFamily, g4, ifType,
    applyVlan, ifVlanId, 0, 0, actionType,
    actionValue, mgid) :-
    Sacl.TrafficInstance(
        policyType, direction, protoFamily1, appType, appValue,
        g2, g4, -, actionType, actionValue, -, -, -, -, -, -, -, -, -, -, -, -, -, 0, 0, groupStage, ifType, applyVlan, ifVlanId),
    Sacl.HpfAclGidType(product),
    Sacl.GetL2L4AclGroupId(product, g2, g4, l2l4),
    Sacl.TrafficParamsToMultiAclGroup(policyType, direction, protoFamily1, appType, appValue, l2l4, mgid),
    I1ToI4(protoFamily1, protoFamily),
    I4ToI1(protoFamily, protoFamily1),
    CmpInt4(g4, 0, 5).
sacl_rule_status(hpfGid, gid, index, 0, statId, 1) :-
    Sacl.SaclStatMapping(hpfGid, gid, index, statId).
namespace Iptable {
%readonly GroupResource
%resource GroupResource(appType: int1, appValue: int4 -> gid: int4) { sequential(max_size(10000)) }
GroupResource(appType, appValue, -) :- IPTableCfg(appType, appValue, -, -, -, -, -).
Acl.HpfGroupInUse(hpfGid, 2, 0) :- GroupResource(-, -, g), Acl.GetHpfGroupId(2, g, hpfGid).
Acl.HpfGroupInUse(hpfGid, 0, 0) :- GroupResource(-, -, g), Acl.GetHpfGroupId(3, g, hpfGid).
%table vAclList(appType: int1, appValue: int4) { transient(finish), variant }
%table vAclGid2(hpfGid: int4, ggid: int4) { transient(finish), variant }
%table vAclGid4(hpfGid: int4, ggid: int4) { transient(finish), variant }
vAclList(appType, appValue) :- IPTableCfg(appType, appValue, -, -, -, -, -).
vAclGid2(hpfGid, ggid) :-
    vAclList(appType, appValue),
    GroupResource(appType, appValue, ggid),
    Acl.GetHpfGroupId(2, ggid, hpfGid).
vAclGid4(hpfGid, ggid) :-
    vAclList(appType, appValue),
    GroupResource(appType, appValue, ggid),
    Acl.GetHpfGroupId(3, ggid, hpfGid).
vAclGid2(hpfGid, ggid) :-
    Acl.vb_RuleChanged(g2),
    IPTableCfg(appType, appValue, -, g2, -, -, -),
    GroupResource(appType, appValue, ggid),
    Acl.GetHpfGroupId(2, ggid, hpfGid).
vAclGid4(hpfGid, ggid) :-
    Acl.vb_RuleChanged(g4),
    IPTableCfg(appType, appValue, -, -, g4, -, -),
    GroupResource(appType, appValue, ggid),
    Acl.GetHpfGroupId(3, ggid, hpfGid).
Acl.vf_UseHpfGroup(hpfGid) :- vAclGid2(hpfGid, -).
Acl.vf_UseHpfGroup(hpfGid) :- vAclGid4(hpfGid, -).
%function GetToPortAction(type: int1, port: int2 -> action: byte40)
Acl.tf_MappingList(hpfGid, 2, g2, prio, action) :-
    vAclGid2(hpfGid, ggid),
    GroupResource(appType, appValue, ggid),
    IPTableCfg(appType, appValue, -, g2, -, toPort, prio),
    GetToPortAction(0, toPort, action).
Acl.tf_MappingList(hpfGid, 0, g4, prio, action) :-
    vAclGid4(hpfGid, ggid),
    GroupResource(appType, appValue, ggid),
    IPTableCfg(appType, appValue, -, -, g4, toPort, prio),
    GetToPortAction(1, toPort, action).
}
namespace Hpf {
%precedence Hpf_ActivateGroup, HpfIpTable
%table HpfIpTable(fwdIf: int4, hpfGidL2: int4, hpfGidL4: int4) { index(0(fwdIf)), tbm }
%readonly HpfFwdif_IpTable
%table HpfFwdif_IpTable(fwdIf: int4, hpfGidL2: int4, hpfGidL4: int4) { }
HpfIpTable(fwdIf, hpfGidL2, hpfGidL4) :-
    HpfFwdif_IpTable(fwdIf, hpfGidL2, hpfGidL4).
HpfFwdif_IpTable(fwdIf, hpfGidL2, hpfGidL4) :-
    Iptable.GroupResource(-, appValue, ggid),
    Acl.GetHpfGroupId(2, ggid, hpfGidL2),
    Acl.GetHpfGroupId(3, ggid, hpfGidL4),
    ctrlif_map_fwdif(appValue, fwdIf).
%table V_HpfFwdif_IpTable(fwdIf: int4, hpfGidL2: int4, hpfGidL4: int4) { transient(finish), variant }
V_HpfFwdif_IpTable(fwdIf, hpfGidL2, hpfGidL4) :-
    HpfFwdif_IpTable(fwdIf, hpfGidL2, hpfGidL4).
%function hpf_fwdif_iptable_args(fwdIf: int4, hpfGidL2: int4, hpfGidL4: int4 -> temp: int4)
hpf_fwdif_ing_l1svc(fwdIf, temp):-
    V_HpfFwdif_IpTable(fwdIf, hpfGidL2, hpfGidL4),
    hpf_fwdif_iptable_args(fwdIf, hpfGidL2, hpfGidL4, temp).
%precedence HpfIpTable, hpf_fwdif_ing_l1svc
}
namespace Hpf {
    %table MstpIfBlocking(fwdifIdx:int4,stpInstance:int2){
        index(0(fwdifIdx, stpInstance)),
        tbm
    }
    %table L2ProtocolTunnel(fwdifIdx: int4, prot: int1){
        index(0(fwdifIdx, prot)),
        tbm
    }
    MstpIfBlocking(fwdifIdx, stpInstance) :-
        BR.MstpIfBlocking(ifIndex, stpInstance),
        ctrlif_map_fwdif(ifIndex, fwdifIdx).
    L2ProtocolTunnel(fwdifIdx, prot) :-
        BR.L2ProtocolTunnel(ifIndex, prot),
        ctrlif_map_fwdif(ifIndex, fwdifIdx).
    %precedence Hpf.hpf_if_name, L2ProtocolTunnel
}
namespace Hpf {
    %table hpf_nat_interface_enable(fwdifIndex: int4, enable: int1) {
        index(0(fwdifIndex)), tbm
    }
    %table hpf_nat_policy(policyName: byte64, policyType: int2, ruleId: int4) {
        index(0(policyName, policyType)), tbm
    }
    %table hpf_nat_policy_address_mask(policyName: byte64, policyType:int2, ruleConSerial: int2, any: int2, except: int2, ipAddr: int4, mask: int4) {
        index(0(policyName, policyType, ruleConSerial, any, except, ipAddr, mask)), tbm
    }
    %table hpf_nat_policy_address_masklen(policyName: byte64, policyType: int2, ruleConSerial: int2, any: int2, except: int2, ipAddr: int4, maskLen: int4) {
        index(0(policyName, policyType, ruleConSerial, any, except, ipAddr, maskLen)), tbm
    }
    %table hpf_nat_policy_address_range(policyName: byte64, policyType:int2, ruleConSerial: int2, any: int2, except: int2, ipAddr1: int4, ipAddr2: int4) {
        index(0(policyName, policyType, ruleConSerial, any, except, ipAddr1, ipAddr2)), tbm
    }
    %table hpf_nat_policy_service(policyName: byte64, policyType:int2, ruleConSerial: int2, condId: int2, any: int2, except: int2, is_proto: int1, proto: int1,
        srcItemNum: int2, srcPortItems: byte128, dstItemNum: int2, dstPortItems: byte128) {
        index(0(policyName, policyType, ruleConSerial, condId, any, except, is_proto, proto, srcItemNum, srcPortItems, dstItemNum, dstPortItems)), tbm
    }
    %table hpf_nat_policy_interface(policyName: byte64, policyType:int2, ruleConSerial: int2, condId: int2, any: int2, count: int4, ifIdxArray: byte32) {
        index(0(policyName, policyType, ruleConSerial, condId, any, count, ifIdxArray)), tbm
    }
    %table hpf_nat_policy_action(policyName: byte64, policyType:int2, actionType:int2, profileId:int4) {
        index(0(policyName, policyType)), tbm
    }
    %table hpf_nat_policy_action_staticMap(policyName: byte64, policyType:int2, profileId: int4) {
        index(0(policyName, policyType)), tbm
    }
    %table hpf_nat_policy_action_dnat(policyName: byte64, policyType:int2, natType: int2, profileId: int4, dstIp: int4, startPort: int2, endPort: int2) {
        index(0(policyName, policyType)), tbm
    }
    %table hpf_nat_policy_action_nonat(policyName: byte64, policyType:int2) {
        index(0(policyName, policyType)), tbm
    }
    %table hpf_nat_server(trans_type: int1, natsvr_noreverse: int1, sec_len: int4,
        global_vrf_idx: int2, host_vrf_idx: int2, global_ip_start: int4, host_ip_start: int4, global_port_start: int2, host_port_start: int2, natsvr_proto: int1) {
        index(0(trans_type, natsvr_noreverse, sec_len, global_vrf_idx, host_vrf_idx, global_ip_start, host_ip_start, global_port_start, host_port_start, natsvr_proto)),
        tbm
    }
    %table hpf_nat_pool_group(pool_id: int2, type: int1) {
        index(0(pool_id)), tbm
    }
    %table hpf_nat_pool_section(pool_id: int2, sect_id: int2, ip: int4, num: int4) {
        index(0(pool_id, sect_id)), tbm
    }
    %table hpf_nat_pool_exclude_ip(pool_id: int2, start_addr: int4, end_addr: int4) {
        index(0(pool_id, start_addr)), tbm
    }
    %table hpf_nat_pool_exclude_port(pool_id: int2, start_port: int2, end_port: int2) {
        index(0(pool_id, start_port)), tbm
    }
    %table hpf_nat_pool_group_type(pool_id: int2, new_type: int1) {
        index(0(pool_id)), tbm
    }
    %table hpf_nat_arp_enable(index: int4) {
        index(0(index)), tbm
    }
    %table hpf_nat_arp_pool_section(serviceId: int2, startIp: int4, ipNum: int4) {
        index(0(serviceId, startIp)), tbm
    }
    %table hpf_nat_arp_pool_exclude(serviceId: int2, excludeIp: int4, excludeIpNum: int4) {
        index(0(serviceId, excludeIp)), tbm
    }
    %table hpf_nat_host_arp(serviceId: int2, startIp: int4, ipNum: int4) {
        index(0(serviceId, startIp)), tbm
    }
    %table hpf_nat_aspf_config(index: int4, ftp: int1, dns: int1, pptp: int1, rtsp: int1, sip: int1) {
        index(0(index)), tbm
    }
    %table hpf_nat_aging_time(agingType: int2, protocolType: int2, agingTime: int2) {
        index(0(agingType, protocolType)), tbm
    }
    %table hpf_nat_interface_aspf_svc(fwdifIndex: int4) {
        index(0(fwdifIndex)), tbm
    }
    hpf_nat_interface_aspf_svc(fwdifIndex) :-
        Ifm.IfL3(ctrlIfIdx),
        Hpf.ctrlif_map_fwdif(ctrlIfIdx, fwdifIndex).
    %precedence Hpf.hpf_if_name, hpf_nat_interface_aspf_svc
    hpf_nat_policy(policyName, policyType, ruleId):-
        Nat.nat_policy(policyName, policyType, ruleId).
    hpf_nat_policy_address_mask(policyName, policyType, ruleConSerial, any, except, ipAddr, mask):-
        Nat.nat_policy_address_mask(policyName, policyType, ruleConSerial, any, except, ipAddr, mask).
    hpf_nat_policy_address_masklen(policyName, policyType, ruleConSerial, any, except, ipAddr, maskLen):-
        Nat.nat_policy_address_masklen(policyName, policyType, ruleConSerial, any, except, ipAddr, maskLen).
    hpf_nat_policy_address_range(policyName, policyType, ruleConSerial, any, except, ipAddr1, ipAddr2):-
        Nat.nat_policy_address_range(policyName, policyType, ruleConSerial, any, except, ipAddr1, ipAddr2).
    hpf_nat_policy_service(policyName, policyType, ruleConSerial, condId, any, except, is_proto, proto,
        srcItemNum, srcPortItems, dstItemNum, dstPortItems):-
        Nat.nat_policy_service(policyName, policyType, ruleConSerial, condId, any, except, is_proto, proto,
            srcItemNum, srcPortItems, dstItemNum, dstPortItems).
    hpf_nat_policy_interface(policyName, policyType, ruleConSerial, condId, any, count, ifIdxArray):-
        Nat.nat_policy_ifIndex_array(policyName, policyType, ruleConSerial, condId, any, count, ifIdxArray).
    hpf_nat_policy_action(policyName, policyType, actionType, profileId):-
        Nat.nat_policy_action(policyName, policyType, actionType, profileId).
    hpf_nat_policy_action_staticMap(policyName, policyType, profileId):-
        Nat.nat_policy_action_staticMap(policyName, policyType, profileId).
    hpf_nat_policy_action_dnat(policyName, policyType, natType, profileId, dstIp, startPort, endPort):-
        Nat.nat_policy_action_dnat(policyName, policyType, natType, profileId, dstIp, startPort, endPort).
    hpf_nat_policy_action_nonat(policyName, policyType):-
        Nat.nat_policy_action_nonat(policyName, policyType).
    hpf_nat_server(trans_type, natsvr_noreverse, sec_len,
        global_vrf_idx, host_vrf_idx, global_ip_start, host_ip_start, global_port_start, host_port_start, natsvr_proto):-
        Nat.nat_server(trans_type, natsvr_noreverse, sec_len,
            global_vrf_idx, host_vrf_idx, global_ip_start, host_ip_start, global_port_start, host_port_start, natsvr_proto).
    hpf_nat_pool_group(pool_id, type):- Nat.nat_pool_group(pool_id, type, -).
    hpf_nat_pool_section(pool_id, sect_id, ip, num):- Nat.nat_pool_section(pool_id, sect_id, ip, num).
    hpf_nat_pool_exclude_ip(pool_id, start_addr, end_addr):- Nat.nat_pool_exclude_ip(pool_id, start_addr, end_addr).
    hpf_nat_pool_exclude_port(pool_id, start_port, end_port):- Nat.nat_pool_exclude_port(pool_id, start_port, end_port).
    hpf_nat_pool_group_type(pool_id, new_type):- Nat.nat_pool_group_type(pool_id, new_type, -).
    hpf_nat_interface_enable(fwdifIndex, enable):-
        Nat.nat_interface_enable(ifIndex, enable),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdifIndex).
    hpf_nat_arp_enable(0):- Nat.nat_arp_enable(-).
    hpf_nat_arp_pool_section(serviceId, startIp, ipNum):- Nat.nat_arp_pool_section(serviceId, startIp, ipNum).
    hpf_nat_arp_pool_exclude(serviceId, excludeIp, excludeIpNum):- Nat.nat_arp_pool_exclude(serviceId, excludeIp, excludeIpNum).
    hpf_nat_host_arp(serviceId, startIp, ipNum):- Nat.nat_host_arp(serviceId, startIp, ipNum).
    hpf_nat_aspf_config(0, ftp, dns, pptp, rtsp, sip):-
        Nat.nat_aspf_config(-, ftp, dns, pptp, rtsp, sip).
    hpf_nat_aging_time(agingType, protocolType, agingTime):-
        Nat.nat_config_aging_time(agingType, protocolType, agingTime).
    %precedence hpf_nat_policy, hpf_nat_policy_address_mask
    %precedence hpf_nat_policy, hpf_nat_policy_address_masklen
    %precedence hpf_nat_policy, hpf_nat_policy_address_range
    %precedence hpf_nat_policy, hpf_nat_policy_interface
    %precedence hpf_nat_policy, hpf_nat_policy_action
    %precedence hpf_nat_policy, hpf_nat_policy_action_staticMap
    %precedence hpf_nat_policy, hpf_nat_policy_action_dnat
    %precedence hpf_nat_policy, hpf_nat_policy_action_nonat
    %precedence hpf_nat_pool_group, hpf_nat_pool_section
    %precedence hpf_nat_pool_group, hpf_nat_pool_exclude_ip
    %precedence hpf_nat_pool_group, hpf_nat_pool_exclude_port
    %precedence hpf_nat_pool_group, hpf_nat_pool_group_type
    %precedence hpf_nat_pool_section, hpf_nat_policy_action_staticMap
    %precedence hpf_nat_pool_section, hpf_nat_policy_action_dnat
}
namespace Usf {
    %resource AclGroupResource(gidstr: byte16, len: int1 -> gid0: int4) { sequential(max_size(10000)) }
    %aggregate ConvertArray(gids: byte16, len: int1 -> gid: int4, priority: int4) { many_to_many }
    %table AclRuleList(gid0: int4, gid1: int4, priority: int4)
    AclGroupResource(gids, len, -) :- PublicAcl(-, gids, len, -, -), CmpInt1(len, 0, 4).
    AclGroupResource(gids, len, -) :- PublicAcl(-, -, -, gids, len), CmpInt1(len, 0, 4).
    AclRuleList(ggid, gid1, prio) :- AclGroupResource(gids, len, ggid)
        GROUP-BY(ggid) ConvertArray(gids, len, gid1, prio).
    %table vAclGid(hpfGid: int4, ggid: int4) { transient(finish), variant }
    Acl.HpfGroupInUse(hpfGid, 0, 0) :-
        AclGroupResource(-, -, ggid),
        Acl.GetHpfGroupId(1, ggid, hpfGid).
    vAclGid(hpfGid, ggid) :-
        AclRuleList(ggid, -, -),
        Acl.GetHpfGroupId(1, ggid, hpfGid).
    vAclGid(hpfGid, ggid) :-
        Acl.vb_RuleChanged(gid1),
        Acl.GroupCfg(gid1, -, gid, -, -, -, 0, -),
        AclRuleList(ggid, gid, -),
        Acl.GetHpfGroupId(1, ggid, hpfGid).
    Acl.vf_UseHpfGroup(hpfGid) :- vAclGid(hpfGid, -).
    Acl.tf_MappingList(hpfGid, 0, cfgGid, prio, "0x00") :-
        vAclGid(hpfGid, ggid),
        Acl.GroupCfg(cfgGid, -, numGid, -, -, -, 0, -),
        AclRuleList(ggid, numGid, prio).
    %table UsfGlobalAcl(hpfGid: int4, cfgGid: int4, cfgType: int1)
    UsfGlobalAcl(hpfGid, cfgGid, cfgType) :-
        PublicUsfConfig(0, -, -, -, -, -, -, -, -, -, -, -, -, -, -, gid_name, -),
        Acl.GroupCfg(cfgGid, cfgType, -, gid_name, 1, -, -, -),
        Acl.GetHpfGroupId(0, cfgGid, hpfGid).
    Acl.GroupInUseOne(hpfGid, cfgGid, cfgType) :- UsfGlobalAcl(hpfGid, cfgGid, cfgType).
    Hpf.hpf_usf_usr_aclid(mac, 1, hpfGid) :-
        PublicAcl(mac, gids, len, -, -),
        AclGroupResource(gids, len, ggid),
        Acl.GetHpfGroupId(1, ggid, hpfGid).
    Hpf.hpf_usf_usr_aclid(mac, 2, hpfGid) :-
        PublicAcl(mac, -, -, gids, len),
        AclGroupResource(gids, len, ggid),
        Acl.GetHpfGroupId(1, ggid, hpfGid).
    Hpf.hpf_usf_usr_aclid("00", 0, hpfGid) :- UsfGlobalAcl(hpfGid, -, -).
    %precedence Hpf.Hpf_ActivateGroup, Hpf.hpf_usf_usr_aclid
    %readonly StaToVap
    %table StaToVap(mac:byte6, ifIndex:int4)
    StaToVap(mac, ifIndex) :- Usf.PublicSta(mac, -, ifIndex, -, -, -, -, -, -, -, -, -, -).
}
namespace Hpf {
    %table CAP_TABLE_USF_STAT(statId:int4, staMac:byte6, ingStatEn:int1, egrStatEn:int1) {
        index(0(statId)),
        tbm
    }
    %table CAP_TABLE_USF_AUTH(staMac:byte6, authStatus:int1, authVlanEn:int1, authVlan:int2, userVipFlag:int1, ingStatEn:int1,
        egrStatEn:int1, statId:int4, userGroupId:int2, userGroupEn:int1, needSendUpMsg:int1, userPreRoamIpv4Addr:int4,
        innerIsolateEn:int1, interIsolateEn:int1){
        index(0(staMac)),
        tbm
    }
    %table CAP_TABLE_USF_PORTAL(staMac:byte6, portalPushEn:int1, denyPushEn:int1, httpSendToL4:int1, denyAll:int1, preAuth:int1, portalEscape:int1, checkUserIp:int1, userIpv4Addr:int4, denyArpEn:int1){
        index(0(staMac)),
        tbm
    }
    %table CAP_TABLE_USF_GLOBAL(configIndex:int1, ipv6PortalFreeRuleGid:int2, httpsPortalEn:int1, httpsPortalDstPort:int2, daaAclGid:int2, ipv6DaaAclGid:int2, localManageEn:int1, manageIpAddr:int4, manageIpv6Addr:byte16, portalDstIp:int4, portalDstPort:int2, portalEnable:int1, portalDnsDisable:int4, httpPortalPort:byte32, httpPortalPortNum:int1, hostModeEn:int1, staIpv6SvcEnable:int1)
        {index(0(configIndex)), tbm}
    %table CAP_TABLE_USF_ANTI_FLOOD(staMac:byte6, type:int4, carId:int4, enable:int1, blacklistEn: int1)
        {index(0(staMac, type)),
        tbm}
    %table CAP_TABLE_USF_ROAM(mac:byte6, roamRole:int1, interIsolate:int1, innerIsolate:int1, tunnelDstRole:int1,
        usrGroupId:int2, homeVapFwdif:int4, foreignVapFwdif:int4, tunnelId:int4, userIsolate:int1)
        {index(0(mac)),
        tbm}
    %table CAP_TABLE_USF_ROAM_VLAN(brId: int4, vlanId: int2)
        {index(0(brId, vlanId)),
        tbm}
    CAP_TABLE_USF_ROAM_VLAN(brId, vlanId) :-
        BR.Vlan(-, brId, vlanId, -, -, -, -, -, -),
        Usf.PublicROAM(-, -, -, -, -, -, -, -, -, -, vlanId, -, -).
    %table CAP_TABLE_USF_ROAM_AP2STA_ELB( vlanId:int2, ipAddr:byte16, userMac:byte6, userIsolate:int1, hapMac:byte6)
        {
        index(0(vlanId, ipAddr, userMac, userIsolate, hapMac)),
        tbm}
    %table PUBLIC_STATION_TRACE(configIndex:int1, enable: int1, isAll: int1, staNum:int1, staMac:byte60)
        {index(0(configIndex)),tbm}
    %table CAP_TABLE_USF_REMARK(staMac:byte6,
        ing8021pEn: int1, ingDscpEn: int1, ingLpEn: int1,
        ing8021pVal: int4, ingDscpVal: int4, ingLpVal: int4,
        egr8021pEn: int1, egrDscpEn: int1, egrLpEn: int1,
        egr8021pVal: int4, egrDscpVal: int4, egrLpVal: int4)
        {index(0(staMac)),
        tbm}
    %table CAP_TABLE_USF_AUTH_EAP_LOG(staMac: byte6, startParse: int1, upload: int1)
        {index(0(staMac)),tbm}
    %table CAP_TABLE_USF_STA(staMac:byte6, pktCacheType:int1, vapIfIndex:int4, globalIdx:int2, staFlag:int1, peerId:int4, userRoamFlag: int1, pfeVapIdx: int4, sessionInfo: int2, radioId : int1, authId: int4)
        {index(0(staMac)), tbm}
    %readwrite hpf_usf_usr_aclid
    %table hpf_usf_usr_aclid(mac: byte6, type: int1, gid0: int4)
    {index(0(mac)), tbm}
    CAP_TABLE_USF_GLOBAL(_1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, 0, _16):-
        Usf.PublicUsfConfig(_1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, -, _16),
        NOT Wmp.ConfigGlobalHostMode(_1, -).
    CAP_TABLE_USF_GLOBAL(_1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, hostMode, _16):-
        Usf.PublicUsfConfig(_1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12, _13, _14, _15, -, _16),
        Wmp.ConfigGlobalHostMode(_1, hostMode).
    CAP_TABLE_USF_AUTH(staMac, authStatus, authVlanEn, authVlan, userVipFlag, ingStatEn, egrStatEn, statId, userGroupId, userGroupEn, needSendUpMsg, userPreRoamIpv4Addr, innerIsolateEn, interIsolateEn):-
        Usf.PublicAuth(staMac, authStatus, authVlanEn, authVlan, userVipFlag, ingStatEn, egrStatEn, statId, userGroupId, userGroupEn, needSendUpMsg, userPreRoamIpv4Addr, innerIsolateEn, interIsolateEn).
    CAP_TABLE_USF_PORTAL(staMac, portalPushEn, denyPushEn, httpSendToL4, denyAll, preAuth, portalEscape, checkUserIp, userIpv4Addr, denyArpEn):-
        Usf.PublicPortal(staMac, portalPushEn, denyPushEn, httpSendToL4, denyAll, preAuth, portalEscape, checkUserIp, userIpv4Addr, denyArpEn).
    CAP_TABLE_USF_ANTI_FLOOD(staMac, type, carId, 1, blacklistEn):-
        Usf.StaToVap(staMac, ifIndex),
        UsfFloodCarId(staMac, type, usfFloodCarid),
        CarId(6, usfFloodCarid, carId),
        Qos.VapFloodBlacklist(ifIndex, type, blacklistEn).
    CAP_TABLE_USF_STAT(statId, staMac, ingStatEn, egrStatEn):-
        Usf.PublicAuth(staMac, -, -, -, -, ingStatEn, egrStatEn, statId, -, -, -, -, -,-).
    CAP_TABLE_USF_STA(staMac, pktCacheType, vapIfIndex, globalIdx, staFlag, peerId, 1, pfeVapIdx, sessionInfo, radioId, authId):-
        Usf.PublicSta(staMac, pktCacheType, vapIfIndex, globalIdx, staFlag, peerId, pfeVapIdx, sessionInfo, radioId, authId, -, -, -),
        Usf.PublicROAM(staMac, -, -, -, -, -, -, -, -, -, -, -, -).
    CAP_TABLE_USF_STA(staMac, pktCacheType, vapIfIndex, globalIdx, staFlag, peerId, 0, pfeVapIdx, sessionInfo, radioId, authId):-
        Usf.PublicSta(staMac, pktCacheType, vapIfIndex, globalIdx, staFlag, peerId, pfeVapIdx, sessionInfo, radioId, authId, -, -, -),
        NOT Usf.PublicROAM(staMac, -, -, -, -, -, -, -, -, -, -, -, -).
    CAP_TABLE_USF_AUTH_EAP_LOG(staMac, startParse, upload):-
        Usf.PublicSta(staMac, -, -, -, -, -, -, -, -, -, -, startParse, upload).
    CAP_TABLE_USF_ROAM(mac, roamRole, interIsolate, innerIsolate, tunnelDstRole, usrGroupId, homeVapFwdif, foreignVapFwdif, tunnelId, userIsolate):-
        Usf.PublicROAM(mac, roamRole, interIsolate, innerIsolate, tunnelDstRole, usrGroupId, homeVapFwdif, foreignVapFwdif, tunnelId, userIsolate, -, -, -).
    %function ShouldApToSta(vlanId: int2, ipAddr: byte16)
    CAP_TABLE_USF_ROAM_AP2STA_ELB(vlanId, ipAddr, mac, userIsolate, hapMac):-
        Usf.PublicROAM(mac, -, -, -, -, -, -, -, -, userIsolate, vlanId, ipAddr, hapMac),
        ShouldApToSta(vlanId, ipAddr).
    %precedence CAP_TABLE_USF_STA, CAP_TABLE_USF_STAT, CAP_TABLE_USF_AUTH
    PUBLIC_STATION_TRACE(configIndex, enable, isAll, staNum, staMac) :-
        Usf.PublicStationTrace(configIndex, enable, isAll, staNum, staMac).
    %table CAP_TABLE_USF_CAR(staMac:byte6, direction:int1, carId:int4, enable:int1)
        {index(0(staMac, direction)),
        tbm}
    %resource UsfCarId(mac: byte6, direction: int1 -> carId: int4) { sequential(max_size(32742)) }
    UsfCarId(mac, 0, -) :- Usf.IngCar(mac, -, -, -, -, -, -, -, -, -, -).
    UsfCarId(mac, 1, -) :- Usf.EgrCar(mac, -, -, -, -, -, -, -, -, -, -).
    CarId(0, id, -) :- UsfCarId(-, -, id).
    CAP_TABLE_CAR(carId, 2,
        _1, _2, _3, _5, _7, _4, _5, _6, _7, _8, 0, 0, _9, 0, 0, _10, 0, 0) :-
        Usf.IngCar(mac, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10),
        UsfCarId(mac, 0, id),
        CarId(0, id, carId).
    CAP_TABLE_CAR(carId, 2,
        _1, _2, _3, _5, _7, _4, _5, _6, _7, _8, 0, 0, _9, 0, 0, _10, 0, 0) :-
        Usf.EgrCar(mac, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10),
        UsfCarId(mac, 1, id),
        CarId(0, id, carId).
    %resource VapUsfCarId(mac: byte6, direction: int1 -> carId: int4) { sequential(max_size(1536)) }
    VapUsfCarId(mac, direction, -) :-
        Qos.VapUserCar(ifIndex, direction, -),
        Usf.StaToVap(mac, ifIndex).
    CarId(9, id, -) :- VapUsfCarId(-, -, id).
    CAP_TABLE_CAR(carId, 2,
        0, 1, 0, cbs, 0, cir, cbs, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0) :-
        Qos.VapUserCar(ifIndex, 0, cir),
        CirToCbs(cir, cbs),
        VapUsfCarId(mac, 0, id),
        Usf.StaToVap(mac, ifIndex),
        CarId(9, id, carId).
    CAP_TABLE_CAR(carId, 2,
        0, 1, 0, cbs, 0, cir, cbs, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0) :-
        Qos.VapUserCar(ifIndex, 1, cir),
        CirToCbs(cir, cbs),
        VapUsfCarId(mac, 1, id),
        Usf.StaToVap(mac, ifIndex),
        CarId(9, id, carId).
    CAP_TABLE_USF_CAR(mac, direction, carId, 1) :-
        UsfCarId(mac, direction, id),
        CarId(0, id, carId),
        NOT VapUsfCarId(mac, direction, -).
    CAP_TABLE_USF_CAR(mac, direction, carId, 1) :-
        VapUsfCarId(mac, direction, id),
        CarId(9, id, carId),
        NOT UsfCarId(mac, direction, -).
    CAP_TABLE_USF_CAR(mac, 0, carId, 1) :-
        VapUsfCarId(mac, 0, vapCarId),
        CarId(9, vapCarId, carId),
        Usf.IngCar(mac, -, -, -, 0, -, -, -, -, -, -).
    CAP_TABLE_USF_CAR(mac, 1, carId, 1) :-
        VapUsfCarId(mac, 1, vapCarId),
        CarId(9, vapCarId, carId),
        Usf.EgrCar(mac, -, -, -, 0, -, -, -, -, -, -).
    CAP_TABLE_USF_CAR(mac, 0, carId, 1) :-
        VapUsfCarId(mac, 0, vapCarId),
        Usf.StaToVap(mac, ifIndex),
        CarId(9, vapCarId, carId),
        Usf.IngCar(mac, -, -, -, authCir, -, -, -, -, -, -),
        Qos.VapUserCar(ifIndex, 0, vapCir),
        CmpInt8(vapCir, authCir, 1).
    CAP_TABLE_USF_CAR(mac, 0, carId, 1) :-
        UsfCarId(mac, 0, authCarId),
        CarId(0, authCarId, carId),
        Usf.IngCar(mac, -, -, -, authCir, -, -, -, -, -, -),
        Qos.VapUserCar(ifIndex, 0, vapCir),
        Usf.StaToVap(mac, ifIndex),
        CmpInt8(vapCir, authCir, 6).
    CAP_TABLE_USF_CAR(mac, 1, carId, 1) :-
        VapUsfCarId(mac, 1, vapCarId),
        Usf.StaToVap(mac, ifIndex),
        CarId(9, vapCarId, carId),
        Usf.EgrCar(mac, -, -, -, authCir, -, -, -, -, -, -),
        Qos.VapUserCar(ifIndex, 1, vapCir),
        CmpInt8(vapCir, authCir, 1).
    CAP_TABLE_USF_CAR(mac, 1, carId, 1) :-
        UsfCarId(mac, 1, authCarId),
        CarId(0, authCarId, carId),
        Usf.EgrCar(mac, -, -, -, authCir, -, -, -, -, -, -),
        Qos.VapUserCar(ifIndex, 1, vapCir),
        Usf.StaToVap(mac, ifIndex),
        CmpInt8(vapCir, authCir, 6).
    CAP_TABLE_USF_REMARK(stamac, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12) :-
        Qos.UserRemark(stamac, _1, _2, _3, _4, _5, _6, _7, _8, _9, _10, _11, _12).
    %precedence CAP_TABLE_USF_AUTH, CAP_TABLE_USF_AUTH_EAP_LOG
    %precedence CAP_TABLE_USF_STA, CAP_TABLE_USF_AUTH_EAP_LOG
    %precedence CAP_TABLE_USF_STA, CAP_TABLE_USF_ANTI_FLOOD
    %precedence CAP_TABLE_USF_STA, CAP_TABLE_USF_PORTAL
    %precedence CAP_TABLE_USF_STA, CAP_TABLE_USF_ROAM
    %precedence CAP_TABLE_USF_STA, CAP_TABLE_USF_REMARK
    %precedence CAP_TABLE_VLAN, CAP_TABLE_USF_ROAM_VLAN
}
namespace Hpf {
    %table hpf_macip_ip(mac: byte6, ipAddr: int4, learnType: int1) {
        index(0(ipAddr)),
        tbm
    }
    %table hpf_macip_mac(mac: byte6, ipAddr: int4, learnType: int1) {
        index(0(mac)),
        tbm
    }
    %table hpf_mac_ipv6(mac: byte6, ipv6Addr: byte16, learnType: int1) {
        index(0(ipv6Addr)),
        tbm
    }
    %table hpf_macipv6_mac(mac: byte6, ipv6Addr: byte16, learnType: int1) {
        index(0(mac)),
        tbm
    }
    hpf_macip_ip(mac, ipAddr, learnType) :-
        Wmp.ConfigMacIp(mac, ipAddr, learnType).
    hpf_macip_mac(mac, ipAddr, learnType) :-
        Wmp.ConfigMacIp(mac, ipAddr, learnType).
    hpf_mac_ipv6(mac, ipv6Addr, learnType) :-
        Wmp.ConfigMacIpv6(mac, ipv6Addr, learnType).
    hpf_macipv6_mac(mac, ipv6Addr, learnType) :-
        Wmp.ConfigMacIpv6(mac, ipv6Addr, learnType).
}
namespace Hpf {
    %table hpf_qos_pri(vapIndex: int4, type: int1, val: byte64) {
        index(0(vapIndex, type)),
        tbm
    }
    %table hpf_if_pri_en(
        fwdIfIdx: int4, ingUp2CosEn: int1, ingUp2TosEn: int1, ingUpTo8021pEn: int1, ingDscpTo8021pEn: int1,
        ingSvpPriEn: int1, ingUp2TnlDscpEn: int1, ingUp2Tnl8021pEn: int1, ingDscpToTnl8021pEn: int1,
        ingDscpToTnlDscpEn: int1, up2CosMode: int1, up2CosFixVal: int4, egrTos2UpEn: int1,
        egrCos2Up: int1, egr8021p2Up: int1, egrDscp2Up: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    hpf_qos_pri(vapIndex_int4, type, val) :-
        Wmp.ConfigPriority(vapIndex_int4, type, val),
        Ifm.ConfigVapIf(-, -, -, -, -, vapIndex_int1, -, -,
            -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -),
        I1ToI4(vapIndex_int1, vapIndex_int4).
    hpf_if_pri_en(
        fwdIfIdx, ingUp2CosEn, ingUp2TosEn, ingUpTo8021pEn, ingDscpTo8021pEn,
        ingSvpPriEn, ingUp2TnlDscpEn, ingUp2Tnl8021pEn, ingDscpToTnl8021pEn,
        ingDscpToTnlDscpEn, up2CosMode, up2CosFixVal, egrTos2UpEn,
        egrCos2Up, egr8021p2Up, egrDscp2Up) :-
        Wmp.ConfigPriEn(
            ifIndex, ingUp2CosEn, ingUp2TosEn, ingUpTo8021pEn, ingDscpTo8021pEn,
            ingSvpPriEn, ingUp2TnlDscpEn, ingUp2Tnl8021pEn, ingDscpToTnl8021pEn,
            ingDscpToTnlDscpEn, up2CosMode, up2CosFixVal, egrTos2UpEn,
            egrCos2Up, egr8021p2Up, egrDscp2Up),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    %precedence hpf_if_name, hpf_if_pri_en
}
namespace Hpf {
    %table qos_mng_port(protoType: int1, port: int2) {
        index(0(protoType)),
        tbm
    }
    %table hpf_if_interface_bcmc_car(fwdIfIdx: int4, type:int4, carId:int4) {
        index(0(fwdIfIdx, type)),
        tbm
    }
    %table hpf_if_vap_car(fwdIfIdx: int4, dir:int4, carId:int4) {
        index(0(fwdIfIdx, dir)),
        tbm
    }
    %table hpf_if_global_bcmc_car(type:int4, carId:int4) {
        index(0(type)),
        tbm
    }
    %table hpf_vap_to_sta(fwdIfIdx: int4, vlanId: int2, staMac: byte6) {
        index(0(fwdIfIdx, vlanId, staMac)),
        tbm
    }
    %table hpf_bcmc_to_uc(fwdIfIdx: int4, type: int4, toUcEn: int1) {
        index(0(fwdIfIdx, type)),
        tbm
    }
    %table hpf_bcmc_deny(fwdIfIdx: int4, mcbcDeny: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_mdns_deny(fwdIfIdx: int4, mdnsDeny: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_bcmc_miss_drop(fwdIfIdx: int4, drop: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_arp_reply(fwdIfIdx: int4, arpReplyEn: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_arp_proxy(fwdIfIdx: int4, arpProxyEn: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    %table hpf_nd_proxy(fwdIfIdx: int4, ndProxyEn: int1) {
        index(0(fwdIfIdx)),
        tbm
    }
    qos_mng_port(protoType, port) :-
        Qos.GlobalMngPort(protoType, port).
    %resource UsfFloodCarId(staMac: byte6, type: int4 -> carId: int4) { sequential(max_size(32742)), index(1(carId)) }
    UsfFloodCarId(staMac, type, -) :-
        Usf.StaToVap(staMac, ifIndex),
        Qos.VapFloodThreshold(ifIndex, type, -).
    CarId(6, id, -) :- UsfFloodCarId(-, -, id).
    CAP_TABLE_CAR(carId, 1,
        0, 0, 0, p, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0) :-
        Qos.VapFloodThreshold(ifIndex, type, pps),
        Usf.StaToVap(staMac, ifIndex),
        UsfFloodCarId(staMac, type, id),
        CarId(6, id, carId),
        I4ToI8(pps, p).
    %resource InterFaceMcBcCarId(ifIndex: int4, type: int4 -> carId: int4) { sequential(max_size(32742)) }
    InterFaceMcBcCarId(ifIndex, type, -) :- Qos.InterFaceMcBcCar(ifIndex, type, -).
    CarId(7, id, -) :- InterFaceMcBcCarId(-, -, id).
    CAP_TABLE_CAR(carId, 1,
        0, 0, 0, p, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0) :-
        Qos.InterFaceMcBcCar(ifIndex, type, pps),
        InterFaceMcBcCarId(ifIndex, type, id),
        CarId(7, id, carId),
        I4ToI8(pps, p).
    hpf_if_interface_bcmc_car(fwdIfIdx, type, carId):-
        InterFaceMcBcCarId(ifIndex, type, interFaceMcBcCarId),
        CarId(7, interFaceMcBcCarId, carId),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    %resource VapCarId(ifIndex: int4, direction: int4 -> carId: int4) { sequential(max_size(32742)) }
    VapCarId(ifIndex, direction, -) :- Qos.VapCar(ifIndex, direction, -).
    CarId(11, id, -) :- VapCarId(-, -, id).
    CAP_TABLE_CAR(carId, 4,
        0, 1, 0, cbs, 0, cir, cbs, 0,
        0, 1, 0, 0, 1, 0, 0, 0, 0, 0) :-
        Qos.VapCar(ifIndex, direction, cir),
        CirToCbs(cir, cbs),
        VapCarId(ifIndex, direction, id),
        CarId(11, id, carId).
    hpf_if_vap_car(fwdIfIdx, direction, carId):-
        VapCarId(ifIndex, direction, vapCarId),
        CarId(11, vapCarId, carId),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    CarId(8, type, -) :- Qos.GlobalMcBcCar(type, -).
    CAP_TABLE_CAR(carId, 1,
        0, 0, 0, p, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0) :-
        Qos.GlobalMcBcCar(type, pps),
        CarId(8, type, carId),
        I4ToI8(pps, p).
    hpf_if_global_bcmc_car(type, carId):-
        CarId(8, type, carId).
    hpf_vap_to_sta(fwdIfIdx, vlanId, staMac) :-
        BR.VlanPort(-, vlanId, ifIndex),
        Usf.StaToVap(staMac, ifIndex),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_bcmc_to_uc(fwdIfIdx, type, toUcEn) :-
        Qos.InterfaceMcBc2Uc(ifIndex, type, toUcEn),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_bcmc_deny(fwdIfIdx, mcbcDeny) :-
        Qos.InterfaceMcBcDeny(ifIndex, mcbcDeny),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_mdns_deny(fwdIfIdx, mdnsDeny) :-
        Qos.InterfaceMdnsDeny(ifIndex, mdnsDeny),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_bcmc_miss_drop(fwdIfIdx, drop) :-
        Qos.InterfaceMcBc2UcMissDrop(ifIndex, drop),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_arp_reply(fwdIfIdx, arpReplyEn) :-
        Arp.IfArpReplay(ifIndex, arpReplyEn),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_arp_proxy(fwdIfIdx, arpProxyEn) :-
        Arp.IfArpProxy(ifIndex, arpProxyEn),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_nd_proxy(fwdIfIdx, ndProxyEn) :-
        Nd.IfNdProxy(ifIndex, ndProxyEn),
        Hpf.ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    %precedence Hpf.hpf_if_name, hpf_if_interface_bcmc_car
    %precedence Hpf.hpf_if_name, hpf_if_vap_car
    %precedence Hpf.hpf_if_name, hpf_bcmc_to_uc
    %precedence Hpf.hpf_if_name, hpf_bcmc_deny
    %precedence hpf_bcmc_deny, hpf_mdns_deny
    %precedence Hpf.hpf_if_name, hpf_bcmc_miss_drop
    %precedence Hpf.hpf_if_name, hpf_arp_reply, hpf_arp_proxy, hpf_nd_proxy
}
namespace Hpf {
    %table CAP_TABLE_SSW_MC_GLOBAL_ENABLE(nsId: int4) { index(0(nsId)), tbm }
    %table CAP_TABLE_SSW_MC_IF_ENABLE(fwdIfIdx: int4) { index(0(fwdIfIdx)), tbm }
    %table CAP_TABLE_SSW_MC_ROUTE_PORT_ELB(brId: int4, vlanId: int2, fwdIfIdx: int4) {
        index(0(brId, vlanId, fwdIfIdx)), tbm
    }
    %table CAP_TABLE_SSW_MC_ELB(brId: int4, vlanId: int2, groupAddr: int4, sourceAddr: int4, fwdIfIdx: int4) {
        index(0(brId, vlanId, groupAddr, sourceAddr, fwdIfIdx)), tbm
    }
    %table CAP_TABLE_SSW_EXCLUDE_MC_ELB(brId: int4, vlanId: int2, fwdIfIdx: int4) {
        index(0(brId, vlanId, fwdIfIdx)), tbm
    }
    %table CAP_TABLE_SSW_MC2UC_ELB(brId: int4, fwdIfIdx:int4, vlanId:int2, groupAddr:int4, sourceAddr:int4, usrMac:byte6) {
        index(0(brId, fwdIfIdx, vlanId, groupAddr, sourceAddr, usrMac)), tbm
    }
    %table CAP_TABLE_SSW_MC2UC_IF_ENABLE(fwdIfIdx:int4) { index(0(fwdIfIdx)), tbm }
    %table CAP_TABLE_MFIB_DUMMY(brId: int2, vlanId: int2, groupAddr: int4, sourceAddr: int4, state: int1) {
        index(0(brId, vlanId, groupAddr, sourceAddr)) , tbm
    }
    CAP_TABLE_MFIB_DUMMY(0, vlanId, groupAddr, sourceAddr, 0) :-
        Igmpsnp.McFwdTbl(vlanId, groupAddr, sourceAddr, -).
    CAP_TABLE_SSW_MC_IF_ENABLE(0) :- tbl_init(12345).
}
namespace Hpf {
    %table CAP_TABLE_SSW_MC_IGMPSNP_IF(fwdIfIdx: int4)
    CAP_TABLE_SSW_MC_IGMPSNP_IF(fwdIfIdx) :-
        Igmpsnp.IgmpSnooping(ctrlIfIdx),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    %table CAP_TABLE_SSW_MC_IF_SNP_COUNT(notUse: int4)
    CAP_TABLE_SSW_MC_IF_SNP_COUNT(0) :- Igmpsnp.IgmpSnooping(-).
    %table CAP_TABLE_SSW_ELB_TEMP(brId: int4, vlanId: int2, fwdIfIdx: int4)
    CAP_TABLE_SSW_ELB_TEMP(0, vlanId, fwdIfIdx) :-
        CAP_TABLE_SSW_MC_IF_SNP_COUNT(-),
        BR.VlanPort(-, vlanId, ctrlIfIdx),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    %table CAP_TABLE_SSW_MC_UP_LINK_PORT(ctrlIfIdx: int4)
    %table CAP_TABLE_SSW_MC_GE_PORT(ctrlIfIdx: int4)
    CAP_TABLE_SSW_MC_GE_PORT(ctrlIfIdx) :-
        Ifm.IfName(ctrlIfIdx, -, -, ifType, -, -, -, -),
        GetHpfIfType(ifType, hpfIfType),
        CmpInt4(hpfIfType, 0, 2).
    CAP_TABLE_SSW_MC_UP_LINK_PORT(ctrlIfIdx) :-
        CAP_TABLE_SSW_MC_GE_PORT(ctrlIfIdx),
        NOT Ifm.EthTrunkMembers(ctrlIfIdx, -, -, -, -),
        NOT Ifm.ConfigLinkUpDownMode(ctrlIfIdx, 1).
    CAP_TABLE_SSW_MC_UP_LINK_PORT(ctrlIfIdx) :-
        Ifm.IfName(ctrlIfIdx, -, -, ifType, -, -, -, -),
        GetHpfIfType(ifType, hpfIfType),
        CmpInt4(hpfIfType, 23, 2).
    CAP_TABLE_SSW_MC_UP_LINK_PORT(ctrlIfIdx) :-
        Ifm.EthTrunk(ctrlIfIdx, -, -, -).
    %table CAP_TABLE_SSW_MC_ELB_TEMP(brId: int4, vlanId: int2, groupAddr: int4, sourceAddr: int4, fwdIfIdx: int4)
    CAP_TABLE_SSW_MC_ELB_TEMP(0, vlanId, groupAddr, sourceAddr, fwdIfIdx) :-
        CAP_TABLE_SSW_MC_IF_SNP_COUNT(-),
        Igmpsnp.IgmpFib(vlanId, groupAddr, sourceAddr, ctrlIfIdx, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_SSW_MC_GLOBAL_ENABLE(0) :- CAP_TABLE_SSW_MC_IF_SNP_COUNT(-).
    CAP_TABLE_SSW_MC_IF_ENABLE(fwdIfIdx) :- CAP_TABLE_SSW_MC_IGMPSNP_IF(fwdIfIdx).
    CAP_TABLE_SSW_MC_ROUTE_PORT_ELB(0, vlanId, fwdIfIdx) :-
        CAP_TABLE_SSW_MC_UP_LINK_PORT(ctrlIfIdx),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx),
        CAP_TABLE_SSW_ELB_TEMP(0, vlanId, fwdIfIdx).
    CAP_TABLE_SSW_MC_ELB(0, vlanId, groupAddr, sourceAddr, fwdIfIdx) :-
        CAP_TABLE_SSW_MC_ELB_TEMP(0, vlanId, groupAddr, sourceAddr, fwdIfIdx).
    CAP_TABLE_SSW_EXCLUDE_MC_ELB(0, vlanId, fwdIfIdx) :-
        CAP_TABLE_SSW_ELB_TEMP(0, vlanId, fwdIfIdx),
        NOT CAP_TABLE_SSW_MC_IGMPSNP_IF(fwdIfIdx).
    CAP_TABLE_SSW_MC2UC_ELB(0, fwdIfIdx, vlanId, groupAddr, sourceAddr, usrMac) :-
        CAP_TABLE_SSW_MC_IF_SNP_COUNT(-),
        Igmpsnp.IgmpMc2uc(ctrlIfIdx, 1),
        Igmpsnp.IgmpFib(vlanId, groupAddr, sourceAddr, ctrlIfIdx, usrMac),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_SSW_MC2UC_IF_ENABLE(fwdIfIdx) :-
        CAP_TABLE_SSW_MC_IF_SNP_COUNT(-),
        Igmpsnp.IgmpMc2uc(ctrlIfIdx, 1),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    %precedence hpf_if_name, CAP_TABLE_SSW_MC_IF_ENABLE
    %precedence hpf_if_name, CAP_TABLE_SSW_MC2UC_IF_ENABLE
}
namespace Hpf {
    %readonly hpf_if_dhcp_snp_bind, hpf_if_dhcp, hpf_if_trust
    %table hpf_if_dhcp_snp_bind(macAddr: byte6, ipv4Addr: int4, vlanId: int2, fwdIfIdx: int4, bindType: int4, ipv6Addr: byte16, isPrefix: int1, prefixLen: int1, protoType: int1, clientRequestTime: int4) {
        index(0(macAddr, ipv4Addr, vlanId, fwdIfIdx)),
        tbm
    }
    %table hpf_if_dhcp(fwdIfIdx: int4, protocolType:int4) { index(0(fwdIfIdx, protocolType)), tbm }
    %table hpf_vlan_dhcp_enable(brId: int4, vlanId: int2, protocolType:int4) {
        index(0(brId, vlanId)), tbm
    }
    %table hpf_if_trust(fwdIfIdx: int4, trust: int1) { index(0(fwdIfIdx)), tbm }
    %function IsDhcp(protocolType:int4)
    hpf_if_dhcp(fwdIfIdx, protocolType) :-
        Hsec.ProtocolEnable(protocolType, -, ifIndex),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        IsDhcp(protocolType).
    hpf_vlan_dhcp_enable(0, vlanId, protocolType) :-
        Hsec.ProtocolEnableForVlan(protocolType, -, vlanId),
        IsDhcp(protocolType).
    hpf_if_trust(fwdIfIdx, trust) :-
        Dhcpsnp.TrustIf(ifIndex, trust),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_if_dhcp_snp_bind(macAddr, ipv4Addr, vlanId, -1, 0, ipv6Addr, isPrefix, prefixLen, protoType, 0) :-
        Dhcpsnp.StaticUserBind(macAddr, ipv4Addr, vlanId, -1, -, ipv6Addr, isPrefix, prefixLen, protoType).
    hpf_if_dhcp_snp_bind(macAddr, ipv4Addr, vlanId, fwdIfIdx, 1, ipv6Addr, isPrefix, prefixLen, protoType, clientRequestTime) :-
        Dhcpsnp.DynamicUserBind(macAddr, ipv4Addr, vlanId, ifIndex, -, -, -, ipv6Addr, isPrefix, prefixLen, protoType, clientRequestTime),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    %precedence Hpf.hpf_if_name, hpf_if_dhcp
    %precedence Hpf.hpf_if_name, hpf_if_trust
    %precedence CAP_TABLE_VLAN, hpf_vlan_dhcp_enable
}
namespace Hpf {
    %readonly hpf_dns_snp_global_enable, hpf_dns_domain, hpf_dns_global_deny_req_from_wan_enable
    %table hpf_dns_snp_global_enable(noUse: int4) { index(0(noUse)), tbm }
    %table hpf_dns_domain(domain:byte128, domainMatchType:int1, domainID:int1) { index(0(domain, domainMatchType)), tbm }
    hpf_dns_snp_global_enable(noUse) :-
        Dnssnp.DnsSnooping(noUse).
    hpf_dns_domain(domain, domainMatchType, domainID) :-
        Dnssnp.DnsDomain(domain, domainMatchType, domainID).
}
namespace Hpf {
    %readonly SacLoad, hpf_sac_profile, hpf_sac_if, hpf_sac_usf, hpf_sac_remark_default_if, hpf_sac_remark_default_usf
    %table SacLoad(nsId: int1, path: str) {
        index(0(nsId)),
        tbm
    }
    %resource sac_profile_id(sacProfileData: byte388 -> sacId: int4) { sequential(max_size(64)) }
    sac_profile_id(sacProfileData, -) :- Sac.SacProfileIf(-, sacProfileData).
    sac_profile_id(sacProfileData, -) :- Sac.SacProfileUsf(-, -, sacProfileData).
    %table hpf_sac_profile(sacId: int4, sacProfileData: byte388) { index(0(sacId)), tbm }
    %table hpf_sac_if(sacId: int4, fwdIfIdx: int4) { index(0(sacId, fwdIfIdx)), tbm }
    %table hpf_sac_usf(sacId: int4, mac: byte6, fwdIfIdx: int4) { index(0(sacId, mac)), tbm }
    %table hpf_sac_remark_default_if(fwdIfIdx: int4, remarkDefaultEn: int1) { index(0(fwdIfIdx)), tbm }
    %table hpf_sac_remark_default_usf(mac: byte6, remarkDefaultEn: int1) { index(0(mac)), tbm }
    SacLoad(nsId, path) :- Sac.SacLoad(nsId, path).
    hpf_sac_profile(sacId, sacProfileData) :-
        sac_profile_id(sacProfileData, sacId).
    hpf_sac_if(sacId, fwdIfIdx) :-
        Sac.SacProfileIf(ifIndex, sacProfileData),
        sac_profile_id(sacProfileData, sacId),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_sac_usf(sacId, mac, fwdIfIdx) :-
        Sac.SacProfileUsf(mac, ifIndex, sacProfileData),
        sac_profile_id(sacProfileData, sacId),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_sac_remark_default_if(fwdIfIdx, remarkDefaultEn) :-
        Sac.SacRemarkDefaultIf(ifIndex, remarkDefaultEn),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    hpf_sac_remark_default_usf(mac, remarkDefaultEn) :-
        Sac.SacRemarkDefaultUsf(mac, remarkDefaultEn).
}
namespace Hpf {
    %readonly hpf_if_nd_snp, hpf_if_nd_trust
    %table hpf_if_nd_snp(ifIndex: int4, fwdIfIdx: int4, protocolType:int4) { index(0(ifIndex, fwdIfIdx, protocolType)), tbm }
    %table hpf_if_nd_trust(fwdIfIdx: int4, trust: int1) { index(0(fwdIfIdx)), tbm }
    %function IsNdSnp(protocolType:int4)
    hpf_if_nd_snp(-1, -1, protocolType) :-
        Hsec.ProtocolEnable(protocolType, -, -1),
        IsNdSnp(protocolType).
    hpf_if_nd_snp(ifIndex, fwdIfIdx, protocolType) :-
        Hsec.ProtocolEnable(protocolType, -, ifIndex),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx),
        IsNdSnp(protocolType).
    hpf_if_nd_trust(fwdIfIdx, trust) :-
        Ndsnp.TrustIf(ifIndex, trust),
        ctrlif_map_fwdif(ifIndex, fwdIfIdx).
    %precedence Hpf.hpf_if_name, hpf_if_nd_snp
    %precedence Hpf.hpf_if_name, hpf_if_nd_trust
}
product_um(0) :- tbl_init(-).
null(0):-product_s380(-).
null(0):-product_s310s(-).
null(0):-product_wlan(-).
null(0):-product_um(-).
null(0) :- Ifm.EthTrunkMacSyncPort(-, -, -, -).
null(0) :- Mir.PortMirror(-,-,-).
null(0) :- Mir.MirrorObserve(-, -).
null(0) :- Mir.MirrorMirror(-, -, -).
null(0) :- BR.MstpIfDynMacFlush(-, -).
null(0) :- BR.PortSecurity(-,-,-,-,-,-).
null(0) :- BR.Instance(-, -, -, -, -).
null(0) :- BR.PortInstanceState(-, -, -).
null(0) :- BR.PortInstanceFlush(-, -).
null(0) :- BR.ErpsRule(-, -, -, -, -, -, -, -).
null(0) :- BR.ErpsRingPort(-, -, -, -, -, -, -, -).
null(0) :- BR.ErpsRingInstance(-, -, -).
null(0) :- BR.ErpsRingInstanceBitmap(-, -).
null(0) :- BR.ErpsRingSFPktTemplate(-, -, -, -, -, -).
null(0) :- BR.ErpsRingBullet(-, -, -).
null(0) :- Hsec.StormControlRate(-,-,-).
null(0) :- Hsec.StormControlAction(-,-).
null(0) :- Hsec.CpcarAttr(-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-,-).
null(0) :- Hsec.TcpSession(-,-,-,-,-,-).
null(0) :- Hsec.Cause(-, -, -, -, -, -, -).
null(0):-Igmpsnp.GlobalState(-,-).
null(0):-Igmpsnp.VlanState(-,-).
null(0):-Igmpsnp.McFwdTbl(-,-,-,-).
null(0):-Igmpsnp.RoutePort(-,-).
null(0):-Igmpsnp.RoutePortTransPacket(-).
Hsec.CpCarIdForLsw(0, 0, 0) :- tbl_init(12345).
BR.PortVlanFwdState(0, 0, 0) :- tbl_init(12345).
BR.VlanTopChange(0) :- tbl_init(12345).
Igmpsnp.MFibTrafficStatisticsId(0, 0, 0, 0, 0, 0) :- tbl_init(12345).
namespace PCMU {
%aggregate GetSubRadioModeFromTemp(subRd: int4, subRdstr: str, isEnable: int4, poePower: int4, femVol: int4, alarmPol: int4, cpuFreq: int8,
    cpuTemperature: int4, envTemperature: int4, irfTemperature: str, exceedTemperature: str, declineTemperature: str
     -> subRadioMode: int4, subRadioModestr: str, poeoutPower: int4, cpuFrequency: int8, femVoltage: int4, alarmPolicy: int4, exceedTemp: str, declineTemp: str) {ordered}
%table Power(apType: str, powerMode: int4) { index(0(apType)), update_partial }
%table RadioMode(apType: str, masterRadioMode: int4) { index(0(apType)), update_partial }
%table DevmDetect(apType: str, cpuTemperature: int4, envTemperature: int4) { index(0(apType)), update_partial }
%table IRFTemperature(apType: str, irfTemperature: str) { index(0(apType)), update_partial }
%table Poeout(apType: str, isEnable: int4) { index(0(apType)), update_partial }
%table PowerPolicy(apType: str, masterRadioMode: int4, powerMode: int4, isEnable: int4, poePower: int4, femVol: int4, alarmPol: int4, cpuFreq: int8)
{ index(0(apType, powerMode, isEnable)) }
%table SubRadioModePolicy(apType: str, powerMode: int4, subRd: int4, subRdstr: str, exceedTemperature: str, declineTemperature: str)
{ index(0(apType, powerMode, subRd)) }
%table SubRadioModeDealTable(apType: str, subRd: int4, subRdstr: str, exceedTemperature: str, declineTemperature: str,
    cpuTemperature: int4, envTemperature: int4, irfTemperature: str, isEnable: int4, poePower: int4, femVol: int4, alarmPol: int4, cpuFreq: int8)
{ index(0(apType, subRd)) }
%table EnergyResultMid(apType: str, subRadioMode: int4, subRadioModestr: str, poeoutPower: int4, cpuFrequency: int8, femVoltage: int4, alarmPolicy: int4, exceedTemp: str, declineTemp: str)
{ index(0(apType)) }
%table EnergyResult(apType: str, subRadioMode: int4, subRadioModestr: str, poeoutPower: int4, cpuFrequency: int8, femVoltage: int4, alarmPolicy: int4, exceedTemp: str, declineTemp: str)
{ index(0(apType)), external }
SubRadioModeDealTable(apType, subRd, subRdstr, exceedTemperature, declineTemperature, cpuTemperature, envTemperature, irfTemperature, isEnable, poePower, femVol, alarmPol, cpuFreq) :-
    Power(apType, powerMode),
    RadioMode(apType, masterRadioMode),
    DevmDetect(apType, cpuTemperature, envTemperature),
    IRFTemperature(apType, irfTemperature),
    Poeout(apType, isEnable),
    PowerPolicy(apType, masterRadioMode, powerMode, isEnable, poePower, femVol, alarmPol, cpuFreq),
    SubRadioModePolicy(apType, powerMode, subRd, subRdstr, exceedTemperature, declineTemperature).
EnergyResultMid(apType, subRadioMode, subRadioModestr, poeoutPower, cpuFrequency, femVoltage, alarmPolicy, exceedTemp, declineTemp) :-
    SubRadioModeDealTable(apType, subRd, subRdstr, exceedTemperature, declineTemperature,
        cpuTemperature, envTemperature, irfTemperature, isEnable, poePower, femVol, alarmPol, cpuFreq) GROUP-BY(apType)
    GetSubRadioModeFromTemp(subRd, subRdstr, isEnable, poePower, femVol, alarmPol, cpuFreq,
        cpuTemperature, envTemperature, irfTemperature, exceedTemperature, declineTemperature,
        subRadioMode, subRadioModestr, poeoutPower, cpuFrequency, femVoltage, alarmPolicy, exceedTemp, declineTemp).
EnergyResult(apType, subRadioMode, subRadioModestr, poeoutPower, cpuFrequency, femVoltage, alarmPolicy, exceedTemp, declineTemp) :-
    EnergyResultMid(apType, subRadioMode, subRadioModestr, poeoutPower, cpuFrequency, femVoltage, alarmPolicy, exceedTemp, declineTemp).
%aggregate GetEnergyResultIfFromPowerMode(portSpeedPolicy: int4, powerifIndex: int4, portIndex: str, portCurStatus: str, portType: str -> ifIndex: int4, portStatus: int1, portSpeed:int8)
{ many_to_many }
%table PortPowerSelect(apType: str, powerifIndex: int4) { index(0(apType)), update_partial }
%table PortCurStatus(apType: str, portIndex: str, portCurStatus: str, portType: str) { index(0(apType)), update_partial }
%table PortPolicy(apType: str, powerMode: int4, portSpeedPolicy: int4) { index(0(apType, powerMode)) }
%table PortDealTable(apType: str, portSpeedPolicy: int4, powerifIndex: int4, portIndex: str, portCurStatus: str, portType: str)
{ index(0(apType)) }
%table EnergyResultIfMid(apType: str, ifIndex: int4, portStatus: int1, portSpeed: int8)
%table EnergyResultIf(apType: str, ifIndex: int4, portStatus: int1, portSpeed: int8)
{ index(0(apType, ifIndex)), external }
PortDealTable(apType, portSpeedPolicy, powerifIndex, portIndex, portCurStatus, portType) :-
    Power(apType, powerMode),
    PortPowerSelect(apType, powerifIndex),
    PortCurStatus(apType, portIndex, portCurStatus, portType),
    PortPolicy(apType, powerMode, portSpeedPolicy).
EnergyResultIfMid(apType, ifIndex, portStatus, portSpeed) :-
    PortDealTable(apType, portSpeedPolicy, powerifIndex, portIndex, portCurStatus, portType) GROUP-BY(apType)
    GetEnergyResultIfFromPowerMode(portSpeedPolicy, powerifIndex, portIndex, portCurStatus, portType, ifIndex, portStatus, portSpeed).
EnergyResultIf(apType, ifIndex, portStatus, portSpeed) :-
    EnergyResultIfMid(apType, ifIndex, portStatus, portSpeed).
}
