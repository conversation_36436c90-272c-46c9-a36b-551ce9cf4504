/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: SUPROLLBACK.h
 * Description: Datalog HotPatch Specification Constraints
 * Author: youwanyong ywx1157510
 * Create: 2023-10-10
 */

#ifndef __SUPROLLBACK_H__
#define __SUPROLLBACK_H__
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "DatalogRun.h"
#include "StructDatalogTable.h"

#define MAX_NAME_LENGTH 512
#define FILE_PATH 512
GmcConnT *g_conn = NULL;
GmcStmtT *g_stmt = NULL;
char g_outputDir[FILE_PATH] = "Datalog_File";
int32_t g_isNeedSleep = 0;
uint32_t g_isNeedReduceLength = 0;
typedef struct {
    int32_t totalCount;
    int32_t callBackCount;
    int32_t negativeCount;
    int32_t positiveCount;
    bool needSleep = false;
    int32_t needSleepTime = 0;
} GIsInvokePubsubHasbyteCallBackCountStruct;
GIsInvokePubsubHasbyteCallBackCountStruct g_isInvokePubsubHasbyteCallBackCount;
typedef struct {
    int32_t totalCount;
    int32_t callBackCount;
    int32_t negativeCount;
    int32_t positiveCount;
    bool needSleep = false;
    int32_t needSleepTime = 0;
} GIsInvokePubsubHasNobyteCallBackCountStruct;
GIsInvokePubsubHasNobyteCallBackCountStruct g_isInvokePubsubHasNobyteCallBackCount;

typedef enum {
    PKONEDELETE,  // 主键字段为一个字段
    PKTWODELETE,  // 主键字段为二个字段
    SECDELETE,    // 二级索引删除
} DELETETYPE;

// 创建外部表
void DatalogCreateExternalTable()
{
    const char *configJson = "{\"isFastReadUncommitted\":false}";
    char *schema = NULL;
    char command[MAX_CMD_SIZE] = {0};
    GmcDropVertexLabel(g_stmt, "N000");
    readJanssonFile("./Datalog_File/N000.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);
    int32_t ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    EXPECT_EQ(GMERR_OK, ret);
    free(schema);
}

// 加载so文件
int LoadSoFile(const char *soName)
{
    char command[MAX_CMD_SIZE] = {0};
    (void)snprintf(command, FILE_PATH, "./%s/%s.so", g_outputDir, soName);
    return TestLoadDatalog(command);
}

int BatchInsertByte256(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum,
    int result = GMERR_OK, bool A1IsStr = false, int32_t begin = 0)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = begin; i < dataNum; i++) {
        int8_t value1 = count[i][0] % 127;
        int16_t value2 = count[i][1] % 32767;
        int32_t value3 = count[i][2];
        int64_t value4 = count[i][3];
        uint8_t value5[1] = {0};
        uint8_t value6[2] = {0};
        uint8_t value7[2] = {0};
        uint8_t value8[1] = {0};
        uint8_t value9[1] = {0};
        int stringLen = 10;
        // string
        uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
        if (buf == NULL) {
            return -1;
        }
        memset(buf, 0, stringLen + 1);
        (void)snprintf((char *)buf, stringLen + 1, "b%08d", value3);
        // set value
        // a
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a1
        if (A1IsStr) {
            ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // b1
        ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c1
        ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d1
        ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e1:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e1", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f1:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g1:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g1", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a2
        ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b2
        ret = GmcSetVertexProperty(stmt, "b2", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c2
        ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d2
        ret = GmcSetVertexProperty(stmt, "d2", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e2:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e2", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f2:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g2", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a3
        ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b3
        ret = GmcSetVertexProperty(stmt, "b3", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c3
        ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d3
        ret = GmcSetVertexProperty(stmt, "d3", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e3:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e3", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f3:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g3:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g3", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a4
        ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b4
        ret = GmcSetVertexProperty(stmt, "b4", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c4
        ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d4
        ret = GmcSetVertexProperty(stmt, "d4", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e4:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e4", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f4:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f4", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g4:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g4", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a5
        ret = GmcSetVertexProperty(stmt, "a5", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b5
        ret = GmcSetVertexProperty(stmt, "b5", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c5
        ret = GmcSetVertexProperty(stmt, "c5", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d5
        ret = GmcSetVertexProperty(stmt, "d5", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e5:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e5", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f5:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f5", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g5:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g5", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a6
        ret = GmcSetVertexProperty(stmt, "a6", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b6
        ret = GmcSetVertexProperty(stmt, "b6", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c6
        ret = GmcSetVertexProperty(stmt, "c6", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d6
        ret = GmcSetVertexProperty(stmt, "d6", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e6:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e6", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f6:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f6", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g6:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g6", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a7
        ret = GmcSetVertexProperty(stmt, "a7", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b7
        ret = GmcSetVertexProperty(stmt, "b7", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c7
        ret = GmcSetVertexProperty(stmt, "c7", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d7
        ret = GmcSetVertexProperty(stmt, "d7", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e7:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e7", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f7:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f7", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g7:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g7", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a8
        ret = GmcSetVertexProperty(stmt, "a8", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b8
        ret = GmcSetVertexProperty(stmt, "b8", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c8
        ret = GmcSetVertexProperty(stmt, "c8", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d8
        ret = GmcSetVertexProperty(stmt, "d8", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e8:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e8", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f8:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a9
        ret = GmcSetVertexProperty(stmt, "a9", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(buf);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_OUT_OF_MEMORY) {
        result = GMERR_OUT_OF_MEMORY;
    }
    if (ret == GMERR_REQUEST_TIME_OUT || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
        result = ret;
    }
    AW_MACRO_EXPECT_EQ_INT(result, ret);
    if (ret) {
        testGmcGetLastError();
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

int BatchDeleteByte256(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum,
    int result = GMERR_OK, bool A1IsStr = false, DELETETYPE Deletetype = PKTWODELETE)
{
    int ret = 0;

    for (int i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int8_t value1 = count[i][0] % 127;
        int16_t value2 = count[i][1] % 32767;
        int32_t value3 = count[i][2];
        int64_t value4 = count[i][3];
        uint8_t value5[1] = {0};
        uint8_t value6[1] = {0};
        uint8_t value7[1] = {0};
        uint8_t value8[1] = {0};
        uint8_t value9[1] = {0};
        int stringLen = 10;
        // string
        uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
        if (buf == NULL) {
            return -1;
        }
        memset(buf, 0, stringLen + 1);
        (void)snprintf((char *)buf, stringLen + 1, "b%08d", value3);
        // set value
        // a
        if (Deletetype == SECDELETE) {
            ret = GmcSetIndexKeyId(stmt, 4);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            value5[0] = count[i][4];
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_FIXED, value5, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else if (Deletetype == PKONEDELETE) {
            ret = GmcSetIndexKeyId(stmt, 0);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // upgradeVersion
            int32_t upgradeVersion = 0;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyId(stmt, 0);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            // upgradeVersion
            int32_t upgradeVersion = 0;
            ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = GmcExecute(stmt);
        if (ret) {
            testGmcGetLastError();
            free(buf);
            break;
        }

        free(buf);
    }
    return ret;
}

int BatchInsertByteOne(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum,
    int result = GMERR_OK, bool A1IsStr = false, int32_t begin = 0)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int stringLen = 10;
    // string
    uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
    if (buf == NULL) {
        return -1;
    }
    memset(buf, 0, stringLen + 1);

    for (int i = begin; i < dataNum; i++) {
        int8_t value1 = count[i][0] % 127;
        int16_t value2 = count[i][1] % 32767;
        int32_t value3 = count[i][2];
        int64_t value4 = count[i][3];
        uint8_t value5[1] = {0};
        uint8_t value6[1] = {0};
        uint8_t value7[1] = {0};
        uint8_t value8[1] = {0};
        uint8_t value9[1] = {0};

        (void)snprintf((char *)buf, stringLen + 1, "b%08d", value3);
        // set value
        // a
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a1
        if (A1IsStr) {
            ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // b1
        ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c1
        ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d1
        ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e1:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e1", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f1:byte1
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g1:byte1
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g1", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a2
        ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b2
        ret = GmcSetVertexProperty(stmt, "b2", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c2
        ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d2
        ret = GmcSetVertexProperty(stmt, "d2", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e2:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e2", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f2:byte1
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte1
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g2", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a3
        ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b3
        ret = GmcSetVertexProperty(stmt, "b3", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c3
        ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d3
        ret = GmcSetVertexProperty(stmt, "d3", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e3:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e3", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f3:byte1
        value6[0] = count[i][5];
        if (A1IsStr) {
            ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_FIXED, value8, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_FIXED, value6, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        //  g3:byte1
        value7[0] = count[i][6];
        if (A1IsStr) {
            ret = GmcSetVertexProperty(stmt, "g3", GMC_DATATYPE_FIXED, value9, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "g3", GMC_DATATYPE_FIXED, value7, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // a4
        ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b4
        ret = GmcSetVertexProperty(stmt, "b4", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c4
        ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d4
        if (strstr(labelName, "S000") != NULL) {
        } else {
            ret = GmcSetVertexProperty(stmt, "d4", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        //  e4:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e4", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f4:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f4", GMC_DATATYPE_FIXED, value8, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g4:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g4", GMC_DATATYPE_FIXED, value9, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a5
        ret = GmcSetVertexProperty(stmt, "a5", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b5
        ret = GmcSetVertexProperty(stmt, "b5", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c5
        ret = GmcSetVertexProperty(stmt, "c5", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d5
        ret = GmcSetVertexProperty(stmt, "d5", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e5:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e5", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f5:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f5", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g5:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g5", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a6
        ret = GmcSetVertexProperty(stmt, "a6", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b6
        ret = GmcSetVertexProperty(stmt, "b6", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c6
        ret = GmcSetVertexProperty(stmt, "c6", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d6
        ret = GmcSetVertexProperty(stmt, "d6", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e6:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e6", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f6:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f6", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g6:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g6", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a7
        ret = GmcSetVertexProperty(stmt, "a7", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b7
        ret = GmcSetVertexProperty(stmt, "b7", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c7
        ret = GmcSetVertexProperty(stmt, "c7", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d7
        ret = GmcSetVertexProperty(stmt, "d7", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e7:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e7", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f7:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f7", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g7:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g7", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a8
        ret = GmcSetVertexProperty(stmt, "a8", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b8
        ret = GmcSetVertexProperty(stmt, "b8", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c8
        ret = GmcSetVertexProperty(stmt, "c8", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d8
        ret = GmcSetVertexProperty(stmt, "d8", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e8:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e8", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f8:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a9
        ret = GmcSetVertexProperty(stmt, "a9", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_OUT_OF_MEMORY) {
        result = GMERR_OUT_OF_MEMORY;
    }
    AW_MACRO_EXPECT_EQ_INT(result, ret);
    if (ret && ret != GMERR_OUT_OF_MEMORY) {
        testGmcGetLastError();
    }
    free(buf);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}
// delete根据主键删除
int BatchDeleteByteOne(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum,
    int result = GMERR_OK, bool A1IsStr = false)
{
    int ret = 0;

    for (int i = 0; i < dataNum; i++) {
        ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_DELETE);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        int8_t value1 = count[i][0] % 127;
        int16_t value2 = count[i][1] % 32767;
        int32_t value3 = count[i][2];
        int64_t value4 = count[i][3];
        uint8_t value5[1] = {0};
        uint8_t value6[1] = {0};
        uint8_t value7[1] = {0};
        uint8_t value8[1] = {0};
        uint8_t value9[1] = {0};
        int stringLen = 10;
        // string
        uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
        if (buf == NULL) {
            return -1;
        }
        memset(buf, 0, stringLen + 1);
        (void)snprintf((char *)buf, stringLen + 1, "b%08d", value3);
        // set value
        // a
        ret = GmcSetIndexKeyId(stmt, 0);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // upgradeVersion
        int32_t upgradeVersion = 0;
        ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b
        ret = GmcSetIndexKeyValue(stmt, 2, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c
        ret = GmcSetIndexKeyValue(stmt, 3, GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d

        ret = GmcSetIndexKeyValue(stmt, 4, GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e:byte1
        value5[0] = count[i][4];
        ret = GmcSetIndexKeyValue(stmt, 5, GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f:byte2
        value6[0] = count[i][5];
        ret = GmcSetIndexKeyValue(stmt, 6, GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetIndexKeyValue(stmt, 7, GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a1
        if (A1IsStr) {
            ret = GmcSetIndexKeyValue(stmt, 8, GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 8, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // b1
        ret = GmcSetIndexKeyValue(stmt, 9, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c1
        ret = GmcSetIndexKeyValue(stmt, 10, GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d1
        ret = GmcSetIndexKeyValue(stmt, 11, GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e1:byte1
        value5[0] = count[i][4];
        ret = GmcSetIndexKeyValue(stmt, 12, GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f1:byte1
        value6[0] = count[i][5];
        ret = GmcSetIndexKeyValue(stmt, 13, GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g1:byte1
        value7[0] = count[i][6];
        ret = GmcSetIndexKeyValue(stmt, 14, GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a2
        ret = GmcSetIndexKeyValue(stmt, 15, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b2
        ret = GmcSetIndexKeyValue(stmt, 16, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c2
        ret = GmcSetIndexKeyValue(stmt, 17, GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d2
        ret = GmcSetIndexKeyValue(stmt, 18, GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e2:byte1
        value5[0] = count[i][4];
        ret = GmcSetIndexKeyValue(stmt, 19, GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f2:byte1
        value6[0] = count[i][5];
        ret = GmcSetIndexKeyValue(stmt, 20, GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte1
        value7[0] = count[i][6];
        ret = GmcSetIndexKeyValue(stmt, 21, GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a3
        ret = GmcSetIndexKeyValue(stmt, 22, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b3
        ret = GmcSetIndexKeyValue(stmt, 23, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c3
        ret = GmcSetIndexKeyValue(stmt, 24, GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d3
        ret = GmcSetIndexKeyValue(stmt, 25, GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e3:byte1
        value5[0] = count[i][4];
        ret = GmcSetIndexKeyValue(stmt, 26, GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f3:byte1
        value6[0] = count[i][5];
        if (A1IsStr) {
            ret = GmcSetIndexKeyValue(stmt, 27, GMC_DATATYPE_FIXED, value8, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 27, GMC_DATATYPE_FIXED, value6, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        value7[0] = count[i][6];
        if (A1IsStr) {
            ret = GmcSetIndexKeyValue(stmt, 28, GMC_DATATYPE_FIXED, value9, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetIndexKeyValue(stmt, 28, GMC_DATATYPE_FIXED, value7, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // a4
        ret = GmcSetIndexKeyValue(stmt, 29, GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b4
        GmcSetIndexKeyValue(stmt, 30, GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c4
        ret = GmcSetIndexKeyValue(stmt, 31, GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcExecute(stmt);
        if (ret == GMERR_OUT_OF_MEMORY) {
            result = GMERR_OUT_OF_MEMORY;
        }
        AW_MACRO_EXPECT_EQ_INT(result, ret);
        if (ret) {
            testGmcGetLastError();
        }

        free(buf);
    }
    return ret;
}

int BatchInsert(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum, int result = GMERR_OK,
    int32_t begin = 0, int32_t countValue = 1)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = begin; i < dataNum; i++) {
        int8_t value1 = count[i][0] % 127;
        int16_t value2 = count[i][1] % 32767;
        int32_t value3 = count[i][2];
        int64_t value4 = count[i][3];
        uint8_t value5[1] = {0};
        uint8_t value6[2] = {0};
        uint8_t value7[2] = {0};
        uint8_t value8[1] = {0};
        uint8_t value9[1] = {0};
        int stringLen = 10;
        // string
        uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
        if (buf == NULL) {
            return -1;
        }
        memset(buf, 0, stringLen + 1);
        (void)snprintf((char *)buf, stringLen + 1, "b%08d", value3);
        // set value
        // a
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, value6, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, value7, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a1
        ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b1
        ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c1
        ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d1
        ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e1:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e1", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f1:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_FIXED, value6, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g1:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g1", GMC_DATATYPE_FIXED, value7, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a2
        ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b2
        ret = GmcSetVertexProperty(stmt, "b2", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c2
        ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d2
        ret = GmcSetVertexProperty(stmt, "d2", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e2:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e2", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f2:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_FIXED, value6, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g2", GMC_DATATYPE_FIXED, value7, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a3
        ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b3
        ret = GmcSetVertexProperty(stmt, "b3", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c3
        ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d3
        ret = GmcSetVertexProperty(stmt, "d3", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e3:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e3", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f3:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_FIXED, value6, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g3:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g3", GMC_DATATYPE_FIXED, value7, 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a4
        ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b4
        ret = GmcSetVertexProperty(stmt, "b4", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c4
        ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d4
        ret = GmcSetVertexProperty(stmt, "d4", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e4:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e4", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f4:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f4", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g4:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g4", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a5
        ret = GmcSetVertexProperty(stmt, "a5", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b5
        ret = GmcSetVertexProperty(stmt, "b5", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c5
        ret = GmcSetVertexProperty(stmt, "c5", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d5
        ret = GmcSetVertexProperty(stmt, "d5", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e5:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e5", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f5:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f5", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g5:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g5", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a6
        ret = GmcSetVertexProperty(stmt, "a6", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b6
        ret = GmcSetVertexProperty(stmt, "b6", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c6
        ret = GmcSetVertexProperty(stmt, "c6", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d6
        ret = GmcSetVertexProperty(stmt, "d6", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e6:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e6", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f6:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f6", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g6:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g6", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a7
        ret = GmcSetVertexProperty(stmt, "a7", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b7
        ret = GmcSetVertexProperty(stmt, "b7", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c7
        ret = GmcSetVertexProperty(stmt, "c7", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d7
        ret = GmcSetVertexProperty(stmt, "d7", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e7:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e7", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f7:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f7", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g7:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g7", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a8
        ret = GmcSetVertexProperty(stmt, "a8", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b8
        ret = GmcSetVertexProperty(stmt, "b8", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c8
        ret = GmcSetVertexProperty(stmt, "c8", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d8
        ret = GmcSetVertexProperty(stmt, "d8", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e8:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e8", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f8:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a9
        ret = GmcSetVertexProperty(stmt, "a9", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &countValue, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(buf);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_OUT_OF_MEMORY) {
        result = GMERR_OUT_OF_MEMORY;
    }
    AW_MACRO_EXPECT_EQ_INT(result, ret);
    if (ret) {
        testGmcGetLastError();
    }

    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return ret;
}

int UpdateSingleInt4Set(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    SingleInt4 *obj = (SingleInt4 *)t;
    int ret = 0;
    int32_t upgradeVersion = 0;
    ret = GmcSetIndexKeyId(stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 0, GMC_DATATYPE_INT32, &upgradeVersion, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(stmt, 1, GMC_DATATYPE_INT32, &obj->a, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int SingleInt4Set(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    SingleInt4 *obj = (SingleInt4 *)t;
    int ret = 0;
    // a
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4Set] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4Set] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

int SingleInt4Cmp(const SingleInt4 *st1, const SingleInt4 *st2, bool isPrint = false, bool isExternal = false)
{
    int ret = -1;
    do {
        if (st1->a != st2->a) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[SingleInt4Cmp] a, st1: %d, st2: %d.", st1->a, st2->a)) : ({});
            break;
        }
        if (st1->b != st2->b) {
            isPrint ? (AW_FUN_Log(LOG_INFO, "[SingleInt4Cmp] a, st1: %d, st2: %d.", st1->a, st2->a)) : ({});
            break;
        }

        if (st1->dtlReservedCount != st2->dtlReservedCount) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[SingleInt4Cmp] dtlReservedCount, st1: %d, st2: %d.", st1->dtlReservedCount,
                    st2->dtlReservedCount);
            }
            break;
        }
        if (st1->upgradeVersion != st2->upgradeVersion) {
            if (isPrint) {
                AW_FUN_Log(LOG_INFO, "[SingleInt4Cmp] upgradeVersion, st1: %d, st2: %d.", st1->upgradeVersion,
                    st2->upgradeVersion);
            }
            break;
        }
        ret = 0;
    } while (0);

    return ret;
}

int SingleInt4Get(GmcStmtT *stmt, void *t, int len, bool isExternal, bool isResource = false)
{
    SingleInt4 *checkObj = (SingleInt4 *)t;
    SingleInt4 getObj = {0};

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4Get] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4Get] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4Get] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(
        stmt, "upgradeVersion", &getObj.upgradeVersion, sizeof(getObj.upgradeVersion), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4Get] get 'upgradeVersion' fail, ret = %d.", ret);
        return ret;
    }
    ret = -1;
    // 校验获取的值
    for (int i = 0; i < len; i++) {
        if (SingleInt4Cmp(&getObj, &checkObj[i], NULL, isExternal) == 0) {
            ret = 0;
            break;
        }
    }
    return ret;
}

// 设置字段值(int1 int2 int8 byte1 byte128 byte256 byte512)
int SetUpgradeTabel(GmcStmtT *stmt, void *t, bool isStateTabelInput = false)
{
    Inp1 *obj = (Inp1 *)t;
    int ret = 0;
    // a
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetUpgradeTabel] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetUpgradeTabel] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT64, &obj->c, sizeof(obj->c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetUpgradeTabel] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }

    ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_FIXED, obj->d, 1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetUpgradeTabel] d: %d, ret = %d.", obj->d, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, obj->e, 128);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetUpgradeTabel] e: %d, ret = %d.", obj->e, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, obj->f, 256);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetUpgradeTabel] f: %d, ret = %d.", obj->f, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, obj->g, 512);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SetUpgradeTabel] g: %d, ret = %d.", obj->g, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    return ret;
}

char g_tableName[128] = "cap";
char g_configJson[128] = "{\"max_record_count\":100000, \"isFastReadUncommitted\": false}";

int CreateKvTable()
{
    int ret = 0;
    // 创建kv表
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcKvDropTable(stmt, g_tableName);
    ret = GmcKvCreateTable(stmt, g_tableName, g_configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcKvPrepareStmtByLabelName(stmt, g_tableName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo = {0};
    int32_t value = 100;
    char key[32] = "para1";
    // 设置k-v值
    kvInfo.key = key;
    kvInfo.keyLen = strlen(key) + 1;
    kvInfo.value = &value;
    kvInfo.valueLen = sizeof(int32_t);
    ret = GmcKvSet(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo1 = {0};
    char key1[32] = "para2";
    char value1[32] = "aaaaaa";
    kvInfo1.key = key1;
    kvInfo1.keyLen = strlen(key1) + 1;
    kvInfo1.value = value1;
    kvInfo1.valueLen = strlen(value1) + 1;
    ret = GmcKvSet(stmt, kvInfo1.key, kvInfo1.keyLen, kvInfo1.value, kvInfo1.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo2 = {0};
    char key2[32] = "para3";
    char value2[64] = {0};
    memset(value2, 'c', 63);
    kvInfo2.key = key2;
    kvInfo2.keyLen = strlen(key2) + 1;
    kvInfo2.value = value2;
    kvInfo2.valueLen = strlen(value2) + 1;
    ret = GmcKvSet(stmt, kvInfo2.key, kvInfo2.keyLen, kvInfo2.value, kvInfo2.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcKvTupleT kvInfo3 = {0};
    char key3[32] = "para4";
    int64_t value3 = 1000;
    kvInfo3.key = key3;
    kvInfo3.keyLen = strlen(key3) + 1;
    kvInfo3.value = &value3;
    kvInfo3.valueLen = sizeof(int64_t);
    ret = GmcKvSet(stmt, kvInfo3.key, kvInfo3.keyLen, kvInfo3.value, kvInfo3.valueLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview show cap");
    return ret;
}

#define MAX_NAME_LENGTH 512
// 生成byte变长字段
Status generate_bytes(int8_t **bytes, uint32_t byteLen)
{
    int8_t *byteArray = (int8_t *)malloc(byteLen + 1);
    if (byteArray == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    for (uint32_t i = 0; i < byteLen; i++) {
        int num = i;
        while (num >= 10) {
            num = num % 10;
        }
        byteArray[i] = (uint8_t)num;
    }
    byteArray[byteLen] = '\n';
    *bytes = byteArray;
    return GMERR_OK;
}

typedef int (*FuncReadPubsubId)(GmcStmtT *stmt, int32_t startid, int32_t endid, int32_t count, bool isPubSubRsc);
typedef int (*FuncCheck)(GmcStmtT *stmt, void *id, void *df, int32_t c);

typedef struct TagSnUserDataWithFuncT {
    SnUserDataT *data;
    int tableType;  // 0:out 1:resource
    int funcType;   // 0:id 1:struct
    char *nsName;
    union {
        struct {
            FuncReadPubsubId readIdFunc;
            int32_t startid;
            int32_t endid;
            int32_t count;
        };
    };
} SnUserDataWithFuncT;

int snCallbackCheckPubsubhasByte(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    char *nsName = userDefinedData->nsName;
    int tableType = userDefinedData->tableType;
    int funcType = userDefinedData->funcType;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);
    GmcRespT *response;
    int ret = GmcCreateResp(subStmt, &response);
    RETURN_IFERR(ret);
    if (userDefinedData->tableType == 0) {  // notify输出表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
        RETURN_IFERR(ret);
        uint16_t failedDataNum = 0;
        uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
        ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
        RETURN_IFERR(ret);
    } else if (userDefinedData->tableType == 1) {  // pubsub型资源表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
        RETURN_IFERR(ret);
    }

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    int32_t valueA;
                    int32_t valueA1;
                    int32_t valueC;
                    ret = GmcGetVertexPropertyByName(subStmt, "a", &valueA, sizeof(valueA), &eof);
                    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "a1", &valueA1, sizeof(valueA1), &eof);
                    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    uint32_t bByteLength = 0;
                    ret = GmcGetVertexPropertySizeByName(subStmt, "b", &bByteLength);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    int8_t *valueB = (int8_t *)malloc(bByteLength + 1);
                    if (valueB == NULL) {
                        return -1;
                    }
                    bByteLength - bByteLength - g_isNeedReduceLength;
                    int8_t *expectBValue = NULL;
                    ret = generate_bytes(&expectBValue, bByteLength);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "b", valueB, bByteLength, &eof);
                    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    if (expectBValue) {
                        ret = memcmp((char *)expectBValue, (char *)valueB, bByteLength);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        free(expectBValue);
                    }
                    ret = GmcGetVertexPropertyByName(subStmt, "dtlReservedCount", &valueC, sizeof(valueC), &eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            
                    g_isInvokePubsubHasbyteCallBackCount.totalCount++;

                    if (valueC == 1) {
                        g_isInvokePubsubHasbyteCallBackCount.positiveCount++;
                    } else {
                        g_isInvokePubsubHasbyteCallBackCount.negativeCount++;
                    }
                    free(valueB);
                    if (tableType == 0) {  // out table
                        ret = userDefinedData->readIdFunc(
                            subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count, 0);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    } else if (tableType == 1) {  // pubsubresource
                        ret = userDefinedData->readIdFunc(
                            subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count, 1);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcSubAddRespDML(response, subStmt);
                        RETURN_IFERR(ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }
    if (userDefinedData->tableType != 2) {
        ret = GmcSendResp(subStmt, response);
        RETURN_IFERR(ret);
        ret = GmcDestroyResp(subStmt, response);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

int snCallbackCheckPubsubNoByte(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    char *nsName = userDefinedData->nsName;
    int tableType = userDefinedData->tableType;
    int funcType = userDefinedData->funcType;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);
    GmcRespT *response;
    int ret = GmcCreateResp(subStmt, &response);
    RETURN_IFERR(ret);
    if (userDefinedData->tableType == 0) {  // notify输出表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
        RETURN_IFERR(ret);
        uint16_t failedDataNum = 0;
        uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
        ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
        RETURN_IFERR(ret);
    } else if (userDefinedData->tableType == 1) {  // pubsub型资源表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
        RETURN_IFERR(ret);
    }

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    int32_t valueA;
                    int32_t valueA1;
                    int32_t valueC;
                    ret = GmcGetVertexPropertyByName(subStmt, "a", &valueA, sizeof(valueA), &eof);
                    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "a1", &valueA1, sizeof(valueA1), &eof);
                    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    uint32_t bByteLength = 0;
                    ret = GmcGetVertexPropertySizeByName(subStmt, "b", &bByteLength);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    int8_t *valueB = (int8_t *)malloc(bByteLength + 1);
                    if (valueB == NULL) {
                        return -1;
                    }
                    bByteLength - bByteLength - g_isNeedReduceLength;
                    int8_t *expectBValue = NULL;
                    ret = generate_bytes(&expectBValue, bByteLength);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    ret = GmcGetVertexPropertyByName(subStmt, "b", valueB, bByteLength, &eof);
                    AW_MACRO_EXPECT_EQ_BOOL(false, eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    if (expectBValue) {
                        ret = memcmp((char *)expectBValue, (char *)valueB, bByteLength);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        free(expectBValue);
                    }
                    ret = GmcGetVertexPropertyByName(subStmt, "dtlReservedCount", &valueC, sizeof(valueC), &eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    g_isInvokePubsubHasNobyteCallBackCount.totalCount++;
                    if (valueC == 1) {
                        g_isInvokePubsubHasNobyteCallBackCount.positiveCount++;
                    } else {
                        g_isInvokePubsubHasNobyteCallBackCount.negativeCount++;
                    }
                    free(valueB);
                    if (tableType == 0) {  // out table
                        ret = userDefinedData->readIdFunc(
                            subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count, 0);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    } else if (tableType == 1) {  // pubsubresource
                        ret = userDefinedData->readIdFunc(
                            subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count, 1);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcSubAddRespDML(response, subStmt);
                        RETURN_IFERR(ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }
    if (userDefinedData->tableType != 2) {
        ret = GmcSendResp(subStmt, response);
        RETURN_IFERR(ret);
        ret = GmcDestroyResp(subStmt, response);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

int snCallbackCheckPubsubResource(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    char *nsName = userDefinedData->nsName;
    int tableType = userDefinedData->tableType;
    int funcType = userDefinedData->funcType;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;
    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);
    GmcRespT *response;
    int ret = GmcCreateResp(subStmt, &response);
    RETURN_IFERR(ret);
    if (userDefinedData->tableType == 0) {  // notify输出表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
        RETURN_IFERR(ret);
        uint16_t failedDataNum = 0;
        uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
        ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
        RETURN_IFERR(ret);
    } else if (userDefinedData->tableType == 1) {  // pubsub型资源表
        ret = GmcSetRespMode(response, GMC_RESP_SEND_BATCH_INSERT);
        RETURN_IFERR(ret);
    }

    bool eof = false;
    while (!eof) {
        ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    int32_t valueC;
                    ret = GmcGetVertexPropertyByName(subStmt, "dtlReservedCount", &valueC, sizeof(valueC), &eof);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

                    g_isInvokePubsubHasNobyteCallBackCount.totalCount++;
                    if (valueC == 1) {
                        g_isInvokePubsubHasNobyteCallBackCount.positiveCount++;
                    } else {
                        g_isInvokePubsubHasNobyteCallBackCount.negativeCount++;
                    }
                    if (tableType == 0) {  // out table
                        ret = userDefinedData->readIdFunc(
                            subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count, 0);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    } else if (tableType == 1) {  // pubsubresource
                        ret = userDefinedData->readIdFunc(
                            subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count, 1);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                        ret = GmcSubAddRespDML(response, subStmt);
                        RETURN_IFERR(ret);
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }
    if (userDefinedData->tableType != 2) {
        ret = GmcSendResp(subStmt, response);
        RETURN_IFERR(ret);
        ret = GmcDestroyResp(subStmt, response);
        RETURN_IFERR(ret);
    }

    return GMERR_OK;
}

void snCallbackPubsubResource(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    // 默认关闭回调阻塞
    if (g_isInvokePubsubHasNobyteCallBackCount.needSleep) {
        if (g_isInvokePubsubHasNobyteCallBackCount.negativeCount != 0) {
            sleep(g_isInvokePubsubHasNobyteCallBackCount.needSleepTime);
        }
    }
    // 触发回调次数统计
    g_isInvokePubsubHasNobyteCallBackCount.callBackCount++;
    int ret = snCallbackCheckPubsubResource(subStmt, info, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void snCallbackPubsubhasByte(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    // 默认关闭回调阻塞
    if (g_isInvokePubsubHasbyteCallBackCount.needSleep) {
        if (g_isInvokePubsubHasbyteCallBackCount.negativeCount != 0) {
            sleep(g_isInvokePubsubHasbyteCallBackCount.needSleepTime);
        }
    }
    // 触发回调次数统计
    g_isInvokePubsubHasbyteCallBackCount.callBackCount++;
    int ret = snCallbackCheckPubsubhasByte(subStmt, info, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void snCallbackPubsubNoByte(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    // 默认关闭回调阻塞
    if (g_isInvokePubsubHasNobyteCallBackCount.needSleep) {
        if (g_isInvokePubsubHasNobyteCallBackCount.negativeCount != 0) {
            sleep(g_isInvokePubsubHasNobyteCallBackCount.needSleepTime);
        }
    }
    // 触发回调次数统计
    g_isInvokePubsubHasNobyteCallBackCount.callBackCount++;
    int ret = snCallbackCheckPubsubNoByte(subStmt, info, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

int Int4Byte_getId(GmcStmtT *stmt, int32_t startid, int32_t endid, int32_t count, bool isPubsubRsc = false)
{
    Int4Byte getObj = {};
    int ret = 0;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Int4Byte_getId] get 'a[Int4Byte_getId]' fail, ret = %d.", ret);
        return ret;
    }
    uint32_t bByteLength = 0;
    ret = GmcGetVertexPropertySizeByName(stmt, "b", &bByteLength);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    uint8_t *buf = (uint8_t *)malloc(bByteLength + 1);
    if (buf == NULL) {
        return -1;
    }
    ret = GmcGetVertexPropertyByName(stmt, "b", buf, bByteLength, &isNull);
    AW_MACRO_EXPECT_EQ_BOOL(false, isNull);
    free(buf);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Int4Byte_getId] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[Int4Byte_getId] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    if (ret == -1) {
        AW_FUN_Log(LOG_ERROR, "[Int4Byte_getId] a: %d, b: %d, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }
    return ret;
}

// 订阅
int TestCreateSubHasByte(GmcStmtT *stmt, GmcConnT *subConn, SnUserDataWithFuncT **userData, int tableType = 0)
{
    const char *subConnNameA = "notifyA";
    char *subInfoA = NULL;
    readJanssonFile((char *)"./Datalog_File/pubsub_A.gmjson", &subInfoA);
    EXPECT_NE((void *)NULL, subInfoA);
    const char *subNameA = "testSub_ns1.A";
    GmcSubConfigT tmp_sub_infoA;
    tmp_sub_infoA.subsName = subNameA;
    tmp_sub_infoA.configJson = subInfoA;
    *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    (*userData)->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((*userData)->data, 0, sizeof(SnUserDataT));
    (*userData)->funcType = 0;
    (*userData)->tableType = tableType;
    (*userData)->readIdFunc = Int4Byte_getId;
    (*userData)->startid = 0;
    (*userData)->endid = 1;
    (*userData)->count = 1;
    int ret = GmcSubscribe(stmt, &tmp_sub_infoA, subConn, snCallbackPubsubhasByte, *userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfoA);
    return ret;
}

// 订阅
int TestCreateSubNoByte(GmcStmtT *stmt, GmcConnT *subConn, SnUserDataWithFuncT **userData, int tableType = 0)
{
    const char *subConnNameA = "notifyB";
    char *subInfoB = NULL;
    readJanssonFile((char *)"./Datalog_File/pubsub_B.gmjson", &subInfoB);
    EXPECT_NE((void *)NULL, subInfoB);
    const char *subNameB = "testSub_ns1.B";
    GmcSubConfigT tmp_sub_infoB;
    tmp_sub_infoB.subsName = subNameB;
    tmp_sub_infoB.configJson = subInfoB;
    *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    (*userData)->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((*userData)->data, 0, sizeof(SnUserDataT));
    (*userData)->funcType = 0;
    (*userData)->tableType = tableType;
    (*userData)->readIdFunc = Int4Byte_getId;
    (*userData)->startid = 0;
    (*userData)->endid = 1;
    (*userData)->count = 1;
    int ret = GmcSubscribe(stmt, &tmp_sub_infoB, subConn, snCallbackPubsubNoByte, *userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfoB);
    return ret;
}

// 订阅
int DoubleInt4_getId(GmcStmtT *stmt, int startid, int endid, int32_t count, bool isPubSubRsc = false)
{
    PubsubInt4 getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'a[DoubleInt4_getId]' fail, ret = %d.", ret);
        return ret;
    }
    ret = GmcGetVertexPropertyByName(stmt, "a1", &getObj.a1, sizeof(getObj.a1), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'a[DoubleInt4_getId]' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    ret = -1;

    if ((getObj.b == 1) && (getObj.dtlReservedCount == 1)) {
        ret = 0;
        getObj.b = getObj.a + 1;
    } else if ((getObj.b == 1) && (getObj.dtlReservedCount == -1)) {
        ret = 0;
    }

    if (ret == -1) {
        AW_FUN_Log(
            LOG_ERROR, "[DoubleInt4_getId] a: %d, b: %d, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }

    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &getObj.a, sizeof(int32_t));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT32, &getObj.a1, sizeof(int32_t));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &getObj.b, sizeof(int32_t));
    RETURN_IFERR(ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &getObj.dtlReservedCount, sizeof(int32_t));
    RETURN_IFERR(ret);
    return ret;
}
int TestCreateSubResource(GmcStmtT *stmt, GmcConnT *subConn, SnUserDataWithFuncT **userData, int tableType = 0)
{
    const char *subConnNameA = "notifyB";
    char *subInfoB = NULL;
    readJanssonFile((char *)"./Datalog_File/pubsub_B.gmjson", &subInfoB);
    EXPECT_NE((void *)NULL, subInfoB);
    const char *subNameB = "testSub_ns1.B";
    GmcSubConfigT tmp_sub_infoB;
    tmp_sub_infoB.subsName = subNameB;
    tmp_sub_infoB.configJson = subInfoB;
    *userData = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    (*userData)->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset((*userData)->data, 0, sizeof(SnUserDataT));
    (*userData)->funcType = 0;
    (*userData)->tableType = 1;
    (*userData)->readIdFunc = DoubleInt4_getId;
    (*userData)->startid = 0;
    (*userData)->endid = 1;
    (*userData)->count = 1;
    int ret = GmcSubscribe(stmt, &tmp_sub_infoB, subConn, snCallbackPubsubResource, *userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(subInfoB);
    return ret;
}

// read out table
void ReadOutTableView(char *labelName, char *coutValue)
{
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s", g_toolPath, labelName);
    int32_t ret = executeCommand(g_command, coutValue);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void ClearCountValue()
{
    g_isInvokePubsubHasbyteCallBackCount.totalCount = 0;
    g_isInvokePubsubHasbyteCallBackCount.positiveCount = 0;
    g_isInvokePubsubHasbyteCallBackCount.negativeCount = 0;
    g_isInvokePubsubHasbyteCallBackCount.callBackCount = 0;
    g_isInvokePubsubHasbyteCallBackCount.needSleep = false;
    g_isInvokePubsubHasbyteCallBackCount.needSleepTime = 0;

    g_isInvokePubsubHasNobyteCallBackCount.totalCount = 0;
    g_isInvokePubsubHasNobyteCallBackCount.positiveCount = 0;
    g_isInvokePubsubHasNobyteCallBackCount.negativeCount = 0;
    g_isInvokePubsubHasNobyteCallBackCount.callBackCount = 0;
    g_isInvokePubsubHasNobyteCallBackCount.needSleep = false;
    g_isInvokePubsubHasNobyteCallBackCount.needSleepTime = 0;
}

// filecontent comparepe
void FileContentCompare(int32_t compareFileType)
{
    // compareFileType 1 (insert delete) 2 (update)
    char command1[MAX_CMD_SIZE] = {0};
    char command2[MAX_CMD_SIZE] = {0};
    (void)memset(command1, 0, sizeof(char) * (MAX_CMD_SIZE));
    (void)memset(command2, 0, sizeof(char) * (MAX_CMD_SIZE));
    if (compareFileType == 1) {
        system("cat /root/_datalog_/RunLogInsert.txt > insert.txt");
        system("tac /root/_datalog_/RunLogDelete.txt > delete.txt");
        (void)snprintf(command1, MAX_CMD_SIZE, "md5sum insert.txt");
        (void)snprintf(command2, MAX_CMD_SIZE, "md5sum delete.txt");
    } else {
        system("cat /root/_datalog_/RunLogUpdateNew.txt > update1.txt");
        system("tac /root/_datalog_/RunLogUpdateold.txt > update2.txt");
        (void)snprintf(command1, MAX_CMD_SIZE, "md5sum update1.txt");
        (void)snprintf(command2, MAX_CMD_SIZE, "md5sum update2.txt");
    }

    int fileContent1 = 0;
    int fileContent2 = -1;
    EXPECT_EQ(0, TestGetResultCommand(command1, &fileContent1, NULL, 0));
    EXPECT_EQ(0, TestGetResultCommand(command2, &fileContent2, NULL, 0));
    AW_MACRO_EXPECT_EQ_INT(fileContent2, fileContent1);
}
// clear file content
void ClearFileContent()
{
    system("> /root/_datalog_/RunLogInsert.txt");
    system("> /root/_datalog_/RunLogDelete.txt");
    system("> /root/_datalog_/RunLogUpdateNew.txt");
    system("> /root/_datalog_/RunLogUpdateold.txt");
}

// PTL_DATALOG_SO_INFO视图校验方式
int querySoView(
    const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL, const char *v4 = NULL, const char *v5 = NULL)
{
    char *viewName = (char *)"V\\$PTL_DATALOG_SO_INFO";
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q %s -f SO_NAME=%s -view_fmt json ", viewName, v1);
    system(g_command);
    return executeCommand(g_command, v1, v2, v3, v4, v5);
}

// 线程函数1加载so进行DML操作
void *ThreadDml(void *args)
{
    int32_t ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    char soName[FILE_PATH] = "Datalog_052_005";    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "编译加载so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 510;
    int32_t recordNum2 = 89;
    // 写内存满时数据量
    int32_t memRecord = 1;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 2;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(conn, stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(conn, stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$CATA_GENERAL_INFO", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ClearFileContent();

    /* 每批写512条，直到内存满 */

    // 更新产生512条数据
    AW_FUN_Log(LOG_INFO, "更新内存满数据量.", recordNum1);
    ret = UpdateRecord(conn, stmt, "inpG2", objIn2, memRecord, UpdateSingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    // 校验输出表
    ret = readRecord(conn, stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(conn, stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "校验数据.");
    // 获取用例数据，强校验数据回滚顺序
    FileContentCompare(2);
    AW_FUN_Log(LOG_INFO, "消息通知表校验数据一致性.");

    ReadOutTableView((char *)"outA", (char *)"45390");
    ReadOutTableView((char *)"outC", (char *)"45390");
    ReadOutTableView((char *)"outD", (char *)"45390");
    ReadOutTableView((char *)"outE", (char *)"45390");
    ReadOutTableView((char *)"outF", (char *)"45390");

    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 加载补丁后不停的加载卸载
void *ThreadInstall(void *args)
{
    char soName1[FILE_PATH] = "Datalog_052_024";
    int32_t ret = 0;
#if defined ENV_RTOSV2X
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    int32_t cycle = 2;
    while (cycle > 0) {
        ret = LoadSoFile(soName1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$CATA_GENERAL_INFO", g_toolPath);
        ret = executeCommand(g_command, "fetched all records, finish!");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 4.卸载so
        AW_FUN_Log(LOG_STEP, "%d:卸载so.", cycle);
        ret = TestUninstallDatalog(soName1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        cycle--;
    }
#if defined ENV_RTOSV2X
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 线程函数1加载so进行DML操作
void *ThreadDmlTbm(void *args)
{
    int32_t ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 插入数据
    int32_t recordNum1 = 140;
    int32_t recordNum2 = 140;
    // 写内存满时数据量
    int32_t memRecord = 4;
    ClearFileContent();
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpTbmG1写入%d条数据.", recordNum1);
    ret = writeRecord(conn, stmt, "inpTbmG1", objIn1, recordNum1, SingleInt4Set, false);

    // 批写数据
    ret = writeRecord(conn, stmt, "inpTbmG2", objIn1, recordNum2, SingleInt4Set, true);
    if (ret) {
        FileContentCompare(1);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_INFO, "tbm表校验数据一致性.");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThreadDmlMsg(void *args)
{
    int32_t ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 插入数据
    int32_t recordNum1 = 140;
    int32_t recordNum2 = 140;
    // 写内存满时数据量
    int32_t memRecord = 4;
    ClearFileContent();
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpMsgG1写入%d条数据.", recordNum1);
    ret = writeRecord(conn, stmt, "inpMsgG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写数据
    ret = writeRecord(conn, stmt, "inpMsgG2", objIn1, recordNum2, SingleInt4Set, true);
    if (ret) {
        FileContentCompare(0);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    free(objIn1);
    free(objIn2);

    AW_FUN_Log(LOG_INFO, "msg表校验数据一致性.");
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void *ThreadDmlPubsub(void *args)
{
    int32_t ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcConnT *subConnA;
    GmcStmtT *subStmtA;
    const char *subConnNameA = "testSubA";
    const char *subNameA = "testSub_ns1.A";
    ret = testSubConnect(&subConnA, &subStmtA, 1, g_epoll_reg_info, subConnNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataA;
    ret = TestCreateSubHasByte(stmt, subConnA, &userDataA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outA表订阅");
    // 清空统计值
    ClearCountValue();
    // 插入数据
    int32_t recordNum1 = 140;
    int32_t recordNum2 = 140;
    // 写内存满时数据量
    int32_t memRecord = 4;
    ClearFileContent();
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpAG1写入%d条数据.", recordNum1);
    ret = writeRecord(conn, stmt, "inpAG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写数据
    ret = writeRecord(conn, stmt, "inpAG2", objIn1, recordNum2, SingleInt4Set, true);
    if (ret == GMERR_OUT_OF_MEMORY || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
        sleep(5);
        ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT,
            g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);

        AW_FUN_Log(LOG_INFO, "outA触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasbyteCallBackCount.callBackCount,
            g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(
            g_isInvokePubsubHasbyteCallBackCount.positiveCount, g_isInvokePubsubHasbyteCallBackCount.negativeCount);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasbyteCallBackCount.totalCount, g_isInvokePubsubHasbyteCallBackCount.positiveCount,
            g_isInvokePubsubHasbyteCallBackCount.negativeCount);
    }

    // 清空统计值
    ClearCountValue();

    AW_FUN_Log(LOG_STEP, "4.取消outA订阅.");
    ret = GmcUnSubscribe(stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnA, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    free(objIn1);
    free(objIn2);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void *ThreadDmlPubsubResource(void *args)
{
    int32_t ret = 0;
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataB;
    ret = TestCreateSubResource(stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    // 清空统计值
    ClearCountValue();
    // 插入数据
    int32_t recordNum1 = 140;
    int32_t recordNum2 = 140;
    // 写内存满时数据量
    int32_t memRecord = 4;
    ClearFileContent();
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpBG1写入%d条数据.", recordNum1);
    ret = writeRecord(conn, stmt, "inpBG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写数据
    ret = writeRecord(conn, stmt, "inpBG2", objIn1, recordNum2, SingleInt4Set, true);
    if (ret) {
        AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret);
        ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT,
            g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);

        AW_FUN_Log(LOG_INFO, "outB触发回滚次数%d次,需接收数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
            g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_MACRO_EXPECT_EQ_INT(
            g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    AW_FUN_Log(LOG_STEP, "3.取消outB订阅.");
    ret = GmcUnSubscribe(stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    free(objIn1);
    free(objIn2);
    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 加载升级的so
int TestLoadUpgradeDatalog(const char *soName)
{
    const int commandSize = 1024;
    char loadCommand[commandSize] = {0};
    if (soName == NULL) {
        RETURN_IFERR(FAILED);
    }
    (void)snprintf(loadCommand, commandSize, "%s/gmimport -s %s -c datalog -upgrade ./Datalog_File/%s.so", g_toolPath,
        g_connServer, soName);

    system(loadCommand);
    return 0;
}

#endif /* __SUPROLLBACK_H__ */
