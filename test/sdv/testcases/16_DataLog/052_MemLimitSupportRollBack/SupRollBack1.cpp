/* ****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2022. All rights reserved.
 Description  : datalog Datalog支持内存极限场景下可回滚
 Notes        :
 History      :
 Author       : youwanyong ywx1157510
 Modification : 2023/10/18
**************************************************************************** */
#include "SupRollBack.h"
using namespace std;

class SupRollBack : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void SupRollBack::SetUpTestCase()
{
    int32_t ret = 0;
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
}

void SupRollBack::TearDownTestCase()
{

    int32_t ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

void SupRollBack::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}

void SupRollBack::TearDown()
{
    GmcDropVertexLabel(g_stmt, "N000");
    int32_t ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    system("rm -rf *.txt");
    AW_CHECK_LOG_END();
}

class SupRollBack1 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void SupRollBack1::SetUpTestCase()
{
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=592\" \"maxSysDynSize=496\" \"maxNormalTableNum=4000\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=2048\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=256\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=32\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultHashType=cceh\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
}

void SupRollBack1::TearDownTestCase()
{

    int32_t ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    system("rm -rf /root/_datalog_/");
}

void SupRollBack1::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}

void SupRollBack1::TearDown()
{
    GmcDropVertexLabel(g_stmt, "N000");
    int32_t ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    system("rm -rf *.txt");
    AW_CHECK_LOG_END();
}

class SupRollBack2 : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void SupRollBack2::SetUpTestCase()
{
    int32_t ret = 0;
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSeMem=1024\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalShmSize=2048\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysShmSize=256\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxTotalDynSize=2048\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"maxSysDynSize=512\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"pageSize=32\"");
    system("sh $TEST_HOME/tools/modifyCfg.sh \"defaultHashType=cceh\"");
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
}

void SupRollBack2::TearDownTestCase()
{

    int32_t ret = close_epoll_thread();
    EXPECT_EQ(GMERR_OK, ret);
    testEnvClean();
    system("sh ${TEST_HOME}/tools/stop.sh");
    system("sh ${TEST_HOME}/tools/start.sh");
}

void SupRollBack2::SetUp()
{
    AW_CHECK_LOG_BEGIN();
    int32_t ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
}

void SupRollBack2::TearDown()
{
    GmcDropVertexLabel(g_stmt, "N000");
    int32_t ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    g_conn = NULL;
    g_stmt = NULL;
    system("rm -rf *.txt");
    AW_CHECK_LOG_END();
}

/* ****************************************************************************
 Description  :   001.单写数据直到内存满，触发pubsub表订阅回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack, Datalog_052_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_052_001";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnA;
    GmcStmtT *subStmtA;
    const char *subConnNameA = "testSubA";
    const char *subNameA = "testSub_ns1.A";
    ret = testSubConnect(&subConnA, &subStmtA, 1, g_epoll_reg_info, subConnNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataA;
    ret = TestCreateSubHasByte(g_stmt, subConnA, &userDataA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outA表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum = 1000;
#ifdef RUN_SIMULATE
    recordNum = 62;
#endif
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 100;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_STEP, "写入%d条数据.", recordNum);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 第二张表刚好写入45条数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, 45, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, recordNum * 45, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, 45, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.再写入数据内存满.");
    // 清空统计值
    ClearCountValue();
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, 1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2,
        RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outA触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasbyteCallBackCount.callBackCount,
        g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasbyteCallBackCount.positiveCount, g_isInvokePubsubHasbyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasbyteCallBackCount.totalCount, g_isInvokePubsubHasbyteCallBackCount.positiveCount,
            g_isInvokePubsubHasbyteCallBackCount.negativeCount);
    }
#ifdef RUN_SIMULATE
    ReadOutTableView((char *)"inpG1", (char *)"62");
    ReadOutTableView((char *)"inpG2", (char *)"45");

    ReadOutTableView((char *)"outC", (char *)"2790");
    ReadOutTableView((char *)"outD", (char *)"2790");
    ReadOutTableView((char *)"outE", (char *)"2790");
    ReadOutTableView((char *)"outF", (char *)"2790");
#else
    ReadOutTableView((char *)"inpG1", (char *)"1000");
    ReadOutTableView((char *)"inpG2", (char *)"45");

    ReadOutTableView((char *)"outC", (char *)"45000");
    ReadOutTableView((char *)"outD", (char *)"45000");
    ReadOutTableView((char *)"outE", (char *)"45000");
    ReadOutTableView((char *)"outF", (char *)"45000");
#endif
    AW_FUN_Log(LOG_STEP, "3.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnA, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   002.批量写，每批写128条，直到内存满，触发pubsub表订阅回滚，再批写512条数据，触发pubsub表订阅回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack1, Datalog_052_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_001";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnA;
    GmcStmtT *subStmtA;
    const char *subConnNameA = "testSubA";
    const char *subNameA = "testSub_ns1.A";
    SnUserDataWithFuncT *userDataA;
    ret = testSubConnect(&subConnA, &subStmtA, 1, g_epoll_reg_info, subConnNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubHasByte(g_stmt, subConnA, &userDataA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outA表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 353;
    int32_t allocSzie = 500;
    // 写内存满时数据量
    int32_t memRecord = 3;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < allocSzie; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < allocSzie; i++) {
        objIn2[i].a = i + 1000;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("gmsysview count");

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret) {
        // 获取写数据报错原因
        system("mv ../../../log/run/rgmserver/rgmserver.log server.log");
    }
    sleep(5);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    /* 每批写128条，直到内存满 */
    // 清空统计值
    ClearCountValue();
    // 批写产生256条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    if(ret != GMERR_SUB_PUSH_QUEUE_FULL){
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    }

    sleep(5);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2,
        RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outA触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasbyteCallBackCount.callBackCount,
        g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasbyteCallBackCount.positiveCount, g_isInvokePubsubHasbyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasbyteCallBackCount.totalCount, g_isInvokePubsubHasbyteCallBackCount.positiveCount,
            g_isInvokePubsubHasbyteCallBackCount.negativeCount);
    }

    // 单写删除数据
    for (int i = 0; i < 2; i++) {
        objIn3[i].a = objIn1[recordNum2 - i - 1].a;
        objIn3[i].dtlReservedCount = -1;
        objIn3[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn3, 2, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, recordNum1 * 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写512条，直到内存满 */
    memRecord = 4;
    // 清空统计值
    ClearCountValue();
    // 批写产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    if(ret != GMERR_SUB_PUSH_QUEUE_FULL){
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    }
    sleep(5);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2,
        RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outA触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasbyteCallBackCount.callBackCount,
        g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasbyteCallBackCount.positiveCount, g_isInvokePubsubHasbyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasbyteCallBackCount.totalCount, g_isInvokePubsubHasbyteCallBackCount.positiveCount,
            g_isInvokePubsubHasbyteCallBackCount.negativeCount);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2 - 2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");
    char valueArray[128] = {0};
    strcpy(valueArray, "44928");

    ReadOutTableView((char *)"outC", valueArray);
    ReadOutTableView((char *)"outD", valueArray);
    ReadOutTableView((char *)"outE", valueArray);
    ReadOutTableView((char *)"outF", valueArray);
    system("gmsysview count");

    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnA, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   003.批量写512条数据直到内存满，不发送回应，订阅超时，触发pubsub表订阅回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack, Datalog_052_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_001";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnA;
    GmcStmtT *subStmtA;
    const char *subConnNameA = "testSubA";
    const char *subNameA = "testSub_ns1.A";
    SnUserDataWithFuncT *userDataA;
    ret = testSubConnect(&subConnA, &subStmtA, 1, g_epoll_reg_info, subConnNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubHasByte(g_stmt, subConnA, &userDataA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outA表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 351;
#ifdef RUN_SIMULATE
    recordNum2 = 20;
#endif
    int32_t allocSzie = 500;
    // 写内存满时数据量
    int32_t memRecord = 4;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < allocSzie; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < allocSzie; i++) {
        objIn2[i].a = i + 1000;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写512条，直到内存满 */

    // 清空统计值
    ClearCountValue();
    // 回滚时超时不应答预期无影响
    g_isInvokePubsubHasbyteCallBackCount.needSleepTime = 2;
    g_isInvokePubsubHasbyteCallBackCount.needSleep = true;
    // 批写产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
#ifdef RUN_SIMULATE
    AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
#endif

    sleep(5);
    ret = testWaitSnRecv(
        userDataA->data, GMC_SUB_EVENT_INSERT, g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2, RECV_TIMEOUT);

    AW_FUN_Log(LOG_INFO, "outA触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasbyteCallBackCount.callBackCount,
        g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasbyteCallBackCount.positiveCount, g_isInvokePubsubHasbyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasbyteCallBackCount.totalCount, g_isInvokePubsubHasbyteCallBackCount.positiveCount,
            g_isInvokePubsubHasbyteCallBackCount.negativeCount);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");
    char valueArray[128] = {0};
    strcpy(valueArray, "44928");
#ifdef RUN_SIMULATE
    strcpy(valueArray, "2560");
#endif
    ReadOutTableView((char *)"outC", valueArray);
    ReadOutTableView((char *)"outD", valueArray);
    ReadOutTableView((char *)"outE", valueArray);
    ReadOutTableView((char *)"outF", valueArray);
    system("gmsysview count");

    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnA, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   004.批量删除512条数据通过udf占用内存，触发pubsub表订阅回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack, Datalog_052_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_001";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnA;
    GmcStmtT *subStmtA;
    const char *subConnNameA = "testSubA";
    const char *subNameA = "testSub_ns1.A";
    SnUserDataWithFuncT *userDataA;
    ret = testSubConnect(&subConnA, &subStmtA, 1, g_epoll_reg_info, subConnNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubHasByte(g_stmt, subConnA, &userDataA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outA表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 354;
#ifdef RUN_SIMULATE
    recordNum2 = 20;
#endif
    int32_t allocSzie = 500;
    // 写内存满时数据量
    int32_t memRecord = 4;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < allocSzie; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < allocSzie; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写512条，直到内存满 */

    // 清空统计值
    ClearCountValue();
    // 批删产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据.");
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
#ifdef RUN_SIMULATE
    AW_MACRO_EXPECT_EQ_INT(GMERR_SUB_PUSH_QUEUE_FULL, ret);
#else
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
#endif

    sleep(5);
    ret = testWaitSnRecv(
        userDataA->data, GMC_SUB_EVENT_INSERT, g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2, RECV_TIMEOUT);

    AW_FUN_Log(LOG_INFO, "outA触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasbyteCallBackCount.callBackCount,
        g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasbyteCallBackCount.positiveCount, g_isInvokePubsubHasbyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasbyteCallBackCount.totalCount, g_isInvokePubsubHasbyteCallBackCount.positiveCount,
            g_isInvokePubsubHasbyteCallBackCount.negativeCount);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");
    char valueArray[128] = {0};
    strcpy(valueArray, "45312");
#ifdef RUN_SIMULATE
    strcpy(valueArray, "2560");
#endif
    system("gmsysview count");

    ReadOutTableView((char *)"outC", valueArray);
    ReadOutTableView((char *)"outD", valueArray);
    ReadOutTableView((char *)"outE", valueArray);
    ReadOutTableView((char *)"outF", valueArray);
    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnA, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   005.批量更新512条数据数据通过udf占用内存，触发pubsub表订阅回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack, Datalog_052_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_001";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnA;
    GmcStmtT *subStmtA;
    const char *subConnNameA = "testSubA";
    const char *subNameA = "testSub_ns1.A";
    SnUserDataWithFuncT *userDataA;
    ret = testSubConnect(&subConnA, &subStmtA, 1, g_epoll_reg_info, subConnNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubHasByte(g_stmt, subConnA, &userDataA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outA表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 510;
    int32_t recordNum2 = 89;
    int32_t allocSzie = 1000;
#ifdef RUN_SIMULATE
    recordNum1 = 32;
    recordNum2 = 87;
#endif
    // 写内存满时数据量
    int32_t memRecord = 1;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * allocSzie);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < allocSzie; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < allocSzie; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 2;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview count");

    /* 每批写512条，直到内存满 */

    // 清空统计值
    ClearCountValue();
    // 更新产生512条数据
    AW_FUN_Log(LOG_INFO, "更新内存满数据量.", recordNum1);
    ret = UpdateRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, UpdateSingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2,
        RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outA触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasbyteCallBackCount.callBackCount,
        g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasbyteCallBackCount.positiveCount, g_isInvokePubsubHasbyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasbyteCallBackCount.totalCount, g_isInvokePubsubHasbyteCallBackCount.positiveCount,
            g_isInvokePubsubHasbyteCallBackCount.negativeCount);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");
    char valueArray[128] = {0};
    strcpy(valueArray, "45390");
#ifdef RUN_SIMULATE
    strcpy(valueArray, "2784");
#endif
    ReadOutTableView((char *)"outC", valueArray);
    ReadOutTableView((char *)"outD", valueArray);
    ReadOutTableView((char *)"outE", valueArray);
    ReadOutTableView((char *)"outF", valueArray);
    system("gmsysview count");
    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnA, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   006.单写数据直到内存满，触发pubsub表订阅回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_052_002";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataB;
    ret = TestCreateSubNoByte(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 510;
    int32_t recordNum2 = 89;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 100;
        objIn2[i].b = i + 100;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_STEP, "写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写输入表占用足够内存
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.再写入数据内存满.");
    // 清空统计值
    ClearCountValue();
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, 1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2,
        RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outA触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasbyteCallBackCount.callBackCount,
        g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasbyteCallBackCount.positiveCount, g_isInvokePubsubHasbyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasbyteCallBackCount.totalCount, g_isInvokePubsubHasbyteCallBackCount.positiveCount,
            g_isInvokePubsubHasbyteCallBackCount.negativeCount);
    }

    ReadOutTableView((char *)"outA", (char *)"45390");
    ReadOutTableView((char *)"outC", (char *)"45390");
    ReadOutTableView((char *)"outD", (char *)"45390");
    ReadOutTableView((char *)"outE", (char *)"45390");
    ReadOutTableView((char *)"outF", (char *)"45390");
    AW_FUN_Log(LOG_STEP, "3.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   007.批量写，每批写128条，直到内存满，触发pubsub表订阅回滚，再批写512条数据，触发pubsub表订阅回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_002";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    SnUserDataWithFuncT *userDataB;
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubNoByte(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 353;
    // 写内存满时数据量
    int32_t memRecord = 3;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].a = i + 1000;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写128条，直到内存满 */
    // 清空统计值
    ClearCountValue();
    // 批写产生256条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.");
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outB触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    // 单写删除数据
    for (int i = 0; i < 2; i++) {
        objIn3[i].a = objIn1[recordNum2 - i - 1].a;
        objIn3[i].dtlReservedCount = -1;
        objIn3[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn3, 2, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写512条，直到内存满 */
    memRecord = 4;
    // 清空统计值
    ClearCountValue();
    // 批写产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outB触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2 - 2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");

    // 删除数据
    AW_FUN_Log(LOG_STEP, "3.删除数据.");
    for (int i = 0; i < recordNum2 - 2; i++) {
        objIn1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2 - 2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * (recordNum2 - 2), RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, 0, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   008.批量写512条数据直到内存满，不发送回应，订阅超时，触发pubsub表订阅回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_002";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    SnUserDataWithFuncT *userDataB;
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubNoByte(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 351;
    // 写内存满时数据量
    int32_t memRecord = 4;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].a = i + 1000;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写512条，直到内存满 */

    // 清空统计值
    ClearCountValue();
    // 回滚时超时不应答预期无影响
    g_isInvokePubsubHasNobyteCallBackCount.needSleepTime = 2;
    g_isInvokePubsubHasNobyteCallBackCount.needSleep = true;
    // 批写产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(
        userDataB->data, GMC_SUB_EVENT_INSERT, g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT);

    AW_FUN_Log(LOG_INFO, "outA触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");

    ReadOutTableView((char *)"outA", (char *)"44928");
    ReadOutTableView((char *)"outC", (char *)"44928");
    ReadOutTableView((char *)"outD", (char *)"44928");
    ReadOutTableView((char *)"outE", (char *)"44928");
    ReadOutTableView((char *)"outF", (char *)"44928");
    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   009.批量删除数据通过udf占用内存，触发pubsub表订阅回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_002";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    SnUserDataWithFuncT *userDataB;
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubNoByte(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 353;
    // 写内存满时数据量
    int32_t memRecord = 4;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写512条，直到内存满 */

    // 清空统计值
    ClearCountValue();
    // 批删产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据.");
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(
        userDataB->data, GMC_SUB_EVENT_INSERT, g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT);

    AW_FUN_Log(LOG_INFO, "outB触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");

    ReadOutTableView((char *)"outA", (char *)"45184");
    ReadOutTableView((char *)"outC", (char *)"45184");
    ReadOutTableView((char *)"outD", (char *)"45184");
    ReadOutTableView((char *)"outE", (char *)"45184");
    ReadOutTableView((char *)"outF", (char *)"45184");
    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   010.批量更新数据通过udf占用内存，触发pubsub表订阅回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_002";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    SnUserDataWithFuncT *userDataB;
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubNoByte(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outA表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 508;
    int32_t recordNum2 = 89;
    // 写内存满时数据量
    int32_t memRecord = 1;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 2;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写512条，直到内存满 */

    // 清空统计值
    ClearCountValue();
    // 更新产生512条数据
    AW_FUN_Log(LOG_INFO, "更新内存满数据量.", recordNum1);
    ret = UpdateRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, UpdateSingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outA触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");

    ReadOutTableView((char *)"outA", (char *)"45212");
    ReadOutTableView((char *)"outC", (char *)"45212");
    ReadOutTableView((char *)"outD", (char *)"45212");
    ReadOutTableView((char *)"outE", (char *)"45212");
    ReadOutTableView((char *)"outF", (char *)"45212");
    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   011.单写数据直到内存满，触发pubsub资源表订阅回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_052_003";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataB;
    ret = TestCreateSubResource(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 506;
    int32_t recordNum2 = 89;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 100;
        objIn2[i].b = i + 100;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_STEP, "写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写输入表占用足够内存
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.再写入数据内存满.");
    // 清空统计值
    ClearCountValue();
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, 1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outB触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    ReadOutTableView((char *)"inpG2", (char *)"89");
    ReadOutTableView((char *)"inpG1", (char *)"506");

    ReadOutTableView((char *)"outA", (char *)"45034");
    ReadOutTableView((char *)"outC", (char *)"45034");
    ReadOutTableView((char *)"outD", (char *)"45034");
    ReadOutTableView((char *)"outE", (char *)"45034");
    ReadOutTableView((char *)"outF", (char *)"45034");
    AW_FUN_Log(LOG_STEP, "3.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   012.批量写，每批写128条，直到内存满，触发pubsub表订阅回滚，再批写512条数据，触发pubsub资源表订阅回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_003";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    SnUserDataWithFuncT *userDataB;
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubResource(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 352;
    // 写内存满时数据量
    int32_t memRecord = 2;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].a = i + 1000;
        objIn2[i].b = i + 1000;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写128条，直到内存满 */
    // 清空统计值
    ClearCountValue();
    // 批写产生256条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.");
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outB触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }
    /* 每批写512条，直到内存满 */
    memRecord = 4;
    // 清空统计值
    ClearCountValue();
    // 批写产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outB触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    ReadOutTableView((char *)"inpG2", (char *)"352");
    ReadOutTableView((char *)"inpG1", (char *)"128");

    ReadOutTableView((char *)"outA", (char *)"45056");
    ReadOutTableView((char *)"outC", (char *)"45056");
    ReadOutTableView((char *)"outD", (char *)"45056");
    ReadOutTableView((char *)"outE", (char *)"45056");
    ReadOutTableView((char *)"outF", (char *)"45056");

    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   013.批量写512条数据直到内存满，不发送回应，订阅超时，触发pubsub资源表订阅回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_003";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    SnUserDataWithFuncT *userDataB;
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubResource(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 353;
    // 写内存满时数据量
    int32_t memRecord = 2;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].a = i + 1000;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写128条，直到内存满 */
    // 清空统计值
    ClearCountValue();
    // 批写产生256条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.");
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outB触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    // 单写删除数据
    for (int i = 0; i < 2; i++) {
        objIn3[i].a = objIn1[recordNum2 - i - 1].a;
        objIn3[i].dtlReservedCount = -1;
        objIn3[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn3, 2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * 2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写512条，直到内存满 */
    memRecord = 4;
    // 清空统计值
    ClearCountValue();
    // 批写产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outB触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    ReadOutTableView((char *)"inpG2", (char *)"351");
    ReadOutTableView((char *)"inpG1", (char *)"128");

    ReadOutTableView((char *)"outA", (char *)"44928");
    ReadOutTableView((char *)"outC", (char *)"44928");
    ReadOutTableView((char *)"outD", (char *)"44928");
    ReadOutTableView((char *)"outE", (char *)"44928");
    ReadOutTableView((char *)"outF", (char *)"44928");

    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   014.批量删除数据通过udf占用内存，触发pubsub资源表订阅回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_003";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    SnUserDataWithFuncT *userDataB;
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubResource(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 353;
    // 写内存满时数据量
    int32_t memRecord = 4;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写512条，直到内存满 */

    // 清空统计值
    ClearCountValue();
    // 批删产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据.");
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(
        userDataB->data, GMC_SUB_EVENT_INSERT, g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT);

    AW_FUN_Log(LOG_INFO, "outB触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");

    ReadOutTableView((char *)"outA", (char *)"45184");
    ReadOutTableView((char *)"outC", (char *)"45184");
    ReadOutTableView((char *)"outD", (char *)"45184");
    ReadOutTableView((char *)"outE", (char *)"45184");
    ReadOutTableView((char *)"outF", (char *)"45184");

    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   015.批量更新数据通过udf占用内存，触发pubsub资源表订阅回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_003";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    SnUserDataWithFuncT *userDataB;
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubResource(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 506;
    int32_t recordNum2 = 89;
    // 写内存满时数据量
    int32_t memRecord = 1;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 2;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT, recordNum1 * recordNum2, RECV_TIMEOUT / 10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /* 每批写512条，直到内存满 */

    // 清空统计值
    ClearCountValue();
    // 更新产生512条数据
    AW_FUN_Log(LOG_INFO, "更新内存满数据量.", recordNum1);
    ret = UpdateRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, UpdateSingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);

    AW_FUN_Log(LOG_INFO, "outA触发回滚次数%d次,需接收数据%d条", g_isInvokePubsubHasNobyteCallBackCount.callBackCount,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount, g_isInvokePubsubHasNobyteCallBackCount.negativeCount);

    if (ret) {
        AW_FUN_Log(LOG_DEBUG, "预期接收%d条数据，count为正数据为%d,count为负数据%d条",
            g_isInvokePubsubHasNobyteCallBackCount.totalCount, g_isInvokePubsubHasNobyteCallBackCount.positiveCount,
            g_isInvokePubsubHasNobyteCallBackCount.negativeCount);
    }

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");
    ReadOutTableView((char *)"outA", (char *)"45034");
    ReadOutTableView((char *)"outC", (char *)"45034");
    ReadOutTableView((char *)"outD", (char *)"45034");
    ReadOutTableView((char *)"outE", (char *)"45034");
    ReadOutTableView((char *)"outF", (char *)"45034");

    AW_FUN_Log(LOG_STEP, "4.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   016.单写数据直到内存满，触发消息通知表回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_052_004";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 506;
    int32_t recordNum2 = 89;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 100;
        objIn2[i].b = i + 100;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_STEP, "写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写输入表占用足够内存
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    ClearFileContent();
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.再写入数据内存满.");

    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, 1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(2);
    // 获取用例数据，强校验数据回滚顺序
    FileContentCompare(1);
    AW_FUN_Log(LOG_INFO, "消息通知表校验数据一致性.");

    ReadOutTableView((char *)"inpG2", (char *)"89");
    ReadOutTableView((char *)"inpG1", (char *)"506");

    ReadOutTableView((char *)"outA", (char *)"45034");
    ReadOutTableView((char *)"outC", (char *)"45034");
    ReadOutTableView((char *)"outD", (char *)"45034");
    ReadOutTableView((char *)"outE", (char *)"45034");
    ReadOutTableView((char *)"outF", (char *)"45034");

    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
017.批量写，batch_msg_size(128)每批写128条，直到内存满，触发消息通知表回滚，再批写512条数据，触发消息通知表回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_004";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 2.so内写入数据并校验
    // 插入数据+
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 353;
    // 写内存满时数据量
    int32_t memRecord = 3;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].a = i + 1000;
        objIn2[i].b = i + 1000;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    // 清空文件内容
    ClearFileContent();
    /* 每批写128条，直到内存满 */
    // 批写产生256条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.");
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    sleep(5);
    // 获取用例数据，强校验数据回滚顺序
    FileContentCompare(1);
    AW_FUN_Log(LOG_INFO, "消息通知表校验数据一致性.");

    // 单写删除数据
    for (int i = 0; i < 2; i++) {
        objIn3[i].a = objIn1[recordNum2 - i - 1].a;
        objIn3[i].b = objIn1[recordNum2 - i - 1].b;
        objIn3[i].dtlReservedCount = -1;
        objIn3[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn3, 2, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 清空文件内容
    ClearFileContent();
    /* 每批写512条，直到内存满 */
    memRecord = 4;

    // 批写产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    sleep(5);
    // 获取用例数据，强校验数据回滚顺序
    FileContentCompare(1);
    AW_FUN_Log(LOG_INFO, "消息通知表校验数据一致性.");

    ReadOutTableView((char *)"inpG2", (char *)"351");
    ReadOutTableView((char *)"inpG1", (char *)"128");

    ReadOutTableView((char *)"outA", (char *)"44928");
    ReadOutTableView((char *)"outC", (char *)"44928");
    ReadOutTableView((char *)"outD", (char *)"44928");
    ReadOutTableView((char *)"outE", (char *)"44928");
    ReadOutTableView((char *)"outF", (char *)"44928");

    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   018.批量删除数据通过udf占用内存，触发消息通知表回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_004";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 353;
    // 写内存满时数据量
    int32_t memRecord = 4;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);

    /* 每批写512条，直到内存满 */
    ClearFileContent();
    // 批删产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据.");
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    // 获取用例数据，强校验数据回滚顺序
    FileContentCompare(1);
    AW_FUN_Log(LOG_INFO, "消息通知表校验数据一致性.");

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");

    // 删除数据
    AW_FUN_Log(LOG_STEP, "3.删除数据.");
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].dtlReservedCount = -1;
    }
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].dtlReservedCount = 1;
    }
    ReadOutTableView((char *)"inpG2", (char *)"0");
    ReadOutTableView((char *)"inpG1", (char *)"128");

    ReadOutTableView((char *)"outA", (char *)"0");
    ReadOutTableView((char *)"outC", (char *)"0");
    ReadOutTableView((char *)"outD", (char *)"0");
    ReadOutTableView((char *)"outE", (char *)"0");
    ReadOutTableView((char *)"outF", (char *)"0");

    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  019.批量更新数据通过udf占用内存，触发消息通知表回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_004";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 510;
    int32_t recordNum2 = 89;
    // 写内存满时数据量
    int32_t memRecord = 1;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 2;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ClearFileContent();

    /* 每批写512条，直到内存满 */

    // 更新产生512条数据
    AW_FUN_Log(LOG_INFO, "更新内存满数据量.", recordNum1);
    ret = UpdateRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, UpdateSingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");
    // 获取用例数据，强校验数据回滚顺序
    FileContentCompare(2);
    AW_FUN_Log(LOG_INFO, "消息通知表校验数据一致性.");

    ReadOutTableView((char *)"outA", (char *)"45390");
    ReadOutTableView((char *)"outC", (char *)"45390");
    ReadOutTableView((char *)"outD", (char *)"45390");
    ReadOutTableView((char *)"outE", (char *)"45390");
    ReadOutTableView((char *)"outF", (char *)"45390");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :   020.单写数据直到内存满，触发tbm表订阅回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    char soName[FILE_PATH] = "Datalog_052_005";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 506;
    int32_t recordNum2 = 89;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 100;
        objIn2[i].b = i + 100;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_STEP, "写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 写输入表占用足够内存
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    sleep(2);
    ClearFileContent();
    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.再写入数据内存满.");

    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, 1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(2);
    // 获取用例数据，强校验数据回滚顺序
    FileContentCompare(1);
    AW_FUN_Log(LOG_INFO, "tbm表校验数据一致性.");

    ReadOutTableView((char *)"inpG2", (char *)"89");
    ReadOutTableView((char *)"inpG1", (char *)"506");

    ReadOutTableView((char *)"outA", (char *)"45034");
    ReadOutTableView((char *)"outC", (char *)"45034");
    ReadOutTableView((char *)"outD", (char *)"45034");
    ReadOutTableView((char *)"outE", (char *)"45034");
    ReadOutTableView((char *)"outF", (char *)"45034");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "4.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :
021.批量写，每批写128条，直到内存满，触发tbm表订阅回滚，再批写512条数据，触发tbm表表订阅回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_005";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 2.so内写入数据并校验
    // 插入数据+
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 353;
    // 写内存满时数据量
    int32_t memRecord = 3;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].a = i + 1000;
        objIn2[i].b = i + 1000;
        objIn2[i].dtlReservedCount = 1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    // 清空文件内容
    ClearFileContent();
    /* 每批写128条，直到内存满 */
    // 批写产生256条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.");
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    sleep(5);
    // 获取用例数据，强校验数据回滚顺序
    FileContentCompare(1);
    AW_FUN_Log(LOG_INFO, "消息通知表校验数据一致性.");

    // 单写删除数据
    for (int i = 0; i < 2; i++) {
        objIn3[i].a = objIn1[recordNum2 - i - 1].a;
        objIn3[i].b = objIn1[recordNum2 - i - 1].b;
        objIn3[i].dtlReservedCount = -1;
        objIn3[i].upgradeVersion = 0;
    }
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn3, 2, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 清空文件内容
    ClearFileContent();
    /* 每批写512条，直到内存满 */
    memRecord = 4;

    // 批写产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据量.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);
    sleep(5);
    // 获取用例数据，强校验数据回滚顺序
    FileContentCompare(1);
    AW_FUN_Log(LOG_INFO, "消息通知表校验数据一致性.");

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2 - 2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");

    ReadOutTableView((char *)"outA", (char *)"44928");
    ReadOutTableView((char *)"outC", (char *)"44928");
    ReadOutTableView((char *)"outD", (char *)"44928");
    ReadOutTableView((char *)"outE", (char *)"44928");
    ReadOutTableView((char *)"outF", (char *)"44928");

    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}
/* ****************************************************************************
 Description  :   022.批量删除数据通过udf占用内存，触发tbm表回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_005";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 128;
    int32_t recordNum2 = 353;
    // 写内存满时数据量
    int32_t memRecord = 4;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum2);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum2; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum2; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);

    /* 每批写512条，直到内存满 */
    ClearFileContent();
    // 批删产生512条数据
    AW_FUN_Log(LOG_INFO, "批写内存满数据.");
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, SingleInt4Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);
    // 获取用例数据，强校验数据回滚顺序
    FileContentCompare(1);
    AW_FUN_Log(LOG_INFO, "消息通知表校验数据一致性.");

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");

    ReadOutTableView((char *)"outA", (char *)"45184");
    ReadOutTableView((char *)"outC", (char *)"45184");
    ReadOutTableView((char *)"outD", (char *)"45184");
    ReadOutTableView((char *)"outE", (char *)"45184");
    ReadOutTableView((char *)"outF", (char *)"45184");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  023.批量更新数据通过udf占用内存，触发tbm表数据回滚
 普通场景能够触发内存极限，不需要udf占用
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack2, Datalog_052_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_005";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 2.so内写入数据并校验
    // 插入数据
    int32_t recordNum1 = 510;
    int32_t recordNum2 = 89;
    // 写内存满时数据量
    int32_t memRecord = 1;
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn3 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 2;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 单写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(5);
    ClearFileContent();

    /* 每批写512条，直到内存满 */

    // 更新产生512条数据
    AW_FUN_Log(LOG_INFO, "更新内存满数据量.", recordNum1);
    ret = UpdateRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, UpdateSingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

    sleep(5);

    // 校验输出表
    ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "2.校验数据.");
    // 获取用例数据，强校验数据回滚顺序
    FileContentCompare(2);
    AW_FUN_Log(LOG_INFO, "消息通知表校验数据一致性.");
    ReadOutTableView((char *)"inpG2", (char *)"89");
    ReadOutTableView((char *)"inpG1", (char *)"510");

    ReadOutTableView((char *)"outA", (char *)"45390");
    ReadOutTableView((char *)"outC", (char *)"45390");
    ReadOutTableView((char *)"outD", (char *)"45390");
    ReadOutTableView((char *)"outE", (char *)"45390");
    ReadOutTableView((char *)"outF", (char *)"45390");
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放资源空间
    free(objIn1);
    free(objIn2);
    free(objIn3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
024.查询内存视图V$COM_DYN_CTX后，加载多个so直到内存不足，查询CATA_GENERAL_INFO视图,查询PTL_DATALOG_SO_INFO视图，写数据，读数据，更新数据，删除数据，卸载so
，查询内存视图V$COM_DYN_CTX，以上操作循环100次
Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack1, Datalog_052_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    char errorMsg5[errCodeLen] = {0};
    char errorMsg6[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    (void)snprintf(errorMsg5, errCodeLen, "GMERR-%d", GMERR_INTERNAL_ERROR);
    (void)snprintf(errorMsg6, errCodeLen, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(6, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5, errorMsg6);
    char soName[FILE_PATH] = "Datalog_052_005";
    char soName1[FILE_PATH] = "maxcountresourcetable001";
    char soName2[FILE_PATH] = "maxcountresourcetable002";
    char soName3[FILE_PATH] = "maxcountresourcetable003";
    char soName4[FILE_PATH] = "Datalog_052_024";
    system("gmsysview -q V\\$CATA_GENERAL_INFO");
    system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");
    system("gmsysview -q V\\$PTL_DATALOG_SO_INFO");
    system("gmsysview count");
    int32_t ret = 0;
#if defined ENV_RTOSV2X || defined RUN_SIMULATE
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    int32_t cycle = 15;

    while (cycle > 0) {
        // 卸载同名datalog.so
        TestUninstallDatalog(soName, NULL, false);
        ret = LoadSoFile(soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "1.编译加载so.");

        // 2.so内写入数据并校验
        // 插入数据
        int32_t recordNum1 = 510;
        int32_t recordNum2 = 89;
        // 写内存满时数据量
        int32_t memRecord = 1;
        SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
        if (objIn1 == NULL) {
            AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
        }
        SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
        if (objIn2 == NULL) {
            AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
        }

        SingleInt4 *objIn3 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
        if (objIn3 == NULL) {
            AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
        }
        for (int i = 0; i < recordNum1; i++) {
            objIn1[i].a = i + 1;
            objIn1[i].b = i + 1;
            objIn1[i].dtlReservedCount = 1;
            objIn1[i].upgradeVersion = 0;
        }

        for (int i = 0; i < recordNum1; i++) {
            objIn2[i].a = i + 1;
            objIn2[i].b = i + 2;
            objIn2[i].dtlReservedCount = -1;
            objIn2[i].upgradeVersion = 0;
        }
        // 单写数据
        AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
        ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 单写数据
        ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 再加载so预期内存不足加载失败
        ret = LoadSoFile(soName4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = writeRecord(g_conn, g_stmt, "ns1.inpG2", objIn1, 50, SingleInt4Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = writeRecord(g_conn, g_stmt, "ns1.inpG1", objIn1, 10, SingleInt4Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        system("gmsysview -q V\\$CATA_GENERAL_INFO");
        system("gmsysview -q V\\$CATA_VERTEX_LABEL_INFO");

        ret = LoadSoFile(soName1);
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

        ret = querySoView(soName1, soName1);
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);

        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$CATA_VERTEX_LABEL_INFO", g_toolPath);
        ret = executeCommand(g_command, "inpG2", "IS_DELETED: 0");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$CATA_GENERAL_INFO", g_toolPath);
        ret = executeCommand(
            g_command, "VERTEX_LABEL_NUM: 21", "TBM_TABLE_NUM: 1", "MSG_NOTIFY_TABLE_NUM: 1", "RESOURCE_POOL_NUM: 0");
        if (ret) {

            system("gmsysview -q V\\$CATA_GENERAL_INFO");
        }
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        sleep(5);
        ClearFileContent();

        /* 每批写512条，直到内存满 */

        // 更新产生512条数据
        AW_FUN_Log(LOG_INFO, "更新内存满数据量.", recordNum1);
        ret = UpdateRecord(g_conn, g_stmt, "inpG2", objIn2, memRecord, UpdateSingleInt4Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OUT_OF_MEMORY, ret);

        sleep(5);
        // 校验输出表
        ret = readRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = readRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "2.校验数据.");
        // 获取用例数据，强校验数据回滚顺序
        FileContentCompare(2);
        AW_FUN_Log(LOG_INFO, "消息通知表校验数据一致性.");

        ReadOutTableView((char *)"inpG2", (char *)"89");
        ReadOutTableView((char *)"inpG1", (char *)"510");
        ReadOutTableView((char *)"ns1.inpG2", (char *)"50");
        ReadOutTableView((char *)"ns1.inpG1", (char *)"10");

        ReadOutTableView((char *)"outA", (char *)"45390");
        ReadOutTableView((char *)"outC", (char *)"45390");
        ReadOutTableView((char *)"outD", (char *)"45390");
        ReadOutTableView((char *)"outE", (char *)"45390");
        ReadOutTableView((char *)"outF", (char *)"45390");

        ReadOutTableView((char *)"ns1.outA", (char *)"500");
        ReadOutTableView((char *)"ns1.outC", (char *)"500");
        ReadOutTableView((char *)"ns1.outD", (char *)"500");
        ReadOutTableView((char *)"ns1.outE", (char *)"500");
        ReadOutTableView((char *)"ns1.outF", (char *)"500");

        // 4.卸载so
        AW_FUN_Log(LOG_STEP, "5.卸载so.");
        ret = TestUninstallDatalog(soName);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestUninstallDatalog(soName4);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 释放资源空间
        free(objIn1);
        free(objIn2);
        free(objIn3);
        sleep(10);
        cycle--;
    }
#if defined ENV_RTOSV2X || defined RUN_SIMULATE
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = testGmcPrepareStmtByLabelName(g_stmt, "NoExist", GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    GmcClearCache(g_stmt, NULL, 0, GMC_VERTEX_LABEL_TYPE);
    sleep(5);
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$CATA_VERTEX_LABEL_INFO", g_toolPath);
    ret = executeCommand(g_command, "inpG2");
    AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    if (!ret) {
        system(g_command);
    }

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  025.多线程，线程1加载so后进行DMl操作，线程2重复加载卸载so直到内存不足
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack1, Datalog_052_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    char errorMsg5[errCodeLen] = {0};
    char errorMsg6[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    (void)snprintf(errorMsg5, errCodeLen, "GMERR-%d", GMERR_INTERNAL_ERROR);
    (void)snprintf(errorMsg6, errCodeLen, "GMERR-%d", GMERR_INVALID_NAME);
    AW_ADD_ERR_WHITE_LIST(6, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5, errorMsg6);
    system("gmsysview -q V\\$CATA_GENERAL_INFO");
    int32_t ret = 0;
#if defined ENV_RTOSV2X || defined RUN_SIMULATE
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif

    // 开启多个线程
    int32_t thread_num = 2;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, ThreadDml, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 普通表
    ret = pthread_create(&client_thr[1], NULL, ThreadInstall, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

#if defined ENV_RTOSV2X || defined RUN_SIMULATE
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :  026.加载含（相同输入表投影到不同输出表）触发内存超限回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack, Datalog_052_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    AW_ADD_ERR_WHITE_LIST(4, errorMsg1, errorMsg2, errorMsg3, errorMsg4);
    char soName[FILE_PATH] = "Datalog_052_026";
    int32_t ret = 0;


    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 订阅pubsub含变长byte表
    GmcConnT *subConnA;
    GmcStmtT *subStmtA;
    const char *subConnNameA = "testSubA";
    const char *subNameA = "testSub_ns1.A";
    ret = testSubConnect(&subConnA, &subStmtA, 1, g_epoll_reg_info, subConnNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    SnUserDataWithFuncT *userDataA;
    ret = TestCreateSubHasByte(g_stmt, subConnA, &userDataA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outA表订阅");

    // 订阅pubsub资源表
    GmcConnT *subConnB;
    GmcStmtT *subStmtB;
    const char *subConnNameB = "testSubB";
    const char *subNameB = "testSub_ns1.B";
    SnUserDataWithFuncT *userDataB;
    ret = testSubConnect(&subConnB, &subStmtB, 1, g_epoll_reg_info, subConnNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = TestCreateSubResource(g_stmt, subConnB, &userDataB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "申请outB表订阅");

    // 插入数据
    int32_t recordNum1 = 140;
    int32_t recordNum2 = 70;
    // 写内存满时数据量
    int32_t memRecord = 4;
    ClearFileContent();
    SingleInt4 *objIn1 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    SingleInt4 *objIn2 = (SingleInt4 *)malloc(sizeof(SingleInt4) * recordNum1);
    if (objIn2 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 0;
    }

    for (int i = 0; i < recordNum1; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].dtlReservedCount = -1;
        objIn2[i].upgradeVersion = 0;
    }
    // 单写数据
    AW_FUN_Log(LOG_INFO, "inpG1写入%d条数据.", recordNum1);
    ret = writeRecord(g_conn, g_stmt, "inpG1", objIn1, recordNum1, SingleInt4Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 批写数据
    ret = writeRecord(g_conn, g_stmt, "inpG2", objIn1, recordNum2, SingleInt4Set, true);
    if (ret == GMERR_OUT_OF_MEMORY || ret == GMERR_SUB_PUSH_QUEUE_FULL) {
        AW_MACRO_EXPECT_NE_INT(GMERR_OK, ret);
    } else {
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    sleep(5);
    // 回调次数验证
    ret = testWaitSnRecv(userDataA->data, GMC_SUB_EVENT_INSERT, g_isInvokePubsubHasbyteCallBackCount.positiveCount * 2,
        RECV_TIMEOUT / 10);
    AW_FUN_Log(LOG_INFO, "含变长byte触发回滚次数%d次,需接收数据%d条",
        g_isInvokePubsubHasbyteCallBackCount.callBackCount, g_isInvokePubsubHasbyteCallBackCount.totalCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitSnRecv(userDataB->data, GMC_SUB_EVENT_INSERT,
        g_isInvokePubsubHasNobyteCallBackCount.positiveCount * 2, RECV_TIMEOUT / 10);
    AW_FUN_Log(LOG_INFO, "固定资源表触发回滚次数%d次,需接收数据%d条",
        g_isInvokePubsubHasNobyteCallBackCount.callBackCount, g_isInvokePubsubHasNobyteCallBackCount.totalCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 文件内容验证tbm及消息通知表回滚内容一致性
    // tbm
    FileContentCompare(1);
    // 消息通知表
    FileContentCompare(0);

    AW_FUN_Log(LOG_STEP, "3.取消订阅.");
    ret = GmcUnSubscribe(g_stmt, subNameA);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnA, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataA->data);
    free(userDataA);

    ret = GmcUnSubscribe(g_stmt, subNameB);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testSubDisConnect(subConnB, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testSnFreeUserData(userDataB->data);
    free(userDataB);

    free(objIn1);
    free(objIn2);
    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  :
027.so中含所有类型的输出表及pubsub资源表，分别在不同的topo图中，开启多线程分别写这些输出表的输入表直到内存超限回滚
 Author       : youwanyong
**************************************************************************** */
TEST_F(SupRollBack, Datalog_052_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    char errorMsg2[errCodeLen] = {0};
    char errorMsg3[errCodeLen] = {0};
    char errorMsg4[errCodeLen] = {0};
    char errorMsg5[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_SUB_PUSH_QUEUE_FULL);
    (void)snprintf(errorMsg2, errCodeLen, "GMERR-%d", GMERR_MEM_CTX_MEMORY_EXCEEDED_THRESHOLD);
    (void)snprintf(errorMsg3, errCodeLen, "GMERR-%d", GMERR_MEMORY_OPERATE_FAILED);
    (void)snprintf(errorMsg4, errCodeLen, "GMERR-%d", GMERR_OUT_OF_MEMORY);
    (void)snprintf(errorMsg5, errCodeLen, "GMERR-%d", GMERR_COMMON_STREAM_OVERLOAD);
    AW_ADD_ERR_WHITE_LIST(5, errorMsg1, errorMsg2, errorMsg3, errorMsg4, errorMsg5);
    char soName[FILE_PATH] = "Datalog_052_027";
    int32_t ret = 0;

    // 卸载同名datalog.so
    TestUninstallDatalog(soName, NULL, false);
    ret = LoadSoFile(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "1.编译加载so.");

    // 开启多个线程
    int32_t thread_num = 4;
    pthread_t client_thr[thread_num];
    ret = pthread_create(&client_thr[0], NULL, ThreadDmlPubsubResource, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr[1], NULL, ThreadDmlPubsub, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr[2], NULL, ThreadDmlMsg, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = pthread_create(&client_thr[3], NULL, ThreadDmlTbm, NULL);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (uint32_t i = 0; i < thread_num; i++) {
        ret = pthread_join(client_thr[i], NULL);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    // 4.卸载so
    AW_FUN_Log(LOG_STEP, "5.卸载so.");
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
    AW_FUN_Log(LOG_STEP, "test end.");
}
