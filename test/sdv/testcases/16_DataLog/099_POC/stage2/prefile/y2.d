// ifm.d
namespace Ifm {
    %table ConfigIf(
        nsId: int4, ifName: byte64, ifIndex: int4, type: int2, phyPort: int4, masterIfIndex: int4, state: int1)
        { index(0(nsId, ifName)), update_partial }
    %table PublishNif(
        ifIndex: int4, ifName: byte64, type: byte16, tp: int4, chip_unit: int1, oda_phy_port: int1,
        state: int1, mac: byte6, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
        unit_id: int4, port_id: int4, cpu_type: int1)
        { index(0(ifIndex)), update_partial }
    %table ConfigAttributes(
        nsId: int4, ifIndex: int4, phyState: int1, mac: byte6, mtu: int4, mtu6: int4, autoneg: int1,
        speed: int8, duplex: int1, combo: int1, loopback: int1, hasV4Addr: int1, alias: byte64,
        service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1, mru: int2,
        link_type: int1, eth_class: int1)
        { index(0(nsId, ifIndex)), update_partial }
    // %table ConfigHpfAttributes(nsId: int4, ifIndex: int4, peerMac: byte6)
        // { index(0(nsId, ifIndex)), update_partial }
    %table ConfigIfIpv4Addr(ifIndex: int4, vrfIndex: int4, address: int4, maskLen: int2, type: int2)
        { index(0(ifIndex, vrfIndex, address, maskLen, type)),
          index(1(ifIndex)),
          update_partial }

    %table Agg_Attributes(
        nsId: int4, ifIndex: int4, ifName: byte64, phyState: int1, adminState: int1,
        onboard_state: int1, if_bw: int8, tp: int4, tb: int4, chip_unit: int1, oda_phy_port: int1, mac: byte6,
        autoneg: int1, speed: int8, duplex: int1, combo: int1, loopback: int1, mtu: int4, mtu6: int4,
        type: int2, hasV4Addr: int1, service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1,
        mru: int2, link_type: int1, eth_class: int1, cpu_type: int1)

    %function GetIfType(ifType: byte16 -> type: int2)
    %function CalculateIfPhyState(type: int2, srcPhyState: int1, adminState: int1 -> phyState: int1)
    %function GetIfShowType(ifName: byte64 -> showType: int2)
    %function CalculateIfLinkState(ifIndex: int4, type: int2, phyState: int1, vlanIfLinkState: int1 -> linkState: int1)
    %function CalculateIfOperState(linkState: int1 -> operState: int1)
    %function CalculateIfIpv4State(type: int2, linkState: int1, hasV4Addr: int1 -> ipv4State: int1)

%readonly ConfigIf
%readonly PublishNif
%readonly ConfigAttributes
%readonly ConfigIfIpv4Addr
%readonly Agg_Attributes

%readonly GetIfType
%readonly CalculateIfPhyState
%readonly GetIfShowType
%readonly CalculateIfLinkState
%readonly CalculateIfOperState
%readonly CalculateIfIpv4State

    Agg_Attributes(
        nsId, ifIndex, ifName, phyState, adminState, 0, 0, tp, 0, 0, 0, mac,
        autoneg, speed, duplex, combo, loopback, mtu, mtu6, type, hasV4Addr, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, 0) :-
        ConfigIf(
            nsId, ifName, ifIndex, type,
            tp, - , srcPhyState),
        ConfigAttributes(
            nsId, ifIndex, adminState, mac, mtu, mtu6,
            autoneg, speed, duplex, combo, loopback, hasV4Addr, - , service_type, autoneg_cap,
            autoneg_adv_cap, medium, mru, link_type, eth_class),
        CalculateIfPhyState(type, srcPhyState, adminState, phyState).
    Agg_Attributes(
        0, ifIndex, ifName, phyState, adminState, 0, 0, tp, 0, chip_unit, oda_phy_port, mac,
        autoneg, speed, duplex, combo, loopback, mtu, mtu6, type, hasV4Addr, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type) :-
        PublishNif(
            ifIndex, ifName, ifType, tp, chip_unit, oda_phy_port, srcPhyState, mac, -, -, -, -, -, -, cpu_type),
        ConfigAttributes(
            -, ifIndex, adminState, -, mtu, mtu6,
            autoneg, speed, duplex, combo, loopback, hasV4Addr, - , service_type,
            autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class),
        GetIfType(ifType, type),
        CalculateIfPhyState(type, srcPhyState, adminState, phyState).

    // add logic interface information 2 yif
    %table Yif_If_ConfigIf(ifIndex: int4, ifName: byte64, ifType: int2, state: int1)
    {
        notify,
        index(0(ifIndex))
    }

    %table Yif_If_ConfigPhyState1(ifIndex: int4, ifName: byte64, phyState: int1)

    %table Yif_If_ConfigPhyState(ifIndex: int4, ifName: byte64, phyState: int1)
    {
        notify,
        index(0(ifIndex))
    }

    %table Yif_If_ConfigMac(ifIndex: int4, ifName: byte64, mac: byte6)
    {
        notify,
        index(0(ifIndex))
    }

    Yif_If_ConfigIf(ifIndex, ifName, ifType, state) :-
        ConfigIf(-, ifName, ifIndex, ifType, -, -, state).

    Yif_If_ConfigPhyState1(ifIndex, ifName, adminState) :-
        PublishNif(ifIndex, ifName, -, -, -, -, -, -, -, -, -, -, -, -, -),
        Agg_Attributes(-, ifIndex, ifName, -, adminState, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).

    Yif_If_ConfigPhyState(ifIndex, ifName, adminState) :-
        Yif_If_ConfigPhyState1(ifIndex, ifName, adminState).

    Yif_If_ConfigMac(ifIndex, ifName, mac) :-
        PublishNif(ifIndex, ifName, -, -, -, -, -, -, -, -, -, -, -, -, -),
        ConfigAttributes(-, ifIndex, -, mac, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).
}

namespace Abstract {
    a_if_name(ifIndex, nsId, ifName, type, showType, "00", "00") :-
            Ifm.Agg_Attributes(nsId, ifIndex, ifName, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, type, -, -, -, -, -, -, -, -, -),
            Ifm.GetIfShowType(ifName, showType).
    a_if_phy(
        ifIndex, ifBw, phyState, adminState, onboardState,
        tb, tp, chip_unit, oda_phy_port, mac, autoeng, speed, duplex, combo, loopback, dev_id, chassis_id, slot_id, card_id, unit_id, port_id, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type) :-
        Ifm.PublishNif(ifIndex, -, -, -, chip_unit, oda_phy_port, -, -, dev_id, chassis_id, slot_id, card_id, unit_id, port_id, -),
        Ifm.Agg_Attributes(
            -, ifIndex, -, phyState, adminState, onboardState, ifBw,
            tp, tb, chip_unit, oda_phy_port, mac, autoeng, speed, duplex, combo, loopback, -, -, -, -, service_type,
            autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type).
    a_if_phy(
        ifIndex, ifBw, phyState, adminState, onboardState,
        tb, tp, 0, 0, mac, autoeng, speed, duplex, combo, loopback, 0, 0, 0, 0, 0, 0, service_type,
        autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type) :-
        Ifm.ConfigIf(-, -, ifIndex, -, -, -, -),
        Ifm.Agg_Attributes(
            -, ifIndex, -, phyState, adminState, onboardState, ifBw,
            tp, tb, -, -, mac, autoeng, speed, duplex, combo, loopback, -, -, -, -, service_type,
            autoneg_cap, autoneg_adv_cap, medium, mru, link_type, eth_class, cpu_type).
    a_if_link(ifIndex, 0, linkState, operState) :-
        Ifm.Agg_Attributes( -, ifIndex, -, phyState, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, type, -, -, -, -, -, -, -, -, -),
        Ifm.CalculateIfLinkState(ifIndex, type, phyState, 0, linkState),
        Ifm.CalculateIfOperState(linkState, operState).
    a_if_net(ifIndex, 0, 0, mtu, ipv4State) :-
        Ifm.Agg_Attributes(-, ifIndex, -, phyState, -, -, -, -, -, -, -, -, -, -, -, -, -, mtu, -, type, hasV4Addr, -, -, -, -, -, -, -, -),
        Ifm.CalculateIfLinkState(ifIndex, type, phyState, 0, linkState),
        Ifm.CalculateIfIpv4State(type, linkState, hasV4Addr, ipv4State).
    a_if_net6(ifIndex, 0, mtu6, 1) :-
        Ifm.ConfigAttributes(-, ifIndex, -, -, -, mtu6, -, -, -, -, -, -, - , -, -, -, -, -, -, -).
    a_config_if_ipv4_addr(if_index, vrf_index, address, mask_len, type) :-
        Ifm.ConfigIfIpv4Addr(if_index, vrf_index, address, mask_len, type).
}

// bridge.d
namespace BR {

/*
 * 1.INPUT TABLES
*/

%table Bridge(ns_id: int4, br_id: int4, br_name: byte16, vlan_filter: int1) {index(0(ns_id, br_id)), update_partial}
%table BridgeMacAttr(ns_id: int4, br_id: int4, mac_age_time: int4, mac_thrd_up: int1, mac_thrd_down: int1) {index(0(ns_id, br_id)), update_partial}
%table Vlan(ns_id: int4, br_id: int4, vlan_id: int2, vlan_type: int1, name: byte32, desc: byte81) {index(0(ns_id, br_id, vlan_id)), update_partial}
%table VlanMacAttr(ns_id: int4, br_id: int4, vlan_id: int2, learn: int1, limit: int4, limit_act: int1, limit_alm: int1) {index(0(ns_id, br_id, vlan_id)), update_partial}
%table VlanTagPort(ns_id: int4, br_id: int4, vlan_id: int2, port_index: int4, if_index: int4) {index(0(ns_id, br_id, vlan_id, port_index)), index(1(if_index)), update_partial}
%table VlanUntagPort(ns_id: int4, br_id: int4, vlan_id: int2, port_index: int4, if_index: int4) {index(0(ns_id, br_id, vlan_id, port_index)), index(1(if_index)), update_partial}
%table Port(if_index: int4, port_index: int4, ns_id: int4, br_id: int4, pvid: int2, use: int1, link_type: int1) {index(0(if_index)), index(1(port_index)), update_partial}
%table PortIsolateGrp0(if_index: int4, port_index: int4, grp: int1)
%table PortIsolateGrp(if_index: int4, port_index: int4, grp: int1) {index(0(if_index)), update_partial}
%table PortMacAttr(if_index: int4, port_index: int4, learn: int1, learn_act: int1, limit: int4, limit_act: int1, limit_alm: int1) {index(0(if_index)), index(1(port_index)), update_partial}
%table PortSecurity(if_index: int4, port_index: int4, mac_sec: int1, mac_sec_max: int4, mac_sec_act: int1, mac_sec_age_time: int4) { index(0(if_index)), update_partial}


/*
 * 2.MID TABLES
*/

%table VlanState0(ns_id: int4, br_id: int4, vlan_id: int2, state: int1)
%table VlanState(ns_id: int4, br_id: int4, vlan_id: int2, state: int1)
%table VlanPortTagBitmap(ns_id: int4, br_id: int4, vlan_id: int2, bitmap: byte16) {index(0(ns_id, br_id, vlan_id))}
%table VlanPortUnTagBitmap(ns_id: int4, br_id: int4, vlan_id: int2, bitmap: byte16) {index(0(ns_id, br_id, vlan_id))}
%table PortVlanTagBitmap(port_index: int4, ns_id: int4, br_id: int4, bitmap: byte512) {index(0(port_index, ns_id, br_id))}
%table PortVlanUntagBitmap(port_index: int4, ns_id: int4, br_id: int4, bitmap: byte512) {index(0(port_index, ns_id, br_id))}
%table PortIsolateGrps(if_index: int4, port_index: int4, grps: byte64) {index(0(if_index, port_index))}
%table PortPort(port_index: int4, isolate_port_index: int4)
%table PortIfPortIf(if_index: int4, isolate_if_index: int4)

%table AllIfPhyState(ifIndex: int4, type: int2, phy_state: int1)
%table VlanIfLinkState0(if_index: int4, link_state: int1)
%table VlanIfLinkState(if_index: int4, link_state: int1)

%table Tbl_Bridge(ns_id: int4, br_id: int4, br_name: byte16, vlan_filter: int1,
                 mac_age_time: int4, mac_thrd_up: int1, mac_thrd_down: int1,
                 mac_pri1_allow_flapping: int1, mac_pri2_allow_flapping: int1, mac_pri3_allow_flapping: int1, mac_pri4_allow_flapping: int1) {index(0(ns_id, br_id))}
%table Tbl_Vlan(ns_id: int4, br_id: int4, vlan_id: int2, vlan_type: int1, name: byte32, desc: byte81, mc_id: int4,
               untag_port_bitmap: byte16, tag_port_bitmap: byte16, state: int1,
               mac_learn: int1, mac_limit_max: int4, mac_limit_act: int1, mac_limit_alarm: int1) {index(0(ns_id, br_id, vlan_id))}
%table Tbl_Port(if_index: int4, port_index: int4, ns_id: int4, br_id: int4, use: int1, link_type: int1,
               pvid: int2, vlan_tag_bitmap: byte512, vlan_untag_bitmap: byte512, isolate_grps: byte64,
               mac_learn: int1, mac_learn_act: int1, mac_learn_pri: int1,
               mac_limit_max: int4, mac_limit_act: int1, mac_limit_alarm: int1,
               mac_sec: int1, mac_sec_max: int4, mac_sec_act: int1, mac_sec_age_time: int4)
{
    index(0(if_index, port_index, ns_id, br_id)),
    index(1(port_index))
}

/*
 * 3.OUTPUT TABLES
*/

%table Hpf_Br(br_id: int4, vlan_filter: int1) { index(0(br_id)), update }
%table Hpf_Vlan(br_id: int4, vlan_id: int2) { index(0(br_id)), update }
%table Hpf_PortMaster(if_index: int4, br_id: int4, l2_port: int1) { index(0(if_index)), update }
%table Hpf_PortPvid(if_index: int4, pvid: int2) { index(0(if_index)), update }
%table Hpf_PortVlan(if_index: int4, vlan_tag_bitmap: byte512, vlan_untag_bitmap: byte512) { index(0(if_index)), update }
%table Hpf_PortUntagVlan(if_index: int4, vlan_untag_bitmap: byte512) { index(0(if_index)), update }
%table Hpf_PortIsolate(if_index: int4, isolate_if_index: int4) { index(0(if_index, isolate_if_index)), update }
%table Hpf_MacAgingTime(br_id: int4, mac_age_time: int4) { index(0(br_id)), update }
%table Hpf_PortMacLearnLimit(if_index: int4, learn: int1, learn_act: int1, limit: int4, limit_act: int1, limit_alm: int1) { index(0(if_index)), update }

/*
 * 4.FUNCTIONS
*/
%function NotEqual1(a: int1, b: int1)
%function NotEqual2(a: int2, b: int2)
%function NotEqual4(a: int4, b: int4)
%function CombineVlanIfIndex(ns_id: int4, br_id: int4, vlan_id: int2 -> ifIndex: int4)
%aggregate Tbl2Bitmap16(port: int4 -> port_bitmap: byte16)
%aggregate Tbl2Bitmap512(vlan: int2 -> vlan_bitmap: byte512)
%aggregate OrLinkState(link_state: int1 -> vlan_state: int1)
%aggregate MergePortIsolateGrp(grp: int1 -> grps: byte64)

/*
 * 5.RULES
*/

VlanPortTagBitmap (ns_id, br_id, vlan_id, bitmap) :-
    VlanTagPort(ns_id, br_id, vlan_id, port_index, -) GROUP-BY(ns_id, br_id, vlan_id) Tbl2Bitmap16(port_index, bitmap).

VlanPortUnTagBitmap (ns_id, br_id, vlan_id, bitmap) :-
    VlanUntagPort(ns_id, br_id, vlan_id, port_index, -) GROUP-BY(ns_id, br_id, vlan_id) Tbl2Bitmap16(port_index, bitmap).

PortVlanTagBitmap(port_index, ns_id, br_id, bitmap) :-
    VlanTagPort(ns_id, br_id, vlan_id, port_index, -) GROUP-BY(ns_id, br_id, port_index) Tbl2Bitmap512(vlan_id, bitmap).

PortVlanUntagBitmap(port_index, ns_id, br_id, bitmap) :-
    VlanUntagPort(ns_id, br_id, vlan_id, port_index, -) GROUP-BY(ns_id, br_id, port_index) Tbl2Bitmap512(vlan_id, bitmap).

AllIfPhyState(ifIndex, type, phy_state) :-
    Ifm.Agg_Attributes(-, ifIndex, -, phy_state, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -, type, -, -, -, -, -, -, -, -, -).

VlanState0 (ns_id, br_id, vlan_id, phy_state) :-
    VlanTagPort(ns_id, br_id, vlan_id, -, if_index), AllIfPhyState(if_index, 0, phy_state).
VlanState0 (ns_id, br_id, vlan_id, phy_state) :-
    VlanUntagPort(ns_id, br_id, vlan_id, -, if_index), AllIfPhyState(if_index, 0, phy_state).
VlanState0 (ns_id, br_id, vlan_id, 0) :-
    VlanTagPort(ns_id, br_id, vlan_id, -, -1).
VlanState0 (ns_id, br_id, vlan_id, 0) :-
    VlanUntagPort(ns_id, br_id, vlan_id, -, -1).
VlanState (ns_id, br_id, vlan_id, vlan_state) :-
    VlanState0(ns_id, br_id, vlan_id, state) GROUP-BY(ns_id, br_id, vlan_id) OrLinkState(state, vlan_state).

PortIsolateGrp0(if_index, port_index, grp) :- PortIsolateGrp(if_index, port_index, grp).
PortIsolateGrps(if_index, port_index, grps) :-
    PortIsolateGrp(if_index, port_index, grp) GROUP-BY(if_index, port_index) MergePortIsolateGrp(grp, grps).
PortPort(port_index, isolate_port_index) :-
    PortIsolateGrp(-, port_index, grps1), PortIsolateGrp0(-, isolate_port_index, grps1),
    NotEqual4(port_index, isolate_port_index),
    NotEqual4(port_index, -1), NotEqual4(isolate_port_index, -1),
    NotEqual1(grps1, 0).
PortIfPortIf(if_index, isolate_if_index) :-
    PortIsolateGrp(if_index, -, grps1), PortIsolateGrp0(isolate_if_index, -, grps1),
    NotEqual4(if_index, isolate_if_index),
    NotEqual4(if_index, -1), NotEqual4(isolate_if_index, -1),
    NotEqual4(if_index, 0),  NotEqual4(isolate_if_index, 0),
    NotEqual1(grps1, 0).

Tbl_Bridge(ns_id, br_id, br_name, vlan_filter, mac_age_time, mac_thrd_up, mac_thrd_down, 0, 0, 0, 0) :-
    Bridge(ns_id, br_id, br_name, vlan_filter),
    BridgeMacAttr(ns_id, br_id, mac_age_time, mac_thrd_up, mac_thrd_down).

Tbl_Vlan(ns_id, br_id, vlan_id, vlan_type, name, desc, 0, untag_port_bitmap, tag_port_bitmap, state, mac_learn, mac_limit_max, mac_limit_act, mac_limit_alarm) :-
    Vlan(ns_id, br_id, vlan_id, vlan_type, name, desc),
    VlanPortTagBitmap(ns_id, br_id, vlan_id, tag_port_bitmap),
    VlanPortUnTagBitmap(ns_id, br_id, vlan_id, untag_port_bitmap),
    VlanState(ns_id, br_id, vlan_id, state),
    VlanMacAttr(ns_id, br_id, vlan_id, mac_learn, mac_limit_max, mac_limit_act, mac_limit_alarm),
    NotEqual2(vlan_id, 0).

Tbl_Port(if_index, port_index, ns_id, br_id, use, link_type, pvid, vlan_tag_bitmap, vlan_untag_bitmap, isolate_grps, mac_learn, mac_learn_act, 0, mac_limit_max, mac_limit_act, mac_limit_alarm, mac_sec, mac_sec_max, mac_sec_act, mac_sec_age_time) :-
    Port(if_index, port_index, ns_id, br_id, pvid, use, link_type),
    PortVlanTagBitmap(port_index, ns_id, br_id, vlan_tag_bitmap),
    PortVlanUntagBitmap(port_index, ns_id, br_id, vlan_untag_bitmap),
    PortIsolateGrps(if_index, port_index, isolate_grps),
    PortMacAttr(if_index, -, mac_learn, mac_learn_act, mac_limit_max, mac_limit_act, mac_limit_alarm),
    PortSecurity(if_index, -, mac_sec, mac_sec_max, mac_sec_act, mac_sec_age_time),
    NotEqual4(port_index, -1).

VlanIfLinkState0(ifIndex, state) :-
    Tbl_Vlan(ns_id, br_id, vlan_id, -, -, -, -, -, -, state, -, -, -, -),
    CombineVlanIfIndex(ns_id, br_id, vlan_id, ifIndex).
VlanIfLinkState(ifIndex, link_state) :-
    VlanIfLinkState0(ifIndex, link_state).
VlanIfLinkState(ifIndex, 0) :-
  AllIfPhyState(ifIndex, -, -),
  NOT VlanIfLinkState0(ifIndex, -).

/* 6.for hpf table */
Hpf_Br(br_id, vlan_filter) :-
    Tbl_Bridge(-, br_id, -, vlan_filter, -, -, -, -, -, -, -).

Hpf_Vlan(br_id, vlan_id) :-
    Tbl_Vlan(-, br_id, vlan_id, -, -, -, -, -, -, -, -, -, -, -).

Hpf_PortMaster(if_index, br_id, l2_port) :-
    Tbl_Port(if_index, -, -, br_id, l2_port, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -).

Hpf_PortPvid(if_index, pvid) :-
    Tbl_Port(if_index, -, -, -, 1, -, pvid, -, -, -, -, -, -, -, -, -, -, -, -, -).

Hpf_PortVlan(if_index, vlan_tag_bitmap, vlan_untag_bitmap) :-
    Tbl_Port(if_index, -, -, -, 1, -, -, vlan_tag_bitmap, vlan_untag_bitmap, -, -, -, -, -, -, -, -, -, -, -).

Hpf_PortUntagVlan(if_index, vlan_untag_bitmap) :-
    Tbl_Port(if_index, -, -, -, 1, -, -, -, vlan_untag_bitmap, -, -, -, -, -, -, -, -, -, -, -).

Hpf_PortIsolate(if_index, isolate_if_index) :-
    PortIfPortIf(if_index, isolate_if_index).

Hpf_MacAgingTime(br_id, mac_age_time) :-
    BridgeMacAttr(-, br_id, mac_age_time, -, -).

Hpf_PortMacLearnLimit(if_index, learn, learn_act, limit, limit_act, limit_alm) :-
    PortMacAttr(if_index, -, learn, learn_act, limit, limit_act, limit_alm).

%precedence Hpf_Vlan, Hpf_Br
%precedence Hpf_PortPvid, Hpf_Vlan
%precedence Hpf_PortVlan, Hpf_Vlan
%precedence Hpf_PortUntagVlan, Hpf_Vlan

%precedence Hpf_PortMaster, Hpf_Br
%precedence Hpf_PortPvid, Hpf_PortMaster
%precedence Hpf_PortVlan, Hpf_PortMaster
%precedence Hpf_PortUntagVlan, Hpf_PortMaster
%precedence Hpf_PortIsolate, Hpf_PortMaster
%precedence Hpf_PortMacLearnLimit, Hpf_PortMaster

%precedence Hpf_MacAgingTime, Hpf_Br
}

// fib.d
namespace Fib {
// ------------------------------------------- INPUT TABLES -------------------------------------------
    %table ConfigIpv4Fwd(
        nsId: int4, vrfId: int4, dstIp: int4, maskLen: int4,
        nhpGroupFlag: int1, routeAttr: int2, routeFlags: int2, pathFlags: int4,
        nhpGroupId: int4, primaryLabel: int4, attributeId: int4, qosId: int2)
    {
        index(0(nsId, vrfId, dstIp, maskLen)), update
    }
    //null(0) :- ConfigIpv4Fwd(nsId, vrfId, dstIp, maskLen, nhpGroupFlag, routeAttr, routeFlags, pathFlags, nhpGroupId,
    //                         primaryLabel, attributeId, qosId).

    %table ConfigNhpGroup(
        nsId: int4, nhpGroupId: int4, nhpNum: int4, vrfId: int4)
    {
        index(0(nsId, nhpGroupId)), update
    }
    //null(0) :- ConfigNhpGroup(nsId, nhpGroupId, nhpNum, vrfId).

    %table ConfigNhpGroupNode(
        nsId: int4, nhpGroupId: int4, attributeId: int4, primaryNhpId: int4,
        primaryLabel: int4, backupNhpId: int4, backupLabel: int4, vrfId: int4)
    {
        index(0(nsId, nhpGroupId, attributeId, primaryNhpId, primaryLabel, backupNhpId, backupLabel)), update,
        index(1(nsId, nhpGroupId)) // For provisioning whole group.
    }
    //null(0) :- ConfigNhpGroupNode(nsId, nhpGroupId, attributeId, primaryNhpId, primaryLabel,
    //                              backupNhpId, backupLabel, vrfId).

    %table ConfigNhpBasic(
        nhpIndex: int4, vrfId: int4, originNhp: int4, iidFlags: int4, nsId: int4)
    {
        index(0(nhpIndex)), update
    }
    //null(0) :- ConfigNhpBasic(nhpIndex, vrfId, originNhp, iidFlags, nsId).

    %table ConfigNhpStandard(
        nhpIndex: int4, nextHop: int4, outIfIndex: int4, vrfId: int4, iidFlags: int4, nsId: int4)
    {
        index(0(nhpIndex, nextHop, outIfIndex)), update,
        index(1(nhpIndex)) // For provisioning whole group.
    }
    //null(0) :- ConfigNhpStandard(nhpIndex, nextHop, outIfIndex, vrfId, iidFlags, nsId).
// ------------------------------------------- RESOURCES/FUNCTIONS -------------------------------------------
    %function int4_to_byte4(a: int4 -> b: byte4)

    %function RouteAttr2Flags(routeAttr: int2 -> dir_route: int1, def_route: int1)

    %function DecideMulNhp(nhpNum: int1, nhpGroupHpfIndex: int4, nhpHpfIndex: int4 -> mul_nhp: int1, outNhpHpfIndex: int4)
    %function IsMulNhp(nhpNum: int1)

    %resource hpf_allocated_nhp_index(nhpId: int4, nextHop: int4 -> nhpHpfIndex: int4) {
            index(2(nhpId)),
            sequential(max_size(10000))
    }

    %resource hpf_allocated_nhp_group_index(nhpGroupId: int4 -> nhpGroupHpfIndex: int4) { sequential(max_size(10000)) }

    %table hpf_get_nhp_index(nhpId: int4, nextHop: int4, outIfIndex: int4, vrfId: int4, iidFlags: int4, nhpHpfIndex: int4)

    %aggregate NhpHpfIndexArrayAgg(nhpHpfIndex: int4 -> firstNhpHpfIndex: int4, nhpHpfIndexesLen: int1, nhpHpfIndexes: byte64)
    {
        ordered
    }

    %table NhpGroupJoinNhp(nhpGroupId: int4, nhpId: int4, nhpGroupHpfIndex: int4, nhpHpfIndex: int4)
    {
        //index(0(nhpGroupId, nhpId)), update,
        //index(1(nhpGroupHpfIndex, nhpHpfIndex))
    }

    %table NhpGroupAggregate(nhpGroupId: int4, nhpGroupHpfIndex: int4, firstNhpHpfIndex: int4, nhpHpfIndexesLen: int1, nhpHpfIndexes: byte64)
    {
        c_struct
    }

%readonly ConfigIpv4Fwd
%readonly ConfigNhpGroup
%readonly ConfigNhpGroupNode
%readonly ConfigNhpBasic
%readonly ConfigNhpStandard
%readonly hpf_get_nhp_index
%readonly NhpGroupJoinNhp
%readonly NhpGroupAggregate

%readonly int4_to_byte4
%readonly RouteAttr2Flags
%readonly DecideMulNhp
%readonly IsMulNhp
// ------------------------------------------- RULES -------------------------------------------
    hpf_allocated_nhp_group_index(nhpGroupId, -) :-
        ConfigNhpGroup(-, nhpGroupId, -, -),
        ConfigNhpGroupNode(-, nhpGroupId, -, nhpId, -, -, -, -),
        ConfigNhpBasic(nhpId, -, -, -, -),
        ConfigNhpStandard(nhpId, -, -, -, -, -).

    hpf_allocated_nhp_index(nhpId, nextHop, -) :-
        ConfigNhpBasic(nhpId, -, -, -, -),
        ConfigNhpStandard(nhpId, nextHop, -, -, -, -).

    NhpGroupJoinNhp(nhpGroupId, nhpId, nhpGroupHpfIndex, nhpHpfIndex) :-
        ConfigNhpGroup(-, nhpGroupId, -, -),
        ConfigNhpGroupNode(-, nhpGroupId, -, nhpId, -, -, -, -),
        ConfigNhpBasic(nhpId, -, -, -, -),
        ConfigNhpStandard(nhpId, nextHop, -, -, -, -),
        hpf_allocated_nhp_index(nhpId, nextHop, nhpHpfIndex),
        hpf_allocated_nhp_group_index(nhpGroupId, nhpGroupHpfIndex).

    NhpGroupAggregate(nhpGroupId, nhpGroupHpfIndex, firstNhpHpfIndex, nhpHpfIndexesLen, nhpHpfIndexes) :-
        NhpGroupJoinNhp(nhpGroupId, -, nhpGroupHpfIndex, nhpHpfIndex)
        GROUP-BY (nhpGroupId, nhpGroupHpfIndex) NhpHpfIndexArrayAgg(nhpHpfIndex, firstNhpHpfIndex, nhpHpfIndexesLen, nhpHpfIndexes).

    hpf_get_nhp_index(nhpId, nextHop, outIfIndex, vrfId, iidFlags, nhpHpfIndex) :-
        ConfigNhpBasic(nhpId, -, -, -, -),
        ConfigNhpStandard(nhpId, nextHop, outIfIndex, vrfId, iidFlags, -),
        hpf_allocated_nhp_index(nhpId, nextHop, nhpHpfIndex).
} // namespace Fib

// ------------------------------------------- FILL ABSTRACT TABLES -------------------------------------------
namespace Abstract {
    a_ip_route4(vrfId, dip, maskLen, outNhpHpfIndex, mulNhp, routeFlags, pathFlags, dir_route, def_route) :-
        Fib.ConfigIpv4Fwd(-, vrfId, dstIp, maskLen, -, routeAttr, routeFlags, pathFlags, nhpGroupId, -, -, -),
        Fib.RouteAttr2Flags(routeAttr, dir_route, def_route),
        Fib.NhpGroupAggregate(nhpGroupId, nhpGroupHpfIndex, firstNhpHpfIndex, nhpNum, -),
        Fib.DecideMulNhp(nhpNum, nhpGroupHpfIndex, firstNhpHpfIndex, mulNhp, outNhpHpfIndex),
        Fib.int4_to_byte4(dstIp, dip).

    a_nhp4(nhpIndex, nextHop, outIfIndex, vrfId, iidFlags, resIndex) :-
        Fib.hpf_get_nhp_index(nhpIndex, nextHop, outIfIndex, vrfId, iidFlags, resIndex).

    a_nhp_group(0, nhpHpfIndexesLen, nhpHpfIndexes, 0, nhpGroupHpfIndex) :-
        Fib.ConfigNhpGroup(-, nhpGroupId, -, -),
        Fib.NhpGroupAggregate(nhpGroupId, nhpGroupHpfIndex, -, nhpHpfIndexesLen, nhpHpfIndexes),
        Fib.IsMulNhp(nhpHpfIndexesLen).
} // namespace Abstract

// arp.d

namespace Arp {

// INPUT TABLES -------------------------------------------

// ConfigArp.type: 0 - static, 1 - dynamic. fakeFlag: 0
// The index number is used in ArpClear. Must with align input side.
// Original index 0 changed to 5.

%function ConvertIpAddress2Bytes(addr: int4 -> addr_bytes: byte4)
//%function AgingDetect(
//    addr: int4, ifIndex: int4, type: int1, mac: byte6,
//    fakeFlag: int1, vlanId: int2, detectCount:int4, agingTime: int8)

%table ConfigArp(
    addr: int4, ifIndex: int4, type: int1, mac: byte6,
    fakeFlag: int1, vlanId: int2, agingTime: int8)
{
    index(0(addr, ifIndex)),    // Primary key, check if type is not part of those.
    index(1(ifIndex)),          // Used in Yed_Arp_RefreshArpAgeTime
    index(2(ifIndex, type)),
    index(3(addr)),
    index(4(addr, ifIndex, type)),
    index(5(type)),

    update_partial
//    update_by_rank, // Use tuple compare function Yed_CmprTuple.
//    timeout(field(agingTime), state_function)
}

//%table tmp(a: int4)
//null(0) :- tmp(a), AgingDetect(a, 0, 0, "00", 0, 0, 0, 0).

// ConfigArp.agingTime is set as ConfigFakeTime.fakeTime + current_time
// at input function.

%table ConfigFakeTime(ifindex: int4, fakeTime: int4)
{
    index(0(ifindex)), update
}

// OUTPUT TABLES ------------------------------------------

%table Db_FakeTime(ifindex: int4, fake_time: int4)
{
    //index(0(ifindex)), update
}

%table Db_Arp(
    ip_address: byte4, if_index: int4, arp_type: int1, mac_address: byte6, fake_flag: int1, vlanid: int2, work_if_index: int4)
{
    //index(0(ip_address, if_index, arp_type)), update_partial
}

/* HPF调ORM接口写 */
%table ArpMiss_HPF(
    addr: int4, ifIndex: int4, workIfIndex: int4, agingTime: int8)
{
    transient(tuple)
}

%function ArpMiss_Pr(addr: int4, ifIndex: int4, workIfIndex: int4, agingTime: int8) {
    access_delta(ConfigArp)
}
/* 1. 写ConfigArp增量表 */

ArpMiss_To_NCTL(addr, ifIndex, workIfIndex, agingTime) :-
    ArpMiss_HPF(addr, ifIndex, workIfIndex, agingTime),
    ArpMiss_Pr(addr, ifIndex, workIfIndex, agingTime).

/* NCTL订阅 */
%table ArpMiss_To_NCTL(
    addr: int4, ifIndex: int4, workIfIndex: int4, agingTime: int8)
{
    notify
}

/* HPF订阅 */
%table ConfigArp_To_HPF(
    addr: int4, ifIndex: int4, type: int1, mac: byte6,
    fakeFlag: int1, vlanId: int2)
{
    notify
}

%readonly ConfigArp
%readonly Db_FakeTime
%readonly Db_Arp
%readonly ArpMiss_HPF
%readonly ArpMiss_To_NCTL
%readonly ConfigArp_To_HPF
// RULES --------------------------------------------------

Db_FakeTime(ifIndex, fakeTime) :-
    ConfigFakeTime(ifIndex, fakeTime).

Db_Arp(addr_bytes, ifIndex, type, mac, fakeFlag, vlanId, 0) :-
    ConfigArp(addr, ifIndex, type, mac, fakeFlag, vlanId, -),
    ConvertIpAddress2Bytes(addr, addr_bytes).

ConfigArp_To_HPF(addr, ifIndex, type, mac, fakeFlag, vlanId) :-
    ConfigArp(addr, ifIndex, type, mac, fakeFlag, vlanId, -).
}

namespace Abstract {
    a_arp(ip_addr, if_index, type, mac, fake_flag, vlan_id, 0, aging_time) :-
        Arp.ConfigArp(ip_addr, if_index, type, mac, fake_flag, vlan_id, aging_time).
}

// external.d
%table if_base(
    if_index: int4, namespace_id: int4, if_name: byte64,
    if_alias: byte64, if_type: int2, if_show_type: int2, description: byte243) {index(0(if_index)), external}
if_base(if_index, namespace_id, if_name, if_alias, if_type, if_show_type, "00") :-
Abstract.a_if_name(if_index, namespace_id, if_name, if_type, if_show_type, if_alias, -).

%table if_phy(
    if_index: int4, if_bw: int8, phy_state: int1, admin_state: int1, onboard_state: int1,
    tb: int4, tp: int4, phy_mac: byte6, eth_autoneg: int1, eth_speed: int8,
    eth_duplex: int1, eth_combo: int1, eth_loopback: int1, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
    unit_id: int4, port_id: int4, chip_unit: int1, oda_phy_port: int1, service_type: int1, autoneg_cap: int1, 
    autoneg_adv_cap: int2, medium: int1, mru: int2, eth_class: int1, eth_type: int1, 
    nat_enable: int1, nat64_enable: int1) {index(0(if_index)), external }
if_phy(
    if_index, if_bw, phy_state, admin_state, onboard_state,
    tb, tp, phy_mac, eth_autoneg, eth_speed,
    eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
    unit_id, port_id, chip_unit, oda_phy_port, service_type, autoneg_cap, autoneg_adv_cap, medium,
    mru, eth_class, 0, 0, 0) :-
Abstract.a_if_phy(
    if_index, if_bw, phy_state, admin_state, onboard_state,
    tb, tp, chip_unit, oda_phy_port, phy_mac, eth_autoneg, eth_speed,
    eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
    unit_id, port_id, service_type, autoneg_cap, autoneg_adv_cap, medium,
    mru, -, eth_class, -) .

%table if_link(
    if_index: int4, link_type: int4, link_state: int1, oper_state: int1
) {index(0(if_index)),  external}
if_link(if_index, link_type, link_state, oper_state) :-
Abstract.a_if_link(if_index, link_type, link_state, oper_state).

%table if_route(
    if_index: int4, vrf_id: int4, ipv4_state: int1, mtu_v4: int4, df_flag: int4, ipv6_state: int1, mtu_v6: int4
) {index(0(if_index)), external}
if_route(if_index, vrf_id, ipv4_state, mtu_v4, df_flag, ipv6_state, mtu_v6) :-
Abstract.a_if_net(if_index, vrf_id, df_flag, mtu_v4, ipv4_state),
Abstract.a_if_net6(if_index, -, mtu_v6, ipv6_state).

// abstract.d
namespace Abstract {
    %table a_if_name(
        if_index: int4, namespace_id: int4, if_name: byte64,
        if_type: int2, if_show_type: int2, if_alias: byte64, description: byte64)
    %table a_if_phy(
        if_index: int4, if_bw: int8, phy_state: int1, admin_state: int1, onboard_state: int1,
        tb: int4, tp: int4, chip_unit: int1, oda_phy_port: int1, phy_mac: byte6, eth_autoneg: int1, eth_speed: int8,
        eth_duplex: int1, eth_combo: int1, eth_loopback: int1, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
        unit_id: int4, port_id: int4, service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1,
        mru: int2, link_type: int1, eth_class: int1, cpu_type: int1)
    %table a_if_link(
        if_index: int4, link_type: int4, link_state: int1, oper_state: int1)
    %table a_if_net(if_index: int4, vrf_id: int4, df_flag: int4, mtu_v4: int4, ipv4_state: int1)
    %table a_if_net6(if_index: int4, vrf_id: int4, mtu_v6: int4, ipv6_state: int1)
    %table a_ip_route4(
               vrfId: int4, dstIp: byte4, maskLen: int4, nhpIndex: int4, mulNhp: int1,
               routeFlags: int2, pathFlags: int4, dir_route: int1,
               def_route: int1)
    %table a_nhp4(nhp_index: int4, next_hop: int4, out_if_index: int4, vrf_id: int4, iid_flags: int4, res_index: int4)
    %table a_nhp_group(frr: int1, nhp_num: int1, nhp_indexes: byte64, hash_index: int1, nhp_group_index: int4)
    %table a_arp(ip_addr: int4, if_index: int4, type: int1, mac: byte6, fake_flag: int1, vlan_id: int2, detect_count:int4, aging_time: int8)
    %table a_config_if_ipv4_addr(if_index: int4, vrf_index: int4, address: int4, mask_len: int2, type: int2)

%readonly a_if_name
%readonly a_if_phy
%readonly a_if_link
%readonly a_if_net
%readonly a_if_net6
%readonly a_ip_route4
%readonly a_nhp4
%readonly a_nhp_group
%readonly a_arp
%readonly a_config_if_ipv4_addr
}

// fwdif.d
/* hpr表暂不调试，需要tbm支持
namespace Hpf {
    %table CAP_TABLE_FWDIF(fwdIfIdx: int4, ctrlIfIdx: int4, flowIfIdx: int4, ifName: byte64, ifType: int4, tp: int4, cpuType: int1, mtu: int4, mac: byte6, flag: int4) {
        hpr_table(linear),
        index(0(fwdIfIdx)),
        max_size(1000)
    }
    %table hpf_if_index(fwdIfIdx: int4, ctrlIfIdx: int4, flowIfIdx: int4) {
        hpr_table(linear),
        index(0(fwdIfIdx)),
        max_size(1000)
    }
    %table hpf_if_name(fwdIfIdx: int4, namespace_id: int4, if_name: byte64,
        hpf_if_type: int4, if_show_type: int2, if_alias: byte64, description: byte64) {
        hpr_table(linear),
        index(0(fwdIfIdx)),
        max_size(1000)
    }
    %table hpf_if_phy(fwdIfIdx: int4, if_bw: int8, phy_state: int1, admin_state: int1, onboard_state: int1,
        tb: int4, tp: int4, chip_unit: int1, oda_phy_port: int1, phy_mac: byte6, eth_autoneg: int1, eth_speed: int8,
        eth_duplex: int1, eth_combo: int1, eth_loopback: int1, dev_id: int4, chassis_id: int4, slot_id: int4, card_id: int4,
        unit_id: int4, port_id: int4, service_type: int1, autoneg_cap: int1, autoneg_adv_cap: int2, medium: int1,
        mru: int2, link_type: int1, eth_class: int1, cpu_type: int1) {
        hpr_table(linear),
        index(0(fwdIfIdx)),
        max_size(1000)
    }
    %table hpf_if_link(fwdIfIdx: int4, link_type: int4, link_state: int1, oper_state: int1) {
        hpr_table(linear),
        index(0(fwdIfIdx)),
        max_size(1000)
    }
    %table hpf_if_net(fwdIfIdx: int4, vrf_id: int4, df_flag: int4, mtu_v4: int4, ipv4_state: int1) {
        hpr_table(linear),
        index(0(fwdIfIdx)),
        max_size(1000)
    }
    %table hpf_if_net6(fwdIfIdx: int4, vrf_id: int4, mtu_v6: int4, ipv6_state: int1) {
        hpr_table(linear),
        index(0(fwdIfIdx)),
        max_size(1000)
    }
    
    %table CAP_TABLE_CTRLIF_MAP_FWDIF(ctrlIfIdx: int4, fwdIfIdx: int4) {
        hpr_table(hash),
        index(0(ctrlIfIdx)),
        max_size(1000)
    }
    %table CAP_TABLE_FLOWIF_MAP_FWDIF(flowIfIdx: int4, fwdIfIdx: int4) {
        hpr_table(hash),
        index(0(flowIfIdx)),
        max_size(1000)
    }

    hpf_if_index(fwdIfIdx, ctrlIfIdx, flowIfIdx) :-
        Abstract.a_if_phy(ctrlIfIdx, -, -, -, -,
            tb, tp, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -,
            -, -, -, -, -, -, -),
        GetFlowIfIndex(tb, tp, 0, flowIfIdx),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    hpf_if_name(fwdIfIdx, namespace_id, if_name, hpf_if_type, if_show_type, if_alias, description) :-
        Abstract.a_if_name(if_index, namespace_id, if_name,
            if_type, if_show_type, if_alias, description),
        GetHpfIfType(if_type, hpf_if_type),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_phy(fwdIfIdx, if_bw, phy_state, admin_state, onboard_state,
            tb, tp, chip_unit, oda_phy_port, phy_mac, eth_autoneg, eth_speed,
            eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
            unit_id, port_id, service_type, autoneg_cap, autoneg_adv_cap, medium,
            mru, link_type, eth_class, cpu_type) :-
        Abstract.a_if_phy(if_index, if_bw, phy_state, admin_state, onboard_state,
            tb, tp, chip_unit, oda_phy_port, phy_mac, eth_autoneg, eth_speed,
            eth_duplex, eth_combo, eth_loopback, dev_id, chassis_id, slot_id, card_id,
            unit_id, port_id, service_type, autoneg_cap, autoneg_adv_cap, medium,
            mru, link_type, eth_class, cpu_type),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_link(fwdIfIdx, link_type, link_state, oper_state) :-
        Abstract.a_if_link(if_index, link_type, link_state, oper_state),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_net(fwdIfIdx, vrf_id, df_flag, mtu_v4, ipv4_state) :-
        Abstract.a_if_net(if_index, vrf_id, df_flag, mtu_v4, ipv4_state),
        ctrlif_map_fwdif(if_index, fwdIfIdx).
    hpf_if_net6(fwdIfIdx, vrf_id, mtu_v6, ipv6_state) :-
        Abstract.a_if_net6(if_index, vrf_id, mtu_v6, ipv6_state),
        ctrlif_map_fwdif(if_index, fwdIfIdx).

    CAP_TABLE_CTRLIF_MAP_FWDIF(ctrlIfIdx, fwdIfIdx) :-
        Abstract.a_if_name(ctrlIfIdx, -, -, -, -, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_FLOWIF_MAP_FWDIF(flowIfIdx, fwdIfIdx) :-
        Abstract.a_if_phy(ctrlIfIdx, -, -, -, -,
            tb, tp, -, -, -, -, -, -, -, -, -, -, -, -, -, -, -,
            -, -, -, -, -, -, -),
        GetFlowIfIndex(tb, tp, 0, flowIfIdx),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).

    %table init(i: int4)
    init(0):- Abstract.a_if_link(-, -, -, -).
    ctrlif_map_fwdif(-134217728, -):- init(-).

    CAP_TABLE_FWDIF(fwdIfIdx, -134217728, -134217728, "abcdef0123456789", 0, 0, 0, 1500, "0x000000000000", 397313) :-
        ctrlif_map_fwdif(-134217728, fwdIfIdx), init(-).
    CAP_TABLE_CTRLIF_MAP_FWDIF(-134217728, fwdIfIdx) :-
        ctrlif_map_fwdif(-134217728, fwdIfIdx), init(-).
    CAP_TABLE_FLOWIF_MAP_FWDIF(-134217728, fwdIfIdx) :-
        ctrlif_map_fwdif(-134217728, fwdIfIdx), init(-).
    //fwdif(-134217728, -134217728, fwdIfIdx, "abcdef0123456789", 0, 0, 0, 1500, "0x000000000000", 397313) :- ctrlif_map_fwdif(-134217728, fwdIfIdx), init(-).
    // flowIfIdx为FWDIF_TYPE_SELF的接口，即0xF8000000，初始化时默认添加
}

// l3.d
namespace Hpf {
    %table CAP_TABLE_IPV4_FIB(prefix: int4, vpn: int4, dip: byte4, opcode: int2, mulNhp: int1, dirRoute: int1, defRoute: int1, nhpIdx: int4) {
        hpr_table(lpmv4),
        index(0(prefix, vpn, dip)),
        max_size(1000)
    }
    %table CAP_TABLE_IPV4_NHP(nhpIdx: int4, ctrlIfIdx: int4, fwdIfIdx: int4, nextHop: int4) {
        hpr_table(linear),
        index(0(nhpIdx)),
        max_size(100)
    }
    %table CAP_TABLE_IPV4_NHP_GROUP(nhpGrpIdx: int4, nhpNum: int1, nhpIndexes: byte64) {
        hpr_table(linear),
        index(0(nhpGrpIdx)),
        max_size(100)
    }
    %table CAP_TABLE_IPV4_ARP(rsv: int2, vpn: int2, nextHop: int4, fake: int1, ctrlIfIdx: int4, l3CtrlIfIdx: int4, fwdIfIdx: int4, dmac: byte6, vlanId: int2) {
        hpr_table(hash),
        index(0(rsv, vpn, nextHop)),
        max_size(100)
    }
    
    %table CAP_TABLE_IPV4_PORTIP(ctrlIfIdx: int4, fwdIfIdx: int4, index: int4) {
        hpr_table(hash),
        index(0(ctrlIfIdx)),
        max_size(100)
    }
    %table CAP_TABLE_IPV4_PORTIPLIST(index: int4, vrf_index: int4, address: int4, mask_len: int2, type: int2) {
        hpr_table(linear),
        index(0(index)),
        max_size(100)
    }

    %resource portip_index_pool(ctrlIfIdx: int4 -> index: int4) { sequential(max_size(100)) }
    portip_index_pool(ctrlIfIdx, -) :- Abstract.a_config_if_ipv4_addr(ctrlIfIdx, -, -, -, -).

    %function GetFlowIfIndex(tb: int4, tp: int4, vlan: int4 -> flowIfIdx: int4)
    %function GetHpfIfType(ifType: int2 -> hpfIfType: int4)

    %resource ctrlif_map_fwdif(ctrlIfIdx: int4 -> fwdIfIdx: int4) { sequential(max_size(100)) }
    ctrlif_map_fwdif(ctrlIfIdx, -) :- Abstract.a_if_name(ctrlIfIdx, -, -, -, -, -, -).

    CAP_TABLE_IPV4_FIB(prefix, vpn, dip, opcode, mulNhp, dir_route, def_route, nhpIndex) :-
        Abstract.a_ip_route4(vpn, dip, prefix, nhpIndex, mulNhp, opcode, -, dir_route, def_route).
    CAP_TABLE_IPV4_NHP(nhpHpfIndex, ctrlIfIdx, fwdIfIdx, nextHop) :-
        Abstract.a_nhp4(-, nextHop, ctrlIfIdx, -, -, nhpHpfIndex),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_IPV4_NHP_GROUP(nhpGrpIdx, nhpNum, nhpIndexes) :-
        Abstract.a_nhp_group(-, nhpNum, nhpIndexes, -, nhpGrpIdx).
        
    CAP_TABLE_IPV4_ARP(0, 0, nextHop, fake, ctrlIfIdx, ctrlIfIdx, fwdIfIdx, mac, vlanId) :-
        Abstract.a_arp(nextHop, ctrlIfIdx, -, mac, fake, vlanId, -, -),
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).

    CAP_TABLE_IPV4_PORTIP(ctrlIfIdx, fwdIfIdx, index) :-
        Abstract.a_config_if_ipv4_addr(ctrlIfIdx, -, -, -, -),
        portip_index_pool(ctrlIfIdx, index), 
        ctrlif_map_fwdif(ctrlIfIdx, fwdIfIdx).
    CAP_TABLE_IPV4_PORTIPLIST(index, vrf_index, address, mask_len, type) :-
        Abstract.a_config_if_ipv4_addr(ctrlIfIdx, vrf_index, address, mask_len, type),
        portip_index_pool(ctrlIfIdx, index).
}
*/
