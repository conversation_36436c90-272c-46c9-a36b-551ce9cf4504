/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: SupUpgVerFun.cpp
 * Description: datalog表创建支持upgrade_version声明, 功能测试
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2024-12-21
 */
#include "SupUpgVer.h"
#include "t_datacom_lite.h"

using namespace std;

class SupUpgVerFun : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SupUpgVerFun ::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());

    int ret;
    g_conn = NULL;
    g_stmt = NULL;
    ret = testGmcConnect(&g_conn, &g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void SupUpgVerFun ::TearDown()
{
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
    AW_CHECK_LOG_END();
}

// 016.%block0，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，热补丁升级，
// 新表写入数据，第三次将新增表与普通表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_016)
{
    char soName[] = "SupUpg_016";
    char patchSoName1[] = "datalogFile/SupUpg_016_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_016_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_016_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_016.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 2;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 017.%block0，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，热补丁升级，
// 新表写入数据，第三次将新增表与部分可更新表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_017)
{
    char soName[] = "SupUpg_017";
    char patchSoName1[] = "datalogFile/SupUpg_017_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_017_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_017_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_017.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    tryCount = 0;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 2;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 018.%block0，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，热补丁升级，
// 新表写入数据，第三次将新增表与可更新表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_018)
{
    char soName[] = "SupUpg_018";
    char patchSoName1[] = "datalogFile/SupUpg_018_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_018_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_018_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_018.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    tryCount = 0;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 2;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 019.%block0，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，热补丁升级，
// 新表写入数据，第三次将新增表与tuple表进行join，热补丁升级，输出表有数据
// 备注：tuple表清空了数据，老数据upgradeVersion无变化
TEST_F(SupUpgVerFun, DataLog_097_019)
{
    char soName[] = "SupUpg_019";
    char patchSoName1[] = "datalogFile/SupUpg_019_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_019_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_019_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_019.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    tryCount = 0;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        upgradeVersionIndex = 1;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 2;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 020.%block0，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，热补丁升级，
// 新表写入数据，第三次将新增表与finish表进行join，热补丁升级，输出表有数据
// 备注：finish表清空了数据，老数据upgradeVersion无变化
TEST_F(SupUpgVerFun, DataLog_097_020)
{
    char soName[] = "SupUpg_020";
    char patchSoName1[] = "datalogFile/SupUpg_020_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_020_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_020_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_020.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    tryCount = 0;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        upgradeVersionIndex = 1;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 2;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 021.%block0，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，热补丁升级，
// 新表写入数据，第三次将新增表与普通中间表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_021)
{
    char soName[] = "SupUpg_021";
    char patchSoName1[] = "datalogFile/SupUpg_021_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_021_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_021_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_021.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 2;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 022.%block0，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，热补丁升级，
// 新表写入数据，第三次将新增表与tuple中间表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_022)
{
    char soName[] = "SupUpg_022";
    char patchSoName1[] = "datalogFile/SupUpg_022_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_022_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_022_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_022.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 2;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 023.%block0，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，热补丁升级，
// 新表写入数据，第三次将新增表与finish中间表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_023)
{
    char soName[] = "SupUpg_023";
    char patchSoName1[] = "datalogFile/SupUpg_023_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_023_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_023_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_023.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 2;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 024.%block0，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，热补丁升级，
// 新表写入数据，第三次将新增表与field中间表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_024)
{
    char soName[] = "SupUpg_024";
    char patchSoName1[] = "datalogFile/SupUpg_024_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_024_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_024_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_024.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    int32_t dtlReservedCount = 1;
    ret = batchSingleWriteFieldTable(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        int32_t expectCValue = 0;
        ret = primaryScanFieldTable(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWriteFieldTable(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        int32_t expectCValue = 0;
        ret = primaryScanFieldTable(
            g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWriteFieldTable(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWriteFieldTable(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 2;
        int32_t expectValue = i;
        int32_t expectCValue = 0;
        ret = primaryScanFieldTable(
            g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 025.%block0，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，热补丁升级，
// 新表写入数据，第三次将新增表与固定型资源表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_025)
{
    char soName[] = "SupUpg_025";
    char patchSoName1[] = "datalogFile/SupUpg_025_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_025_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_025_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_025.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableRsc[] = "rsc";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableRsc, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, false,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    int32_t dtlReservedCount = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, dtlReservedCount, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableRsc, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, false,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, dtlReservedCount, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 2;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 026.%block0，升级三次，第一次升级将原表upgradeVersion+1，第二次声明%redo REDO_OFF修改tbmUDF实现，
// 同时新增表（upgradeVersion=1）与表join投影到tbm表，热补丁升级，新表写入数据，输出表有数据，
// 第三次声明%redo REDO_OFF新增表（upgradeVersion=1）新增规则投影到tbm表，热补丁升级，新表写入数据，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_026)
{
    char soName[] = "SupUpg_026";
    char patchSoName1[] = "datalogFile/SupUpg_026_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_026_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_026_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_026.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableInput3[] = "inp3";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    system("rm -rf /root/SupUpg.txt");
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    system("rm -rf /root/SupUpg.txt");
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 2;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, false,
            expectDtlReservedCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    system("rm -rf /root/SupUpg.txt");
    ret = batchSingleWrite(g_stmt, g_conn, tableInput3, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput3, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }
    ret = system("cat /root/SupUpg.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 027.%block0，.d文件中包含ns1，第一次将新增表（upgradeVersion=1）在ns2（%readwrite）
// 与现有的表进行join，热补丁升级，新表写入数据，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_027)
{
    char soName[] = "SupUpg_027";
    char patchSoName1[] = "datalogFile/SupUpg_027_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_027.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "ns1.inp1";
    char tableInput2[] = "ns2.inp2";
    char tableMid1[] = "ns1.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableOutput1[] = "ns1.out1";
    char tableOutput2[] = "ns2.out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 028.%block0，升级两次，第一次将新增表（upgradeVersion=5）投影到空表，热补丁升级，新表写入数据，
// 第二次将新增表进行join热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_028)
{
    char soName[] = "SupUpg_028";
    char patchSoName1[] = "datalogFile/SupUpg_028_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_028_patchV3.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_028.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 5;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        upgradeVersionIndex = 0;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 029.%block0，新增表（upgradeVersion=1）与原表进行join，热补丁升级，新表写入数据，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_029)
{
    char soName[] = "SupUpg_029";
    char patchSoName1[] = "datalogFile/SupUpg_029_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_029.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 030.%block 0，原始表中定义输出表定义upgradeVersion(2)，function中access（current）进行读表数据
TEST_F(SupUpgVerFun, DataLog_097_030)
{
    char soName[] = "SupUpg_030";
    char patchSoName1[] = "datalogFile/SupUpg_030_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_030.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 1;
    system("rm -rf /root/097_Func.txt");
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = system("cat /root/097_Func.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 1; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 2;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    system("rm -rf /root/097_Func.txt");
    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 11;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = system("cat /root/097_Func.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 031.%block1，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，
// 热补丁升级，新表写入数据，第三次将新增表与普通表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_031)
{
    char soName[] = "SupUpg_031";
    char patchSoName1[] = "datalogFile/SupUpg_031_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_031_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_031_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_031.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 032.%block1，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，
// 热补丁升级，新表写入数据，第三次将新增表与部分可更新表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_032)
{
    char soName[] = "SupUpg_032";
    char patchSoName1[] = "datalogFile/SupUpg_032_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_032_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_032_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_032.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    tryCount = 0;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 033.%block1，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，
// 热补丁升级，新表写入数据，第三次将新增表与可更新表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_033)
{
    char soName[] = "SupUpg_033";
    char patchSoName1[] = "datalogFile/SupUpg_033_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_033_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_033_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_033.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    tryCount = 0;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 034.%block1，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，
// 热补丁升级，新表写入数据，第三次将新增表与tuple表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_034)
{
    char soName[] = "SupUpg_034";
    char patchSoName1[] = "datalogFile/SupUpg_034_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_034_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_034_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_034.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    tryCount = 0;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        upgradeVersionIndex = 1;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 035.%block1，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，
// 热补丁升级，新表写入数据，第三次将新增表与finish表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_035)
{
    char soName[] = "SupUpg_035";
    char patchSoName1[] = "datalogFile/SupUpg_035_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_035_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_035_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_035.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    tryCount = 0;
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        upgradeVersionIndex = 1;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 036.%block1，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，
// 热补丁升级，新表写入数据，第三次将新增表与普通中间表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_036)
{
    char soName[] = "SupUpg_036";
    char patchSoName1[] = "datalogFile/SupUpg_036_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_036_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_036_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_036.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 037.%block1，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，
// 热补丁升级，新表写入数据，第三次将新增表与tuple中间表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_037)
{
    char soName[] = "SupUpg_037";
    char patchSoName1[] = "datalogFile/SupUpg_037_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_037_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_037_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_037.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 038.%block1，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，
// 热补丁升级，新表写入数据，第三次将新增表与finish中间表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_038)
{
    char soName[] = "SupUpg_038";
    char patchSoName1[] = "datalogFile/SupUpg_038_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_038_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_038_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_038.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 039.%block1，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，
// 热补丁升级，新表写入数据，第三次将新增表与field中间表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_039)
{
    char soName[] = "SupUpg_039";
    char patchSoName1[] = "datalogFile/SupUpg_039_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_039_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_039_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_039.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    int32_t dtlReservedCount = 1;
    ret = batchSingleWriteFieldTable(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        int32_t expectCValue = 0;
        ret = primaryScanFieldTable(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWriteFieldTable(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        int32_t expectCValue = 0;
        ret = primaryScanFieldTable(
            g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWriteFieldTable(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWriteFieldTable(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        int32_t expectCValue = 0;
        ret = primaryScanFieldTable(
            g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScanFieldTable(
            g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, expectCValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 040.%block1，升级三次，第一次升级将原表upgradeVersion+1，第二次将新增表（upgradeVersion=1）投影到空表，
// 热补丁升级，新表写入数据，第三次将新增表与固定型资源表进行join，热补丁升级，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_040)
{
    char soName[] = "SupUpg_040";
    char patchSoName1[] = "datalogFile/SupUpg_040_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_040_patchV3.so";
    char patchSoName3[] = "datalogFile/SupUpg_040_patchV4.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_040.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableRsc[] = "rsc";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableRsc, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, false,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    int32_t dtlReservedCount = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, dtlReservedCount, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 1;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableRsc, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, false,
            expectDtlReservedCount, true);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第三次, 写数据, 读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName3));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum, dtlReservedCount, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 041.%block1，升级两次，第一次升级将原表upgradeVersion+1，
// 第二次声明%redo REDO_OFF修改tbmUDF实现同时新增表（upgradeVersion=1）与表join投影到tbm表，
// 热补丁升级，新表写入数据，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_041)
{
    char soName[] = "SupUpg_041";
    char patchSoName1[] = "datalogFile/SupUpg_041_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_041_patchV3.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_041.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    system("rm -rf /root/SupUpg.txt");
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    system("rm -rf /root/SupUpg.txt");
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        int32_t expectDtlReservedCount = 2;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, false,
            expectDtlReservedCount);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = system("cat /root/SupUpg.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 042.%block1，升级两次，第一次升级将原表upgradeVersion+1，
// 第二次声明%redo REDO_OFF新增表（upgradeVersion=1）新增规则投影到tbm表，热补丁升级，新表写入数据，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_042)
{
    char soName[] = "SupUpg_042";
    char patchSoName1[] = "datalogFile/SupUpg_042_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_042_patchV3.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_042.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    system("rm -rf /root/SupUpg.txt");
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    system("rm -rf /root/SupUpg.txt");
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = system("cat /root/SupUpg.txt");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    TestUninstallDatalog(soName);
}

// 043.%block1，.d文件中包含ns1，第一次将新增表（upgradeVersion=1）在ns2（%readwrite）
// 与现有的表进行join，热补丁升级，新表写入数据，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_043)
{
    char soName[] = "SupUpg_043";
    char patchSoName1[] = "datalogFile/SupUpg_043_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_043.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "ns1.inp1";
    char tableInput2[] = "ns2.inp2";
    char tableMid1[] = "ns1.mid1";
    char tableMid2[] = "ns2.mid2";
    char tableOutput1[] = "ns1.out1";
    char tableOutput2[] = "ns2.out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 044.%block1，升级两次，第一次将新增表（upgradeVersion=5）投影到空表上热补丁升级，新表写入数据，
// 第二次将新增表与输入表进行join热补丁升级，输出表无数据
TEST_F(SupUpgVerFun, DataLog_097_044)
{
    char soName[] = "SupUpg_044";
    char patchSoName1[] = "datalogFile/SupUpg_044_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_044_patchV3.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_044.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 5;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        upgradeVersionIndex = 0;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        upgradeVersionIndex = 5;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    count = readCount(g_stmt, tableMid1);
    AW_MACRO_EXPECT_EQ_INT(0, count);
    count = readCount(g_stmt, tableOutput1);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    TestUninstallDatalog(soName);
}

//  045.%block1，升级两次，第一次将新增表（upgradeVersion=5）投影到空表上热补丁升级，新表写入数据，
// 第二次将新增表与中间表进行join热补丁升级，输出表无数据
TEST_F(SupUpgVerFun, DataLog_097_045)
{
    char soName[] = "SupUpg_045";
    char patchSoName1[] = "datalogFile/SupUpg_045_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_045_patchV3.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_045.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 1;
    endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 5;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        upgradeVersionIndex = 0;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t count = 0;
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        upgradeVersionIndex = 5;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    count = readCount(g_stmt, tableOutput1);
    AW_MACRO_EXPECT_EQ_INT(0, count);

    TestUninstallDatalog(soName);
}

// 046.%block1，升级两次，第一次将upgradeVersion=+1，第二次将新增表（upgradeVersion=1）与原有输入表进行join，
// 新表写数据，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_046)
{
    char soName[] = "SupUpg_046";
    char patchSoName1[] = "datalogFile/SupUpg_046_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_046_patchV3.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_046.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次, 写数据读数据");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 047.%block1，升级一次，新增表（upgradeVersion=5）与原表进行join，新表写入数据，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_047)
{
    char soName[] = "SupUpg_047";
    char patchSoName1[] = "datalogFile/SupUpg_047_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_047.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 048.%block1，第一个补丁新增一个中间表（updteversion=1），原始.d的表投影到这个中间表，中间表再投影到已有的输出表
TEST_F(SupUpgVerFun, DataLog_097_048)
{
    char soName[] = "SupUpg_048";
    char patchSoName1[] = "datalogFile/SupUpg_048_patchV2.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_048.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableMid3[] = "mid3";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (i >= 11 && i <= 20) {
            ret = primaryScan(g_stmt, tableMid3, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            int32_t expectDtlReservedCount = 2;
            ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex,
                false, expectDtlReservedCount);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret =
                primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 049.原始.d定义inp1(updteversion=1)，inp2(updteversion=2)，输入表写入数据join不出数据
TEST_F(SupUpgVerFun, DataLog_097_049)
{
    char soName[] = "SupUpg_049";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_049.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    int32_t count = 0;
    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        upgradeVersionIndex = 2;
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        count = readCount(g_stmt, tableMid1);
        AW_MACRO_EXPECT_EQ_INT(0, count);
        count = readCount(g_stmt, tableOutput1);
        AW_MACRO_EXPECT_EQ_INT(0, count);
    }

    TestUninstallDatalog(soName);
}

// 050.原始.d定义inp1(updteversion=1)，inp2(updteversion=1)，输入表写入数据join出数据
TEST_F(SupUpgVerFun, DataLog_097_050)
{
    char soName[] = "SupUpg_050";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_050.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}

// 051.%block1，升级两次，第一次升级将原表upgradeVersion+1，
// 第二次新增表（upgradeVersion不设置）与原表进行join，新表写入数据，输出表有数据
TEST_F(SupUpgVerFun, DataLog_097_051)
{
    char soName[] = "SupUpg_051";
    char patchSoName1[] = "datalogFile/SupUpg_051_patchV2.so";
    char patchSoName2[] = "datalogFile/SupUpg_051_patchV3.so";
    int ret = 0;
    TestLoadDatalog("./datalogFile/SupUpg_051.so");

    AW_FUN_Log(LOG_STEP, "写数据, 读数据");
    char tableInput1[] = "inp1";
    char tableInput2[] = "inp2";
    char tableMid1[] = "mid1";
    char tableMid2[] = "mid2";
    char tableOutput1[] = "out1";
    char tableOutput2[] = "out2";

    int32_t startNum = 1;
    int32_t endNum = 10;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 10; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 0;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    AW_FUN_Log(LOG_STEP, "热补丁升级第一次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    int tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "热补丁升级第二次");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    tryCount = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    while (ret != GMERR_OK && tryCount < MAX_TRY_COUNT) {
        usleep(1000);
        ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
        tryCount++;
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    startNum = 11;
    endNum = 20;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput1, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    startNum = 1;
    ret = batchSingleWrite(g_stmt, g_conn, tableInput2, startNum, endNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    for (int32_t i = 1; i <= 20; i++) {
        int32_t primaryIndex = i;
        int32_t upgradeVersionIndex = 1;
        int32_t expectValue = i;
        ret = primaryScan(g_stmt, tableInput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableInput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableMid2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = primaryScan(g_stmt, tableOutput1, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        bool isCValueAlter = true;
        ret = primaryScan(
            g_stmt, tableOutput2, primaryIndex, upgradeVersionIndex, expectValue, upgradeVersionIndex, isCValueAlter);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    TestUninstallDatalog(soName);
}
