/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: SupUpgVerCom.cpp
 * Description: datalog表创建支持upgrade_version声明, 编译测试
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2024-12-23
 */
#include "SupUpgVer.h"
#include "t_datacom_lite.h"

using namespace std;

class SupUpgVerCom : public testing::Test {
protected:
    static void SetUpTestCase()
    {}
    static void TearDownTestCase()
    {}

public:
    virtual void SetUp();
    virtual void TearDown();
};

void SupUpgVerCom ::SetUp()
{
    AW_CHECK_LOG_BEGIN();
}

void SupUpgVerCom ::TearDown()
{
    AW_CHECK_LOG_END();
}

// 001.patch.d中的一张表，重复出现upgrade_version关键字，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_001.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_001.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_001_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_001_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "duplicate option in table \"inp2\" near line 7");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 002.patch.d中的一张表，upgrade_version(1,1)关键字多个入参，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_002.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_002.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_002_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_002_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "there should be only 1 parameter for keyword in table \"inp2\" near line 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 003.patch.d中的一张表，upgrade_version()关键字0个入参，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_003.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_003.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_003_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_003_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: ')' near line 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 004.patch.d中的一张表，upgrade_version(-1)入参为负数，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_004.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_004.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_004_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_004_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "number of option in table \"inp2\" should be positive near line 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 005.patch.d中的一张表，upgrade_version(1.5)入参为小数，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_005.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_005.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_005_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_005_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: '.' near line 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 006.patch.d中的一张表，upgrade_version(ONE)入参为大写字母，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_006.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_006.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_006_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_006_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "invalid number in table \"inp2\" near line 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 007.patch.d中的一张表，upgrade_version(one)入参为小写字母，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_007.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_007.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_007_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_007_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "invalid number in table \"inp2\" near line 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 008.patch.d中的一张表，upgrade_version(0xff)入参为16进制数字，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_008.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_008.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_008_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_008_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Failed to parse datalog program, syntax error: 'xff' near line 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 009.patch.d中的一张表，upgrade_version(0)入参为0，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_009.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_009.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_009_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_009_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "number of option in table \"inp2\" should be positive near line 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 010.patch.d中的一张表，upgrade_version(2147483648)入参为int32_max+1，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_010.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_010.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_010_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_010_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "number of option in table \"inp2\" should le int32 max near line 6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 011.patch.d中的一张表，大写的UPGRADE_VERSION关键字，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_011.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_011.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_011_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_011_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "invalid option in table \"inp2\": UPGRADE_VERSION near line 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 012.patch.d中的一张表，upgrade_versio关键字不对，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_012.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_012.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_012_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_012_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "invalid option in table \"inp2\": upgrade_versio near line 3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 013.patch.d中的一张表，upgrade_version(2147483647)入参为int32_max，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_013.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_013.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_013_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_013_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 014.block 1, patch.d中的一张表，upgrade_version(2147483647)入参为int32_max，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_014.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_014.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_014_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_014_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s -supUpgErr", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 015.%redooff, patch.d中的一张表，upgrade_version(2147483647)入参为int32_max，编译.d文件
TEST_F(SupUpgVerCom, DataLog_097_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inputFile1[1024] = "./datalogFileCom/SupUpg_015.d";
    char outputFile1[1024] = "./datalogFileCom/SupUpg_015.c";
    char oldFile[1024] = "./datalogFileCom/SupUpg_015_rule.d";
    char patchFile[1024] = "./datalogFileCom/SupUpg_015_patch.d";

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile1, outputFile1);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -U %s %s -supUpgErr", g_toolPath, oldFile, patchFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 052.gmconvert转换带upgradeVersion定义的.d文件
TEST_F(SupUpgVerCom, DataLog_097_052)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char inPath[256] = "datalogFileCom/SupUpg_052.d";
    char outPath[256] = "datalogFileCom";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmconvert -i %s -o %s", g_toolPath, inPath, outPath);
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = executeCommand(g_command, "successfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验gmjson
    char *result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat datalogFileCom/inp1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectInp1, result));
    free(result);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat datalogFileCom/mid1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectMid1, result));
    free(result);

    result = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat datalogFileCom/out1.gmjson");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &result);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    EXPECT_STRNE(NULL, strstr(g_expectOut1, result));
    free(result);
}
