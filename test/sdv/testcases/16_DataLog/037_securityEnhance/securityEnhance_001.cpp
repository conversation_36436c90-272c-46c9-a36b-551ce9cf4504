/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
 Description  : GMDBV503 迭代三 DML&流控安全能力增强
 Notes        : 
 History      :
 Author       : luyang/l00618033
 Create       : [2023.04.14]
*****************************************************************************/

#include "abnormal.h"
#include "t_datacom_lite.h"

using namespace std;

class securityEnhance_001 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void securityEnhance_001::SetUp()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_INVALID_NAME);
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
}
void securityEnhance_001::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}

/* ****************************************************************************
 Description  : 001.使用GmcDropVertexLabel接口同步删除datalog表
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test01";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    AsyncUserDataT data = {0};
    int ret = 0;

    /*--------------------------------------------------写输入表-----------------------------------------*/
    char tableinp1[LABEL_NAME] = "inp1";
    int recordNum = 2;
    int32_t data1[recordNum][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}};
    TEST_INPUT_01 inp1[recordNum] = {{1, 0, 1, 1, 1}, {2, 0, 2, 2, 2}};
    ret = structBatchWriteTable(g_conn, g_stmt, tableinp1, inp1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 输入表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /*--------------------------------------------------读输出表-----------------------------------------*/
    char tableout1[LABEL_NAME] = "out1";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableout1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "out1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 调用GmcDropVertexLabel接口,删除失败
    ret = GmcDropVertexLabel(g_stmt, tableinp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    /*--------------------------------------------------读输入表-----------------------------------------*/
    // 校验输入表和输出表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = readTable(g_conn, g_stmt, tableinp1, data1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*--------------------------------------------------读输出表-----------------------------------------*/
    int32_t readout1[recordNum][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}};
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableout1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = readTable(g_conn, g_stmt, tableout1, readout1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.使用GmcDropVertexLabelAsync接口异步删除datalog表
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test01";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    AsyncUserDataT data = {0};

    // 建立异步连接
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    int ret = 0;
    ret = testGmcConnect(&g_conn_async, NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    // 分配句柄
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 利用同步插入数据


    /*--------------------------------------------------写输入表-----------------------------------------*/
    char tableinp1[LABEL_NAME] = "inp1";
    int recordNum = 2;
    int32_t data1[recordNum][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}};
    ret = batchWriteTable(g_conn, g_stmt, tableinp1, data1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 输入表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /*--------------------------------------------------读输出表-----------------------------------------*/
    char tableout1[LABEL_NAME] = "out1";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableout1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "out1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    /*--------------------------------------------------读输入表-----------------------------------------*/

    // 使用GmcDropVertexLabelAsync接口删表，不会返回错误码，删不掉
    // 切换g_stmt_async命名空间，校验客户端和服务端
    ret = GmcDropVertexLabelAsync(g_stmt_async, tableinp1, drop_vertex_label_callback, &data);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, data.status);

    // 校验输入表和输出表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = readTable(g_conn, g_stmt, tableinp1, data1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*--------------------------------------------------读输出表-----------------------------------------*/
    int32_t readout1[recordNum][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}};
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableout1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = readTable(g_conn, g_stmt, tableout1, readout1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.使用GmcTruncateVertexLabel接口清空datalog表的记录
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test01";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    AsyncUserDataT data = {0};
    int ret = 0;

    /*--------------------------------------------------写输入表-----------------------------------------*/
    char tableinp1[LABEL_NAME] = "inp1";
    int recordNum = 2;
    int32_t data1[recordNum][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}};
    ret = batchWriteTable(g_conn, g_stmt, tableinp1, data1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 输入表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*--------------------------------------------------读输出表-----------------------------------------*/
    char tableout1[LABEL_NAME] = "out1";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableout1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "out1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 调用GmcTruncateVertexLabel接口
    ret = GmcTruncateVertexLabel(g_stmt, tableinp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    /*--------------------------------------------------读输入表-----------------------------------------*/
    // 校验输入表和输出表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = readTable(g_conn, g_stmt, tableinp1, data1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*--------------------------------------------------读输出表-----------------------------------------*/
    int32_t readout1[recordNum][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}};
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableout1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = readTable(g_conn, g_stmt, tableout1, readout1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.使用GmcTruncateVertexLabelAsync接口清空datalog表的记录
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test01";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    AsyncUserDataT data = {0};

    // 建立异步连接
    GmcConnT *g_conn_async = NULL;
    GmcStmtT *g_stmt_async = NULL;
    int ret = 0;
    ret = testGmcConnect(&g_conn_async, NULL, GMC_CONN_TYPE_ASYNC, 1, g_epoll_reg_info);
    ASSERT_EQ(GMERR_OK, ret);
    // 分配句柄
    ret = GmcAllocStmt(g_conn_async, &g_stmt_async);
    EXPECT_EQ(GMERR_OK, ret);

    // 利用同步插入数据


    /*--------------------------------------------------写输入表-----------------------------------------*/
    char tableinp1[LABEL_NAME] = "inp1";
    int recordNum = 2;
    int32_t data1[recordNum][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}};
    ret = batchWriteTable(g_conn, g_stmt, tableinp1, data1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 输入表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*--------------------------------------------------读输出表-----------------------------------------*/
    char tableout1[LABEL_NAME] = "out1";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableout1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "out1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*--------------------------------------------------读输入表-----------------------------------------*/
    // 切换g_stmt_async命名空间，校验客户端和服务端
    // 使用GmcTruncateVertexLabelAsync接口清空表数据，不会反会错误码
    ret = GmcTruncateVertexLabelAsync(g_stmt_async, tableinp1, truncate_vertex_label_callback, &data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testWaitAsyncRecv(&data);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, data.status);

    //  GmcDeleteAllFast,需要禁用
    ret = GmcDeleteAllFast(g_stmt, tableinp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 校验输入表和输出表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = readTable(g_conn, g_stmt, tableinp1, data1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*--------------------------------------------------读输出表-----------------------------------------*/
    int32_t readout1[recordNum][4] = {{1, 1, 1, 1}, {2, 2, 2, 1}};
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableout1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = readTable(g_conn, g_stmt, tableout1, readout1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.写数据开启对账
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test01";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;

    /*--------------------------------------------------写输入表-----------------------------------------*/
    char tableinp1[LABEL_NAME] = "inp1";
    int recordNum = 2;
    int32_t data1[recordNum][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}};

    ret = batchWriteTable(g_conn, g_stmt, tableinp1, data1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 输入表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*--------------------------------------------------读输出表-----------------------------------------*/
    char tableout1[LABEL_NAME] = "out1";
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableout1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "out1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcCheckInfoT *checkInfo;
    GmcCheckStatusE checkStatus;

    // 开启对账,支持
    ret = GmcBeginCheck(g_stmt, tableinp1, FULLTABLE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    AW_FUN_Log(LOG_STEP, "主键索引字段更新数据");
    int32_t indexValue = 1;
    int32_t upgradeVersionValue = 0;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableinp1, GMC_OPERATION_UPDATE);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t value = 3;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    indexValue = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableinp1, GMC_OPERATION_UPDATE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &upgradeVersionValue, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT32, &indexValue, sizeof(indexValue));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    value = 4;
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT32, &value, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 调用获取对账数据信息句柄
    ret = GmcGetCheckInfo(g_stmt, tableinp1, FULLTABLE, &checkInfo);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 调用GmcGetCheckStatus接口获取对账状态
    ret = GmcGetCheckStatus(checkInfo, &checkStatus);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "[INFO]checkStatus message status: %d", checkStatus);
    // 结束对账
    bool isAbnormal = false;
    ret = GmcEndCheck(g_stmt, tableinp1, FULLTABLE, isAbnormal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 校验输入表和输出表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    int32_t readinp1[recordNum][4] = {{3, 3, 3, 1}, {4, 4, 4, 2}};
    system(g_command);
    ret = readTable(g_conn, g_stmt, tableinp1, readinp1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    /*--------------------------------------------------读输出表表-----------------------------------------*/
    int32_t readout1[recordNum][4] = {{3, 3, 3, 1}, {4, 4, 4, 1}};
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, tableout1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = readTable(g_conn, g_stmt, tableout1, readout1, recordNum);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.将资源字段与资源池解绑
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;

    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    ret = GmcUnbindResPoolFromLabel(g_stmt, resName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 007.将资源表与资源池解绑
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    // 调用GmcUnbindResPoolFromLabel报错
    ret = GmcUnbindResPoolFromLabel(g_stmt, resName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 008.销毁资源池
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";
    char resPoolName[RES_POOL_NAME] = {};

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    // 获取资源池的名字
    ret = TestGetResPoolName(resPoolName, RES_POOL_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 调用GmcDestroyResPool报错
    ret = GmcDestroyResPool(g_stmt, resPoolName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 009.创建资源池将资源池绑定到扩展资源池
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_errorCode04, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode03, g_errorCode04);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";
    char resPoolName[RES_POOL_NAME] = {};

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;

    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    ret = GmcDestroyResPool(g_stmt, "ResourcePool");
    ret = GmcCreateResPool(g_stmt, g_resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 获取资源池的名字
    ret = TestGetResPoolName(resPoolName, RES_POOL_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 调用GmcBindExtResPool接口报错
    ret = GmcBindExtResPool(g_stmt, resPoolName, "ResourcePool");
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    system(g_command);

    // 销毁资源池
    ret = GmcDestroyResPool(g_stmt, "ResourcePool");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.创建资源池将资源字段绑定到资源池
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_errorCode04, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode03, g_errorCode04);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    // 创建资源池
    ret = GmcDestroyResPool(g_stmt, "ResourcePool");
    ret = GmcCreateResPool(g_stmt, g_resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_command);

    // 销毁资源池
    ret = GmcDestroyResPool(g_stmt, "ResourcePool");
    if (ret != GMERR_OK) {
        testGmcGetLastError();
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.加载含resource表的so，重复将资源字段绑定到加载生成的资源池
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";
    char resPoolName[RES_POOL_NAME] = {};

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    // 获取资源池的名字
    ret = TestGetResPoolName(resPoolName, RES_POOL_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.利用GmcBindResPoolToLabel，将资源表绑定到资源池上
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    (void)snprintf(g_errorCode04, MAX_CMD_SIZE, "GMERR-%d", GMERR_RESOURCE_POOL_ERROR);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode03, g_errorCode04);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    ret = GmcDestroyResPool(g_stmt, "ResourcePool");
    ret = GmcCreateResPool(g_stmt, g_resPoolConfigJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 调用GmcBindResPoolToLabel，报错目前成功
    AW_FUN_Log(LOG_STEP, "将资源表绑定到新建的资源池");
    ret = GmcBindResPoolToLabel(g_stmt, "ResourcePool", resName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    if (ret != GMERR_OK) {
        testGmcGetLastError();
    }

    system(g_command);

    // 销毁资源池
    ret = GmcDestroyResPool(g_stmt, "ResourcePool");
    if (ret != GMERR_OK) {
        testGmcGetLastError();
    }
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.加载so，将资源表再绑定到加载生成的资源池
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";
    char resPoolName[RES_POOL_NAME] = {};

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    // 调用GmcBindResPoolToLabel，报错目前成功
    AW_FUN_Log(LOG_STEP, "将资源表绑定到资源池");

    // 获取资源池的名字
    ret = TestGetResPoolName(resPoolName, RES_POOL_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 将资源表绑定到加载生成的资源池上
    ret = GmcBindResPoolToLabel(g_stmt, resPoolName, resName);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    if (ret != GMERR_OK) {
        testGmcGetLastError();
    }
    
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.设置资源池相关接口：GmcSetPoolIdResource、GmcSetCountResource、
 GmcSetStartIdxResource预期报错
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    // 对资源表插入数据
    int32_t a = 1;
    int32_t b = 1;
    int32_t c = 1;
    int32_t d = 1;
    int32_t dtlcount = 1;
    
    // GMDB 503.1.0 不支持给中间表插入数据，客户端直接拦截
    ret = testGmcPrepareStmtByLabelName(g_stmt, resName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    ret = GmcSetVertexProperty(g_stmt, "a", GMC_DATATYPE_INT32, &a, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetVertexProperty(g_stmt, "b", GMC_DATATYPE_INT32, &b, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetVertexProperty(g_stmt, "c", GMC_DATATYPE_INT32, &c, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetVertexProperty(g_stmt, "d", GMC_DATATYPE_INT32, &d, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    ret = GmcSetVertexProperty(g_stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &dtlcount, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    uint16_t respoolId = 0xFFF;
    uint16_t count = 2;
    uint32_t startIndex = 0xFFFFFFFF;
    uint64_t tmpResIdx = 0;
    ret = GmcSetPoolIdResource(respoolId, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetCountResource(count, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetStartIdxResource(startIndex, &tmpResIdx);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 报错
    ret = GmcSetVertexProperty(g_stmt, "d", GMC_DATATYPE_RESOURCE, &tmpResIdx, sizeof(tmpResIdx));
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);
    // GmcExecute 报错
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_WRONG_STMT_OBJECT, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.获取资源池的定义信息
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";
    char *resPoolJson = NULL;
    char *resTestJson = NULL;
    char resPoolName[RES_POOL_NAME] = {};

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    // 获取资源池的名字
    ret = TestGetResPoolName(resPoolName, RES_POOL_NAME);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetResPool(g_stmt, resPoolName, &resPoolJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 检验获取的json, 获取的是NULL
    EXPECT_STREQ(resTestJson, resPoolJson);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.获取最近插入到buf的资源ID信息
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    /*--------------------------------------------------写输入表-----------------------------------------*/
    char tableinp1[LABEL_NAME] = "inp1";
    int recordNum = 2;
    int32_t data1[recordNum][4] = {{1, 1, 1, 1}};
    ret = singleWriteTable(g_conn, g_stmt, tableinp1, data1, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 输入表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    // 单写可以，批写不可以
    uint32_t bufLen = 0;
    ret = GmcGetResIdNum(g_stmt, &bufLen);
    if (ret != GMERR_OK) {
        testGmcGetLastError();
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_NO_DATA, ret);
    AW_FUN_Log(LOG_DEBUG, "[INFO]Resource_numbers: %d", bufLen);
    uint64_t resIdInfo[bufLen];
    ret = GmcGetResIdInfo(g_stmt, resIdInfo, &bufLen);
    if (ret != GMERR_OK) {
        testGmcGetLastError();
    }
    AW_MACRO_EXPECT_EQ_INT(GMERR_INVALID_PARAMETER_VALUE, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.获取资源的资源池ID、数量、起始索引
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test02";
    char resName[LABEL_NAME] = "res1";

    char libName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    /*--------------------------------------------------写输入表-----------------------------------------*/
    char tableinp1[LABEL_NAME] = "inp1";
    int recordNum = 2;
    int32_t data1[recordNum][4] = {{1, 1, 1, 1}, {2, 2, 2, 2}};
    ret = batchWriteTable(g_conn, g_stmt, tableinp1, data1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 输入表
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableinp1, g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 资源表记录
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview record %s -s %s -ns %s", g_toolPath, resName, g_connServer,
        g_testNameSpace);
    system(g_command);

    // 查看资源池相关的信息
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$CATA_RESOURCE_INFO");
    system(g_command);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s ", g_toolPath, "V\\$STORAGE_RESOURCE_SINGLE_POOL_STAT");
    system(g_command);

    // 获取的信息无意义
    uint64_t resVal = 0;
    uint64_t tmpPoolId = 0, tmpCount = 0, tmpStartIndex = 0;
    ret = GmcGetPoolIdResource(resVal, (uint16_t *)&tmpPoolId);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetCountResource(resVal, (uint16_t *)&tmpCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetStartIdxResource(resVal, (uint32_t *)&tmpStartIndex);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.利用gmddl工具对datalog表进行升级
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test01";
    char shcema_file[FILE_PATH] = "./schema_file/simple_table.gmjson";

    char libName[FILE_PATH] = {0};
    char tableinp1[FILE_PATH] = "inp1";
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmddl -c alter -u online -t %s -f %s -ns %s -s %s", g_toolPath,
        tableinp1, shcema_file, g_testNameSpace, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "[ERROR] Alter schema upgrade unsuccessfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.利用gmddl工具对datalog表进行降级
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test01";

    char libName[FILE_PATH] = {0};
    char tableinp1[FILE_PATH] = "inp1";
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmddl -c alter -t %s -v %d -d %s -ns %s -s %s", g_toolPath, tableinp1,
        1, "sync", g_testNameSpace, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "[ERROR] Alter schema degrade unsuccessfully");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 020.利用gmddl工具,动态删除datalog表
**************************************************************************** */
TEST_F(securityEnhance_001, DataLog_037_001_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode03);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "test01";
    char shcema_file[FILE_PATH] = "./schema_file/simple_table1.gmjson";

    char libName[FILE_PATH] = {0};
    char tableinp1[FILE_PATH] = "inp1";
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)TestUninstallDatalog(soName);
    // 加载
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int ret = 0;
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmddl -c drop -t %s -ns %s -s %s", g_toolPath, tableinp1,
        g_testNameSpace, g_connServer);
    system(g_command);

    // 报错
    ret = executeCommand(g_command, "[ERROR] drop label inp1 unsuccessfully.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
