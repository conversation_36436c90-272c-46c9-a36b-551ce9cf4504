/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: countInFuncTest.cpp
 * Description: udf支持dtlReservedCount值传入
 * Author: <PERSON><PERSON><PERSON><PERSON> ywx1252574
 * Create: 2023-09-13
 */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "t_rd_sn.h"
#include "DatalogRunEnhance.h"

#define MAX_CMD_SIZE 1024


class countInFuncTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase()
    {

    }
    static void TearDownTestCase()
    {
        
    }

};
void countInFuncTest::SetUp()
{
    int ret = 0;
    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_RESOURCE_POOL_NOT_ENOUGH);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);
    system("sh $TEST_HOME/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
}

void countInFuncTest::TearDown()
{
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    testEnvClean();
}
// 001.单表count为1时join
TEST_F(countInFuncTest, DataLog_047_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func1.B";
    char labelName_out[] = "ns_func1.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 002.单表count为2时join
TEST_F(countInFuncTest, DataLog_047_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func1.B";
    char labelName_out[] = "ns_func1.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2,2};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": -3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 003.单表count为-1时join
TEST_F(countInFuncTest, DataLog_047_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func1.B";
    char labelName_out[] = "ns_func1.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,-1,-1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": -1","\"b\": -6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 004.单表count为2147483647时join
TEST_F(countInFuncTest, DataLog_047_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func1.B";
    char labelName_out[] = "ns_func1.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,2147483647,2147483647};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 2147483642");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 005.带忽略字段的单表count为1时join
TEST_F(countInFuncTest, DataLog_047_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func2.B";
    char labelName_out[] = "ns_func2.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 006.带忽略字段的单表Count为-1时join
TEST_F(countInFuncTest, DataLog_047_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func2.B";
    char labelName_out[] = "ns_func2.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,-1,-1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": -1","\"b\": -6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 007.带忽略字段的单表count为2时join
TEST_F(countInFuncTest, DataLog_047_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func2.B";
    char labelName_out[] = "ns_func2.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,2,2};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": -3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 008.带忽略字段的单表count为2147483647时join
TEST_F(countInFuncTest, DataLog_047_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func2.B";
    char labelName_out[] = "ns_func2.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,2147483647,2147483647};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 2147483642");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 009.单表带常量1过滤后count为1时join
TEST_F(countInFuncTest, DataLog_047_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func3.B";
    char labelName_out[] = "ns_func3.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {5,1,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 010.单表带常量1过滤后count为-1时join
TEST_F(countInFuncTest, DataLog_047_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func3.B";
    char labelName_out[] = "ns_func3.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {5,1,-1,-1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": -1","\"a\": 5","\"b\": -6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 011.单表带常量1过滤后count为2时join
TEST_F(countInFuncTest, DataLog_047_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func3.B";
    char labelName_out[] = "ns_func3.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {5,1,2,2};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": -3");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 012.单表带常量1过滤后count为2147483647时join
TEST_F(countInFuncTest, DataLog_047_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func3.B";
    char labelName_out[] = "ns_func3.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {5,1,2147483647,2147483647};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": 2147483642");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 013.单表count为1时join带常量1过滤的func
TEST_F(countInFuncTest, DataLog_047_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func4.B";
    char labelName_out[] = "ns_func4.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {5,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 014.单表count为2时join带常量1过滤的func
TEST_F(countInFuncTest, DataLog_047_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func4.B";
    char labelName_out[] = "ns_func4.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {5,-1,-1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": -1","\"a\": 5","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 015.单表count为-1时join带常量1过滤的func
TEST_F(countInFuncTest, DataLog_047_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func4.B";
    char labelName_out[] = "ns_func4.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {5,2,2};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": -3");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 016.单表count为2147483647时join带常量1过滤的func
TEST_F(countInFuncTest, DataLog_047_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func4.B";
    char labelName_out[] = "ns_func4.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {5,2147483647,2147483647};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": 2147483642");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 017.多表count分别为1,1时join
TEST_F(countInFuncTest, DataLog_047_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func5.A";
    char labelName_in1[] = "ns_func5.B";
    char labelName_out[] = "ns_func5.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {5,2,1};
    int32_t data1[4] = {2,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 018.多表count分别为1，-2时join
TEST_F(countInFuncTest, DataLog_047_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func5.A";
    char labelName_in1[] = "ns_func5.B";
    char labelName_out[] = "ns_func5.C";

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {5,2,1};
    int32_t data1[4] = {2,-1,-2};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": -1","\"a\": 5","\"b\": -6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 019.多表count分别为2,2时join
TEST_F(countInFuncTest, DataLog_047_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func5.A";
    char labelName_in1[] = "ns_func5.B";
    char labelName_out[] = "ns_func5.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {8,3,2};
    int32_t data1[4] = {3,1,2};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 8","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 020.多表count分别为2147483647,1时join
TEST_F(countInFuncTest, DataLog_047_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func5.A";
    char labelName_in1[] = "ns_func5.B";
    char labelName_out[] = "ns_func5.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {7,1,1};
    int32_t data1[4] = {1,1,2147483647};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 7","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 021.多表中某一个表带常量过滤后join
TEST_F(countInFuncTest, DataLog_047_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";

    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func6.A";
    char labelName_in1[] = "ns_func6.B";
    char labelName_out[] = "ns_func6.C";
    const int errCodeLen = 1024;


    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,7,3};
    int32_t data1[4] = {1,3,90,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data1[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 7","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 022.多表中所有表带常量过滤后Join
TEST_F(countInFuncTest, DataLog_047_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func6_1.A";
    char labelName_in1[] = "ns_func6_1.B";
    char labelName_out[] = "ns_func6_1.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,3,56,9};
    int32_t data1[4] = {-2,9,90,-1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data1[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": -1","\"a\": 3","\"b\": -6");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 023.多表中某一个表带忽略字段后join
TEST_F(countInFuncTest, DataLog_047_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func7.A";
    char labelName_in1[] = "ns_func7.B";
    char labelName_out[] = "ns_func7.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {2,4,9};
    int32_t data1[4] = {2,9,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data1[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 4","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 024.多表中所有表带忽略字段后join
TEST_F(countInFuncTest, DataLog_047_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func7_1.A";
    char labelName_in1[] = "ns_func7_1.B";
    char labelName_out[] = "ns_func7_1.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,1,9};
    int32_t data1[4] = {2147483647,9,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &data1[3], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 025.多表count分别为1,1时join带常量过滤的func
TEST_F(countInFuncTest, DataLog_047_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func8.A";
    char labelName_in1[] = "ns_func8.B";
    char labelName_out[] = "ns_func8.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,9};
    int32_t data1[4] = {1,9,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 026.多表count分别为1，-2时join带常量过滤的func
TEST_F(countInFuncTest, DataLog_047_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func8.A";
    char labelName_in1[] = "ns_func8.B";
    char labelName_out[] = "ns_func8.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,9};
    int32_t data1[4] = {-2,9,-1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": -1","\"a\": 5","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 027.多表count分别为2,2时join带常量过滤的func
TEST_F(countInFuncTest, DataLog_047_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func8.A";
    char labelName_in1[] = "ns_func8.B";
    char labelName_out[] = "ns_func8.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {2,5,9};
    int32_t data1[4] = {2,9,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 028.多表count分别为2147483647,1时join带常量过滤的func
TEST_F(countInFuncTest, DataLog_047_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func8.A";
    char labelName_in1[] = "ns_func8.B";
    char labelName_out[] = "ns_func8.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {1,5,9};
    int32_t data1[4] = {2147483647,9,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 029.单表count为1时和manytoone，不带ordered的Agg函数join
TEST_F(countInFuncTest, DataLog_047_029)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_agg1.A";
    char labelName_in1[] = "ns_agg1.B";
    char labelName_out[] = "ns_agg1.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int writeCount = 20;
    ThreeInt4St *Obj_in = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = 1;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 2","\"b\": 145","\"a\": 1","\"b\": 45");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 030.单表count为1时和manytoone，带ordered的Agg函数join
TEST_F(countInFuncTest, DataLog_047_030)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_agg2.A";
    char labelName_in1[] = "ns_agg2.B";
    char labelName_out[] = "ns_agg2.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int32_t sum[2] = {0, 0};
    int writeCount = 20;
    ThreeInt4St *Obj_in = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = 1;
        Obj_in[i].dtlReservedCount = 1;
        (i < writeCount/2) ? (sum[0] += Obj_in[i].b) : (sum[1] += Obj_in[i].b);
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 2","\"b\": 145","\"a\": 1","\"b\": 45");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 031.单表count为2时和manytomany，带ordered的Agg函数join
TEST_F(countInFuncTest, DataLog_047_031)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_agg4.A";
    char labelName_in1[] = "ns_agg4.B";
    char labelName_out[] = "ns_agg4.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    ThreeInt4St *Obj_in = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = 2;
        Obj_in[i].dtlReservedCount = 2;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 1 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 0","\"b\": 45","\"b\": 9");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 2 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 145","\"b\": 19","\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 032.单表count为-1时和manytoone，带ordered的Agg函数join
TEST_F(countInFuncTest, DataLog_047_032)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_agg2.A";
    char labelName_in1[] = "ns_agg2.B";
    char labelName_out[] = "ns_agg2.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int writeCount = 20;
    ThreeInt4St *Obj_in = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = -1;
        Obj_in[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 2","\"b\": 145","\"a\": 1","\"b\": 45");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 033.单表count为1时和manytomany，不带ordered的Agg函数join
TEST_F(countInFuncTest, DataLog_047_033)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_agg3.A";
    char labelName_in1[] = "ns_agg3.B";
    char labelName_out[] = "ns_agg3.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    ThreeInt4St *Obj_in = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = 1;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 1 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 0","\"b\": 45","\"b\": 9");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 2 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 145","\"b\": 19","\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 034.单表count为2时和manytoone，不带ordered的Agg函数join
TEST_F(countInFuncTest, DataLog_047_034)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_agg1.A";
    char labelName_in1[] = "ns_agg1.B";
    char labelName_out[] = "ns_agg1.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int writeCount = 20;
    ThreeInt4St *Obj_in = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = 2;
        Obj_in[i].dtlReservedCount = 2;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 2","\"b\": 145","\"a\": 1","\"b\": 45");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 035.单表count为-1时和manytomany，不带ordered的Agg函数join
TEST_F(countInFuncTest, DataLog_047_035)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_agg3.A";
    char labelName_in1[] = "ns_agg3.B";
    char labelName_out[] = "ns_agg3.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    ThreeInt4St *Obj_in = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = -1;
        Obj_in[i].dtlReservedCount = -1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 1 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 0","\"b\": 45","\"b\": 9");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 2 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 145","\"b\": 19","\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 036.单表count为2147483647时和manytomany，带ordered的Agg函数join
TEST_F(countInFuncTest, DataLog_047_036)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_agg4.A";
    char labelName_in1[] = "ns_agg4.B";
    char labelName_out[] = "ns_agg4.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int32_t groupOut[2][3] = {{0, 9, 45}, {10, 19, 145}};   //min, max, sum
    int writeCount = 20;
    ThreeInt4St *Obj_in = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = 2147483647;
        Obj_in[i].dtlReservedCount = 2147483647;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 1 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 0","\"b\": 45","\"b\": 9");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 2 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 145","\"b\": 19","\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 037.单表插入数据时count值为0和func进行join操作
TEST_F(countInFuncTest, DataLog_047_037)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func1.B";
    char labelName_out[] = "ns_func1.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {0,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 038.单表插入数据时count值为0和agg进行join操作
TEST_F(countInFuncTest, DataLog_047_038)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_agg1.A";
    char labelName_in1[] = "ns_agg1.B";
    char labelName_out[] = "ns_agg1.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int writeCount = 20;
    ThreeInt4St *Obj_in = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = 0;
        Obj_in[i].dtlReservedCount = 0;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 1 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 0","\"b\": 45","\"b\": 9");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 2 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 145","\"b\": 19","\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 039.多表插入数据,其中一个表count值为0时和func进行join操作
TEST_F(countInFuncTest, DataLog_047_039)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func5.A";
    char labelName_in1[] = "ns_func5.B";
    char labelName_out[] = "ns_func5.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[4] = {0,5,2};
    int32_t data1[4] = {1,2,0};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in1, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data1[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data1[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data1[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 5","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 040.表内带variant关键字，count为-2时和func进行Join
TEST_F(countInFuncTest, DataLog_047_040)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func9.A";
    char labelName_out[] = "ns_func9.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {-2,1,1};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": -4");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 041.表内带variant关键字，count为1的和agg进行join
TEST_F(countInFuncTest, DataLog_047_041)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_agg5.A";
    char labelName_in1[] = "ns_agg5.B";
    char labelName_out[] = "ns_agg5.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int writeCount = 20;
    ThreeInt4St *Obj_in = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = 2;
        Obj_in[i].dtlReservedCount = 2;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 1 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 0","\"b\": 45","\"b\": 9");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -c a -eq 2 -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 145","\"b\": 19","\"b\": 10");
    AW_MACRO_EXPECT_EQ_INT(-1,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 042.在count传入func后进行计算
TEST_F(countInFuncTest, DataLog_047_042)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_func10.B";
    char labelName_out[] = "ns_func10.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t data[3] = {1,1,5};

    ret = testGmcPrepareStmtByLabelName(stmt, labelName_in, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // set value
    ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &data[0], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &data[1], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &data[2], sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"b\": 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 043.在count传入agg后进行计算
TEST_F(countInFuncTest, DataLog_047_043)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";
    char labelName_in[] = "ns_agg6.A";
    char labelName_in1[] = "ns_agg6.B";
    char labelName_out[] = "ns_agg6.C";
    const int errCodeLen = 1024;

    (void)TestUninstallDatalog(nsName);
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int groupId[2] = {1, 2};
    int writeCount = 20;
    ThreeInt4St *Obj_in = (ThreeInt4St *)malloc(sizeof(ThreeInt4St) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        Obj_in[i].a = (i < writeCount/2) ? groupId[0] : groupId[1];
        Obj_in[i].b = i;
        Obj_in[i].c = 5;
        Obj_in[i].dtlReservedCount = 1;
    }
    ret = writeRecord(conn, stmt, labelName_in, Obj_in, writeCount/2, ThreeInt4_set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = writeRecord(conn, stmt, labelName_in, (Obj_in+writeCount/2), (writeCount-writeCount/2), ThreeInt4_set);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(Obj_in);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview record %s -ns %s", &labelName_out, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "\"dtlReservedCount\": 1","\"a\": 2","\"b\": 145","\"a\": 1","\"b\": 45");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
}
// 044.使用gmprecompiler离线查询func和agg物理计划(已不支持)
TEST_F(countInFuncTest, DataLog_047_044)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char fileName1[] = "function/countInFunc.c";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";

    (void)TestUninstallDatalog(nsName); 
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -f %s", &fileName);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmprecompiler -s %s", &fileName1);
    ret = executeCommand(g_command, "The first option \"-s\" is undefined."," Exit with code 1009006.");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

}
// 045.加载.so文件后使用gmsysview在线查询func和agg视图
TEST_F(countInFuncTest, DataLog_047_045)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";

    (void)TestUninstallDatalog(nsName); 
    ret = TestLoadDatalog(libName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmsysview -q V\\$DATALOG_PLAN_EXPLAIN_INFO");
    ret = executeCommand(g_command, "TABLE_NAME: ns_agg6.C");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = executeCommand(g_command, "TABLE_NAME: ns_func10.C");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command, "Output: dtlReservedCount, a, b");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    ret = executeCommand(g_command, "Input Parameters: (dtlReservedCount, b, c)");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    
    // 卸载
    ret = TestUninstallDatalog(nsName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");

}
// 046.使用gmconvert工具将func和agg生成gmjson文件
TEST_F(countInFuncTest, DataLog_047_046)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char fileName[] = "function/countInFunc.d";
    char fileName1[] = "./function/ns_func1_funcT.gmjson";
    char fileName2[] = "./function/ns_agg1_aggT.gmjson";
    char udfFileName[] = "function/countInFunc_udf.c";
    char libName[] = "function/countInFunc.so";
    char nsName[] = "countInFunc";

    (void)TestUninstallDatalog(nsName); 
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmconvert -i %s -o ./function", &fileName);
    system(g_command);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s", &fileName1);
    ret = executeCommand(g_command, "\"fields\"","\"name\": \"dtlReservedCount\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat %s", &fileName2);
    ret = executeCommand(g_command, "\"input_field_num\": 3,","\"fields\"","\"name\": \"dtlReservedCount\"");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

}
