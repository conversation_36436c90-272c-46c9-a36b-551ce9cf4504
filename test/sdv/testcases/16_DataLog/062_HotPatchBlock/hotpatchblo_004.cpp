/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.0.0 迭代一Datalog热补丁支持阻塞非合并式重做-并发可靠性测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2024.01.17]
*****************************************************************************/
#include "hotpatchblock.h"
#include "DatalogRun.h"

using namespace std;

class hotpatchblo_004_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void hotpatchblo_004_test::SetUp()
{
    // 避免受其他用例影响
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void hotpatchblo_004_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

class hotpatchblo_004_test1 : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void hotpatchblo_004_test1::SetUp()
{
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    memset(g_catalogTableName, 0, sizeof(g_catalogTableName));
    
}
void hotpatchblo_004_test1::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    // 校验引用计数
    ret = CheckRefCount();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    system("rm -rf /root/_datalog_/");
}

/* ****************************************************************************
 Description  : 001.patch.d声明设置block为1，线程1对旧so进行DML操作，
 线程2并发加载升级so拿到datalog_service锁（一级hung死时间），预期加载成功
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable, NULL);
    // 加载升级so, 加载线程锁的超时时间是一级hung时间3s
    sleep(13);
    pthread_create(&thr_arr[1], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.patch.d声明设置block为1，线程1对旧so进行DML操作，
 线程2并发加载升级so拿不到锁(datalog serverice)，预期加载失败
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
#ifndef ENV_RTOSV2X
    system("top -b -d1 -n60 -H 1 >> DataLog_062_004_002.txt &");
#endif
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable2, NULL);
    // 加载升级so, 加载线程锁的超时时间是euler一级hung时间3s，V2x设备一级hung死时间11s，仿真环境一级Hung死时间15s
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadLoadUpgradeSo1, (void *)patchSoName);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 003.patch.d声明设置block为1，线程1对旧so进行DML操作，
 线程2加载降级so拿到锁(datalog serverice)，预期加载成功
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    sleep(30);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    sleep(13);
    // 加载降级so拿到datalog service锁， euler：3s， V2x设备：11s
    pthread_create(&thr_arr[1], NULL, ThreadLoadRollbackSo, (void *)rollbackSoName);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 004.patch.d声明设置block为1，线程1对旧so进行DML操作，
 线程2加载降级so拿不到锁(datalog serverice)，预期加载失败
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    sleep(30);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    pthread_t thr_arr[2];
    // 一个线程写datalog输入表，执行15s
    pthread_create(&thr_arr[0], NULL, ThreadBatchWriteDatalogTable1, NULL);
    sleep(13);
    // 加载降级so拿到datalog service锁， euler：3s， V2x设备：11s
    pthread_create(&thr_arr[1], NULL, ThreadLoadRollbackSo, (void *)rollbackSoName);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.patch.d声明设置block为1,线程1加载升级so，线程2并发卸载so，立即报错
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    pthread_t thr_arr[2];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 加载卸载so
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadLoadUnloadSo, (void *)soName);
    sleep(30);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.patch.d声明设置block为1,线程1加载降级so，线程2并发卸载so，立即报错
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    AddWhiteList(GMERR_FEATURE_NOT_SUPPORTED);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    sleep(31);

    pthread_t thr_arr[2];
    // 一个线程加载降级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadRollbackSo, (void *)rollbackSoName);
    // 加载卸载so
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadLoadUnloadSo, (void *)soName);
    sleep(30);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// block 0 + enable 0  == block 1
/* ****************************************************************************
 Description  : 007.patch.d声明设置block为1,线程1加载升级so，sleep 1s，
 线程2并发进行DML操作 insert（拿到锁 5*一级hung死时间），预期执行DML成功
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    pthread_t thr_arr[1];
    // 编译生成升级so
    // 加载升级so,重做需要114s
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(1);
    // 线程1并发写输入表inp1, 1级hung死*5 时间内，拿到锁  euler：15s   V2x:55s
    // 现在是通过latch锁卡时间(事务锁时间)，每超时1次有一次12002打屏；总体小于5*1级hung死；euler：重试15次   V2x设备：重试2次 （60s）
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable1, NULL);
    pthread_join(thr_arr[0], NULL);
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn3[recordNum + 1] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}, {1, upVerVal, 5, 5, 10}};
    C3Int8T objIn4[recordNum - 1] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8},
        {1, upVerVal, 5, 5, 10}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum + 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// block 0 + enable 0  == block 1
/* ****************************************************************************
 Description  : 008.patch.d声明设置block为1，线程1加载升级so，sleep 1s，
 线程2并发进行DML操作 insert（拿不到锁 设备上55s，euler 15s），预期报1012002错误码
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
#ifdef ENV_RTOSV2X
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode02,  g_errorCode03);
#endif
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int recordNum = 15;
    // 写表
    C3Int8T objIn1[recordNum] = {0};
    C3Int8T objIn2[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].c = 2 * i + 2;
        objIn1[i].upgradeVersion = 0;
        objIn1[i].dtlReservedCount = i + 1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].c = 2 * i + 2;
        objIn2[i].upgradeVersion = 0;
        objIn2[i].dtlReservedCount = 1;
    }
    // 2024年5月27日变更：服务端超时时间比客户端超时时间少5秒；批写19条执行57s会超时，改成单写
    // 2024年8月20日变更：批写15条数据，能够达到效果
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "insert datalog table test end.");
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp2 read complete!!!");
    // 一个线程写datalog输入表，执行15s
    pthread_t thr_arr[1];
    // 编译生成升级so
    // 加载升级so,重做需要90s
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(1);
    // 线程1并发写输入表inp1, 阻塞拿不到锁
    pthread_create(&thr_arr[0], NULL, ThreadSingleWriteDatalogTable, NULL);
    pthread_join(thr_arr[0], NULL);
    // 数据重做时间，最少45s*2
    // 仿真和设备sleep 40s；euler sleep 80s;
#ifdef ENV_RTOSV2X
    sleep(40);
#else
    sleep(80);
#endif
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].upgradeVersion = upVerVal;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].upgradeVersion = upVerVal;
    }
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// block 0 + enable 0  == block 1
/* ****************************************************************************
 Description  : 009.patch.d声明设置block为1，线程1加载降级so，sleep 1s，
 线程2并发进行DML操作 delete（拿到锁），预期执行DML成功
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab002";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(10);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    pthread_t thr_arr[2];
    // 一个线程加载降级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadRollbackSo, (void *)rollbackSoName);
    // 一个线程并发delete数据, delete操作拿到锁
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadDeleteRecord, NULL);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    sleep(10);
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    C3Int8T objIn3[recordNum - 1] = {{1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4}, {3, upVerVal, 3, 3, 3},
        {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 1] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8},
        {1, upVerVal, 3, 3, 6}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 1, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// block 0 + enable 0  == block 1
/* ****************************************************************************
 Description  : 010.patch.d声明设置block为1，线程1加载降级so，sleep 1s，
 线程2并发进行DML操作 delete(拿不到锁)，预期报1012002错误码
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_LOCK_NOT_AVAILABLE);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
#ifdef ENV_RTOSV2X
    (void)snprintf(g_errorCode02, MAX_CMD_SIZE, "GMERR-%d", GMERR_NO_DATA);
    (void)snprintf(g_errorCode03, MAX_CMD_SIZE, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode02,  g_errorCode03);
#endif
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int recordNum = 15;
    // 写表
    C3Int8T objIn1[recordNum] = {0};
    C3Int8T objIn2[recordNum] = {0};
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i + 1;
        objIn1[i].b = i + 1;
        objIn1[i].c = 2 * i + 2;
        objIn1[i].upgradeVersion = 0;
        objIn1[i].dtlReservedCount = i + 1;
    }
    for (int i = 0; i < recordNum; i++) {
        objIn2[i].a = i + 1;
        objIn2[i].b = i + 1;
        objIn2[i].c = 2 * i + 2;
        objIn2[i].upgradeVersion = 0;
        objIn2[i].dtlReservedCount = 1;
    }
    // 2024年5月27日变更：服务端超时时间比客户端超时时间少5秒；批写19条执行57s会超时，改成单写
    // 2024年8月20日变更：批写15条数据，能够达到效果
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "insert datalog table test end.");

    pthread_t thr_arr[2];
    // 一个线程加载降级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadRollbackSo, (void *)rollbackSoName);
    // 一个线程并发delete数据, delete操作拿不到锁
    sleep(1);
    pthread_create(&thr_arr[1], NULL, ThreadDeleteRecord1, NULL);

    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);
    // 数据重做时间，最少45s*2
    // 仿真和设备sleep 40s；euler sleep 80s;
#ifdef ENV_RTOSV2X
    sleep(40);
#else
    sleep(80);
#endif
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "15");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// block 0 + enable 0  == block 1
/* ****************************************************************************
 Description  : 011.patch.d声明设置block为1，线程1加载升级so，线程2并发直连读非触发表;
 线程3并发直连读重做表；预期能读到
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    pthread_t thr_arr[3];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 一个线程直连读读非触发表
    pthread_create(&thr_arr[1], NULL, ThreadScanTable, (void *)"inp2");
    // 一个线程直连读读触发表
    pthread_create(&thr_arr[2], NULL, ThreadScanTable, (void *)"inp1");

    for (int i = 0; i < 3; i++) {
        pthread_join(thr_arr[i], NULL);
    }

    sleep(30);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.patch.d声明设置block为1，线程1加载降级so，
 线程2并发利用直连读查询触发表；线程3并发利用gmsysview record查询触发表；预期能查到
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab003";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    pthread_t thr_arr[4];
    // 一个线程加载降级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadRollbackSo, (void *)rollbackSoName);
    sleep(3);
    // 一个线程直连读读触发表
    pthread_create(&thr_arr[1], NULL, ThreadScanTable, (void *)"inp1");
    // 一个线程gmsysview record读inp1
    pthread_create(&thr_arr[2], NULL, ThreadGmsysviewRecordTable, (void *)"inp1");
    // 一个线程gmsysview record读out1
    pthread_create(&thr_arr[3], NULL, ThreadGmsysviewRecordTable, (void *)"out1");

    for (int i = 0; i < 4; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    sleep(30);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.patch.d声明设置block为0，enableDatalogDmlWhenUpgrading为1时，
 线程1加载升级so(重做需要时间)，线程2并发加载降级so，预期报错
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab004";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 3, 3, 6}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    pthread_t thr_arr[2];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    sleep(3);
    // 一个线程加载降级so
    pthread_create(&thr_arr[1], NULL, ThreadLoadRollbackSo1, (void *)rollbackSoName);
    
    for (int i = 0; i < 2; i++) {
        pthread_join(thr_arr[i], NULL);
    }
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    for (int i = 0; i < 20; i++) {
        system(g_command);
        sleep(1);
    }

    sleep(30);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.第1个升级sopatch.d声明设置block为1，enableDatalogDmlWhenUpgrading为1，
 加载第1个升级so；第二个升级sopatch.d声明设置block为0，enableDatalogDmlWhenUpgrading为1；
 重做完成，加载第二个升级so，查看upgradeVersion为1
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName1[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName1[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
     (void)sprintf(patchSoName1, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName1, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 3, 3, 3}, {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 0", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    // 加载第二次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 1", "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);

    // 加载降级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName1));
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 0", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 0", "BLOCK_MODE: null", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.第1个升级sopatch.d声明设置block为0，enableDatalogDmlWhenUpgrading为1，
 加载第1个升级so；第二个升级sopatch.d声明设置block为1，enableDatalogDmlWhenUpgrading为1；
 重做过程中，并发加载第二个升级so，预期理立即报错
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    AddWhiteList(GMERR_LOCK_NOT_AVAILABLE);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab006";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName1[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName1[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName1, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName1, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    pthread_t thr_arr[1];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo1, (void *)patchSoName1);
    pthread_join(thr_arr[0], NULL);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    sleep(30);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.第1个升级sopatch.d声明设置block为1，enableDatalogDmlWhenUpgrading为1，
 加载第1个升级so；第二个升级sopatch.d声明设置block为0，enableDatalogDmlWhenUpgrading为1；
 重做过程中，并发加载第二个升级so（拿不到upgradeInfo锁，1级hung死时间），预期加载报错
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab007";
    int ret = 0;
    int32_t upVerVal = -1;
    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName1[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName1[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName1, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName1, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    pthread_t thr_arr[1];
    // 一个线程加载升级so
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo1, (void *)patchSoName1);
    pthread_join(thr_arr[0], NULL);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    sleep(28);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    
    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, "inp1", g_connServer,
        g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "inp1", "5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.patch.d声明设置block为1，加载升降级so过程中，并发查询热补丁视图，预期正常
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 创建4个线程：线程1升级，线程2不停地查热补丁视图，线程3降级，线程4不停地查热补丁视图
    // 编译生成升级so和回滚so
    // 加载第一次升级so
    pthread_t thr_arr[4];
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    sleep(1);
    // 一个线程查热补丁视图
    pthread_create(&thr_arr[1], NULL, ThreadScanPatchView, (void *)soName);
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 0", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 加载回滚so
    pthread_create(&thr_arr[2], NULL, ThreadLoadRollbackSo, (void *)rollbackSoName);
    sleep(1);
    // 一个线程查热补丁视图
    pthread_create(&thr_arr[3], NULL, ThreadScanPatchView, (void *)soName);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.第1个升级sopatch.d声明设置block为1，enableDatalogDmlWhenUpgrading为1，
 加载第1个升级so；第二个升级sopatch.d声明设置block为0，enableDatalogDmlWhenUpgrading为1；
 重做完成，加载第二个升级so；加载对应的降级so；循环操作1000次；无内存泄露，引用计数清除干净；catalog视图
**************************************************************************** */
TEST_F(hotpatchblo_004_test1, DataLog_062_004_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab008";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

    (void)snprintf(g_catalogTableName, MAX_CMD_SIZE, "inp1");

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 1000;
#endif

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    system("gmsysview count");
    system("gmsysview record out1");

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 加载第二次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02));
    AW_FUN_Log(LOG_DEBUG, "after upgrade two scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    system("gmsysview count");
    system("gmsysview record out1");

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn5[recordNum - 2] = {{1, upVerVal1, 1, 2, 3}, {1, upVerVal1, 2, 2, 4}, {1, upVerVal1, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade two out1 read complete!!!");
    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    // 返回卸载rollbackV3.so和加载patchV3.so看内存
#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    
    pthread_t thr_arr[1];
    // 循环升降级1000次
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
    }
    sleep(10);
    ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
#ifndef ENV_RTOSV2X
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
#endif
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.第1个升级sopatch.d声明设置block为0，enableDatalogDmlWhenUpgrading为1，
 加载第1个升级so；第二个升级sopatch.d声明设置block为1，enableDatalogDmlWhenUpgrading为1；
 重做完成，加载第二个升级so；加载对应的降级so；循环操作1500次；无内存泄露，引用计数清除干净;catalog视图
**************************************************************************** */
TEST_F(hotpatchblo_004_test1, DataLog_062_004_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab009";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char patchSoName02[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoName02[FILE_PATH] = {0};

    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};

    (void)snprintf(g_catalogTableName, MAX_CMD_SIZE, "inp1");

#ifdef ENV_RTOSV2X
    cycleCnt = 100;
#else
    cycleCnt = 1500;
#endif

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName02, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName02, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{2, 0, 1, 1, 1}, {2, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {2, 0, 3, 3, 3}, {2, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{2, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4}, {2, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 加载第二次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02));
    AW_FUN_Log(LOG_DEBUG, "after upgrade two scan table");
    sleep(2);

    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal1);
    C3Int8T objIn5[recordNum - 2] = {{2, upVerVal1, 1, 2, 3}, {2, upVerVal1, 2, 2, 4}, {2, upVerVal1, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade two out1 read complete!!!");

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    // 返回卸载rollbackV3.so和加载patchV3.so看内存
#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    system(g_command);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#endif
    ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "before Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
    
    // 每次重做完成，才往下执行
    pthread_t thr_arr[1];
    // 循环升降级1500次
    for (int i = 0; i < cycleCnt; i++) {
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName02, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName02, NULL, false));
        (void)ThreadScanPatchView((void *)soName);
    }
    sleep(10);
    ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_DEBUG, "after Rollback And Upgrade");
    AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
    AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
    AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
    AW_MACRO_EXPECT_EQ_STR(tableShareMem01, tableShareMem02);
#if defined RUN_INDEPENDENT
    AW_MACRO_EXPECT_EQ_STR(udfDynMem01, udfDynMem02);
#endif
    AW_MACRO_EXPECT_EQ_STR(planCacheDynMem01, planCacheDynMem02);

#if defined ENV_RTOSV2X
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopShareMemCtx", g_toolPath);
    ret = executeCommand(g_command, "fetched all records, finish!");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
#else
    snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=RtMsgTopDynmemCtx", g_toolPath);
    ret = executeCommand(g_command, "TOTAL_ALLOC_SIZE: [0] MB [0] KB [0] Byte", "MEMORY_USAGE: 0.00%");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system(g_command);
#endif
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 提个单转需求；
/* ****************************************************************************
 Description  : 020.升级so的block设置为0；同时将其对应so的降级so的block设置为1，预期加载降级so报错
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab010";
    char soName1[FILE_PATH] = "reliab008";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t cycleCnt = 0;

    char libName[FILE_PATH] = {0};
    char patchSoName01[FILE_PATH] = {0};
    char rollbackSoName01[FILE_PATH] = {0};
    char rollbackSoNameOrign[FILE_PATH] = {0};

    // enableDatalogDmlWhenUpgrading设置为1
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 预期成功
    ret = executeCommand(g_command, "after setting config:", "config current value: 1");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName01, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName01, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoNameOrign, "%s/%s_rollbackV2.so", outputFilePath, soName1);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 编译生成升级so和回滚so
    // 加载第一次升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName01));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    (void)SystemSnprintf("rm -rf %s", rollbackSoName01);
    (void)SystemSnprintf("cp %s %s", rollbackSoNameOrign, rollbackSoName01);

    // 加载降级so
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -s %s -c datalog -rollback %s -ns %s", g_toolPath, g_connServer,
        rollbackSoName01, g_testNameSpace);
    system(g_command);
    sleep(2);
    // 查看加载降级so，视图的状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: LOAD_FAIL_ROLL_BACK_SUC");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "test end.");
}

// 回调函数中加sleep
/* ****************************************************************************
 Description  : 021.补充异常场景，显示声明block为1，重做时，订阅推送失败，卸载so之后，看资源是否回收干净
**************************************************************************** */
TEST_F(hotpatchblo_004_test1, DataLog_062_004_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_REQUEST_TIME_OUT);
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONNECTION_RESET_BY_PEER);
    AW_ADD_ERR_WHITE_LIST(2, g_errorCode01, g_errorCode02);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab011";
    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};
    int failCnt = 0;
    int cycleCnt = 0;
    int totalCycleCnt = 5;
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)snprintf(g_catalogTableName, MAX_CMD_SIZE, "inp1");

    // 初始化
    pthread_mutex_init(&g_threadLock, NULL);
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    while (cycleCnt < totalCycleCnt) {
        // 每次执行都置0
        g_threadWait = 0;
        // 建连前，查内存视图
        ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "before load upgrade so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
        AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        // 创建连接
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
        // 创建订阅关系
        int chanRingLen = 256;
        GmcConnT *conn_sn_sync = NULL;
        GmcStmtT *stmt_sn_sync = NULL;
        const char *subConnName = "subConnName026";
        const char *subName01 = "subNotifyout1";
        const char *subName02 = "subNotifyrs2";
        testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
        // 创建订阅关系, pubsub普通表
        char *sub_info01 = NULL;
        readJanssonFile("./schema_file/subInfopub.json", &sub_info01);
        EXPECT_NE((void *)NULL, sub_info01);
        GmcSubConfigT tmp_sub_info01;
        tmp_sub_info01.subsName = subName01;
        tmp_sub_info01.configJson = sub_info01;
        SnUserDataT *userData01 = (SnUserDataT *)malloc(sizeof(SnUserDataT));

        // pubsub回调超时时间改成30s
        ret = GmcSubscribe(stmt, &tmp_sub_info01, conn_sn_sync, snCallback1, userData01);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        free(sub_info01);
        
        // 插入数据
        int recordNum = 5;
        C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3},
            {4, 0, 4, 4, 8}};
        C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3},
            {1, 0, 4, 4, 8}};
        ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        pthread_mutex_lock(&g_threadLock);
        g_threadWait++;
        pthread_mutex_unlock(&g_threadLock);
        // 校验输入表
        ret = readRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

        // 加载升级so
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
        AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
        // v2x环境pubsub回调超时15s
#ifdef ENV_RTOSV2X
        sleep(16);
#else
        sleep(3);
#endif
        // 校验热补丁视图重做状态
        (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
        system(g_command);
        ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 取消订阅关系
        ret = GmcUnSubscribe(stmt, subName01);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // 释放分配的内存
        free(userData01);
        // 卸载
        ret = TestUninstallDatalog(soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(10);
        // 断连后，查内存视图
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        AW_FUN_Log(LOG_DEBUG, "after unload so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
        AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        if (strcmp(tableShareMem01, tableShareMem02) != 0 || strcmp(udfDynMem01, udfDynMem02) != 0 ||
            strcmp(planCacheDynMem01, planCacheDynMem01) != 0) {
                failCnt++;
            }
        cycleCnt++;
    }
    AW_FUN_Log(LOG_DEBUG, "cycleCnt is %d", cycleCnt);
    AW_FUN_Log(LOG_DEBUG, "failCnt is %d", failCnt);
    AW_MACRO_EXPECT_EQ_BOOL(true, failCnt < 3);
    pthread_mutex_destroy(&g_threadLock);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.补充异常场景，重做时，表资源超限，卸载so，看资源是否回收干净
**************************************************************************** */
TEST_F(hotpatchblo_004_test1, DataLog_062_004_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_CONFIGURATION_LIMIT_EXCEEDED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab012";
    char tableShareMem01[128] = {0};
    char tableShareMem02[128] = {0};
    char udfDynMem01[128] = {0};
    char udfDynMem02[128] = {0};
    char planCacheDynMem01[128] = {0};
    char planCacheDynMem02[128] = {0};
    int failCnt = 0;
    int cycleCnt = 0;
    int totalCycleCnt = 5;
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)snprintf(g_catalogTableName, MAX_CMD_SIZE, "inp1");

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;

    // 查PTL_DATALOG_SO_INFO和PTL_DATALOG_PATCH_INFO视图
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > text.txt", g_toolPath, g_viewName,
        g_connServer);
    system(g_command);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s > test.txt", g_toolPath,
        "V\\$PTL_DATALOG_SO_INFO", g_connServer);
    system(g_command);
    // 增加共享内存和动态内存视图打印
    system("gmsysview -q V\\$COM_SHMEM_CTX -f CTX_NAME=\"catalog share memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");
    system("gmsysview -q V\\$COM_DYN_CTX -f CTX_NAME=\"catalog dynamic memory context\"| grep \"TOTAL_ALLOC_SIZE:\" ");

    while (cycleCnt < totalCycleCnt) {
        // 建连前，查内存视图
        ret = TestGetCTXStr(tableShareMem01, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planCacheDynMem01, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_DEBUG, "before load upgrade so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem01 is %s", tableShareMem01);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem01 is %s", udfDynMem01);
        AW_FUN_Log(LOG_DEBUG, "planCacheDynMem01 is %s", planCacheDynMem01);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        // 创建连接
        ret = testGmcConnect(&conn, &stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
        // 插入数据
        int recordNum = 5;
        C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {2, 0, 2, 2, 2}, {3, 0, 3, 3, 3},
            {4, 0, 4, 4, 8}};
        C3Int8T objIn2[recordNum - 2] = {{1, 0, 1, 1, 10}, {1, 0, 1, 2, 10}, {1, 0, 2, 2, 10}};
        ret = writeRecord(conn, stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 校验输入表
        ret = readRecord(conn, stmt, "out1", objIn2, recordNum - 2, C3Int8Get);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

        // 加载升级so
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
        AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
        sleep(2);

        // 校验热补丁视图重做状态
        (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
        ret = executeCommand(g_command, "BLOCK_MODE: null", "PATCH_STATE: REDO_FAIL_ROLL_BACK_SUC");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
  
        ret = testGmcDisconnect(conn, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        // 卸载
        ret = TestUninstallDatalog(soName);
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        sleep(10);
        // 断连后，查内存视图
        ret = TestGetCTXStr(tableShareMem02, 128, (char *)"COM_SHMEM_CTX", (char *)"catalog share memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(udfDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"catalog dynamic memory context");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = TestGetCTXStr(planCacheDynMem02, 128, (char *)"COM_DYN_CTX", (char *)"datalog plan cache memCtx");
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        
        AW_FUN_Log(LOG_DEBUG, "after unload so");
        AW_FUN_Log(LOG_DEBUG, "tableShareMem02 is %s", tableShareMem02);
        AW_FUN_Log(LOG_DEBUG, "udfDynMem02 is %s", udfDynMem02);
        AW_FUN_Log(LOG_DEBUG, "planCacheDynMem02 is %s", planCacheDynMem02);
        AW_FUN_Log(LOG_DEBUG, "cycleCnt %d\n", cycleCnt);
        if (strcmp(tableShareMem01, tableShareMem02) != 0 || strcmp(udfDynMem01, udfDynMem02) != 0 ||
            strcmp(planCacheDynMem01, planCacheDynMem01) != 0) {
                failCnt++;
            }
        cycleCnt++;
    }
    AW_FUN_Log(LOG_DEBUG, "cycleCnt is %d", cycleCnt);
    AW_FUN_Log(LOG_DEBUG, "failCnt is %d", failCnt);
    AW_MACRO_EXPECT_EQ_BOOL(true, failCnt < 3);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.block显示声明为1时，重做过程中，并发修改配置项
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab001";
    int ret = 0;
    int32_t upVerVal = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 创建4个线程：线程1升级，线程2、线程4不停地修改enableDatalogDmlWhenUpgrading配置项，线程3降级
    // 编译生成升级so和回滚so
    // 加载第一次升级so
    pthread_t thr_arr[4];
    pthread_create(&thr_arr[0], NULL, ThreadLoadUpgradeSo, (void *)patchSoName);
    // 一个线程写datalog输入表
    pthread_create(&thr_arr[1], NULL, ThreadChangeEableDmlUprade, (void *)soName);
    pthread_join(thr_arr[0], NULL);
    pthread_join(thr_arr[1], NULL);

    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "UPGRADE_VERSION: 0", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 加载回滚so
    pthread_create(&thr_arr[2], NULL, ThreadLoadRollbackSo, (void *)rollbackSoName);
    // 一个线程写datalog输入表
    pthread_create(&thr_arr[3], NULL, ThreadChangeEableDmlUprade, (void *)soName);
    pthread_join(thr_arr[2], NULL);
    pthread_join(thr_arr[3], NULL);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 024.显示声明block为1，将fetch Size设置最大2147483647，原so输入表预置50万条数据，加载升级so，预期成功
**************************************************************************** */
TEST_F(hotpatchblo_004_test, DataLog_062_004_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");

    char inputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char outputFilePath[FILE_PATH] = "./datalogFile/reliab";
    char soName[FILE_PATH] = "reliab013";
    int ret = 0;
    int32_t upVerVal = -1;

    // 修改datalogUpgradeFetchSize为2147483647
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 2147483647);
    system(g_command);

    // 修改upgradeMemActualAndEstimatedPercentage为100
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "upgradeMemActualAndEstimatedPercentage");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 100);
    system(g_command);

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    int recordNum = 0;
#if defined ENV_RTOSV2X
    recordNum = 10000;
#else
    recordNum = 500000;
#endif

    // 构造输入表的数据
    C3Int8T *objIn1 = (C3Int8T *)malloc(sizeof(C3Int8T) * recordNum);
    if (objIn1 == NULL) {
        AW_FUN_Log(LOG_DEBUG, "objIn1 malloc failed !!!");
    }
    memset(objIn1, 0, sizeof(C3Int8T) * recordNum);
    for (int i = 0; i < recordNum; i++) {
        objIn1[i].a = i;
        objIn1[i].b = i;
        objIn1[i].c = i;
        objIn1[i].dtlReservedCount = 1;
        objIn1[i].upgradeVersion = 100;
    }
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "insert datalog table test end.");
    system("gmsysview count inp1");
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));

    // 校验重做的数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);

    // 并发查热补丁视图，看什么时候重做完成
    pthread_t thr_arr[1];
    pthread_create(&thr_arr[0], NULL, ThreadScanPatchView, (void *)soName);
    pthread_join(thr_arr[0], NULL);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 修改upgradeMemActualAndEstimatedPercentage为100
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "upgradeMemActualAndEstimatedPercentage");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 100);
    system(g_command);

    AW_FUN_Log(LOG_STEP, "test end.");
}
