/*****************************************************************************
 Copyright (c) Huawei Technologies Co., Ltd. 2024. All rights reserved.
 Description  : GMDB 505.0.0 迭代一Datalog热补丁支持阻塞非合并式重做-执行测试
 Notes        :
 History      :
 Author       : luyang/l00618033
 Create       : [2024.01.13]
*****************************************************************************/
#include "hotpatchblock.h"
#include "DatalogRun.h"

using namespace std;

class hotpatchblo_002_test : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret = testEnvInit();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        ret = create_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret = close_epoll_thread();
        AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
        GmcDetachAllShmSeg();
        testEnvClean();
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void hotpatchblo_002_test::SetUp()
{
    // 避免受其他用例影响
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建连接
    int ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_CHECK_LOG_BEGIN();
    
}
void hotpatchblo_002_test::TearDown()
{
    AW_CHECK_LOG_END();
    // 断开同步连接
    int ret;
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 恢复默认配置项
    // enableDatalogDmlWhenUpgrading配置项设置为0
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "datalogUpgradeFetchSize");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);

    system("rm -rf /root/_datalog_/");
}

/* ****************************************************************************
 Description  : 001.显示设置block为0，enableDatalogDmlWhenUpgrading为0时，patch.d
 中新增的输入表表与输入表进行join，加载升降级so，查看数据以及热补丁视图，upgradeVersion会变动
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 修改配置项
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp2
    ------------------
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: inp2
    ------------------
    TABLE_NAME: inp1
    ------------------
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 002.显示设置block为0，enableDatalogDmlWhenUpgrading为1时，patch.d
 中新增的输入表表与输入表进行join，加载升降级so，查看数据以及热补丁视图，upgradeVersion会变动
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 修改配置项
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp2
    ------------------
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: inp2
    ------------------
    TABLE_NAME: inp1
    ------------------
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 1", "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 不支持，编译拦截，上架euler
// 2024年2月22日设计变更
/* ****************************************************************************
 Description  : 003.显示设置block为1，enableDatalogDmlWhenUpgrading为0时，
 patch.d中新增的输入表表与输入表进行join，加载升降级so，查看数据以及热补丁视图，upgradeVersion始终为0，编译报错
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable002";
    char inputFile[FILE_PATH] = "./datalogFile/execute";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    int ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.",
      GMERR_SEMANTIC_ERROR);
    // 预期失败
    ret = executeCommand(g_command, "Error: \"%block 1\" not support topo combine, and rule:\"r0\" involve topo "
      "combine mid1 and inp2 are in different topo near line 7.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 下架
/* ****************************************************************************
 Description  : 004.显示设置block为1，enableDatalogDmlWhenUpgrading为1时，
 patch.d中新增的输入表表与输入表进行join，加载升降级so，查看数据以及热补丁视图，upgradeVersion始终为0
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DISABLED_DataLog_062_002_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 修改配置项
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 19, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
  Node[id: 20, name: REDO_RELATED_TABLES]
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 21, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 1", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 查看降级热补丁视图
    system(g_command);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 005.显示设置block为0，enableDatalogDmlWhenUpgrading为0时，
 patch.d中新增的输入表表与中间表进行join，加载升降级so，查看数据以及热补丁视图，upgradeVersion会变动
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 修改配置项
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 0);
    system(g_command);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp2
    ------------------
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: inp2
    ------------------
    TABLE_NAME: inp1
    ------------------
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 006.显示设置block为0，enableDatalogDmlWhenUpgrading为1时，
 patch.d中新增的输入表与中间表进行join，加载升降级so，查看数据以及热补丁视图，upgradeVersion会变动
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 修改配置项
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp2
    ------------------
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: inp2
    ------------------
    TABLE_NAME: inp1
    ------------------
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 1", "BLOCK_MODE: 0", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例下架，block为1模式下，不支持不同topo表进行join
/* ****************************************************************************
 Description  : 007.显示设置block为1，enableDatalogDmlWhenUpgrading为0时，
 patch.d中新增的输入表与中间表进行join，加载升降级so，查看数据以及热补丁视图，upgradeVersion始终为0，编译报错
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable004";
    int ret = 0;
    char inputFile[FILE_PATH] = "./datalogFile/execute";
    char cOutputFile[FILE_PATH] = {0};
    char ruleFile[FILE_PATH] = {0};
    char patchFile[FILE_PATH] = {0};
    char patchCOutputFile[FILE_PATH] = {0};
    char fullOutputFile[FILE_PATH] = {0};

    (void)snprintf(inputFile, FILE_PATH, "%s/%s.d", inputFilePath, soName);
    (void)snprintf(cOutputFile, FILE_PATH, "%s/%s.c", inputFilePath, soName);
    (void)snprintf(ruleFile, FILE_PATH, "%s/%s_rule.d", inputFilePath, soName);
    (void)snprintf(patchFile, FILE_PATH, "%s/%s_patch.d", inputFilePath, soName);
    (void)snprintf(fullOutputFile, FILE_PATH, "%s/%s_ruleV%d.d", outputFilePath, soName, 2);
    (void)snprintf(patchCOutputFile, FILE_PATH, "%s/%s_patch.c", outputFilePath, soName);

    // .d -> .c
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -f %s %s", g_toolPath, inputFile, cOutputFile);
    system(g_command);
    ret = executeCommand(g_command, "Serialize done.");
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    // rule.d + patch.d -> patch.c + full.d
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmprecompiler -u %s %s %s %s", g_toolPath, ruleFile, patchFile,
        patchCOutputFile, fullOutputFile);
    system(g_command);
    memset(g_errorMsg, 0, MAX_CMD_SIZE);
    (void)snprintf(g_errorMsg, MAX_CMD_SIZE, "Fail to verify datalog file for upgrade. Exit with code %d.",
      GMERR_SEMANTIC_ERROR);
    // 预期失败
    ret = executeCommand(g_command, "Error: \"%block 1\" not support topo combine, and rule:\"r1\" involve topo "
      "combine out1 and inp2 are in different topo near line 7.", g_errorMsg);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 用例下架
/* ****************************************************************************
 Description  : 008.显示设置block为1，enableDatalogDmlWhenUpgrading为1时，
 patch.d中新增的输入表表与中间表进行join，加载升降级so，查看数据以及热补丁视图，upgradeVersion始终为0
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DISABLED_DataLog_062_002_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 修改配置项
    memset(g_cfgName, 0, MAX_CMD_SIZE);
    (void)snprintf(g_cfgName, MAX_CMD_SIZE, "enableDatalogDmlWhenUpgrading");
    memset(g_command, 0, MAX_CMD_SIZE);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmadmin -cfgName %s -cfgVal %d", g_toolPath, g_cfgName, 1);
    system(g_command);
    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 19, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: mid1
  Node[id: 20, name: REDO_RELATED_TABLES]
    TABLE_NAME: out1
  Node[id: 21, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 1", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 查看降级后，热补丁视图
    system(g_command);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 下架
/* ****************************************************************************
 Description  : 009.显示设置block为1，两张图合成1张图，查看数据以及热补丁视图
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DISABLED_DataLog_062_002_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out4", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out4 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 19, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp2
    ------------------
    TABLE_NAME: inp3
    ------------------
    TABLE_NAME: inp1
  Node[id: 20, name: REDO_RELATED_TABLES]
    TABLE_NAME: out3
    ------------------
    TABLE_NAME: out4
    ------------------
    TABLE_NAME: out2
  Node[id: 21, name: REDO_OUT_TABLES]
    TABLE_NAME: out3
    ------------------
    TABLE_NAME: out4
    ------------------
    TABLE_NAME: out2
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum] = {{1, upVerVal, 1, 1, 10}, {1, upVerVal, 1, 2, 10}, {1, upVerVal, 2, 2, 10},
        {1, upVerVal, 3, 3, 10}, {1, upVerVal, 4, 4, 10}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    // 对新增的输入表inp4写数据
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out2", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read two complete!!!");
    ret = readRecord(g_conn, g_stmt, "out4", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out4 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn7[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out2 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 010.显示设置block为1，新增2个输入表，升级前是2张图，升级后还是2张图，查看数据
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DISABLED_DataLog_062_002_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable006";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 19, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp2
    ------------------
    TABLE_NAME: inp1
  Node[id: 20, name: REDO_RELATED_TABLES]
    TABLE_NAME: out3
    ------------------
    TABLE_NAME: out2
  Node[id: 21, name: REDO_OUT_TABLES]
    TABLE_NAME: out3
    ------------------
    TABLE_NAME: out2
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn4, 0, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    // 对新增的输入表inp2写数据
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = readRecord(g_conn, g_stmt, "out2", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read two complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 read two complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out2 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 011.显示设置block为1，分两个so新增输入表与输入表join
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DISABLED_DataLog_062_002_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable007";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 对输入表写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    const char *expectHotPatchView = R"(
  Node[id: 19, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
  Node[id: 20, name: REDO_RELATED_TABLES]
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 21, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 2}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 6}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 012.显示设置block为1，分两个so加载新增输入表与中间表join
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DISABLED_DataLog_062_002_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addtable008";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName2[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName2[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName2, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName2, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    sleep(2);
    // 对输入表写数据
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName2));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 19, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: mid1
  Node[id: 20, name: REDO_RELATED_TABLES]
    TABLE_NAME: out1
  Node[id: 21, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "ENABLE_DATALOG_DML_WHEN_UPGRADING: 0", "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName2));
    sleep(2);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 2}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 6}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 013.显示设置block为1，新增普通function，查看数据以及热补丁视图
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "addudf001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
    UDF_NAME: dtl_ext_func_func2
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 2] = {{1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
   
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 2}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 6}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 014.显示设置block为1，修改普通function实现，查看数据以及热补丁视图
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterudf001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
    UDF_NAME: dtl_ext_func_func
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum - 3] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 2, 2, 4}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, recordNum - 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");
    
    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 2}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 6}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 015.显示设置block为1，某张表有多个来源，修改其中一个规则
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 2;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}};
    C3Int8T objIn2[recordNum] = {{1, 0, 2, 2, 1}, {1, 0, 3, 3, 3}};
    C3Int8T objIn3[recordNum] = {{1, 0, 4, 2, 1}, {1, 0, 5, 3, 3}};
    C3Int8T objIn4[6] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 1}, {1, 0, 3, 3, 3}, {1, 0, 4, 2, 1},
        {1, 0, 5, 3, 3}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn2, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn3, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn4, 6, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}};
    C3Int8T objIn6[10] = {{1, upVerVal, 1, 1, 10}, {1, upVerVal, 1, 2, 10}, {1, upVerVal, 2, 2, 1},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 2, 1}, {1, upVerVal, 5, 3, 3}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn6, 6, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    // 校验加载回滚so之后的数据
    C3Int8T objIn7[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}};
    C3Int8T objIn8[6] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 1},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 2, 1}, {1, upVerVal3, 5, 3, 3}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out1", objIn8, 6, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验funcLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_funclogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_funclogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_015.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 016.显示设置block为1，某张表输出到多个表，修改其中两个规则
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule002";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
    ------------------
    RULE_NAME: r3
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
    ------------------
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: out1
    ------------------
    TABLE_NAME: out4
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
    ------------------
    TABLE_NAME: out4
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[recordNum] = {{1, upVerVal, 1, 1, 10}, {1, upVerVal, 1, 2, 10}, {1, upVerVal, 2, 2, 10},
        {1, upVerVal, 3, 3, 10}, {1, upVerVal, 4, 4, 10}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_016.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 017.显示设置block为1，修改两个规则中触发表为相同表的场景
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule003";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
    ------------------
    RULE_NAME: r1
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
    ------------------
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: out1
    ------------------
    TABLE_NAME: out2
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
    ------------------
    TABLE_NAME: out2
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "BLOCK_MODE: 1", "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 1}, {1, upVerVal3, 1, 2, 3}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("rm -rf test.txt");
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_017.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 018.显示设置block为1，修改两个规则中触发表为不同表的场景
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule004";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r1
    ------------------
    RULE_NAME: r0
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
    ------------------
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: out2
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out2
    ------------------
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    C3Int8T objIn4[0] = {};
    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
    
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	  (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_018.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 019.显示设置block为1，修改复杂规则中的某一小块
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule005";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r7
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: mid33
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: out2
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out2
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};

    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	  (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_019.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

// 2024年2月20日变更   block为1时，不支持规则合并
/* ****************************************************************************
 Description  : 020.显示设置block为1，规则合并的场景1
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule006";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);
   
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};

    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");

    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    
    // 校验TbmRunLog.txt文件内容
	  (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_020.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 021.显示设置block为1，规则合并的场景含function
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule007";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r1
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp3
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	  (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_021.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 022.patch.d新增输入表，触发规则合并
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DISABLED_DataLog_062_002_022)
{
        AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule008";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 对新增输入表写数据
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *expectHotPatchView = R"(
  Node[id: 14, name: UPDATE_RULES]
    RULE_NAME: r0
  Node[id: 15, name: DROP_RULES]
  Node[id: 16, name: ADD_UDFS]
  Node[id: 17, name: UPDATE_UDFS]
  Node[id: 18, name: DROP_UDFS]
  Node[id: 19, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
  Node[id: 20, name: REDO_RELATED_TABLES]
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 21, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	(void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
	(void)snprintf(g_command, MAX_CMD_SIZE, "md5sum test.txt");
	system(g_command);
#if defined RUN_INDEPENDENT
	ret = executeCommand(g_command, "bc6f6023be1c191227471296cab0b82c");
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	if (ret != 0) {
		(void)SystemSnprintf("cat test.txt");
	}
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 023.patch.d新增function，触发规则合并
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule009";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
    UDF_NAME: dtl_ext_func_func1
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	  (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_023.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 024.原始.d中，中间表mid1在前面进行join，patch.d新增输入表，触发规则合并
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DISABLED_DataLog_062_002_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule010";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 对新增输入表写数据
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    const char *expectHotPatchView = R"(
  Node[id: 14, name: UPDATE_RULES]
    RULE_NAME: r1
  Node[id: 15, name: DROP_RULES]
  Node[id: 16, name: ADD_UDFS]
  Node[id: 17, name: UPDATE_UDFS]
  Node[id: 18, name: DROP_UDFS]
  Node[id: 19, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
  Node[id: 20, name: REDO_RELATED_TABLES]
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: out1
  Node[id: 21, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	(void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
	(void)snprintf(g_command, MAX_CMD_SIZE, "md5sum test.txt");
	system(g_command);
#if defined RUN_INDEPENDENT
	ret = executeCommand(g_command, "bc6f6023be1c191227471296cab0b82c");
	AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
	if (ret != 0) {
		(void)SystemSnprintf("cat test.txt");
	}
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 025.显示设置block为1，投影规则合并的场景1
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule011";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp4
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r3
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: mid111
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_025.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 026.显示设置block为1，投影规则合并的场景2
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule012";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: inp1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: mid1
    ------------------
    TABLE_NAME: mid11
    ------------------
    TABLE_NAME: mid111
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	  (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_026.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 027.显示设置block为1，投影规则合并的场景3
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_027)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule013";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char patchSoName1[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char rollbackSoName1[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(patchSoName1, "%s/%s_patchV3.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    (void)sprintf(rollbackSoName1, "%s/%s_rollbackV3.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp4
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r1
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: mid1
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: mid11
    ------------------
    TABLE_NAME: mid111
    ------------------
    TABLE_NAME: out1
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: out1
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName1));
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName1));
    sleep(2);
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback inp1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
    (void)SystemSnprintf("rm -rf test.txt");
    (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_027.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 028.显示设备block为1，.d中含namespace和precedence场景
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_028)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alterrule014";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal1 = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};
    char path1[FILE_PATH] = {0}, path2[FILE_PATH] = {0}, flag[128] = {'/', '\0'};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 3}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    ret = writeRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "ns1.inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn1, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "ns1.inp1 read complete!!!");

    system("gmsysview count ns1.inp1");
    system("gmsysview count ns1.inp2");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);

    const char *expectHotPatchView = R"(
  Node[id: 12, name: ADD_TABLES]
    TABLE_NAME: inp4
  Node[id: 13, name: UPDATE_TABLES]
  Node[id: 14, name: DROP_TABLES]
  Node[id: 15, name: ADD_RULES]
  Node[id: 16, name: UPDATE_RULES]
    RULE_NAME: r0
    ------------------
    RULE_NAME: r1
    ------------------
    RULE_NAME: r2
  Node[id: 17, name: DROP_RULES]
  Node[id: 18, name: ADD_UDFS]
    UDF_NAME: dtl_ext_func_func
  Node[id: 19, name: UPDATE_UDFS]
  Node[id: 20, name: DROP_UDFS]
  Node[id: 21, name: REDO_TRIGGER_TABLES]
    TABLE_NAME: ns1.inp1
    ------------------
    TABLE_NAME: ns1.inp1
    ------------------
    TABLE_NAME: ns1.inp3
  Node[id: 22, name: REDO_RELATED_TABLES]
    TABLE_NAME: ns1.mid1
    ------------------
    TABLE_NAME: ns2.out2
    ------------------
    TABLE_NAME: ns1.out1
    ------------------
    TABLE_NAME: ns2.out3
  Node[id: 23, name: REDO_OUT_TABLES]
    TABLE_NAME: ns2.out2
    ------------------
    TABLE_NAME: ns1.out1
    ------------------
    TABLE_NAME: ns2.out3
)";

    // 查看热补丁视图并校验热补丁触发表以及重做表
    ret = CheckHotPatchView(soName, expectHotPatchView);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载升级so，数据会重做，输出表数据会减少
    // 校验inp1和out1的数据
    // out1中的数据会被清掉
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    C3Int8T objIn3[recordNum] = {{1, upVerVal, 1, 1, 1}, {1, upVerVal, 1, 2, 3}, {2, upVerVal, 2, 2, 4},
        {3, upVerVal, 3, 3, 3}, {4, upVerVal, 4, 4, 8}};
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade ns1.inp1 read complete!!!");

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验热补丁视图重做状态
    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmsysview -q %s -s %s", g_toolPath, g_viewName, g_connServer);
    system(g_command);
    ret = executeCommand(g_command, "PATCH_STATE: SUCCESS");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "ns1.inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    // 校验加载回滚so之后的数据
    C3Int8T objIn6[recordNum] = {{1, upVerVal2, 1, 1, 1}, {1, upVerVal2, 1, 2, 3}, {2, upVerVal2, 2, 2, 4},
        {3, upVerVal2, 3, 3, 3}, {4, upVerVal2, 4, 4, 8}};
    
    ret = readRecord(g_conn, g_stmt, "ns1.inp1", objIn6, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback ns1.inp1 read complete!!!");
    
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);

    // 校验TbmRunLog.txt文件内容
	  (void)SystemSnprintf("cat /root/_datalog_/%s > test.txt", g_tbmlogName);
    (void)snprintf(path1, MAX_CMD_SIZE, "%croot/_datalog_/%s", flag[0], g_tbmlogName);
    (void)snprintf(path2, MAX_CMD_SIZE, "./expectFile/%s", "DataLog_062_002_028.txt");
#if defined RUN_INDEPENDENT
    ret = VerifyForSyntaxLog2(path1, path2);
    if (ret != 0) {
      (void)SystemSnprintf("cat test.txt");
    }
#else
    (void)SystemSnprintf("cat test.txt");
#endif
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
 Description  : 029.显示设备block为1，.d中覆盖所有类型的表
**************************************************************************** */
TEST_F(hotpatchblo_002_test, DataLog_062_002_029)
{   
    AW_FUN_Log(LOG_STEP, "test start.");
    (void)snprintf(g_errorCode01, MAX_CMD_SIZE, "GMERR-%d", GMERR_FEATURE_NOT_SUPPORTED);
    AW_ADD_ERR_WHITE_LIST(1, g_errorCode01);
    char inputFilePath[FILE_PATH] = "./datalogFile/execute";
    char outputFilePath[FILE_PATH] = "./datalogFile/execute";
    char soName[FILE_PATH] = "alltype001";
    int ret = 0;
    int32_t upVerVal = -1;
    int32_t upVerVal2 = -1;
    int32_t upVerVal3 = -1;
    int32_t upVerVal4 = -1;
    int32_t upVerVal5 = -1;
    int32_t upVerVal6 = -1;
    int32_t upVerVal7 = -1;
    int32_t upVerVal8 = -1;
    int32_t upVerVal9 = -1;
    int32_t upVerVal10 = -1;
    int32_t upVerVal11 = -1;

    char libName[FILE_PATH] = {0};
    char patchSoName[FILE_PATH] = {0};
    char rollbackPatchCName[FILE_PATH] = {0};
    char rollbackSoName[FILE_PATH] = {0};

    (void)sprintf(libName, "%s/%s.so", outputFilePath, soName);
    (void)sprintf(patchSoName, "%s/%s_patchV2.so", outputFilePath, soName);
    (void)sprintf(rollbackPatchCName, "%s/%s_patch_rollback.c", outputFilePath, soName);
    (void)sprintf(rollbackSoName, "%s/%s_rollbackV2.so", outputFilePath, soName);

    // 切换namespace为public

    
    char *schema = NULL;
    GmcDropVertexLabel(g_stmt, "extern");
    readJanssonFile("./schema_file/external02.gmjson", &schema);
    EXPECT_NE((void *)NULL, schema);

    const char *configJson = "{\"max_record_count\":10000, \"isFastReadUncommitted\":0}";
    ret = GmcCreateVertexLabel(g_stmt, schema, configJson);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(schema);

    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadDatalog(libName));
    // pubsub普通表推送的数据
    C3Int8T objPub[25] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 3}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {1, 0, 4, 4, 8},
        {-1, 0, 1, 1, 2}, {-1, 0, 1, 2, 3}, {-1, 0, 2, 2, 4}, {-1, 0, 3, 3, 6}, {-1, 0, 4, 4, 8}, {1, 0, 1, 1, 2},
        {1, 0, 2, 2, 4}, {1, 0, 4, 4, 8}, {1, 0, 5, 4, 9}, {-1, 0, 1, 1, 2}, {1, 0, 1, 1, 2}, {1, 0, 1, 2, 3},
        {-1, 0, 2, 2, 4}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 6}, {-1, 0, 4, 4, 8}, {1, 0, 4, 4, 8}, {1, 0, 5, 5, 10},
        {-1, 0, 5, 4, 9}, {1, 0, 5, 4, 9}};
    // pubsub资源型表推送的数据
    C3Int8C1Int4T objPub2[14] = {{1, 0, 1, 1, 2, 1}, {1, 0, 1, 2, 2, 1}, {1, 0, 2, 2, 4, 1}, {1, 0, 3, 3, 3, 1},
        {1, 0, 4, 4, 8, 1}};
    // 创建订阅关系
    int chanRingLen = 256;
    GmcConnT *conn_sn_sync = NULL;
    GmcStmtT *stmt_sn_sync = NULL;
    const char *subConnName = "subConnName026";
    const char *subName01 = "subNotifyout1";
    const char *subName02 = "subNotifyrs2";
    testSubConnect(&conn_sn_sync, &stmt_sn_sync, 1, g_epoll_reg_info, subConnName, &chanRingLen);
    // 创建订阅关系, pubsub普通表
    char *sub_info01 = NULL;
    readJanssonFile("./schema_file/subInfo023.json", &sub_info01);
    EXPECT_NE((void *)NULL, sub_info01);
    GmcSubConfigT tmp_sub_info01;
    tmp_sub_info01.subsName = subName01;
    tmp_sub_info01.configJson = sub_info01;
    SnUserDataWithFuncT *userData01 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData01->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData01->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData01->readResFunc = C3Int8RescGet;
    userData01->funcType = 1;
    userData01->objLen = 25;
    userData01->obj = objPub;
    userData01->isResourcePubSub = false;

    ret = GmcSubscribe(g_stmt, &tmp_sub_info01, conn_sn_sync, snCallback, userData01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info01);

    // 创建订阅关系, pubsub资源表
    char *sub_info02 = NULL;
    readJanssonFile("./schema_file/subInfors2.json", &sub_info02);
    EXPECT_NE((void *)NULL, sub_info02);
    GmcSubConfigT tmp_sub_info02;
    tmp_sub_info02.subsName = subName02;
    tmp_sub_info02.configJson = sub_info02;
    SnUserDataWithFuncT *userData02 = (SnUserDataWithFuncT *)malloc(sizeof(SnUserDataWithFuncT));
    userData02->data = (SnUserDataT *)malloc(sizeof(SnUserDataT));
    memset(userData02->data, 0, sizeof(SnUserDataT));
    // struct模式
    userData02->readResFunc = C3Int8C1Int4RescGet;
    userData02->funcType = 1;
    userData02->objLen = 5;
    userData02->obj = objPub2;
    userData02->isResourcePubSub = true;
    
    ret = GmcSubscribe(g_stmt, &tmp_sub_info02, conn_sn_sync, snCallback, userData02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(sub_info02);

    // 插入数据
    int recordNum = 5;
    C3Int8T objIn1[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {2, 0, 2, 2, 4}, {3, 0, 3, 3, 3}, {4, 0, 4, 4, 8}};
    C3Int8T objIn2[recordNum] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3}, {1, 0, 4, 4, 8}};
    C3Int8T objIn3[recordNum] = {{1, 0, 1, 1, 1}, {1, 0, 1, 2, 1}, {1, 0, 2, 2, 1}, {1, 0, 3, 3, 1}, {1, 0, 4, 4, 1}};
    C3Int8C1Int4T objIn4[8] = {{1, 0, 1, 1, 2, 0}, {1, 0, 1, 2, 2, 1}, {1, 0, 2, 2, 4, 2}, {1, 0, 3, 3, 3, 3},
        {1, 0, 4, 4, 8, 4}, {1, 0, 2, 2, 2, 5}, {1, 0, 4, 4, 4, 6}, {1, 0, 8, 8, 8, 7}};
    C3Int8T objTimeout[recordNum] = {{1, 0, 1, 1, -1}, {1, 0, 1, 2, -3}, {2, 0, 2, 6, 4}, {3, 0, 3, 3, 3},
        {4, 0, 4, 4, 8}};
    C3Int8T objTimeout2[recordNum] = {{2, 0, 2, 2, 9}, {2, 0, 2, 4, 7}, {2, 0, 2, 6, 4}, {3, 0, 3, 3, 3},
        {4, 0, 4, 4, 8}};
    // 校验输入表
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp7", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp8", objTimeout, recordNum, C3Int8TimeoutSet, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 等待过期表过期
    sleep(3);
    // 校验输入表
    ret = readRecord(g_conn, g_stmt, "inp1", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "inp8", objTimeout2, recordNum, C3Int8TimeoutGet);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "inp8 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn2, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out4", objIn3, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out4 read complete!!!");
    // 校验out5输出表
    ret = readRecord(g_conn, g_stmt, "out5", objIn4, 8, C3Int8C1Int4Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "out5 read complete!!!");

    // 加载升级so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadUpgradeSo(patchSoName));
    AW_FUN_Log(LOG_DEBUG, "after upgrade scan table");
    sleep(2);
    // 加载升级so，数据会重做，输出表数据会减少
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp1", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal2);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal3);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out4", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal4);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out5", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal5);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal5);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp6", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal6);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal6);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp7", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal7);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal7);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "extern", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 报错
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal8);
    AW_MACRO_EXPECT_EQ_INT(GMERR_FEATURE_NOT_SUPPORTED, ret);
    AW_MACRO_EXPECT_EQ_INT(-1, upVerVal8);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "inp8", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal9);

    C3Int8T objIn5[recordNum] = {{1, upVerVal, 1, 1, 2}, {1, upVerVal, 1, 2, 2}, {1, upVerVal, 2, 2, 4},
        {1, upVerVal, 3, 3, 3}, {1, upVerVal, 4, 4, 8}};
    C3Int8T objIn6[recordNum - 2] = {{1, upVerVal2, 1, 1, 2}, {1, upVerVal2, 2, 2, 4}, {1, upVerVal2, 4, 4, 8}};
    C3Int8T objIn7[recordNum] = {{1, upVerVal3, 1, 1, 2}, {1, upVerVal3, 1, 2, 2}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}};
    C3Int8T objIn8[recordNum] = {{1, upVerVal4, 1, 1, 1}, {1, upVerVal4, 1, 2, 1}, {1, upVerVal4, 2, 2, 1},
        {1, upVerVal4, 3, 3, 1}, {1, upVerVal4, 4, 4, 1}};
    C3Int8T objIn9[recordNum + 3] = {{1, upVerVal5, 1, 1, 2}, {1, upVerVal5, 1, 2, 2}, {1, upVerVal5, 2, 2, 4},
      {1, upVerVal5, 4, 4, 8}, {1, upVerVal4, 2, 2, 2}, {1, upVerVal4, 3, 3, 3}, {1, upVerVal4, 4, 4, 4, },
      {1, upVerVal4, 8, 8, 8}};
    ret = readRecord(g_conn, g_stmt, "inp1", objIn5, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade inp1 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out2", objIn6, recordNum - 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn7, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out4", objIn8, recordNum, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out4 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out5", objIn9, recordNum + 3, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out5 read complete!!!");

    // 插入数据
    // 升级后对输入表插入数据
    ret = writeRecord(g_conn, g_stmt, "tb1", objIn1, recordNum, C3Int8Set, false);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    C3Int8T objIn10[2] = {{1, 100, 5, 5, 5}, {1, 100, 5, 4, 9}};
    C3Int8T objIn11[recordNum] = {{1, upVerVal3, 1, 1, 2}, {1, upVerVal3, 1, 2, 2}, {1, upVerVal3, 2, 2, 4},
        {1, upVerVal3, 3, 3, 3}, {1, upVerVal3, 4, 4, 8}, {1, upVerVal3, 5, 5, 5}, {1, upVerVal3, 5, 4, 9}};
    ret = writeRecord(g_conn, g_stmt, "inp1", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp2", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp3", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp4", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp5", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = writeRecord(g_conn, g_stmt, "inp6", objIn10, 2, C3Int8Set, true);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 校验out3输出表的数据
    ret = readRecord(g_conn, g_stmt, "out3", objIn11, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "upgrade out3 two read complete!!!");
    // 主键读写入的数据
    ret = ReadTablePKScan(g_conn, g_stmt, "out2", 5, 4, 9);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = ReadTablePKScan(g_conn, g_stmt, "out4", 5, 4, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 加载回滚so
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, TestLoadRollbackSo(rollbackSoName));
    AW_FUN_Log(LOG_DEBUG, "after rollback scan table");
    sleep(2);

    // 校验数据
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out2", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal10);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal10);
    ret = testGmcPrepareStmtByLabelName(g_stmt, "out3", GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetUpgradeVersion(g_stmt, &upVerVal11);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, upVerVal11);

    C3Int8T objIn12[recordNum + 2] = {{1, 0, 1, 1, 2}, {1, 0, 1, 2, 2}, {1, 0, 2, 2, 4}, {1, 0, 3, 3, 3},
        {1, 0, 4, 4, 8}, {1, 0, 5, 5, 5}, {1, 0, 5, 4, 9}};
    
    ret = readRecord(g_conn, g_stmt, "out2", objIn12, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out2 read complete!!!");
    ret = readRecord(g_conn, g_stmt, "out3", objIn12, recordNum + 2, C3Int8Get);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "rollback out3 read complete!!!");

    // 取消订阅关系
    ret = GmcUnSubscribe(g_stmt, subName01);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUnSubscribe(g_stmt, subName02);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testGmcDisconnect(conn_sn_sync, stmt_sn_sync);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 释放分配的内存
    free(userData01->data);
    free(userData01);
    free(userData02->data);
    free(userData02);

    // 删除表
    ret = GmcDropVertexLabel(g_stmt, "extern");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 卸载
    ret = TestUninstallDatalog(soName);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    sleep(10);

    // 查看tbm表记录
    (void)SystemSnprintf("cat `find / -name %s |head -1`", g_tbmlogName);
    (void)SystemSnprintf("cat `find / -name %s |head -1`", g_msglogName);

    AW_FUN_Log(LOG_STEP, "test end.");
}
