/*  版权所有 (c) 华为技术有限公司 2021-2022 */
#ifndef DATALOGLOADRUNENHANCE_H
#define DATALOGLOADRUNENHANCE_H

#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/time.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <pthread.h>
#include <semaphore.h>
#include <errno.h>
#include <time.h>
#include <stdarg.h>
#include "t_datacom_lite.h"
#include "share_function.h"
const char *g_schemaJson = R"([ {
        "type" : "record",
        "name" : "%s",
        "fields" : [
            {"name" : "dtlReservedCount", "type" : "int32"},
            {"name" : "upgradeVersion", "type" : "int32"},
            {"name" : "a", "type" : "int32"},
            {"name" : "b", "type" : "int32"}
        ],
        "keys" : [
            {
                "node" : "%s",
                "name" : "0",
                "fields" : ["upgradeVersion", "a", "b"],
                "index" : {"type" : "primary"},
                "constraints" : {"unique" : true}
            }
        ]
    } ])";

/* table struct define */
// SingleInt4St :   int4
// DoubleInt4St :   int4, int4
// ThreeInt4St  :   int4, int4, int4
// StrAndByte8St:   byte8, str

/* callback function define : set/get */
// xxx_set
// xxx_setId
// xxx_setIdSt
// xxx_get
// xxx_getSt
// xxx_getId
// xxx_getIdSt

#pragma pack(1)
struct SingleInt4St {
    int32_t a;
    int32_t dtlReservedCount;
};

struct DoubleInt4St {
    int32_t a;
    int32_t b;
    int32_t dtlReservedCount;
};

struct ThreeInt4St {
    int32_t a;
    int32_t b;
    int32_t c;
    int32_t dtlReservedCount;
};

struct DoubleByte8St {
    uint8_t a[8];
    uint8_t b[8];
    int32_t dtlReservedCount;
};

struct Byte8AndDoubleStrSt {
    uint8_t a[8];
    char *b;
    int bLen;
    char *c;
    int cLen;
    int32_t dtlReservedCount;
};

struct StrAndByte8St {
    uint8_t a[8];
    char *b;
    int bLen;
    int32_t dtlReservedCount;
};

struct C1StrSt {
    char *a;
    int aLen;
    int32_t dtlReservedCount;
};

struct C1Byte128St {
    char a[128];
    int32_t dtlReservedCount;
};

#pragma pack()

#define MAX_CMD_SIZE 1024
char g_command[MAX_CMD_SIZE];

/*
0 : check
1 : debug
*/
int g_usrMode = 0;
char g_libDir[128] = "loadlib";

void SystemSnprintf(const char *format, ...)
{
    va_list p;
    va_start(p, format);
    (void)vsnprintf(g_command, MAX_CMD_SIZE, format, p);
    va_end(p);

    if (g_usrMode == 1) {
        printf("cmd: %s \n", g_command);
    }
    system(g_command);
}

int Debug_executeCommand(char *cmd, const char *v1 = NULL, const char *v2 = NULL, const char *v3 = NULL,
    const char *v4 = NULL, const char *v5 = NULL)
{
    int ret = 0;
    if (g_usrMode == 0) {
        ret = executeCommand(cmd, v1, v2, v3, v4, v5);
    } else if (g_usrMode == 1) {
        printf("cmd: %s \n", cmd);
        system(cmd);
    } else {
        ret = -1;
    }
    return ret;
}
int LoadingLib(const char *fileName, const char *libName, const char *udfName = NULL)
{
    int ret = 0;

    if (udfName) {
        (void)snprintf(g_command, MAX_CMD_SIZE, "sh getLib.sh %s:%s %s \n", fileName, udfName, libName);
    } else {
        (void)snprintf(g_command, MAX_CMD_SIZE, "sh getLib.sh %s %s \n", fileName, libName);
    }
    ret = Debug_executeCommand(g_command, "complie success");
    if (ret != GMERR_OK) {
        return ret;
    }

    (void)snprintf(g_command, MAX_CMD_SIZE, "%s/gmimport -c datalog -f %s/%s \n", g_toolPath, g_libDir, libName);
    ret = Debug_executeCommand(g_command, "Command type: import_datalog, Import datalog file", "successfully");
    if (ret != GMERR_OK) {
        return ret;
    }

    SystemSnprintf("sh getLib.sh %s %s clean \n", fileName, libName);

    return ret;
}

int SingleInt4_setId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    SingleInt4St setObj = {};
    setObj.a = (int32_t)value;
    setObj.dtlReservedCount = count;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &setObj.a, sizeof(setObj.a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4_set] a: %d, ret = %d.", setObj.a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4_set] count: %d, ret = %d.", setObj.dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

// 批量写 byte1&&cisint
int BatchInsertByteOne(GmcConnT *conn, GmcStmtT *stmt, char *labelName, int32_t count[][7], int dataNum,
    int result = GMERR_OK, bool A1IsStr = false)
{
    int ret = 0;
    // insert
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchOptionT batchOption;
    ret = GmcBatchOptionInit(&batchOption);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    // prepare
    ret = GmcBatchPrepare(conn, &batchOption, &batch);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcBatchBindStmt(batch, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int stringLen = 10;
    // string
    uint8_t *buf = (uint8_t *)malloc(stringLen + 1);
    if (buf == NULL) {
        return -1;
    }
    memset(buf, stringLen + 1, 0);

    for (int i = 0; i < dataNum; i++) {
        int8_t value1 = count[i][0];
        int16_t value2 = count[i][1];
        int32_t value3 = count[i][2];
        int8_t value4 = count[i][3];
        uint8_t value5[1] = {0};
        uint8_t value6[1] = {0};
        uint8_t value7[1] = {0};
        uint8_t value8[128] = {0};
        uint8_t value9[256] = {0};

        (void)snprintf((char *)buf, stringLen + 1, "b%08d", value3);
        // set value
        // a
        ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b
        ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c
        ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d
        ret = GmcSetVertexProperty(stmt, "d", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f:byte2
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte2
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a1
        if (A1IsStr) {
            ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "a1", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        // b1
        ret = GmcSetVertexProperty(stmt, "b1", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c1
        ret = GmcSetVertexProperty(stmt, "c1", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d1
        ret = GmcSetVertexProperty(stmt, "d1", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e1:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e1", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f1:byte1
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f1", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g1:byte1
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g1", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a2
        ret = GmcSetVertexProperty(stmt, "a2", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b2
        ret = GmcSetVertexProperty(stmt, "b2", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c2
        ret = GmcSetVertexProperty(stmt, "c2", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d2
        ret = GmcSetVertexProperty(stmt, "d2", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e2:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e2", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f2:byte1
        value6[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f2", GMC_DATATYPE_FIXED, value6, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g:byte1
        value7[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g2", GMC_DATATYPE_FIXED, value7, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a3
        ret = GmcSetVertexProperty(stmt, "a3", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b3
        ret = GmcSetVertexProperty(stmt, "b3", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c3
        ret = GmcSetVertexProperty(stmt, "c3", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d3
        ret = GmcSetVertexProperty(stmt, "d3", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e3:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e3", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f3:byte1
        value6[0] = count[i][5];
        if (A1IsStr) {
            ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_FIXED, value8, 128);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "f3", GMC_DATATYPE_FIXED, value6, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        //  g3:byte1
        value7[0] = count[i][6];
        if (A1IsStr) {
            ret = GmcSetVertexProperty(stmt, "g3", GMC_DATATYPE_FIXED, value9, 256);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        } else {
            ret = GmcSetVertexProperty(stmt, "g3", GMC_DATATYPE_FIXED, value7, 1);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }

        // a4
        ret = GmcSetVertexProperty(stmt, "a4", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b4
        ret = GmcSetVertexProperty(stmt, "b4", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c4
        ret = GmcSetVertexProperty(stmt, "c4", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d4
        ret = GmcSetVertexProperty(stmt, "d4", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e4:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e4", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f4:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f4", GMC_DATATYPE_FIXED, value8, 128);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g4:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g4", GMC_DATATYPE_FIXED, value9, 256);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a5
        ret = GmcSetVertexProperty(stmt, "a5", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b5
        ret = GmcSetVertexProperty(stmt, "b5", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c5
        ret = GmcSetVertexProperty(stmt, "c5", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d5
        ret = GmcSetVertexProperty(stmt, "d5", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e5:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e5", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f5:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f5", GMC_DATATYPE_FIXED, value6, 128);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g5:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g5", GMC_DATATYPE_FIXED, value7, 256);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a6
        ret = GmcSetVertexProperty(stmt, "a6", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b6
        ret = GmcSetVertexProperty(stmt, "b6", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c6
        ret = GmcSetVertexProperty(stmt, "c6", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d6
        ret = GmcSetVertexProperty(stmt, "d6", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e6:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e6", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f6:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f6", GMC_DATATYPE_FIXED, value6, 128);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g6:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g6", GMC_DATATYPE_FIXED, value7, 256);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a7
        ret = GmcSetVertexProperty(stmt, "a7", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b7
        ret = GmcSetVertexProperty(stmt, "b7", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c7
        ret = GmcSetVertexProperty(stmt, "c7", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d7
        ret = GmcSetVertexProperty(stmt, "d7", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e7:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e7", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f7:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f7", GMC_DATATYPE_FIXED, value6, 128);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g7:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g7", GMC_DATATYPE_FIXED, value7, 256);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a8
        ret = GmcSetVertexProperty(stmt, "a8", GMC_DATATYPE_INT8, &value1, sizeof(int8_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // b8
        ret = GmcSetVertexProperty(stmt, "b8", GMC_DATATYPE_INT16, &value2, sizeof(int16_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // c8
        ret = GmcSetVertexProperty(stmt, "c8", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // d8
        ret = GmcSetVertexProperty(stmt, "d8", GMC_DATATYPE_INT64, &value4, sizeof(int64_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  e8:byte1
        value5[0] = count[i][4];
        ret = GmcSetVertexProperty(stmt, "e8", GMC_DATATYPE_FIXED, value5, 1);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // f8:byte128
        value8[0] = count[i][5];
        ret = GmcSetVertexProperty(stmt, "f8", GMC_DATATYPE_FIXED, value6, 128);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        //  g8:byte256
        value9[0] = count[i][6];
        ret = GmcSetVertexProperty(stmt, "g8", GMC_DATATYPE_FIXED, value7, 256);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        // a9
        ret = GmcSetVertexProperty(stmt, "a9", GMC_DATATYPE_STRING, buf, (strlen((char *)buf)));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcSetVertexProperty(stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &value3, sizeof(int32_t));
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

        ret = GmcBatchAddDML(batch, stmt);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    }

    ret = GmcBatchExecute(batch, &batchRet);
    if (ret == GMERR_OUT_OF_MEMORY) {
        result = GMERR_OUT_OF_MEMORY;
    }
    AW_MACRO_EXPECT_EQ_INT(result, ret);
    if (ret && ret != GMERR_OUT_OF_MEMORY) {
        testGmcGetLastError();
    }
    free(buf);
    GmcBatchUnbindStmt(batch, stmt);
    GmcBatchDestroy(batch);
    return result;
}

int TestViewData(char *cmd, char **result)
{
    int ret = 0;
    FILE *fd = popen(cmd, "r");
    int size = 102400;
    *result = (char *)malloc(sizeof(char) * size);
    memset(*result, 0, size);

    char buf[1024] = {0};
    while (fgets(buf, sizeof(buf), fd) != NULL) {
        strcat((char *)*result, buf);
    }

    ret = pclose(fd);
    return ret;
}

// 校验数据
int ScanTableData(const char *expectOutput, char *tableName, const char *tableNameSpace = "public")
{
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmsysview record %s 10 -ns %s > test.txt", g_toolPath, tableName, tableNameSpace);
    printf("%s\n", g_command);
    int ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    memset(g_command, 0, sizeof(g_command));
    char *actualResult = NULL;
    (void)snprintf(g_command, MAX_CMD_SIZE, "cat test.txt");
    AW_FUN_Log(LOG_STEP, "%s", g_command);
    ret = TestViewData(g_command, &actualResult);
    printf("%s\n", actualResult);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 预期值比实际值多一个字符
    const char *result = strstr(actualResult, expectOutput);
    if (result != NULL) {
        EXPECT_STRNE(NULL, result);
        free(actualResult);
    } else {
        free(actualResult);
        AW_FUN_Log(LOG_STEP, "There is no data what you need!");
        return -1;
    }
    return ret;
}
int SingleInt4_set(GmcStmtT *stmt, void *t)
{
    SingleInt4St *obj = (SingleInt4St *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4_set] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4_set] count: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int DoubleInt4_setId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    DoubleInt4St setObj = {};
    setObj.a = (int32_t)value;
    setObj.b = (int32_t)value;
    setObj.dtlReservedCount = count;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &setObj.a, sizeof(setObj.a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_set] a: %d, ret = %d.", setObj.a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &setObj.b, sizeof(setObj.b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_set] b: %d, ret = %d.", setObj.b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_set] count: %d, ret = %d.", setObj.dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int DoubleInt4_set(GmcStmtT *stmt, void *t)
{
    DoubleInt4St *obj = (DoubleInt4St *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_set] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_set] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_set] count: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int ThreeInt4_setId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    ThreeInt4St setObj = {};
    setObj.a = (int32_t)value;
    setObj.b = (int32_t)value;
    setObj.c = (int32_t)value;
    setObj.dtlReservedCount = count;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &setObj.a, sizeof(setObj.a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_setId] a: %d, ret = %d.", setObj.a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &setObj.b, sizeof(setObj.b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_setId] b: %d, ret = %d.", setObj.b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &setObj.c, sizeof(setObj.c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_setId] c: %d, ret = %d.", setObj.c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_setId] count: %d, ret = %d.", setObj.dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int ThreeInt4_set(GmcStmtT *stmt, void *t)
{
    ThreeInt4St *obj = (ThreeInt4St *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_INT32, &obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_set] a: %d, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_INT32, &obj->b, sizeof(obj->b));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_set] b: %d, ret = %d.", obj->b, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_INT32, &obj->c, sizeof(obj->c));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_set] c: %d, ret = %d.", obj->c, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_set] count: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int StrAndByte8_setId(GmcStmtT *stmt, int64_t value, int32_t count)
{
    StrAndByte8St setObj = {};
    memset_s(setObj.a, sizeof(setObj.a), value, sizeof(setObj.a));
    char strTemp[128] = {};
    (void)snprintf(strTemp, sizeof(strTemp), "str_%lld", value);
    setObj.b = strTemp;
    setObj.dtlReservedCount = count;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_FIXED, &setObj.a, sizeof(setObj.a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_STRING, &setObj.b, strlen(setObj.b));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &setObj.dtlReservedCount, sizeof(setObj.dtlReservedCount));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

int DoubleByte8_set(GmcStmtT *stmt, void *t)
{
    DoubleByte8St *obj = (DoubleByte8St *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_FIXED, obj->a, sizeof(obj->a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_FIXED, obj->b, sizeof(obj->b));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

int Byte8AndDoubleStr_set(GmcStmtT *stmt, void *t)
{
    Byte8AndDoubleStrSt *obj = (Byte8AndDoubleStrSt *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_FIXED, obj->a, sizeof(obj->a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_STRING, obj->b, obj->bLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "c", GMC_DATATYPE_STRING, obj->c, obj->cLen);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

int StrAndByte8_set(GmcStmtT *stmt, void *t)
{
    StrAndByte8St *obj = (StrAndByte8St *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_FIXED, obj->a, sizeof(obj->a));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(stmt, "b", GMC_DATATYPE_STRING, obj->b, obj->bLen - 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

int C1Str_set(GmcStmtT *stmt, void *t)
{
    C1StrSt *obj = (C1StrSt *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_STRING, obj->a, obj->aLen - 1);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Str_set] a: %s, len: %d, ret = %d.", obj->a, obj->aLen, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Str_set] count: %d, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int C1Byte128_set(GmcStmtT *stmt, void *t)
{
    C1Byte128St *obj = (C1Byte128St *)t;
    int ret = 0;
    ret = GmcSetVertexProperty(stmt, "a", GMC_DATATYPE_FIXED, obj->a, sizeof(obj->a));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Byte128_set] a: %X, ret = %d.", obj->a, ret);
        return ret;
    }
    ret = GmcSetVertexProperty(
        stmt, "dtlReservedCount", GMC_DATATYPE_INT32, &obj->dtlReservedCount, sizeof(obj->dtlReservedCount));
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[C1Byte128_set] count: %X, ret = %d.", obj->dtlReservedCount, ret);
        return ret;
    }
    return ret;
}

int SingleInt4_get(GmcStmtT *stmt, void *t, int len)
{
    SingleInt4St *obj = (SingleInt4St *)t;
    SingleInt4St getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4_get] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4_get] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    ret = -1;
    for (int i = 0; i < len; i++) {
        if ((getObj.a == obj[i].a) && (getObj.dtlReservedCount == obj[i].dtlReservedCount)) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4_get] a: %d, count: %d.", getObj.a, getObj.dtlReservedCount);
    }

    return ret;
}

int SingleInt4_getId(GmcStmtT *stmt, int startid, int endid, int32_t count)
{
    SingleInt4St getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4_get] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4_get] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    ret = -1;
    for (int i = startid; i < endid; i++) {
        if ((getObj.a == i) && (getObj.dtlReservedCount == count)) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        AW_FUN_Log(LOG_ERROR, "[SingleInt4_getId] a: %d, count: %d.", getObj.a, getObj.dtlReservedCount);
    }

    return ret;
}

int DoubleInt4_get(GmcStmtT *stmt, void *t, int len)
{
    DoubleInt4St *obj = (DoubleInt4St *)t;
    DoubleInt4St getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_get] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_get] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_get] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    ret = -1;
    for (int i = 0; i < len; i++) {
        if ((getObj.a == obj[i].a) && (getObj.b == obj[i].b) && (getObj.dtlReservedCount == obj[i].dtlReservedCount)) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_get] a: %d, b: %d, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }

    return ret;
}

int DoubleInt4_getId(GmcStmtT *stmt, int startid, int endid, int32_t count)
{
    DoubleInt4St getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_getId] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    ret = -1;
    for (int i = startid; i < endid; i++) {
        if ((getObj.a == i) && (getObj.b == i) && (getObj.dtlReservedCount == count)) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        AW_FUN_Log(
            LOG_ERROR, "[DoubleInt4_getId] a: %d, b: %d, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }

    return ret;
}

/*  checkId : a-int4
    default : b-int4     */
int DoubleInt4_checkId(GmcStmtT *stmt, void *checkId, void *defaultValue, int32_t count)
{
    DoubleInt4St expertObj = {.a = *(int32_t *)checkId, .b = (*(int32_t *)defaultValue), .dtlReservedCount = count};
    DoubleInt4St getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_get] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_get] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[DoubleInt4_get] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    ret = -1;
    if ((getObj.a == expertObj.a) && (getObj.b == expertObj.b) &&
        (getObj.dtlReservedCount == expertObj.dtlReservedCount)) {
        ret = 0;
    }

    if (ret == -1) {
        AW_FUN_Log(
            LOG_ERROR, "[DoubleInt4_checkId] a: %d, b: %d, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }

    return ret;
}

int ThreeInt4_get(GmcStmtT *stmt, void *t, int len)
{
    ThreeInt4St *obj = (ThreeInt4St *)t;
    ThreeInt4St getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_get] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_get] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_get] get 'c' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_get] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    ret = -1;
    for (int i = 0; i < len; i++) {
        if ((getObj.a == obj[i].a) && (getObj.b == obj[i].b) && (getObj.c == obj[i].c) &&
            (getObj.dtlReservedCount == obj[i].dtlReservedCount)) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_get] a: %d, b: %d, c: %d, count: %d.", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount);
    }

    return ret;
}

int ThreeInt4_getId(GmcStmtT *stmt, int startid, int endid, int32_t count)
{
    ThreeInt4St getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_getId] get 'a' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_getId] get 'b' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(stmt, "c", &getObj.c, sizeof(getObj.c), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_getId] get 'c' fail, ret = %d.", ret);
        return ret;
    }

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    if (ret != GMERR_OK) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_getId] get 'dtlReservedCount' fail, ret = %d.", ret);
        return ret;
    }

    ret = -1;
    for (int i = startid; i < endid; i++) {
        if ((getObj.a == i) && (getObj.b == i) && (getObj.c == i) && (getObj.dtlReservedCount == count)) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        AW_FUN_Log(LOG_ERROR, "[ThreeInt4_getId] a: %d, b: %d, c: %d, count: %d.", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount);
    }

    return ret;
}

int StrAndByte8_get(GmcStmtT *stmt, void *t, int len)
{
    StrAndByte8St *obj = (StrAndByte8St *)t;
    StrAndByte8St getObj = {};
    char strTemp[128] = "str_test_0";
    getObj.b = strTemp;
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", getObj.a, sizeof(getObj.a), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, "b", getObj.b, strlen(strTemp) + 1, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = -1;
    for (int i = 0; i < len; i++) {
        if ((arrycmp(getObj.a, obj[i].a, sizeof(getObj.a)) == 0) && (strcmp(getObj.b, obj[i].b) == 0) &&
            (getObj.dtlReservedCount == obj[i].dtlReservedCount)) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        AW_FUN_Log(
            LOG_ERROR, "[StrAndByte8_get] a: %X, b: %s, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }

    return ret;
}

int StrAndByte8_getId(GmcStmtT *stmt, int startid, int endid, int32_t count)
{
    StrAndByte8St getObj = {};
    char strTemp[128];
    getObj.b = strTemp;

    StrAndByte8St expectObj = {};
    char strExpect[128] = {};
    expectObj.b = strExpect;

    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", &getObj.a, sizeof(getObj.a), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, "b", &getObj.b, sizeof(getObj.b), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = -1;
    for (int i = startid; i < endid; i++) {
        memset_s(expectObj.a, sizeof(expectObj.a), i, sizeof(expectObj.a));
        (void)snprintf(expectObj.b, sizeof(expectObj.b), "str_%d", i);
        if ((arrycmp(expectObj.a, getObj.a, sizeof(getObj.a)) == 0) && (strcmp(expectObj.b, getObj.b) == 0) &&
            (getObj.dtlReservedCount == count)) {
            ret = 0;
            break;
        }
    }

    if (ret == -1) {
        AW_FUN_Log(
            LOG_ERROR, "[StrAndByte8_getId] a: %X, b: %s, count: %d.", getObj.a, getObj.b, getObj.dtlReservedCount);
    }

    return ret;
}

int DoubleByte8_get(GmcStmtT *stmt, void *t, int len)
{
    DoubleByte8St *obj = (DoubleByte8St *)t;
    DoubleByte8St getObj = {};
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", getObj.a, sizeof(getObj.a), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, "b", getObj.b, sizeof(getObj.b), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = -1;
    for (int i = 0; i < len; i++) {
        if ((arrycmp(getObj.a, obj[i].a, sizeof(getObj.a)) == 0) &&
            (arrycmp(getObj.b, obj[i].b, sizeof(getObj.b)) == 0) &&
            (getObj.dtlReservedCount == obj[i].dtlReservedCount)) {
            ret = 0;
            break;
        }
    }

    if ((ret == -1) && (g_usrMode == 1)) {
        printf("[DoubleByte8St_get] fetch value, a: %x, b: %x, count: %d. \n", getObj.a, getObj.b,
            getObj.dtlReservedCount);
    }

    return ret;
}

int Byte8AndDoubleStr_get(GmcStmtT *stmt, void *t, int len)
{
    Byte8AndDoubleStrSt *obj = (Byte8AndDoubleStrSt *)t;
    Byte8AndDoubleStrSt getObj = {};
    getObj.bLen = obj->bLen;
    getObj.b = (char *)malloc(getObj.bLen);
    getObj.cLen = obj->cLen;
    getObj.c = (char *)malloc(getObj.cLen);
    int ret = 0;
    bool isNull;

    ret = GmcGetVertexPropertyByName(stmt, "a", getObj.a, sizeof(getObj.a), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, "b", getObj.b, getObj.bLen, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(stmt, "c", getObj.c, getObj.cLen, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = -1;
    for (int i = 0; i < len; i++) {
        if ((arrycmp(getObj.a, obj[i].a, sizeof(getObj.a)) == 0) && (strcmp(getObj.b, obj[i].b) == 0) &&
            (strcmp(getObj.c, obj[i].c) == 0) && (getObj.dtlReservedCount == obj[i].dtlReservedCount)) {
            ret = 0;
            break;
        }
    }

    if ((ret == -1) && (g_usrMode == 1)) {
        printf("[DoubleByte8St_get] fetch value, a: %x, b: %s, c: %s, count: %d. \n", getObj.a, getObj.b, getObj.c,
            getObj.dtlReservedCount);
    }
    free(getObj.b);
    free(getObj.c);

    return ret;
}

int C1Str_get(GmcStmtT *stmt, void *t, int len)
{
    C1StrSt *obj = (C1StrSt *)t;
    C1StrSt getObj = {};
    getObj.aLen = obj->aLen;
    getObj.a = (char *)malloc(getObj.aLen);
    int ret = 0;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "a", getObj.a, getObj.aLen, &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = -1;
    for (int i = 0; i < len; i++) {
        if ((strcmp(getObj.a, obj[i].a) == 0) && (getObj.dtlReservedCount == obj[i].dtlReservedCount)) {
            ret = 0;
            break;
        }
    }

    if ((ret == -1) && (g_usrMode == 1)) {
        printf("[C1Str_get] fetch value, a: %s, count: %d. \n", getObj.a, getObj.dtlReservedCount);
    }
    free(getObj.a);

    return ret;
}

int C1Byte128_get(GmcStmtT *stmt, void *t, int len)
{
    C1Byte128St *obj = (C1Byte128St *)t;
    C1Byte128St getObj = {};
    int ret = 0;
    bool isNull;
    ret = GmcGetVertexPropertyByName(stmt, "a", getObj.a, sizeof(getObj.a), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcGetVertexPropertyByName(
        stmt, "dtlReservedCount", &getObj.dtlReservedCount, sizeof(getObj.dtlReservedCount), &isNull);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = -1;
    for (int i = 0; i < len; i++) {
        if ((arrycmp(getObj.a, obj[i].a, sizeof(getObj.a)) == 0) &&
            (getObj.dtlReservedCount == obj[i].dtlReservedCount)) {
            ret = 0;
            break;
        }
    }

    if ((ret == -1) && (g_usrMode == 1)) {
        printf("[C1Str_get] fetch value, a: %X, count: %d. \n", getObj.a, getObj.dtlReservedCount);
    }

    return ret;
}

typedef int (*FuncWrite)(GmcStmtT *stmt, void *t);

template <typename StructObjT>
int writeRecord(GmcConnT *conn, GmcStmtT *stmt, char *labelName, StructObjT *obj, int objLen, FuncWrite func,
    bool isBatch = true, bool isStruct = false, bool isFlowCtr = true)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }
    char schemaJson[1024] = {0};
    (void)sprintf(schemaJson, g_schemaJson, labelName, labelName);
    ret = TestGetVertexLabelFromSchema(schemaJson, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    TestLabelInfoT labelInfo = {labelName, 0, g_testNameSpace};

    for (int i = 0; i < objLen; i++) {
        ret = (isStruct) ? testStructSetVertexWithBuf(stmt, (obj + i), &labelInfo) : func(stmt, (void *)(obj + i));
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecord] GmcBatchAddDML fail, set tRet, i: %d, objLen: %d, ret = %d.", i,
                    objLen, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = isFlowCtr ? GmcExecute(stmt) : GmcExecute(stmt);
            if (ret) {
                AW_FUN_Log(LOG_INFO, "[writeRecord] GmcExecute fail, ret = %d.", ret);
                break;
            }
        }
    }
    if (isBatch) {
        ret = isFlowCtr ? GmcBatchExecute(batch, &batchRet) : GmcBatchExecute(batch, &batchRet);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "[writeRecord] GmcBatchExecute fail, ret = %d.", ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }
    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

typedef int (*FuncWriteId)(GmcStmtT *stmt, int64_t value, int32_t count);

int writeRecordId(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int64_t startid, int64_t endid, int32_t count,
    FuncWriteId func, bool isBatch = true, bool isStruct = false, bool isFlowCtr = true)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_INSERT);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    GmcBatchOptionT batchOption;
    GmcBatchT *batch;
    GmcBatchRetT batchRet;
    if (isBatch) {
        ret = GmcBatchOptionInit(&batchOption);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        ret = GmcBatchPrepare(conn, &batchOption, &batch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isStruct) {
            ret = GmcBatchBindStmt(batch, stmt);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
    }

    for (int i = startid; i < endid; i++) {
        ret = func(stmt, i, count);
        if (ret) {
            AW_FUN_Log(LOG_ERROR, "[writeRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        if (isBatch) {
            ret = GmcBatchAddDML(batch, stmt);
            if (ret != GMERR_OK) {
                AW_FUN_Log(LOG_ERROR, "[writeRecordId] GmcBatchAddDML fail, set tRet, i: %d, ret = %d.", i, ret);
                tRet = ret;
                break;
            }
        } else {
            ret = isFlowCtr ? GmcExecute(stmt) : GmcExecute(stmt);
            if (ret) {
                AW_FUN_Log(LOG_INFO, "[writeRecordId] GmcExecute fail, ret = %d.", ret);
                break;
            }
        }
    }

    if (isBatch) {
        ret = isFlowCtr ? GmcBatchExecute(batch, &batchRet) : GmcBatchExecute(batch, &batchRet);
        if (ret) {
            AW_FUN_Log(LOG_INFO, "[writeRecord] GmcBatchExecute fail, ret = %d.", ret);
        }
        if (isStruct) {
            GmcBatchUnbindStmt(batch, stmt);
        }
        GmcBatchDestroy(batch);
    }

    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[writeRecordId] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

typedef int (*FuncRead)(GmcStmtT *stmt, void *t, int len);

template <typename StructObjT>
int readRecord(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, StructObjT *obj, int objLen, FuncRead func,
    bool checkRecord = true, int *outCnt = NULL)
{
    int ret = 0;
    int tRet = GMERR_OK;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = func(stmt, (void *)obj, objLen);
        if ((checkRecord) && (ret)) {
            AW_FUN_Log(LOG_ERROR, "[readRecord] func fail, set tRet, ret = %d.", ret);
            tRet = ret;
        }
        cnt++;
    }
    if (outCnt) {
        *outCnt = cnt;
    }
    if ((checkRecord) && (objLen != cnt)) {
        tRet = (tRet == GMERR_OK) ? -1 : tRet;
        AW_FUN_Log(LOG_ERROR, "[readRecord]  set tRet, cnt = %d, objLen = %d, tRet = %d.", cnt, objLen, tRet);
    } else if (g_usrMode == 1) {
        AW_FUN_Log(LOG_DEBUG, "[readRecord] fetch %s finish, cnt: %d, objLen: %d.", labelName, cnt, objLen);
    }

    if (tRet) {
        AW_FUN_Log(LOG_INFO, "[readRecord] labelName : %s, return tRet.", labelName);
        return tRet;
    }
    return ret;
}

typedef int (*FuncReadId)(GmcStmtT *stmt, int startid, int endid, int32_t count);

int readRecordId(GmcConnT *conn, GmcStmtT *stmt, const char *labelName, int startid, int endid, int32_t count,
    FuncReadId func, bool checkRecord = true, int *outCnt = NULL)
{
    int ret = 0;
    ret = testGmcPrepareStmtByLabelName(stmt, labelName, GMC_OPERATION_SCAN);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = GmcExecute(stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    bool isFinish;
    int cnt = 0;
    while (true) {
        ret = GmcFetch(stmt, &isFinish);  // 获取扫描的顶点
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (isFinish) {
            break;
        }
        ret = func(stmt, startid, endid, count);
        if (checkRecord) {
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        }
        cnt++;
    }
    if (checkRecord) {
        EXPECT_EQ((endid - startid), cnt);
    } else {
        if (outCnt) {
            *outCnt = cnt;
        }
        if (g_usrMode == 1) {
            printf("[readRecord] fetch finish, cnt: %d. \n", cnt);
        }
    }

    return ret;
}

#define MAX_NAME_LENGTH 512

typedef int (*FuncCheck)(GmcStmtT *stmt, void *id, void *df, int32_t c);

typedef struct TagSnUserDataWithFuncT {
    SnUserDataT *data;
    int tableType;  // 0:out 1:resource
    int funcType;   // 0:id 1:struct
    char *nsName;
    union {
        struct {
            FuncCheck checkIdFunc;
            FuncReadId readIdFunc;
            FuncWriteId writeIdFunc;
            int startid;
            int endid;
            int32_t count;
        };
        struct {
            FuncRead readFunc;
            FuncWrite writeFunc;
            int objLen;
            union {
                SingleInt4St *objInt1;
                DoubleInt4St *objInt2;
                ThreeInt4St *objInt3;
            };
        };
    };
} SnUserDataWithFuncT;
int snCallbackCheck(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    SnUserDataWithFuncT *userDefinedData = (SnUserDataWithFuncT *)userData;
    SnUserDataT *data = userDefinedData->data;
    char *nsName = userDefinedData->nsName;
    int tableType = userDefinedData->tableType;
    int funcType = userDefinedData->funcType;
    char labelName[MAX_NAME_LENGTH] = {0};
    unsigned int labelNameLen;

    AW_MACRO_EXPECT_EQ_INT(1, info->labelCount);

    bool eof = false;
    while (!eof) {
        int ret = GmcFetch(subStmt, &eof);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (eof == true) {
            break;
        }

        for (uint16_t i = 0; i < info->labelCount; i++) {
            memset(labelName, 0, sizeof(labelName));
            labelNameLen = MAX_NAME_LENGTH;
            ret = GmcSubGetLabelName(subStmt, i, labelName, &labelNameLen);
            AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
            AW_MACRO_EXPECT_EQ_INT(strlen(labelName), labelNameLen);

            switch (info->eventType) {
                case GMC_SUB_EVENT_INSERT: {
                    ret = GmcSubSetFetchMode(subStmt, GMC_SUB_FETCH_NEW);
                    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    if (tableType == 0) {  // out table
                        ret = userDefinedData->readIdFunc(
                            subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count);
                        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
                    } else if (tableType == 1) {  // pubsubresource
                        if (funcType == 0) {
                            ret = userDefinedData->readIdFunc(
                                subStmt, userDefinedData->startid, userDefinedData->endid, userDefinedData->count);
                            AW_MACRO_EXPECT_EQ_INT(-1, ret);
                        } else if (funcType == 1) {
                        }
                    }
                    break;
                }
                default: {
                    AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                    assert(0);
                }
            }
        }
        switch (info->eventType) {
            case GMC_SUB_EVENT_INSERT: {
                data->insertNum++;
                break;
            }
            default: {
                AW_FUN_Log(LOG_STEP, "default: invalid eventType");
                assert(0);
            }
        }
    }

    return GMERR_OK;
}

void snCallback(GmcStmtT *subStmt, const GmcSubMsgInfoT *info, void *userData)
{
    // 用户消息创建
    GmcRespT *response;
    int ret = GmcCreateResp(subStmt, &response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetRespMode(response, GMC_RESP_SEND_FAILED_INDEX);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 用户处理错误信息回填
    uint16_t failedDataNum = 0;
    uint16_t failedIndexes[GMC_SUB_BATCH_MAX] = {0};
    ret = GmcSetSubFailedIndex(response, failedDataNum, failedIndexes);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 用户信息发送
    ret = GmcSendResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 用户信息销毁
    ret = GmcDestroyResp(subStmt, response);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = snCallbackCheck(subStmt, info, userData);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

typedef struct tagWriteTableStruct {
    char *nsName;
    char *labelName;
    FuncWriteId setfunc;
    int64_t startid;
    int64_t endid;
    int32_t count;
    bool isBatch;
    uint64_t cycleTimes;
} WriteTableStruct;

void *WriteIdTable(void *arg)
{
    char *nsName = ((WriteTableStruct *)arg)->nsName;
    char *labelName = ((WriteTableStruct *)arg)->labelName;
    int64_t startid = ((WriteTableStruct *)arg)->startid;
    int64_t endid = ((WriteTableStruct *)arg)->endid;
    int32_t count = ((WriteTableStruct *)arg)->count;
    FuncWriteId func = ((WriteTableStruct *)arg)->setfunc;
    bool isBatch = ((WriteTableStruct *)arg)->isBatch;
    uint64_t cycleNum = ((WriteTableStruct *)arg)->cycleTimes;

    int ret = 0;
    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);    while (cycleNum--) {
        ret = writeRecordId(conn, stmt, labelName, startid, endid, count, func, isBatch);
        AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
        if (ret) {
            break;
        }
    }

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    return NULL;
}

#endif
