/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: RbsoTest.cpp
 * Description: datalogDFX增强
 * Author: <PERSON><PERSON><PERSON><PERSON> ywx1252574
 * Create: 2024-08-08
 */
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "t_rd_sn.h"
#include "DatalogRunEnhance.h"

#define MAX_CMD_SIZE 1024
char debug[] = "gmsysview -q V\\$PTL_DATALOG_SO_INFO";
char view[] = "V\\$PTL_DATALOG_SO_INFO";

class RbsoTest : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();
protected:
    static void SetUpTestCase()
    {
        
    }
    static void TearDownTestCase()
    {
        
    }
};

void RbsoTest::SetUp()
{
    int ret = 0;
    system("sh $TEST_HOME/tools/start.sh");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());
    ret = testEnvInit();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("mkdir /root/_datalog_/");
    system("touch /root/_datalog_/TbmRunLog.txt");
    system("chmod 777 -R  /root/_datalog_/");
    AW_CHECK_LOG_BEGIN();
}

void RbsoTest::TearDown()
{   
    AW_CHECK_LOG_END();
    int ret = close_epoll_thread();
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    testEnvClean();
}
// 001.生成升级so文件后，修改patch文件修改的rule规则内容，生成降级so
TEST_F(RbsoTest, DataLog_089_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile1.so";
    char upfileName[] = "UPfile/dtlfile1_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile1_rollbackV2.so";
    char libName[] = "dtlfile1";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile1_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    sleep(1);
    // 加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 002.生成升级so文件后，修改patch文件修改的对应rule规则，生成降级so
TEST_F(RbsoTest, DataLog_089_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile2.so";
    char upfileName[] = "UPfile/dtlfile2_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile2_rollbackV2.so";
    char libName[] = "dtlfile2";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile2_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    // 加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc", "ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 003.生成升级so文件后，修改patch文件的版本号（1.0->3.0改为1.0->2.0），生成降级so
TEST_F(RbsoTest, DataLog_089_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile3.so";
    char upfileName[] = "UPfile/dtlfile3_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile3_rollbackV2.so";
    char libName[] = "dtlfile3";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile3_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 004.生成升级so文件后，修改patch文件的版本号（1.0->3.0改为1.0->4.0），生成降级so
TEST_F(RbsoTest, DataLog_089_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile4.so";
    char upfileName[] = "UPfile/dtlfile4_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile4_rollbackV2.so";
    char libName[] = "dtlfile4";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile4_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 005.生成升级so文件后，修改patch文件内新增表的表名，生成降级so
TEST_F(RbsoTest, DataLog_089_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile5.so";
    char upfileName[] = "UPfile/dtlfile5_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile5_rollbackV2.so";
    char libName[] = "dtlfile5";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile5_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    system(g_command);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 006.生成升级so文件后，修改patch文件内新增规则的规则名，生成降级so
TEST_F(RbsoTest, DataLog_089_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile6.so";
    char upfileName[] = "UPfile/dtlfile6_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile6_rollbackV2.so";
    char libName[] = "dtlfile6";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile6_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 007.生成升级so文件后，在patch文件里添加%block0标识，生成降级so
TEST_F(RbsoTest, DataLog_089_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile7.so";
    char upfileName[] = "UPfile/dtlfile7_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile7_rollbackV2.so";
    char libName[] = "dtlfile7";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile7_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 008.生成升级so文件后，在patch文件里添加%block1标识，生成降级so
TEST_F(RbsoTest, DataLog_089_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile8.so";
    char upfileName[] = "UPfile/dtlfile8_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile8_rollbackV2.so";
    char libName[] = "dtlfile8";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile8_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 009.使用%block1标识的patch文件生成升级so文件，然后删除%block1标识，生成降级文件
TEST_F(RbsoTest, DataLog_089_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile9.so";
    char upfileName[] = "UPfile/dtlfile9_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile9_rollbackV2.so";
    char libName[] = "dtlfile9";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile9_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 010.使用%block0标识的patch文件生成升级so文件，然后删除%block0标识，生成降级文件
TEST_F(RbsoTest, DataLog_089_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile10.so";
    char upfileName[] = "UPfile/dtlfile10_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile10_rollbackV2.so";
    char libName[] = "dtlfile10";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile10_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 011.使用%block1标识的patch文件生成升级so文件，然后修改patch文件为%block0标识，生成降级文件
TEST_F(RbsoTest, DataLog_089_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile11.so";
    char upfileName[] = "UPfile/dtlfile11_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile11_rollbackV2.so";
    char libName[] = "dtlfile11";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile11_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 012.使用%block0标识的patch文件生成升级so文件，然后修改patch文件为%block1标识，生成降级文件
TEST_F(RbsoTest, DataLog_089_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile12.so";
    char upfileName[] = "UPfile/dtlfile12_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile12_rollbackV2.so";
    char libName[] = "dtlfile9";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile12_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 013.使用带有%redo标识的patch文件生成升级so文件，然后删除%redo标识，生成降级文件
TEST_F(RbsoTest, DataLog_089_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile13.so";
    char upfileName[] = "UPfile/dtlfile13_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile13_rollbackV2.so";
    char libName[] = "dtlfile13";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile13_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 014.使用没有%redo标识的patch文件生成升级so文件，然后添加%redo标识，生成降级文件
TEST_F(RbsoTest, DataLog_089_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile14.so";
    char upfileName[] = "UPfile/dtlfile14_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile14_rollbackV2.so";
    char libName[] = "dtlfile14";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile14_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 015.生成升级so文件后，新增redo的表的数量后生成降级文件
TEST_F(RbsoTest, DataLog_089_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile15.so";
    char upfileName[] = "UPfile/dtlfile15_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile15_rollbackV2.so";
    char libName[] = "dtlfile15";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile15_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 016.生成升级so文件后，减少redo的表的数量后生成降级文件
TEST_F(RbsoTest, DataLog_089_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile16.so";
    char upfileName[] = "UPfile/dtlfile16_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile16_rollbackV2.so";
    char libName[] = "dtlfile16";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile16_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 017.使用带有%redo标识的patch文件生成升级so文件，然后修改为%redo REDO_OFF，生成降级文件
TEST_F(RbsoTest, DataLog_089_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile17.so";
    char upfileName[] = "UPfile/dtlfile17_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile17_rollbackV2.so";
    char libName[] = "dtlfile17";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile17_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 018.生成升级so文件后，篡改function名后生成降级文件
TEST_F(RbsoTest, DataLog_089_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile18.so";
    char upfileName[] = "UPfile/dtlfile18_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile18_rollbackV2.so";
    char libName[] = "dtlfile18";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile18_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 019.生成%block 0升级so文件后，减少新增规则后生成降级文件
TEST_F(RbsoTest, DataLog_089_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile19.so";
    char upfileName[] = "UPfile/dtlfile19_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile19_rollbackV2.so";
    char libName[] = "dtlfile19";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile19_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 020.生成%block 1升级so文件后，增加新增规则后生成降级文件
TEST_F(RbsoTest, DataLog_089_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile20.so";
    char upfileName[] = "UPfile/dtlfile20_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile20_rollbackV2.so";
    char libName[] = "dtlfile20";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile20_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
// 021.多次升级后加载篡改后的rollback文件
TEST_F(RbsoTest, DataLog_089_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    int ret = 0;
    char sofile[] = "UPfile/dtlfile21.so";
    char upfileName[] = "UPfile/dtlfile21_patchV2.so";
    char rbfileName[] = "RBfile/dtlfile21_rollbackV4.so";
    char libName[] = "dtlfile21";
    char labelName_in[] = "inpA";
    char labelName_out[] = "outA";

    const int errCodeLen = 1024;
    char errorMsg1[errCodeLen] = {0};
    (void)snprintf(errorMsg1, errCodeLen, "GMERR-%d", GMERR_DATA_EXCEPTION);
    AW_ADD_ERR_WHITE_LIST(1, errorMsg1);

    GmcConnT *conn;
    GmcStmtT *stmt;
    ret = testGmcConnect(&conn, &stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(sofile);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    //加载升级文件
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile21_patchV2.so -ns %s", g_testNameSpace);
    system(g_command);
    // 加载升级文件2
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile21_patchV3.so -ns %s", g_testNameSpace);
    system(g_command);
    // 加载升级文件3
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "gmimport -c datalog -upgrade UPfile/dtlfile21_patchV4.so -ns %s", g_testNameSpace);
    system(g_command);
    //加载被篡改的回滚文件
    memset(g_command, 0, sizeof(g_command));
    (void)snprintf(g_command, MAX_CMD_SIZE, "gmimport -c datalog -rollback %s -ns %s", rbfileName, g_testNameSpace);
    ret = executeCommand(g_command, "Import datalog file unsucc","ret = 1004000");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK,ret);

    // 卸载
    ret = TestUninstallAllDatalog();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcDisconnect(conn, stmt);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}
