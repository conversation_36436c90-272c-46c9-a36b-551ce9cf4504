%table inp1(a:int8, b:int8, c:int8){index(0(a, b)), update}
%table inp2(a:int8, b:int8, c:int8){index(0(a, b)), update_partial, status_merge_sub(true)}
%table inp3(a:int8, b:int8, c:int8){index(0(a, b)), status_merge_sub(true)}
%table inp4(a:int8, b:int8, c:int8){index(0(a, b)), update, status_merge_sub(true)}
%table mid1(a:int8, b:int8, c:int8){index(0(a, b)), status_merge_sub(true)}
%table mid2(a:int8, b:int8, c:int8){index(0(a, b))}
%table out1(a:int8, b:int8, c:int8){index(0(a, b)), update, status_merge_sub(true)}
%table out2(a:int8, b:int8, c:int8){index(0(a, b, c)), index(1(a, b)), status_merge_sub(true), update}
%table notifytable(a:int8, b:int8, c:int8){index(0(a, b)), notify}
%table tbmtable(a:int8, b:int8, c:int8){index(0(a, b)), tbm}

%function init()
%function uninit()

mid1(a, b, c) :- inp1(a, b, c), inp2(a, b, c).
tbmtable(a, b, c) :- mid1(a, b, c), NOT inp3(a, b, c).
out1(a, b, c) :- mid1(a, b, c), NOT inp3(a, b, c).

mid2(a, b, c) :- inp3(a, b, c), NOT inp4(a, b, c).
notifytable(a, b, c) :- inp2(a, b, c), NOT mid2(a, b, c).
out2(a, b, c) :- inp2(a, b, c), NOT mid2(a, b, c).
out2(a, b, -1) :- inp1(a, b, c), NOT inp3(a, b, c).
