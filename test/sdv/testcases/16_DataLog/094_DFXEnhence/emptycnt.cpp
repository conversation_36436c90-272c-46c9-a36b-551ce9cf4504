/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * File Name: compilertool.cpp
 * Description: NewSub Interface for Suspend and Resume
 * Author: qibingsen 00880292
 * Create: 2025-01-06
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gtest/gtest.h"
#include "t_datacom_lite.h"
#include "newsubfunction.h"

int ret = 0;

class emptycal : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void emptycal::SetUpTestCase()
{
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void emptycal::TearDownTestCase()
{
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void emptycal::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
#ifndef RUN_INDEPENDENT
    system("echo \"\" > /opt/vrpv8/home/<USER>/diag.log");
    sleep(3);
#endif
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void emptycal::TearDown()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 0");
    printf("\n======================TEST:END========================\n");
}

/* ****************************************************************************
创建包含notify表和tbm表，其中两张表由inp1,inp2的join生成的.d文件，编译生成so，
设置链路日志配置型为8，加载so，向输入表inp1单写5条数据，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为10，同时日志文件中产生10条dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_001)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_001.so";
    char soname[MAX_FILE_PATH] = "empty_003_001";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含notify表和tbm,msgnotify,外部表，其中两张表由inp1,inp2的join生成的.d文件，编译生成so，
设置链路日志配置型为8，加载so，向输入表inp1单写5条数据，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为10，同时日志文件中产生10条dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_002)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_002.so";
    char soname[MAX_FILE_PATH] = "empty_003_002";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = LoadExternalVertex(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DropExternalVertex(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含notify表和tbm表，其中两张表由inp1,inp2的not join生成的.d文件，编译生成so，
设置链路日志配置型为8，加载so，向输入表inp1单写5条数据，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为0，同时日志文件中产生0条dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_003)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_003.so";
    char soname[MAX_FILE_PATH] = "empty_003_003";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含notify表和tbm表，其中两张表由inp1,inp2的not join生成的.d文件，编译生成so，
设置链路日志配置型为8，加载so，向输入表inp2单写5条数据，校验视图PTL_DATALOG_SO_INFO，
预期EMPTY_CAL_CNT字段数值为10，同时日志文件中产生10条dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_004)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_003.so";
    char soname[MAX_FILE_PATH] = "empty_003_003";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp2, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含notify表和tbm表，其中两张表由inp1,inp2的join生成的.d文件，编译生成so，
关闭日志折叠，不设置链路日志配置型为8，加载so，向输入表inp1单写1000条数据，校验视图PTL_DATALOG_SO_INFO，
预期EMPTY_CAL_CNT字段数值为2000，同时日志文件中未产生dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_005)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_001.so";
    char soname[MAX_FILE_PATH] = "empty_003_001";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 1000;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp2, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含notify表和tbm表，其中两张表由inp1,inp2的join生成的.d文件，编译生成so，
设置链路日志配置型为8，加载so，
向输入表inp1单写5条数据，校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为10，
向输入表inp2单写5条无法join的数据，校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为20，
同时日志文件中产生20条dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_006)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_001.so";
    char soname[MAX_FILE_PATH] = "empty_003_001";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SingleTableStructT *objIn1 = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    GtlabelCfgT vertexCfg1 = { GMC_OPERATION_INSERT, g_singleinp2, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn1, writeCount, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg1, SingleTableSet, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含由同一个输入表投影得到的notify表和tbm表的.d文件，编译生成so，
设置链路日志配置型为8，加载so，在同一batch中向输入表批写100条数据并批量删除100条数据，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为2，同时日志文件中产生2条dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_007)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_001.so";
    char soname[MAX_FILE_PATH] = "empty_003_001";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 100;
    ret = WriteDeleteOneTableData(g_conn, g_stmt, writeCount, g_singleinp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含由同一个输入表投影得到的notify表和tbm表的.d文件，编译生成so，加载so，
在同一batch中向输入表批写100条数据并批量删除100条数据，校验视图PTL_DATALOG_SO_INFO，
预期EMPTY_CAL_CNT字段数值为2，同时日志文件中不产生dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_008)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_001.so";
    char soname[MAX_FILE_PATH] = "empty_003_001";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 100;
    ret = WriteDeleteOneTableData(g_conn, g_stmt, writeCount, g_singleinp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含notify表和tbm表，其中两张表由inp1,inp2的join生成的.d文件，编译生成so，
设置链路日志配置型为8，加载so，向输入表inp1单写5条数据，校验视图PTL_DATALOG_SO_INFO，
预期EMPTY_CAL_CNT字段数值为10，同时日志文件中产生10条dml告警日志，预期校验成功，
再向输入表inp2中写入10条数据，其中5条能够join成功，校验视图PTL_DATALOG_SO_INFO，
预期EMPTY_CAL_CNT字段数值为20，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_009)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_001.so";
    char soname[MAX_FILE_PATH] = "empty_003_001";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    writeCount = 10;
    SingleTableStructT *objIn1 = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    GtlabelCfgT vertexCfg1 = { GMC_OPERATION_INSERT, g_singleinp2, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn1, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg1, SingleTableSet, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含由输入表inp1投影得到的notify表，经过与inp2进行join到tbm表的.d文件，编译生成so，
设置链路日志配置型为8，加载so，先向inp2单写100条数据，
在同一batch中向inp1批写100条数据并批量删除100条数据，校验视图PTL_DATALOG_SO_INFO，
预期EMPTY_CAL_CNT字段数值为100，同时日志文件中产生100条dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_010)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_010.so";
    char soname[MAX_FILE_PATH] = "empty_003_010";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 100;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp2, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteDeleteOneTableData(g_conn, g_stmt, writeCount, g_singleinp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}


/* ****************************************************************************
创建包含由输入表inp1投影得到的notify表，经过与inp2进行join到mid表中，mid表与inp3join投影到tbm表的.d文件，编译生成so，
设置链路日志配置型为8，加载so，向inp2写入批写100条数据，向inp1单写100条数据，
在同一batch中向inp3先写入100条数据，再删除100条数据，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为102，同时日志文件中产生102条dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_011)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_011.so";
    char soname[MAX_FILE_PATH] = "empty_003_011";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 100;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = true, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp2, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname,  1, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    isBatch = false;
    SingleTableStructT *objIn1 = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    GtlabelCfgT vertexCfg1 = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn1, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg1, SingleTableSet, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteDeleteOneTableData(g_conn, g_stmt, writeCount, g_singleinp3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount + 1, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含notify表和tbm表，其中两张表由inp1,inp2的join生成的.d文件，编译生成so，设置链路日志配置型为8，加载so，
向输入表inp1单写5条数据，校验视图PTL_DATALOG_SO_INFO，
预期EMPTY_CAL_CNT字段数值为10，同时日志文件中产生10条dml告警日志，预期校验成功，
卸载so，再重新加载so，校验视图PTL_DATALOG_SO_INFO，EMPTY_CAL_CNT字段数值为0，预期校验成功，
在同一batch中向inp1,inp2批写100条数据，
校验视图PTL_DATALOG_SO_INFO，EMPTY_CAL_CNT字段数值为0，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_012)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_001.so";
    char soname[MAX_FILE_PATH] = "empty_003_001";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp2, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteTwoTableData(g_conn, g_stmt, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含可更新输入表inp1 join inp2,分别投影到notify表和tbm表，编译生成so，设置链路日志配置型为8，加载so，
向输入表inp1单写5条数据，校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为10，
同时日志文件中产生10条dml告警日志，预期校验成功，向输入表单写5条与之前相同主键数据，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为30/20/10，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_013)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_013.so";
    char soname[MAX_FILE_PATH] = "empty_003_013";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SingleTableStructT *objIn1 = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    SetSingleplusObjInValue(objIn1, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record inp2");
    ret = CheckEmptyCalculateResult(soname, writeCount * 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}


/* ****************************************************************************
创建包含部分可更新输入表inp1 join inp2,分别投影到notify表和tbm表，编译生成so，设置链路日志配置型为8，加载so，
向输入表inp1单写5条数据，校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为10，
同时日志文件中产生10条dml告警日志，预期校验成功，向输入表单写5条与之前相同主键数据，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为20，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_014)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_014.so";
    char soname[MAX_FILE_PATH] = "empty_003_014";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    SingleTableStructT *objIn1 = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    SetSingleplusObjInValue(objIn1, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    free(objIn1);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含notify表和tbm表，其中两张表由inp1,inp2的join生成的.d文件，编译生成so，
设置链路日志配置型为8，加载so，向输入表inp1和inp2同一batch批写5条数据，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为0，同时日志文件中产生0条dml告警日志，
删除inp1所有数据，校验视图PTL_DATALOG_SO_INFO，
预期EMPTY_CAL_CNT字段数值为0，同时日志文件中产生0条dml告警日志，
批量删除inp2所有数据，校验视图PTL_DATALOG_SO_INFO，
预期EMPTY_CAL_CNT字段数值为2，同时日志文件中产生2条dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_015)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_001.so";
    char soname[MAX_FILE_PATH] = "empty_003_001";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    ret = WriteTwoTableData(g_conn, g_stmt, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DeleteKnownData(g_conn, g_stmt, writeCount, g_singleinp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = DeleteKnownData(g_conn, g_stmt, writeCount, g_singleinp2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含过期表inp1 join inp2分别投影到notify表和tbm表，编译生成so，设置链路日志配置型为8，加载so，
向输入表inp1单写5条数据，延时3s+，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为10，同时日志文件中产生10条dml告警日志，预期校验成功，
20s后，校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为20，告警日志20条，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_016)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_016.so";
    char soname[MAX_FILE_PATH] = "empty_003_016";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 72");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 72");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    for (int i = 0; i < writeCount; i++) {
        objIn[i].a = i;
        objIn[i].b = i;
        objIn[i].c = (i + 1) * 6000;
        objIn[i].dtlReservedCount = 1;
        objIn[i].upgradeVersion = 0;
    }
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    sleep(36);
    ret = CheckEmptyCalculateResult(soname, writeCount * 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("gmsysview record inp1");
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建主键为a,b,c包含agg函数,groupby a b, many_to_one投影到mid，mid分别投影到notify表和tbm表，编译生成so，
设置链路日志配置型为8，加载so，向输入表inp1单写5条ab相同，c不同的数据，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为0，同时日志文件中不产生dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_017)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_017.so";
    char soname[MAX_FILE_PATH] = "empty_003_017";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含function函数，返回值为NO_DATA，投影到notify表和tbm表，编译生成so，
设置链路日志配置型为8，加载so，向输入表单写5条数据，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为10，同时日志文件中产生10条dml告警日志，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_018)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_018.so";
    char soname[MAX_FILE_PATH] = "empty_003_018";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
创建包含输入表inp1 join inp2投影到notify表，inp1投影到tbm表，编译生成so，
设置链路日志配置型为8，加载so，向输入表inp1单写5条数据，
校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为10，同时日志文件中产生10条dml告警日志，预期校验成功，
同一batch中向inp1删除之前5条数据并向inp2插入5条数据，校验视图PTL_DATALOG_SO_INFO，预期EMPTY_CAL_CNT字段数值为12，预期校验成功。
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_019)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_001.so";
    char soname[MAX_FILE_PATH] = "empty_003_001";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteDeleteTableData(g_conn, g_stmt, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2 + 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount + 1, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount + 1, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
热升级，block0 新增notify表，tbm表输入表，校验空计算是否符合预期
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_020)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_020.so";
    char patchfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_020_patchV2.so";
    char rollbackfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_020_rollbackV2.so";
    char soname[MAX_FILE_PATH] = "empty_003_020";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpgradeFinished(patchfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.labelName = g_singleinp2;
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDowngradeFinished(rollbackfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_notifytable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}


class emptycalfecthsize : public testing::Test {
public:
    virtual void SetUp();
    virtual void TearDown();

protected:
    static void SetUpTestCase();
    static void TearDownTestCase();
};

void emptycalfecthsize::SetUpTestCase()
{
    system("sh ${TEST_HOME}/tools/start.sh");
    ret = testEnvInit();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = create_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}
void emptycalfecthsize::TearDownTestCase()
{
    ret = close_epoll_thread();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = testEnvClean();
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void emptycalfecthsize::SetUp()
{
    printf("\n======================TEST:BEGIN======================\n");
    system("rm -rf /root/_datalog_/");
    system("mkdir -p /root/_datalog_/");
    system("chmod -R 777 /root/_datalog_/");
#ifndef RUN_INDEPENDENT
    system("echo \"\" > /opt/vrpv8/home/<USER>/diag.log");
    sleep(3);
#endif
    system("sh ${TEST_HOME}/tools/stop.sh -f");
    system("sh ${TEST_HOME}/tools/start.sh");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 创建同步客户端连接
    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

void emptycalfecthsize::TearDown()
{
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    // 断开同步连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    system("rm -rf /root/_datalog_/");
    system("gmadmin -cfgName enableLogFold -cfgVal 1");
    system("gmadmin -cfgName datalogRunLinkLog -cfgVal 0");
    printf("\n======================TEST:END========================\n");
}

/* ****************************************************************************
热升级，block0 datalogUpgradeFetchSize为5, 新增notify表，tbm表输入表，校验空计算是否符合预期
**************************************************************************** */
TEST_F(emptycalfecthsize, DataLog_094_003_021)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_020.so";
    char patchfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_020_patchV2.so";
    char rollbackfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_020_rollbackV2.so";
    char soname[MAX_FILE_PATH] = "empty_003_020";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 5");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 5");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpgradeFinished(patchfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_notifytable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.labelName = g_singleinp2;
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_notifytable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDowngradeFinished(rollbackfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 4);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 2, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 2, g_notifytable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    system("gmadmin -cfgName datalogUpgradeFetchSize -cfgVal 1");
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
热升级，block0 新增输入表与原输入表join，变为空计算，校验空计算是否符合预期
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_022)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_022.so";
    char patchfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_022_patchV2.so";
    char rollbackfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_022_rollbackV2.so";
    char soname[MAX_FILE_PATH] = "empty_003_022";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp1, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpgradeFinished(patchfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.labelName = g_singleinp2;
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDowngradeFinished(rollbackfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 0, g_notifytable1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
热升级，block1 新增输入表与原输入表join，变为空计算，校验空计算是否符合预期
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_023)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_023.so";
    char patchfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_023_patchV2.so";
    char rollbackfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_023_rollbackV2.so";
    char soname[MAX_FILE_PATH] = "empty_003_023";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp2, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpgradeFinished(patchfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.labelName = g_singleinp3;
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteDeleteOneTableData(g_conn, g_stmt, writeCount, g_singleinp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.labelName = g_singleinp1;
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDowngradeFinished(rollbackfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 3);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
热升级，block1 %redooff 新增输入表与原输入表join，变为空计算，校验空计算是否符合预期
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_024)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_024.so";
    char patchfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_024_patchV2.so";
    char rollbackfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_024_rollbackV2.so";
    char soname[MAX_FILE_PATH] = "empty_003_024";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp2, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpgradeFinished(patchfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.labelName = g_singleinp3;
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = WriteDeleteOneTableData(g_conn, g_stmt, writeCount, g_singleinp1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDowngradeFinished(rollbackfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
热升级，block1 新增function与原输入表join，变为空计算，校验空计算是否符合预期
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_025)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_025.so";
    char patchfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_025_patchV2.so";
    char rollbackfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_025_rollbackV2.so";
    char soname[MAX_FILE_PATH] = "empty_003_025";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp2, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpgradeFinished(patchfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.labelName = g_singleinp1;
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDowngradeFinished(rollbackfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}

/* ****************************************************************************
热升级，block0 新增function与原输入表join，变为空计算，校验空计算是否符合预期
**************************************************************************** */
TEST_F(emptycal, DataLog_094_003_026)
{
    AW_FUN_Log(LOG_STEP, "test start.");
    char filepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_026.so";
    char patchfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_026_patchV2.so";
    char rollbackfilepath[MAX_FILE_PATH] = "datalogfile/execute/empty_003_026_rollbackV2.so";
    char soname[MAX_FILE_PATH] = "empty_003_026";
    char gmadmincmd[MAX_NAME_LENGTH] = {0};
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName enableLogFold -cfgVal 0");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(gmadmincmd, MAX_NAME_LENGTH, "gmadmin -cfgName datalogRunLinkLog -cfgVal 8");
    ret = executeCommand(gmadmincmd, "after setting config:", "config current value: 8");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcUseNamespace(g_stmt, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestLoadDatalog(filepath, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    int32_t writeCount = 5;
    SingleTableStructT *objIn = (SingleTableStructT *)malloc(sizeof(SingleTableStructT) * writeCount);
    bool isBatch = false, isStruct = false;
    GtlabelCfgT vertexCfg = { GMC_OPERATION_INSERT, g_singleinp2, writeCount, isBatch, isStruct };
    SetSingleObjInValue(objIn, writeCount);
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckUpgradeFinished(patchfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    vertexCfg.labelName = g_singleinp1;
    ret = WriteKnownData(g_conn, g_stmt, vertexCfg, SingleTableSet, objIn);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2 + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckDowngradeFinished(rollbackfilepath);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateResult(soname, writeCount * 2 + 1);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, writeCount * 2, g_tbmtable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = CheckEmptyCalculateLog(soname, 1, g_notifytable);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = TestUninstallDatalog(soname, g_testNameSpace);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    free(objIn);
    AW_FUN_Log(LOG_STEP, "test end.");
}
