/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: DLSupDelAuth.cpp
 * Description: Datalog融合引擎支持按照key删除数据
 * Author: ya<PERSON><PERSON><PERSON> ywx1060383
 * Create: 2023-02-16
 */
#include "DLSupDel.h"
#include "t_datacom_lite.h"

#define MAX_CMD_SIZE 1024

char g_command[MAX_CMD_SIZE];
char g_command2[MAX_CMD_SIZE];

using namespace std;

class DLSupDelPri : public testing::Test {
protected:
    static void SetUpTestCase()
    {
        system("sh $TEST_HOME/tools/stop.sh -f");
        system("sh $TEST_HOME/tools/modifyCfg.sh \"userPolicyMode=2\" ");
        system("sh ${TEST_HOME}/tools/start.sh");
        int ret;
        ret = testEnvInit();
        EXPECT_EQ(GMERR_OK, ret);
        ret = create_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
    }
    static void TearDownTestCase()
    {
        int ret;
        ret = close_epoll_thread();
        EXPECT_EQ(GMERR_OK, ret);
        testEnvClean();
        system("sh $TEST_HOME/tools/modifyCfg.sh recover");
    }

public:
    virtual void SetUp();
    virtual void TearDown();
};

void DLSupDelPri::SetUp()
{
    AW_ADD_ERR_WHITE_LIST(3, "GMERR-1009012", "GMERR-1001000", "GMERR-1018004");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, TestUninstallAllDatalog());  // 用例执行前卸载残留的so
    AW_CHECK_LOG_BEGIN();
}
void DLSupDelPri::TearDown()
{
    AW_CHECK_LOG_END();
}

// 035.开启鉴权，授予系统权限delete_any, 不授予对象权限delete, 删除成功
TEST_F(DLSupDelPri, DataLog_024_035)
{
    // 导入白名单
    char allowListFile[128] = "allow_list/allow_list.gmuser";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allowListFile, g_connServer);
    int ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 导入delete_any系统权限
    char sysPrivFile[128] = "sys_file/sysVertex1.gmpolicy";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, sysPrivFile, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_pk_002";
    TestLoadDatalog("./project_file/project_pk_002.so");

    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};

    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用主键索引删除数据");
    int64_t A10 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &A10, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    A10 = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &A10, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "删除数据后查询数据条数");
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "A10", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "B10", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);

    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);

    // 撤销权限
    (void)snprintf(
        g_command2, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, sysPrivFile, g_connServer);
    ret = system(g_command2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
}

// 036.开启鉴权，不授予系统权限delete_any, 授予对象权限delete, 删除成功
TEST_F(DLSupDelPri, DataLog_024_036)
{
    // 导入白名单
    char allowListFile[128] = "allow_list/allow_list.gmuser";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allowListFile, g_connServer);
    int ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 导入无delete_any的系统权限
    char sysPrivFile[128] = "sys_file/sysVertex2.gmpolicy";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, sysPrivFile, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_pk_002";
    TestLoadDatalog("./project_file/project_pk_002.so");

    // 导入delete对象权限
    char objPrivFile[128] = "obj_file/objVertex_001.gmpolicy";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, objPrivFile, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};

    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用主键索引删除数据");
    int64_t A10 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &A10, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    A10 = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &A10, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "删除数据后查询数据条数");
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "A10", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "B10", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销对象权限
    (void)snprintf(
        g_command2, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, objPrivFile, g_connServer);
    ret = system(g_command2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);

    // 撤销系统权限
    (void)snprintf(
        g_command2, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, sysPrivFile, g_connServer);
    ret = system(g_command2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 037.开启鉴权，既授予系统权限delete_any, 又授予对象权限delete, 删除成功
TEST_F(DLSupDelPri, DataLog_024_037)
{
    // 导入白名单
    char allowListFile[128] = "allow_list/allow_list.gmuser";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allowListFile, g_connServer);
    int ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入delete_any的系统权限
    char sysPrivFile[128] = "sys_file/sysVertex1.gmpolicy";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, sysPrivFile, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_pk_002";
    TestLoadDatalog("./project_file/project_pk_002.so");

    // 导入delete对象权限
    char objPrivFile[128] = "obj_file/objVertex_001.gmpolicy";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, objPrivFile, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};

    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用主键索引删除数据");
    int64_t A10 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &A10, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);
    A10 = 2;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &A10, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcGetStmtAttr(g_stmt, GMC_STMT_ATTR_AFFECTED_ROWS, &affectRows, sizeof(affectRows));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(1, affectRows);

    AW_FUN_Log(LOG_STEP, "删除数据后查询数据条数");
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "A10", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "B10", "0");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销对象权限
    (void)snprintf(
        g_command2, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, objPrivFile, g_connServer);
    ret = system(g_command2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);

    // 撤销系统权限
    (void)snprintf(
        g_command2, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, sysPrivFile, g_connServer);
    ret = system(g_command2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}

// 044.不授予系统权限insert_any, 不授予对象权限insert, 删除失败
TEST_F(DLSupDelPri, DataLog_024_044)
{
    // 导入白名单
    char allowListFile[128] = "allow_list/allow_list.gmuser";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_allowlist -f %s -s %s ", g_toolPath, allowListFile, g_connServer);
    int ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入insert_any的系统权限
    char sysPrivFile[128] = "sys_file/sysVertex2.gmpolicy";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, sysPrivFile, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    ret = testGmcConnect(&g_conn, &g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    char tableA10[] = "A10";
    char tableB10[] = "B10";
    int affectRows = 0;
    char ns[] = "project_pk_002";
    TestLoadDatalog("./project_file/project_pk_002.so");

    AW_FUN_Log(LOG_STEP, "插入数据");
    int32_t count1[][4] = {{1, 1, 1, 1}, {2, 2, 2, -2}};

    ret = batchA10(g_stmt, tableA10, count1, 2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    AW_MACRO_EXPECT_EQ_INT(0, TestWaitDatalogQueue());

    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销INSET_ANY系统权限
    char sysPrivFile3[128] = "sys_file/sysVertex3_revoke.gmpolicy";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, sysPrivFile3, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 导入无insert对象权限
    char objPrivFile[128] = "obj_file/objVertex_002.gmpolicy";
    (void)snprintf(
        g_command, MAX_CMD_SIZE, "%s/gmrule -c import_policy -f %s -s %s ", g_toolPath, objPrivFile, g_connServer);
    ret = system(g_command);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    AW_FUN_Log(LOG_STEP, "使用主键索引删除数据，权限不足报18004");
    int64_t A10 = 1;
    ret = testGmcPrepareStmtByLabelName(g_stmt, tableA10, GMC_OPERATION_DELETE);
    AW_MACRO_ASSERT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyId(g_stmt, 0);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 0, GMC_DATATYPE_INT32, &A10, sizeof(int32_t));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 1, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 2, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcSetIndexKeyValue(g_stmt, 3, GMC_DATATYPE_INT64, &A10, sizeof(A10));
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    ret = GmcExecute(g_stmt);
    AW_MACRO_EXPECT_EQ_INT(GMERR_INSUFFICIENT_PRIVILEGE, ret);  // 18004

    AW_FUN_Log(LOG_STEP, "删除数据后查询数据条数");
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableA10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "A10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);
    (void)snprintf(g_command2, MAX_CMD_SIZE, "%s/gmsysview count %s -s %s -ns %s", g_toolPath, tableB10, g_connServer,
        g_testNameSpace);
    ret = executeCommand(g_command2, "B10", "2");
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 撤销对象权限
    (void)snprintf(
        g_command2, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, objPrivFile, g_connServer);
    ret = system(g_command2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 卸载
    TestUninstallDatalog(ns, NULL, false);

    // 撤销系统权限
    (void)snprintf(
        g_command2, MAX_CMD_SIZE, "%s/gmrule -c revoke_policy  -f %s -s %s ", g_toolPath, sysPrivFile, g_connServer);
    ret = system(g_command2);
    AW_MACRO_EXPECT_EQ_INT(GMERR_OK, ret);

    // 断开连接
    ret = testGmcDisconnect(g_conn, g_stmt);
    EXPECT_EQ(GMERR_OK, ret);
}
