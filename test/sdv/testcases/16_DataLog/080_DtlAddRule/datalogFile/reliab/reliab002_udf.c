/*  版权所有 (c) 华为技术有限公司 2022-2023 */
#include "gm_udf.h"
#include "stdio.h"
#include "unistd.h"

#pragma pack(1)

typedef struct Out1 {
    int32_t dtlReservedCount;
    int32_t upgradeVersion;
    int64_t a;
    int64_t b;
    int64_t c;
    int64_t d;
    int64_t e;
} Out1;

typedef struct B {
    int32_t dtlReservedCount;
    int64_t a;
    int64_t b;
    int64_t c;
} B;

#pragma pack(0)

const char *g_logName = "/root/_datalog_/TbmRunLog.txt";
const char *g_logfuncName = "/root/_datalog_/funcLog.txt";

int32_t dtl_ext_func_ns1_func(void *tuple, GmUdfCtxT *ctx)
{
    sleep(3);
    B *b = (B *)tuple;
    FILE *fp = fopen(g_logfuncName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_ext_func_func, a = %d, b = %d, c = %d, dtlReservedCount = %d.\n", b->a, b->b, b->c,
        b->dtlReservedCount);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_ns1_func10(void *tuple, GmUdfCtxT *ctx)
{
    B *b = (B *)tuple;
    FILE *fp = fopen(g_logfuncName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_ext_func_func10, a = %d, b = %d, c = %d, dtlReservedCount = %d.\n", b->a, b->b, b->c,
        b->dtlReservedCount);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_ext_func_init(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_ext_func_init.\n");
    (void)fclose(fp);

    return GMERR_OK;
}

int32_t dtl_ext_func_uninit(GmUdfCtxT *ctx)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_ext_func_uninit.\n");
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_ns1_out1(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_tbm_tbl_out1, op = %d, a = %d, b = %d, c = %d, d = %d, e = %d, dtlReservedCount = %d, "
        "upgradeVersion = %d.\n", op, ((Out1 *)tuple)->a, ((Out1 *)tuple)->b, ((Out1 *)tuple)->c, ((Out1 *)tuple)->d,
        ((Out1 *)tuple)->e, ((Out1 *)tuple)->dtlReservedCount, ((Out1 *)tuple)->upgradeVersion);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_ns1_out2(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_tbm_tbl_out2, op = %d, a = %d, b = %d, c = %d, d = %d, e = %d, dtlReservedCount = %d, "
        "upgradeVersion = %d.\n", op, ((Out1 *)tuple)->a, ((Out1 *)tuple)->b, ((Out1 *)tuple)->c, ((Out1 *)tuple)->d,
        ((Out1 *)tuple)->e, ((Out1 *)tuple)->dtlReservedCount, ((Out1 *)tuple)->upgradeVersion);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_ns2_out3(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_tbm_tbl_out3, op = %d, a = %d, b = %d, c = %d, d = %d, e = %d, dtlReservedCount = %d, "
        "upgradeVersion = %d.\n", op, ((Out1 *)tuple)->a, ((Out1 *)tuple)->b, ((Out1 *)tuple)->c, ((Out1 *)tuple)->d,
        ((Out1 *)tuple)->e, ((Out1 *)tuple)->dtlReservedCount, ((Out1 *)tuple)->upgradeVersion);
    (void)fclose(fp);
    return GMERR_OK;
}

int32_t dtl_tbm_tbl_ns2_out4(uint32_t op, void *tuple)
{
    FILE *fp = fopen(g_logName, "a+");
    if (fp == NULL) {
        return -1;
    }
    (void)fprintf(fp, "dtl_tbm_tbl_out4, op = %d, a = %d, b = %d, c = %d, d = %d, e = %d, dtlReservedCount = %d, "
        "upgradeVersion = %d.\n", op, ((Out1 *)tuple)->a, ((Out1 *)tuple)->b, ((Out1 *)tuple)->c, ((Out1 *)tuple)->d,
        ((Out1 *)tuple)->e, ((Out1 *)tuple)->dtlReservedCount, ((Out1 *)tuple)->upgradeVersion);
    (void)fclose(fp);
    return GMERR_OK;
}
